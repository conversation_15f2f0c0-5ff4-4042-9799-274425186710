<?xml version="1.0" encoding="UTF-8"?>
<module type="JAVA_MODULE" version="4">
  <component name="NewModuleRootManager" inherit-compiler-output="true">
    <exclude-output />
    <content url="file://$MODULE_DIR$">
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/bin/cache/dart-sdk/lib/_internal/js_runtime/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/bin/cache/dart-sdk/lib/_internal/js_runtime/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/bin/cache/dart-sdk/lib/_internal/js_runtime/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/bin/cache/dart-sdk/lib/_internal/sdk_library_metadata/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/bin/cache/dart-sdk/lib/_internal/sdk_library_metadata/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/bin/cache/dart-sdk/lib/_internal/sdk_library_metadata/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/bin/cache/dart-sdk/pkg/_macros/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/bin/cache/dart-sdk/pkg/_macros/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/bin/cache/dart-sdk/pkg/_macros/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/bin/cache/pkg/flutter_gpu/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/bin/cache/pkg/flutter_gpu/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/bin/cache/pkg/flutter_gpu/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/bin/cache/pkg/sky_engine/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/bin/cache/pkg/sky_engine/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/bin/cache/pkg/sky_engine/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/a11y_assessments/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/a11y_assessments/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/a11y_assessments/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/automated_tests/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/automated_tests/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/automated_tests/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/benchmarks/complex_layout/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/benchmarks/complex_layout/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/benchmarks/complex_layout/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/benchmarks/macrobenchmarks/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/benchmarks/macrobenchmarks/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/benchmarks/macrobenchmarks/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/benchmarks/microbenchmarks/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/benchmarks/microbenchmarks/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/benchmarks/microbenchmarks/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/benchmarks/multiple_flutters/module/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/benchmarks/multiple_flutters/module/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/benchmarks/multiple_flutters/module/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/benchmarks/platform_channels_benchmarks/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/benchmarks/platform_channels_benchmarks/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/benchmarks/platform_channels_benchmarks/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/benchmarks/platform_views_layout/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/benchmarks/platform_views_layout/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/benchmarks/platform_views_layout/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/benchmarks/platform_views_layout_hybrid_composition/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/benchmarks/platform_views_layout_hybrid_composition/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/benchmarks/platform_views_layout_hybrid_composition/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/benchmarks/test_apps/stocks/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/benchmarks/test_apps/stocks/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/benchmarks/test_apps/stocks/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/bots/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/bots/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/bots/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/conductor/core/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/conductor/core/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/conductor/core/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/customer_testing/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/customer_testing/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/customer_testing/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/devicelab/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/devicelab/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/devicelab/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/docs/platform_integration/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/docs/platform_integration/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/docs/platform_integration/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/docs/renderers/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/docs/renderers/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/docs/renderers/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/forbidden_from_release_tests/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/forbidden_from_release_tests/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/forbidden_from_release_tests/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/integration_tests/abstract_method_smoke_test/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/integration_tests/abstract_method_smoke_test/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/integration_tests/abstract_method_smoke_test/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/integration_tests/android_embedding_v2_smoke_test/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/integration_tests/android_embedding_v2_smoke_test/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/integration_tests/android_embedding_v2_smoke_test/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/integration_tests/android_semantics_testing/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/integration_tests/android_semantics_testing/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/integration_tests/android_semantics_testing/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/integration_tests/android_verified_input/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/integration_tests/android_verified_input/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/integration_tests/android_verified_input/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/integration_tests/android_views/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/integration_tests/android_views/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/integration_tests/android_views/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/integration_tests/channels/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/integration_tests/channels/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/integration_tests/channels/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/integration_tests/deferred_components_test/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/integration_tests/deferred_components_test/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/integration_tests/deferred_components_test/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/integration_tests/external_textures/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/integration_tests/external_textures/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/integration_tests/external_textures/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/integration_tests/flavors/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/integration_tests/flavors/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/integration_tests/flavors/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/integration_tests/flutter_gallery/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/integration_tests/flutter_gallery/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/integration_tests/flutter_gallery/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/integration_tests/gradle_deprecated_settings/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/integration_tests/gradle_deprecated_settings/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/integration_tests/gradle_deprecated_settings/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/integration_tests/hybrid_android_views/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/integration_tests/hybrid_android_views/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/integration_tests/hybrid_android_views/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/integration_tests/ios_add2app_life_cycle/flutterapp/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/integration_tests/ios_add2app_life_cycle/flutterapp/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/integration_tests/ios_add2app_life_cycle/flutterapp/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/integration_tests/ios_app_with_extensions/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/integration_tests/ios_app_with_extensions/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/integration_tests/ios_app_with_extensions/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/integration_tests/ios_platform_view_tests/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/integration_tests/ios_platform_view_tests/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/integration_tests/ios_platform_view_tests/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/integration_tests/link_hook/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/integration_tests/link_hook/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/integration_tests/link_hook/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/integration_tests/native_driver_test/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/integration_tests/native_driver_test/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/integration_tests/native_driver_test/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/integration_tests/new_gallery/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/integration_tests/new_gallery/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/integration_tests/new_gallery/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/integration_tests/non_nullable/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/integration_tests/non_nullable/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/integration_tests/non_nullable/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/integration_tests/platform_interaction/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/integration_tests/platform_interaction/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/integration_tests/platform_interaction/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/integration_tests/release_smoke_test/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/integration_tests/release_smoke_test/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/integration_tests/release_smoke_test/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/integration_tests/spell_check/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/integration_tests/spell_check/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/integration_tests/spell_check/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/integration_tests/ui/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/integration_tests/ui/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/integration_tests/ui/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/integration_tests/web/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/integration_tests/web/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/integration_tests/web/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/integration_tests/web_compile_tests/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/integration_tests/web_compile_tests/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/integration_tests/web_compile_tests/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/integration_tests/web_e2e_tests/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/integration_tests/web_e2e_tests/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/integration_tests/web_e2e_tests/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/integration_tests/wide_gamut_test/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/integration_tests/wide_gamut_test/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/integration_tests/wide_gamut_test/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/integration_tests/windows_startup_test/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/integration_tests/windows_startup_test/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/integration_tests/windows_startup_test/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/manual_tests/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/manual_tests/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/manual_tests/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/missing_dependency_tests/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/missing_dependency_tests/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/missing_dependency_tests/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/snippets/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/snippets/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/snippets/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/tools/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/tools/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/tools/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/tools/gen_defaults/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/tools/gen_defaults/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/tools/gen_defaults/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/tools/gen_keycodes/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/tools/gen_keycodes/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/tools/gen_keycodes/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/tools/native_driver/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/tools/native_driver/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/tools/native_driver/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/tools/vitool/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/tools/vitool/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/tools/vitool/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/tracing_tests/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/tracing_tests/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/dev/tracing_tests/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/examples/api/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/examples/api/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/examples/api/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/examples/flutter_view/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/examples/flutter_view/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/examples/flutter_view/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/examples/hello_world/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/examples/hello_world/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/examples/hello_world/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/examples/image_list/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/examples/image_list/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/examples/image_list/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/examples/layers/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/examples/layers/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/examples/layers/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/examples/platform_channel/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/examples/platform_channel/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/examples/platform_channel/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/examples/platform_channel_swift/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/examples/platform_channel_swift/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/examples/platform_channel_swift/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/examples/platform_view/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/examples/platform_view/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/examples/platform_view/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/examples/splash/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/examples/splash/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/examples/splash/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/examples/texture/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/examples/texture/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/examples/texture/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/packages/flutter/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/packages/flutter/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/packages/flutter/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/packages/flutter/test_private/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/packages/flutter/test_private/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/packages/flutter/test_private/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/packages/flutter/test_private/test/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/packages/flutter/test_private/test/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/packages/flutter/test_private/test/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/packages/flutter_driver/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/packages/flutter_driver/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/packages/flutter_driver/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/packages/flutter_goldens/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/packages/flutter_goldens/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/packages/flutter_goldens/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/packages/flutter_localizations/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/packages/flutter_localizations/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/packages/flutter_localizations/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/packages/flutter_test/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/packages/flutter_test/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/packages/flutter_test/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/packages/flutter_test/test/test_config/project_root/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/packages/flutter_test/test/test_config/project_root/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/packages/flutter_test/test/test_config/project_root/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/packages/flutter_tools/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/packages/flutter_tools/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/packages/flutter_tools/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/packages/flutter_web_plugins/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/packages/flutter_web_plugins/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/packages/flutter_web_plugins/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/packages/fuchsia_remote_debug_protocol/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/packages/fuchsia_remote_debug_protocol/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/packages/fuchsia_remote_debug_protocol/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/packages/integration_test/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/packages/integration_test/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/packages/integration_test/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/packages/integration_test/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/packages/integration_test/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/packages/integration_test/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/packages/integration_test/integration_test_macos/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/packages/integration_test/integration_test_macos/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/flutter_sdk/packages/integration_test/integration_test_macos/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/bin/cache/dart-sdk/lib/_internal/js_runtime/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/bin/cache/dart-sdk/lib/_internal/js_runtime/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/bin/cache/dart-sdk/lib/_internal/js_runtime/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/bin/cache/dart-sdk/lib/_internal/sdk_library_metadata/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/bin/cache/dart-sdk/lib/_internal/sdk_library_metadata/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/bin/cache/dart-sdk/lib/_internal/sdk_library_metadata/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/bin/cache/dart-sdk/pkg/_macros/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/bin/cache/dart-sdk/pkg/_macros/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/bin/cache/dart-sdk/pkg/_macros/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/bin/cache/pkg/flutter_gpu/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/bin/cache/pkg/flutter_gpu/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/bin/cache/pkg/flutter_gpu/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/bin/cache/pkg/sky_engine/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/bin/cache/pkg/sky_engine/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/bin/cache/pkg/sky_engine/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/a11y_assessments/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/a11y_assessments/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/a11y_assessments/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/automated_tests/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/automated_tests/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/automated_tests/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/benchmarks/complex_layout/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/benchmarks/complex_layout/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/benchmarks/complex_layout/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/benchmarks/macrobenchmarks/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/benchmarks/macrobenchmarks/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/benchmarks/macrobenchmarks/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/benchmarks/microbenchmarks/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/benchmarks/microbenchmarks/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/benchmarks/microbenchmarks/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/benchmarks/multiple_flutters/module/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/benchmarks/multiple_flutters/module/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/benchmarks/multiple_flutters/module/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/benchmarks/platform_channels_benchmarks/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/benchmarks/platform_channels_benchmarks/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/benchmarks/platform_channels_benchmarks/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/benchmarks/platform_views_layout/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/benchmarks/platform_views_layout/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/benchmarks/platform_views_layout/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/benchmarks/platform_views_layout_hybrid_composition/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/benchmarks/platform_views_layout_hybrid_composition/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/benchmarks/platform_views_layout_hybrid_composition/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/benchmarks/test_apps/stocks/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/benchmarks/test_apps/stocks/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/benchmarks/test_apps/stocks/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/bots/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/bots/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/bots/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/conductor/core/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/conductor/core/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/conductor/core/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/customer_testing/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/customer_testing/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/customer_testing/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/devicelab/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/devicelab/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/devicelab/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/docs/platform_integration/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/docs/platform_integration/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/docs/platform_integration/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/docs/renderers/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/docs/renderers/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/docs/renderers/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/forbidden_from_release_tests/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/forbidden_from_release_tests/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/forbidden_from_release_tests/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/integration_tests/abstract_method_smoke_test/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/integration_tests/abstract_method_smoke_test/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/integration_tests/abstract_method_smoke_test/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/integration_tests/android_embedding_v2_smoke_test/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/integration_tests/android_embedding_v2_smoke_test/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/integration_tests/android_embedding_v2_smoke_test/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/integration_tests/android_semantics_testing/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/integration_tests/android_semantics_testing/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/integration_tests/android_semantics_testing/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/integration_tests/android_verified_input/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/integration_tests/android_verified_input/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/integration_tests/android_verified_input/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/integration_tests/android_views/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/integration_tests/android_views/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/integration_tests/android_views/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/integration_tests/channels/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/integration_tests/channels/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/integration_tests/channels/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/integration_tests/deferred_components_test/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/integration_tests/deferred_components_test/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/integration_tests/deferred_components_test/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/integration_tests/external_textures/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/integration_tests/external_textures/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/integration_tests/external_textures/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/integration_tests/flavors/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/integration_tests/flavors/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/integration_tests/flavors/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/integration_tests/flutter_gallery/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/integration_tests/flutter_gallery/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/integration_tests/flutter_gallery/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/integration_tests/gradle_deprecated_settings/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/integration_tests/gradle_deprecated_settings/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/integration_tests/gradle_deprecated_settings/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/integration_tests/hybrid_android_views/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/integration_tests/hybrid_android_views/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/integration_tests/hybrid_android_views/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/integration_tests/ios_add2app_life_cycle/flutterapp/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/integration_tests/ios_add2app_life_cycle/flutterapp/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/integration_tests/ios_add2app_life_cycle/flutterapp/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/integration_tests/ios_app_with_extensions/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/integration_tests/ios_app_with_extensions/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/integration_tests/ios_app_with_extensions/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/integration_tests/ios_platform_view_tests/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/integration_tests/ios_platform_view_tests/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/integration_tests/ios_platform_view_tests/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/integration_tests/link_hook/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/integration_tests/link_hook/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/integration_tests/link_hook/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/integration_tests/native_driver_test/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/integration_tests/native_driver_test/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/integration_tests/native_driver_test/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/integration_tests/new_gallery/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/integration_tests/new_gallery/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/integration_tests/new_gallery/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/integration_tests/non_nullable/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/integration_tests/non_nullable/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/integration_tests/non_nullable/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/integration_tests/platform_interaction/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/integration_tests/platform_interaction/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/integration_tests/platform_interaction/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/integration_tests/release_smoke_test/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/integration_tests/release_smoke_test/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/integration_tests/release_smoke_test/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/integration_tests/spell_check/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/integration_tests/spell_check/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/integration_tests/spell_check/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/integration_tests/ui/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/integration_tests/ui/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/integration_tests/ui/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/integration_tests/web/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/integration_tests/web/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/integration_tests/web/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/integration_tests/web_compile_tests/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/integration_tests/web_compile_tests/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/integration_tests/web_compile_tests/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/integration_tests/web_e2e_tests/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/integration_tests/web_e2e_tests/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/integration_tests/web_e2e_tests/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/integration_tests/wide_gamut_test/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/integration_tests/wide_gamut_test/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/integration_tests/wide_gamut_test/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/integration_tests/windows_startup_test/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/integration_tests/windows_startup_test/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/integration_tests/windows_startup_test/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/manual_tests/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/manual_tests/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/manual_tests/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/missing_dependency_tests/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/missing_dependency_tests/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/missing_dependency_tests/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/snippets/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/snippets/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/snippets/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/tools/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/tools/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/tools/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/tools/gen_defaults/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/tools/gen_defaults/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/tools/gen_defaults/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/tools/gen_keycodes/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/tools/gen_keycodes/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/tools/gen_keycodes/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/tools/native_driver/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/tools/native_driver/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/tools/native_driver/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/tools/vitool/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/tools/vitool/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/tools/vitool/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/tracing_tests/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/tracing_tests/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/dev/tracing_tests/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/examples/api/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/examples/api/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/examples/api/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/examples/flutter_view/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/examples/flutter_view/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/examples/flutter_view/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/examples/hello_world/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/examples/hello_world/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/examples/hello_world/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/examples/image_list/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/examples/image_list/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/examples/image_list/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/examples/layers/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/examples/layers/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/examples/layers/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/examples/platform_channel/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/examples/platform_channel/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/examples/platform_channel/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/examples/platform_channel_swift/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/examples/platform_channel_swift/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/examples/platform_channel_swift/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/examples/platform_view/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/examples/platform_view/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/examples/platform_view/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/examples/splash/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/examples/splash/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/examples/splash/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/examples/texture/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/examples/texture/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/examples/texture/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/packages/flutter/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/packages/flutter/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/packages/flutter/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/packages/flutter/test_private/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/packages/flutter/test_private/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/packages/flutter/test_private/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/packages/flutter/test_private/test/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/packages/flutter/test_private/test/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/packages/flutter/test_private/test/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/packages/flutter_driver/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/packages/flutter_driver/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/packages/flutter_driver/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/packages/flutter_goldens/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/packages/flutter_goldens/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/packages/flutter_goldens/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/packages/flutter_localizations/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/packages/flutter_localizations/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/packages/flutter_localizations/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/packages/flutter_test/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/packages/flutter_test/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/packages/flutter_test/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/packages/flutter_test/test/test_config/project_root/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/packages/flutter_test/test/test_config/project_root/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/packages/flutter_test/test/test_config/project_root/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/packages/flutter_tools/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/packages/flutter_tools/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/packages/flutter_tools/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/packages/flutter_web_plugins/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/packages/flutter_web_plugins/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/packages/flutter_web_plugins/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/packages/fuchsia_remote_debug_protocol/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/packages/fuchsia_remote_debug_protocol/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/packages/fuchsia_remote_debug_protocol/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/packages/integration_test/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/packages/integration_test/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/packages/integration_test/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/packages/integration_test/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/packages/integration_test/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/packages/integration_test/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/packages/integration_test/integration_test_macos/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/packages/integration_test/integration_test_macos/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/.fvm/versions/3.27.2/packages/integration_test/integration_test_macos/build" />
      <excludeFolder url="file://$MODULE_DIR$/app/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/app/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/app/build" />
      <excludeFolder url="file://$MODULE_DIR$/app/chatio_plugin/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/app/chatio_plugin/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/app/chatio_plugin/build" />
      <excludeFolder url="file://$MODULE_DIR$/app/flutter_ios_voip_kit/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/app/flutter_ios_voip_kit/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/app/flutter_ios_voip_kit/build" />
      <excludeFolder url="file://$MODULE_DIR$/app/image_crop/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/app/image_crop/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/app/image_crop/build" />
      <excludeFolder url="file://$MODULE_DIR$/app/security_keyboard_plugin/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/app/security_keyboard_plugin/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/app/security_keyboard_plugin/build" />
      <excludeFolder url="file://$MODULE_DIR$/app/security_keyboard_plugin/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/app/security_keyboard_plugin/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/app/security_keyboard_plugin/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/audioplayers_windows/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/audioplayers_windows/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/audioplayers_windows/build" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/connectivity_plus/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/connectivity_plus/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/connectivity_plus/build" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/connectivity_plus/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/connectivity_plus/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/connectivity_plus/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/device_info_plus/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/device_info_plus/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/device_info_plus/build" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/device_info_plus/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/device_info_plus/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/device_info_plus/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/emoji_picker_flutter/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/emoji_picker_flutter/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/emoji_picker_flutter/build" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/emoji_picker_flutter/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/emoji_picker_flutter/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/emoji_picker_flutter/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/file_picker/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/file_picker/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/file_picker/build" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/file_picker/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/file_picker/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/file_picker/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/file_selector_windows/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/file_selector_windows/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/file_selector_windows/build" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/file_selector_windows/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/file_selector_windows/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/file_selector_windows/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/firebase_core/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/firebase_core/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/firebase_core/build" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/firebase_core/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/firebase_core/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/firebase_core/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/flutter_inappwebview_windows/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/flutter_inappwebview_windows/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/flutter_inappwebview_windows/build" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/flutter_inappwebview_windows/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/flutter_inappwebview_windows/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/flutter_inappwebview_windows/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/flutter_keyboard_visibility_windows/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/flutter_keyboard_visibility_windows/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/flutter_keyboard_visibility_windows/build" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/flutter_localization/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/flutter_localization/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/flutter_localization/build" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/flutter_localization/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/flutter_localization/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/flutter_localization/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/flutter_secure_storage_windows/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/flutter_secure_storage_windows/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/flutter_secure_storage_windows/build" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/flutter_webrtc/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/flutter_webrtc/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/flutter_webrtc/build" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/flutter_webrtc/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/flutter_webrtc/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/flutter_webrtc/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/image_picker_windows/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/image_picker_windows/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/image_picker_windows/build" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/image_picker_windows/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/image_picker_windows/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/image_picker_windows/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/local_auth_windows/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/local_auth_windows/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/local_auth_windows/build" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/local_auth_windows/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/local_auth_windows/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/local_auth_windows/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/package_info_plus/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/package_info_plus/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/package_info_plus/build" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/package_info_plus/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/package_info_plus/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/package_info_plus/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/path_provider_windows/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/path_provider_windows/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/path_provider_windows/build" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/path_provider_windows/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/path_provider_windows/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/path_provider_windows/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/permission_handler_windows/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/permission_handler_windows/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/permission_handler_windows/build" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/permission_handler_windows/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/permission_handler_windows/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/permission_handler_windows/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/record_windows/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/record_windows/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/record_windows/build" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/share_plus/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/share_plus/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/share_plus/build" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/share_plus/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/share_plus/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/share_plus/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/shared_preferences_windows/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/shared_preferences_windows/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/shared_preferences_windows/build" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/shared_preferences_windows/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/shared_preferences_windows/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/shared_preferences_windows/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/sqlcipher_flutter_libs/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/sqlcipher_flutter_libs/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/sqlcipher_flutter_libs/build" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/sqlcipher_flutter_libs/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/sqlcipher_flutter_libs/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/sqlcipher_flutter_libs/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/url_launcher_windows/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/url_launcher_windows/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/url_launcher_windows/build" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/url_launcher_windows/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/url_launcher_windows/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/url_launcher_windows/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/wakelock_plus/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/wakelock_plus/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/wakelock_plus/build" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/wakelock_plus/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/wakelock_plus/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/app/windows/flutter/ephemeral/.plugin_symlinks/wakelock_plus/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_browser/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_browser/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_browser/build" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_wallet_eth/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_wallet_eth/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_wallet_eth/build" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_wallet_eth/linux/flutter/ephemeral/.plugin_symlinks/device_info_plus/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_wallet_eth/linux/flutter/ephemeral/.plugin_symlinks/device_info_plus/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_wallet_eth/linux/flutter/ephemeral/.plugin_symlinks/device_info_plus/build" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_wallet_eth/linux/flutter/ephemeral/.plugin_symlinks/device_info_plus/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_wallet_eth/linux/flutter/ephemeral/.plugin_symlinks/device_info_plus/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_wallet_eth/linux/flutter/ephemeral/.plugin_symlinks/device_info_plus/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_wallet_eth/linux/flutter/ephemeral/.plugin_symlinks/flutter_localization/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_wallet_eth/linux/flutter/ephemeral/.plugin_symlinks/flutter_localization/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_wallet_eth/linux/flutter/ephemeral/.plugin_symlinks/flutter_localization/build" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_wallet_eth/linux/flutter/ephemeral/.plugin_symlinks/flutter_localization/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_wallet_eth/linux/flutter/ephemeral/.plugin_symlinks/flutter_localization/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_wallet_eth/linux/flutter/ephemeral/.plugin_symlinks/flutter_localization/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_wallet_eth/linux/flutter/ephemeral/.plugin_symlinks/flutter_secure_storage_linux/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_wallet_eth/linux/flutter/ephemeral/.plugin_symlinks/flutter_secure_storage_linux/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_wallet_eth/linux/flutter/ephemeral/.plugin_symlinks/flutter_secure_storage_linux/build" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_wallet_eth/linux/flutter/ephemeral/.plugin_symlinks/path_provider_linux/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_wallet_eth/linux/flutter/ephemeral/.plugin_symlinks/path_provider_linux/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_wallet_eth/linux/flutter/ephemeral/.plugin_symlinks/path_provider_linux/build" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_wallet_eth/linux/flutter/ephemeral/.plugin_symlinks/path_provider_linux/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_wallet_eth/linux/flutter/ephemeral/.plugin_symlinks/path_provider_linux/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_wallet_eth/linux/flutter/ephemeral/.plugin_symlinks/path_provider_linux/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_wallet_eth/linux/flutter/ephemeral/.plugin_symlinks/share_plus/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_wallet_eth/linux/flutter/ephemeral/.plugin_symlinks/share_plus/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_wallet_eth/linux/flutter/ephemeral/.plugin_symlinks/share_plus/build" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_wallet_eth/linux/flutter/ephemeral/.plugin_symlinks/share_plus/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_wallet_eth/linux/flutter/ephemeral/.plugin_symlinks/share_plus/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_wallet_eth/linux/flutter/ephemeral/.plugin_symlinks/share_plus/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_wallet_eth/linux/flutter/ephemeral/.plugin_symlinks/shared_preferences_linux/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_wallet_eth/linux/flutter/ephemeral/.plugin_symlinks/shared_preferences_linux/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_wallet_eth/linux/flutter/ephemeral/.plugin_symlinks/shared_preferences_linux/build" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_wallet_eth/linux/flutter/ephemeral/.plugin_symlinks/shared_preferences_linux/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_wallet_eth/linux/flutter/ephemeral/.plugin_symlinks/shared_preferences_linux/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_wallet_eth/linux/flutter/ephemeral/.plugin_symlinks/shared_preferences_linux/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_wallet_eth/linux/flutter/ephemeral/.plugin_symlinks/sqlcipher_flutter_libs/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_wallet_eth/linux/flutter/ephemeral/.plugin_symlinks/sqlcipher_flutter_libs/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_wallet_eth/linux/flutter/ephemeral/.plugin_symlinks/sqlcipher_flutter_libs/build" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_wallet_eth/linux/flutter/ephemeral/.plugin_symlinks/sqlcipher_flutter_libs/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_wallet_eth/linux/flutter/ephemeral/.plugin_symlinks/sqlcipher_flutter_libs/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_wallet_eth/linux/flutter/ephemeral/.plugin_symlinks/sqlcipher_flutter_libs/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_wallet_eth/linux/flutter/ephemeral/.plugin_symlinks/url_launcher_linux/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_wallet_eth/linux/flutter/ephemeral/.plugin_symlinks/url_launcher_linux/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_wallet_eth/linux/flutter/ephemeral/.plugin_symlinks/url_launcher_linux/build" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_wallet_eth/linux/flutter/ephemeral/.plugin_symlinks/url_launcher_linux/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_wallet_eth/linux/flutter/ephemeral/.plugin_symlinks/url_launcher_linux/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_wallet_eth/linux/flutter/ephemeral/.plugin_symlinks/url_launcher_linux/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_wallet_eth/windows/flutter/ephemeral/.plugin_symlinks/device_info_plus/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_wallet_eth/windows/flutter/ephemeral/.plugin_symlinks/device_info_plus/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_wallet_eth/windows/flutter/ephemeral/.plugin_symlinks/device_info_plus/build" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_wallet_eth/windows/flutter/ephemeral/.plugin_symlinks/device_info_plus/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_wallet_eth/windows/flutter/ephemeral/.plugin_symlinks/device_info_plus/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_wallet_eth/windows/flutter/ephemeral/.plugin_symlinks/device_info_plus/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_wallet_eth/windows/flutter/ephemeral/.plugin_symlinks/flutter_inappwebview_windows/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_wallet_eth/windows/flutter/ephemeral/.plugin_symlinks/flutter_inappwebview_windows/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_wallet_eth/windows/flutter/ephemeral/.plugin_symlinks/flutter_inappwebview_windows/build" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_wallet_eth/windows/flutter/ephemeral/.plugin_symlinks/flutter_inappwebview_windows/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_wallet_eth/windows/flutter/ephemeral/.plugin_symlinks/flutter_inappwebview_windows/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_wallet_eth/windows/flutter/ephemeral/.plugin_symlinks/flutter_inappwebview_windows/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_wallet_eth/windows/flutter/ephemeral/.plugin_symlinks/flutter_localization/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_wallet_eth/windows/flutter/ephemeral/.plugin_symlinks/flutter_localization/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_wallet_eth/windows/flutter/ephemeral/.plugin_symlinks/flutter_localization/build" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_wallet_eth/windows/flutter/ephemeral/.plugin_symlinks/flutter_localization/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_wallet_eth/windows/flutter/ephemeral/.plugin_symlinks/flutter_localization/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_wallet_eth/windows/flutter/ephemeral/.plugin_symlinks/flutter_localization/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_wallet_eth/windows/flutter/ephemeral/.plugin_symlinks/flutter_secure_storage_windows/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_wallet_eth/windows/flutter/ephemeral/.plugin_symlinks/flutter_secure_storage_windows/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_wallet_eth/windows/flutter/ephemeral/.plugin_symlinks/flutter_secure_storage_windows/build" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_wallet_eth/windows/flutter/ephemeral/.plugin_symlinks/path_provider_windows/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_wallet_eth/windows/flutter/ephemeral/.plugin_symlinks/path_provider_windows/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_wallet_eth/windows/flutter/ephemeral/.plugin_symlinks/path_provider_windows/build" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_wallet_eth/windows/flutter/ephemeral/.plugin_symlinks/path_provider_windows/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_wallet_eth/windows/flutter/ephemeral/.plugin_symlinks/path_provider_windows/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_wallet_eth/windows/flutter/ephemeral/.plugin_symlinks/path_provider_windows/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_wallet_eth/windows/flutter/ephemeral/.plugin_symlinks/permission_handler_windows/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_wallet_eth/windows/flutter/ephemeral/.plugin_symlinks/permission_handler_windows/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_wallet_eth/windows/flutter/ephemeral/.plugin_symlinks/permission_handler_windows/build" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_wallet_eth/windows/flutter/ephemeral/.plugin_symlinks/permission_handler_windows/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_wallet_eth/windows/flutter/ephemeral/.plugin_symlinks/permission_handler_windows/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_wallet_eth/windows/flutter/ephemeral/.plugin_symlinks/permission_handler_windows/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_wallet_eth/windows/flutter/ephemeral/.plugin_symlinks/share_plus/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_wallet_eth/windows/flutter/ephemeral/.plugin_symlinks/share_plus/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_wallet_eth/windows/flutter/ephemeral/.plugin_symlinks/share_plus/build" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_wallet_eth/windows/flutter/ephemeral/.plugin_symlinks/share_plus/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_wallet_eth/windows/flutter/ephemeral/.plugin_symlinks/share_plus/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_wallet_eth/windows/flutter/ephemeral/.plugin_symlinks/share_plus/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_wallet_eth/windows/flutter/ephemeral/.plugin_symlinks/shared_preferences_windows/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_wallet_eth/windows/flutter/ephemeral/.plugin_symlinks/shared_preferences_windows/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_wallet_eth/windows/flutter/ephemeral/.plugin_symlinks/shared_preferences_windows/build" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_wallet_eth/windows/flutter/ephemeral/.plugin_symlinks/shared_preferences_windows/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_wallet_eth/windows/flutter/ephemeral/.plugin_symlinks/shared_preferences_windows/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_wallet_eth/windows/flutter/ephemeral/.plugin_symlinks/shared_preferences_windows/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_wallet_eth/windows/flutter/ephemeral/.plugin_symlinks/sqlcipher_flutter_libs/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_wallet_eth/windows/flutter/ephemeral/.plugin_symlinks/sqlcipher_flutter_libs/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_wallet_eth/windows/flutter/ephemeral/.plugin_symlinks/sqlcipher_flutter_libs/build" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_wallet_eth/windows/flutter/ephemeral/.plugin_symlinks/sqlcipher_flutter_libs/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_wallet_eth/windows/flutter/ephemeral/.plugin_symlinks/sqlcipher_flutter_libs/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_wallet_eth/windows/flutter/ephemeral/.plugin_symlinks/sqlcipher_flutter_libs/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_wallet_eth/windows/flutter/ephemeral/.plugin_symlinks/url_launcher_windows/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_wallet_eth/windows/flutter/ephemeral/.plugin_symlinks/url_launcher_windows/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_wallet_eth/windows/flutter/ephemeral/.plugin_symlinks/url_launcher_windows/build" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_wallet_eth/windows/flutter/ephemeral/.plugin_symlinks/url_launcher_windows/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_wallet_eth/windows/flutter/ephemeral/.plugin_symlinks/url_launcher_windows/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_wallet_eth/windows/flutter/ephemeral/.plugin_symlinks/url_launcher_windows/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_wallet_trx/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_wallet_trx/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_wallet_trx/build" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_wallet_trx/plugin/dart_tron/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_wallet_trx/plugin/dart_tron/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/flutter_wallet_trx/plugin/dart_tron/build" />
      <excludeFolder url="file://$MODULE_DIR$/http/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/http/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/http/build" />
      <excludeFolder url="file://$MODULE_DIR$/pro_image_editor/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/pro_image_editor/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/pro_image_editor/build" />
    </content>
    <orderEntry type="inheritedJdk" />
    <orderEntry type="sourceFolder" forTests="false" />
  </component>
</module>