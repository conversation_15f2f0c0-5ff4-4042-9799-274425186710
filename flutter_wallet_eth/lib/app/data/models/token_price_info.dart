



/// 价格信息模型：price 表示 1 token = x 价格(eg. WK,U)；change24h 为 24h 涨跌幅（百分比）
class TokenPriceInfo {
  final String selfUnit;
  final double priceInUnit;
  final String unit;
  final double? change24h;
  final double? high24h;
  final double? low24h;
  final double? priceInCurrency;
  final String? currency;
  final String? key;  // 用于显示chart
  const TokenPriceInfo({required this.priceInUnit, required this.unit, required this.selfUnit, this.change24h, this.high24h, this.low24h, this.priceInCurrency, this.currency, this.key});

  TokenPriceInfo updatePriceInCurrency(double priceInCurr, String curr) {
    return TokenPriceInfo(priceInUnit: priceInUnit, unit: unit, selfUnit: selfUnit, change24h: change24h, high24h: high24h, low24h: low24h, priceInCurrency: priceInCurr, currency: curr, key: key);
  }
}