class RecentTransactionAddress {
  final String address;
  final String chainId;
  final DateTime timestamp;

  RecentTransactionAddress({
    required this.address,
    required this.chainId,
    required this.timestamp,
  });

  Map<String, dynamic> toJson() => {
    'address': address,
    'chain_id': chainId,
    'timestamp': timestamp.toIso8601String(),
  };

  factory RecentTransactionAddress.fromJson(Map<String, dynamic> json) {
    return RecentTransactionAddress(
      address: json['address'],
      chainId: json['chain_id'],
      timestamp: DateTime.parse(json['timestamp']),
    );
  }
}
