import 'dart:convert';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:dio/io.dart';
import 'package:flutter_web3/app/core/utils/util.dart';
import 'package:flutter_web3/app/core/values/config.dart';
import 'package:flutter_web3/app/data/models/token_price_info.dart';

class TokenPriceService {
  Dio getDio() {
    Dio dio = Dio();
    dio.options.connectTimeout = const Duration(seconds: 30);
    dio.options.sendTimeout = const Duration(seconds: 30);

    dio.httpClientAdapter = IOHttpClientAdapter(
      createHttpClient: () {
        final client = HttpClient();
        client.findProxy = null;
        return client;
      },
    );
    return dio;
  }
  
  Future<Map<String, TokenPriceInfo>?> fetchPriceInfoOnKChain() async {
    try {
      final resp = await getDio().get('https://kchartv2.linksay.io/public/tokenList');
      if (resp.statusCode != 200 || resp.data == null) return null;
      dynamic body = resp.data;
      if (body is String) {
        body = json.decode(body);
      }
      final list = (body['data']?['tokenList'] as List?) ?? [];
      if(list.isEmpty) return null;
      // 构建地址->价格的映射到临时 Map
      final Map<String, TokenPriceInfo> _tmp = <String, TokenPriceInfo>{};

      for (final item in list) {
        try {
          final name0 = item['tokenName0']?.toString().toUpperCase();
          final name1 = item['tokenName1']?.toString().toUpperCase();
          final addr0 = item['address0']?.toString().toLowerCase();
          final addr1 = item['address1']?.toString().toLowerCase();
          final p01 = (item['currentPrice01'] as num?)?.toDouble(); // 0 -> 1
          final p10 = (item['currentPrice10'] as num?)?.toDouble(); // 1 -> 0
          final ch01 = _parseChange(item['change24h01']);
          final ch10 = _parseChange(item['change24h10']);
          final high01 = (item['high24h01'] as num?)?.toDouble();
          final high10 = (item['high24h10'] as num?)?.toDouble();
          final low01 = (item['low24h01'] as num?)?.toDouble();
          final low10 = (item['low24h10'] as num?)?.toDouble();
          final key = item['key']?.toString();

          if(name0 == null || name1 == null || addr0 == null || addr1 == null || p01 == null 
            || p10 == null || ch01 == null || ch10 == null || high01 == null || high10 == null 
            || low01 == null || low10 == null || addr0.isEmpty || addr1.isEmpty) continue;

          // 归一到 “1枚该币 = x 枚WK”
          if (name0 == 'WK') {
            if(name1 != 'USDT') {
              _tmp[addr1] = TokenPriceInfo(selfUnit: name1, priceInUnit: p10, unit: name0, change24h: ch10, high24h: high10, low24h: low10, key: key); // 其他币种兑换为 WK
            }
          } else if (name1 == 'WK') {
            if(name0 != 'USDT') {
              _tmp[addr0] = TokenPriceInfo(selfUnit: name0, priceInUnit: p01, unit: name1, change24h: ch01, high24h: high01, low24h: low01, key: key); // 其他币种兑换为 WK
            } else {
              // 当name0=usdt & name1=wk
              // 添加WK->USDT & USDT->WK的价格信息
              _tmp[addr1] = TokenPriceInfo(selfUnit: name1, priceInUnit: p10, unit: name0, change24h: ch10, high24h: high10, low24h: low10, key: key); // WK兑换为USDT
              _tmp[addr0] = TokenPriceInfo(selfUnit: name0, priceInUnit: p01, unit: name1, change24h: ch01); // USDT兑换为WK，不存key因为不显示chart
            }
          }
        } catch (e) {
          walletLog('fetchPriceInfoOnKChain parse tokenList item error: $e');
        }
      }
      return _tmp;
    } catch (e) {
      walletLog('fetchPriceInfoOnKChain error: $e');
      return null;
    }
  }

  Future<Map<String, TokenPriceInfo>?> fetchPriceInfoOnBscChain() async {
    try {
      final resp = await getDio().post('https://mfapi.okai.systems/api/OKAI/getCoinValue');
      if (resp.statusCode != 200 || resp.data == null) return null;
      dynamic body = resp.data;
      if (body is String) {
        body = json.decode(body);
      }
      if(body["status"] == false) return null;
      var data = body["data"];
      if(data == null || data.isEmpty) return null;

      final Map<String, TokenPriceInfo> _tmp = <String, TokenPriceInfo>{};
      for(String key in data.keys){
        double? priceInUnit = double.tryParse(data[key]);
        if(priceInUnit == null) continue;
        if(key.toUpperCase() == "KCOIN"){
          _tmp["k"] = TokenPriceInfo(selfUnit: "K", priceInUnit: priceInUnit, unit: "USDT", priceInCurrency: priceInUnit, currency: CurrencySymbol.USD);  
          continue;
        }
        _tmp[key.toLowerCase()] = TokenPriceInfo(selfUnit: key, priceInUnit: priceInUnit, unit: "USDT", priceInCurrency: priceInUnit, currency: CurrencySymbol.USD);
      }
      return _tmp;
    } catch (e) {
      walletLog('fetchPriceInfoOnBscChain error: $e');
      return null;
    }
  }  
  
  double? _parseChange(dynamic v) {
    if (v == null) return null;
    if (v is num) return v.toDouble();
    var s = v.toString().trim();
    if (s.isEmpty) return null;
    if (s.endsWith('%')) s = s.substring(0, s.length - 1);
    s = s.replaceAll('+', '');
    // s = s.replaceAll('-', '');
    return double.tryParse(s);
  }
}