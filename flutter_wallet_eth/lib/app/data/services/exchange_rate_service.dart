import 'dart:convert';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:dio/io.dart';
import 'package:flutter_web3/app/core/utils/util.dart';
import 'package:flutter_web3/app/core/values/config.dart';
import 'package:shared_preferences/shared_preferences.dart';

class ExchangeRateService {
  static const String _baseUrl =
      'https://api.bnm.gov.my/public/exchange-rate';
  static const String _acceptHeader = 'application/vnd.BNM.API.v1+json';

  static const List<String> _supported = [CurrencySymbol.USD, CurrencySymbol.SGD, CurrencySymbol.CNY, CurrencySymbol.HKD];

  static const String _storagePrefix = 'exchange_rate_';

  Dio getDio() {
    Dio dio = Dio();
    dio.options.connectTimeout = const Duration(seconds: 10);
    dio.options.sendTimeout = const Duration(seconds: 10);
    dio.options.headers = {'Accept': _acceptHeader};

    dio.httpClientAdapter = IOHttpClientAdapter(
      createHttpClient: () {
        final client = HttpClient();
        client.findProxy = null;
        return client;
      },
    );
    return dio;
  }


  /// 1. 根据当前时间判定时间段并获取汇率
  Future<Map<String, double>?> getCurrentExchangeRates() async {
    // 时间段判定
    var targetDateAndSession = _getTargetDateAndSession();
    var rates;
    if(targetDateAndSession != null) {
      final session = targetDateAndSession['timeSlot'] as String;
      final date = targetDateAndSession['date'] as DateTime;
      // 获取当前时间段的汇率
      rates = await _getExchangeRates(session, date);
    }

    // 若取不到就取本地保存的最新数据(若有)
    if (rates == null) {
      rates = await _getLatestLocalRates();
    }

    return rates;
  }

  /// 2. USD转换为其他货币 (对外公开的方法)
  /// 使用已获取的rates批量/单次换算USD到目标货币，避免重复请求
  double? convertUSDToCurrencyWithRates(
      double usdAmount, String targetCurrency, Map<String, double> rates) {
    if (usdAmount <= 0) return 0.0;
    if (targetCurrency == CurrencySymbol.USD) return usdAmount;
    if (!rates.containsKey(CurrencySymbol.USD)) return null;

    final usdToMyrRate = rates[CurrencySymbol.USD]!;
    if (targetCurrency == CurrencySymbol.MYR) {
      return usdAmount * usdToMyrRate;
    }
    if (!rates.containsKey(targetCurrency)) {
      return null;
    }
    final targetToMyrRate = rates[targetCurrency]!;
    if(targetToMyrRate.isInfinite || targetToMyrRate == 0.0 || targetToMyrRate.isNaN) return null;
    return (usdAmount * usdToMyrRate) / targetToMyrRate;
  }

  /// 获取指定时间段的汇率数据
  /// 传入session (0900/1200/1700) 和日期
  /// 先查本地数据，没有才调用接口
  Future<Map<String, double>?> _getExchangeRates(String session, DateTime targetDate) async {
    try {
      final String dateStr = _formatDate(targetDate);

      // 先查本地数据
      final localRates = await _getLocalRates(dateStr, session);
      if (localRates != null && localRates.isNotEmpty) {
        return localRates;
      }

      // 本地没有数据，调用接口
      final apiResponse = await _fetchRatesFromAPI();
      if (apiResponse != null && apiResponse['rates'] != null && (apiResponse['rates'] as Map<String, double>).isNotEmpty) {
        final apiRates = apiResponse['rates'] as Map<String, double>;
        final responseData = apiResponse['response_data'];
        final respSession = responseData['meta']?['session']?.toString();
        final respDate = responseData['data']?[0]['rate']?['date']?.toString();
        if(respSession == null || respDate == null) return null;
        await _saveLocalRates(respDate, respSession, apiRates);
        return apiRates;
      }

      return null;
    } catch (e) {
      walletLog('_getExchangeRates Error getting exchange rates: $e');
      return null;
    }
  }

  /// 从API获取汇率数据
  /// 返回包含rates和原始response数据的Map，用于后续验证
  Future<Map<String, dynamic>?> _fetchRatesFromAPI() async {
    try {
      final dio = getDio();
      String url = _baseUrl;
      if(_isInSpecialTimeWindow()){
        url = "$url?session=0900&quote=rm";
      }
      final response =  await dio.get('$url');
      if (response.statusCode == 200 && response.data != null) {
        final data = response.data;

        // 检查是否有数据
        if (data['data'] == null || (data['data'] as List).isEmpty) {
          return null;
        }

        final List<dynamic> ratesList = data['data'];
        final Map<String, double> rates = {};

        for (final item in ratesList) {
          final String currencyCode = item['currency_code'];

          // 只处理支持的货币
          if (_supported.contains(currencyCode)) {
            final int unit = item['unit'] ?? 1;
            final double middleRate = (item['rate']['middle_rate'] ?? 0.0).toDouble();

            // 统一unit为1
            final double normalizedRate = middleRate / unit;
            rates[currencyCode] = normalizedRate;
          }
        }

        if (rates.isNotEmpty) {
          return {
            'rates': rates,
            'response_data': data, // 保留原始响应数据用于date&session的保存
          };
        }
      }

      return null;
    } catch (e) {
      walletLog('_fetchRatesFromAPI Error fetching rates from API: $e');
      return null;
    }
  }

  /// 从本地获取汇率数据
  Future<Map<String, double>?> _getLocalRates(String date, String session) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final key = '$_storagePrefix${date}_$session';
      final jsonStr = prefs.getString(key);

      if (jsonStr != null) {
        final Map<String, dynamic> data = json.decode(jsonStr);
        return data.map((key, value) => MapEntry(key, (value as num).toDouble()));
      }

      return null;
    } catch (e) {
      walletLog('_getLocalRates Error getting local rates: $e');
      return null;
    }
  }

  /// 从本地获取<最新的>汇率数据
  Future<Map<String, double>?> _getLatestLocalRates() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs
          .getKeys()
          .where((k) => k.startsWith(_storagePrefix))
          .toList()
          ..sort((a, b) {
            final dateA = DateTime.parse(
              a.split("exchange_rate_")[1].replaceAll("_", "T"),
            );
            final dateB = DateTime.parse(
              b.split("exchange_rate_")[1].replaceAll("_", "T"),
            );
            return dateA.compareTo(dateB);
          }); // 按键排序

      walletLog('_getLatestLocalRates keys: $keys');

      if (keys.isNotEmpty) {
        final latestKey = keys.last;
        final jsonStr = prefs.getString(latestKey);
        if (jsonStr != null) {
          final Map<String, dynamic> data = json.decode(jsonStr);
          walletLog('_getLatestLocalRates latest rates: $latestKey => $data');
          return data.map((key, value) => MapEntry(key, (value as num).toDouble()));
        }
      }

      return null;
    } catch (e) {
      walletLog('_getLatestLocalRates Error getting latest local rates: $e');
      return null;
    }
  }

  /// 保存汇率数据到本地存储
  Future<void> _saveLocalRates(String date, String session, Map<String, double> rates) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final key = '$_storagePrefix${date}_$session';
      final jsonStr = json.encode(rates);
      await prefs.setString(key, jsonStr);
    } catch (e) {
      walletLog('_saveLocalRates Error saving local rates: $e');
    }
  }

  /// 清理过期的本地数据 (只保留4天的数据)
  Future<void> cleanupOldData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs
          .getKeys()
          .where((k) => k.startsWith(_storagePrefix))
          .toList();
      final now = DateTime.now();

      for (final key in keys) {
        // 提取日期部分
        final parts = key.replaceFirst(_storagePrefix, '').split('_');
        final dateStr = parts[0];
        try {
          final date = DateTime.parse(dateStr);
          final daysDiff = now.difference(date).inDays;

          // 删除超过4天的数据
          if (daysDiff > 3) {
            await prefs.remove(key);
          }
        } catch (e) {
          // 如果日期解析失败，删除该键
          await prefs.remove(key);
        }     
      }
    } catch (e) {
      walletLog('_cleanupOldData Error cleaning up old data: $e');
    }
  }

  /// 格式化日期为 YYYY-MM-DD
  String _formatDate(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }

  /// 根据现在的时间判断该获取数据的时间段
  /// 1）00:00-08:59 → 昨天的1700； 
  /// 2）09:00–11:59 → 0900; 
  /// 3）12:00–16:59 → 1200； 
  /// 4）17:00–23:59 → 1700； 
  /// 5) 特别情况：周六, 周日, 周一 before 09:00, 都返回上周五的1700
  Map<String, dynamic>? _getTargetDateAndSession() {
    try {
      final now = DateTime.now();
      final weekday = now.weekday; // Monday=1, Sunday=7

      DateTime date;
      String timeSlot;

      // 特殊情况：Saturday(6), Sunday(7), Monday(1) < 09:00
      if (weekday == DateTime.saturday ||
          weekday == DateTime.sunday ||
          (weekday == DateTime.monday && now.hour < 9)) {
        // 找到上周五 17:00
        final daysToSubtract = weekday == DateTime.saturday
            ? 1
            : (weekday == DateTime.sunday ? 2 : 3);
        final friday = now.subtract(Duration(days: daysToSubtract));
        date = DateTime(friday.year, friday.month, friday.day);
        timeSlot = "1700";
      } else if (now.hour < 9) {
        // 00:00 - 08:59 → 昨天 17:00
        final yesterday = now.subtract(const Duration(days: 1));
        date = DateTime(yesterday.year, yesterday.month, yesterday.day);
        timeSlot = "1700";
      } else if (now.hour < 12) {
        // 09:00 - 11:59 → 09:00
        date = DateTime(now.year, now.month, now.day);
        timeSlot = "0900";
      } else if (now.hour < 17) {
        // 12:00 - 16:59 → 12:00
        date = DateTime(now.year, now.month, now.day);
        timeSlot = "1200";
      } else {
        // 17:00 - 23:59 → 17:00
        date = DateTime(now.year, now.month, now.day);
        timeSlot = "1700";
      }
      walletLog('_getTargetDateAndSession date: $date, timeSlot: $timeSlot');

      return {
        "date": date,
        "timeSlot": timeSlot,
      };
    } catch (e) {
      walletLog("_getTargetDateAndSession Error: $e");
      return null;
    }
  }

  /// 判定当前时间是否为11:30-11:59且不是周六、周日
  /// 
  /// 当为此区间，调用带session参数的接口, 并查询当天0900的汇率
  /// 
  /// eg. exchange-rate?session=0900&quote=rm
  bool _isInSpecialTimeWindow() {
    final now = DateTime.now();
    final weekday = now.weekday;
    final hour = now.hour;
    final minute = now.minute;

    // 检查是否为周六(6)或周日(7)
    if (weekday == DateTime.saturday || weekday == DateTime.sunday) {
      return false;
    }

    // 检查是否为11:30-11:59
    if (hour == 11 && minute >= 30) {
      return true;
    }

    return false;
  }

  /// 清理所有本地数据
  // Future<void> cleanAllData() async {
  //   try {
  //     final prefs = await SharedPreferences.getInstance();
  //     final keys = prefs
  //         .getKeys()
  //         .where((k) => k.startsWith(_storagePrefix))
  //         .toList();

  //     for (final key in keys) {
  //       await prefs.remove(key);  
  //     }
  //   } catch (e) {
  //     walletLog('cleanAllData Error cleaning all data: $e');
  //   }
  // }

  // /// 验证API响应数据
  // /// 1. 确保返回的数据data.rate.date和targetDate一致
  // /// 2. 确保返回的数据meta.session和session一致
  // bool _validateApiResponse(Map<String, dynamic> responseData, DateTime targetDate, String session) {
  //   try {
  //     final String expectedDateStr = _formatDate(targetDate);

  //     // 验证meta.session
  //     final metaSession = responseData['meta']?['session']?.toString();
  //     if (metaSession != session) {
  //       walletLog('_validateApiResponse session mismatch: expected=$session, actual=$metaSession');
  //       return false;
  //     }

  //     // 验证data中每个汇率的date
  //     final List<dynamic>? dataList = responseData['data'];
  //     if (dataList == null || dataList.isEmpty) {
  //       walletLog('_validateApiResponse no data found');
  //       return false;
  //     }

  //     final item = dataList[0];
  //     final String? rateDate = item['rate']?['date']?.toString();
  //     if (rateDate != expectedDateStr) {
  //       walletLog('_validateApiResponse date mismatch: expected=$expectedDateStr, actual=$rateDate');
  //       return false;
  //     }

  //     walletLog('_validateApiResponse validation passed: date=$expectedDateStr, session=$session');
  //     return true;
  //   } catch (e) {
  //     walletLog('_validateApiResponse validation error: $e');
  //     return false;
  //   }
  // }

  // /// 获取上一个时间段
  // Map<String, dynamic> _getPreviousSession(String currentSession, bool isCurrentYesterday) {
  //   switch (currentSession) {
  //     case '0900':
  //       return {'session': '1700', 'isYesterday': true};
  //     case '1200':
  //       return {'session': '0900', 'isYesterday': isCurrentYesterday};
  //     case '1700':
  //       return {'session': '1200', 'isYesterday': isCurrentYesterday};
  //     default:
  //       return {'session': '1700', 'isYesterday': true};
  //   }
  // }

  /// 开发验证使用
  // /// 获取所有本地已保存的汇率数据并打印出来
  // /// 返回：key 为 'YYYY-MM-DD_session'，value 为 该时段的 {currency: rate}
  // Future<void> getAllLocalRatesAndPrint() async {
  //   try {
  //     final prefs = await SharedPreferences.getInstance();
  //     final keys = prefs
  //         .getKeys()
  //         .where((k) => k.startsWith(_storagePrefix))
  //         .toList()
  //       ..sort(); // 按键排序，便于阅读

  //     walletLog('==== Local Exchange Rates (${keys.length}) ====');

  //     for (final key in keys) {
  //       try {
  //         final raw = prefs.getString(key);
  //         if (raw == null) continue;
  //         final Map<String, dynamic> decoded = json.decode(raw);
  //         final rates = decoded.map((k, v) => MapEntry(k, (v as num).toDouble()));
  //         walletLog('==== Local Exchange Rates [key=$key] => $rates');
  //       } catch (e) {
  //         walletLog('getAllLocalRatesAndPrint parse error for key=$key, e=$e');
  //       }
  //     }

  //     walletLog('==== Local Exchange Rates End ====');
  //   } catch (e) {
  //     walletLog('getAllLocalRatesAndPrint error: $e');
  //   }
  // }
}