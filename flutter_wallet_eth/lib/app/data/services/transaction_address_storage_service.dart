import 'dart:convert';

import 'package:shared_preferences/shared_preferences.dart';

import '../models/recent_transaction_address.dart';

class TransactionAddressStorageService {
  static const String _key = "recent_transaction_address";
  static const Duration _expireDuration = Duration(days: 14);

  static final TransactionAddressStorageService _instance =
      TransactionAddressStorageService._internal();

  factory TransactionAddressStorageService() => _instance;

  TransactionAddressStorageService._internal();

  Future<List<RecentTransactionAddress>> _loadRecords() async {
    final prefs = await SharedPreferences.getInstance();
    final data = prefs.getStringList(_key) ?? [];
    return data
        .map((e) => RecentTransactionAddress.fromJson(jsonDecode(e)))
        .toList();
  }

  Future<void> _saveRecords(List<RecentTransactionAddress> records) async {
    final prefs = await SharedPreferences.getInstance();
    final data = records.map((r) => jsonEncode(r.toJson())).toList();
    await prefs.setStringList(_key, data);
  }

  /// 添加或更新一条记录
  Future<void> addRecord(String address, String chainId) async {
    final records = await _loadRecords();
    final now = DateTime.now();

    final normalizedAddress = address.toLowerCase();

    // 移除相同地址的旧记录
    records.removeWhere(
        (r) => r.address == normalizedAddress && r.chainId == chainId);

    // 添加新记录（无需考虑顺序）
    records.add(RecentTransactionAddress(address: normalizedAddress, chainId: chainId, timestamp: now));

    await _saveRecords(records);
  }

  /// 获取最近记录
  Future<List<RecentTransactionAddress>> getRecentRecords(String chainId) async {
    final records = await _loadRecords();
    final filtered =
        records.where((r) => r.chainId == chainId).toList();
    // 最新在前
    filtered.sort((a, b) => b.timestamp.compareTo(a.timestamp));
    return filtered;
  }

  /// 删除过期的记录（>7天）
  Future<void> removeExpiredRecords() async {
    final records = await _loadRecords();
    final now = DateTime.now();
    final filtered = records
        .where((r) => now.difference(r.timestamp) <= _expireDuration)
        .toList();
    await _saveRecords(filtered);
  }

  /// 删除指定地址
  Future<void> removeRecord(String address, String chainId) async {
    final records = await _loadRecords();
    final normalizedAddress = address.toLowerCase();

    records.removeWhere(
        (r) => r.address == normalizedAddress && r.chainId == chainId);

    await _saveRecords(records);
  }

  /// 清除所有记录
  Future<void> clear() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_key);
  }
}