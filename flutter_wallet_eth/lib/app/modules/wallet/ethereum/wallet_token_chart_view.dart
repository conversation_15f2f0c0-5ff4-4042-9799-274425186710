import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_web3/app/core/values/colors.dart';
import 'package:flutter_web3/app/core/values/config.dart';
import 'package:flutter_web3/app/data/models/token_price_info.dart';
import 'package:flutter_web3/app/modules/wallet/ethereum/wallet_controller.dart';
import 'package:flutter_web3/app/widgets/ic_widget.dart';
import 'package:flutter_web3/r.dart';
import 'package:flutter_web3/web3dart/src/utils/typed_data.dart';
import 'package:get/get.dart';

import '../../../data/models/token_model.dart';

class WalletTokenChartView extends StatefulWidget {
  const WalletTokenChartView({super.key, required this.model, this.isMain, required this.symbol, required this.address});
  final TokenDataModel model;
  final bool? isMain;
  final String symbol;
  final String address;

  @override
  State<WalletTokenChartView> createState() => _WalletTokenChartViewState();
}

class _WalletTokenChartViewState extends State<WalletTokenChartView> {
  Timer? _timer;
  final _walletController = Get.find<WalletController>();
  late final uuid;
  late final chainid ;
  TokenPriceInfo? priceData;
  bool isChartLoading = true;
  late final String _key;
  bool webViewHasError = false;
  @override
  void initState() {
    uuid = _walletController.netModel.value.uuid;
    chainid = _walletController.netModel.value.chainid;
    _key = _walletController.isKNet.value ? widget.address : _walletController.isBSCNet.value ? widget.symbol : "";
    // 直接获取当前的数据做展示，避免等待
    priceData = _walletController.getPriceInfoByKey(_key);
    _updatePriceData();
    _timer = Timer.periodic(const Duration(seconds: 15), (_) async {
      _updatePriceData();
    });
    super.initState();
  }

  @override
  void dispose() {
    _timer?.cancel(); // 避免内存泄漏
    super.dispose();
  }

  void _updatePriceData() async {
    await _walletController.fetchPriceOnChain();
    setState(() {
      priceData = _walletController.getPriceInfoByKey(_key);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(height: 10.r),
        // 数据展示
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 20.r),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 图标，Symbol
              Row(
                // mainAxisSize: MainAxisSize.min,
                children: [
                  IcWidget(
                    isNet: false,
                    isMain: widget.isMain ?? false,
                    chainid: chainid,
                    filePath: widget.model.image,
                    symbol: widget.symbol,
                    uuid: uuid,
                    diameter: 30,
                  ),
                  SizedBox(width: 10.r),
                  if(priceData != null)
                    Text(
                      "${priceData!.selfUnit}/${priceData!.unit}",
                      style: TextStyle(
                        fontSize: 19.sp,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  Spacer(),
                  GestureDetector(
                    behavior: HitTestBehavior.opaque,
                    onTap: () {
                      Get.back();
                    },
                    child: Icon(
                      Icons.close,
                      color: WalletColors.colorBlack,
                      size: 20.r,
                    ),
                  ),
                ],
              ),
              SizedBox(height: 8.r),
              // 价格
              if(priceData != null)
                Text(
                  decimalData(priceData!.priceInUnit,6),
                  style: TextStyle(
                    fontSize: 26.sp,
                    fontWeight: FontWeight.w600,
                    color: priceData!.change24h! < 0
                      ? WalletColors.colorFFFF0000
                      : (priceData!.change24h! == 0
                          ? WalletColors.colorFFB2B2B2
                          : WalletColors.colorFF13B755),
                  ),
                ),
              SizedBox(height: 10.r),
              // 信息相关
              if(priceData != null)
                Row(
                  // mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    _buildStateDisplay(
                      "24h Change", 
                      "${priceData!.change24h! > 0 ? '+' : priceData!.change24h! < 0 ? '-' : ''}${_walletController.formatToTwoDecimal(decimalData((priceData!.change24h!).abs(), 2))}%",
                      valueColor: priceData!.change24h! < 0
                      ? WalletColors.colorFFFF0000
                      : (priceData!.change24h! == 0
                          ? WalletColors.colorFFB2B2B2
                          : WalletColors.colorFF13B755),
                    ),
                    SizedBox(width: 40.r,),
                    _buildStateDisplay(
                      "24h High", 
                      decimalData(priceData!.high24h ?? 0.0, 6),
                    ),
                    SizedBox(width: 40.r,),
                    _buildStateDisplay(
                      "24h Low", 
                      decimalData(priceData!.low24h ?? 0.0, 6),
                    ),
                  ],
                ),
            ],
          ),
        ),
        SizedBox(height: 14.r),
        // 图表
        SizedBox(
          height: 500.r,
          width: 1.sw,
          child: Stack(
            children: [
              if(priceData!=null)
                webViewHasError 
                  ? _errorWidget() 
                  : InAppWebView(
                      initialUrlRequest: URLRequest(
                        url: WebUri("https://kchart.vercel.app/?key=${priceData!.key}&embed=1"),
                      ),
                      initialSettings: InAppWebViewSettings(
                        javaScriptCanOpenWindowsAutomatically: true,
                        supportMultipleWindows: true,
                        isFraudulentWebsiteWarningEnabled: true,
                        safeBrowsingEnabled: true,
                        mediaPlaybackRequiresUserGesture: false,
                        allowsInlineMediaPlayback: true,
                        // transparentBackground: true,
                      ),
                      onWebViewCreated: (controller) {
                        // _webViewController = controller;
                      },
                      onProgressChanged: (controller, progressValue) {
                        if(progressValue == 100 && isChartLoading) {
                          setState(() {
                            isChartLoading = false;
                          });
                        }
                      },
                      gestureRecognizers: {
                        Factory<OneSequenceGestureRecognizer>(
                          () => EagerGestureRecognizer(), // 把拖拦截交给 WebView
                        ),
                      },
                      onReceivedError: (controller, request, error) {
                        setState(() {
                          webViewHasError = true;
                          isChartLoading = false;
                        });
                      },
                    ),
              // Loading UI
              if(isChartLoading)
                Center(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      SizedBox(
                        width: 22.r,
                        height: 22.r,
                        child: CircularProgressIndicator(color: WalletColors.colorFF249ED9, strokeWidth: 3,),
                      ),
                      SizedBox(height: 10.r),
                      Text(
                        "loading".tr,
                        style: TextStyle(
                          fontSize: 12.sp,
                          color: WalletColors.colorFF249ED9,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildStateDisplay(String label, String value, {Color? valueColor}) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 11.sp,
            color: WalletColors.colorFF808080,
            fontWeight: FontWeight.w400,
          ),
        ),
        Text(
          value,
          style: TextStyle(
            fontSize: 13.sp,
            fontWeight: FontWeight.w500,
            color: valueColor ?? WalletColors.colorBlack,
          ),
        ),
      ],
    );
  }

  Widget _errorWidget() {
    return Center(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Image.asset(
            R.icoEmpty,
            width: 100.r,
            height: 100.r,
            package: WalletConfig.packageName,
          ),
          SizedBox(height: 8.r,),
          Text(
            "chart_load_failed".tr,
            style: TextStyle(
              fontSize: 12.sp,
              color: WalletColors.colorFF808080,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 36.r,),
          SizedBox(
            width: 100.r,
            height: 40.r,
            child: ElevatedButton(
              onPressed: () {
                setState(() {
                  webViewHasError = false;
                  isChartLoading = true;
                });
              },
              child: Text(
                "reload".tr,
                style: TextStyle(
                  fontSize: 13.sp,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}