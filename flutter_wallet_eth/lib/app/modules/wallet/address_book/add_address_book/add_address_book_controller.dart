import 'package:flutter/material.dart';
import 'package:flutter_web3/app/core/language/s.dart';
import 'package:flutter_web3/app/core/utils/util.dart';
import 'package:flutter_web3/app/db/database.dart';
import 'package:get/get.dart';

import '../../ethereum/wallet_controller.dart';

class AddAddressBookController extends GetxController {
  Rx<NetworkInfoData> selectedNetwork = const NetworkInfoData(id: -1, uuid: '', rpcUrl: '', chainid: -1).obs;
  RxList<NetworkInfoData> allNetWorkList = Get.find<WalletController>().allListNet;
  final FocusNode focusNode = FocusNode();
  final FocusNode focusNode2 = FocusNode();
  final TextEditingController addressController = TextEditingController();
  final TextEditingController nameController = TextEditingController();

  @override
  void onInit() {
    selectedNetwork = Get.find<WalletController>().netModel.value.obs;
    super.onInit();
  }

  void onNetworkSelected(NetworkInfoData data) {
    selectedNetwork.value = data;
    Get.back();
  }

  Future<void> onSaveTap() async {
    if (addressController.text.isEmpty) {
      toast(S.current.address_cannot_be_empty);
      return;
    }
    if (nameController.text.isEmpty) {
      toast(S.current.name_cannot_be_empty);
      return;
    }

    final db = Get.find<WalletDatabase>();
    final int chainId = selectedNetwork.value.chainid;
    final String address = addressController.text.trim().toLowerCase();
    final String name = nameController.text.trim();

    // Check existence by (chainId, address) using allAddressBook()
    final all = await db.allAddressBook().get();
    final exists = all.any((e) =>
        ((e.chainid ?? -1) == chainId) &&
        ((e.walletAddress ?? '').toLowerCase() == address));
    if (exists) {
      toast(S.current.address_book_already_exist);
      return;
    }

    final double now = DateTime.now().millisecondsSinceEpoch.toDouble();
    await db.insertAddressBook(
      AddressBookCompanion.insert(
        chainid: ofNotNull(chainId),
        walletAddress: ofNotNull(address),
        name: ofNotNull(name),
        createTime: ofNotNull(now),
        updateTime: ofNotNull(now),
      ),
    );
    toast(S.current.added_successfully);
    Get.back();
  }

}