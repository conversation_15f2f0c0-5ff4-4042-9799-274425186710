import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_web3/app/core/utils/util.dart';
import 'package:flutter_web3/app/core/values/config.dart';
import 'package:flutter_web3/app/db/database.dart';
import 'package:flutter_web3/app/modules/wallet/scan/scan_page.dart';
import 'package:flutter_web3/r.dart';
import 'package:get/get.dart';

import '../../../../core/language/s.dart';
import '../../../../core/values/colors.dart';
import '../../../../widgets/ic_widget.dart';
import 'add_address_book_controller.dart';

class AddAddressBookView extends GetView<AddAddressBookController> {
  const AddAddressBookView({super.key});

  @override
  Widget build(BuildContext context) {
    double mHeight = 26.r;
    return GestureDetector(
      onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
      child: Scaffold(
        backgroundColor: Colors.white,
        appBar: AppBar(
          title: Text(
            "${S.current.add_address}",
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          centerTitle: true,
        ),
        body: ListView(
          padding: EdgeInsets.symmetric(horizontal: 30.r),
          children: [
            SizedBox(height: mHeight),
            _buildNetwork(),
            SizedBox(height: mHeight),
            _buildAddress(),
            SizedBox(height: mHeight),
            _buildName(),
            SizedBox(height: 80.r),
            ElevatedButton(
              onPressed: () {
                controller.onSaveTap();
              }, 
              child: Text(
                S.current.save,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 网络
  Widget _buildNetwork() {
    return Container(
      height: 46.r,
      padding: EdgeInsets.only(
        left: 13.6.r,
      ),
      decoration: BoxDecoration(
        color: WalletColors.colorFFF8F8F8,
        borderRadius: BorderRadius.all(Radius.circular(10.r)),
      ),
      child: Obx(() {
        NetworkInfoData currentNetwork = controller.selectedNetwork.value;
        // value.name ?? 'ETH';
        // NetworkInfoData curNetData =
        //     Get.find<WalletController>().netModel.value;
        return Row(
          children: [
            IcWidget(
              isNet: true,
              diameter: 30.r,
              chainid: currentNetwork.chainid,
              filePath: currentNetwork.chainPath,
            ),
            SizedBox(width: 8.r),
            Text(
              currentNetwork.name ?? 'ETH',
              style: TextStyle(
                fontSize: 14.sp,
              ),
            ),
            const Spacer(),
            Text(
              S.current.transferNetwork,
              style: TextStyle(fontSize: 11.sp),
            ),
            IconButton(
              icon: Icon(
                Icons.keyboard_arrow_down,
                color: WalletColors.colorFFBBBBBB,
                size: 25.r,
              ),
              onPressed: () async {
                showModalBottomSheetUtil(
                  widget: _buildNetworkBottomSheet(), 
                  context: Get.context!,
                );
              },
            ),
          ],
        );
      }),
    );
  }

  Widget _buildNetworkBottomSheet() {
    return StatefulBuilder(
      builder: (context, setModalState) {
        return Container(
          padding: const EdgeInsets.only(top: 14,bottom: 16, left: 16, right: 16).r,
          width: double.infinity,
          constraints: BoxConstraints(maxHeight: 463.h),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 标题 
              Text(
                S.current.choose_address,
                style: TextStyle(
                  fontSize: 18.sp,
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(height: 16.r),

              // 内容
              Expanded(
                child: ListView.separated(
                  itemBuilder: (context, index) {
                    var data = controller.allNetWorkList[index];
                    return _buildNetworkItem(
                      data:data, 
                      onSelectTap: () {
                        controller.onNetworkSelected(data);
                      },
                    );
                  },
                  itemCount: controller.allNetWorkList.length,
                  separatorBuilder: (BuildContext context, int index) =>
                      Divider(height: 0.5.h, color: WalletColors.colorFFE6E6E6),
                ),
              ),

            ],
          ),
        );
      }
    );
  }

  Widget _buildNetworkItem({
    required NetworkInfoData data,
    GestureTapCallback? onSelectTap,
  }) {
    bool isSelected = data.chainid == controller.selectedNetwork.value.chainid;

    return Column(
      children: [
        Material(
          color: Colors.transparent,
          child: InkWell(
            borderRadius: null,
            onTap: onSelectTap,
            child: Container(
              height: 60.h,
              padding: const EdgeInsets.only(
                left: 16,
                right: 16,
              ).r,
              child: Row(
                children: [
                  IcWidget(isNet: true,chainid:data.chainid,filePath: data.chainPath,),
                  SizedBox(
                    width: 10.w,
                  ),
                  Expanded(
                    child: Container(
                      alignment: Alignment.centerLeft,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Row(
                            children: [
                              Container(
                                constraints: BoxConstraints(maxWidth: 150.w),
                                child: Text(
                                  data.name ?? "",
                                  style: TextStyle(
                                    fontSize: 14.sp,
                                    fontWeight: FontWeight.bold,
                                  ),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                  GestureDetector(
                    onTap: onSelectTap,
                    behavior: HitTestBehavior.opaque,
                    child: Padding(
                      padding: EdgeInsets.symmetric(
                          horizontal: 8.h, vertical: 8.h), // 增加点击区域
                      child: Image.asset(
                        isSelected ? R.icoSelected : R.icoUnselected,
                        width: 16.h,
                        height: 16.h,
                        package: WalletConfig.packageName,
                      ),
                    ),
                  ),
                  SizedBox(
                    width: 16.r,
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// 地址
  Widget _buildAddress() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          S.current.wallet_address,
          style: TextStyle(
            height: 1.3,
            fontSize: 16.sp,
          ),
        ),
        SizedBox(height: 2.r),
        Container(
          constraints: BoxConstraints(minHeight: 44.r),
          child: TextField(
            focusNode: controller.focusNode,
            controller: controller.addressController,
            maxLines: 1,
            decoration: InputDecoration(
              contentPadding: EdgeInsets.only(left: 3.r, top: 0.5),
              hintText: S.current.hint_input_address,
              hintStyle: TextStyle(
                fontSize: 13.sp,
                color: WalletColors.colorFFBBBBBB,
              ),
              focusedBorder: const UnderlineInputBorder(
                  borderSide: BorderSide(color: WalletColors.colorFF249ED9)),
              enabledBorder: const UnderlineInputBorder(
                  borderSide: BorderSide(color: WalletColors.colorFFCBCBCB)),
              suffixIconConstraints: BoxConstraints(maxHeight: 18.r),
              suffixIcon: GestureDetector(
                onTap: () {
                  Get.to(const ScanPage())?.then((value) {
                    if (value.runtimeType == String) {
                      controller.addressController.text = value ?? '';
                    }
                  });
                },
                child: Padding(
                  padding: EdgeInsets.only(left: 12.r),
                  child: Image.asset(R.iconScanner,
                      width: 18.r, height: 18.r, package: WalletConfig.packageName),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// 名字
  Widget _buildName() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          S.current.set_address_name,
          style: TextStyle(
            height: 1.3,
            fontSize: 16.sp,
          ),
        ),
        SizedBox(height: 2.r),
        Container(
          constraints: BoxConstraints(minHeight: 44.r),
          child: TextField(
            focusNode: controller.focusNode2,
            controller: controller.nameController,
            maxLines: 1,
            maxLength: 50,
            decoration: InputDecoration(
              contentPadding: EdgeInsets.only(left: 3.r, top: 0.5),
              hintText: S.current.hint_input_name,
              hintStyle: TextStyle(
                fontSize: 13.sp,
                color: WalletColors.colorFFBBBBBB,
              ),
              focusedBorder: const UnderlineInputBorder(
                  borderSide: BorderSide(color: WalletColors.colorFF249ED9)),
              enabledBorder: const UnderlineInputBorder(
                  borderSide: BorderSide(color: WalletColors.colorFFCBCBCB)),
            ),
          ),
        ),
      ],
    );
  }

}