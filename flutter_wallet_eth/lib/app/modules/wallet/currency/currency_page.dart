import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_web3/app/core/language/s.dart';
import 'package:flutter_web3/app/core/values/colors.dart';
import 'package:flutter_web3/app/core/values/config.dart';
import 'package:flutter_web3/app/modules/wallet/ethereum/wallet_controller.dart';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';

class CurrencyPage extends StatefulWidget {
  const CurrencyPage({super.key});

  @override
  State<CurrencyPage> createState() => _CurrencyPageState();
}

class _CurrencyPageState extends State<CurrencyPage> {
  final _walletController = Get.find<WalletController>();
  final _supportedCurrencies = WalletConfig.supportedCurrency;
  var _selectedCurrency;

  @override
  void initState() {
    _selectedCurrency = _walletController.targetCurrency.value;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: WalletColors.colorFFF2F2F2,
      appBar: AppBar(
        title: Text(
          "currency_unit".tr,
          style: TextStyle(
            fontSize: 16.sp,
          ),
        ),
        centerTitle: true,
        actions: [
          Center(
            child: Padding(
              padding: EdgeInsets.symmetric(vertical: 8.0.r, horizontal: 14.r),
              child: GestureDetector(
                behavior: HitTestBehavior.opaque,
                onTap: () async {
                  _walletController.targetCurrency.value = _selectedCurrency;
                  _walletController.getAllData();
                  var sharedPref = await SharedPreferences.getInstance();
                  sharedPref.setString(WalletConfig.CURRENCY_UNIT_KEY, _selectedCurrency);
                  Get.back();
                },
                child: Text(
                  "save".tr,
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: WalletColors.colorFF249ED9,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
      body: SingleChildScrollView(
        physics: const BouncingScrollPhysics(),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Flexible(
              child: Container(
                margin: const EdgeInsets.only(left: 14, right: 14, top: 15,).r,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(16.r),
                  color: WalletColors.colorWhite,
                  boxShadow: [
                    BoxShadow(
                      color: WalletColors.color29000000,
                      blurRadius: 6,
                      offset: const Offset(0, 3),
                    ),
                  ],
                ),
                child: Padding(
                  padding: EdgeInsets.all(14.r),
                  child: ListView.separated(
                    physics: const NeverScrollableScrollPhysics(),
                    shrinkWrap: true,
                    itemBuilder: (context, index) {
                      return GestureDetector(
                        behavior: HitTestBehavior.opaque,
                        onTap: () {
                          setState(() {
                            _selectedCurrency = _supportedCurrencies.keys.elementAt(index);
                          });
                        },
                        child: Container(
                          height: 30.r,
                          padding: EdgeInsets.only(right: 10.r),
                          child: Center(
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  "${_supportedCurrencies.keys.elementAt(index)}   ${WalletConfig.currencySymbol[_supportedCurrencies.keys.elementAt(index)]}",
                                  style: TextStyle(
                                    fontSize: 14.sp,
                                    // fontWeight: FontWeight.w600,
                                    height: 1.2
                                  ),
                                ),
                                if(_selectedCurrency == _supportedCurrencies.keys.elementAt(index))
                                  Icon(
                                    Icons.check,
                                    size: 22.r,
                                    color: WalletColors.colorFF249ED9,
                                  ),
                              ],
                            ),
                          ),
                        ),
                      );
                    },
                    itemCount: _supportedCurrencies.length,
                    separatorBuilder: (BuildContext context, int index) => Divider(),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}