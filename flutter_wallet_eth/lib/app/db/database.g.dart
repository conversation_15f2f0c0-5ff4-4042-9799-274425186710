// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'database.dart';

// ignore_for_file: type=lint
class TransactionRecord extends Table
    with TableInfo<TransactionRecord, TransactionRecordData> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  TransactionRecord(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  late final GeneratedColumn<int> id = GeneratedColumn<int>(
      'id', aliasedName, false,
      hasAutoIncrement: true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      $customConstraints: 'NOT NULL PRIMARY KEY AUTOINCREMENT');
  static const VerificationMeta _transactionHashMeta =
      const VerificationMeta('transactionHash');
  late final GeneratedColumn<String> transactionHash = GeneratedColumn<String>(
      'transaction_hash', aliasedName, false,
      type: DriftSqlType.string,
      requiredDuringInsert: true,
      $customConstraints: 'NOT NULL UNIQUE');
  static const VerificationMeta _fromMeta = const VerificationMeta('from');
  late final GeneratedColumn<String> from = GeneratedColumn<String>(
      'from', aliasedName, false,
      type: DriftSqlType.string,
      requiredDuringInsert: true,
      $customConstraints: 'NOT NULL');
  static const VerificationMeta _toMeta = const VerificationMeta('to');
  late final GeneratedColumn<String> to = GeneratedColumn<String>(
      'to', aliasedName, false,
      type: DriftSqlType.string,
      requiredDuringInsert: true,
      $customConstraints: 'NOT NULL');
  static const VerificationMeta _chainidMeta =
      const VerificationMeta('chainid');
  late final GeneratedColumn<String> chainid = GeneratedColumn<String>(
      'chainid', aliasedName, false,
      type: DriftSqlType.string,
      requiredDuringInsert: true,
      $customConstraints: 'NOT NULL');
  static const VerificationMeta _netUuidMeta =
      const VerificationMeta('netUuid');
  late final GeneratedColumn<String> netUuid = GeneratedColumn<String>(
      'net_uuid', aliasedName, false,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: 'NOT NULL DEFAULT \'\'',
      defaultValue: const CustomExpression('\'\''));
  static const VerificationMeta _blockNumberMeta =
      const VerificationMeta('blockNumber');
  late final GeneratedColumn<int> blockNumber = GeneratedColumn<int>(
      'block_number', aliasedName, true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _moneyMeta = const VerificationMeta('money');
  late final GeneratedColumn<String> money = GeneratedColumn<String>(
      'money', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _optionTypeMeta =
      const VerificationMeta('optionType');
  late final GeneratedColumn<int> optionType = GeneratedColumn<int>(
      'option_type', aliasedName, true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _statusMeta = const VerificationMeta('status');
  late final GeneratedColumn<int> status = GeneratedColumn<int>(
      'status', aliasedName, true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _timeMeta = const VerificationMeta('time');
  late final GeneratedColumn<double> time = GeneratedColumn<double>(
      'time', aliasedName, true,
      type: DriftSqlType.double,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _cumulativeGasUsedMeta =
      const VerificationMeta('cumulativeGasUsed');
  late final GeneratedColumn<String> cumulativeGasUsed =
      GeneratedColumn<String>('cumulative_gas_used', aliasedName, true,
          type: DriftSqlType.string,
          requiredDuringInsert: false,
          $customConstraints: '');
  static const VerificationMeta _gasUsedMeta =
      const VerificationMeta('gasUsed');
  late final GeneratedColumn<String> gasUsed = GeneratedColumn<String>(
      'gas_used', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _effectiveGasPriceMeta =
      const VerificationMeta('effectiveGasPrice');
  late final GeneratedColumn<String> effectiveGasPrice =
      GeneratedColumn<String>('effective_gas_price', aliasedName, true,
          type: DriftSqlType.string,
          requiredDuringInsert: false,
          $customConstraints: '');
  static const VerificationMeta _tokenMeta = const VerificationMeta('token');
  late final GeneratedColumn<String> token = GeneratedColumn<String>(
      'token', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _memoMeta = const VerificationMeta('memo');
  late final GeneratedColumn<String> memo = GeneratedColumn<String>(
      'memo', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _transactionTypeMeta =
      const VerificationMeta('transactionType');
  late final GeneratedColumn<int> transactionType = GeneratedColumn<int>(
      'transaction_type', aliasedName, false,
      type: DriftSqlType.int,
      requiredDuringInsert: true,
      $customConstraints: 'NOT NULL');
  static const VerificationMeta _createTimeMeta =
      const VerificationMeta('createTime');
  late final GeneratedColumn<double> createTime = GeneratedColumn<double>(
      'create_time', aliasedName, true,
      type: DriftSqlType.double,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _chainTypeMeta =
      const VerificationMeta('chainType');
  late final GeneratedColumn<int> chainType = GeneratedColumn<int>(
      'chain_type', aliasedName, true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _updateTimeMeta =
      const VerificationMeta('updateTime');
  late final GeneratedColumn<double> updateTime = GeneratedColumn<double>(
      'update_time', aliasedName, true,
      type: DriftSqlType.double,
      requiredDuringInsert: false,
      $customConstraints: '');
  @override
  List<GeneratedColumn> get $columns => [
        id,
        transactionHash,
        from,
        to,
        chainid,
        netUuid,
        blockNumber,
        money,
        optionType,
        status,
        time,
        cumulativeGasUsed,
        gasUsed,
        effectiveGasPrice,
        token,
        memo,
        transactionType,
        createTime,
        chainType,
        updateTime
      ];
  @override
  String get aliasedName => _alias ?? 'transaction_record';
  @override
  String get actualTableName => 'transaction_record';
  @override
  VerificationContext validateIntegrity(
      Insertable<TransactionRecordData> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    }
    if (data.containsKey('transaction_hash')) {
      context.handle(
          _transactionHashMeta,
          transactionHash.isAcceptableOrUnknown(
              data['transaction_hash']!, _transactionHashMeta));
    } else if (isInserting) {
      context.missing(_transactionHashMeta);
    }
    if (data.containsKey('from')) {
      context.handle(
          _fromMeta, from.isAcceptableOrUnknown(data['from']!, _fromMeta));
    } else if (isInserting) {
      context.missing(_fromMeta);
    }
    if (data.containsKey('to')) {
      context.handle(_toMeta, to.isAcceptableOrUnknown(data['to']!, _toMeta));
    } else if (isInserting) {
      context.missing(_toMeta);
    }
    if (data.containsKey('chainid')) {
      context.handle(_chainidMeta,
          chainid.isAcceptableOrUnknown(data['chainid']!, _chainidMeta));
    } else if (isInserting) {
      context.missing(_chainidMeta);
    }
    if (data.containsKey('net_uuid')) {
      context.handle(_netUuidMeta,
          netUuid.isAcceptableOrUnknown(data['net_uuid']!, _netUuidMeta));
    }
    if (data.containsKey('block_number')) {
      context.handle(
          _blockNumberMeta,
          blockNumber.isAcceptableOrUnknown(
              data['block_number']!, _blockNumberMeta));
    }
    if (data.containsKey('money')) {
      context.handle(
          _moneyMeta, money.isAcceptableOrUnknown(data['money']!, _moneyMeta));
    }
    if (data.containsKey('option_type')) {
      context.handle(
          _optionTypeMeta,
          optionType.isAcceptableOrUnknown(
              data['option_type']!, _optionTypeMeta));
    }
    if (data.containsKey('status')) {
      context.handle(_statusMeta,
          status.isAcceptableOrUnknown(data['status']!, _statusMeta));
    }
    if (data.containsKey('time')) {
      context.handle(
          _timeMeta, time.isAcceptableOrUnknown(data['time']!, _timeMeta));
    }
    if (data.containsKey('cumulative_gas_used')) {
      context.handle(
          _cumulativeGasUsedMeta,
          cumulativeGasUsed.isAcceptableOrUnknown(
              data['cumulative_gas_used']!, _cumulativeGasUsedMeta));
    }
    if (data.containsKey('gas_used')) {
      context.handle(_gasUsedMeta,
          gasUsed.isAcceptableOrUnknown(data['gas_used']!, _gasUsedMeta));
    }
    if (data.containsKey('effective_gas_price')) {
      context.handle(
          _effectiveGasPriceMeta,
          effectiveGasPrice.isAcceptableOrUnknown(
              data['effective_gas_price']!, _effectiveGasPriceMeta));
    }
    if (data.containsKey('token')) {
      context.handle(
          _tokenMeta, token.isAcceptableOrUnknown(data['token']!, _tokenMeta));
    }
    if (data.containsKey('memo')) {
      context.handle(
          _memoMeta, memo.isAcceptableOrUnknown(data['memo']!, _memoMeta));
    }
    if (data.containsKey('transaction_type')) {
      context.handle(
          _transactionTypeMeta,
          transactionType.isAcceptableOrUnknown(
              data['transaction_type']!, _transactionTypeMeta));
    } else if (isInserting) {
      context.missing(_transactionTypeMeta);
    }
    if (data.containsKey('create_time')) {
      context.handle(
          _createTimeMeta,
          createTime.isAcceptableOrUnknown(
              data['create_time']!, _createTimeMeta));
    }
    if (data.containsKey('chain_type')) {
      context.handle(_chainTypeMeta,
          chainType.isAcceptableOrUnknown(data['chain_type']!, _chainTypeMeta));
    }
    if (data.containsKey('update_time')) {
      context.handle(
          _updateTimeMeta,
          updateTime.isAcceptableOrUnknown(
              data['update_time']!, _updateTimeMeta));
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  TransactionRecordData map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return TransactionRecordData(
      id: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}id'])!,
      transactionHash: attachedDatabase.typeMapping.read(
          DriftSqlType.string, data['${effectivePrefix}transaction_hash'])!,
      from: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}from'])!,
      to: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}to'])!,
      chainid: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}chainid'])!,
      netUuid: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}net_uuid'])!,
      blockNumber: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}block_number']),
      money: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}money']),
      optionType: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}option_type']),
      status: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}status']),
      time: attachedDatabase.typeMapping
          .read(DriftSqlType.double, data['${effectivePrefix}time']),
      cumulativeGasUsed: attachedDatabase.typeMapping.read(
          DriftSqlType.string, data['${effectivePrefix}cumulative_gas_used']),
      gasUsed: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}gas_used']),
      effectiveGasPrice: attachedDatabase.typeMapping.read(
          DriftSqlType.string, data['${effectivePrefix}effective_gas_price']),
      token: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}token']),
      memo: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}memo']),
      transactionType: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}transaction_type'])!,
      createTime: attachedDatabase.typeMapping
          .read(DriftSqlType.double, data['${effectivePrefix}create_time']),
      chainType: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}chain_type']),
      updateTime: attachedDatabase.typeMapping
          .read(DriftSqlType.double, data['${effectivePrefix}update_time']),
    );
  }

  @override
  TransactionRecord createAlias(String alias) {
    return TransactionRecord(attachedDatabase, alias);
  }

  @override
  bool get dontWriteConstraints => true;
}

class TransactionRecordData extends DataClass
    implements Insertable<TransactionRecordData> {
  final int id;
  final String transactionHash;
  final String from;
  final String to;
  final String chainid;
  final String netUuid;
  final int? blockNumber;
  final String? money;
  final int? optionType;
  final int? status;
  final double? time;
  final String? cumulativeGasUsed;
  final String? gasUsed;
  final String? effectiveGasPrice;
  final String? token;
  final String? memo;
  final int transactionType;
  final double? createTime;
  final int? chainType;
  final double? updateTime;
  const TransactionRecordData(
      {required this.id,
      required this.transactionHash,
      required this.from,
      required this.to,
      required this.chainid,
      required this.netUuid,
      this.blockNumber,
      this.money,
      this.optionType,
      this.status,
      this.time,
      this.cumulativeGasUsed,
      this.gasUsed,
      this.effectiveGasPrice,
      this.token,
      this.memo,
      required this.transactionType,
      this.createTime,
      this.chainType,
      this.updateTime});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<int>(id);
    map['transaction_hash'] = Variable<String>(transactionHash);
    map['from'] = Variable<String>(from);
    map['to'] = Variable<String>(to);
    map['chainid'] = Variable<String>(chainid);
    map['net_uuid'] = Variable<String>(netUuid);
    if (!nullToAbsent || blockNumber != null) {
      map['block_number'] = Variable<int>(blockNumber);
    }
    if (!nullToAbsent || money != null) {
      map['money'] = Variable<String>(money);
    }
    if (!nullToAbsent || optionType != null) {
      map['option_type'] = Variable<int>(optionType);
    }
    if (!nullToAbsent || status != null) {
      map['status'] = Variable<int>(status);
    }
    if (!nullToAbsent || time != null) {
      map['time'] = Variable<double>(time);
    }
    if (!nullToAbsent || cumulativeGasUsed != null) {
      map['cumulative_gas_used'] = Variable<String>(cumulativeGasUsed);
    }
    if (!nullToAbsent || gasUsed != null) {
      map['gas_used'] = Variable<String>(gasUsed);
    }
    if (!nullToAbsent || effectiveGasPrice != null) {
      map['effective_gas_price'] = Variable<String>(effectiveGasPrice);
    }
    if (!nullToAbsent || token != null) {
      map['token'] = Variable<String>(token);
    }
    if (!nullToAbsent || memo != null) {
      map['memo'] = Variable<String>(memo);
    }
    map['transaction_type'] = Variable<int>(transactionType);
    if (!nullToAbsent || createTime != null) {
      map['create_time'] = Variable<double>(createTime);
    }
    if (!nullToAbsent || chainType != null) {
      map['chain_type'] = Variable<int>(chainType);
    }
    if (!nullToAbsent || updateTime != null) {
      map['update_time'] = Variable<double>(updateTime);
    }
    return map;
  }

  TransactionRecordCompanion toCompanion(bool nullToAbsent) {
    return TransactionRecordCompanion(
      id: Value(id),
      transactionHash: Value(transactionHash),
      from: Value(from),
      to: Value(to),
      chainid: Value(chainid),
      netUuid: Value(netUuid),
      blockNumber: blockNumber == null && nullToAbsent
          ? const Value.absent()
          : Value(blockNumber),
      money:
          money == null && nullToAbsent ? const Value.absent() : Value(money),
      optionType: optionType == null && nullToAbsent
          ? const Value.absent()
          : Value(optionType),
      status:
          status == null && nullToAbsent ? const Value.absent() : Value(status),
      time: time == null && nullToAbsent ? const Value.absent() : Value(time),
      cumulativeGasUsed: cumulativeGasUsed == null && nullToAbsent
          ? const Value.absent()
          : Value(cumulativeGasUsed),
      gasUsed: gasUsed == null && nullToAbsent
          ? const Value.absent()
          : Value(gasUsed),
      effectiveGasPrice: effectiveGasPrice == null && nullToAbsent
          ? const Value.absent()
          : Value(effectiveGasPrice),
      token:
          token == null && nullToAbsent ? const Value.absent() : Value(token),
      memo: memo == null && nullToAbsent ? const Value.absent() : Value(memo),
      transactionType: Value(transactionType),
      createTime: createTime == null && nullToAbsent
          ? const Value.absent()
          : Value(createTime),
      chainType: chainType == null && nullToAbsent
          ? const Value.absent()
          : Value(chainType),
      updateTime: updateTime == null && nullToAbsent
          ? const Value.absent()
          : Value(updateTime),
    );
  }

  factory TransactionRecordData.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return TransactionRecordData(
      id: serializer.fromJson<int>(json['id']),
      transactionHash: serializer.fromJson<String>(json['transaction_hash']),
      from: serializer.fromJson<String>(json['from']),
      to: serializer.fromJson<String>(json['to']),
      chainid: serializer.fromJson<String>(json['chainid']),
      netUuid: serializer.fromJson<String>(json['net_uuid']),
      blockNumber: serializer.fromJson<int?>(json['block_number']),
      money: serializer.fromJson<String?>(json['money']),
      optionType: serializer.fromJson<int?>(json['option_type']),
      status: serializer.fromJson<int?>(json['status']),
      time: serializer.fromJson<double?>(json['time']),
      cumulativeGasUsed:
          serializer.fromJson<String?>(json['cumulative_gas_used']),
      gasUsed: serializer.fromJson<String?>(json['gas_used']),
      effectiveGasPrice:
          serializer.fromJson<String?>(json['effective_gas_price']),
      token: serializer.fromJson<String?>(json['token']),
      memo: serializer.fromJson<String?>(json['memo']),
      transactionType: serializer.fromJson<int>(json['transaction_type']),
      createTime: serializer.fromJson<double?>(json['create_time']),
      chainType: serializer.fromJson<int?>(json['chain_type']),
      updateTime: serializer.fromJson<double?>(json['update_time']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<int>(id),
      'transaction_hash': serializer.toJson<String>(transactionHash),
      'from': serializer.toJson<String>(from),
      'to': serializer.toJson<String>(to),
      'chainid': serializer.toJson<String>(chainid),
      'net_uuid': serializer.toJson<String>(netUuid),
      'block_number': serializer.toJson<int?>(blockNumber),
      'money': serializer.toJson<String?>(money),
      'option_type': serializer.toJson<int?>(optionType),
      'status': serializer.toJson<int?>(status),
      'time': serializer.toJson<double?>(time),
      'cumulative_gas_used': serializer.toJson<String?>(cumulativeGasUsed),
      'gas_used': serializer.toJson<String?>(gasUsed),
      'effective_gas_price': serializer.toJson<String?>(effectiveGasPrice),
      'token': serializer.toJson<String?>(token),
      'memo': serializer.toJson<String?>(memo),
      'transaction_type': serializer.toJson<int>(transactionType),
      'create_time': serializer.toJson<double?>(createTime),
      'chain_type': serializer.toJson<int?>(chainType),
      'update_time': serializer.toJson<double?>(updateTime),
    };
  }

  TransactionRecordData copyWith(
          {int? id,
          String? transactionHash,
          String? from,
          String? to,
          String? chainid,
          String? netUuid,
          Value<int?> blockNumber = const Value.absent(),
          Value<String?> money = const Value.absent(),
          Value<int?> optionType = const Value.absent(),
          Value<int?> status = const Value.absent(),
          Value<double?> time = const Value.absent(),
          Value<String?> cumulativeGasUsed = const Value.absent(),
          Value<String?> gasUsed = const Value.absent(),
          Value<String?> effectiveGasPrice = const Value.absent(),
          Value<String?> token = const Value.absent(),
          Value<String?> memo = const Value.absent(),
          int? transactionType,
          Value<double?> createTime = const Value.absent(),
          Value<int?> chainType = const Value.absent(),
          Value<double?> updateTime = const Value.absent()}) =>
      TransactionRecordData(
        id: id ?? this.id,
        transactionHash: transactionHash ?? this.transactionHash,
        from: from ?? this.from,
        to: to ?? this.to,
        chainid: chainid ?? this.chainid,
        netUuid: netUuid ?? this.netUuid,
        blockNumber: blockNumber.present ? blockNumber.value : this.blockNumber,
        money: money.present ? money.value : this.money,
        optionType: optionType.present ? optionType.value : this.optionType,
        status: status.present ? status.value : this.status,
        time: time.present ? time.value : this.time,
        cumulativeGasUsed: cumulativeGasUsed.present
            ? cumulativeGasUsed.value
            : this.cumulativeGasUsed,
        gasUsed: gasUsed.present ? gasUsed.value : this.gasUsed,
        effectiveGasPrice: effectiveGasPrice.present
            ? effectiveGasPrice.value
            : this.effectiveGasPrice,
        token: token.present ? token.value : this.token,
        memo: memo.present ? memo.value : this.memo,
        transactionType: transactionType ?? this.transactionType,
        createTime: createTime.present ? createTime.value : this.createTime,
        chainType: chainType.present ? chainType.value : this.chainType,
        updateTime: updateTime.present ? updateTime.value : this.updateTime,
      );
  @override
  String toString() {
    return (StringBuffer('TransactionRecordData(')
          ..write('id: $id, ')
          ..write('transactionHash: $transactionHash, ')
          ..write('from: $from, ')
          ..write('to: $to, ')
          ..write('chainid: $chainid, ')
          ..write('netUuid: $netUuid, ')
          ..write('blockNumber: $blockNumber, ')
          ..write('money: $money, ')
          ..write('optionType: $optionType, ')
          ..write('status: $status, ')
          ..write('time: $time, ')
          ..write('cumulativeGasUsed: $cumulativeGasUsed, ')
          ..write('gasUsed: $gasUsed, ')
          ..write('effectiveGasPrice: $effectiveGasPrice, ')
          ..write('token: $token, ')
          ..write('memo: $memo, ')
          ..write('transactionType: $transactionType, ')
          ..write('createTime: $createTime, ')
          ..write('chainType: $chainType, ')
          ..write('updateTime: $updateTime')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(
      id,
      transactionHash,
      from,
      to,
      chainid,
      netUuid,
      blockNumber,
      money,
      optionType,
      status,
      time,
      cumulativeGasUsed,
      gasUsed,
      effectiveGasPrice,
      token,
      memo,
      transactionType,
      createTime,
      chainType,
      updateTime);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is TransactionRecordData &&
          other.id == this.id &&
          other.transactionHash == this.transactionHash &&
          other.from == this.from &&
          other.to == this.to &&
          other.chainid == this.chainid &&
          other.netUuid == this.netUuid &&
          other.blockNumber == this.blockNumber &&
          other.money == this.money &&
          other.optionType == this.optionType &&
          other.status == this.status &&
          other.time == this.time &&
          other.cumulativeGasUsed == this.cumulativeGasUsed &&
          other.gasUsed == this.gasUsed &&
          other.effectiveGasPrice == this.effectiveGasPrice &&
          other.token == this.token &&
          other.memo == this.memo &&
          other.transactionType == this.transactionType &&
          other.createTime == this.createTime &&
          other.chainType == this.chainType &&
          other.updateTime == this.updateTime);
}

class TransactionRecordCompanion
    extends UpdateCompanion<TransactionRecordData> {
  final Value<int> id;
  final Value<String> transactionHash;
  final Value<String> from;
  final Value<String> to;
  final Value<String> chainid;
  final Value<String> netUuid;
  final Value<int?> blockNumber;
  final Value<String?> money;
  final Value<int?> optionType;
  final Value<int?> status;
  final Value<double?> time;
  final Value<String?> cumulativeGasUsed;
  final Value<String?> gasUsed;
  final Value<String?> effectiveGasPrice;
  final Value<String?> token;
  final Value<String?> memo;
  final Value<int> transactionType;
  final Value<double?> createTime;
  final Value<int?> chainType;
  final Value<double?> updateTime;
  const TransactionRecordCompanion({
    this.id = const Value.absent(),
    this.transactionHash = const Value.absent(),
    this.from = const Value.absent(),
    this.to = const Value.absent(),
    this.chainid = const Value.absent(),
    this.netUuid = const Value.absent(),
    this.blockNumber = const Value.absent(),
    this.money = const Value.absent(),
    this.optionType = const Value.absent(),
    this.status = const Value.absent(),
    this.time = const Value.absent(),
    this.cumulativeGasUsed = const Value.absent(),
    this.gasUsed = const Value.absent(),
    this.effectiveGasPrice = const Value.absent(),
    this.token = const Value.absent(),
    this.memo = const Value.absent(),
    this.transactionType = const Value.absent(),
    this.createTime = const Value.absent(),
    this.chainType = const Value.absent(),
    this.updateTime = const Value.absent(),
  });
  TransactionRecordCompanion.insert({
    this.id = const Value.absent(),
    required String transactionHash,
    required String from,
    required String to,
    required String chainid,
    this.netUuid = const Value.absent(),
    this.blockNumber = const Value.absent(),
    this.money = const Value.absent(),
    this.optionType = const Value.absent(),
    this.status = const Value.absent(),
    this.time = const Value.absent(),
    this.cumulativeGasUsed = const Value.absent(),
    this.gasUsed = const Value.absent(),
    this.effectiveGasPrice = const Value.absent(),
    this.token = const Value.absent(),
    this.memo = const Value.absent(),
    required int transactionType,
    this.createTime = const Value.absent(),
    this.chainType = const Value.absent(),
    this.updateTime = const Value.absent(),
  })  : transactionHash = Value(transactionHash),
        from = Value(from),
        to = Value(to),
        chainid = Value(chainid),
        transactionType = Value(transactionType);
  static Insertable<TransactionRecordData> custom({
    Expression<int>? id,
    Expression<String>? transactionHash,
    Expression<String>? from,
    Expression<String>? to,
    Expression<String>? chainid,
    Expression<String>? netUuid,
    Expression<int>? blockNumber,
    Expression<String>? money,
    Expression<int>? optionType,
    Expression<int>? status,
    Expression<double>? time,
    Expression<String>? cumulativeGasUsed,
    Expression<String>? gasUsed,
    Expression<String>? effectiveGasPrice,
    Expression<String>? token,
    Expression<String>? memo,
    Expression<int>? transactionType,
    Expression<double>? createTime,
    Expression<int>? chainType,
    Expression<double>? updateTime,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (transactionHash != null) 'transaction_hash': transactionHash,
      if (from != null) 'from': from,
      if (to != null) 'to': to,
      if (chainid != null) 'chainid': chainid,
      if (netUuid != null) 'net_uuid': netUuid,
      if (blockNumber != null) 'block_number': blockNumber,
      if (money != null) 'money': money,
      if (optionType != null) 'option_type': optionType,
      if (status != null) 'status': status,
      if (time != null) 'time': time,
      if (cumulativeGasUsed != null) 'cumulative_gas_used': cumulativeGasUsed,
      if (gasUsed != null) 'gas_used': gasUsed,
      if (effectiveGasPrice != null) 'effective_gas_price': effectiveGasPrice,
      if (token != null) 'token': token,
      if (memo != null) 'memo': memo,
      if (transactionType != null) 'transaction_type': transactionType,
      if (createTime != null) 'create_time': createTime,
      if (chainType != null) 'chain_type': chainType,
      if (updateTime != null) 'update_time': updateTime,
    });
  }

  TransactionRecordCompanion copyWith(
      {Value<int>? id,
      Value<String>? transactionHash,
      Value<String>? from,
      Value<String>? to,
      Value<String>? chainid,
      Value<String>? netUuid,
      Value<int?>? blockNumber,
      Value<String?>? money,
      Value<int?>? optionType,
      Value<int?>? status,
      Value<double?>? time,
      Value<String?>? cumulativeGasUsed,
      Value<String?>? gasUsed,
      Value<String?>? effectiveGasPrice,
      Value<String?>? token,
      Value<String?>? memo,
      Value<int>? transactionType,
      Value<double?>? createTime,
      Value<int?>? chainType,
      Value<double?>? updateTime}) {
    return TransactionRecordCompanion(
      id: id ?? this.id,
      transactionHash: transactionHash ?? this.transactionHash,
      from: from ?? this.from,
      to: to ?? this.to,
      chainid: chainid ?? this.chainid,
      netUuid: netUuid ?? this.netUuid,
      blockNumber: blockNumber ?? this.blockNumber,
      money: money ?? this.money,
      optionType: optionType ?? this.optionType,
      status: status ?? this.status,
      time: time ?? this.time,
      cumulativeGasUsed: cumulativeGasUsed ?? this.cumulativeGasUsed,
      gasUsed: gasUsed ?? this.gasUsed,
      effectiveGasPrice: effectiveGasPrice ?? this.effectiveGasPrice,
      token: token ?? this.token,
      memo: memo ?? this.memo,
      transactionType: transactionType ?? this.transactionType,
      createTime: createTime ?? this.createTime,
      chainType: chainType ?? this.chainType,
      updateTime: updateTime ?? this.updateTime,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<int>(id.value);
    }
    if (transactionHash.present) {
      map['transaction_hash'] = Variable<String>(transactionHash.value);
    }
    if (from.present) {
      map['from'] = Variable<String>(from.value);
    }
    if (to.present) {
      map['to'] = Variable<String>(to.value);
    }
    if (chainid.present) {
      map['chainid'] = Variable<String>(chainid.value);
    }
    if (netUuid.present) {
      map['net_uuid'] = Variable<String>(netUuid.value);
    }
    if (blockNumber.present) {
      map['block_number'] = Variable<int>(blockNumber.value);
    }
    if (money.present) {
      map['money'] = Variable<String>(money.value);
    }
    if (optionType.present) {
      map['option_type'] = Variable<int>(optionType.value);
    }
    if (status.present) {
      map['status'] = Variable<int>(status.value);
    }
    if (time.present) {
      map['time'] = Variable<double>(time.value);
    }
    if (cumulativeGasUsed.present) {
      map['cumulative_gas_used'] = Variable<String>(cumulativeGasUsed.value);
    }
    if (gasUsed.present) {
      map['gas_used'] = Variable<String>(gasUsed.value);
    }
    if (effectiveGasPrice.present) {
      map['effective_gas_price'] = Variable<String>(effectiveGasPrice.value);
    }
    if (token.present) {
      map['token'] = Variable<String>(token.value);
    }
    if (memo.present) {
      map['memo'] = Variable<String>(memo.value);
    }
    if (transactionType.present) {
      map['transaction_type'] = Variable<int>(transactionType.value);
    }
    if (createTime.present) {
      map['create_time'] = Variable<double>(createTime.value);
    }
    if (chainType.present) {
      map['chain_type'] = Variable<int>(chainType.value);
    }
    if (updateTime.present) {
      map['update_time'] = Variable<double>(updateTime.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('TransactionRecordCompanion(')
          ..write('id: $id, ')
          ..write('transactionHash: $transactionHash, ')
          ..write('from: $from, ')
          ..write('to: $to, ')
          ..write('chainid: $chainid, ')
          ..write('netUuid: $netUuid, ')
          ..write('blockNumber: $blockNumber, ')
          ..write('money: $money, ')
          ..write('optionType: $optionType, ')
          ..write('status: $status, ')
          ..write('time: $time, ')
          ..write('cumulativeGasUsed: $cumulativeGasUsed, ')
          ..write('gasUsed: $gasUsed, ')
          ..write('effectiveGasPrice: $effectiveGasPrice, ')
          ..write('token: $token, ')
          ..write('memo: $memo, ')
          ..write('transactionType: $transactionType, ')
          ..write('createTime: $createTime, ')
          ..write('chainType: $chainType, ')
          ..write('updateTime: $updateTime')
          ..write(')'))
        .toString();
  }
}

class NetworkInfo extends Table with TableInfo<NetworkInfo, NetworkInfoData> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  NetworkInfo(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  late final GeneratedColumn<int> id = GeneratedColumn<int>(
      'id', aliasedName, false,
      hasAutoIncrement: true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      $customConstraints: 'NOT NULL PRIMARY KEY AUTOINCREMENT');
  static const VerificationMeta _uuidMeta = const VerificationMeta('uuid');
  late final GeneratedColumn<String> uuid = GeneratedColumn<String>(
      'uuid', aliasedName, false,
      type: DriftSqlType.string,
      requiredDuringInsert: true,
      $customConstraints: 'NOT NULL UNIQUE');
  static const VerificationMeta _rpcUrlMeta = const VerificationMeta('rpcUrl');
  late final GeneratedColumn<String> rpcUrl = GeneratedColumn<String>(
      'rpc_url', aliasedName, false,
      type: DriftSqlType.string,
      requiredDuringInsert: true,
      $customConstraints: 'NOT NULL');
  static const VerificationMeta _chainidMeta =
      const VerificationMeta('chainid');
  late final GeneratedColumn<int> chainid = GeneratedColumn<int>(
      'chainid', aliasedName, false,
      type: DriftSqlType.int,
      requiredDuringInsert: true,
      $customConstraints: 'NOT NULL');
  static const VerificationMeta _nameMeta = const VerificationMeta('name');
  late final GeneratedColumn<String> name = GeneratedColumn<String>(
      'name', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _symbolMeta = const VerificationMeta('symbol');
  late final GeneratedColumn<String> symbol = GeneratedColumn<String>(
      'symbol', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _browserUrlMeta =
      const VerificationMeta('browserUrl');
  late final GeneratedColumn<String> browserUrl = GeneratedColumn<String>(
      'browser_url', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _chainLogoMeta =
      const VerificationMeta('chainLogo');
  late final GeneratedColumn<String> chainLogo = GeneratedColumn<String>(
      'chain_logo', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _chainPathMeta =
      const VerificationMeta('chainPath');
  late final GeneratedColumn<String> chainPath = GeneratedColumn<String>(
      'chain_path', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _coinLogoMeta =
      const VerificationMeta('coinLogo');
  late final GeneratedColumn<String> coinLogo = GeneratedColumn<String>(
      'coin_logo', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _coinPathMeta =
      const VerificationMeta('coinPath');
  late final GeneratedColumn<String> coinPath = GeneratedColumn<String>(
      'coin_path', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _assetTipsMeta =
      const VerificationMeta('assetTips');
  late final GeneratedColumn<String> assetTips = GeneratedColumn<String>(
      'asset_tips', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _createTimeMeta =
      const VerificationMeta('createTime');
  late final GeneratedColumn<double> createTime = GeneratedColumn<double>(
      'create_time', aliasedName, true,
      type: DriftSqlType.double,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _chainTypeMeta =
      const VerificationMeta('chainType');
  late final GeneratedColumn<int> chainType = GeneratedColumn<int>(
      'chain_type', aliasedName, true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _isEditedMeta =
      const VerificationMeta('isEdited');
  late final GeneratedColumn<bool> isEdited = GeneratedColumn<bool>(
      'isEdited', aliasedName, true,
      type: DriftSqlType.bool,
      requiredDuringInsert: false,
      $customConstraints: 'DEFAULT FALSE',
      defaultValue: const CustomExpression('FALSE'));
  static const VerificationMeta _showStatusMeta =
      const VerificationMeta('showStatus');
  late final GeneratedColumn<int> showStatus = GeneratedColumn<int>(
      'show_status', aliasedName, true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _topIndexMeta =
      const VerificationMeta('topIndex');
  late final GeneratedColumn<int> topIndex = GeneratedColumn<int>(
      'top_index', aliasedName, true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _isDelMeta = const VerificationMeta('isDel');
  late final GeneratedColumn<bool> isDel = GeneratedColumn<bool>(
      'isDel', aliasedName, true,
      type: DriftSqlType.bool,
      requiredDuringInsert: false,
      $customConstraints: 'DEFAULT FALSE',
      defaultValue: const CustomExpression('FALSE'));
  static const VerificationMeta _sourceMeta = const VerificationMeta('source');
  late final GeneratedColumn<int> source = GeneratedColumn<int>(
      'source', aliasedName, true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _serverConfRpcMeta =
      const VerificationMeta('serverConfRpc');
  late final GeneratedColumn<String> serverConfRpc = GeneratedColumn<String>(
      'server_conf_rpc', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _localConfRpcMeta =
      const VerificationMeta('localConfRpc');
  late final GeneratedColumn<String> localConfRpc = GeneratedColumn<String>(
      'local_conf_rpc', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _updateTimeMeta =
      const VerificationMeta('updateTime');
  late final GeneratedColumn<double> updateTime = GeneratedColumn<double>(
      'update_time', aliasedName, true,
      type: DriftSqlType.double,
      requiredDuringInsert: false,
      $customConstraints: '');
  @override
  List<GeneratedColumn> get $columns => [
        id,
        uuid,
        rpcUrl,
        chainid,
        name,
        symbol,
        browserUrl,
        chainLogo,
        chainPath,
        coinLogo,
        coinPath,
        assetTips,
        createTime,
        chainType,
        isEdited,
        showStatus,
        topIndex,
        isDel,
        source,
        serverConfRpc,
        localConfRpc,
        updateTime
      ];
  @override
  String get aliasedName => _alias ?? 'network_info';
  @override
  String get actualTableName => 'network_info';
  @override
  VerificationContext validateIntegrity(Insertable<NetworkInfoData> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    }
    if (data.containsKey('uuid')) {
      context.handle(
          _uuidMeta, uuid.isAcceptableOrUnknown(data['uuid']!, _uuidMeta));
    } else if (isInserting) {
      context.missing(_uuidMeta);
    }
    if (data.containsKey('rpc_url')) {
      context.handle(_rpcUrlMeta,
          rpcUrl.isAcceptableOrUnknown(data['rpc_url']!, _rpcUrlMeta));
    } else if (isInserting) {
      context.missing(_rpcUrlMeta);
    }
    if (data.containsKey('chainid')) {
      context.handle(_chainidMeta,
          chainid.isAcceptableOrUnknown(data['chainid']!, _chainidMeta));
    } else if (isInserting) {
      context.missing(_chainidMeta);
    }
    if (data.containsKey('name')) {
      context.handle(
          _nameMeta, name.isAcceptableOrUnknown(data['name']!, _nameMeta));
    }
    if (data.containsKey('symbol')) {
      context.handle(_symbolMeta,
          symbol.isAcceptableOrUnknown(data['symbol']!, _symbolMeta));
    }
    if (data.containsKey('browser_url')) {
      context.handle(
          _browserUrlMeta,
          browserUrl.isAcceptableOrUnknown(
              data['browser_url']!, _browserUrlMeta));
    }
    if (data.containsKey('chain_logo')) {
      context.handle(_chainLogoMeta,
          chainLogo.isAcceptableOrUnknown(data['chain_logo']!, _chainLogoMeta));
    }
    if (data.containsKey('chain_path')) {
      context.handle(_chainPathMeta,
          chainPath.isAcceptableOrUnknown(data['chain_path']!, _chainPathMeta));
    }
    if (data.containsKey('coin_logo')) {
      context.handle(_coinLogoMeta,
          coinLogo.isAcceptableOrUnknown(data['coin_logo']!, _coinLogoMeta));
    }
    if (data.containsKey('coin_path')) {
      context.handle(_coinPathMeta,
          coinPath.isAcceptableOrUnknown(data['coin_path']!, _coinPathMeta));
    }
    if (data.containsKey('asset_tips')) {
      context.handle(_assetTipsMeta,
          assetTips.isAcceptableOrUnknown(data['asset_tips']!, _assetTipsMeta));
    }
    if (data.containsKey('create_time')) {
      context.handle(
          _createTimeMeta,
          createTime.isAcceptableOrUnknown(
              data['create_time']!, _createTimeMeta));
    }
    if (data.containsKey('chain_type')) {
      context.handle(_chainTypeMeta,
          chainType.isAcceptableOrUnknown(data['chain_type']!, _chainTypeMeta));
    }
    if (data.containsKey('isEdited')) {
      context.handle(_isEditedMeta,
          isEdited.isAcceptableOrUnknown(data['isEdited']!, _isEditedMeta));
    }
    if (data.containsKey('show_status')) {
      context.handle(
          _showStatusMeta,
          showStatus.isAcceptableOrUnknown(
              data['show_status']!, _showStatusMeta));
    }
    if (data.containsKey('top_index')) {
      context.handle(_topIndexMeta,
          topIndex.isAcceptableOrUnknown(data['top_index']!, _topIndexMeta));
    }
    if (data.containsKey('isDel')) {
      context.handle(
          _isDelMeta, isDel.isAcceptableOrUnknown(data['isDel']!, _isDelMeta));
    }
    if (data.containsKey('source')) {
      context.handle(_sourceMeta,
          source.isAcceptableOrUnknown(data['source']!, _sourceMeta));
    }
    if (data.containsKey('server_conf_rpc')) {
      context.handle(
          _serverConfRpcMeta,
          serverConfRpc.isAcceptableOrUnknown(
              data['server_conf_rpc']!, _serverConfRpcMeta));
    }
    if (data.containsKey('local_conf_rpc')) {
      context.handle(
          _localConfRpcMeta,
          localConfRpc.isAcceptableOrUnknown(
              data['local_conf_rpc']!, _localConfRpcMeta));
    }
    if (data.containsKey('update_time')) {
      context.handle(
          _updateTimeMeta,
          updateTime.isAcceptableOrUnknown(
              data['update_time']!, _updateTimeMeta));
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  NetworkInfoData map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return NetworkInfoData(
      id: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}id'])!,
      uuid: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}uuid'])!,
      rpcUrl: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}rpc_url'])!,
      chainid: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}chainid'])!,
      name: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}name']),
      symbol: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}symbol']),
      browserUrl: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}browser_url']),
      chainLogo: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}chain_logo']),
      chainPath: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}chain_path']),
      coinLogo: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}coin_logo']),
      coinPath: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}coin_path']),
      assetTips: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}asset_tips']),
      createTime: attachedDatabase.typeMapping
          .read(DriftSqlType.double, data['${effectivePrefix}create_time']),
      chainType: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}chain_type']),
      isEdited: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}isEdited']),
      showStatus: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}show_status']),
      topIndex: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}top_index']),
      isDel: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}isDel']),
      source: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}source']),
      serverConfRpc: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}server_conf_rpc']),
      localConfRpc: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}local_conf_rpc']),
      updateTime: attachedDatabase.typeMapping
          .read(DriftSqlType.double, data['${effectivePrefix}update_time']),
    );
  }

  @override
  NetworkInfo createAlias(String alias) {
    return NetworkInfo(attachedDatabase, alias);
  }

  @override
  bool get dontWriteConstraints => true;
}

class NetworkInfoData extends DataClass implements Insertable<NetworkInfoData> {
  final int id;
  final String uuid;
  final String rpcUrl;
  final int chainid;
  final String? name;
  final String? symbol;
  final String? browserUrl;
  final String? chainLogo;
  final String? chainPath;
  final String? coinLogo;
  final String? coinPath;
  final String? assetTips;
  final double? createTime;
  final int? chainType;
  final bool? isEdited;
  final int? showStatus;
  final int? topIndex;
  final bool? isDel;
  final int? source;
  final String? serverConfRpc;
  final String? localConfRpc;
  final double? updateTime;
  const NetworkInfoData(
      {required this.id,
      required this.uuid,
      required this.rpcUrl,
      required this.chainid,
      this.name,
      this.symbol,
      this.browserUrl,
      this.chainLogo,
      this.chainPath,
      this.coinLogo,
      this.coinPath,
      this.assetTips,
      this.createTime,
      this.chainType,
      this.isEdited,
      this.showStatus,
      this.topIndex,
      this.isDel,
      this.source,
      this.serverConfRpc,
      this.localConfRpc,
      this.updateTime});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<int>(id);
    map['uuid'] = Variable<String>(uuid);
    map['rpc_url'] = Variable<String>(rpcUrl);
    map['chainid'] = Variable<int>(chainid);
    if (!nullToAbsent || name != null) {
      map['name'] = Variable<String>(name);
    }
    if (!nullToAbsent || symbol != null) {
      map['symbol'] = Variable<String>(symbol);
    }
    if (!nullToAbsent || browserUrl != null) {
      map['browser_url'] = Variable<String>(browserUrl);
    }
    if (!nullToAbsent || chainLogo != null) {
      map['chain_logo'] = Variable<String>(chainLogo);
    }
    if (!nullToAbsent || chainPath != null) {
      map['chain_path'] = Variable<String>(chainPath);
    }
    if (!nullToAbsent || coinLogo != null) {
      map['coin_logo'] = Variable<String>(coinLogo);
    }
    if (!nullToAbsent || coinPath != null) {
      map['coin_path'] = Variable<String>(coinPath);
    }
    if (!nullToAbsent || assetTips != null) {
      map['asset_tips'] = Variable<String>(assetTips);
    }
    if (!nullToAbsent || createTime != null) {
      map['create_time'] = Variable<double>(createTime);
    }
    if (!nullToAbsent || chainType != null) {
      map['chain_type'] = Variable<int>(chainType);
    }
    if (!nullToAbsent || isEdited != null) {
      map['isEdited'] = Variable<bool>(isEdited);
    }
    if (!nullToAbsent || showStatus != null) {
      map['show_status'] = Variable<int>(showStatus);
    }
    if (!nullToAbsent || topIndex != null) {
      map['top_index'] = Variable<int>(topIndex);
    }
    if (!nullToAbsent || isDel != null) {
      map['isDel'] = Variable<bool>(isDel);
    }
    if (!nullToAbsent || source != null) {
      map['source'] = Variable<int>(source);
    }
    if (!nullToAbsent || serverConfRpc != null) {
      map['server_conf_rpc'] = Variable<String>(serverConfRpc);
    }
    if (!nullToAbsent || localConfRpc != null) {
      map['local_conf_rpc'] = Variable<String>(localConfRpc);
    }
    if (!nullToAbsent || updateTime != null) {
      map['update_time'] = Variable<double>(updateTime);
    }
    return map;
  }

  NetworkInfoCompanion toCompanion(bool nullToAbsent) {
    return NetworkInfoCompanion(
      id: Value(id),
      uuid: Value(uuid),
      rpcUrl: Value(rpcUrl),
      chainid: Value(chainid),
      name: name == null && nullToAbsent ? const Value.absent() : Value(name),
      symbol:
          symbol == null && nullToAbsent ? const Value.absent() : Value(symbol),
      browserUrl: browserUrl == null && nullToAbsent
          ? const Value.absent()
          : Value(browserUrl),
      chainLogo: chainLogo == null && nullToAbsent
          ? const Value.absent()
          : Value(chainLogo),
      chainPath: chainPath == null && nullToAbsent
          ? const Value.absent()
          : Value(chainPath),
      coinLogo: coinLogo == null && nullToAbsent
          ? const Value.absent()
          : Value(coinLogo),
      coinPath: coinPath == null && nullToAbsent
          ? const Value.absent()
          : Value(coinPath),
      assetTips: assetTips == null && nullToAbsent
          ? const Value.absent()
          : Value(assetTips),
      createTime: createTime == null && nullToAbsent
          ? const Value.absent()
          : Value(createTime),
      chainType: chainType == null && nullToAbsent
          ? const Value.absent()
          : Value(chainType),
      isEdited: isEdited == null && nullToAbsent
          ? const Value.absent()
          : Value(isEdited),
      showStatus: showStatus == null && nullToAbsent
          ? const Value.absent()
          : Value(showStatus),
      topIndex: topIndex == null && nullToAbsent
          ? const Value.absent()
          : Value(topIndex),
      isDel:
          isDel == null && nullToAbsent ? const Value.absent() : Value(isDel),
      source:
          source == null && nullToAbsent ? const Value.absent() : Value(source),
      serverConfRpc: serverConfRpc == null && nullToAbsent
          ? const Value.absent()
          : Value(serverConfRpc),
      localConfRpc: localConfRpc == null && nullToAbsent
          ? const Value.absent()
          : Value(localConfRpc),
      updateTime: updateTime == null && nullToAbsent
          ? const Value.absent()
          : Value(updateTime),
    );
  }

  factory NetworkInfoData.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return NetworkInfoData(
      id: serializer.fromJson<int>(json['id']),
      uuid: serializer.fromJson<String>(json['uuid']),
      rpcUrl: serializer.fromJson<String>(json['rpc_url']),
      chainid: serializer.fromJson<int>(json['chainid']),
      name: serializer.fromJson<String?>(json['name']),
      symbol: serializer.fromJson<String?>(json['symbol']),
      browserUrl: serializer.fromJson<String?>(json['browser_url']),
      chainLogo: serializer.fromJson<String?>(json['chain_logo']),
      chainPath: serializer.fromJson<String?>(json['chain_path']),
      coinLogo: serializer.fromJson<String?>(json['coin_logo']),
      coinPath: serializer.fromJson<String?>(json['coin_path']),
      assetTips: serializer.fromJson<String?>(json['asset_tips']),
      createTime: serializer.fromJson<double?>(json['create_time']),
      chainType: serializer.fromJson<int?>(json['chain_type']),
      isEdited: serializer.fromJson<bool?>(json['isEdited']),
      showStatus: serializer.fromJson<int?>(json['show_status']),
      topIndex: serializer.fromJson<int?>(json['top_index']),
      isDel: serializer.fromJson<bool?>(json['isDel']),
      source: serializer.fromJson<int?>(json['source']),
      serverConfRpc: serializer.fromJson<String?>(json['server_conf_rpc']),
      localConfRpc: serializer.fromJson<String?>(json['local_conf_rpc']),
      updateTime: serializer.fromJson<double?>(json['update_time']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<int>(id),
      'uuid': serializer.toJson<String>(uuid),
      'rpc_url': serializer.toJson<String>(rpcUrl),
      'chainid': serializer.toJson<int>(chainid),
      'name': serializer.toJson<String?>(name),
      'symbol': serializer.toJson<String?>(symbol),
      'browser_url': serializer.toJson<String?>(browserUrl),
      'chain_logo': serializer.toJson<String?>(chainLogo),
      'chain_path': serializer.toJson<String?>(chainPath),
      'coin_logo': serializer.toJson<String?>(coinLogo),
      'coin_path': serializer.toJson<String?>(coinPath),
      'asset_tips': serializer.toJson<String?>(assetTips),
      'create_time': serializer.toJson<double?>(createTime),
      'chain_type': serializer.toJson<int?>(chainType),
      'isEdited': serializer.toJson<bool?>(isEdited),
      'show_status': serializer.toJson<int?>(showStatus),
      'top_index': serializer.toJson<int?>(topIndex),
      'isDel': serializer.toJson<bool?>(isDel),
      'source': serializer.toJson<int?>(source),
      'server_conf_rpc': serializer.toJson<String?>(serverConfRpc),
      'local_conf_rpc': serializer.toJson<String?>(localConfRpc),
      'update_time': serializer.toJson<double?>(updateTime),
    };
  }

  NetworkInfoData copyWith(
          {int? id,
          String? uuid,
          String? rpcUrl,
          int? chainid,
          Value<String?> name = const Value.absent(),
          Value<String?> symbol = const Value.absent(),
          Value<String?> browserUrl = const Value.absent(),
          Value<String?> chainLogo = const Value.absent(),
          Value<String?> chainPath = const Value.absent(),
          Value<String?> coinLogo = const Value.absent(),
          Value<String?> coinPath = const Value.absent(),
          Value<String?> assetTips = const Value.absent(),
          Value<double?> createTime = const Value.absent(),
          Value<int?> chainType = const Value.absent(),
          Value<bool?> isEdited = const Value.absent(),
          Value<int?> showStatus = const Value.absent(),
          Value<int?> topIndex = const Value.absent(),
          Value<bool?> isDel = const Value.absent(),
          Value<int?> source = const Value.absent(),
          Value<String?> serverConfRpc = const Value.absent(),
          Value<String?> localConfRpc = const Value.absent(),
          Value<double?> updateTime = const Value.absent()}) =>
      NetworkInfoData(
        id: id ?? this.id,
        uuid: uuid ?? this.uuid,
        rpcUrl: rpcUrl ?? this.rpcUrl,
        chainid: chainid ?? this.chainid,
        name: name.present ? name.value : this.name,
        symbol: symbol.present ? symbol.value : this.symbol,
        browserUrl: browserUrl.present ? browserUrl.value : this.browserUrl,
        chainLogo: chainLogo.present ? chainLogo.value : this.chainLogo,
        chainPath: chainPath.present ? chainPath.value : this.chainPath,
        coinLogo: coinLogo.present ? coinLogo.value : this.coinLogo,
        coinPath: coinPath.present ? coinPath.value : this.coinPath,
        assetTips: assetTips.present ? assetTips.value : this.assetTips,
        createTime: createTime.present ? createTime.value : this.createTime,
        chainType: chainType.present ? chainType.value : this.chainType,
        isEdited: isEdited.present ? isEdited.value : this.isEdited,
        showStatus: showStatus.present ? showStatus.value : this.showStatus,
        topIndex: topIndex.present ? topIndex.value : this.topIndex,
        isDel: isDel.present ? isDel.value : this.isDel,
        source: source.present ? source.value : this.source,
        serverConfRpc:
            serverConfRpc.present ? serverConfRpc.value : this.serverConfRpc,
        localConfRpc:
            localConfRpc.present ? localConfRpc.value : this.localConfRpc,
        updateTime: updateTime.present ? updateTime.value : this.updateTime,
      );
  @override
  String toString() {
    return (StringBuffer('NetworkInfoData(')
          ..write('id: $id, ')
          ..write('uuid: $uuid, ')
          ..write('rpcUrl: $rpcUrl, ')
          ..write('chainid: $chainid, ')
          ..write('name: $name, ')
          ..write('symbol: $symbol, ')
          ..write('browserUrl: $browserUrl, ')
          ..write('chainLogo: $chainLogo, ')
          ..write('chainPath: $chainPath, ')
          ..write('coinLogo: $coinLogo, ')
          ..write('coinPath: $coinPath, ')
          ..write('assetTips: $assetTips, ')
          ..write('createTime: $createTime, ')
          ..write('chainType: $chainType, ')
          ..write('isEdited: $isEdited, ')
          ..write('showStatus: $showStatus, ')
          ..write('topIndex: $topIndex, ')
          ..write('isDel: $isDel, ')
          ..write('source: $source, ')
          ..write('serverConfRpc: $serverConfRpc, ')
          ..write('localConfRpc: $localConfRpc, ')
          ..write('updateTime: $updateTime')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hashAll([
        id,
        uuid,
        rpcUrl,
        chainid,
        name,
        symbol,
        browserUrl,
        chainLogo,
        chainPath,
        coinLogo,
        coinPath,
        assetTips,
        createTime,
        chainType,
        isEdited,
        showStatus,
        topIndex,
        isDel,
        source,
        serverConfRpc,
        localConfRpc,
        updateTime
      ]);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is NetworkInfoData &&
          other.id == this.id &&
          other.uuid == this.uuid &&
          other.rpcUrl == this.rpcUrl &&
          other.chainid == this.chainid &&
          other.name == this.name &&
          other.symbol == this.symbol &&
          other.browserUrl == this.browserUrl &&
          other.chainLogo == this.chainLogo &&
          other.chainPath == this.chainPath &&
          other.coinLogo == this.coinLogo &&
          other.coinPath == this.coinPath &&
          other.assetTips == this.assetTips &&
          other.createTime == this.createTime &&
          other.chainType == this.chainType &&
          other.isEdited == this.isEdited &&
          other.showStatus == this.showStatus &&
          other.topIndex == this.topIndex &&
          other.isDel == this.isDel &&
          other.source == this.source &&
          other.serverConfRpc == this.serverConfRpc &&
          other.localConfRpc == this.localConfRpc &&
          other.updateTime == this.updateTime);
}

class NetworkInfoCompanion extends UpdateCompanion<NetworkInfoData> {
  final Value<int> id;
  final Value<String> uuid;
  final Value<String> rpcUrl;
  final Value<int> chainid;
  final Value<String?> name;
  final Value<String?> symbol;
  final Value<String?> browserUrl;
  final Value<String?> chainLogo;
  final Value<String?> chainPath;
  final Value<String?> coinLogo;
  final Value<String?> coinPath;
  final Value<String?> assetTips;
  final Value<double?> createTime;
  final Value<int?> chainType;
  final Value<bool?> isEdited;
  final Value<int?> showStatus;
  final Value<int?> topIndex;
  final Value<bool?> isDel;
  final Value<int?> source;
  final Value<String?> serverConfRpc;
  final Value<String?> localConfRpc;
  final Value<double?> updateTime;
  const NetworkInfoCompanion({
    this.id = const Value.absent(),
    this.uuid = const Value.absent(),
    this.rpcUrl = const Value.absent(),
    this.chainid = const Value.absent(),
    this.name = const Value.absent(),
    this.symbol = const Value.absent(),
    this.browserUrl = const Value.absent(),
    this.chainLogo = const Value.absent(),
    this.chainPath = const Value.absent(),
    this.coinLogo = const Value.absent(),
    this.coinPath = const Value.absent(),
    this.assetTips = const Value.absent(),
    this.createTime = const Value.absent(),
    this.chainType = const Value.absent(),
    this.isEdited = const Value.absent(),
    this.showStatus = const Value.absent(),
    this.topIndex = const Value.absent(),
    this.isDel = const Value.absent(),
    this.source = const Value.absent(),
    this.serverConfRpc = const Value.absent(),
    this.localConfRpc = const Value.absent(),
    this.updateTime = const Value.absent(),
  });
  NetworkInfoCompanion.insert({
    this.id = const Value.absent(),
    required String uuid,
    required String rpcUrl,
    required int chainid,
    this.name = const Value.absent(),
    this.symbol = const Value.absent(),
    this.browserUrl = const Value.absent(),
    this.chainLogo = const Value.absent(),
    this.chainPath = const Value.absent(),
    this.coinLogo = const Value.absent(),
    this.coinPath = const Value.absent(),
    this.assetTips = const Value.absent(),
    this.createTime = const Value.absent(),
    this.chainType = const Value.absent(),
    this.isEdited = const Value.absent(),
    this.showStatus = const Value.absent(),
    this.topIndex = const Value.absent(),
    this.isDel = const Value.absent(),
    this.source = const Value.absent(),
    this.serverConfRpc = const Value.absent(),
    this.localConfRpc = const Value.absent(),
    this.updateTime = const Value.absent(),
  })  : uuid = Value(uuid),
        rpcUrl = Value(rpcUrl),
        chainid = Value(chainid);
  static Insertable<NetworkInfoData> custom({
    Expression<int>? id,
    Expression<String>? uuid,
    Expression<String>? rpcUrl,
    Expression<int>? chainid,
    Expression<String>? name,
    Expression<String>? symbol,
    Expression<String>? browserUrl,
    Expression<String>? chainLogo,
    Expression<String>? chainPath,
    Expression<String>? coinLogo,
    Expression<String>? coinPath,
    Expression<String>? assetTips,
    Expression<double>? createTime,
    Expression<int>? chainType,
    Expression<bool>? isEdited,
    Expression<int>? showStatus,
    Expression<int>? topIndex,
    Expression<bool>? isDel,
    Expression<int>? source,
    Expression<String>? serverConfRpc,
    Expression<String>? localConfRpc,
    Expression<double>? updateTime,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (uuid != null) 'uuid': uuid,
      if (rpcUrl != null) 'rpc_url': rpcUrl,
      if (chainid != null) 'chainid': chainid,
      if (name != null) 'name': name,
      if (symbol != null) 'symbol': symbol,
      if (browserUrl != null) 'browser_url': browserUrl,
      if (chainLogo != null) 'chain_logo': chainLogo,
      if (chainPath != null) 'chain_path': chainPath,
      if (coinLogo != null) 'coin_logo': coinLogo,
      if (coinPath != null) 'coin_path': coinPath,
      if (assetTips != null) 'asset_tips': assetTips,
      if (createTime != null) 'create_time': createTime,
      if (chainType != null) 'chain_type': chainType,
      if (isEdited != null) 'isEdited': isEdited,
      if (showStatus != null) 'show_status': showStatus,
      if (topIndex != null) 'top_index': topIndex,
      if (isDel != null) 'isDel': isDel,
      if (source != null) 'source': source,
      if (serverConfRpc != null) 'server_conf_rpc': serverConfRpc,
      if (localConfRpc != null) 'local_conf_rpc': localConfRpc,
      if (updateTime != null) 'update_time': updateTime,
    });
  }

  NetworkInfoCompanion copyWith(
      {Value<int>? id,
      Value<String>? uuid,
      Value<String>? rpcUrl,
      Value<int>? chainid,
      Value<String?>? name,
      Value<String?>? symbol,
      Value<String?>? browserUrl,
      Value<String?>? chainLogo,
      Value<String?>? chainPath,
      Value<String?>? coinLogo,
      Value<String?>? coinPath,
      Value<String?>? assetTips,
      Value<double?>? createTime,
      Value<int?>? chainType,
      Value<bool?>? isEdited,
      Value<int?>? showStatus,
      Value<int?>? topIndex,
      Value<bool?>? isDel,
      Value<int?>? source,
      Value<String?>? serverConfRpc,
      Value<String?>? localConfRpc,
      Value<double?>? updateTime}) {
    return NetworkInfoCompanion(
      id: id ?? this.id,
      uuid: uuid ?? this.uuid,
      rpcUrl: rpcUrl ?? this.rpcUrl,
      chainid: chainid ?? this.chainid,
      name: name ?? this.name,
      symbol: symbol ?? this.symbol,
      browserUrl: browserUrl ?? this.browserUrl,
      chainLogo: chainLogo ?? this.chainLogo,
      chainPath: chainPath ?? this.chainPath,
      coinLogo: coinLogo ?? this.coinLogo,
      coinPath: coinPath ?? this.coinPath,
      assetTips: assetTips ?? this.assetTips,
      createTime: createTime ?? this.createTime,
      chainType: chainType ?? this.chainType,
      isEdited: isEdited ?? this.isEdited,
      showStatus: showStatus ?? this.showStatus,
      topIndex: topIndex ?? this.topIndex,
      isDel: isDel ?? this.isDel,
      source: source ?? this.source,
      serverConfRpc: serverConfRpc ?? this.serverConfRpc,
      localConfRpc: localConfRpc ?? this.localConfRpc,
      updateTime: updateTime ?? this.updateTime,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<int>(id.value);
    }
    if (uuid.present) {
      map['uuid'] = Variable<String>(uuid.value);
    }
    if (rpcUrl.present) {
      map['rpc_url'] = Variable<String>(rpcUrl.value);
    }
    if (chainid.present) {
      map['chainid'] = Variable<int>(chainid.value);
    }
    if (name.present) {
      map['name'] = Variable<String>(name.value);
    }
    if (symbol.present) {
      map['symbol'] = Variable<String>(symbol.value);
    }
    if (browserUrl.present) {
      map['browser_url'] = Variable<String>(browserUrl.value);
    }
    if (chainLogo.present) {
      map['chain_logo'] = Variable<String>(chainLogo.value);
    }
    if (chainPath.present) {
      map['chain_path'] = Variable<String>(chainPath.value);
    }
    if (coinLogo.present) {
      map['coin_logo'] = Variable<String>(coinLogo.value);
    }
    if (coinPath.present) {
      map['coin_path'] = Variable<String>(coinPath.value);
    }
    if (assetTips.present) {
      map['asset_tips'] = Variable<String>(assetTips.value);
    }
    if (createTime.present) {
      map['create_time'] = Variable<double>(createTime.value);
    }
    if (chainType.present) {
      map['chain_type'] = Variable<int>(chainType.value);
    }
    if (isEdited.present) {
      map['isEdited'] = Variable<bool>(isEdited.value);
    }
    if (showStatus.present) {
      map['show_status'] = Variable<int>(showStatus.value);
    }
    if (topIndex.present) {
      map['top_index'] = Variable<int>(topIndex.value);
    }
    if (isDel.present) {
      map['isDel'] = Variable<bool>(isDel.value);
    }
    if (source.present) {
      map['source'] = Variable<int>(source.value);
    }
    if (serverConfRpc.present) {
      map['server_conf_rpc'] = Variable<String>(serverConfRpc.value);
    }
    if (localConfRpc.present) {
      map['local_conf_rpc'] = Variable<String>(localConfRpc.value);
    }
    if (updateTime.present) {
      map['update_time'] = Variable<double>(updateTime.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('NetworkInfoCompanion(')
          ..write('id: $id, ')
          ..write('uuid: $uuid, ')
          ..write('rpcUrl: $rpcUrl, ')
          ..write('chainid: $chainid, ')
          ..write('name: $name, ')
          ..write('symbol: $symbol, ')
          ..write('browserUrl: $browserUrl, ')
          ..write('chainLogo: $chainLogo, ')
          ..write('chainPath: $chainPath, ')
          ..write('coinLogo: $coinLogo, ')
          ..write('coinPath: $coinPath, ')
          ..write('assetTips: $assetTips, ')
          ..write('createTime: $createTime, ')
          ..write('chainType: $chainType, ')
          ..write('isEdited: $isEdited, ')
          ..write('showStatus: $showStatus, ')
          ..write('topIndex: $topIndex, ')
          ..write('isDel: $isDel, ')
          ..write('source: $source, ')
          ..write('serverConfRpc: $serverConfRpc, ')
          ..write('localConfRpc: $localConfRpc, ')
          ..write('updateTime: $updateTime')
          ..write(')'))
        .toString();
  }
}

class TokenInfo extends Table with TableInfo<TokenInfo, TokenInfoData> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  TokenInfo(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  late final GeneratedColumn<int> id = GeneratedColumn<int>(
      'id', aliasedName, false,
      hasAutoIncrement: true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      $customConstraints: 'NOT NULL PRIMARY KEY AUTOINCREMENT');
  static const VerificationMeta _uuidMeta = const VerificationMeta('uuid');
  late final GeneratedColumn<String> uuid = GeneratedColumn<String>(
      'uuid', aliasedName, false,
      type: DriftSqlType.string,
      requiredDuringInsert: true,
      $customConstraints: 'NOT NULL UNIQUE');
  static const VerificationMeta _chainidMeta =
      const VerificationMeta('chainid');
  late final GeneratedColumn<int> chainid = GeneratedColumn<int>(
      'chainid', aliasedName, true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _addressMeta =
      const VerificationMeta('address');
  late final GeneratedColumn<String> address = GeneratedColumn<String>(
      'address', aliasedName, false,
      type: DriftSqlType.string,
      requiredDuringInsert: true,
      $customConstraints: 'NOT NULL');
  static const VerificationMeta _netUuidMeta =
      const VerificationMeta('netUuid');
  late final GeneratedColumn<String> netUuid = GeneratedColumn<String>(
      'net_uuid', aliasedName, false,
      type: DriftSqlType.string,
      requiredDuringInsert: true,
      $customConstraints: 'NOT NULL');
  static const VerificationMeta _nameMeta = const VerificationMeta('name');
  late final GeneratedColumn<String> name = GeneratedColumn<String>(
      'name', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _symbolMeta = const VerificationMeta('symbol');
  late final GeneratedColumn<String> symbol = GeneratedColumn<String>(
      'symbol', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _decimalsMeta =
      const VerificationMeta('decimals');
  late final GeneratedColumn<int> decimals = GeneratedColumn<int>(
      'decimals', aliasedName, true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _imageMeta = const VerificationMeta('image');
  late final GeneratedColumn<String> image = GeneratedColumn<String>(
      'image', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _chainTypeMeta =
      const VerificationMeta('chainType');
  late final GeneratedColumn<int> chainType = GeneratedColumn<int>(
      'chain_type', aliasedName, true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _balanceMeta =
      const VerificationMeta('balance');
  late final GeneratedColumn<double> balance = GeneratedColumn<double>(
      'balance', aliasedName, true,
      type: DriftSqlType.double,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _createTimeMeta =
      const VerificationMeta('createTime');
  late final GeneratedColumn<double> createTime = GeneratedColumn<double>(
      'create_time', aliasedName, true,
      type: DriftSqlType.double,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _coinLogoMeta =
      const VerificationMeta('coinLogo');
  late final GeneratedColumn<String> coinLogo = GeneratedColumn<String>(
      'coin_logo', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _coinPathMeta =
      const VerificationMeta('coinPath');
  late final GeneratedColumn<String> coinPath = GeneratedColumn<String>(
      'coin_path', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _showStatusMeta =
      const VerificationMeta('showStatus');
  late final GeneratedColumn<int> showStatus = GeneratedColumn<int>(
      'show_status', aliasedName, true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _topIndexMeta =
      const VerificationMeta('topIndex');
  late final GeneratedColumn<int> topIndex = GeneratedColumn<int>(
      'top_index', aliasedName, true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _sourceMeta = const VerificationMeta('source');
  late final GeneratedColumn<int> source = GeneratedColumn<int>(
      'source', aliasedName, true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _isDelMeta = const VerificationMeta('isDel');
  late final GeneratedColumn<bool> isDel = GeneratedColumn<bool>(
      'isDel', aliasedName, true,
      type: DriftSqlType.bool,
      requiredDuringInsert: false,
      $customConstraints: 'DEFAULT FALSE',
      defaultValue: const CustomExpression('FALSE'));
  static const VerificationMeta _updateTimeMeta =
      const VerificationMeta('updateTime');
  late final GeneratedColumn<double> updateTime = GeneratedColumn<double>(
      'update_time', aliasedName, true,
      type: DriftSqlType.double,
      requiredDuringInsert: false,
      $customConstraints: '');
  @override
  List<GeneratedColumn> get $columns => [
        id,
        uuid,
        chainid,
        address,
        netUuid,
        name,
        symbol,
        decimals,
        image,
        chainType,
        balance,
        createTime,
        coinLogo,
        coinPath,
        showStatus,
        topIndex,
        source,
        isDel,
        updateTime
      ];
  @override
  String get aliasedName => _alias ?? 'token_info';
  @override
  String get actualTableName => 'token_info';
  @override
  VerificationContext validateIntegrity(Insertable<TokenInfoData> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    }
    if (data.containsKey('uuid')) {
      context.handle(
          _uuidMeta, uuid.isAcceptableOrUnknown(data['uuid']!, _uuidMeta));
    } else if (isInserting) {
      context.missing(_uuidMeta);
    }
    if (data.containsKey('chainid')) {
      context.handle(_chainidMeta,
          chainid.isAcceptableOrUnknown(data['chainid']!, _chainidMeta));
    }
    if (data.containsKey('address')) {
      context.handle(_addressMeta,
          address.isAcceptableOrUnknown(data['address']!, _addressMeta));
    } else if (isInserting) {
      context.missing(_addressMeta);
    }
    if (data.containsKey('net_uuid')) {
      context.handle(_netUuidMeta,
          netUuid.isAcceptableOrUnknown(data['net_uuid']!, _netUuidMeta));
    } else if (isInserting) {
      context.missing(_netUuidMeta);
    }
    if (data.containsKey('name')) {
      context.handle(
          _nameMeta, name.isAcceptableOrUnknown(data['name']!, _nameMeta));
    }
    if (data.containsKey('symbol')) {
      context.handle(_symbolMeta,
          symbol.isAcceptableOrUnknown(data['symbol']!, _symbolMeta));
    }
    if (data.containsKey('decimals')) {
      context.handle(_decimalsMeta,
          decimals.isAcceptableOrUnknown(data['decimals']!, _decimalsMeta));
    }
    if (data.containsKey('image')) {
      context.handle(
          _imageMeta, image.isAcceptableOrUnknown(data['image']!, _imageMeta));
    }
    if (data.containsKey('chain_type')) {
      context.handle(_chainTypeMeta,
          chainType.isAcceptableOrUnknown(data['chain_type']!, _chainTypeMeta));
    }
    if (data.containsKey('balance')) {
      context.handle(_balanceMeta,
          balance.isAcceptableOrUnknown(data['balance']!, _balanceMeta));
    }
    if (data.containsKey('create_time')) {
      context.handle(
          _createTimeMeta,
          createTime.isAcceptableOrUnknown(
              data['create_time']!, _createTimeMeta));
    }
    if (data.containsKey('coin_logo')) {
      context.handle(_coinLogoMeta,
          coinLogo.isAcceptableOrUnknown(data['coin_logo']!, _coinLogoMeta));
    }
    if (data.containsKey('coin_path')) {
      context.handle(_coinPathMeta,
          coinPath.isAcceptableOrUnknown(data['coin_path']!, _coinPathMeta));
    }
    if (data.containsKey('show_status')) {
      context.handle(
          _showStatusMeta,
          showStatus.isAcceptableOrUnknown(
              data['show_status']!, _showStatusMeta));
    }
    if (data.containsKey('top_index')) {
      context.handle(_topIndexMeta,
          topIndex.isAcceptableOrUnknown(data['top_index']!, _topIndexMeta));
    }
    if (data.containsKey('source')) {
      context.handle(_sourceMeta,
          source.isAcceptableOrUnknown(data['source']!, _sourceMeta));
    }
    if (data.containsKey('isDel')) {
      context.handle(
          _isDelMeta, isDel.isAcceptableOrUnknown(data['isDel']!, _isDelMeta));
    }
    if (data.containsKey('update_time')) {
      context.handle(
          _updateTimeMeta,
          updateTime.isAcceptableOrUnknown(
              data['update_time']!, _updateTimeMeta));
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  TokenInfoData map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return TokenInfoData(
      id: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}id'])!,
      uuid: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}uuid'])!,
      chainid: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}chainid']),
      address: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}address'])!,
      netUuid: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}net_uuid'])!,
      name: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}name']),
      symbol: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}symbol']),
      decimals: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}decimals']),
      image: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}image']),
      chainType: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}chain_type']),
      balance: attachedDatabase.typeMapping
          .read(DriftSqlType.double, data['${effectivePrefix}balance']),
      createTime: attachedDatabase.typeMapping
          .read(DriftSqlType.double, data['${effectivePrefix}create_time']),
      coinLogo: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}coin_logo']),
      coinPath: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}coin_path']),
      showStatus: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}show_status']),
      topIndex: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}top_index']),
      source: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}source']),
      isDel: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}isDel']),
      updateTime: attachedDatabase.typeMapping
          .read(DriftSqlType.double, data['${effectivePrefix}update_time']),
    );
  }

  @override
  TokenInfo createAlias(String alias) {
    return TokenInfo(attachedDatabase, alias);
  }

  @override
  bool get dontWriteConstraints => true;
}

class TokenInfoData extends DataClass implements Insertable<TokenInfoData> {
  final int id;
  final String uuid;
  final int? chainid;
  final String address;
  final String netUuid;
  final String? name;
  final String? symbol;
  final int? decimals;
  final String? image;
  final int? chainType;
  final double? balance;
  final double? createTime;
  final String? coinLogo;
  final String? coinPath;
  final int? showStatus;
  final int? topIndex;
  final int? source;
  final bool? isDel;
  final double? updateTime;
  const TokenInfoData(
      {required this.id,
      required this.uuid,
      this.chainid,
      required this.address,
      required this.netUuid,
      this.name,
      this.symbol,
      this.decimals,
      this.image,
      this.chainType,
      this.balance,
      this.createTime,
      this.coinLogo,
      this.coinPath,
      this.showStatus,
      this.topIndex,
      this.source,
      this.isDel,
      this.updateTime});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<int>(id);
    map['uuid'] = Variable<String>(uuid);
    if (!nullToAbsent || chainid != null) {
      map['chainid'] = Variable<int>(chainid);
    }
    map['address'] = Variable<String>(address);
    map['net_uuid'] = Variable<String>(netUuid);
    if (!nullToAbsent || name != null) {
      map['name'] = Variable<String>(name);
    }
    if (!nullToAbsent || symbol != null) {
      map['symbol'] = Variable<String>(symbol);
    }
    if (!nullToAbsent || decimals != null) {
      map['decimals'] = Variable<int>(decimals);
    }
    if (!nullToAbsent || image != null) {
      map['image'] = Variable<String>(image);
    }
    if (!nullToAbsent || chainType != null) {
      map['chain_type'] = Variable<int>(chainType);
    }
    if (!nullToAbsent || balance != null) {
      map['balance'] = Variable<double>(balance);
    }
    if (!nullToAbsent || createTime != null) {
      map['create_time'] = Variable<double>(createTime);
    }
    if (!nullToAbsent || coinLogo != null) {
      map['coin_logo'] = Variable<String>(coinLogo);
    }
    if (!nullToAbsent || coinPath != null) {
      map['coin_path'] = Variable<String>(coinPath);
    }
    if (!nullToAbsent || showStatus != null) {
      map['show_status'] = Variable<int>(showStatus);
    }
    if (!nullToAbsent || topIndex != null) {
      map['top_index'] = Variable<int>(topIndex);
    }
    if (!nullToAbsent || source != null) {
      map['source'] = Variable<int>(source);
    }
    if (!nullToAbsent || isDel != null) {
      map['isDel'] = Variable<bool>(isDel);
    }
    if (!nullToAbsent || updateTime != null) {
      map['update_time'] = Variable<double>(updateTime);
    }
    return map;
  }

  TokenInfoCompanion toCompanion(bool nullToAbsent) {
    return TokenInfoCompanion(
      id: Value(id),
      uuid: Value(uuid),
      chainid: chainid == null && nullToAbsent
          ? const Value.absent()
          : Value(chainid),
      address: Value(address),
      netUuid: Value(netUuid),
      name: name == null && nullToAbsent ? const Value.absent() : Value(name),
      symbol:
          symbol == null && nullToAbsent ? const Value.absent() : Value(symbol),
      decimals: decimals == null && nullToAbsent
          ? const Value.absent()
          : Value(decimals),
      image:
          image == null && nullToAbsent ? const Value.absent() : Value(image),
      chainType: chainType == null && nullToAbsent
          ? const Value.absent()
          : Value(chainType),
      balance: balance == null && nullToAbsent
          ? const Value.absent()
          : Value(balance),
      createTime: createTime == null && nullToAbsent
          ? const Value.absent()
          : Value(createTime),
      coinLogo: coinLogo == null && nullToAbsent
          ? const Value.absent()
          : Value(coinLogo),
      coinPath: coinPath == null && nullToAbsent
          ? const Value.absent()
          : Value(coinPath),
      showStatus: showStatus == null && nullToAbsent
          ? const Value.absent()
          : Value(showStatus),
      topIndex: topIndex == null && nullToAbsent
          ? const Value.absent()
          : Value(topIndex),
      source:
          source == null && nullToAbsent ? const Value.absent() : Value(source),
      isDel:
          isDel == null && nullToAbsent ? const Value.absent() : Value(isDel),
      updateTime: updateTime == null && nullToAbsent
          ? const Value.absent()
          : Value(updateTime),
    );
  }

  factory TokenInfoData.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return TokenInfoData(
      id: serializer.fromJson<int>(json['id']),
      uuid: serializer.fromJson<String>(json['uuid']),
      chainid: serializer.fromJson<int?>(json['chainid']),
      address: serializer.fromJson<String>(json['address']),
      netUuid: serializer.fromJson<String>(json['net_uuid']),
      name: serializer.fromJson<String?>(json['name']),
      symbol: serializer.fromJson<String?>(json['symbol']),
      decimals: serializer.fromJson<int?>(json['decimals']),
      image: serializer.fromJson<String?>(json['image']),
      chainType: serializer.fromJson<int?>(json['chain_type']),
      balance: serializer.fromJson<double?>(json['balance']),
      createTime: serializer.fromJson<double?>(json['create_time']),
      coinLogo: serializer.fromJson<String?>(json['coin_logo']),
      coinPath: serializer.fromJson<String?>(json['coin_path']),
      showStatus: serializer.fromJson<int?>(json['show_status']),
      topIndex: serializer.fromJson<int?>(json['top_index']),
      source: serializer.fromJson<int?>(json['source']),
      isDel: serializer.fromJson<bool?>(json['isDel']),
      updateTime: serializer.fromJson<double?>(json['update_time']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<int>(id),
      'uuid': serializer.toJson<String>(uuid),
      'chainid': serializer.toJson<int?>(chainid),
      'address': serializer.toJson<String>(address),
      'net_uuid': serializer.toJson<String>(netUuid),
      'name': serializer.toJson<String?>(name),
      'symbol': serializer.toJson<String?>(symbol),
      'decimals': serializer.toJson<int?>(decimals),
      'image': serializer.toJson<String?>(image),
      'chain_type': serializer.toJson<int?>(chainType),
      'balance': serializer.toJson<double?>(balance),
      'create_time': serializer.toJson<double?>(createTime),
      'coin_logo': serializer.toJson<String?>(coinLogo),
      'coin_path': serializer.toJson<String?>(coinPath),
      'show_status': serializer.toJson<int?>(showStatus),
      'top_index': serializer.toJson<int?>(topIndex),
      'source': serializer.toJson<int?>(source),
      'isDel': serializer.toJson<bool?>(isDel),
      'update_time': serializer.toJson<double?>(updateTime),
    };
  }

  TokenInfoData copyWith(
          {int? id,
          String? uuid,
          Value<int?> chainid = const Value.absent(),
          String? address,
          String? netUuid,
          Value<String?> name = const Value.absent(),
          Value<String?> symbol = const Value.absent(),
          Value<int?> decimals = const Value.absent(),
          Value<String?> image = const Value.absent(),
          Value<int?> chainType = const Value.absent(),
          Value<double?> balance = const Value.absent(),
          Value<double?> createTime = const Value.absent(),
          Value<String?> coinLogo = const Value.absent(),
          Value<String?> coinPath = const Value.absent(),
          Value<int?> showStatus = const Value.absent(),
          Value<int?> topIndex = const Value.absent(),
          Value<int?> source = const Value.absent(),
          Value<bool?> isDel = const Value.absent(),
          Value<double?> updateTime = const Value.absent()}) =>
      TokenInfoData(
        id: id ?? this.id,
        uuid: uuid ?? this.uuid,
        chainid: chainid.present ? chainid.value : this.chainid,
        address: address ?? this.address,
        netUuid: netUuid ?? this.netUuid,
        name: name.present ? name.value : this.name,
        symbol: symbol.present ? symbol.value : this.symbol,
        decimals: decimals.present ? decimals.value : this.decimals,
        image: image.present ? image.value : this.image,
        chainType: chainType.present ? chainType.value : this.chainType,
        balance: balance.present ? balance.value : this.balance,
        createTime: createTime.present ? createTime.value : this.createTime,
        coinLogo: coinLogo.present ? coinLogo.value : this.coinLogo,
        coinPath: coinPath.present ? coinPath.value : this.coinPath,
        showStatus: showStatus.present ? showStatus.value : this.showStatus,
        topIndex: topIndex.present ? topIndex.value : this.topIndex,
        source: source.present ? source.value : this.source,
        isDel: isDel.present ? isDel.value : this.isDel,
        updateTime: updateTime.present ? updateTime.value : this.updateTime,
      );
  @override
  String toString() {
    return (StringBuffer('TokenInfoData(')
          ..write('id: $id, ')
          ..write('uuid: $uuid, ')
          ..write('chainid: $chainid, ')
          ..write('address: $address, ')
          ..write('netUuid: $netUuid, ')
          ..write('name: $name, ')
          ..write('symbol: $symbol, ')
          ..write('decimals: $decimals, ')
          ..write('image: $image, ')
          ..write('chainType: $chainType, ')
          ..write('balance: $balance, ')
          ..write('createTime: $createTime, ')
          ..write('coinLogo: $coinLogo, ')
          ..write('coinPath: $coinPath, ')
          ..write('showStatus: $showStatus, ')
          ..write('topIndex: $topIndex, ')
          ..write('source: $source, ')
          ..write('isDel: $isDel, ')
          ..write('updateTime: $updateTime')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(
      id,
      uuid,
      chainid,
      address,
      netUuid,
      name,
      symbol,
      decimals,
      image,
      chainType,
      balance,
      createTime,
      coinLogo,
      coinPath,
      showStatus,
      topIndex,
      source,
      isDel,
      updateTime);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is TokenInfoData &&
          other.id == this.id &&
          other.uuid == this.uuid &&
          other.chainid == this.chainid &&
          other.address == this.address &&
          other.netUuid == this.netUuid &&
          other.name == this.name &&
          other.symbol == this.symbol &&
          other.decimals == this.decimals &&
          other.image == this.image &&
          other.chainType == this.chainType &&
          other.balance == this.balance &&
          other.createTime == this.createTime &&
          other.coinLogo == this.coinLogo &&
          other.coinPath == this.coinPath &&
          other.showStatus == this.showStatus &&
          other.topIndex == this.topIndex &&
          other.source == this.source &&
          other.isDel == this.isDel &&
          other.updateTime == this.updateTime);
}

class TokenInfoCompanion extends UpdateCompanion<TokenInfoData> {
  final Value<int> id;
  final Value<String> uuid;
  final Value<int?> chainid;
  final Value<String> address;
  final Value<String> netUuid;
  final Value<String?> name;
  final Value<String?> symbol;
  final Value<int?> decimals;
  final Value<String?> image;
  final Value<int?> chainType;
  final Value<double?> balance;
  final Value<double?> createTime;
  final Value<String?> coinLogo;
  final Value<String?> coinPath;
  final Value<int?> showStatus;
  final Value<int?> topIndex;
  final Value<int?> source;
  final Value<bool?> isDel;
  final Value<double?> updateTime;
  const TokenInfoCompanion({
    this.id = const Value.absent(),
    this.uuid = const Value.absent(),
    this.chainid = const Value.absent(),
    this.address = const Value.absent(),
    this.netUuid = const Value.absent(),
    this.name = const Value.absent(),
    this.symbol = const Value.absent(),
    this.decimals = const Value.absent(),
    this.image = const Value.absent(),
    this.chainType = const Value.absent(),
    this.balance = const Value.absent(),
    this.createTime = const Value.absent(),
    this.coinLogo = const Value.absent(),
    this.coinPath = const Value.absent(),
    this.showStatus = const Value.absent(),
    this.topIndex = const Value.absent(),
    this.source = const Value.absent(),
    this.isDel = const Value.absent(),
    this.updateTime = const Value.absent(),
  });
  TokenInfoCompanion.insert({
    this.id = const Value.absent(),
    required String uuid,
    this.chainid = const Value.absent(),
    required String address,
    required String netUuid,
    this.name = const Value.absent(),
    this.symbol = const Value.absent(),
    this.decimals = const Value.absent(),
    this.image = const Value.absent(),
    this.chainType = const Value.absent(),
    this.balance = const Value.absent(),
    this.createTime = const Value.absent(),
    this.coinLogo = const Value.absent(),
    this.coinPath = const Value.absent(),
    this.showStatus = const Value.absent(),
    this.topIndex = const Value.absent(),
    this.source = const Value.absent(),
    this.isDel = const Value.absent(),
    this.updateTime = const Value.absent(),
  })  : uuid = Value(uuid),
        address = Value(address),
        netUuid = Value(netUuid);
  static Insertable<TokenInfoData> custom({
    Expression<int>? id,
    Expression<String>? uuid,
    Expression<int>? chainid,
    Expression<String>? address,
    Expression<String>? netUuid,
    Expression<String>? name,
    Expression<String>? symbol,
    Expression<int>? decimals,
    Expression<String>? image,
    Expression<int>? chainType,
    Expression<double>? balance,
    Expression<double>? createTime,
    Expression<String>? coinLogo,
    Expression<String>? coinPath,
    Expression<int>? showStatus,
    Expression<int>? topIndex,
    Expression<int>? source,
    Expression<bool>? isDel,
    Expression<double>? updateTime,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (uuid != null) 'uuid': uuid,
      if (chainid != null) 'chainid': chainid,
      if (address != null) 'address': address,
      if (netUuid != null) 'net_uuid': netUuid,
      if (name != null) 'name': name,
      if (symbol != null) 'symbol': symbol,
      if (decimals != null) 'decimals': decimals,
      if (image != null) 'image': image,
      if (chainType != null) 'chain_type': chainType,
      if (balance != null) 'balance': balance,
      if (createTime != null) 'create_time': createTime,
      if (coinLogo != null) 'coin_logo': coinLogo,
      if (coinPath != null) 'coin_path': coinPath,
      if (showStatus != null) 'show_status': showStatus,
      if (topIndex != null) 'top_index': topIndex,
      if (source != null) 'source': source,
      if (isDel != null) 'isDel': isDel,
      if (updateTime != null) 'update_time': updateTime,
    });
  }

  TokenInfoCompanion copyWith(
      {Value<int>? id,
      Value<String>? uuid,
      Value<int?>? chainid,
      Value<String>? address,
      Value<String>? netUuid,
      Value<String?>? name,
      Value<String?>? symbol,
      Value<int?>? decimals,
      Value<String?>? image,
      Value<int?>? chainType,
      Value<double?>? balance,
      Value<double?>? createTime,
      Value<String?>? coinLogo,
      Value<String?>? coinPath,
      Value<int?>? showStatus,
      Value<int?>? topIndex,
      Value<int?>? source,
      Value<bool?>? isDel,
      Value<double?>? updateTime}) {
    return TokenInfoCompanion(
      id: id ?? this.id,
      uuid: uuid ?? this.uuid,
      chainid: chainid ?? this.chainid,
      address: address ?? this.address,
      netUuid: netUuid ?? this.netUuid,
      name: name ?? this.name,
      symbol: symbol ?? this.symbol,
      decimals: decimals ?? this.decimals,
      image: image ?? this.image,
      chainType: chainType ?? this.chainType,
      balance: balance ?? this.balance,
      createTime: createTime ?? this.createTime,
      coinLogo: coinLogo ?? this.coinLogo,
      coinPath: coinPath ?? this.coinPath,
      showStatus: showStatus ?? this.showStatus,
      topIndex: topIndex ?? this.topIndex,
      source: source ?? this.source,
      isDel: isDel ?? this.isDel,
      updateTime: updateTime ?? this.updateTime,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<int>(id.value);
    }
    if (uuid.present) {
      map['uuid'] = Variable<String>(uuid.value);
    }
    if (chainid.present) {
      map['chainid'] = Variable<int>(chainid.value);
    }
    if (address.present) {
      map['address'] = Variable<String>(address.value);
    }
    if (netUuid.present) {
      map['net_uuid'] = Variable<String>(netUuid.value);
    }
    if (name.present) {
      map['name'] = Variable<String>(name.value);
    }
    if (symbol.present) {
      map['symbol'] = Variable<String>(symbol.value);
    }
    if (decimals.present) {
      map['decimals'] = Variable<int>(decimals.value);
    }
    if (image.present) {
      map['image'] = Variable<String>(image.value);
    }
    if (chainType.present) {
      map['chain_type'] = Variable<int>(chainType.value);
    }
    if (balance.present) {
      map['balance'] = Variable<double>(balance.value);
    }
    if (createTime.present) {
      map['create_time'] = Variable<double>(createTime.value);
    }
    if (coinLogo.present) {
      map['coin_logo'] = Variable<String>(coinLogo.value);
    }
    if (coinPath.present) {
      map['coin_path'] = Variable<String>(coinPath.value);
    }
    if (showStatus.present) {
      map['show_status'] = Variable<int>(showStatus.value);
    }
    if (topIndex.present) {
      map['top_index'] = Variable<int>(topIndex.value);
    }
    if (source.present) {
      map['source'] = Variable<int>(source.value);
    }
    if (isDel.present) {
      map['isDel'] = Variable<bool>(isDel.value);
    }
    if (updateTime.present) {
      map['update_time'] = Variable<double>(updateTime.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('TokenInfoCompanion(')
          ..write('id: $id, ')
          ..write('uuid: $uuid, ')
          ..write('chainid: $chainid, ')
          ..write('address: $address, ')
          ..write('netUuid: $netUuid, ')
          ..write('name: $name, ')
          ..write('symbol: $symbol, ')
          ..write('decimals: $decimals, ')
          ..write('image: $image, ')
          ..write('chainType: $chainType, ')
          ..write('balance: $balance, ')
          ..write('createTime: $createTime, ')
          ..write('coinLogo: $coinLogo, ')
          ..write('coinPath: $coinPath, ')
          ..write('showStatus: $showStatus, ')
          ..write('topIndex: $topIndex, ')
          ..write('source: $source, ')
          ..write('isDel: $isDel, ')
          ..write('updateTime: $updateTime')
          ..write(')'))
        .toString();
  }
}

class DappConnect extends Table with TableInfo<DappConnect, DappConnectData> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  DappConnect(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  late final GeneratedColumn<int> id = GeneratedColumn<int>(
      'id', aliasedName, false,
      hasAutoIncrement: true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      $customConstraints: 'NOT NULL PRIMARY KEY AUTOINCREMENT');
  static const VerificationMeta _uuidMeta = const VerificationMeta('uuid');
  late final GeneratedColumn<String> uuid = GeneratedColumn<String>(
      'uuid', aliasedName, false,
      type: DriftSqlType.string,
      requiredDuringInsert: true,
      $customConstraints: 'NOT NULL UNIQUE');
  static const VerificationMeta _urlMeta = const VerificationMeta('url');
  late final GeneratedColumn<String> url = GeneratedColumn<String>(
      'url', aliasedName, false,
      type: DriftSqlType.string,
      requiredDuringInsert: true,
      $customConstraints: 'NOT NULL');
  static const VerificationMeta _iconUrlMeta =
      const VerificationMeta('iconUrl');
  late final GeneratedColumn<String> iconUrl = GeneratedColumn<String>(
      'icon_url', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _nameMeta = const VerificationMeta('name');
  late final GeneratedColumn<String> name = GeneratedColumn<String>(
      'name', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _chainTypeMeta =
      const VerificationMeta('chainType');
  late final GeneratedColumn<int> chainType = GeneratedColumn<int>(
      'chain_type', aliasedName, true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _netUuidMeta =
      const VerificationMeta('netUuid');
  late final GeneratedColumn<String> netUuid = GeneratedColumn<String>(
      'net_uuid', aliasedName, false,
      type: DriftSqlType.string,
      requiredDuringInsert: true,
      $customConstraints: 'NOT NULL');
  static const VerificationMeta _walletAddressMeta =
      const VerificationMeta('walletAddress');
  late final GeneratedColumn<String> walletAddress = GeneratedColumn<String>(
      'wallet_address', aliasedName, false,
      type: DriftSqlType.string,
      requiredDuringInsert: true,
      $customConstraints: 'NOT NULL');
  static const VerificationMeta _createTimeMeta =
      const VerificationMeta('createTime');
  late final GeneratedColumn<double> createTime = GeneratedColumn<double>(
      'create_time', aliasedName, true,
      type: DriftSqlType.double,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _updateTimeMeta =
      const VerificationMeta('updateTime');
  late final GeneratedColumn<double> updateTime = GeneratedColumn<double>(
      'update_time', aliasedName, true,
      type: DriftSqlType.double,
      requiredDuringInsert: false,
      $customConstraints: '');
  @override
  List<GeneratedColumn> get $columns => [
        id,
        uuid,
        url,
        iconUrl,
        name,
        chainType,
        netUuid,
        walletAddress,
        createTime,
        updateTime
      ];
  @override
  String get aliasedName => _alias ?? 'dapp_connect';
  @override
  String get actualTableName => 'dapp_connect';
  @override
  VerificationContext validateIntegrity(Insertable<DappConnectData> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    }
    if (data.containsKey('uuid')) {
      context.handle(
          _uuidMeta, uuid.isAcceptableOrUnknown(data['uuid']!, _uuidMeta));
    } else if (isInserting) {
      context.missing(_uuidMeta);
    }
    if (data.containsKey('url')) {
      context.handle(
          _urlMeta, url.isAcceptableOrUnknown(data['url']!, _urlMeta));
    } else if (isInserting) {
      context.missing(_urlMeta);
    }
    if (data.containsKey('icon_url')) {
      context.handle(_iconUrlMeta,
          iconUrl.isAcceptableOrUnknown(data['icon_url']!, _iconUrlMeta));
    }
    if (data.containsKey('name')) {
      context.handle(
          _nameMeta, name.isAcceptableOrUnknown(data['name']!, _nameMeta));
    }
    if (data.containsKey('chain_type')) {
      context.handle(_chainTypeMeta,
          chainType.isAcceptableOrUnknown(data['chain_type']!, _chainTypeMeta));
    }
    if (data.containsKey('net_uuid')) {
      context.handle(_netUuidMeta,
          netUuid.isAcceptableOrUnknown(data['net_uuid']!, _netUuidMeta));
    } else if (isInserting) {
      context.missing(_netUuidMeta);
    }
    if (data.containsKey('wallet_address')) {
      context.handle(
          _walletAddressMeta,
          walletAddress.isAcceptableOrUnknown(
              data['wallet_address']!, _walletAddressMeta));
    } else if (isInserting) {
      context.missing(_walletAddressMeta);
    }
    if (data.containsKey('create_time')) {
      context.handle(
          _createTimeMeta,
          createTime.isAcceptableOrUnknown(
              data['create_time']!, _createTimeMeta));
    }
    if (data.containsKey('update_time')) {
      context.handle(
          _updateTimeMeta,
          updateTime.isAcceptableOrUnknown(
              data['update_time']!, _updateTimeMeta));
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  DappConnectData map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return DappConnectData(
      id: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}id'])!,
      uuid: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}uuid'])!,
      url: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}url'])!,
      iconUrl: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}icon_url']),
      name: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}name']),
      chainType: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}chain_type']),
      netUuid: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}net_uuid'])!,
      walletAddress: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}wallet_address'])!,
      createTime: attachedDatabase.typeMapping
          .read(DriftSqlType.double, data['${effectivePrefix}create_time']),
      updateTime: attachedDatabase.typeMapping
          .read(DriftSqlType.double, data['${effectivePrefix}update_time']),
    );
  }

  @override
  DappConnect createAlias(String alias) {
    return DappConnect(attachedDatabase, alias);
  }

  @override
  bool get dontWriteConstraints => true;
}

class DappConnectData extends DataClass implements Insertable<DappConnectData> {
  final int id;
  final String uuid;
  final String url;
  final String? iconUrl;
  final String? name;
  final int? chainType;
  final String netUuid;
  final String walletAddress;
  final double? createTime;
  final double? updateTime;
  const DappConnectData(
      {required this.id,
      required this.uuid,
      required this.url,
      this.iconUrl,
      this.name,
      this.chainType,
      required this.netUuid,
      required this.walletAddress,
      this.createTime,
      this.updateTime});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<int>(id);
    map['uuid'] = Variable<String>(uuid);
    map['url'] = Variable<String>(url);
    if (!nullToAbsent || iconUrl != null) {
      map['icon_url'] = Variable<String>(iconUrl);
    }
    if (!nullToAbsent || name != null) {
      map['name'] = Variable<String>(name);
    }
    if (!nullToAbsent || chainType != null) {
      map['chain_type'] = Variable<int>(chainType);
    }
    map['net_uuid'] = Variable<String>(netUuid);
    map['wallet_address'] = Variable<String>(walletAddress);
    if (!nullToAbsent || createTime != null) {
      map['create_time'] = Variable<double>(createTime);
    }
    if (!nullToAbsent || updateTime != null) {
      map['update_time'] = Variable<double>(updateTime);
    }
    return map;
  }

  DappConnectCompanion toCompanion(bool nullToAbsent) {
    return DappConnectCompanion(
      id: Value(id),
      uuid: Value(uuid),
      url: Value(url),
      iconUrl: iconUrl == null && nullToAbsent
          ? const Value.absent()
          : Value(iconUrl),
      name: name == null && nullToAbsent ? const Value.absent() : Value(name),
      chainType: chainType == null && nullToAbsent
          ? const Value.absent()
          : Value(chainType),
      netUuid: Value(netUuid),
      walletAddress: Value(walletAddress),
      createTime: createTime == null && nullToAbsent
          ? const Value.absent()
          : Value(createTime),
      updateTime: updateTime == null && nullToAbsent
          ? const Value.absent()
          : Value(updateTime),
    );
  }

  factory DappConnectData.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return DappConnectData(
      id: serializer.fromJson<int>(json['id']),
      uuid: serializer.fromJson<String>(json['uuid']),
      url: serializer.fromJson<String>(json['url']),
      iconUrl: serializer.fromJson<String?>(json['icon_url']),
      name: serializer.fromJson<String?>(json['name']),
      chainType: serializer.fromJson<int?>(json['chain_type']),
      netUuid: serializer.fromJson<String>(json['net_uuid']),
      walletAddress: serializer.fromJson<String>(json['wallet_address']),
      createTime: serializer.fromJson<double?>(json['create_time']),
      updateTime: serializer.fromJson<double?>(json['update_time']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<int>(id),
      'uuid': serializer.toJson<String>(uuid),
      'url': serializer.toJson<String>(url),
      'icon_url': serializer.toJson<String?>(iconUrl),
      'name': serializer.toJson<String?>(name),
      'chain_type': serializer.toJson<int?>(chainType),
      'net_uuid': serializer.toJson<String>(netUuid),
      'wallet_address': serializer.toJson<String>(walletAddress),
      'create_time': serializer.toJson<double?>(createTime),
      'update_time': serializer.toJson<double?>(updateTime),
    };
  }

  DappConnectData copyWith(
          {int? id,
          String? uuid,
          String? url,
          Value<String?> iconUrl = const Value.absent(),
          Value<String?> name = const Value.absent(),
          Value<int?> chainType = const Value.absent(),
          String? netUuid,
          String? walletAddress,
          Value<double?> createTime = const Value.absent(),
          Value<double?> updateTime = const Value.absent()}) =>
      DappConnectData(
        id: id ?? this.id,
        uuid: uuid ?? this.uuid,
        url: url ?? this.url,
        iconUrl: iconUrl.present ? iconUrl.value : this.iconUrl,
        name: name.present ? name.value : this.name,
        chainType: chainType.present ? chainType.value : this.chainType,
        netUuid: netUuid ?? this.netUuid,
        walletAddress: walletAddress ?? this.walletAddress,
        createTime: createTime.present ? createTime.value : this.createTime,
        updateTime: updateTime.present ? updateTime.value : this.updateTime,
      );
  @override
  String toString() {
    return (StringBuffer('DappConnectData(')
          ..write('id: $id, ')
          ..write('uuid: $uuid, ')
          ..write('url: $url, ')
          ..write('iconUrl: $iconUrl, ')
          ..write('name: $name, ')
          ..write('chainType: $chainType, ')
          ..write('netUuid: $netUuid, ')
          ..write('walletAddress: $walletAddress, ')
          ..write('createTime: $createTime, ')
          ..write('updateTime: $updateTime')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(id, uuid, url, iconUrl, name, chainType,
      netUuid, walletAddress, createTime, updateTime);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is DappConnectData &&
          other.id == this.id &&
          other.uuid == this.uuid &&
          other.url == this.url &&
          other.iconUrl == this.iconUrl &&
          other.name == this.name &&
          other.chainType == this.chainType &&
          other.netUuid == this.netUuid &&
          other.walletAddress == this.walletAddress &&
          other.createTime == this.createTime &&
          other.updateTime == this.updateTime);
}

class DappConnectCompanion extends UpdateCompanion<DappConnectData> {
  final Value<int> id;
  final Value<String> uuid;
  final Value<String> url;
  final Value<String?> iconUrl;
  final Value<String?> name;
  final Value<int?> chainType;
  final Value<String> netUuid;
  final Value<String> walletAddress;
  final Value<double?> createTime;
  final Value<double?> updateTime;
  const DappConnectCompanion({
    this.id = const Value.absent(),
    this.uuid = const Value.absent(),
    this.url = const Value.absent(),
    this.iconUrl = const Value.absent(),
    this.name = const Value.absent(),
    this.chainType = const Value.absent(),
    this.netUuid = const Value.absent(),
    this.walletAddress = const Value.absent(),
    this.createTime = const Value.absent(),
    this.updateTime = const Value.absent(),
  });
  DappConnectCompanion.insert({
    this.id = const Value.absent(),
    required String uuid,
    required String url,
    this.iconUrl = const Value.absent(),
    this.name = const Value.absent(),
    this.chainType = const Value.absent(),
    required String netUuid,
    required String walletAddress,
    this.createTime = const Value.absent(),
    this.updateTime = const Value.absent(),
  })  : uuid = Value(uuid),
        url = Value(url),
        netUuid = Value(netUuid),
        walletAddress = Value(walletAddress);
  static Insertable<DappConnectData> custom({
    Expression<int>? id,
    Expression<String>? uuid,
    Expression<String>? url,
    Expression<String>? iconUrl,
    Expression<String>? name,
    Expression<int>? chainType,
    Expression<String>? netUuid,
    Expression<String>? walletAddress,
    Expression<double>? createTime,
    Expression<double>? updateTime,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (uuid != null) 'uuid': uuid,
      if (url != null) 'url': url,
      if (iconUrl != null) 'icon_url': iconUrl,
      if (name != null) 'name': name,
      if (chainType != null) 'chain_type': chainType,
      if (netUuid != null) 'net_uuid': netUuid,
      if (walletAddress != null) 'wallet_address': walletAddress,
      if (createTime != null) 'create_time': createTime,
      if (updateTime != null) 'update_time': updateTime,
    });
  }

  DappConnectCompanion copyWith(
      {Value<int>? id,
      Value<String>? uuid,
      Value<String>? url,
      Value<String?>? iconUrl,
      Value<String?>? name,
      Value<int?>? chainType,
      Value<String>? netUuid,
      Value<String>? walletAddress,
      Value<double?>? createTime,
      Value<double?>? updateTime}) {
    return DappConnectCompanion(
      id: id ?? this.id,
      uuid: uuid ?? this.uuid,
      url: url ?? this.url,
      iconUrl: iconUrl ?? this.iconUrl,
      name: name ?? this.name,
      chainType: chainType ?? this.chainType,
      netUuid: netUuid ?? this.netUuid,
      walletAddress: walletAddress ?? this.walletAddress,
      createTime: createTime ?? this.createTime,
      updateTime: updateTime ?? this.updateTime,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<int>(id.value);
    }
    if (uuid.present) {
      map['uuid'] = Variable<String>(uuid.value);
    }
    if (url.present) {
      map['url'] = Variable<String>(url.value);
    }
    if (iconUrl.present) {
      map['icon_url'] = Variable<String>(iconUrl.value);
    }
    if (name.present) {
      map['name'] = Variable<String>(name.value);
    }
    if (chainType.present) {
      map['chain_type'] = Variable<int>(chainType.value);
    }
    if (netUuid.present) {
      map['net_uuid'] = Variable<String>(netUuid.value);
    }
    if (walletAddress.present) {
      map['wallet_address'] = Variable<String>(walletAddress.value);
    }
    if (createTime.present) {
      map['create_time'] = Variable<double>(createTime.value);
    }
    if (updateTime.present) {
      map['update_time'] = Variable<double>(updateTime.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('DappConnectCompanion(')
          ..write('id: $id, ')
          ..write('uuid: $uuid, ')
          ..write('url: $url, ')
          ..write('iconUrl: $iconUrl, ')
          ..write('name: $name, ')
          ..write('chainType: $chainType, ')
          ..write('netUuid: $netUuid, ')
          ..write('walletAddress: $walletAddress, ')
          ..write('createTime: $createTime, ')
          ..write('updateTime: $updateTime')
          ..write(')'))
        .toString();
  }
}

class WalletInfo extends Table with TableInfo<WalletInfo, WalletInfoData> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  WalletInfo(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  late final GeneratedColumn<int> id = GeneratedColumn<int>(
      'id', aliasedName, false,
      hasAutoIncrement: true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      $customConstraints: 'NOT NULL PRIMARY KEY AUTOINCREMENT');
  static const VerificationMeta _addressMeta =
      const VerificationMeta('address');
  late final GeneratedColumn<String> address = GeneratedColumn<String>(
      'address', aliasedName, false,
      type: DriftSqlType.string,
      requiredDuringInsert: true,
      $customConstraints: 'NOT NULL UNIQUE');
  static const VerificationMeta _nameMeta = const VerificationMeta('name');
  late final GeneratedColumn<String> name = GeneratedColumn<String>(
      'name', aliasedName, false,
      type: DriftSqlType.string,
      requiredDuringInsert: true,
      $customConstraints: 'NOT NULL');
  static const VerificationMeta _icoPictureMeta =
      const VerificationMeta('icoPicture');
  late final GeneratedColumn<String> icoPicture = GeneratedColumn<String>(
      'ico_picture', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _typeMeta = const VerificationMeta('type');
  late final GeneratedColumn<int> type = GeneratedColumn<int>(
      'type', aliasedName, true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _indexMeta = const VerificationMeta('index');
  late final GeneratedColumn<int> index = GeneratedColumn<int>(
      'index', aliasedName, true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _chainTypeMeta =
      const VerificationMeta('chainType');
  late final GeneratedColumn<int> chainType = GeneratedColumn<int>(
      'chain_type', aliasedName, true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _paymentTypeMeta =
      const VerificationMeta('paymentType');
  late final GeneratedColumn<int> paymentType = GeneratedColumn<int>(
      'payment_type', aliasedName, true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      $customConstraints: 'DEFAULT 0',
      defaultValue: const CustomExpression('0'));
  static const VerificationMeta _createTimeMeta =
      const VerificationMeta('createTime');
  late final GeneratedColumn<double> createTime = GeneratedColumn<double>(
      'create_time', aliasedName, true,
      type: DriftSqlType.double,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _keyboxIdMeta =
      const VerificationMeta('keyboxId');
  late final GeneratedColumn<String> keyboxId = GeneratedColumn<String>(
      'keybox_id', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _cardIdMeta = const VerificationMeta('cardId');
  late final GeneratedColumn<String> cardId = GeneratedColumn<String>(
      'card_id', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _publicKeyMeta =
      const VerificationMeta('publicKey');
  late final GeneratedColumn<String> publicKey = GeneratedColumn<String>(
      'public_key', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _sortIndexMeta =
      const VerificationMeta('sortIndex');
  late final GeneratedColumn<int> sortIndex = GeneratedColumn<int>(
      'sort_index', aliasedName, true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _walletTypeMeta =
      const VerificationMeta('walletType');
  late final GeneratedColumn<int> walletType = GeneratedColumn<int>(
      'wallet_type', aliasedName, true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _updateTimeMeta =
      const VerificationMeta('updateTime');
  late final GeneratedColumn<double> updateTime = GeneratedColumn<double>(
      'update_time', aliasedName, true,
      type: DriftSqlType.double,
      requiredDuringInsert: false,
      $customConstraints: '');
  @override
  List<GeneratedColumn> get $columns => [
        id,
        address,
        name,
        icoPicture,
        type,
        index,
        chainType,
        paymentType,
        createTime,
        keyboxId,
        cardId,
        publicKey,
        sortIndex,
        walletType,
        updateTime
      ];
  @override
  String get aliasedName => _alias ?? 'wallet_info';
  @override
  String get actualTableName => 'wallet_info';
  @override
  VerificationContext validateIntegrity(Insertable<WalletInfoData> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    }
    if (data.containsKey('address')) {
      context.handle(_addressMeta,
          address.isAcceptableOrUnknown(data['address']!, _addressMeta));
    } else if (isInserting) {
      context.missing(_addressMeta);
    }
    if (data.containsKey('name')) {
      context.handle(
          _nameMeta, name.isAcceptableOrUnknown(data['name']!, _nameMeta));
    } else if (isInserting) {
      context.missing(_nameMeta);
    }
    if (data.containsKey('ico_picture')) {
      context.handle(
          _icoPictureMeta,
          icoPicture.isAcceptableOrUnknown(
              data['ico_picture']!, _icoPictureMeta));
    }
    if (data.containsKey('type')) {
      context.handle(
          _typeMeta, type.isAcceptableOrUnknown(data['type']!, _typeMeta));
    }
    if (data.containsKey('index')) {
      context.handle(
          _indexMeta, index.isAcceptableOrUnknown(data['index']!, _indexMeta));
    }
    if (data.containsKey('chain_type')) {
      context.handle(_chainTypeMeta,
          chainType.isAcceptableOrUnknown(data['chain_type']!, _chainTypeMeta));
    }
    if (data.containsKey('payment_type')) {
      context.handle(
          _paymentTypeMeta,
          paymentType.isAcceptableOrUnknown(
              data['payment_type']!, _paymentTypeMeta));
    }
    if (data.containsKey('create_time')) {
      context.handle(
          _createTimeMeta,
          createTime.isAcceptableOrUnknown(
              data['create_time']!, _createTimeMeta));
    }
    if (data.containsKey('keybox_id')) {
      context.handle(_keyboxIdMeta,
          keyboxId.isAcceptableOrUnknown(data['keybox_id']!, _keyboxIdMeta));
    }
    if (data.containsKey('card_id')) {
      context.handle(_cardIdMeta,
          cardId.isAcceptableOrUnknown(data['card_id']!, _cardIdMeta));
    }
    if (data.containsKey('public_key')) {
      context.handle(_publicKeyMeta,
          publicKey.isAcceptableOrUnknown(data['public_key']!, _publicKeyMeta));
    }
    if (data.containsKey('sort_index')) {
      context.handle(_sortIndexMeta,
          sortIndex.isAcceptableOrUnknown(data['sort_index']!, _sortIndexMeta));
    }
    if (data.containsKey('wallet_type')) {
      context.handle(
          _walletTypeMeta,
          walletType.isAcceptableOrUnknown(
              data['wallet_type']!, _walletTypeMeta));
    }
    if (data.containsKey('update_time')) {
      context.handle(
          _updateTimeMeta,
          updateTime.isAcceptableOrUnknown(
              data['update_time']!, _updateTimeMeta));
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  WalletInfoData map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return WalletInfoData(
      id: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}id'])!,
      address: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}address'])!,
      name: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}name'])!,
      icoPicture: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}ico_picture']),
      type: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}type']),
      index: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}index']),
      chainType: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}chain_type']),
      paymentType: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}payment_type']),
      createTime: attachedDatabase.typeMapping
          .read(DriftSqlType.double, data['${effectivePrefix}create_time']),
      keyboxId: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}keybox_id']),
      cardId: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}card_id']),
      publicKey: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}public_key']),
      sortIndex: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}sort_index']),
      walletType: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}wallet_type']),
      updateTime: attachedDatabase.typeMapping
          .read(DriftSqlType.double, data['${effectivePrefix}update_time']),
    );
  }

  @override
  WalletInfo createAlias(String alias) {
    return WalletInfo(attachedDatabase, alias);
  }

  @override
  bool get dontWriteConstraints => true;
}

class WalletInfoData extends DataClass implements Insertable<WalletInfoData> {
  final int id;
  final String address;
  final String name;
  final String? icoPicture;
  final int? type;
  final int? index;
  final int? chainType;
  final int? paymentType;
  final double? createTime;
  final String? keyboxId;
  final String? cardId;
  final String? publicKey;
  final int? sortIndex;
  final int? walletType;
  final double? updateTime;
  const WalletInfoData(
      {required this.id,
      required this.address,
      required this.name,
      this.icoPicture,
      this.type,
      this.index,
      this.chainType,
      this.paymentType,
      this.createTime,
      this.keyboxId,
      this.cardId,
      this.publicKey,
      this.sortIndex,
      this.walletType,
      this.updateTime});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<int>(id);
    map['address'] = Variable<String>(address);
    map['name'] = Variable<String>(name);
    if (!nullToAbsent || icoPicture != null) {
      map['ico_picture'] = Variable<String>(icoPicture);
    }
    if (!nullToAbsent || type != null) {
      map['type'] = Variable<int>(type);
    }
    if (!nullToAbsent || index != null) {
      map['index'] = Variable<int>(index);
    }
    if (!nullToAbsent || chainType != null) {
      map['chain_type'] = Variable<int>(chainType);
    }
    if (!nullToAbsent || paymentType != null) {
      map['payment_type'] = Variable<int>(paymentType);
    }
    if (!nullToAbsent || createTime != null) {
      map['create_time'] = Variable<double>(createTime);
    }
    if (!nullToAbsent || keyboxId != null) {
      map['keybox_id'] = Variable<String>(keyboxId);
    }
    if (!nullToAbsent || cardId != null) {
      map['card_id'] = Variable<String>(cardId);
    }
    if (!nullToAbsent || publicKey != null) {
      map['public_key'] = Variable<String>(publicKey);
    }
    if (!nullToAbsent || sortIndex != null) {
      map['sort_index'] = Variable<int>(sortIndex);
    }
    if (!nullToAbsent || walletType != null) {
      map['wallet_type'] = Variable<int>(walletType);
    }
    if (!nullToAbsent || updateTime != null) {
      map['update_time'] = Variable<double>(updateTime);
    }
    return map;
  }

  WalletInfoCompanion toCompanion(bool nullToAbsent) {
    return WalletInfoCompanion(
      id: Value(id),
      address: Value(address),
      name: Value(name),
      icoPicture: icoPicture == null && nullToAbsent
          ? const Value.absent()
          : Value(icoPicture),
      type: type == null && nullToAbsent ? const Value.absent() : Value(type),
      index:
          index == null && nullToAbsent ? const Value.absent() : Value(index),
      chainType: chainType == null && nullToAbsent
          ? const Value.absent()
          : Value(chainType),
      paymentType: paymentType == null && nullToAbsent
          ? const Value.absent()
          : Value(paymentType),
      createTime: createTime == null && nullToAbsent
          ? const Value.absent()
          : Value(createTime),
      keyboxId: keyboxId == null && nullToAbsent
          ? const Value.absent()
          : Value(keyboxId),
      cardId:
          cardId == null && nullToAbsent ? const Value.absent() : Value(cardId),
      publicKey: publicKey == null && nullToAbsent
          ? const Value.absent()
          : Value(publicKey),
      sortIndex: sortIndex == null && nullToAbsent
          ? const Value.absent()
          : Value(sortIndex),
      walletType: walletType == null && nullToAbsent
          ? const Value.absent()
          : Value(walletType),
      updateTime: updateTime == null && nullToAbsent
          ? const Value.absent()
          : Value(updateTime),
    );
  }

  factory WalletInfoData.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return WalletInfoData(
      id: serializer.fromJson<int>(json['id']),
      address: serializer.fromJson<String>(json['address']),
      name: serializer.fromJson<String>(json['name']),
      icoPicture: serializer.fromJson<String?>(json['ico_picture']),
      type: serializer.fromJson<int?>(json['type']),
      index: serializer.fromJson<int?>(json['index']),
      chainType: serializer.fromJson<int?>(json['chain_type']),
      paymentType: serializer.fromJson<int?>(json['payment_type']),
      createTime: serializer.fromJson<double?>(json['create_time']),
      keyboxId: serializer.fromJson<String?>(json['keybox_id']),
      cardId: serializer.fromJson<String?>(json['card_id']),
      publicKey: serializer.fromJson<String?>(json['public_key']),
      sortIndex: serializer.fromJson<int?>(json['sort_index']),
      walletType: serializer.fromJson<int?>(json['wallet_type']),
      updateTime: serializer.fromJson<double?>(json['update_time']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<int>(id),
      'address': serializer.toJson<String>(address),
      'name': serializer.toJson<String>(name),
      'ico_picture': serializer.toJson<String?>(icoPicture),
      'type': serializer.toJson<int?>(type),
      'index': serializer.toJson<int?>(index),
      'chain_type': serializer.toJson<int?>(chainType),
      'payment_type': serializer.toJson<int?>(paymentType),
      'create_time': serializer.toJson<double?>(createTime),
      'keybox_id': serializer.toJson<String?>(keyboxId),
      'card_id': serializer.toJson<String?>(cardId),
      'public_key': serializer.toJson<String?>(publicKey),
      'sort_index': serializer.toJson<int?>(sortIndex),
      'wallet_type': serializer.toJson<int?>(walletType),
      'update_time': serializer.toJson<double?>(updateTime),
    };
  }

  WalletInfoData copyWith(
          {int? id,
          String? address,
          String? name,
          Value<String?> icoPicture = const Value.absent(),
          Value<int?> type = const Value.absent(),
          Value<int?> index = const Value.absent(),
          Value<int?> chainType = const Value.absent(),
          Value<int?> paymentType = const Value.absent(),
          Value<double?> createTime = const Value.absent(),
          Value<String?> keyboxId = const Value.absent(),
          Value<String?> cardId = const Value.absent(),
          Value<String?> publicKey = const Value.absent(),
          Value<int?> sortIndex = const Value.absent(),
          Value<int?> walletType = const Value.absent(),
          Value<double?> updateTime = const Value.absent()}) =>
      WalletInfoData(
        id: id ?? this.id,
        address: address ?? this.address,
        name: name ?? this.name,
        icoPicture: icoPicture.present ? icoPicture.value : this.icoPicture,
        type: type.present ? type.value : this.type,
        index: index.present ? index.value : this.index,
        chainType: chainType.present ? chainType.value : this.chainType,
        paymentType: paymentType.present ? paymentType.value : this.paymentType,
        createTime: createTime.present ? createTime.value : this.createTime,
        keyboxId: keyboxId.present ? keyboxId.value : this.keyboxId,
        cardId: cardId.present ? cardId.value : this.cardId,
        publicKey: publicKey.present ? publicKey.value : this.publicKey,
        sortIndex: sortIndex.present ? sortIndex.value : this.sortIndex,
        walletType: walletType.present ? walletType.value : this.walletType,
        updateTime: updateTime.present ? updateTime.value : this.updateTime,
      );
  @override
  String toString() {
    return (StringBuffer('WalletInfoData(')
          ..write('id: $id, ')
          ..write('address: $address, ')
          ..write('name: $name, ')
          ..write('icoPicture: $icoPicture, ')
          ..write('type: $type, ')
          ..write('index: $index, ')
          ..write('chainType: $chainType, ')
          ..write('paymentType: $paymentType, ')
          ..write('createTime: $createTime, ')
          ..write('keyboxId: $keyboxId, ')
          ..write('cardId: $cardId, ')
          ..write('publicKey: $publicKey, ')
          ..write('sortIndex: $sortIndex, ')
          ..write('walletType: $walletType, ')
          ..write('updateTime: $updateTime')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(
      id,
      address,
      name,
      icoPicture,
      type,
      index,
      chainType,
      paymentType,
      createTime,
      keyboxId,
      cardId,
      publicKey,
      sortIndex,
      walletType,
      updateTime);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is WalletInfoData &&
          other.id == this.id &&
          other.address == this.address &&
          other.name == this.name &&
          other.icoPicture == this.icoPicture &&
          other.type == this.type &&
          other.index == this.index &&
          other.chainType == this.chainType &&
          other.paymentType == this.paymentType &&
          other.createTime == this.createTime &&
          other.keyboxId == this.keyboxId &&
          other.cardId == this.cardId &&
          other.publicKey == this.publicKey &&
          other.sortIndex == this.sortIndex &&
          other.walletType == this.walletType &&
          other.updateTime == this.updateTime);
}

class WalletInfoCompanion extends UpdateCompanion<WalletInfoData> {
  final Value<int> id;
  final Value<String> address;
  final Value<String> name;
  final Value<String?> icoPicture;
  final Value<int?> type;
  final Value<int?> index;
  final Value<int?> chainType;
  final Value<int?> paymentType;
  final Value<double?> createTime;
  final Value<String?> keyboxId;
  final Value<String?> cardId;
  final Value<String?> publicKey;
  final Value<int?> sortIndex;
  final Value<int?> walletType;
  final Value<double?> updateTime;
  const WalletInfoCompanion({
    this.id = const Value.absent(),
    this.address = const Value.absent(),
    this.name = const Value.absent(),
    this.icoPicture = const Value.absent(),
    this.type = const Value.absent(),
    this.index = const Value.absent(),
    this.chainType = const Value.absent(),
    this.paymentType = const Value.absent(),
    this.createTime = const Value.absent(),
    this.keyboxId = const Value.absent(),
    this.cardId = const Value.absent(),
    this.publicKey = const Value.absent(),
    this.sortIndex = const Value.absent(),
    this.walletType = const Value.absent(),
    this.updateTime = const Value.absent(),
  });
  WalletInfoCompanion.insert({
    this.id = const Value.absent(),
    required String address,
    required String name,
    this.icoPicture = const Value.absent(),
    this.type = const Value.absent(),
    this.index = const Value.absent(),
    this.chainType = const Value.absent(),
    this.paymentType = const Value.absent(),
    this.createTime = const Value.absent(),
    this.keyboxId = const Value.absent(),
    this.cardId = const Value.absent(),
    this.publicKey = const Value.absent(),
    this.sortIndex = const Value.absent(),
    this.walletType = const Value.absent(),
    this.updateTime = const Value.absent(),
  })  : address = Value(address),
        name = Value(name);
  static Insertable<WalletInfoData> custom({
    Expression<int>? id,
    Expression<String>? address,
    Expression<String>? name,
    Expression<String>? icoPicture,
    Expression<int>? type,
    Expression<int>? index,
    Expression<int>? chainType,
    Expression<int>? paymentType,
    Expression<double>? createTime,
    Expression<String>? keyboxId,
    Expression<String>? cardId,
    Expression<String>? publicKey,
    Expression<int>? sortIndex,
    Expression<int>? walletType,
    Expression<double>? updateTime,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (address != null) 'address': address,
      if (name != null) 'name': name,
      if (icoPicture != null) 'ico_picture': icoPicture,
      if (type != null) 'type': type,
      if (index != null) 'index': index,
      if (chainType != null) 'chain_type': chainType,
      if (paymentType != null) 'payment_type': paymentType,
      if (createTime != null) 'create_time': createTime,
      if (keyboxId != null) 'keybox_id': keyboxId,
      if (cardId != null) 'card_id': cardId,
      if (publicKey != null) 'public_key': publicKey,
      if (sortIndex != null) 'sort_index': sortIndex,
      if (walletType != null) 'wallet_type': walletType,
      if (updateTime != null) 'update_time': updateTime,
    });
  }

  WalletInfoCompanion copyWith(
      {Value<int>? id,
      Value<String>? address,
      Value<String>? name,
      Value<String?>? icoPicture,
      Value<int?>? type,
      Value<int?>? index,
      Value<int?>? chainType,
      Value<int?>? paymentType,
      Value<double?>? createTime,
      Value<String?>? keyboxId,
      Value<String?>? cardId,
      Value<String?>? publicKey,
      Value<int?>? sortIndex,
      Value<int?>? walletType,
      Value<double?>? updateTime}) {
    return WalletInfoCompanion(
      id: id ?? this.id,
      address: address ?? this.address,
      name: name ?? this.name,
      icoPicture: icoPicture ?? this.icoPicture,
      type: type ?? this.type,
      index: index ?? this.index,
      chainType: chainType ?? this.chainType,
      paymentType: paymentType ?? this.paymentType,
      createTime: createTime ?? this.createTime,
      keyboxId: keyboxId ?? this.keyboxId,
      cardId: cardId ?? this.cardId,
      publicKey: publicKey ?? this.publicKey,
      sortIndex: sortIndex ?? this.sortIndex,
      walletType: walletType ?? this.walletType,
      updateTime: updateTime ?? this.updateTime,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<int>(id.value);
    }
    if (address.present) {
      map['address'] = Variable<String>(address.value);
    }
    if (name.present) {
      map['name'] = Variable<String>(name.value);
    }
    if (icoPicture.present) {
      map['ico_picture'] = Variable<String>(icoPicture.value);
    }
    if (type.present) {
      map['type'] = Variable<int>(type.value);
    }
    if (index.present) {
      map['index'] = Variable<int>(index.value);
    }
    if (chainType.present) {
      map['chain_type'] = Variable<int>(chainType.value);
    }
    if (paymentType.present) {
      map['payment_type'] = Variable<int>(paymentType.value);
    }
    if (createTime.present) {
      map['create_time'] = Variable<double>(createTime.value);
    }
    if (keyboxId.present) {
      map['keybox_id'] = Variable<String>(keyboxId.value);
    }
    if (cardId.present) {
      map['card_id'] = Variable<String>(cardId.value);
    }
    if (publicKey.present) {
      map['public_key'] = Variable<String>(publicKey.value);
    }
    if (sortIndex.present) {
      map['sort_index'] = Variable<int>(sortIndex.value);
    }
    if (walletType.present) {
      map['wallet_type'] = Variable<int>(walletType.value);
    }
    if (updateTime.present) {
      map['update_time'] = Variable<double>(updateTime.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('WalletInfoCompanion(')
          ..write('id: $id, ')
          ..write('address: $address, ')
          ..write('name: $name, ')
          ..write('icoPicture: $icoPicture, ')
          ..write('type: $type, ')
          ..write('index: $index, ')
          ..write('chainType: $chainType, ')
          ..write('paymentType: $paymentType, ')
          ..write('createTime: $createTime, ')
          ..write('keyboxId: $keyboxId, ')
          ..write('cardId: $cardId, ')
          ..write('publicKey: $publicKey, ')
          ..write('sortIndex: $sortIndex, ')
          ..write('walletType: $walletType, ')
          ..write('updateTime: $updateTime')
          ..write(')'))
        .toString();
  }
}

class NftInfo extends Table with TableInfo<NftInfo, NftInfoData> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  NftInfo(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  late final GeneratedColumn<int> id = GeneratedColumn<int>(
      'id', aliasedName, false,
      hasAutoIncrement: true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      $customConstraints: 'NOT NULL PRIMARY KEY AUTOINCREMENT');
  static const VerificationMeta _chainidMeta =
      const VerificationMeta('chainid');
  late final GeneratedColumn<int> chainid = GeneratedColumn<int>(
      'chainid', aliasedName, true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _nftAddressMeta =
      const VerificationMeta('nftAddress');
  late final GeneratedColumn<String> nftAddress = GeneratedColumn<String>(
      'nft_address', aliasedName, false,
      type: DriftSqlType.string,
      requiredDuringInsert: true,
      $customConstraints: 'NOT NULL UNIQUE');
  static const VerificationMeta _nftNameMeta =
      const VerificationMeta('nftName');
  late final GeneratedColumn<String> nftName = GeneratedColumn<String>(
      'nft_name', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _nftIdsMeta = const VerificationMeta('nftIds');
  late final GeneratedColumn<String> nftIds = GeneratedColumn<String>(
      'nft_ids', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _configNftIdsMeta =
      const VerificationMeta('configNftIds');
  late final GeneratedColumn<String> configNftIds = GeneratedColumn<String>(
      'config_nft_ids', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _nftTypeMeta =
      const VerificationMeta('nftType');
  late final GeneratedColumn<String> nftType = GeneratedColumn<String>(
      'nft_type', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _logoMeta = const VerificationMeta('logo');
  late final GeneratedColumn<String> logo = GeneratedColumn<String>(
      'logo', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _pathMeta = const VerificationMeta('path');
  late final GeneratedColumn<String> path = GeneratedColumn<String>(
      'path', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _showStatusMeta =
      const VerificationMeta('showStatus');
  late final GeneratedColumn<int> showStatus = GeneratedColumn<int>(
      'show_status', aliasedName, true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _topIndexMeta =
      const VerificationMeta('topIndex');
  late final GeneratedColumn<int> topIndex = GeneratedColumn<int>(
      'top_index', aliasedName, true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _chainTypeMeta =
      const VerificationMeta('chainType');
  late final GeneratedColumn<int> chainType = GeneratedColumn<int>(
      'chain_type', aliasedName, true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _isEditedMeta =
      const VerificationMeta('isEdited');
  late final GeneratedColumn<bool> isEdited = GeneratedColumn<bool>(
      'isEdited', aliasedName, true,
      type: DriftSqlType.bool,
      requiredDuringInsert: false,
      $customConstraints: 'DEFAULT FALSE',
      defaultValue: const CustomExpression('FALSE'));
  static const VerificationMeta _isDelMeta = const VerificationMeta('isDel');
  late final GeneratedColumn<bool> isDel = GeneratedColumn<bool>(
      'isDel', aliasedName, true,
      type: DriftSqlType.bool,
      requiredDuringInsert: false,
      $customConstraints: 'DEFAULT FALSE',
      defaultValue: const CustomExpression('FALSE'));
  static const VerificationMeta _sourceMeta = const VerificationMeta('source');
  late final GeneratedColumn<int> source = GeneratedColumn<int>(
      'source', aliasedName, true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _createTimeMeta =
      const VerificationMeta('createTime');
  late final GeneratedColumn<double> createTime = GeneratedColumn<double>(
      'create_time', aliasedName, true,
      type: DriftSqlType.double,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _updateTimeMeta =
      const VerificationMeta('updateTime');
  late final GeneratedColumn<double> updateTime = GeneratedColumn<double>(
      'update_time', aliasedName, true,
      type: DriftSqlType.double,
      requiredDuringInsert: false,
      $customConstraints: '');
  @override
  List<GeneratedColumn> get $columns => [
        id,
        chainid,
        nftAddress,
        nftName,
        nftIds,
        configNftIds,
        nftType,
        logo,
        path,
        showStatus,
        topIndex,
        chainType,
        isEdited,
        isDel,
        source,
        createTime,
        updateTime
      ];
  @override
  String get aliasedName => _alias ?? 'nft_info';
  @override
  String get actualTableName => 'nft_info';
  @override
  VerificationContext validateIntegrity(Insertable<NftInfoData> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    }
    if (data.containsKey('chainid')) {
      context.handle(_chainidMeta,
          chainid.isAcceptableOrUnknown(data['chainid']!, _chainidMeta));
    }
    if (data.containsKey('nft_address')) {
      context.handle(
          _nftAddressMeta,
          nftAddress.isAcceptableOrUnknown(
              data['nft_address']!, _nftAddressMeta));
    } else if (isInserting) {
      context.missing(_nftAddressMeta);
    }
    if (data.containsKey('nft_name')) {
      context.handle(_nftNameMeta,
          nftName.isAcceptableOrUnknown(data['nft_name']!, _nftNameMeta));
    }
    if (data.containsKey('nft_ids')) {
      context.handle(_nftIdsMeta,
          nftIds.isAcceptableOrUnknown(data['nft_ids']!, _nftIdsMeta));
    }
    if (data.containsKey('config_nft_ids')) {
      context.handle(
          _configNftIdsMeta,
          configNftIds.isAcceptableOrUnknown(
              data['config_nft_ids']!, _configNftIdsMeta));
    }
    if (data.containsKey('nft_type')) {
      context.handle(_nftTypeMeta,
          nftType.isAcceptableOrUnknown(data['nft_type']!, _nftTypeMeta));
    }
    if (data.containsKey('logo')) {
      context.handle(
          _logoMeta, logo.isAcceptableOrUnknown(data['logo']!, _logoMeta));
    }
    if (data.containsKey('path')) {
      context.handle(
          _pathMeta, path.isAcceptableOrUnknown(data['path']!, _pathMeta));
    }
    if (data.containsKey('show_status')) {
      context.handle(
          _showStatusMeta,
          showStatus.isAcceptableOrUnknown(
              data['show_status']!, _showStatusMeta));
    }
    if (data.containsKey('top_index')) {
      context.handle(_topIndexMeta,
          topIndex.isAcceptableOrUnknown(data['top_index']!, _topIndexMeta));
    }
    if (data.containsKey('chain_type')) {
      context.handle(_chainTypeMeta,
          chainType.isAcceptableOrUnknown(data['chain_type']!, _chainTypeMeta));
    }
    if (data.containsKey('isEdited')) {
      context.handle(_isEditedMeta,
          isEdited.isAcceptableOrUnknown(data['isEdited']!, _isEditedMeta));
    }
    if (data.containsKey('isDel')) {
      context.handle(
          _isDelMeta, isDel.isAcceptableOrUnknown(data['isDel']!, _isDelMeta));
    }
    if (data.containsKey('source')) {
      context.handle(_sourceMeta,
          source.isAcceptableOrUnknown(data['source']!, _sourceMeta));
    }
    if (data.containsKey('create_time')) {
      context.handle(
          _createTimeMeta,
          createTime.isAcceptableOrUnknown(
              data['create_time']!, _createTimeMeta));
    }
    if (data.containsKey('update_time')) {
      context.handle(
          _updateTimeMeta,
          updateTime.isAcceptableOrUnknown(
              data['update_time']!, _updateTimeMeta));
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  NftInfoData map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return NftInfoData(
      id: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}id'])!,
      chainid: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}chainid']),
      nftAddress: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}nft_address'])!,
      nftName: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}nft_name']),
      nftIds: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}nft_ids']),
      configNftIds: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}config_nft_ids']),
      nftType: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}nft_type']),
      logo: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}logo']),
      path: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}path']),
      showStatus: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}show_status']),
      topIndex: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}top_index']),
      chainType: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}chain_type']),
      isEdited: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}isEdited']),
      isDel: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}isDel']),
      source: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}source']),
      createTime: attachedDatabase.typeMapping
          .read(DriftSqlType.double, data['${effectivePrefix}create_time']),
      updateTime: attachedDatabase.typeMapping
          .read(DriftSqlType.double, data['${effectivePrefix}update_time']),
    );
  }

  @override
  NftInfo createAlias(String alias) {
    return NftInfo(attachedDatabase, alias);
  }

  @override
  bool get dontWriteConstraints => true;
}

class NftInfoData extends DataClass implements Insertable<NftInfoData> {
  final int id;
  final int? chainid;
  final String nftAddress;
  final String? nftName;
  final String? nftIds;
  final String? configNftIds;
  final String? nftType;
  final String? logo;
  final String? path;
  final int? showStatus;
  final int? topIndex;
  final int? chainType;
  final bool? isEdited;
  final bool? isDel;
  final int? source;
  final double? createTime;
  final double? updateTime;
  const NftInfoData(
      {required this.id,
      this.chainid,
      required this.nftAddress,
      this.nftName,
      this.nftIds,
      this.configNftIds,
      this.nftType,
      this.logo,
      this.path,
      this.showStatus,
      this.topIndex,
      this.chainType,
      this.isEdited,
      this.isDel,
      this.source,
      this.createTime,
      this.updateTime});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<int>(id);
    if (!nullToAbsent || chainid != null) {
      map['chainid'] = Variable<int>(chainid);
    }
    map['nft_address'] = Variable<String>(nftAddress);
    if (!nullToAbsent || nftName != null) {
      map['nft_name'] = Variable<String>(nftName);
    }
    if (!nullToAbsent || nftIds != null) {
      map['nft_ids'] = Variable<String>(nftIds);
    }
    if (!nullToAbsent || configNftIds != null) {
      map['config_nft_ids'] = Variable<String>(configNftIds);
    }
    if (!nullToAbsent || nftType != null) {
      map['nft_type'] = Variable<String>(nftType);
    }
    if (!nullToAbsent || logo != null) {
      map['logo'] = Variable<String>(logo);
    }
    if (!nullToAbsent || path != null) {
      map['path'] = Variable<String>(path);
    }
    if (!nullToAbsent || showStatus != null) {
      map['show_status'] = Variable<int>(showStatus);
    }
    if (!nullToAbsent || topIndex != null) {
      map['top_index'] = Variable<int>(topIndex);
    }
    if (!nullToAbsent || chainType != null) {
      map['chain_type'] = Variable<int>(chainType);
    }
    if (!nullToAbsent || isEdited != null) {
      map['isEdited'] = Variable<bool>(isEdited);
    }
    if (!nullToAbsent || isDel != null) {
      map['isDel'] = Variable<bool>(isDel);
    }
    if (!nullToAbsent || source != null) {
      map['source'] = Variable<int>(source);
    }
    if (!nullToAbsent || createTime != null) {
      map['create_time'] = Variable<double>(createTime);
    }
    if (!nullToAbsent || updateTime != null) {
      map['update_time'] = Variable<double>(updateTime);
    }
    return map;
  }

  NftInfoCompanion toCompanion(bool nullToAbsent) {
    return NftInfoCompanion(
      id: Value(id),
      chainid: chainid == null && nullToAbsent
          ? const Value.absent()
          : Value(chainid),
      nftAddress: Value(nftAddress),
      nftName: nftName == null && nullToAbsent
          ? const Value.absent()
          : Value(nftName),
      nftIds:
          nftIds == null && nullToAbsent ? const Value.absent() : Value(nftIds),
      configNftIds: configNftIds == null && nullToAbsent
          ? const Value.absent()
          : Value(configNftIds),
      nftType: nftType == null && nullToAbsent
          ? const Value.absent()
          : Value(nftType),
      logo: logo == null && nullToAbsent ? const Value.absent() : Value(logo),
      path: path == null && nullToAbsent ? const Value.absent() : Value(path),
      showStatus: showStatus == null && nullToAbsent
          ? const Value.absent()
          : Value(showStatus),
      topIndex: topIndex == null && nullToAbsent
          ? const Value.absent()
          : Value(topIndex),
      chainType: chainType == null && nullToAbsent
          ? const Value.absent()
          : Value(chainType),
      isEdited: isEdited == null && nullToAbsent
          ? const Value.absent()
          : Value(isEdited),
      isDel:
          isDel == null && nullToAbsent ? const Value.absent() : Value(isDel),
      source:
          source == null && nullToAbsent ? const Value.absent() : Value(source),
      createTime: createTime == null && nullToAbsent
          ? const Value.absent()
          : Value(createTime),
      updateTime: updateTime == null && nullToAbsent
          ? const Value.absent()
          : Value(updateTime),
    );
  }

  factory NftInfoData.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return NftInfoData(
      id: serializer.fromJson<int>(json['id']),
      chainid: serializer.fromJson<int?>(json['chainid']),
      nftAddress: serializer.fromJson<String>(json['nft_address']),
      nftName: serializer.fromJson<String?>(json['nft_name']),
      nftIds: serializer.fromJson<String?>(json['nft_ids']),
      configNftIds: serializer.fromJson<String?>(json['config_nft_ids']),
      nftType: serializer.fromJson<String?>(json['nft_type']),
      logo: serializer.fromJson<String?>(json['logo']),
      path: serializer.fromJson<String?>(json['path']),
      showStatus: serializer.fromJson<int?>(json['show_status']),
      topIndex: serializer.fromJson<int?>(json['top_index']),
      chainType: serializer.fromJson<int?>(json['chain_type']),
      isEdited: serializer.fromJson<bool?>(json['isEdited']),
      isDel: serializer.fromJson<bool?>(json['isDel']),
      source: serializer.fromJson<int?>(json['source']),
      createTime: serializer.fromJson<double?>(json['create_time']),
      updateTime: serializer.fromJson<double?>(json['update_time']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<int>(id),
      'chainid': serializer.toJson<int?>(chainid),
      'nft_address': serializer.toJson<String>(nftAddress),
      'nft_name': serializer.toJson<String?>(nftName),
      'nft_ids': serializer.toJson<String?>(nftIds),
      'config_nft_ids': serializer.toJson<String?>(configNftIds),
      'nft_type': serializer.toJson<String?>(nftType),
      'logo': serializer.toJson<String?>(logo),
      'path': serializer.toJson<String?>(path),
      'show_status': serializer.toJson<int?>(showStatus),
      'top_index': serializer.toJson<int?>(topIndex),
      'chain_type': serializer.toJson<int?>(chainType),
      'isEdited': serializer.toJson<bool?>(isEdited),
      'isDel': serializer.toJson<bool?>(isDel),
      'source': serializer.toJson<int?>(source),
      'create_time': serializer.toJson<double?>(createTime),
      'update_time': serializer.toJson<double?>(updateTime),
    };
  }

  NftInfoData copyWith(
          {int? id,
          Value<int?> chainid = const Value.absent(),
          String? nftAddress,
          Value<String?> nftName = const Value.absent(),
          Value<String?> nftIds = const Value.absent(),
          Value<String?> configNftIds = const Value.absent(),
          Value<String?> nftType = const Value.absent(),
          Value<String?> logo = const Value.absent(),
          Value<String?> path = const Value.absent(),
          Value<int?> showStatus = const Value.absent(),
          Value<int?> topIndex = const Value.absent(),
          Value<int?> chainType = const Value.absent(),
          Value<bool?> isEdited = const Value.absent(),
          Value<bool?> isDel = const Value.absent(),
          Value<int?> source = const Value.absent(),
          Value<double?> createTime = const Value.absent(),
          Value<double?> updateTime = const Value.absent()}) =>
      NftInfoData(
        id: id ?? this.id,
        chainid: chainid.present ? chainid.value : this.chainid,
        nftAddress: nftAddress ?? this.nftAddress,
        nftName: nftName.present ? nftName.value : this.nftName,
        nftIds: nftIds.present ? nftIds.value : this.nftIds,
        configNftIds:
            configNftIds.present ? configNftIds.value : this.configNftIds,
        nftType: nftType.present ? nftType.value : this.nftType,
        logo: logo.present ? logo.value : this.logo,
        path: path.present ? path.value : this.path,
        showStatus: showStatus.present ? showStatus.value : this.showStatus,
        topIndex: topIndex.present ? topIndex.value : this.topIndex,
        chainType: chainType.present ? chainType.value : this.chainType,
        isEdited: isEdited.present ? isEdited.value : this.isEdited,
        isDel: isDel.present ? isDel.value : this.isDel,
        source: source.present ? source.value : this.source,
        createTime: createTime.present ? createTime.value : this.createTime,
        updateTime: updateTime.present ? updateTime.value : this.updateTime,
      );
  @override
  String toString() {
    return (StringBuffer('NftInfoData(')
          ..write('id: $id, ')
          ..write('chainid: $chainid, ')
          ..write('nftAddress: $nftAddress, ')
          ..write('nftName: $nftName, ')
          ..write('nftIds: $nftIds, ')
          ..write('configNftIds: $configNftIds, ')
          ..write('nftType: $nftType, ')
          ..write('logo: $logo, ')
          ..write('path: $path, ')
          ..write('showStatus: $showStatus, ')
          ..write('topIndex: $topIndex, ')
          ..write('chainType: $chainType, ')
          ..write('isEdited: $isEdited, ')
          ..write('isDel: $isDel, ')
          ..write('source: $source, ')
          ..write('createTime: $createTime, ')
          ..write('updateTime: $updateTime')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(
      id,
      chainid,
      nftAddress,
      nftName,
      nftIds,
      configNftIds,
      nftType,
      logo,
      path,
      showStatus,
      topIndex,
      chainType,
      isEdited,
      isDel,
      source,
      createTime,
      updateTime);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is NftInfoData &&
          other.id == this.id &&
          other.chainid == this.chainid &&
          other.nftAddress == this.nftAddress &&
          other.nftName == this.nftName &&
          other.nftIds == this.nftIds &&
          other.configNftIds == this.configNftIds &&
          other.nftType == this.nftType &&
          other.logo == this.logo &&
          other.path == this.path &&
          other.showStatus == this.showStatus &&
          other.topIndex == this.topIndex &&
          other.chainType == this.chainType &&
          other.isEdited == this.isEdited &&
          other.isDel == this.isDel &&
          other.source == this.source &&
          other.createTime == this.createTime &&
          other.updateTime == this.updateTime);
}

class NftInfoCompanion extends UpdateCompanion<NftInfoData> {
  final Value<int> id;
  final Value<int?> chainid;
  final Value<String> nftAddress;
  final Value<String?> nftName;
  final Value<String?> nftIds;
  final Value<String?> configNftIds;
  final Value<String?> nftType;
  final Value<String?> logo;
  final Value<String?> path;
  final Value<int?> showStatus;
  final Value<int?> topIndex;
  final Value<int?> chainType;
  final Value<bool?> isEdited;
  final Value<bool?> isDel;
  final Value<int?> source;
  final Value<double?> createTime;
  final Value<double?> updateTime;
  const NftInfoCompanion({
    this.id = const Value.absent(),
    this.chainid = const Value.absent(),
    this.nftAddress = const Value.absent(),
    this.nftName = const Value.absent(),
    this.nftIds = const Value.absent(),
    this.configNftIds = const Value.absent(),
    this.nftType = const Value.absent(),
    this.logo = const Value.absent(),
    this.path = const Value.absent(),
    this.showStatus = const Value.absent(),
    this.topIndex = const Value.absent(),
    this.chainType = const Value.absent(),
    this.isEdited = const Value.absent(),
    this.isDel = const Value.absent(),
    this.source = const Value.absent(),
    this.createTime = const Value.absent(),
    this.updateTime = const Value.absent(),
  });
  NftInfoCompanion.insert({
    this.id = const Value.absent(),
    this.chainid = const Value.absent(),
    required String nftAddress,
    this.nftName = const Value.absent(),
    this.nftIds = const Value.absent(),
    this.configNftIds = const Value.absent(),
    this.nftType = const Value.absent(),
    this.logo = const Value.absent(),
    this.path = const Value.absent(),
    this.showStatus = const Value.absent(),
    this.topIndex = const Value.absent(),
    this.chainType = const Value.absent(),
    this.isEdited = const Value.absent(),
    this.isDel = const Value.absent(),
    this.source = const Value.absent(),
    this.createTime = const Value.absent(),
    this.updateTime = const Value.absent(),
  }) : nftAddress = Value(nftAddress);
  static Insertable<NftInfoData> custom({
    Expression<int>? id,
    Expression<int>? chainid,
    Expression<String>? nftAddress,
    Expression<String>? nftName,
    Expression<String>? nftIds,
    Expression<String>? configNftIds,
    Expression<String>? nftType,
    Expression<String>? logo,
    Expression<String>? path,
    Expression<int>? showStatus,
    Expression<int>? topIndex,
    Expression<int>? chainType,
    Expression<bool>? isEdited,
    Expression<bool>? isDel,
    Expression<int>? source,
    Expression<double>? createTime,
    Expression<double>? updateTime,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (chainid != null) 'chainid': chainid,
      if (nftAddress != null) 'nft_address': nftAddress,
      if (nftName != null) 'nft_name': nftName,
      if (nftIds != null) 'nft_ids': nftIds,
      if (configNftIds != null) 'config_nft_ids': configNftIds,
      if (nftType != null) 'nft_type': nftType,
      if (logo != null) 'logo': logo,
      if (path != null) 'path': path,
      if (showStatus != null) 'show_status': showStatus,
      if (topIndex != null) 'top_index': topIndex,
      if (chainType != null) 'chain_type': chainType,
      if (isEdited != null) 'isEdited': isEdited,
      if (isDel != null) 'isDel': isDel,
      if (source != null) 'source': source,
      if (createTime != null) 'create_time': createTime,
      if (updateTime != null) 'update_time': updateTime,
    });
  }

  NftInfoCompanion copyWith(
      {Value<int>? id,
      Value<int?>? chainid,
      Value<String>? nftAddress,
      Value<String?>? nftName,
      Value<String?>? nftIds,
      Value<String?>? configNftIds,
      Value<String?>? nftType,
      Value<String?>? logo,
      Value<String?>? path,
      Value<int?>? showStatus,
      Value<int?>? topIndex,
      Value<int?>? chainType,
      Value<bool?>? isEdited,
      Value<bool?>? isDel,
      Value<int?>? source,
      Value<double?>? createTime,
      Value<double?>? updateTime}) {
    return NftInfoCompanion(
      id: id ?? this.id,
      chainid: chainid ?? this.chainid,
      nftAddress: nftAddress ?? this.nftAddress,
      nftName: nftName ?? this.nftName,
      nftIds: nftIds ?? this.nftIds,
      configNftIds: configNftIds ?? this.configNftIds,
      nftType: nftType ?? this.nftType,
      logo: logo ?? this.logo,
      path: path ?? this.path,
      showStatus: showStatus ?? this.showStatus,
      topIndex: topIndex ?? this.topIndex,
      chainType: chainType ?? this.chainType,
      isEdited: isEdited ?? this.isEdited,
      isDel: isDel ?? this.isDel,
      source: source ?? this.source,
      createTime: createTime ?? this.createTime,
      updateTime: updateTime ?? this.updateTime,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<int>(id.value);
    }
    if (chainid.present) {
      map['chainid'] = Variable<int>(chainid.value);
    }
    if (nftAddress.present) {
      map['nft_address'] = Variable<String>(nftAddress.value);
    }
    if (nftName.present) {
      map['nft_name'] = Variable<String>(nftName.value);
    }
    if (nftIds.present) {
      map['nft_ids'] = Variable<String>(nftIds.value);
    }
    if (configNftIds.present) {
      map['config_nft_ids'] = Variable<String>(configNftIds.value);
    }
    if (nftType.present) {
      map['nft_type'] = Variable<String>(nftType.value);
    }
    if (logo.present) {
      map['logo'] = Variable<String>(logo.value);
    }
    if (path.present) {
      map['path'] = Variable<String>(path.value);
    }
    if (showStatus.present) {
      map['show_status'] = Variable<int>(showStatus.value);
    }
    if (topIndex.present) {
      map['top_index'] = Variable<int>(topIndex.value);
    }
    if (chainType.present) {
      map['chain_type'] = Variable<int>(chainType.value);
    }
    if (isEdited.present) {
      map['isEdited'] = Variable<bool>(isEdited.value);
    }
    if (isDel.present) {
      map['isDel'] = Variable<bool>(isDel.value);
    }
    if (source.present) {
      map['source'] = Variable<int>(source.value);
    }
    if (createTime.present) {
      map['create_time'] = Variable<double>(createTime.value);
    }
    if (updateTime.present) {
      map['update_time'] = Variable<double>(updateTime.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('NftInfoCompanion(')
          ..write('id: $id, ')
          ..write('chainid: $chainid, ')
          ..write('nftAddress: $nftAddress, ')
          ..write('nftName: $nftName, ')
          ..write('nftIds: $nftIds, ')
          ..write('configNftIds: $configNftIds, ')
          ..write('nftType: $nftType, ')
          ..write('logo: $logo, ')
          ..write('path: $path, ')
          ..write('showStatus: $showStatus, ')
          ..write('topIndex: $topIndex, ')
          ..write('chainType: $chainType, ')
          ..write('isEdited: $isEdited, ')
          ..write('isDel: $isDel, ')
          ..write('source: $source, ')
          ..write('createTime: $createTime, ')
          ..write('updateTime: $updateTime')
          ..write(')'))
        .toString();
  }
}

class AddressBook extends Table with TableInfo<AddressBook, AddressBookData> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  AddressBook(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  late final GeneratedColumn<int> id = GeneratedColumn<int>(
      'id', aliasedName, false,
      hasAutoIncrement: true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      $customConstraints: 'NOT NULL PRIMARY KEY AUTOINCREMENT');
  static const VerificationMeta _chainidMeta =
      const VerificationMeta('chainid');
  late final GeneratedColumn<int> chainid = GeneratedColumn<int>(
      'chainid', aliasedName, true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _walletAddressMeta =
      const VerificationMeta('walletAddress');
  late final GeneratedColumn<String> walletAddress = GeneratedColumn<String>(
      'wallet_address', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _nameMeta = const VerificationMeta('name');
  late final GeneratedColumn<String> name = GeneratedColumn<String>(
      'name', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _createTimeMeta =
      const VerificationMeta('createTime');
  late final GeneratedColumn<double> createTime = GeneratedColumn<double>(
      'create_time', aliasedName, true,
      type: DriftSqlType.double,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _updateTimeMeta =
      const VerificationMeta('updateTime');
  late final GeneratedColumn<double> updateTime = GeneratedColumn<double>(
      'update_time', aliasedName, true,
      type: DriftSqlType.double,
      requiredDuringInsert: false,
      $customConstraints: '');
  @override
  List<GeneratedColumn> get $columns =>
      [id, chainid, walletAddress, name, createTime, updateTime];
  @override
  String get aliasedName => _alias ?? 'address_book';
  @override
  String get actualTableName => 'address_book';
  @override
  VerificationContext validateIntegrity(Insertable<AddressBookData> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    }
    if (data.containsKey('chainid')) {
      context.handle(_chainidMeta,
          chainid.isAcceptableOrUnknown(data['chainid']!, _chainidMeta));
    }
    if (data.containsKey('wallet_address')) {
      context.handle(
          _walletAddressMeta,
          walletAddress.isAcceptableOrUnknown(
              data['wallet_address']!, _walletAddressMeta));
    }
    if (data.containsKey('name')) {
      context.handle(
          _nameMeta, name.isAcceptableOrUnknown(data['name']!, _nameMeta));
    }
    if (data.containsKey('create_time')) {
      context.handle(
          _createTimeMeta,
          createTime.isAcceptableOrUnknown(
              data['create_time']!, _createTimeMeta));
    }
    if (data.containsKey('update_time')) {
      context.handle(
          _updateTimeMeta,
          updateTime.isAcceptableOrUnknown(
              data['update_time']!, _updateTimeMeta));
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  AddressBookData map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return AddressBookData(
      id: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}id'])!,
      chainid: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}chainid']),
      walletAddress: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}wallet_address']),
      name: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}name']),
      createTime: attachedDatabase.typeMapping
          .read(DriftSqlType.double, data['${effectivePrefix}create_time']),
      updateTime: attachedDatabase.typeMapping
          .read(DriftSqlType.double, data['${effectivePrefix}update_time']),
    );
  }

  @override
  AddressBook createAlias(String alias) {
    return AddressBook(attachedDatabase, alias);
  }

  @override
  bool get dontWriteConstraints => true;
}

class AddressBookData extends DataClass implements Insertable<AddressBookData> {
  final int id;
  final int? chainid;
  final String? walletAddress;
  final String? name;
  final double? createTime;
  final double? updateTime;
  const AddressBookData(
      {required this.id,
      this.chainid,
      this.walletAddress,
      this.name,
      this.createTime,
      this.updateTime});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<int>(id);
    if (!nullToAbsent || chainid != null) {
      map['chainid'] = Variable<int>(chainid);
    }
    if (!nullToAbsent || walletAddress != null) {
      map['wallet_address'] = Variable<String>(walletAddress);
    }
    if (!nullToAbsent || name != null) {
      map['name'] = Variable<String>(name);
    }
    if (!nullToAbsent || createTime != null) {
      map['create_time'] = Variable<double>(createTime);
    }
    if (!nullToAbsent || updateTime != null) {
      map['update_time'] = Variable<double>(updateTime);
    }
    return map;
  }

  AddressBookCompanion toCompanion(bool nullToAbsent) {
    return AddressBookCompanion(
      id: Value(id),
      chainid: chainid == null && nullToAbsent
          ? const Value.absent()
          : Value(chainid),
      walletAddress: walletAddress == null && nullToAbsent
          ? const Value.absent()
          : Value(walletAddress),
      name: name == null && nullToAbsent ? const Value.absent() : Value(name),
      createTime: createTime == null && nullToAbsent
          ? const Value.absent()
          : Value(createTime),
      updateTime: updateTime == null && nullToAbsent
          ? const Value.absent()
          : Value(updateTime),
    );
  }

  factory AddressBookData.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return AddressBookData(
      id: serializer.fromJson<int>(json['id']),
      chainid: serializer.fromJson<int?>(json['chainid']),
      walletAddress: serializer.fromJson<String?>(json['wallet_address']),
      name: serializer.fromJson<String?>(json['name']),
      createTime: serializer.fromJson<double?>(json['create_time']),
      updateTime: serializer.fromJson<double?>(json['update_time']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<int>(id),
      'chainid': serializer.toJson<int?>(chainid),
      'wallet_address': serializer.toJson<String?>(walletAddress),
      'name': serializer.toJson<String?>(name),
      'create_time': serializer.toJson<double?>(createTime),
      'update_time': serializer.toJson<double?>(updateTime),
    };
  }

  AddressBookData copyWith(
          {int? id,
          Value<int?> chainid = const Value.absent(),
          Value<String?> walletAddress = const Value.absent(),
          Value<String?> name = const Value.absent(),
          Value<double?> createTime = const Value.absent(),
          Value<double?> updateTime = const Value.absent()}) =>
      AddressBookData(
        id: id ?? this.id,
        chainid: chainid.present ? chainid.value : this.chainid,
        walletAddress:
            walletAddress.present ? walletAddress.value : this.walletAddress,
        name: name.present ? name.value : this.name,
        createTime: createTime.present ? createTime.value : this.createTime,
        updateTime: updateTime.present ? updateTime.value : this.updateTime,
      );
  @override
  String toString() {
    return (StringBuffer('AddressBookData(')
          ..write('id: $id, ')
          ..write('chainid: $chainid, ')
          ..write('walletAddress: $walletAddress, ')
          ..write('name: $name, ')
          ..write('createTime: $createTime, ')
          ..write('updateTime: $updateTime')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode =>
      Object.hash(id, chainid, walletAddress, name, createTime, updateTime);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is AddressBookData &&
          other.id == this.id &&
          other.chainid == this.chainid &&
          other.walletAddress == this.walletAddress &&
          other.name == this.name &&
          other.createTime == this.createTime &&
          other.updateTime == this.updateTime);
}

class AddressBookCompanion extends UpdateCompanion<AddressBookData> {
  final Value<int> id;
  final Value<int?> chainid;
  final Value<String?> walletAddress;
  final Value<String?> name;
  final Value<double?> createTime;
  final Value<double?> updateTime;
  const AddressBookCompanion({
    this.id = const Value.absent(),
    this.chainid = const Value.absent(),
    this.walletAddress = const Value.absent(),
    this.name = const Value.absent(),
    this.createTime = const Value.absent(),
    this.updateTime = const Value.absent(),
  });
  AddressBookCompanion.insert({
    this.id = const Value.absent(),
    this.chainid = const Value.absent(),
    this.walletAddress = const Value.absent(),
    this.name = const Value.absent(),
    this.createTime = const Value.absent(),
    this.updateTime = const Value.absent(),
  });
  static Insertable<AddressBookData> custom({
    Expression<int>? id,
    Expression<int>? chainid,
    Expression<String>? walletAddress,
    Expression<String>? name,
    Expression<double>? createTime,
    Expression<double>? updateTime,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (chainid != null) 'chainid': chainid,
      if (walletAddress != null) 'wallet_address': walletAddress,
      if (name != null) 'name': name,
      if (createTime != null) 'create_time': createTime,
      if (updateTime != null) 'update_time': updateTime,
    });
  }

  AddressBookCompanion copyWith(
      {Value<int>? id,
      Value<int?>? chainid,
      Value<String?>? walletAddress,
      Value<String?>? name,
      Value<double?>? createTime,
      Value<double?>? updateTime}) {
    return AddressBookCompanion(
      id: id ?? this.id,
      chainid: chainid ?? this.chainid,
      walletAddress: walletAddress ?? this.walletAddress,
      name: name ?? this.name,
      createTime: createTime ?? this.createTime,
      updateTime: updateTime ?? this.updateTime,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<int>(id.value);
    }
    if (chainid.present) {
      map['chainid'] = Variable<int>(chainid.value);
    }
    if (walletAddress.present) {
      map['wallet_address'] = Variable<String>(walletAddress.value);
    }
    if (name.present) {
      map['name'] = Variable<String>(name.value);
    }
    if (createTime.present) {
      map['create_time'] = Variable<double>(createTime.value);
    }
    if (updateTime.present) {
      map['update_time'] = Variable<double>(updateTime.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('AddressBookCompanion(')
          ..write('id: $id, ')
          ..write('chainid: $chainid, ')
          ..write('walletAddress: $walletAddress, ')
          ..write('name: $name, ')
          ..write('createTime: $createTime, ')
          ..write('updateTime: $updateTime')
          ..write(')'))
        .toString();
  }
}

abstract class _$WalletDatabase extends GeneratedDatabase {
  _$WalletDatabase(QueryExecutor e) : super(e);
  late final TransactionRecord transactionRecord = TransactionRecord(this);
  late final NetworkInfo networkInfo = NetworkInfo(this);
  late final TokenInfo tokenInfo = TokenInfo(this);
  late final DappConnect dappConnect = DappConnect(this);
  late final WalletInfo walletInfo = WalletInfo(this);
  late final NftInfo nftInfo = NftInfo(this);
  late final AddressBook addressBook = AddressBook(this);
  Selectable<TransactionRecordData> allTransactionRecordByStatus(
      String netUuid) {
    return customSelect(
        'SELECT * FROM Transaction_record WHERE status = 0 AND net_uuid = ?1',
        variables: [
          Variable<String>(netUuid)
        ],
        readsFrom: {
          transactionRecord,
        }).asyncMap(transactionRecord.mapFromRow);
  }

  Selectable<TransactionRecordData> allTransactionRecordByMain(
      String var1, String var2, int? var3, int var4) {
    return customSelect(
        'SELECT * FROM Transaction_record WHERE status = 1 AND "from" = ?1 AND net_uuid = ?2 AND option_type = ?3 AND transaction_type = ?4 ORDER BY time DESC',
        variables: [
          Variable<String>(var1),
          Variable<String>(var2),
          Variable<int>(var3),
          Variable<int>(var4)
        ],
        readsFrom: {
          transactionRecord,
        }).asyncMap(transactionRecord.mapFromRow);
  }

  Selectable<TransactionRecordData> allTransactionRecordByToken(
      String var1, String var2, String? var3, int? var4, int var5) {
    return customSelect(
        'SELECT * FROM Transaction_record WHERE status = 1 AND "from" = ?1 AND net_uuid = ?2 AND token = ?3 AND option_type = ?4 AND transaction_type = ?5 ORDER BY time DESC',
        variables: [
          Variable<String>(var1),
          Variable<String>(var2),
          Variable<String>(var3),
          Variable<int>(var4),
          Variable<int>(var5)
        ],
        readsFrom: {
          transactionRecord,
        }).asyncMap(transactionRecord.mapFromRow);
  }

  Selectable<NetworkInfoData> allNetworkInfo() {
    return customSelect(
        'SELECT * FROM network_info WHERE(show_status IS NULL OR show_status = 1)GROUP BY chainid ORDER BY top_index IS NULL ASC, top_index ASC, create_time ASC',
        variables: [],
        readsFrom: {
          networkInfo,
        }).asyncMap(networkInfo.mapFromRow);
  }

  Selectable<NetworkInfoData> networkInfoByUuid(List<String> var1) {
    var $arrayStartIndex = 1;
    final expandedvar1 = $expandVar($arrayStartIndex, var1.length);
    $arrayStartIndex += var1.length;
    return customSelect(
        'SELECT * FROM network_info WHERE uuid IN ($expandedvar1)',
        variables: [
          for (var $ in var1) Variable<String>($)
        ],
        readsFrom: {
          networkInfo,
        }).asyncMap(networkInfo.mapFromRow);
  }

  Selectable<WalletInfoData> allWallet(int? chainType) {
    return customSelect(
        'SELECT * FROM wallet_info WHERE chain_type = ?1 ORDER BY id ASC',
        variables: [
          Variable<int>(chainType)
        ],
        readsFrom: {
          walletInfo,
        }).asyncMap(walletInfo.mapFromRow);
  }

  Selectable<WalletInfoData> getWalletByAddress(String address) {
    return customSelect('SELECT * FROM wallet_info WHERE address = ?1',
        variables: [
          Variable<String>(address)
        ],
        readsFrom: {
          walletInfo,
        }).asyncMap(walletInfo.mapFromRow);
  }

  Selectable<WalletInfoData> getWalletByType(int? type, int? chainType) {
    return customSelect(
        'SELECT * FROM wallet_info WHERE type = ?1 AND chain_type = ?2',
        variables: [
          Variable<int>(type),
          Variable<int>(chainType)
        ],
        readsFrom: {
          walletInfo,
        }).asyncMap(walletInfo.mapFromRow);
  }

  Selectable<TokenInfoData> allTokenInfoByNetuuid(String netUuid) {
    return customSelect(
        'SELECT * FROM token_info WHERE net_uuid = ?1 GROUP BY address ORDER BY top_index IS NULL ASC, top_index ASC, create_time ASC',
        variables: [
          Variable<String>(netUuid)
        ],
        readsFrom: {
          tokenInfo,
        }).asyncMap(tokenInfo.mapFromRow);
  }

  Future<int> deleteNetworkInfoByUuid(String uuid) {
    return customUpdate(
      'DELETE FROM network_info WHERE uuid = ?1',
      variables: [Variable<String>(uuid)],
      updates: {networkInfo},
      updateKind: UpdateKind.delete,
    );
  }

  Future<int> deleteTokenInfoByNetuuid(String netUuid) {
    return customUpdate(
      'DELETE FROM token_info WHERE net_uuid = ?1',
      variables: [Variable<String>(netUuid)],
      updates: {tokenInfo},
      updateKind: UpdateKind.delete,
    );
  }

  Future<int> deleteTransactionRecordByNetuuid(String netUuid) {
    return customUpdate(
      'DELETE FROM transaction_record WHERE net_uuid = ?1',
      variables: [Variable<String>(netUuid)],
      updates: {transactionRecord},
      updateKind: UpdateKind.delete,
    );
  }

  Selectable<TokenInfoData> tokenInfoByAddress(String netUuid, String address) {
    return customSelect(
        'SELECT * FROM token_info WHERE net_uuid = ?1 AND address = ?2',
        variables: [
          Variable<String>(netUuid),
          Variable<String>(address)
        ],
        readsFrom: {
          tokenInfo,
        }).asyncMap(tokenInfo.mapFromRow);
  }

  Selectable<DappConnectData> allDappConnetByNetuuid(
      String netUuid, String walletAddress) {
    return customSelect(
        'SELECT * FROM dapp_connect WHERE net_uuid = ?1 AND wallet_address = ?2',
        variables: [
          Variable<String>(netUuid),
          Variable<String>(walletAddress)
        ],
        readsFrom: {
          dappConnect,
        }).asyncMap(dappConnect.mapFromRow);
  }

  Future<int> deleteDappConnetByUrl(
      String url, String netUuid, String walletAddress) {
    return customUpdate(
      'DELETE FROM dapp_connect WHERE url = ?1 AND net_uuid = ?2 AND wallet_address = ?3',
      variables: [
        Variable<String>(url),
        Variable<String>(netUuid),
        Variable<String>(walletAddress)
      ],
      updates: {dappConnect},
      updateKind: UpdateKind.delete,
    );
  }

  Selectable<DappConnectData> oneDappConnetByUrl(
      String url, String netUuid, String walletAddress) {
    return customSelect(
        'SELECT * FROM dapp_connect WHERE url = ?1 AND net_uuid = ?2 AND wallet_address = ?3',
        variables: [
          Variable<String>(url),
          Variable<String>(netUuid),
          Variable<String>(walletAddress)
        ],
        readsFrom: {
          dappConnect,
        }).asyncMap(dappConnect.mapFromRow);
  }

  Selectable<int> dbConnect() {
    return customSelect('SELECT count(*) AS _c0 FROM sqlite_master',
        variables: [],
        readsFrom: {}).map((QueryRow row) => row.read<int>('_c0'));
  }

  Future<int> deleteTokenByAddress(String address) {
    return customUpdate(
      'DELETE FROM token_info WHERE address = ?1',
      variables: [Variable<String>(address)],
      updates: {tokenInfo},
      updateKind: UpdateKind.delete,
    );
  }

  Future<int> updateTransactionRecordNorm() {
    return customUpdate(
      'UPDATE Transaction_record SET chain_type = 0 WHERE chain_type IS NULL',
      variables: [],
      updates: {transactionRecord},
      updateKind: UpdateKind.update,
    );
  }

  Future<int> updateWalletInfoNorm() {
    return customUpdate(
      'UPDATE wallet_info SET chain_type = 0 WHERE chain_type IS NULL',
      variables: [],
      updates: {walletInfo},
      updateKind: UpdateKind.update,
    );
  }

  Future<int> updateNetworkInfoNorm() {
    return customUpdate(
      'UPDATE network_info SET chain_type = 0 WHERE chain_type IS NULL',
      variables: [],
      updates: {networkInfo},
      updateKind: UpdateKind.update,
    );
  }

  Selectable<TokenInfoData> allTokenInfoIsDel() {
    return customSelect('SELECT * FROM token_info WHERE isDel = TRUE',
        variables: [],
        readsFrom: {
          tokenInfo,
        }).asyncMap(tokenInfo.mapFromRow);
  }

  Selectable<TokenInfoData> allTokenInfo() {
    return customSelect('SELECT * FROM token_info', variables: [], readsFrom: {
      tokenInfo,
    }).asyncMap(tokenInfo.mapFromRow);
  }

  Selectable<WalletInfoData> getWalletByTypeAndIndex(
      int? var1, int? var2, int? var3, String? var4) {
    return customSelect(
        'SELECT * FROM wallet_info WHERE type = ?1 AND chain_type = ?2 AND "index" = ?3 AND card_id = ?4',
        variables: [
          Variable<int>(var1),
          Variable<int>(var2),
          Variable<int>(var3),
          Variable<String>(var4)
        ],
        readsFrom: {
          walletInfo,
        }).asyncMap(walletInfo.mapFromRow);
  }

  Selectable<TokenInfoData> tokenRepeatInfo() {
    return customSelect(
        'SELECT * FROM token_info WHERE address IN (SELECT address FROM token_info GROUP BY address HAVING count(address) > 1)',
        variables: [],
        readsFrom: {
          tokenInfo,
        }).asyncMap(tokenInfo.mapFromRow);
  }

  Selectable<TokenInfoData> getTokenInfoByAddress(String address) {
    return customSelect('SELECT * FROM token_info WHERE address = ?1',
        variables: [
          Variable<String>(address)
        ],
        readsFrom: {
          tokenInfo,
        }).asyncMap(tokenInfo.mapFromRow);
  }

  @override
  Iterable<TableInfo<Table, Object?>> get allTables =>
      allSchemaEntities.whereType<TableInfo<Table, Object?>>();
  @override
  List<DatabaseSchemaEntity> get allSchemaEntities => [
        transactionRecord,
        networkInfo,
        tokenInfo,
        dappConnect,
        walletInfo,
        nftInfo,
        addressBook
      ];
}
