# include: package:flutter_lints/flutter.yaml

analyzer:
  exclude:
    # - flutter_browser/**
    - flutter_wallet_trx/**
    - http/**
    - pro_image_editor/**
#   language:
#     strict-casts: true
#     strict-inference: true
#     strict-raw-types: true
# linter:
#   rules:
#     prefer_const_constructors: true
#     prefer_final_locals: true
#     require_trailing_commas: true
#     always_declare_return_types: true
#     use_super_parameters: true
#     avoid_unnecessary_containers: true
#     use_key_in_widget_constructors: true
#     unawaited_futures: true
#     avoid_catching_errors: true

#     no_logic_in_create_state: true
#     use_build_context_synchronously: true
#     avoid_returning_null_for_void: true
#     # Add, remove, or comment out whatever fits your vibe.