{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "app",
            "cwd": "app",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "app (profile mode)",
            "cwd": "app",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "app (release mode)",
            "cwd": "app",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        },
        {
            "name": "flutter_browser",
            "cwd": "flutter_browser",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "flutter_browser (profile mode)",
            "cwd": "flutter_browser",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "flutter_browser (release mode)",
            "cwd": "flutter_browser",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        },
        {
            "name": "flutter_wallet_eth",
            "cwd": "flutter_wallet_eth",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "flutter_wallet_eth (profile mode)",
            "cwd": "flutter_wallet_eth",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "flutter_wallet_eth (release mode)",
            "cwd": "flutter_wallet_eth",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        },
        {
            "name": "flutter_wallet_trx",
            "cwd": "flutter_wallet_trx",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "flutter_wallet_trx (profile mode)",
            "cwd": "flutter_wallet_trx",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "flutter_wallet_trx (release mode)",
            "cwd": "flutter_wallet_trx",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        },
        {
            "name": "http",
            "cwd": "http",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "chatio_plugin",
            "cwd": "app/chatio_plugin",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "chatio_plugin (profile mode)",
            "cwd": "app/chatio_plugin",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "chatio_plugin (release mode)",
            "cwd": "app/chatio_plugin",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        },
        {
            "name": "flutter_ios_voip_kit",
            "cwd": "app/flutter_ios_voip_kit",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "flutter_ios_voip_kit (profile mode)",
            "cwd": "app/flutter_ios_voip_kit",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "flutter_ios_voip_kit (release mode)",
            "cwd": "app/flutter_ios_voip_kit",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        },
        {
            "name": "image_crop",
            "cwd": "app/image_crop",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "image_crop (profile mode)",
            "cwd": "app/image_crop",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "image_crop (release mode)",
            "cwd": "app/image_crop",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        },
        {
            "name": "security_keyboard_plugin",
            "cwd": "app/security_keyboard_plugin",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "security_keyboard_plugin (profile mode)",
            "cwd": "app/security_keyboard_plugin",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "security_keyboard_plugin (release mode)",
            "cwd": "app/security_keyboard_plugin",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        },
        {
            "name": "example",
            "cwd": "app/security_keyboard_plugin/example",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "example (profile mode)",
            "cwd": "app/security_keyboard_plugin/example",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "example (release mode)",
            "cwd": "app/security_keyboard_plugin/example",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        },
        {
            "name": "dart_tron",
            "cwd": "flutter_wallet_trx/plugin/dart_tron",
            "request": "launch",
            "type": "dart"
        }
    ]
}