# Gas Price Selection Feature Implementation

## 概述
成功为 `eth_transfer_page` 的 miner fee 部分添加了点击选择 gas price 的功能，类似于 `pay_pwd.dart` 中的实现。

## 修改的文件

### 1. flutter_wallet_eth/lib/app/modules/wallet/ethereum/eth_transfer_controller.dart

#### 新增方法：

**setGasPrice(String value)**
- 功能：设置Gas价格倍数
- 参数：value - Gas价格倍数的字符串表示
- 逻辑：
  - 解析输入的倍数值
  - 更新 `_gasModel` 的 `multiple` 属性
  - 重新计算 `rxGas.value`

**getCurrentWalletModel()**
- 功能：获取当前钱包模型用于Gas设置
- 返回：包含当前钱包信息和gas模型的 `WalletModel` 实例
- 用途：为Gas设置对话框提供必要的钱包信息

### 2. flutter_wallet_eth/lib/app/modules/wallet/ethereum/eth_transfer_page.dart

#### 修改的方法：

**_buildMinerFee()**
- 原功能：仅显示矿工费信息
- 新功能：
  - 保持原有的显示功能
  - 添加点击手势检测
  - 点击时打开Gas价格选择对话框
  - 显示右箭头图标表示可点击

#### 新增方法：

**_showGasPriceDialog()**
- 功能：显示Gas价格选择对话框
- 选项：
  - 标准 (1x)
  - 快速 (3x)  
  - 自定义 (用户输入倍数)

**_showCustomGasPriceDialog()**
- 功能：显示自定义Gas价格输入对话框
- 特性：
  - 数字键盘输入
  - 输入验证
  - 只接受正整数

## 功能特点

1. **用户友好的界面**：
   - 矿工费区域现在可点击
   - 显示右箭头图标提示用户可以点击
   - 保持原有的费用显示格式

2. **灵活的Gas价格选择**：
   - 预设选项：标准(1x)和快速(3x)
   - 自定义选项：用户可以输入任意正整数倍数

3. **实时更新**：
   - 选择新的Gas价格后，界面会立即更新显示新的费用
   - 使用 Obx 响应式更新确保UI同步

4. **错误处理**：
   - 输入验证确保只接受有效的数字
   - 异常捕获防止应用崩溃

## 使用方法

1. 在转账页面，用户可以看到矿工费部分
2. 点击矿工费区域
3. 选择预设的Gas价格倍数或选择自定义
4. 如果选择自定义，输入期望的倍数
5. 确认后，页面会更新显示新的矿工费

## 技术实现细节

- 使用 `GestureDetector` 包装矿工费显示区域
- 通过 `showDialog` 显示选择对话框
- 使用 `Navigator.of(context).pop(result)` 返回选择结果
- 调用 controller 的 `setGasPrice` 方法更新Gas设置
- 通过 `rxGas.value` 的响应式更新自动刷新UI

## 兼容性

- 保持与原有代码的完全兼容
- 不影响现有的转账流程
- 可以与其他Gas相关功能协同工作
