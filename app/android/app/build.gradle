plugins {
    id "com.android.application"
    id "kotlin-android"
    id "dev.flutter.flutter-gradle-plugin"
    id "com.google.gms.google-services"
}


def isHwMetatel = true//海外metatel
def isGooglPlay = false

def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    flutterVersionCode = '1'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '1.0'
}

android {
    compileSdkVersion 34

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = '1.8'
    }

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }
    splits {
        abi {
            enable true
            reset()
            include 'arm64-v8a'
            universalApk true
        }
    }
    defaultConfig {
        applicationId "com.chat.linksay"
        minSdkVersion 26
        targetSdkVersion 34
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName
        ndk {
            abiFilters /*'armeabi-v7a',*/'arm64-v8a'
        }
        manifestPlaceholders = [
                VIVO_APPKEY : "622c86374fcd3b6ba87080e1875b76d0",
                VIVO_APPID  : "105619714",
                //
                HONOUR_APPID: "11111111",

                URL_SCHEME: "metatel",
                HOST: applicationId+".open",
        ]

    }
    lintOptions {
        // 如打包出现Failed to transform libs.jar to match attributes
        checkReleaseBuilds false
    }

    sourceSets {
        main {
            if (isGooglPlay) {
                manifest.srcFile 'src/main/googleplay/AndroidManifest.xml'
            } else {
                manifest.srcFile 'src/main/AndroidManifest.xml'
            }
            res.srcDirs = ['src/main/hw_res']


        }
    }

    signingConfigs {

        debug {
            if(isGooglPlay&&isHwMetatel){
                keyAlias 'links'
                keyPassword 'linksay2024!'
                storeFile file('home/jenkins/link_s.jks')
                storePassword 'linksay2024!'
            }else if(isHwMetatel){
                keyAlias 'links'
                keyPassword 'linksay2024!'
                storeFile file('home/jenkins/link_s.jks')
                storePassword 'linksay2024!'
            }


        }

        release {
            if(isGooglPlay&&isHwMetatel){
                keyAlias 'links'
                keyPassword 'linksay2024!'
                storeFile file('home/jenkins/link_s.jks')
                storePassword 'linksay2024!'
            } else if(isHwMetatel){
                keyAlias 'links'
                keyPassword 'linksay2024!'
                storeFile file('home/jenkins/link_s.jks')
                storePassword 'linksay2024!'
            }



        }
    }
    buildTypes{
        //输出文件名称
        android.applicationVariants.all { variant ->
//            variant.outputs.all {
//                    // 输出apk名称为OY-AndroidMobile-2.2.232.214-201908201843-debug
//               outputFileName = isIoi ? "Ioi" :  "Metatel" + "-" + defaultConfig.versionName + "-" + new Date().format("yyMMddHHmm") + "-" + buildType.name + ".apk";
//            }
        }
        release {
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.release
        }

        debug {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.release
        }

    }
    packagingOptions {
        exclude 'lib/arm64-v8a/libgojni.so'
        exclude 'lib/x86/libgojni.so'
        exclude 'lib/x86_64/libgojni.so'
    }
}

flutter {
    source '../..'
}

dependencies {
//    configurations.all {
//        resolutionStrategy {
//            force 'androidx.webkit:webkit:1.4.0'
//        }
//    }
    implementation 'androidx.activity:activity-ktx:1.8.0'
    implementation 'androidx.fragment:fragment-ktx:1.5.4'
    implementation 'com.google.firebase:firebase-analytics-ktx'
    implementation 'com.google.firebase:firebase-messaging-ktx'
    implementation platform('com.google.firebase:firebase-bom:31.0.2')
    implementation 'com.google.firebase:firebase-installations-ktx:17.1.0'
    implementation 'io.github.happylishang:antifake:1.7.0'

}
tasks.withType(JavaCompile) {
    options.encoding = "UTF-8"
}