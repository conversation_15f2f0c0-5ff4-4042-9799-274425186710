package org.metatel.im

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.media.AudioManager
import android.os.Bundle
import android.provider.Settings
import android.util.AttributeSet
import android.view.View
import android.view.WindowManager
import androidx.annotation.NonNull
import io.flutter.embedding.android.FlutterFragmentActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel
import io.flutter.plugins.GeneratedPluginRegistrant
import java.io.File
import android.net.Uri
import android.util.Log
import com.example.flutter_metatel.Utils
import com.snail.antifake.deviceid.AndroidDeviceIMEIUtil
import com.snail.antifake.jni.EmulatorDetectUtil
import android.provider.Settings.Global


class MainActivity: FlutterFragmentActivity()   {
    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)
        val methodChannel = MethodChannel(flutterEngine.dartExecutor.binaryMessenger, "org/metatel/flutter_plugin")
        methodChannel.setMethodCallHandler { methodCall: MethodCall, result: MethodChannel.Result ->
            onMethodCall(methodCall,result)
        }
    }
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
    }
    
    fun onMethodCall(call: MethodCall, result: MethodChannel.Result) {
        android.util.Log.d("onMethodCall","call.method ="+call.method)
        when (call.method) {
            "brightness" -> {
                result.success(getBrightness())
            }
            "setBrightness" -> {
                val brightness = call.argument<Double>("brightness")!!
                val layoutParams: WindowManager.LayoutParams = window.attributes
                layoutParams.screenBrightness = brightness.toFloat()
                window.attributes = layoutParams
                result.success(null)
            }
            "isKeptOn" -> {
                val flags: Int = window.attributes.flags
                result.success(flags and WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON != 0)
            }
            "keepOn" -> {
                val on = call.argument<Boolean>("on")
                if (on!!) {
                    println("Keeping screen on ")
                    window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
                } else {
                    println("Not keeping screen on")
                    window.clearFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
                }
                result.success(null)
            }
            "Speakerphone"->{
                val enable = call.argument<Boolean>("enable")!!
                val isCall = call.argument<Boolean>("isCall")!!
                val audioManager:AudioManager = getSystemService(AUDIO_SERVICE) as AudioManager
                println(
                    "Speakerphone 1111 start isCall:$isCall audioManager.mode:${audioManager.mode} speaker:${audioManager.isSpeakerphoneOn} enable:" +
                            "$enable"
                )
                if (enable) {
                    if (isCall){
                        audioManager.mode =AudioManager.MODE_IN_COMMUNICATION
                    }else{
                    audioManager.mode = AudioManager.MODE_NORMAL
                    }
                } else {
                    audioManager.mode = AudioManager.MODE_IN_COMMUNICATION
                }
                audioManager.isSpeakerphoneOn = enable
                println("Speakerphone 1111 end isCall:$isCall audioManager.mode:${audioManager.mode} speaker:${audioManager.isSpeakerphoneOn}")
                result.success(null)
            }
            "setmode"->{
                val value = call.argument<Int>("value")!!
                println("setmode start value:$value")
                val audioManager:AudioManager = getSystemService(AUDIO_SERVICE) as AudioManager
                audioManager.mode=value
                println("setmode end audioManager.mode:${audioManager.mode}")
                result.success(null)
            }
            "isSpeakerphoneOn"->{
                val audioManager:AudioManager = getSystemService(AUDIO_SERVICE) as AudioManager
                val  isSpeak=audioManager.isSpeakerphoneOn
                result.success(isSpeak)

            }
            "runOnEmulator"->{
                val runOnEmulator = AndroidDeviceIMEIUtil.isRunOnEmulator(this) //特征值判断
                val runOnEmulatorAll = EmulatorDetectUtil.isEmulatorFromAll(this) //激进判断
                Log.d("runOnEmulator","runOnEmulator: $runOnEmulator");
                Log.d("runOnEmulator","runOnEmulatorAll: $runOnEmulatorAll");
                result.success(runOnEmulator)

            }
            "toSystemHome"->{
                val intent = Intent() // 创建Intent对象
                intent.action = Intent.ACTION_MAIN // 设置Intent动作
                intent.addCategory(Intent.CATEGORY_HOME) // 设置Intent种类
                intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK //标记
                startActivity(intent)
                result.success(null)
            }
            "requestAudioFocus"->{
                val value = call.argument<Boolean>("isRequest")!!
                requestAudioFocus(value)
                result.success(null)
            }
            "restartApp"->{
                android.os.Process.killProcess(android.os.Process.myPid())
            }
            "getAndroidId"->{
                result.success(getAndroidId())
            }
            "updateFile"->{
                val path = call.argument<String>("path")!!
                broadcastFileUpdate(path)
                result.success(null)
            }
            "isRoot"->{
                result.success(isRoot())
            }
            "isOpenedAdb"->{
                result.success(isOpenedAdb())
            }
            "isOpenedMore"->{
                result.success(isOpenedMore())
            }
            "disableLockScreen"->{
                val on = call.argument<Boolean>("on")
                disableLockScreen(on!!)
                result.success(null)
            }
            "isAirplaneModeOn"->{
                val isAirplaneMode = Global.getInt(contentResolver, Global.AIRPLANE_MODE_ON, 0) != 0
                result.success(isAirplaneMode)
            }
            else -> result.notImplemented()
        }
    }

    private fun disableLockScreen(request: Boolean) {
        if (request == true) {
            window.addFlags(WindowManager.LayoutParams.FLAG_DISMISS_KEYGUARD or WindowManager.LayoutParams.FLAG_SHOW_WHEN_LOCKED)
        } else {
            window.clearFlags(WindowManager.LayoutParams.FLAG_DISMISS_KEYGUARD or WindowManager.LayoutParams.FLAG_SHOW_WHEN_LOCKED)
        }
    }

    private fun broadcastFileUpdate(path: String) {
        sendBroadcast(Intent(Intent.ACTION_MEDIA_SCANNER_SCAN_FILE, Uri.fromFile(File(path))))
        println("updated!"+path)
    }

    private fun getBrightness(): Float {
        var result: Float = window.attributes.screenBrightness
        if (result < 0) { // the application is using the system brightness
            try {
                result = Settings.System.getInt(contentResolver, Settings.System.SCREEN_BRIGHTNESS) / 255.toFloat()
            } catch (e: Settings.SettingNotFoundException) {
                result = 1.0f
                e.printStackTrace()
            }
        }
        return result
    }
    @SuppressLint("HardwareIds")
    private fun getAndroidId(): String? {
        return Settings.Secure.getString(contentResolver, Settings.Secure.ANDROID_ID)
    }
    /**获取焦点 */
    private var audioFocused = false
    fun requestAudioFocus(request: Boolean) {
        val audioManager:AudioManager = getSystemService(AUDIO_SERVICE) as AudioManager
        if (request) {
            if (!audioFocused) {
                val res = audioManager.requestAudioFocus(null, AudioManager.STREAM_VOICE_CALL, AudioManager.AUDIOFOCUS_GAIN_TRANSIENT)
                //Logger.d("Audio focus requested: " + (res == AudioManager.AUDIOFOCUS_REQUEST_GRANTED ? "Granted" : "Denied"));
                if (res == AudioManager.AUDIOFOCUS_REQUEST_GRANTED) {
                    audioFocused = true
                }
            }
        } else {
            if (audioFocused) {
                audioManager.abandonAudioFocus(null)
                audioFocused = false
            }
        }
    }
    fun isOpenedAdb():Boolean {
        val enableAdb: Boolean =
            Settings.Secure.getInt(getContentResolver(), Settings.Secure.ADB_ENABLED, 0) > 0
        return  enableAdb;
    }
    fun isRoot(): Boolean {
        var res = false
        try {
            res = !(!File("/system/bin/su").exists() &&
                    !File("/system/xbin/su").exists())
        } catch (e: Exception) {
        }
        return res
    }

    fun isOpenedMore():Boolean {
        val enableAdb: Boolean =
            Utils().haveDualAppEx(this)
        return  enableAdb;
    }
}
