package com.example.flutter_metatel

import android.content.Context
import android.os.Build
import java.io.File
import java.io.FileOutputStream
import java.lang.Exception
import java.nio.file.Files
import java.nio.file.Paths

class Utils {
    // 判断是否具有ROOT权限
    fun isRoot(): <PERSON><PERSON>an {
        var res = false
        try {
            res = !(!File("/system/bin/su").exists() &&
                    !File("/system/xbin/su").exists())
        } catch (e: Exception) {
        }
        return res
    }
    //用于收集可以实现多开的app包名，已收集到最新市面上的大部分多开app
    private val packagename = mutableListOf(
        "com.bly.dkplat",  //多开分身本身的包名
        "com.by.chaos",  //chaos引擎
        "com.lbe.parallel",  //平行空间
        "com.excelliance.dualaid",  //双开助手
        "com.lody.virtual",  //VirtualXposed，VirtualApp
        "com.qihoo.magic",  //360分身大师
        "com.dual.dualgenius",  //DualGenius/双开精灵
        "com.excean.dualaid",  //DualGenius/双开精灵
        "com.bly.dkplat",  //小X分身
        "cn.chuci.and.wkfenshen",  //无尽分身
        "com.jiubang.commerce.gomultiple", //GO Multiple/Go双开
        "com.felix.clonedelf", //分身精灵
        "com.xunrui.duokai_box", //微分身管家
        "info.red.virtual", //悟空分身
        "com.boly.wxmultopen", //多开
        "com.mjbrother.mutil", //麻吉分身
    )

    //通过读取文件包的方式进行比对
    fun haveDualAppEx(context: Context): Boolean {
        try {
            var haveMySelf = false
            val pm = context.packageManager
            val pkgs = pm.getInstalledPackages(0)
            var have = false
            val page = context.packageName;
            for (info in pkgs) {
                have = packagename.contains(info.packageName)
                if (have) {
                    return true
                }
                if (page != null && info.packageName == page) {
                    haveMySelf = true
                }
            }
            return !haveMySelf
        } catch (ignore: java.lang.Exception) {
        }
        return true
    }

    fun isDualAppEx(context: Context): Boolean {
        try {
            val simpleName = "wtf_jack"
            val testPath = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                context.dataDir.path + File.separator + simpleName
            } else {
                return false;
            }
            val fos = FileOutputStream(testPath)
            val fileDescriptor = fos.fd
            val fid_descriptor = fileDescriptor.javaClass.getDeclaredField("descriptor")
            fid_descriptor.isAccessible = true
            // 获取到 fd
            val fd = fid_descriptor[fileDescriptor] as Int
            // fd 反查真实路径
            val fdPath = String.format("/proc/self/fd/%d", fd)
            var realPath: String? = null
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                realPath = Files.readSymbolicLink(Paths.get(fdPath)).toString()
            }
            if (realPath!!.substring(realPath.lastIndexOf(File.separator)) != File.separator + simpleName) {

                // 文件名被修改
                return true
            }

            // 尝试访问真实路径的父目录
            val fatherDirPath = realPath.replace(simpleName, "..")
            val fatherDir = File(fatherDirPath)
            if (fatherDir.canRead()) {

                // 父目录可访问
                return true
            }
        } catch (e: Exception) {
            e.printStackTrace()
            return true
        }
        return false
    }
}