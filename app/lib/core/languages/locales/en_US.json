{"session_all": "All", "session_private": "Private", "session_channel": "Channel", "session_dao": "DAO", "session_private_group_chat": "Private Group Chat", "update_tips": "The update is ready. Please turn on the airplane mode and click OK to update.", "rpc_is_illegeal": "RPC is illegal, it needs to start with http:// or https://", "rpc_not_empty": "RPC cannot be empty", "confirm_select": "Confirm Selection", "optiona_rpc_nodes": "Optional RPC Nodes", "delete_rpc_hint": "Are you sure you want to delete this RPC?", "manage_nodes": "Manage Nodes", "add_custom": "Add Custom", "rpc_nodes": "RPC Nodes", "add_rpc": "Add RPC", "edit_rpc": "Edit RPC", "select_rpc": "Select RPC", "set_as_default": "Set as default", "gas_price": "Gas Price (Gwei)", "gas_limit": "Gas Limit", "fee": "Fee", "set_as_default_dailog_info": "After enabling Fixed Settings, the next time you enter this page, it will continue using the Gas Price you set last time. \n \nPlease note, do you want to continue?", "customizable": "customizable", "gas_fee_detail_info": "≈Gas(@gaslimit)* Gas Price(@gasprice Gwei)", "recommendation": "Recommendation", "recommendation_value": "15 seconds", "speedy_time": "3 seconds", "select_gas_fee": "Select Gas Fee", "speedy": "<PERSON>y", "custom": "custom", "no_relevant_network_was_found_locally_please_add_the_network_first": "No relevant network was found locally, please add the network first", "transaction_date": "Transaction Date", "view_token_information": "View Token Information", "copy_wallet_address": "Copy Wallet Address", "exit_meeting": "Exit  meeting", "the_meeting_info": "[Meeting]", "retry": "retry", "sensing_timeout": "Sensing timeout", "please_click_to_re_your_card_wallet": "Please click \"Retry\" to re-sense your card wallet", "scan_successfully_and_confirm_with_fingerprint_or_pin_code": "Scan successfully and confirm with fingerprint or PIN code", "please_bring_your_card_wallet_closer_for_scanning": "Please bring your card wallet closer for scanning", "ready_to_scan": "Ready to scan", "note_pin_error": "Note: After 6 verification errors, the card wallet Pin code verification will be locked", "your_card_wallet_has_failed_left": "Your card wallet has failed verification ", "your_card_wallet_has_failed_midl": " times, and there are ", "your_card_wallet_has_failed_right": " verification opportunities left", "your_card_wallet_pin_verification_has_been_locked": "Your card wallet Pin verification has been locked", "the_wallet_address_does_not_match_please_select_the_correct_wallet": "The wallet address does not match, please select the correct wallet", "fast_transfer_step2": "Step 2: Select an operation", "fast_transfer_step1": "Step 1: Choose a wallet", "fast_transfer": "Linksay Card Fast Transfer", "card_reset": "Card reset", "fingerprint_payment": "Fingerprint payment", "pin_code_payment": "Pin code payment", "wallet_export_failed": "Wallet export failed", "the_wallet_is_not_initialized_please_initialize_it_first": "The wallet is not initialized, please initialize it first", "wallet_init_title": "Wallet initialization", "wallet_init_ins": "Please complete the wallet initialization according to the instructions", "wallet_init_ins_set": "Please set a PIN code or two sets of fingerprint passwords for your card wallet according to the instructions. Bring the card wallet close to your phone to start the initialization.", "scan_Success": "<PERSON><PERSON> Successfully", "start_init": "Start initialization", "fingerprint_set": "Fingerprint settings", "write_mnemonics": "Write mnemonics", "card_wallet_scan": "Please bring the card wallet close for scanning", "confirm_pin": "Confirm PIN", "pin_not_match": "The two entered PIN codes do not match", "set_up_success": "Successful setting", "enter_6_pin": "Please enter a 6-digit PIN code", "pin_set_err": "PIN code setting error. code:@arg", "setup_two_fingerprint": "Please set up two sets of fingerprints according to the instructions", "satrt_fingerprint_reg": "Start registering the fingerprint", "enter_fingerprint": "Registering fingerprint @arg", "card_wallet_enter_fingerprint": "Please do not move the card wallet, and proceed to register the @arg fingerprint.", "start": "Start", "entry_success": "Entry successful", "fingerprint_num": "Fingerprint @arg", "enter_fingerprint_err": "Error typing fingerprint. code:@arg", "init_success": "Initialization successful", "init_succ_import_sys": "The card wallet has been successfully initialized and can be used normally after being imported into the system", "finish": "Finish", "import_wallet": "Import wallet", "mnemonic_verif_succ": "Mnemonic verification successful", "scan_card_write_mnemonic": "Please bring the card wallet close for scanning to write the mnemonic phrase", "start_write": "Start writing", "set_pin_next": "The card wallet has already set a PIN code, and will jump to the next step of setting", "set_fingerprint_next": "The current fingerprint has been entered and will jump to the next step of settings", "set_mnemonic_next": "The card wallet has been written with the mnemonic, and will jump to the next step of setting", "mnemonic_write_fail": "Mnemonic phrase writing failed", "nfc_connected": "NFC is connected", "nfc_disconnected": "NFC is disconnected", "pin_verif_err": "PIN verification error", "use_pin": "Use PIN", "use_fingerprint": "Use fingerprint", "sign_auth": "Signature Authorization", "sign_fingerprint_pin": "Please bring the card wallet close for scanning, and confirm with your fingerprint or PIN code for signature verification", "pin_code": "Pin Code", "pin_verif": "PIN verification", "card_manage": "Card Management", "connected": "Connected", "not_connected": "Not connected", "fig_no_leave": "The finger has not been removed", "fig_no_touch": "The finger is not touching the sensor", "w_pin_set": "PIN Setting", "pin_verification_failed": "PIN verification failed", "wallet_management": "Wallet Management", "click_to_purchase": "Click to purchase", "make_your_assets_safer": "Make your assets safer", "import_private_key_into_wallet": "Import private key into wallet", "importing_private_keys": "Importing private keys", "card_wallet": "Card Wallet", "create_single_wallet": "Create a single wallet", "create_multiple_wallet": "Creating multiple wallets", "wallet_name": "Wallet Name", "wallet_path": "Wallet Path", "batch_creation": "Batch creation", "the_path_already_exists_please_reselect_the_path": "The path already exists. Please reselect the path.", "number_of_wallets": "Number of wallets", "wallet_created_successfully": "@num Wallet created successfully", "hardware_wallets": "Hardware wallets", "fingerprint_verification_failed": "Fingerprint verification failed", "start_importing_wallet": "Start importing wallet", "card_wallet_not_imported": "Card wallet not imported", "hardware_wallet_import": "Hardware wallet import (ID:@id)", "fingerprint_not_recognized": "Fingerprint not recognized", "please_confirm_the_fingerprint_signature_on_the_hardware_card_wallet": "Please approach the card wallet and press the card wallet fingerprint to scan", "please_use_fingerprint_to_pay": "Please use fingerprint to pay", "exceeded_the_aximum_number_of_sends": "Exceeded the maximum number of sends", "what_is_the_red_packet_for": "What's the Red Packet for?", "transaction_to_create_miner_failed": "Transaction to create miner failed", "start_the_blockchain_node": "Start the Blockchain Node", "create_absenteeism": "Create Absenteeism", "create_a_bls_account": "Create a bls account", "loading_the_keystore": "Loading the keystore", "no_corresponding_wallet_address_found_please_import_it_to_the_wallet_first": "No corresponding wallet address found, please import it to the wallet first", "un_known": "Unkown", "node_wallet": "@name", "there_is_an_error_in_obtaining_tokens_please_reselect_your_wallet": "There is an error in obtaining tokens. Please reselect your wallet", "insufficient_available_balance_please_switch_to_another_wallet": "Insufficient available balance, please switch to another wallet", "the_pledge_amount_cannot_be_less_than_the_minimum_pledge_amount": "The pledge amount cannot be less than the minimum pledge amount", "the_pledge_amount_cannot_be_illegal": "The pledge amount cannot be illegal", "the_pledge_amount_cannot_be_empty": "The pledge amount cannot be empty", "current_status": "Current Status: ", "miner_name_info": " At least 5 characters, including English letters or numbers", "miner_name": "Miner Name", "min_pledge_quantity": "Minimum staking amount:@num", "pledge_quantity": "Pledge Quantity", "balance_wallet": "Balance:@num", "miner": "Miner", "chain_ID": "Chain ID", "node_rpc": "Node RPC", "block_node": "Block Node", "regular_node": "Regular Node", "un_know": "UnKnow", "en_US": "English", "zh_CN": "简体中文", "zh_TW": "繁体中文", "ja_JP": "日本語", "vi_VN": "Tiếng <PERSON>", "pt_BR": "Português", "ko_KR": "한국어", "generating_account": "Generating account...", "node_update_success": "The initial node update is successful, please restart the application", "initial_node_update": "Initial Node Update", "the_main_wallet_and_service_wallet_cannot_be_the_same_wallet": "The main wallet and the service wallet cannot be the same wallet", "update_pin_code": "Modify PIN code", "new_pin_code": "New Pin Code", "old_pin_code": "Original factory pin code", "not_activated": "Not enabled", "activated": "Enabled", "activate": "start", "activate_keybox": "Start keybox", "edit_pin": "Modify PIN code", "node_pledge_application_info": "Node pledge verification message", "node_staking_details": "Node Staking Details", "desc_wallet_can_not_empty": "Verification description cannot be empty", "keyBoxType_1": "Storage", "keyBoxType_2": "Meeting", "keyBoxType_3": "Communication", "keyBoxType_4": "Channel", "keyBoxType_5": "Forward", "keyBoxType_6": "Block", "keyBoxType_7": "Prefix", "keyBoxType_8": "Live Streaming", "keyBoxType_9": "Wallet", "keyBoxType_10": "Video", "keyBoxType_11": "Cross-chain", "keyBoxType_12": "Trade", "please_select_the_server_type": "Please select node type", "main_wallet_can_not_empty": "The master wallet cannot be empty", "server_wallet_can_not_empty": "Node wallet cannot be empty", "the_node_can_not_empty": "Node domain cannot be empty", "the_sevice_wallet_error": "Node wallet exception", "the_pin_codes_can_not_empty": "Pin code cannot be empty", "service_wallet": "Node wallet", "the_pin_codes_entered_twice_are_inconsistent": "The PIN codes entered twice are inconsistent", "please_enter_a_new_pin_code_again": "Please enter the Pin code again", "please_enter_a_pin_code": "Please enter Pin code", "the_pin_code_cannot_be_less_than_6_digits": "The PIN code cannot be less than 6 digits", "please_enter_a_new_pin_code": "Please enter new Pin code", "set_up_the_pledge_pin_code": "Set Staking Pin Code", "node_initialization": "Node initialization", "submit_your_application": "Submit verification", "service_type": "Node type", "server_domain_name": "Node domain name", "service_info": "Node information", "master_wallet": "Master wallet", "wallet_info": "Wallet information", "node_pledge_application": "Node pledge verification", "k_server": "Keybox status:", "Application_time": "Verification time:", "Application_description": "Verification description:", "Application_description_1": "Verify description", "type": "Type:", "domain_name": "Domain:", "node_status": "Node status", "authentication_failed": "Authentication failed", "verifying": "Verifying", "passed": "Verification successful", "all": "Transfer All", "node_list": "node list", "pledge_application": "Pledge verification", "node_staking": "Node Staking", "node_staking_list": "Node pledge list", "message_dialog_un_translate": "Cancel translation", "translate_info": "Translation service provided by Google", "message_dialog_translate": "translation", "please_set_avatar": "Please set avatar", "switching_node": "Node switching is in progress. Please wait!", "switch_node_precautions": "As you begin the process of switching nodes, make sure you are aware of the following considerations:\nNode switching is an irreversible operation. If the node switching is successful, the individual chat and private group chat data under the original node will be deleted. Please make a data backup!", "precautions": "Things to note", "confirm_switch": "Confirm Switch", "notice_info": "After the node is switched, the single chat and private group chat data under the original node will be deleted. Please back up the data.", "notice": "Note", "available_node": "Available nodes", "current_node": "Current node", "node_switching": "Node switching", "refresh": "Refresh", "chat_mining_switch_02": "Authentication switch", "click_close_to_stop_participating_in_chat_mining_rewards": "Click Close and you will not be able to participate in chat mining rewards", "verification_successful": "Verification successful", "verification_failed": "Authentication failed", "human_machine_verification": "Human-machine verification", "chat_mining_switch": "Chat mining switch", "please_wait_for_service_update": "Service update, please wait!", "service_connection_failed_please_restart_the_application": "Service connection failed, please restart the application", "http": "http", "socks5": "socks5", "protocol_type": "protocol type", "qr_code_info": "QR code content", "recognize_QR_code": "Recognize QR code", "handling_fee_02": "Handling fee:@numU", "operation_restricted": "Operation restricted", "the_server_returned_failure_please_try_again": "The server returned failure, please try again", "order_repurchase_tips": "You have an unapproved order", "repurchase_tid_submit_info": "Non-DID users cannot apply for redemption", "repurchase_min_number": "The minimum amount for redemption must exceed @num1 and be less than @num2", "submit_success": "Submission successful", "repurchase_record": "Exchange record", "repurchase_0": "refuse", "repurchase_1": "Applying", "repurchase_2": "Completed", "repurchase_time": "date", "repurchase_quantity": "Quantity(K)", "amount_received": "Amount(U)", "state": "condition", "exceeds_the_maximum_redeemable_amount": "Exceeds the maximum redeemable amount", "expected_note": "①Exchange U will be credited to the Linksay main wallet account by default, you need to switch to the Binance Chain network to query\n② Redemption fee is 5%, minimum 1U/transaction\n③Estimated arrival time for repurchase is T+2", "expected_to_be_available": "expected to be available", "please_enter_the_repurchase_amount": "Please enter the redemption quantity", "repurchase_unit_price": "Exchange unit price: 1U = @p K token", "apply_for_redemption": "Apply for exchange", "can_be_repurchased": "Redeemable", "repurchase": "exchange", "storage_optimization": "Storage Optimization", "the_nodes_are_inconsistent_please_switch_the_PCnode": "The nodes are inconsistent, please switch the PC node.", "the_hongbao_title": "Best wishes", "the_hongbao_info": "[Red Packet]", "the_red_envelope": "Red Packet", "the_recommender_has_been_bound_please_try_again_the_next_day": "The referrer has been bound, please try again the next day", "new_message_count": "@number unread messages", "temp_did_identification": "DID Temporary Identification Identification", "receive_benefits_3": "temporary interest", "setting_local_proxy": "local front point", "searbar_hint_search_tid": "Search DID", "temp_tbs_info": "Successfully claimed the benefits, valid for @number1 days, quantity @number2", "undownloaded": "Not downloaded", "undownloaded_source_files_cannot_be_shared": "Undownloaded source files cannot be shared", "validity_period": "Validity period", "hosting_number": "AI hosting quantity", "equity_success_info_02": "Note: The balance of tokens can be checked in \"Wallet\";\nThe DID can be queried on the \"My\" page.", "did_identification": "DID identification", "create_meeting": "Create meeting", "computing_power_hosting": "DID computing power hosting", "ai_hosting_state_1": "In custody", "ai_hosting_state_2": "Not hosted", "ai_hosting_state": "AI hosting status", "ai_hosting": "Ai Hosting", "did_list": "DID list", "did_number": "DID quantity", "did_times": "@numbers1peak", "computing_power_did": "DID computing power", "equity_success_info": "The blockchain contract is being executed, please check later.", "get_user_address": "Communication account address:@address", "get_wallet_address": "Wallet address:@address", "receive_benefits_1": "Equity 1", "receive_benefits_2": "Equity 2", "receive_benefits": "Receive benefits", "app_more_run": "Multi-open applications are not allowed. Please uninstall multi-open related software, which will affect the mining function.", "trade_status": "Transaction status:", "trade_id": "Transaction number:", "create_time": "Creation time:", "logger_type": "Log type:", "balance_at_time_of_change": "Balance at the time of change:", "third_party_transaction_status": "Three-party transaction status:", "trade_type": "Transaction type:", "currency": "Transaction currency:", "pre_authorization_currency": "Pre-authorized currency:", "pre_authorization_amount": "Pre-authorization amount:", "transaction_description": "Transaction description:", "trade_detail": "Transaction details", "more02": "More>>", "consumption": "consumption", "transfer_in": "transfer in", "import": "import", "free_virtual_memory": "Not enough storage space to use this feature", "import_mnemonic": "Import mnemonics", "image_saved_failed": "Image saving failed", "personal_information_collection_list": "Personal information collection list", "third_party_information_and_data_sharing": "Third-party information and data sharing", "bill": "bill", "removed": "Removed", "remove": "Remove", "added_blacklist": "@name was added to the blacklist", "add_blacklist": "Add to blacklist", "channel_share_generated_txt": "Go to other apps and share it with your friends. If your friends copy the link and enter Linksay, the join group chat page will automatically pop up.", "un_block": "Remove blacklist", "send_a_transfer": "Initiate a transfer", "receive_a_transfer": "received a transfer", "transfer_Notification": "Transfer notification", "top_cancel_success": "Cancellation successful", "top_cancel_failed": "Cancellation failed", "failed_top": "<PERSON><PERSON> failed", "top_msg": "Pinned message", "whether_to_display_this_message_at_the_top_of_the_session": "Do you want to keep this message at the top of the conversation?", "visible_to_all_group_members": "Visible to all group members", "top_success": "Pinned successfully", "approve_number": "License quantity", "channel_number_count": "The maximum number of people has been reached", "amount": "quantity", "currency_exchange": "Currency exchange", "buy": "Buy", "sell": "sell", "total_price": "total price", "unit_price": "unit price", "please_enter_user_name": "Please enter username", "pwd": "password", "user_name": "Username", "node_income": "Node income", "recommender_msg_body": "Hello, I’m @nickName, and I’ve added you as a referrer!", "withdrawal_520_3t": "Withdraw K coins once", "withdrawal_amount_is_at_least": "The withdrawal amount must be at least @amount", "computational_bonus": "Computing power income", "divide_into_detail": "Revenue details", "power_sharing": "Computing power income", "the_computing_power_of_this_period_is_divided_into": "Computing power income this period", "direct_subordinate": "Direct user", "indirect_subordinate": "Non-direct users", "divide_directly_into": "direct income", "indirect_share": "indirect income", "bonus_type": "Benefit type", "current_bonus": "Income for this period", "genesis_node": "Creation node", "city_node": "City ​​node", "session_del_toast": "After deletion, the message history of the chat will be cleared.", "english": "English", "chinese": "Chinese", "follow_the_system": "Auto", "multi_language": "Multi-Language", "backup_data_generated": "Backup data has been generated", "backup_hint_1": "1. Backup data only saves local contact account data.", "backup_hint_2": "2. After the backup data is generated, it can be saved to the album with one click as an image.", "backup_hint_3": "3. When restoring data, directly select the image for import to restore.", "backup_hint_4": "4. When sending from an external tool, send in file or original image mode. Other methods may cause backup file corruption and cannot be restored.", "backup_data": "Backup Data", "import_data": "Import Data", "use_password": "Use password", "operation_failed_please_contact_admin": "Operation failed, please contact the administrator", "operation_failed_please_try_again": "Operation failed, please try again", "insufficient_balance": "Insufficient balance", "mining_withdraw_same_hint": "The quantities withdrawn in the same revenue cycle cannot be consistent, please re-enter and try again.", "mining_withdraw_more_three_hint": "Only three withdrawal applications can be initiated in the same cycle, and the application limit has been reached.", "connection_failed": "Connection failed", "max_gas": "Maximum gas fee", "min_gas": "Minimum gas fee", "execute_the_contract": "Execute contract", "browser_download": "Browser Download", "introduction_yet": "No introduction yet~", "withdrawal_address": "Withdrawal address", "please_enter_the_withdrawal_address": "Please enter the withdrawal address", "search_channel": "Search channels", "mining_withdraw_apply_submitted": "Retrieval request has been submitted", "mining_withdraw_apply_submitted_hint": "Please log in to DAPP to refresh and check the token extraction progress.", "popular": "Popular", "channel": "Channel", "computing_power_contribution": "Computing power contribution", "chat_contribution": "<PERSON><PERSON> Contribution", "sign_in_reward": "Today’s sign-in reward", "collect_money": "Collection", "sign_in_failed": "Sign in failed", "mining_withdraw_no_recommender_hint": "Please fill in your referrer to activate the computing power reward before proceeding with the withdrawal operation.", "sign_in_today": "Check in today", "points": "Contribution points", "has_bean_withdrawn": "<PERSON><PERSON><PERSON> has been applied for", "currently_available": "Currently available", "please_enter_the_withdrawal_amount": "Please enter the withdrawal amount", "withdraw_hint": "Before withdrawing the token, please complete the binding of the inviter to obtain the withdrawal permission.", "mining_withdraw_amount": "quantity", "sign_in_successfully_contribution_value": "Sign in successfully, contribution value", "sign_in_for": "Continuous check-ins", "consecutive_days": "sky", "extra_reward_100_contribution_points": ", additional rewards", "i_see": "I see", "sign_in_mining": "Sign in", "has_sign_in_mining": "Signed in", "eth_evm_Address": "ETH/EVM Address", "tron_Address": "TRON Address", "virtual_open_failed_toast": "The virtual card business is in limited trial, so stay tuned!", "send_token": "Transfer", "wallet_address": "wallet address", "address_sharing": "Address sharing", "trans_the_amount_not_be_less_than": "The amount must not be less than 0.01 USD", "card_opening_amount_must_not_be_less_than_10": "The card opening amount must not be less than $10", "can_create_card_amount": "Number of cards that can be opened", "trans_fee_hint": "A transaction fee of 3% of the transferred amount will be charged for each transfer.", "create_card_amount_input_hint": "The card opening amount must not be less than $10, and a $5 card opening fee will be charged.", "vote": "vote:", "has_read_3t_card_user_terms": "I have read and agreed to the \"Linksay User Terms\"", "whether_create_virtual_card_account": "Whether to open a virtual card account", "create_virtual_card_account_hint": "Linksay is a prepaid card type virtual card with a recharge function. It uses USDT (TRC-20) for recharge. It has a similar use to a physical card, but does not have a physical card. It covers Amazon, eBay, Netflix, App store content subscription, business travel and other scenarios. Online payment meets flexible card usage needs.", "banned": "Banned", "reviewing": "Under review", "amount_is_required": "Amount required", "can_not_create_card_max": "The maximum number of card openings has been reached and cannot continue to open cards.", "status": "condition", "card_ing": "Opening a card", "card_protect": "Under protection", "card_using": "In use", "card_freeze": "Freezing", "card_del": "delete", "card_sys_freeze": "system freeze", "pledge_2": "Staking 2.0:", "no_yet": "None", "open_an_account": "Open an account", "open_an_account_failed": "Activation failed!", "recharge_amount_must_be_entered": "Recharge amount must be entered", "you_must_select_an_available_card_segment": "Available card segments must be selected", "first_name_must_be_entered": "Name required", "last_name_must_be_entered": "Last name required", "state_must_be_selected": "State must be selected", "city_must_be_selected": "City must be selected", "country_must_be_selected": "Country must be selected", "post_code_must_be_entered": "Postcode required", "address_less_one": "Enter at least one in address bar 1 and address bar 2", "TRC10_transfer": "TRC10 transfer", "call_contract": "Call contract:", "unlock_2": "Unlock 2:", "the_amount": "Amount:", "get_voting_rights": "Obtain voting rights:", "reduced_voting_rights": "Reduce voting rights:", "ticket": "ticket", "handling_fee": "Handling fee:", "required_resources": "Resources needed:", "energy": "energy", "bandwidth": "Bandwidth", "also_clear_pinned_messages": "Also clear pinned messages", "transfer_hint": "Transfer the amount from the basic account to the card for consumption.", "unfreeze_hint": "The card must be unfrozen before use. The system will allow consumption within 1 hour after unfreezing. After the expiration date, the card will be frozen again to automatically protect the security of the card.", "transaction_hash": "Transaction Hash", "recharge_record": "Recharge record", "pending": "Pending", "transfer": "transfer", "unfreeze": "<PERSON>haw", "card_segment": "stuck section", "card_segment_hint": "stuck segment", "recharge_amount": "Recharge amount", "recharge_amount_hint": "Please enter the recharge amount", "randomly_generated": "randomly generated", "state_county_province_or_region": "State, county, province or region", "postal_code": "Zip code", "city": "City", "address_line1": "Address bar 1", "address_line2": "Address bar 2", "country": "nation", "billing_address": "billing address", "name_on_card": "Name on card", "please_enter_your_name": "Please enter your name", "add_card_information": "Add card information", "transfer_out_et_hint": "Please enter the transfer amount", "transfer_out_hint": "Transfer the available amount from the card to the basic account", "transfer_out": "transfer out", "delete_card": "Delete card", "delete_card_hint": "This card will no longer be usable after deletion. If there is still a balance in the card, it will not be retrieved after deletion. Do you confirm deleting this card?", "quota": "Quota: @quota", "time": "time", "virtual_card": "virtual card", "transaction_record": "transaction history", "base_account": "Basic account", "account_name": "Account name", "freeze": "Freeze", "normal": "normal", "card_list": "card list", "recharge": "top up", "contact_limit": "Restrictions on Adding Friends", "please_open_permissions": "Please turn on permissions", "it_is_forbidden_to_run_in_the_emulator_the_program_is_about_to_exit": "It is forbidden to run in the simulator, the program will exit soon! ! !", "turn_on": "to open", "allow_notifications": "Allow message notifications", "mining_data_help_title_1": "total revenue", "mining_data_help_title_2": "Current computing power contribution", "mining_data_help_title_3": "New incentives", "mining_data_help_title_4": "Mission guidance rewards", "mining_data_help_title_5": "<PERSON><PERSON> Contribution", "the_data_shows": "Data description", "mining_data_help_1": "The cumulative number of tokens the user has obtained in Linksay.", "mining_data_help_2": "The total computing power contribution of users in the current revenue cycle, the data is as of the previous calendar day, and is not real-time data.", "mining_data_help_3": "The cumulative number of tokens obtained by users participating in the promotion and sharing of APP, and the cumulative new attraction incentives = cumulative promotion rewards + cumulative activity rewards of recommended users.", "mining_data_help_4": "The user completes the guidance task and obtains the corresponding number of tokens.", "mining_data_help_5": "The computing power contribution data generated by the user's current chat can obtain the corresponding token reward according to the contract rules.", "total_activity_rewards": "Indirect task rewards", "total_lower_level_rewards": "Direct task rewards", "undone": "Not completed", "completed": "Completed", "contribution_value_details": "Contribution value details", "select_mini_amount": "Select period", "mini_amount": "Issue @amount", "detail_3t_list": "K value details", "my_3t_value": "My K value", "obtained_today": "Obtained today", "detail_3t": "K coin details", "app_name": "<PERSON><PERSON>", "registButton": "I want to register", "qrcode": "QR code", "my_qrcode": "My QR Code", "scaner_hint": "Scan the QR code below to get the address ID", "qrcode_edit": "Edit QR code content", "choose_number": "Please select an ID:", "vitualnumber_regist": "Virtual account registration", "phone_regist": "Mobile phone number registration", "hint_phone": "Mobile phone number (only supports mainland China numbers)", "hint_verifycode": "Please enter the verification code", "get_verifycode": "Get verification code", "second": "Second", "regist_submit": "Submit registration", "bad_network": "The network is currently unavailable", "metatel_file_assistant": "My computer", "metatel_server_number": "System service number", "metatel_system_warning": "System notification", "module_call_busy": "The current user is busy, please call again later!", "channel_name_of_application_notification": "App notifications", "channel_description_of_application_notification": "App notification description", "channel_name_of_message_notification": "Application messages", "channel_description_of_message_notification": "Message notification description", "foreground_service_started": "Service is in the foreground", "application_in_progress": "<PERSON><PERSON> is running", "module_activity_web_url_failure_toast_text": "Wrong web address", "module_activity_theme_details_tv_apply_text": "Select", "module_activity_theme_set_success_text": "Setup successful", "module_activity_edit_qrcode_lv_item_tv_areacode_text": "area code", "module_activity_edit_qrcode_lv_item_tv_horizontal_line_text": "-", "module_activity_edit_qrcode_lv_item_tv_metatelnum_text": "ID:", "module_activity_edit_qrcode_lv_item_tv_title_text": "Edit QR code content", "module_activity_edit_qrcode_lv_item_tv_name_hint_text": "Remarks (required)", "module_activity_edit_qrcode_lv_item_tv_organization_hint_text": "unit", "module_activity_edit_qrcode_lv_item_tv_save_text": "save", "module_activity_group_qrcode_tv_lockinvite_hint_text": "The group owner has enabled group chat invitation restrictions\nGroup members will not be able to invite friends to the group", "hwpush_ability_value": "successRateAnalytics", "module_activity_boot_set_home_title_text": "mine", "module_activity_boot_set_sigle_permiss_title_text": "Single permission", "module_activity_boot_set_high_power_title_text": "Turn off high battery consumption reminder", "module_activity_boot_set_boot_manager_text": "Start management", "module_activity_boot_set_stay_connect_text": "Keep Wi-Fi connected", "module_activity_boot_set_process_manager_text": "Process management", "module_activity_boot_set_limit_system_text": "Allow modification of system settings", "module_activity_boot_set_ignore_battery_text": "Ignore battery optimization", "module_activity_boot_set_notify_manager_text": "Notification management", "module_activity_boot_set_open_notify_title_text": "Turn on lock screen notifications", "module_activity_boot_set_confirm_text": "Finish", "module_activity_boot_set_next_step_text": "Start setting up", "module_activity_boot_set_ignore_battery_toast_text": "Battery optimization settings have been ignored", "module_activity_boot_set_notify_has_set_toast_text": "Already set", "module_activity_system_setting_button_text": "Go to settings", "module_fragment_contactor_title_friend_request_text": "friend request", "module_fragment_contactor_title_my_grou_text": "my group chat", "module_fragment_contactor_title_company_list_text": "Corporate address book", "module_fragment_contactor_title_my_friend_text": "my friends", "module_activity_chat_has_expired_text": "The file has expired", "module_activity_chat_toast_none_wps_text": "WPS is not installed, please download it in the browser before using it.", "audio_meeting_create_load": "Creating an encrypted voice conference", "audio_meeting_accept_load": "Listening to an encrypted voice conference", "audio_meeting_again_in_load": "Joining an encrypted voice conference", "module_fragment_dial_call_failure_toast_text": "call failed", "load_one": "Loading...", "searbar_hint_search": "Search", "module_activity_group_chat_tv_text_no_data": "No related group chat", "module_activity_search_tv_text_please_input": "Please enter what you want to search for", "module_activity_encrypt_file_tv_title_text": "Encrypt files", "module_activity_encrypt_file_tv_no_such_file_text": "There are no files in this category", "module_activity_audioplayer_tv_title_text": "audio name", "module_activity_audioplayer_tv_audio_name_text": "audio name", "audio_size": "10.00MB", "text_number": "%1$s", "the_other_party_is_not_online": "The other party is not online", "called_unanswered_please_dial_later": "The called did not answer, please call again later.", "connection_failure_please_try_again_later": "The current network is unstable and the call has ended", "please_check_network_availability": "Please check if the network is available", "delete_group_chart": "Dismiss group chat", "disband_the_group": "Dismiss group chat", "disband_the_group_desc": "Are you sure to disband this group?", "this_group_has_been_disbanded_by_you": "This group has been dismissed by you", "the_group_has_been_disbanded_by_the_group_leader": "This group has been disbanded by the group owner", "you_have_been_removed_from_group_chat": "You have been removed from the group chat by the group owner", "can_not_download_file": "The file cannot be downloaded", "send_error": "Sending failed", "is_calling": "Calling", "is_connection": "Connecting", "temporarily_unable_to_connect": "Unable to connect at the moment", "you_are_already_in_a_call_you_cannot_dial_again": "You are already on a call and cannot dial again", "please_wait_later": "Please wait!", "audio_player_file_size": "File size:", "backup_exit_tip": "Exit reminder", "backup_if_cancel_backup_will_del": "The data is currently being backed up. The data backup will be canceled after returning and the backed up data will be deleted. Do you confirm the cancellation?", "backup_contact_is_backup_waiting": "Contacts are being backed up, please wait...", "backup_contact_backup_success": "Contact backup successful", "backup_confirm": "Confirm", "backup_contact_backup_fail": "Contact backup failed", "backup_contact_backup_cancel": "Contact backup canceled", "backup_file_has_send_please_confirm_success_on_pc": "The backup file has been sent, please confirm the backup is successful on the PC client", "backup_contact_backup_fail_connect_break": "Contact backup failed, connection disconnected", "backup_contact_backup": "Contact backup", "backup_fcontact_backup_to_obox": "Contact backup to O<PERSON><PERSON>", "backup_begin_backup": "Start backup", "backup_backup_fail": "Backup failed", "call_other_side_refuse_your_request": "The other party rejected your request", "call_other_side_invite_video_chat": "The other party invites you to video chat", "call_unfamiliar_number": "Unknown number", "call_other_side_maybe_offline": "The other party may not be online", "call_record_permission_refuse_and_call_has_end": "Recording permission has been denied and the call has automatically hung up.", "call_speaker": "hands-free", "call_to_video": "Switch to video call", "call_silence": "mute", "call_memo": "Remarks", "call_camera": "camera", "call_to_voice": "Switch to voice", "call_end_call": "Hang up", "call_switch_camera": "Switch camera", "camera_please_agree_record_permission_then_reuse_record_function": "Please agree to the recording permission before using the video recording function again.", "camera_please_agree_related_permission_then_operate": "Please agree to the relevant permissions before proceeding.", "chat_service_number": "System notification", "chat_system_notify": "System notification", "chat_file_trans_assist": "My computer", "chat_voice_is_connect_can_not_use_this_func": "While on a call, this feature cannot be used", "chat_downing": "Downloading...", "chat_decoding_file": "Decrypting files...", "chat_storage_permission_refuse_then_cannot_operate_file": "Storage permission has been denied, file related operations will not work properly.", "chat_camera_permission_refused_then_cannot_use_camera": "Camera permission has been denied, the shooting function will not work properly", "chat_del_confirm": "Delete confirmation", "chat_if_confirm_del_this_message": "Are you sure to delete this message?", "if_delete_choice_msg": "Delete selected messages?", "chat_file_has_del": "File has been deleted", "chat_click_look_word": "Click to view the message", "chat_second": "Second", "chat_file_has_expire": "The file has expired", "chat_downloaded": "Downloaded", "chat_send_success": "<PERSON><PERSON> successfully", "chat_click_download": "Click to download", "chat_downing_2": "Downloading", "chat_download_again": "Download again", "chat_sending": "Sending", "chat_send_fail": "Sending failed", "chat_you_has_removed_form_group_chat": "You have been removed from the group chat", "chat_add_friend_first_may_other_side_del_you": "Please add a friend first. The other party may have deleted you by mistake.", "chat_pic_has_del": "Picture has been deleted", "chat_send_word_not_allow_exceed": "The number of words sent cannot exceed", "chat_word": "Character", "chat_record_time_too_short": "Recording time is too short", "chat_the_file_your_send_not_exit": "The file to be sent does not exist", "chat_file_size_not_exceed_50_m": "File size cannot exceed", "chat_pic_size_not_exceed_20_m": "Image size cannot exceed 20M", "chat_video_had_del": "Video has been deleted", "chat_downloading": "Downloading...", "chat_voice_chat_not_use_this_fun": "While on a call, this feature cannot be used", "chat_downloading_repeat_again_later": "Downloading, please try again later", "chat_file_decode_fail": "File decryption failed", "chat_has_copy_to_shear_plate": "Copied to clipboard", "chat_has_forward": "Forwarded", "chat_info_chat_setting": "Chat settings", "chat_info_look_for_more_group_member": "View more group members >", "chat_info_group_chat_name": "Group Name", "chat_info_group_chat_describe": "Group Description", "chat_info_group_chat_announcement": "Group Announcement", "chat_info_group_announcement": "Group Announcement", "chat_info_not_name": "Unnamed", "chat_info_not_describe": "No description added", "chat_info_group_qr_code": "Group QR code", "chat_info_my_group_nickname": "My Group Nickname", "chat_info_not_setting": "not set", "chat_info_stick_chat": "pinned chat", "chat_info_message_no_disturbing": "Do Not Disturb", "chat_info_clear_chat_record": "Clear chat history", "chat_info_group_privilege": "Group owner privileges", "chat_info_del_and_exit": "Delete and exit", "chat_info_touch_phone_and_red_phone_not_support_create_group": "Red machines do not support group creation", "chat_info_cleat_confirm": "clear confirmation", "chat_info_if_confirm_clear_chat_record_with_this_contact": "Are you sure you want to clear the chat history with this contact?", "chat_info_group_name_not_allowed_empty_or_blank": "The group name cannot be empty or spaces.", "chat_info_nickname_not_allowed_empty_or_blank": "Nickname cannot be empty or spaces", "chat_info_group_chat_invite_limit": "Group chat invitation restrictions", "chat_info_group_owner_has_open_group_chat_limit_you_are_not_allowed_join_group": "The group owner has enabled group chat invitation restrictions.\nYou will not be able to invite friends to the group", "chat_info_group_chat_setting": "Group Chat Details", "chat_info_loading_group_info_and_waiting": "Loading group information, please wait.", "chat_info_chat_name": "Group chat name", "chat_info_group_name_has_lock": "Group name is locked", "chat_info_group_owner_locked_group_name_other_not_modify": "The group owner has locked the group name and other members do not have permission to modify it.", "chat_info_group_not_modify": "No permission to modify", "chat_info_group_title_not_change": "The group name has not changed", "chat_info_group_describe_not_change": "The group description has not changed", "chat_info_exit_group_chat": "Exit group chat", "chat_info_if_confirm_del_and_exit_group": "Are you sure you want to delete and exit this group?", "chat_info_group_name": "Group name", "chat_info_group_describe": "Group Description", "chat_info_nickname": "Nickname", "chat_info_not_blank_or_empty": "@text cannot be empty or spaces", "chat_info_get_it": "knew", "chat_info_you_are_abandon_group_owner_confirm_select_nickname_as_new_group_owner": "You will automatically give up your identity as the group owner. Are you sure to select {nickName}({userName}) as the new group owner?", "chat_info_transfer_group_owner_permission": "Transfer group owner permissions", "chat_contact_select_group_member": "Select @group members", "chat_contact_search_contact": "Search for contacts", "chat_contact_search_no_result": "No search results", "chat_contact_confirm": "Confirm", "chat_contact_relieve_friend_relate_will_del": "Unfriending will delete all and", "chat_contact_if_del_relate_info": "Do you want to delete the relevant information?", "chat_contact_confirm_del_contact_from_enterprise": "Are you sure you want to remove this contact from your corporate address book?", "chat_contact_del_confirm": "Delete confirmation", "chat_contact_save": "Save", "chat_contact_add_note": "Add notes(0/15)", "chat_contact_add_contact": "Add contact", "chat_contact_edit_contact": "Edit contact", "chat_contact_has_send_add_request": "Add request sent", "chat_contact_modify_success": "Modification successful", "chat_contact_del_friend_success": "Failed to delete friend", "chat_contact_number": "ID:", "chat_contact_max_11": "Required (maximum 11 characters)", "chat_contact_number_not_exit": "The ID does not exist", "chat_contact_note_first_name": "name", "chat_contact_note_last_name": "surname", "chat_contact_default_is_number": "Default is ID (0/15)", "chat_contact_unit": "Unit:", "chat_contact_please_pass_friend_verify": "I am... Please verify through friends (0/15)", "chat_contact_apply_add_friend": "Add friends", "chat_contact_cancel": "Cancel", "chat_contact_contact_error": "Contact error", "chat_contact_number_not_allowed_empty": "ID cannot be empty", "chat_contact_cannot_add_own_number": "Can't add my own number", "chat_contact_allow_6_number_and_11_phone_number": "Only 6-digit ID numbers and 11-digit phone numbers can be entered", "chat_contact_not_allowed_repeat_add": "This user already exists in the corporate address book and cannot be added again.", "chat_contact_has_add_enterprise_record": "This number belongs to the same company as this phone and has been added to the corporate address book.", "chat_contact_has_in_friends_not_allowed_repeat_add": "This user already exists in my friends and cannot be added again", "chat_contact_check_number_fail": "Network connection failed, please check the network", "chat_contact_no_change": "No changes currently", "chat_contact_del_success": "Delete successfully", "chat_contact_del_friend_fail": "Failed to delete friend", "chat_contact_confirm_2": "Confirm(", "chat_contact_max_allow_select_member": "You can only select up to 8 people", "chat_contact_select_all": "Select all", "chat_contact_selected_number_exceed_limit": "The current number of selected people has reached the maximum number of people in the group chat", "chat_contact_select_none": "Select none", "chat_contact_confirm_3": "OK(0/8)", "chat_contact_group_chat": "group chat\n\"", "chat_contact_confirm_forward_to": "Confirm forward to", "chat_contact_ok": "?", "chat_contact_forward_confirm": "Forward confirmation", "chat_contact_file_transfer_assist": "My computer", "chat_contact_my_group_chat": "my group chat", "chat_contact_del": "delete", "chat_save_local": "save", "chat_contact_has_select_enc_file": "Files selected for encryption:", "chat_contact_size": ",size", "chat_contact_group_chat_2": "group chat\n\"", "chat_contact_send_to": "send to", "chat_contact_relax_operate_too_much": "Please take a break. You are operating too frequently.", "chat_contact_add_to_enterprise_chat_list": "Add to business address book", "chat_contact_agree_record_permission": "Please agree to the recording permission first", "chat_contact_not_connect_service": "Unable to connect to server", "chat_contact_not_call_myself": "Can't call myself", "chat_contact_add_number": "Please add the number first: \"", "chat_contact_number_as_friend": "』 users are friends", "chat_contact_has_send_friend_add_request": "Friend request sent", "dial_record_del_call_record": "Delete call history", "dial_record_cancel_all": "Select none", "doc_preview_preview": "Document preview", "edit_oudoc_file_editor": "file editor", "edit_oudoc_dec_fail": "Decryption failed", "encryped_file_manager_doc": "Documentation", "encryped_file_manager_pic": "pictures", "encryped_file_manager_music": "Audio", "encryped_file_manager_video": "video", "encryped_file_manager_other": "other", "encryped_file_manager_if_del_enc_file": "Confirm to delete encrypted files:", "confirm_delete_encryped_file_list": "Are you sure to delete the selected encrypted files?", "encryped_file_manager_size": ",size", "encryped_file_manager_del_file": "Delete files", "encryped_file_manager_if_send_file": "Are you sure you want to send this file?", "encryped_file_manager_send_file": "Send file", "encryped_file_manager_receive_file": "Retrieving files...", "encryped_file_manager_receive_file_fail": "Failed to get file", "group_member_group_member": "Group Members", "group_member_group_invite_limit": "Group chat invitation restrictions", "group_member_group_member_2": "Group Members (", "group_member_not_invite_friend_to_group": "The group owner has enabled group chat invitation restrictions. You will not be able to invite friends to the group.", "help_help": "help", "help_phone_setting": "Phone settings", "help_user_handbook": "User manual", "help_answer_to_question": "FAQ", "help_contact_backup_to_obox": "Contact backup to O<PERSON><PERSON>", "help_not_upload_version": "This version has not been uploaded", "help_document": "Documentation!", "help_load_fail_repeat_again": "Loading failed, please click to try again!", "help_launch_setting": "Startup settings", "help_battery_setting": "Battery settings", "login_number": "ID:", "login_update_success": "Upgrade successful", "login_regist_success": "Registration successful", "login_number_2": "ID:", "login_qr_code_not_reuse": "Note: The registration QR code has expired and cannot be reused.", "login_login": "<PERSON><PERSON>", "login_download_fail": "Download failed", "login_downloading_theme": "Downloading theme...", "login_fail": "Registration failed", "main_contact": "Contact person", "main_browser": "Browser", "main_dial": "<PERSON><PERSON>", "main_message": "Cha<PERSON>", "main_about": "I", "main_collections": "Collection", "main_account_tip": "Account reminder", "main_regist_code_error_clear_data_tip": "The registration code has been reset, please clear the data first", "main_account_error_clear_data_tip": "There is something wrong with the account, please clear the data first", "main_downloading_skin": "Downloading skin...", "main_no_feedback_content": "No feedback content", "main_your_tip_summit_success": "<PERSON><PERSON><PERSON> submitted successfully", "main_your_tip_summit_fail": "Feed<PERSON> submission failed", "main_metatel_setting": "System settings", "setting_notification": "Notifications", "setting_data_storage": "Data and storage", "main_unlock_password": "Unlock password", "main_file_manage": "File management", "privacy_security": "Privacy and security", "blacklist": "Blacklist", "main_feedback_suggest": "Feedback and suggestions", "main_help": "help", "network_node": "Network Node", "main_version_update": "App Version", "main_number": "ID:", "main_feedback": "feedback", "main_please_input_feedback_suggest": "Please enter your feedback suggestions", "main_public": "(announcement)", "main_cancel_stick_fail": "Unpinned failed", "main_stick_fail": "<PERSON><PERSON> failed", "main_confirm_clear_service_message": "Confirm the clear service account message?", "main_confirm_del_message": "Confirm to delete this message?", "main_total": "common", "main_number_contact": "contacts", "main_call_and_not_use_func": "While on a call, this feature cannot be used", "main_add_friend": "Contact information", "main_scan": "<PERSON><PERSON>", "main_if_del_friend_req": "Whether to delete friend request", "main_agree": "Agree", "main_has_agree": "Accepted", "main_agree_failed": "Agree failed", "main_del_error": "Delete error", "main_invalid_qr": "Invalid QR code", "main_enterprise_friend": "corporate friends", "main_title_group": "my team", "main_my_friend": "my friend", "main_my_group": "My Private Group", "main_friend_req": "friend request", "main_voicemeeting_title": "my meeting", "main_group_message_tip": "Group information reminder", "main_not_connect_service": "Unable to connect to server", "main_not_contain": "ID cannot contain #, * and other characters", "main_add_number": "Please add the number first: \"", "main_user_as_friend": "』 users are friends", "voice_meeting_voice_meeting": "Voice conferencing", "voice_meeting_meeting_theme": "Conference theme:", "voice_meeting_option": "Optional(0/15)", "voice_meeting_join_member": "Participants:", "voice_meeting_begin_call": "start call", "voice_meeting_create_meeting_fail_retry": "Meeting creation failed, please try again", "voice_meeting_recall": "Reinitiate", "voice_meeting_voice_meeting_notify": "Voice conference reminder", "voice_meeting_meeting_end_if_retry": "The meeting has ended. Do you want to restart it?", "voice_meeting_meeting_if_join": "The meeting is in progress. Do you want to join the meeting?", "voice_meeting_net_not_avail": "The network is currently unavailable", "voice_meeting_please_agree_voice_permission": "Please agree to voice permissions first", "message_dialog_forward": "Forward to", "password_check_input_password": "Enter password", "password_check_password_error": "There are too many incorrect passwords, please", "password_check_retry_later": "Try again in minutes", "password_check_retry_later_hour": "Try again in hours", "password_check_input_password_error": "Password entered incorrectly", "password_check_restore_unlock_password": "Recover unlock password", "password_check_tip_error": "Password question:", "password_check_answer": "answer:", "password_check_input_answer": "Please enter answer", "password_check_reset_password": "Reset unlock password:", "password_check_blank_del_password": "Leave it empty to delete the unlock password", "password_check_input_again": "Enter again:", "password_check_repeat_input_6_password": "Please enter the 6-digit password again", "password_check_confirm": "Confirm", "password_check_reset_success": "Password reset successful", "password_check_length_6": "The unlock password must be a 6-digit password", "password_check_password_not_same": "The two password inputs are inconsistent", "password_check_set_unlock_password": "Set unlock password", "password_check_old_unlock_password": "Old unlock code:", "password_check_input_6_number": "Please enter the 6-digit password", "password_check_unlock_password": "Unlock password:", "password_check_password_tip": "Password question:", "password_check_use_restore_password": "Used to recover unlock password", "password_check_can_select": "(optional)", "password_check_input_password_6": "Please enter the 6-digit password", "password_check_modify_password": "Change unlock password", "password_check_setting_unlock_password": "Set unlock password", "password_check_new_unlock_password": "New unlock code", "password_check_blank_del_password_2": "(Empty to delete unlock password)", "password_check_password_modify_success": "Unlock password cleared", "password_check_password_set_success": "Password set successfully", "password_check_please_input_answer": "Please enter answer", "password_check_old_password_error": "Old password is wrong", "change_password_need_new_password": "Please enter new password", "pc_login_pc_confirm_login": "Desktop login confirmation", "pc_login_confirm_login": "Confirm login", "pc_login_cancel_login": "Cancel login", "pc_login_confirm_agree_fail": "Confirm authorization failed", "pc_login_qr_overdue": "QR code has expired", "pc_login_confirm_agree_success": "Confirm authorization is successful", "pc_login_refuse_agree_fail": "Authorization denied failed", "pc_login_tip": "hint", "pc_login_if_exit_windows_number": "Do you want to exit the Windows client?", "photo_burn_pic_load_error_retry": "Image loading error, please go back and try again", "push_lack_param": "Push is missing parameters", "qr_code_abandon_modify_data": "Give up modifying data", "qr_code_abandon_back_up": "You have not saved the modified information. Do you want to abandon the modification and return to the previous page?", "qr_code_abandon": "give up", "qr_code_no_abandon": "don't give up", "qr_code_number": "ID:", "qr_code_name_not_empty": "Name cannot be empty", "qr_code_edit_qr_content": "Edit QR code content", "qr_code_group_qr": "Group QR code business card", "qr_code_scan_join": "Scan the QR code above to join this group\nThis QR code is valid for three days", "recover_contact_restore": "Contact recovery", "recover_contact_restore_to_phone": "Restore contacts to mobile phone", "recover_start_restore": "Start recovery", "recover_quit_tip": "Exit reminder", "recover_back_may_lose_data": "Data is currently being restored. Data recovery will be canceled after returning. Some data may be lost. Do you confirm the cancellation?", "recover_restore_waiting": "Contacts are being restored, please wait...", "recover_restore_fail": "Contact recovery failed", "recover_restore_cancel": "Contact recovery canceled", "recover_restore_success": "Contacts restored successfully", "recover_restore_fail_disconnect": "Contact recovery failed, connection disconnected", "recover_restore_fail_2": "Recovery failed", "regist_6_number": "6-digit unlock password (optional)", "regist_regist_success": "Registration successful", "regist_input_right_number": "Please enter the correct mobile phone number", "regist_please_send_code": "Please send verification code first", "regist_input_right_verify_code": "Please enter the correct verification code", "regist_input_6_password": "Please enter the 6-digit password", "regist_regist_fail": "Registration failed", "regist_confirm_select": "The ID you selected is:", "regist_as_number": "』 as ID", "regist_number_confirm": "Number confirmation", "ringtone_setting": "Ringtone settings", "ringtone_restore_default": "Restore default", "ringtone_ringtone": "ringtone", "ringtone_music": "music", "ringtone_name": "Ringtone name", "ringtone_set_success": "Ringtone set successfully", "search_search": "search", "search_input_content": "", "search_no_result": "No search results", "search_public": "(announcement)", "search_look_more": "View more", "search_record": "record", "search_call": "call", "search_chat": "chat", "search_group_chat": "group chat", "search_fumc": "Function", "search_uni": "Mini program", "main_setting": "settings", "setting_message_voice_tip": "Message Sound Prompt", "setting_vibrate_tip": "Message Vibration Prompt", "setting_security_mode": "Strong security mode", "setting_ringtone_setting": "Ringtone settings", "setting_theme_setting": "Theme settings", "setting_del_all": "Delete all messages", "setting_if_del_all": "All personal and group chat records will be deleted. Are you sure you want to delete all messages?", "setting_default_theme": "default theme", "setting_del_success": "Delete successfully", "setting_del_fail": "Delete failed", "share_select_friend": "Select friends", "share_creat_new_chat": "Create new chat", "share_latest": "Recent Chats", "share_share_to": "Share", "share_share_meeting": "Share to this conversation?", "share_not_support": "This type of sharing is not supported yet", "splash_check_net": "Please check if the network is available", "splash_has_reset_clear_data": "The registration code has been reset, please clear the data first", "splash_has_erroe_clear_data": "There is something wrong with the account, please clear the data first", "splash_account_tip": "Account reminder", "splash_has_update": "There is currently a patch update", "splash_updating": "Updating", "splash_restart": "The update has been completed and will be restarted soon", "splash_agree_permission": "Please agree to relevant permissions first", "splash_update_fail_repeat": "Failed to upgrade data, please try again", "theme_setting": "Theme settings", "video_pre": "Video preview", "theme_file_is_exit": "Please check if the video file exists", "weh_reload": "reload", "weh_retry": "Failed to obtain data, please check the network and try again ~", "web_rtc_invite_video_chat": "The other party invites you to video chat", "web_rtc_refuse": "The other party rejected your request", "web_rtc_permission_refuse_end_call": "Recording permission has been denied and the call has automatically hung up.", "capture_qr": "QR code scanning", "capture_scan_qr": "Please scan the authorization QR code to complete the registration\nAfter successful registration, the QR code will become invalid.", "capture_input_qr_number": "Please enter the QR code serial number", "capture_open_light": "The environment is too dark, please turn on the flash", "capture_reopen": "There was an error opening the camera, please reopen the page and scan the QR code.", "capture_max_40": "Enter up to 40 characters", "capture_number_no_empty": "Registration serial number cannot be empty", "capture_file_not_pic": "The file is not a picture", "capture_scan_fail_retry": "<PERSON><PERSON> failed, please try again!", "capture_qr_invalid": "QR code is invalid", "other_calling": "The other party is on the call", "other_not_online_retry_later": "The called did not answer, please call again later.", "other_not_online_": "It is not convenient for the other party to answer the call", "other_connect_error_retry_later": "Connection failure, please try again later", "other_user_not_online": "The called user is not online", "other_call_fail": "call failed", "other_you": "you", "other_you_small": "you", "other_second": "Second", "other_open": "open", "other_clear_msg": "Clear messages", "other_reset_unread": "<PERSON> as unread", "other_reset_has_read": "<PERSON> as read", "other_cancel_stick": "Unpin", "other_stick": "pinned chat", "other_del_chat": "delete chat", "other_copy": "Copy", "other_repeal": "Recall", "other_multiple_selection": "Multiple choice", "other_confirm_del": "Confirm to delete?", "other_not_support_file_format": "This file format is not supported yet", "other_max_input_15": "Enter up to 15 characters", "other_max_input_40": "Enter up to 40 characters", "other_updating": "Upgrading...", "other_not_close_page": "Please do not close the page", "other_loading": "Loading...", "other_no_support_this_type_file": "This type of file is not supported", "other_default_ring": "Default ringtone", "other_giving": "giving", "other_dial": "Dial...", "other_coming_call": "of calls", "other_calling_2": "incoming call", "other_metatel_call": "You have a missed call", "other_with": "Being with", "other_calling_3": "Calling...", "other_metatel": "ID:", "other_invite_join_voice_meeting": "Invite you to a voice conference", "other_add_friend": "Request to add you as a friend", "other_enterprise_public": "Corporate Announcement", "other_has_message": "You have news", "other_has_call": "You have a call", "other_net_unusable": "Your network is unavailable", "other_error": "Communication abnormality", "other_secret_manage_error": "Abnormal secret management", "other_config_error_metatel_will_exit": "Getting configuration error, <PERSON><PERSON> is about to exit", "other_param_error_then_exit": "Parameter error, <PERSON><PERSON> is about to exit", "other_id_not_exit": "The process ID does not exist and <PERSON><PERSON> is about to exit.", "other_service_config_error": "Exception in parsing the server configuration file, <PERSON><PERSON> is about to exit", "other_metatel_will_exit": "<PERSON><PERSON> is about to quit", "other_not_find_config_will_exit": "The server cannot find the configuration, <PERSON><PERSON> is about to exit", "other_not_call_self": "Can't call myself", "other_modify_group_name_as": "Modify the group name to", "other_invite": "invite you", "other_with_2": "and", "other_join_group_chat": "Join group chat", "join_group_chat": "Join", "other_invite_2": "invite", "other_remove_by_group_owner": "You have been removed from the group chat by the group owner or administrator", "other_has": "has been", "other_remove_from_group": "Kick Out of Group Chat", "other_update_group_info": "Group information has been updated", "other_you_invite": "you invite", "other_join_group": "Join group chat", "other_invite_join_group": "Invite you to join group chat", "other_group_owner": "Group owner", "other_has_lock_group_name": "Group name is locked", "other_lock_group_name_cancel": "Group name lock has been canceled", "other_has_limit": "Group chat invitation restrictions have been enabled, group members will not be able to invite friends to the group", "other_has_close_limit": "Group chat invitation restrictions have been turned off. Group members can invite friends to the group.", "other_as_new_group_owner": "Has become a new group owner", "other_by_scan": "by scanning", "other_join_group_by_code": "Share the group QR code to join the group chat", "other_has_exit_group": "Exited group chat", "other_pass_verify": "After the friend verification is passed, you can use <PERSON><PERSON> to communicate confidentially with your friends!", "other_connect_error_retry": "Abnormal connection to server, please restart Linksay", "other_not_take_pic": "The current camera resources are occupied and the shooting function cannot be performed!", "other_file_open_fail_retry": "File opening failed, please try again", "other_not_support_multi_show": "Your phone is lower than Android 4.1.2 and does not support multi-line notification display! !", "other_download_complete": "Download completed", "other_not_support_headup": "The version is lower than Andriod5.0 and cannot experience HeadUp style notifications.", "other_read_dismiss": "Messages that will burn after reading", "other_voice_message": "[Voice message]", "other_pic_message": "[Picture message]", "other_video_message": "[Video message]", "other_file_message": "[File message]", "other_pic_word_message": "[Image and text sharing]", "other_calendar_message": "[Mini Program News]", "other_chat_history": "[Chat history]", "other_chat_card": "[Contact Business Card]", "other_time": "time(", "other_second_2": "Second)", "other_release_cancel_send": "Lift your finger to cancel sending", "other_finger_move_up_cancel_send": "Swipe up to cancel sending", "other_record_too_short": "Recording is too short", "other_release_send": "Release to send", "other_press_said": "Swipe left to cancel", "other_connect_fail_click_refresh": "Network connection failed, please check the network", "other_connect_error_repeat": "Error connecting to server, trying to reconnect...", "other_net_not_available_check_net": "The current network is unavailable, please check whether the network is normal!", "other_check_net_available": "Please check if the network is available!", "other_retry_waiting": "Network connection, please try again later", "other_connect_service": "Network connection...", "other_at_max": "The input limit has been reached", "other_word": "Character", "other_forget_password": "Forgot password", "other_del": "delete", "other_can_said_time": "It can also be said", "other_click_notify": "Notification click msgId", "other_friend_detail": "Friend details", "other_manage_right_trans": "Group owner management rights transfer", "other_lock_group_name": "Lock group name", "other_lock_other_not_modify": "Once locked, other members cannot change the group name.", "other_only_group_owner_can_invite": "Once enabled, group members will not be able to invite friends to the group, and only the group owner can invite friends to the group.", "other_windows_login": "Windows Linksay is logged in", "other_not_operate_self_can_exit": "If you are not operating it yourself, you can exit immediately", "other_trans_file": "Transfer files", "other_exit_by_key": "Exit with one click", "other_pic_pre": "Picture preview", "other_send": "Send", "other_take_pic": "Take Photo", "other_connect_not_avail": "Network connection not available", "other_read_dismiss_2": "Burn after reading", "voice_phone": "Voice call", "video_phone": "Video call", "other_file": "Document", "other_note": "Remarks:", "other_default_number": "Default is ID", "other_company": "Unit:", "other_unit": "Unit:", "other_photo_album": "photo album", "other_preview": "Preview", "other_note_2": "other_note", "other_number": "number", "other_accept": "accept", "accpted": "Accepted", "other_look_for_more_contact": "View more contacts", "other_invite_video_connect": "Invite you to a video call", "other_name": "name", "other_click_take_photo": "Click to take a photo", "other_name_touch_take_photo_press_take_movie_2": "Touch to take a photo, long press to take a video", "other_no_relate_contact": "No relevant contacts", "other_no_relate_group_contact": "No related group chat", "other_no_friend_request": "No friend requests yet", "other_no_meeting_record": "No meeting minutes yet", "other_total": "share", "other_number_group": "group chat", "password_not_empty": "Unlock password cannot be empty", "input_password_again": "Please enter the unlock password again", "no_customer": "There is currently no available customer service", "button_create_voice_meeting": "Start a voice", "button_create_video_meeting": "Start a video", "video_meeting_title": "video conferencing", "confirm_delete_friend_verify": "Are you sure to delete the selected friend request?", "enter_conference": "Enter the meeting", "video_meeting_state_notify": "Video conference reminder", "video_meeting_create_error": "Failed to create meeting", "enter_meeting_progress": "Entering the meeting, please wait.", "create_video_meeting_error": "The current network is unstable, please try again later", "video_meeting_network_error": "The current network is unstable, please reconnect", "in_other_meeting_error": "You are participating in a meeting and cannot initiate or join it again", "video_meeting_is_full": "The current meeting room is full", "video_meeting_miss": "The meeting does not exist or has ended", "create_group_txt": "Create a public channel", "create_private_group_txt": "Create a private group chat", "delete_call_log_confirm": "Confirm to delete the selected call history?", "delete_call_log_progress": "Deleting call history...", "connect_customer": "Contact customer service", "original_pic": "Original picture", "metatel_id": "ID:", "register_has_bind": "Registration code has been bound", "register_code_error": "Registration code error", "register_code_invalid": "Registration code invalid", "number_rush_registration": "The number has been registered", "number_bind_fail": "ID binding failed", "code_error": "Mobile phone verification code error", "send_sms_error": "Failed to send verification code", "you_where_mentioned": "[Someone@me]", "call_out": "exhale", "call_in": "Incoming call", "no_second": "0 seconds", "hour": "hours", "minutes": "minute", "other_second_3": "Second", "include": "Include:", "draft": "draft:", "other_voice_message_2": "voice message", "chat_info_clear_chat_record_success": "Cleared chat history successfully", "chat_info_clear_chat_record_fail": "Failed to clear chat history", "file_not_exit": "File does not exist", "decrypting_file_in_progress_hold_on": "Decrypting files, please wait...", "the_network_is_not_available_please_try_again": "The current network is unstable, please try again", "unreachable": "There is no corresponding program and the link cannot be accessed", "parameter_inconsistency": "Inconsistent with the parameters passed in last time", "fail_to_get_the_theme": "Failed to obtain theme resources", "self": "Own", "share_to": "Share to", "related_messages": "Related chat records", "scan_qr": "Scan code", "check_file_if_exit": "Please check if the file exists", "unknown": "unknown error", "network_time_out": "Network timeout, please confirm whether the network is available!", "network_unavailable": "Network connection failed, please confirm whether the network is available!", "service_unavailable": "Unable to find the server, please confirm whether the network is available!", "source_unavailable": "Invalid resource", "return_is_null": "The return value is empty", "too_short": "Recording time is too short", "hw_security": "Security authorization", "disable_warning": "Deactivation will cause some functions of this software to not work properly. Are you sure you want to deactivate it?", "know": "Read", "title_permissions": "Permission usage statement", "read_statement": "If you click to agree and start using this software, it means that you have read and accepted the <a href=\"http://www.huawei.mdm.com\":\"\"Disclaimer and Software License Agreement\"</a>.", "accept_btn": "Agree", "exit_btn": "quit", "title_license": "Disclaimer and Software License Agreement", "view_detail": "View details", "uni_app": "workbench", "schedule": "schedule", "talkback": "intercom", "sharing_platform": "Sharing platform", "approval": "Approval", "temporary_not_open": "Not yet open", "add": "Add to", "complete": "Complete", "canceled": "Canceled", "opposite": "the other party", "call_duration": "Call duration", "call_terminate": "Call not connected", "me": "I", "send_msg": "Send message", "id": "ID:", "name": "Remarks:", "capture_scan_qr_4_rabbit": "Please scan the authorization QR code to complete the registration\nAfter successful registration, the QR code will become invalid.", "regist_as_number_4_rabbit": "』 as ID", "login_regist_success_4_rabbit": "Registration successful", "login_update_success_4_rabbit": "Upgrade successful", "module_activity_encrypt_file_tv_title_text_4_rabbit": "Encrypt files", "metatel_server_number_4_rabbit": "System notification", "chat_service_number_4_rabbit": "System notification", "no_company_contactor": "There is no related team yet", "metatel_file_assistant_4_rabbit": "My computer", "title_system_setting": "Set prompts", "system_setting_content": "In order to ensure a good user experience, it is recommended to activate the device manager to improve online status and call completion rate.", "ignore": "Don't prompt again", "uni_tip_title": "Warm reminder", "uni_tip_content": "The frequently used mini programs you added have not been saved. Do you want to save them?", "uni_all": "all", "uni_common": "Commonly used", "other_has_message_4_rabbit": "You have news", "chat_contact_number_not_exit_4_rabbit": "This number does not exist", "chat_contact_default_is_number_4_rabbit": "Default is ID (0/15)", "other_pass_verify_4_rabbit": "After the friend verification is passed, you can use <PERSON><PERSON> to communicate confidentially with your friends!", "chat_contact_allow_6_number_and_11_phone_number_4_rabbit": "Only 6-digit numbers and 11-digit phone numbers can be entered", "qr_code_number_4_rabbit": "ID:", "qrcode_edit_4_rabbit": "Edit QR code", "metatel_customer": "System customer service", "the_other_party_mobile_phone_may_not_be_around_please_try_again_later": "The other party's phone may not be with you. It is recommended to try again later.", "system_setting_overlays": "Your mobile phone does not authorize <PERSON><PERSON> to obtain the floating window permission. Please enable the permission to ensure normal use of audio and video calls.", "tip_title": "Warm reminder", "inviated_tip": "The meeting invitation was successful. If you do not enter after 45 seconds, please invite again.", "transport_pc_data_failed": "Authorization failed, please scan the code again", "transport_pc_data_success": "Authorization successful", "password_setting": "Unlock settings", "unlock_password": "Unlock password", "unlock_finger": "Fingerprint unlock", "change_lock_password": "Change unlock password", "password_check": "Password verification", "destory_setting": "Self-destruct settings", "destory_tip": "It cannot be restored after self-destruction, and it will start immediately after entering the password, so please use it with caution.", "destroy_switch": "Self-destruct function switch", "destory_password": "Self-destructing digital password", "destory_tip_title": "hint", "destory_tip_content": "The self-destruct function is mainly used to protect sensitive data and private information in Linksay from being leaked in case of emergencies, and to clear usage traces promptly, quickly and covertly. Once the self-destruct function is enabled, data cannot be restored. Please back up important data before operation.", "update_destory_password": "Change self-destruct password", "set_destory_password": "Set a self-destruct password", "destory_prefix_password": "Self-destruct password:", "old_destory_prefix_password": "Old self-destruct password:", "new_destory_prefix_password": "New self-destruct password:", "destory_password_not_empty": "The self-destruct password cannot be empty", "destory_password_check_length_6": "The self-destruct password must be a 6-digit password", "input_destory_password_again": "Please enter the self-destruct password again", "no_same_2_screen_password": "The self-destruct password cannot be the same as the lock screen password", "password_check_title": "Please enter the lock screen password", "destory_password_update_successed": "Self-destruct password changed successfully", "no_same_2_destory_password": "The lock screen password cannot be the same as the self-destruct password", "tip_screen_password_first": "Please enable <PERSON><PERSON> unlock password first", "low_version_not_support_finger_print": "Your system version is too low and does not support the fingerprint function", "your_phone_not_support_finger_print": "Your phone does not support fingerprint function", "add_finger_print": "You haven't set up a lock screen yet. Please set up a lock screen and add a fingerprint first.", "need_finger_print": "You need to add at least one fingerprint in system settings", "verify_finger_print": "Please verify fingerprint unlocking", "verify_finger_print_fail_try_again": "Fingerprint authentication failed, please try again", "verify_finger_print_fail_try_again_later": "Too many attempts, please try again later", "user_finger_print": "Use fingerprint", "tip_set_finger_print": "Please enable the fingerprint unlocking function in the mobile phone system first and enter the relevant fingerprints", "new_msg_tip": "new messages", "latest_unread_msg": "First unread message", "please_select_item": "Please select the item to be deleted", "please_select_share_item": "Please select the items you want to forward", "share_tip": "Among the messages you selected, voice messages, disappearing after reading, undownloaded files, expired files, and mini program notification messages cannot be forwarded. Do you want to continue?", "please_select_valid_item": "Please select a message to forward", "destory_no_avaliable": "The current network is unavailable, please check the network", "load_fail_alert_retry": "Loading failed, please try again later!", "loading": "Loading...", "tips": "Hint", "enterprise_number": "Service number cannot be empty", "connection_failure_control": "The current network is unstable and the connection has been disconnected", "max_select_20": "Select up to 100 messages", "module_activity_boot_set_alar_text": "Floating window settings", "http_request_error": "Requesting data failed", "password_no_same_to_old": "The new password cannot be the same as the old password", "clear_uni_app": "Clear workbench applet", "clear_all_store_files": "Clear all stored files", "clear_company_book": "Clear corporate address book", "clear_friend_book": "Clear friend address book", "marketing_notice": "Marketing Notice", "not_find_boot": "No boot interface found", "it_is_currently_the_default_theme": "It is currently the default theme", "tbs_is_update": "The plug-in is being updated, please wait!", "web_rtc_meeting_permission_refuse": "Please enable recording and camera permissions first", "camera_permission_refuse_end_call": "Camera permission denied, this feature cannot be used", "pc_export_file_confirm": "Desktop metatel file export confirmation", "pc_export_confirm": "Confirm export", "pc_export_cancel": "Cancel", "channel_call_of_notification": "Incoming call notification", "channel_msg_of_notification": "Message notification", "chat_audio_permission_refused_then_cannot_use_camera": "Audio permission has been denied, the recording function will not work properly", "chat_info_if_confirm_clear_chat_group_record_with_this_contact": "Are you sure you want to clear the chat history with this group?", "channel_description_of_call_notification": "Incoming call notification description", "please_enter_your_password": "Please enter password", "confirm": "Confirm", "please_input_user_nickname": "Please enter your nickname", "finished": "Finish", "nick_name_not_empty": "Nickname cannot be empty", "capture_picture": "Photograph", "get_picture_from_phone": "Select from album", "save_photo": "Save Image", "edit_user_name": "Modify Avatar", "init_camera_fail": "Failed to initialize the camera, please reopen it", "picture_tailor_error": "Image cropping failed", "access_exception": "An exception occurred while obtaining permissions, please re-enter.", "camera_permission_not_open": "You do not have recording permission enabled", "camera_permission_not_open_": "You do not have permission to record. Please enable permission first before recording.", "camera_or_write_permission_not_open": "You do not have permission to open the camera or read and write files", "edit_user_icon": "profile picture", "contact_info": "Contact Details", "edit": "Edit", "add_buddy": "Add friends", "media_links_and_docs": "Media and Documents", "group_photo": "Group avatar", "background": "Set chat background", "share_contactor": "Share Contact Card", "search_dialog": "Search chat history", "mute": "Do Not Disturb", "block_user": "Add to blacklist", "clear_dialog": "Clear Chat History", "delete_contact": "Delete Contact", "select": "Select", "photo": "pictures", "no_content": "no content", "exiting_a_Group": "Exit group chat", "other_members_of_the_group_are_notified_when_the_group_name_is_changed": "Only the group owner and administrators can modify the group name.", "other_members_of_the_group_are_notified_when_the_group_describe_is_changed": "Only the group owner and administrators can modify the group description.", "i_am_a_group_chat_name": "Group name", "i_am_a_group_chat_describe": "Group description", "description": "describe", "quit_group": "Quit", "dissolution_group": "Disband", "dissolution": "Dismiss group chat", "select_contact": "Select contact", "create": "create", "members": "group members", "members_num": "@num members", "chat_img_video": "Photos & Videos", "chat_img": "photo", "chat_camera": "Photography & Videography", "chat_camera_pic": "Photograph", "invite_you_video_call": "Invite you to a video call", "invite_you_audio_call": "Invite you to a voice call", "network_text": "@networknode", "personal_data": "personal data", "personal_data_profile_photo": "avatar", "personal_data_name": "Nickname", "personal_data_my_qr_code": "My QR Code", "one_by_one_forward": "forward one by one", "combine_and_forward": "merge forward", "replay": "Reply", "audio": "Voice", "select_a_group": "Select group chat", "forward_to": "forward to", "un_msg_user": "User Name:%s", "un_msg_id": "ID:%s", "marge_msg_history": "@title's chat history", "chat_msg_history": "group chat", "continue_": "continue", "forword_tips": "Special messages such as voice messages, call records, and undownloaded source files cannot be forwarded. Do you want to continue?", "re_edit": "Re-edit", "waiting": "Waiting", "the_video": "video", "the_videos": "Videos", "the_file": "Documents", "the_picture": "Images", "has_save_to": "Saved to ", "contact_card": "Contact business card", "chat_item_pin": "pin to top", "you_missed_call": "Missed call", "welcome": "welcome", "create_you_account": "Create your account", "please_input_user_phone_number": "Please enter your mobile phone number", "register_info": "Unregistered mobile phone numbers will be automatically registered after verification", "read_and_agree": "I have read and agree", "user_service_agreement": "\"User Service Agreement\"\n", "rivacy_clause": "\"Privacy Policy\"", "send_sms_verification_code": "Send SMS verification code", "please_input_sms_code": "Please enter the SMS verification code", "sms_code_info": "Verification code has been sent to @1s\nPlease enter the 6-digit verification code below", "resend_after": "Resend after @1s", "reset_resend_after": "Click to resend", "please_user_service_agreement": "Please agree to the user agreement", "user_phone_number_type": "Mobile phone number format is incorrect", "search_user": "Search for contacts", "scan_qr_code_to_add": "Scan the QR code to add a contact", "find_metatel_user": "Find a contact", "can_not_add_myself_friend": "You cannot add yourself as a friend!", "can_not_search_the_contactor": "The contact was not found!", "retracted_a_msg": "@name retracted a message", "real_authenticate": "Real name authentication", "please_input_name": "Please enter name", "please_input_id_card": "Please enter your ID number", "go_authenticate": "Go to authentication", "identity_authenticate": "Identity authentication", "take_a_photo_of_your_own_face_please_make_sure_you_are_facing_the_phone_and_there_is_sufficient_light": "To take a picture of your own face, please make sure it is facing the phone and there is sufficient light.", "gather_my_face": "Collect your face", "account": "Account number", "about_account": "ABOUT ACCOUNT", "create_account": "Create Identity", "first_use_account": "First time using Linksay", "restore_account": "Restore Identity", "had_account_mnemonic": "Already have mnemonic", "mnemonic": "Mnemonic words", "mnemonic_store": "Mnemonic recovery", "private_key": "private key", "soon_login": "Log in now", "please_enter_mnemonic_words_separated_by_spaces": "Manually enter the mnemonic words in order and separate them with spaces", "restore_identity": "Restore Identity", "please_enter_the_identity_credentials": "Please enter authorization code", "next_step": "Next step", "create_group_failed": "Failed to create group chat", "secret_world": "<PERSON><PERSON>", "what_is_mnemonic": "What is a mnemonic phrase?", "what_is_matatel": "What is <PERSON><PERSON>?", "what_is_identity": "What is identity?", "create_account_generate_mnemonic_hint": "1. The mnemonic phrase is your private identity key. Please do not easily send your mnemonic phrase to others.\n\n2. The mnemonic phrase cannot be retrieved after it is lost, so please keep it properly.\n\n3. It is recommended that you save and back up the mnemonic phrases in a non-networked way, such as handwriting.\n\n4. Please do not spread the mnemonic words at will. If the mnemonic words are spread at will, the account assets will be lost and cannot be recovered.", "generate_mnemonic": "Generate mnemonic words", "verify": "Verify", "mnemonic_view_hint": "1. Please copy and record the mnemonic words in order to ensure that the recording order is correct.\n\n2. The mnemonic phrase has been generated successfully. This will be your identity credential to enter Linksay. Please keep it properly.\n\n3. Please do not share or store the mnemonic phrase in an online environment, such as emails, photo albums, social applications, network disks, etc.", "verify_credentials": "Verify credentials", "verify_credentials_hint": "Please fill in your promotion authorization code in the input box below and click the Next button to continue.", "please_enter_mnemonic": "Please enter mnemonic phrase", "nft_advance_number": "DID:", "address": "address:", "not_yet_develop": "This function is in internal testing and is only available to some users.", "please_select_the_mnemonic_in_order_to_ensure_that_the_mnemonic_recorded_in_the_transcription_is_correct": "Please select the mnemonic words in order to ensure that the mnemonic words in the transcribed records are correct.", "welcome_to_the_group_chat": "Welcome everyone to join the group chat", "chat_history_cleared_successfully": "Chat history cleared successfully!", "check_for_updates_failed": "Failed to check for updates!", "message_has_no_content": "The message has no content!", "unknown_name": "Unknown name!", "unknown_account": "unknown account", "the_message_cannot_be_decrypted_click_to_receive_gain": "The message cannot be decrypted, click to receive it again", "unknown_file": "unknown file", "image_failed_to_load": "Image loading failed", "unknown_message_type": "Unknown message type", "unknown_alert_message": "Unknown prompt message", "not_a_picture": "not a picture", "authentication_failed_please_check_and_try_again": "Authentication failed, please check and try again!", "save_the_mnemonic_successfully": "Save mnemonic successfully", "save_the_mnemonic_failed": "Failed to save mnemonic phrase", "selected": "selected", "update_immediately": "Update Now", "new_version_update": "Version update", "update_content": "Update content", "broken_update_package": "The update package is damaged", "insufficient_permissions": "Insufficient permissions", "file_download_failed": "File download failed", "application_package_parsing_failed": "Application package parsing failed", "about_to_install_the_update_package": "Update package will be installed soon", "the_mnemonic_is_invalid_please_check_it_carefully_and_try_again": "The mnemonic phrase is invalid, please check carefully and try again!", "about": "About", "privacy_policy": "Privacy Policy", "terms_of_use": "Terms of Use", "use_comments_or_feedback_suggestions": "Use comments or feedback suggestions", "nick_name": "Nickname", "chat": "Cha<PERSON>", "delete_friend": "delete friend", "view_Avatar": "View Avatar", "save": "Save", "unauthorized": "Unauthorized, please click to try again", "group_invalid": "The current group is invalid", "nickname_has_been_modified_but_not_saved_OK_to_exit": "The nickname has been modified but not saved. Are you sure you want to exit?", "today": "today", "yesterday": "yesterday", "image": "pictures", "file": "Document", "contact": "Contact person", "the_account_is_illegal_and_forbidden_to_use_pleas_contact_the_administrator": "Sorry, your account @account is illegal and is prohibited from use. Please contact the administrator!", "my_collection": "my collection", "collection_list": "Collection list", "collection_avatar": "avatar", "collection_wallpaper": "wallpaper", "set_as_avatar": "Set as avatar", "avatar_set_successfully": "Avatar set successfully", "avatar_set_failed": "Avatar setting failed", "set_background": "set background", "input_max_length": "The input characters cannot exceed @length words", "verification_mnemonic": "Verify mnemonic phrase", "all_member": "All members", "account_address_acquisition_failed": "Failed to obtain account address!", "all_contacts_chat_background_set_successfully": "All contact chat backgrounds are set successfully", "cancel": "Cancel", "mute_talk": "Mute", "admin_rights": "Administrative Permissions", "year": "Year", "month": "moon", "day": "sky", "am": "morning", "pm": "afternoon", "one_month": "January", "save_and_share": "Save and share", "group_management": "Group management", "personalization_and_others": "Personalization and more", "choose_from_the_collection": "Choose from collection", "channel_share": "Secret code sharing", "channel_airdrop": "airdrop", "channel_share_generated": "Password has been generated", "group_admin": "Group administrator", "has_joined_this_group": "Already joined the group", "this_group_is_invalid": "This group is invalid", "channel_member": "@number people", "channel_card": "Invite you to join group chat", "cannot_join_this_group": "Can't join this group", "request_sub_channel": "Please wait for administrator review and approval", "request_sub_channel_error": "Failed to apply to join group chat", "channel_card_save_success": "Group business card saved successfully", "channel_card_save_error": "Group business card failed to save", "share_info": "Copy this content and open Linksay....", "join_channel_apply": "Join Requests", "join_channel_apply_text": "@name applies to join the group", "all_mute": "Mute All Members", "invite_limit": "Invitation restrictions", "join_group_audit": "Group Join Verification", "admin": "Administrator", "confirmed": "Confirmed", "invitation_has_expired": "The invitation has expired", "notShare": "Sharing is prohibited in this group", "customize": "Customize", "mute_time": "Mute Du<PERSON>", "choose_the_duration_of_the_mute": "Choose the mute duration", "taboo": "Banned...", "taboo_all": "All members are banned", "other_channel_card": "[Invite you to join the group chat]", "undo_time": "Recall Duration", "select_undo_time": "Choose the recall duration", "group_member_mute_body": "@targetDisplayName has been banned by @operationDisplayName for @time", "group_member_remove_mute_body": "@targetDisplayName has been unbanned", "assigned_as_administrator": "@time @targetDisplayName is assigned as an administrator", "administrator_privileges_revoked": "The administrator rights of @time @targetDisplayName have been revoked", "group_kick_member": "@targetDisplayName has been kicked out of this group by @operationDisplayName", "open_all_mute": "Enable all bans", "close_all_mute": "Close all bans", "remove_group_member": "Remove Group Member", "confirm_remove_group_member": "Are you sure you want to remove this member from this group?", "undo_all_msg": "Recall all messages sent by this member", "group_member_details": "Group Member Details", "collection_sticker_messages": "[Collection Sticker Message]", "tap_lock_to_stop": "<PERSON><PERSON>", "slide_to_cancel": "Slide to cancel", "sticker": "<PERSON>er", "restore_default_background": "Restore default background", "click_msg": "Click to view the message", "share_to_other": "I shared [@title]@url", "nft_advance_number_tab": "DID", "stare": "Keep an eye on the market", "apply_for_your_nickname_and_avatar": "Apply to get your nickname and avatar", "refuse": "refuse", "allow": "allow", "authorization_failed_please_try_again_late": "Authorization failed, please try again later!", "exceeded_selected": "Exceeded the maximum number of people selected (@num)", "fail_to_edit": "Modification failed!", "group_only_self": "Only myself is left in the group", "file_select_max_length": "The selected file cannot be larger than @len", "image_saved_successfully": "Picture saved successfully", "nft_advance_number_no": "No digital identity yet", "main_chat": "Cha<PERSON>", "close_friend": "Close Friend", "theme": "Theme", "wallet": "Wallet", "service_connect_failed": "Service connection failed, please try again", "channel_group": "Channel Group", "group_chat": "Group Chat", "crowd_creation": "Crowd Creation", "market": "Market", "trend": "Trend", "application": "Application", "set_as_nft_advance_number": "Set up a digital identity", "nft_advance_number_set_successfully": "Digital identity set up successfully", "nft_advance_number_not_set": "No digital identity set up", "enter_the_authorization_code": "Enter authorization code", "public_node": "public node", "private_node": "private node", "node_selection": "Node selection", "msg_merge_fail": "Message merge failed, please try again!", "skip": "skip", "no_relevant_node_data": "No relevant node data!", "sorry_the_current_node_is_not_on_the_chain_please_try_again_later": "Sorry, the current node is not on the chain yet, please try again later!", "opinion_feedback_tips": "If you have any questions that need to be consulted or solved, please follow the official account or scan the QR code below to add customer service for feedback.", "save_qr": "Save QR code", "save_success": "Saved successfully", "save_failed": "Save failed", "authorization_code_exception": "Authorization code exception", "authorization_code_exception_1": "The annotation word is invalid. Please re-enter it.", "connect_tips": "Connecting...", "receive_tips": "Receiving...", "search_contact_msg": "Search contacts/groups and messages", "default_tag": "<PERSON><PERSON><PERSON>", "more": "More", "set_label": "settings", "you_retracted_msg": "You recalled a message", "other_retracted_msg": "The other party recalled a message", "undo_fail": "Network abnormality, withdrawal failed", "record_send": "Is the currently recorded voice sent?", "you_can_only_select_up_to_16_images_or_videos": "You can only select up to 16 pictures or videos", "copy_address": "Copy Address", "app_destroyed_about_to_restart": "The application has been destroyed and will be restarted soon! ! !", "app_destroyed_about_to_exit": "The application is destroyed and is about to exit! ! !", "sorry_can_not_add_myself_as_a_friend": "Sorry, you can't add yourself as a friend! ! !", "block_a_close_friend": "Block contact", "set_new_avatar": "Set a new avatar", "set_myself_display_name": "Set your nickname", "set_myself_display_name_hint": "A good name can make it easier for your friends to remember you!", "press_speak": "Hold to speak", "the_group_owner_modified_the_group_information": "The group owner modified the group information", "remove_group_chat": "Removed from group chat", "the_message_was_recall": "The message has been withdrawn", "the_length_of_the_silence_is_not_allowed_0": "The muting duration cannot be 0", "the_message_cannot_be_empty": "Message cannot be empty", "the_message_length_cannot_exceed_1000_characters": "Message length cannot exceed 2000 characters", "the_current_conversation_is_exceptional": "The current conversation is abnormal", "the_operation_failed": "Operation failed", "main_logout": "Delete Account", "log_off_title": "Things to note when canceling your account", "log_off_describe": "When you start the logout process, please make sure you know the following precautions: Account logout is an irreversible operation. Once the logout is successful, personal information, avatars, purchase records and other data will be cleared and cannot be reacquired. You will no longer be able to use this method again. Log in with your account and use the services of this App.", "destroy_describe": "Enter the self-destruct password. After self-destruction is triggered, personal information, avatars, purchase records and other data will be cleared and cannot be retrieved. You will no longer be able to log in and use the services of this App with this account.", "add_friend_describe": "Messages and calls are end-to-end encrypted.\nAnyone outside the conversation, even <PERSON><PERSON>,\nNeither can be read or listened to.", "delete_account": "Confirm Delete", "your_account_is_already_logged_in_on_another_device_do_you_want_to_continue": "Your account has been logged in on other devices. Do you want to continue?", "opinion_feedback_tips_im": "If you have any questions that need consultation or resolution, please send an email for feedback", "email": "Email:", "copy_success": "<PERSON><PERSON>d successfully", "empty": "Clear", "secure_keyboard": "secure keyboard", "please_enter_pin": "Please enter pin", "set_up_a_pin": "Set PIN code", "modify_pin": "Modify PIN code", "pin_set": "PIN Settings", "must_be_a_6_digit_password": "Must be a 6-digit password", "pin": "PIN code:", "new_pin": "New PIN Code:", "old_pin": "Old PIN Code:", "self_destructing_password_and_pin_cannot_be_the_same": "The self-destruct password and pin cannot be the same", "pin_and_self_destructing_password_cannot_be_the_same": "The pin and self-destruct password cannot be the same", "pin_cannot_be_empty": "Pin cannot be empty", "dao_create_personal": "Personal DAO creation", "dao_create_company": "Enterprise DAO creation", "idcard_not_empty": "ID number cannot be empty", "idcard": "ID number", "idcard_name": "Name", "idcard_error": "Wrong ID number", "id_name_mismatch": "Name/ID number does not match", "certification_expired": "Certification has expired", "file_transfer_assistant_failed": "My computer login failed, please refresh the QR code and try again!", "file_transfer_assistant_success": "My computer login was successful!", "trying_register": "Trying to register...", "trying_wait": "Trying, please wait...", "register_network_exception": "Network abnormality, registration failed, please try again", "verifying_real_name": "Real name verification in progress...", "wallet_password_prefix": "Wallet password:", "set_wallet_password": "Set Transaction Password", "modify_wallet_password": "Modify Transaction Password", "reset_wallet_password": "Reset Transaction Password", "current_password": "current password", "new_password": "new password", "confirm_password": "Confirm password", "mnemonic_words_separated_by_spaces": "Mnemonic words, separated by spaces", "the_mnemonic_was_entered_incorrectly_please_make_sure_it_is_the_user_mnemonic": "The mnemonic phrase is entered incorrectly, please make sure it is the mnemonic phrase of this user!", "set_pwd": "Set password", "balance": "Balance", "gas_fee_for_this_transaction": "Gas fee for this transaction", "please_enter_payment_password": "Enter PIN", "please_enter_wallet_password": "Please enter wallet password", "fingerprint": "Fingerprint ", "face_id": "Face ID ", "no_pin_set_need_set": "PIN is not set. Cannot export mnemonic. Do you want to set it up?", "no_wallet_password_need_set": "Currently, trading cannot be performed without a trading password set. Do you want to set one?", "no_wallet_password_need_set_to_biometrics": "Currently, the transaction password cannot be set and @biometricsType payment cannot be enabled. Do you want to set it?", "payment": "Pay", "please_verify_the_payment_with_message": "Please verify that you have @biometricsType to pay", "authentication_required": "Authentication required", "authentication_failed_please_try_again": "Authentication failed, please try again", "complaint": "<PERSON><PERSON><PERSON>", "complaint_total_msg": "Total @num messages", "complaint_message": "<PERSON>mplaint message", "reason_for_complaint": "Report a content issue (required)", "complaints": "Complaint content (optional)", "please_input": "Please enter", "submit": "submit", "please_phone_hint": "Please enter mobile phone number", "phone_not_empty": "Mobile phone number cannot be empty", "proxy_settings": "Prepoint setting", "proxy_name_empty": "The prefix name cannot be empty", "proxy_host_empty": "The front point host cannot be empty", "proxy_port_empty": "Port cannot be empty", "proxy_delete_tips": "Are you sure you want to delete this prepoint?", "add_proxy": "Add prepoint", "proxy_detail": "Preposition details", "proxy_name": "Prefix name", "input_proxy_name": "Please enter the prefix name", "proxy_host": "Front point host", "input_proxy_host": "Please enter the pre-point host address", "proxy_port": "port", "input_proxy_port": "Please enter the prefix host port", "publish": "publish", "create_dao": "Create <PERSON><PERSON>", "post_news": "Post updates", "publish_works": "publish works", "please_select_content": "Please select content", "complaint_success": "Report successful", "my_Order": "Order", "create_center": "create", "points_management": "Points", "my_account": "Account", "no_data": "No data yet", "whether_to_clear_th_cache": "Clear cache?", "complaints_times": "Maximum 5 complaints per day", "address_not_empty": "Chain address cannot be empty", "code_not_empty": "Authorization code cannot be empty", "ddcid_not_empty": "ddcid cannot be empty", "add_collection_success": "Collection added successfully", "add_collection_fail": "Failed to add collection", "add_collection": "Add collection", "add_collection_hint": "It supports querying and linking the CyberPanda DAO series collections of the Yuan Exchange through the chain address, ddcid, and authorization code for casting and pledging.", "input_address_hint": "Please enter your chain address", "input_code_hint": "Please enter your authorization code", "input_ddcid_hint": "Please enter your ddcid", "ddc": "DDC", "pledge": "pledge", "not_sensitive_words": "Please do not bring sensitive words", "user_agreement_and_privacy_policy": "We take the protection of your personal information and privacy very seriously. In order to better protect your personal rights and interests, please read carefully before using our products.", "and": "and ", "user_agreement_and_privacy_policy_1": "\n1. Our rules and terms for the collection/storage/use/external provision/protection of your personal information, as well as your user rights and other terms:\n\n2. Agree on our limitations of liability and exemption clauses:\n \n3. Other important terms identified in color or bold. If you have any questions about the above agreement, you can contact us through manual customer service. Your click on 'Agree and Continue' means that you have read and agreed to the entire content of the above agreement. If you agree to the above agreement, please click 'Agree and Continue' to start using our products and services!", "not_agree": "Disagree", "user_agreement_and_privacy_policy_title": "User Agreement and Privacy Policy", "emoji": "expression", "about_ioi": "About the secret world", "data_backup": "Data backup", "data_backup_recover": "Data Backup and Recovery", "other_members_of_the_group_are_notified_when_the_group_announcement_is_changed": "Note: Only group owners and administrators can modify group announcements", "other_copy_word": "<PERSON><PERSON> Mnemonic", "save_to_local": "Save to local", "file_assistant_not_online": "File Assistant is not online, please log in", "input_recover_pwd": "Please enter data recovery password", "backup_recover_hint1": "1. This password is used to encrypt the data backed up this time to ensure data security.", "backup_recover_hint2": "2. When recovering data, you need to enter the recovery password set to verify. If the verification is passes, the data recovery will be completed.", "data_recover": "Data Recovery", "backup_data_enc_fail": "Backup data encryption failed", "backup_file_fail": "Backup file generation failed", "backup_file_send": "Backup file sent", "backup_file_no_exists": "Backup file does not exist", "backup_file_dec_fail": "Backup file decryption failed", "backup_data_dec_fail": "Backup data decryption failed", "not_current_account_backup_data": "It is not the backup data of the current account", "data_backup_success": "Data backup successful", "data_recover_success": "Data recovery successful", "no_friend_backup": "There is no friend data in the current account and no backup is required.", "create_account_r": "Bind mobile phone number", "reacquire": "reacquire", "have_auth_code": "Already have an authorization code", "phone_format_war": "Mobile phone number format is wrong", "note_this_operation_only_backs_up_close_friend_data": "This operation only backs up contact data.", "no_messages_can_be_forwarded": "No messages can be forwarded", "feedback": "Problem feedback", "help_center": "Help Center", "group_num_max": "The number of group members has reached the upper limit", "mnemonic_export": "Export Mnemonic", "register_mnemonic_tips": "The mnemonic phrase will be your identity document, please be sure to check whether it has been recorded!", "year_of_rabbit_limit": "Year of the Rabbit customization", "year_of_rabbit_emoji": "Year of the Rabbit emoji", "db_open_fail": "The database is opening abnormally and the program is about to exit.", "the_other_party_is_not_your_communication": "The other party is not a friend in your address book, beware of telecom fraud", "blocked": "Blacklisted", "after_being_added_to_the_blacklist": "After being added to the blacklist, you will no longer be able to receive messages from the other party.", "block": "Add to Blacklist", "join_now": "Participate now", "share_promotion": "Share and Promote", "share_poster": "Share poster", "my_recommender": "<PERSON> Referrer", "invited_people": "Invited People:", "please_enter_the_mailing_address_of_your_recommender_oversea": "Please enter the invitation code", "please_enter_the_mailing_address_of_your_recommender": "Please enter the mailing address of your referrer", "invite_now": "Invite Now", "invitation_list": "Invitation List", "input_recommender": "Please enter your referrer", "set_share_invite_text": "Earn money with your friends, and enjoy rebates when you invite new people", "share_invite_text": "If you invite successfully, you will be rewarded with points. The rewards are unlimited. The more you invite, the more you will get.", "bind_recommender_success": "Referrer bound successfully", "recommender_address_can_not_empty": "Referrer address cannot be empty", "invite_friends_to_get_rebates": "Invite friends and get rebates", "bind_recommender": "Bind Referrer", "share_pop_text": "Fill in the invitation code, activate computing power contribution and Token withdrawal permissions, and enjoy permanent incentives.", "my_invite_code": "My Invitation Code", "pornographic_vulgar": "Pornographic and vulgar", "bloody_violence": "bloody violence", "false_promotional_links": "False promotional links", "malicious_fraud": "malicious fraud", "disgusting_content": "The content is disturbing", "other": "other", "server_error_retry": "Server exception, please try again", "payment_amount": "Transaction amount", "copy_link": "Copy link", "up_to_people": "Up to @number people", "invitation_sent": "Invitation sent", "chat_mining": "My K coins", "detail": "Details", "total_income": "total revenue", "withdraw": "Go to withdraw cash", "invite_new": "Recruit", "task": "Task motivation", "choose_the_number_of_periods": "Select period", "effective_time_period": "Valid time period", "contribution_value_of_the_day": "Contribution value of the day +@num", "pt_win_3t": "Participate in chat to get K token rewards", "my_3t": "My K coins", "view_now": "View now", "set": "Settings", "data_overview": "Data overview", "start_and_end_time": "Start and end time:", "the_number_of_participants": "Number of participants", "mining_points": "Current income", "total_ore": "Total amount of Tokens in this period", "chat_points": "Current computing power contribution", "past_data": "Past period data", "main_mine": "Me", "view_original_photo": "View original image", "official_name": "<PERSON><PERSON>", "introduce": "introduce", "official_introduce": "Linksay is based on the decentralized blockchain protocol, the DID identity system on the native WEB3 chain, and builds a new generation of decentralized, private and secure new social communication software.", "emoji_from_mit": "from microsoft", "emoji_down_failed": "Failed to download @emoji resource", "emoji_decode_failed": "Failed to decompress @emoji resource", "emoji_load_success": "@emoji loaded successfully", "splash_bottom": "Supported by ©K Net Lab Sdn.Bhd", "translate_desc": "You are using the translation function of a third-party software. Do you agree to send information to the third-party software for translation?", "crop": "Crop", "brush": "brush", "text": "Word", "add_text": "Add text", "size": "font size", "color": "color", "delete_text": "Delete text", "delete_message": "Are you sure you want to delete this text?", "loading_not_more": "No more data", "loading_failed": "Loading Failed", "transferInSuccess": "Transfer successful", "network_fee": "Network Fee", "receiver": "Receiver", "sender": "Sender", "quantity": "Quantity", "manage": "Manage", "delete_wallet_hint": "Are you sure to delete this wallet?", "send_nft": "send NFT", "network_lag": "Network lag", "tokenAddress": "Contract address", "token_information": "Token information", "executing_contract": "Executing contract", "balance_data_has_not_been_queried": "Balance data has not been queried, please confirm whether the current connection network is correct.", "wallet_name_cannot_be_empty": "Wallet name cannot be empty", "import_private_tag": "Import", "switch_network_success": "Network switch successful!", "work": "Work (@num)", "desc": "Description", "contract_address": "Contract address", "token_standard": "Token standard", "total_number_of_goods": "Total number of goods", "test": "test", "send": "Send", "receive": "Receive", "scan": "<PERSON><PERSON>", "tokens": "Tokens", "nfts": "NFTs", "transferNetwork": "transfer network", "paymentAddress": "Payment Address", "inputPaymentAddress": "Please enter payment address", "transferAmount": "Transfer Amount", "available": "available", "remark": "Remark", "inputMemo": "Please enter memo", "minerFee": "Miner <PERSON>", "generateWallet": "Generate wallet", "addACustomNetwork": "Add a custom network", "transferAccounts": "Transfer", "noTransactionRecord": "No transaction record", "transactionIn": "Transfer In", "transactionOut": "Transfer Out", "confirmW": "Confirm", "addToken": "Add token", "searchToken": "Search coin name or contract", "popularToken": "Popular currencies", "receiveMoney": "Receive Money", "receiveMoneyAddress": "Receive money address", "copy": "Copy", "share": "Share", "receiveMoneyHint": "Only transfer ETH/ERC20 related assets to this address", "receiveTrxMoneyHint": "Only transfer TRX/TRC20 related assets to this address", "rpcAddress": "RPC Address", "optional": "Optional", "networkName": "Network Name", "chainID": "Chain ID", "tokenSymbol": "Token Symbol", "blockchainBrowser": "Block<PERSON>in Browser", "walletList": "Wallet List", "net": "Net", "copiedToClipboard": "Copied to clipboard", "argsNotEmpty": "{arg} cannot be empty, please fill in", "argsExisted": "{arg} existed", "noRecords": "No Records", "exceeds_max_balance": "Exceeded the upper limit of transferable quantity", "send_cancel": "send cancel", "network_abnormal_send_failed": "Contract execution exception, sending failed", "send_success": "successfully sent", "to": "To", "transferring": "Transferring", "transferSuccess": "Transferring Success", "transferFailed": "Transferring Failed", "importWallet": "Import Wallet", "importPrivateKey": "Import PrivateKey", "pleaseEnterThePrivateKeyString": "Please enter the private key string", "importWalletHint": "Tips: \nThe imported account can be viewed in the wallet list, but cannot be retrieved through the mnemonic recovery", "confirmImport": "Confirm Import", "rpc_url_user": "This URL is currently used by the @arg network", "chanid_different": "RPC endpoint uses a different chain ID for the chain: @arg", "rpc_correct": "Unable to get chain id. Is your RPC URL correct?", "token_network": "Token network", "token_address": "Token address", "input_token_address": "Please enter token address", "token_symbol": "Token symbol", "input_token_symbol": "Please enter the token symbol", "token_is_add": "Token has been added", "token_use_diff_symbol": "token uses different symbols on the chain：@arg", "editCustomNetwork": "Edit custom network", "delete": "Delete", "hint": "Hint", "delete_net_hint": "Are you sure you want to delete this network and its associated data?", "alreadyExistsNoNeedToImportAgain": " already exists, no need to import again", "delete_token_hint": "Are you sure to delete this token?", "signature_auth_title": "Signature authorization", "connect_network": "Link network", "signature_switch_network_title": "Allow this DAPP to switch networks？", "authorized_address": "Authorized address", "remember_it": "Remember it, from the right to log in", "authorized_hint_0": "This operation will switch the selected network in the wallet to the network specified by the DAPP, and authorize the DAPP to obtain the following permissions:", "authorized_hint_1": "After authorization, the application will obtain the following permissions", "authorized_hint_2": "·Query information on your chain", "authorized_hint_3": "·Request transaction authorization from you", "signature": "Signature", "confirm_delete": "Confirm Delete", "authorized_network": "Authorized network", "authorized_items": "Authorized items", "dapp_manage": "DAPP Management", "walletDetail": "Wallet Detail", "modifyWalletName": "Modify Wallet Name", "myWallet": "My Wallet", "viewPrivateKey": "View Private Key", "DAPPManage": "DAPP Manage", "privateKey": "Private key", "viewPrivateKeyNoSetPwdHint": "For the safety of your assets, please set the wallet password before checking", "importPrivateKeyError": "The private key was entered incorrectly, please check carefully and re-enter", "db_open_exce": "Database open exception", "current_receiving_network": "Current receiving network", "approximately": "Approximately ", "token_address_invalid": "Token address invalid", "signal_data": "Signature content", "successfully_modified": "Successfully modified", "sending_currency": "Sending currency", "play_address_invalid": "The payment address is not the same as the wallet address.", "address_invalid": "address invalid", "transaction_details": "Transaction Details", "transaction_exception_again": "Transaction exception, please try again!", "tools": "Tool", "share_contact": "Share to Contact", "change_transferNetwork": "Choose transfer network", "change_connect_address": "Select payment address", "no_payment_information_for_him_yet": "There's no payment information for him yet!", "intercambio_flash": "<PERSON><PERSON><PERSON>", "wallet_update": "Wallet is being upgraded", "receiveMoneyHint_K": "Only transfer @name related assets to this address", "enter_url": "enter URL", "new_tab": "New tab", "new_incognito_tab": "New Tab Page", "favorites": "Favorites", "history": "History", "reload": "Reload", "clean": "Clean", "please_enter_words_or_url": "Please enter Words or URL", "recently": "Recently", "no_Favorites": "No Favorites", "no_history": "No History", "hot": "Hot", "test2": "How are you?", "please_update_your_default_nickname": "Please Update Your Default Nickname", "please_restart_app": "Please restart the app", "activate_wallet": "Activate Wallet", "no_pin_set_activate_wallet_need_set": "PIN is not set. Cannot activate/close wallet. Do you want to set it up?", "mnemonic_verify_failed_please_check": "Mnemonic verify failed, please check again!", "backup_mnemonic": "Backup Mnemonic", "verify_backup": "Verify Backup", "verify_mnemonic_description": "Click the words to arrange them in the correct order to verify that your backed-up mnemonic is correct.", "mnemonic_verify_success": "Mnemonic verify success!", "close_wallet_confirmation_desc": "Are you sure you want to close the wallet?", "activate_wallet_confirmation_desc": "Wallet is not activate. Do you want to set it up?", "main_my_channel": "My Channel", "image_save_to_ablum": "The image has been successfully saved to the album", "image_save_to_ablum_failed": "Failed to save image to album", "pro_editor_various_loadingDialogMsg": "Please wait...", "pro_editor_various_closeEditorWarningMessage": "Are you sure you want to close the Image Editor? Your changes will not be saved.", "pro_editor_various_closeEditorWarningTitle": "Close Image Editor?", "pro_editor_various_closeEditorWarningConfirmBtn": "OK", "pro_editor_various_closeEditorWarningCancelBtn": "Cancel", "pro_editor_paint_bottomNavigationBarText": "Paint", "pro_editor_paint_freestyle": "Freestyle", "pro_editor_paint_arrow": "Arrow", "pro_editor_paint_line": "Line", "pro_editor_paint_rectangle": "Rectangle", "pro_editor_paint_circle": "Circle", "pro_editor_paint_dashLine": "Dash line", "pro_editor_paint_lineWidth": "Line width", "pro_editor_paint_toggleFill": "Toggle fill", "pro_editor_paint_undo": "Undo", "pro_editor_paint_redo": "Redo", "pro_editor_paint_done": "Done", "pro_editor_paint_back": "Back", "pro_editor_paint_smallScreenMoreTooltip": "More", "pro_editor_text_inputHintText": "Enter text", "pro_editor_text_bottomNavigationBarText": "Text", "pro_editor_text_back": "Back", "pro_editor_text_done": "Done", "pro_editor_text_textAlign": "Align text", "pro_editor_text_fontScale": "Font scale", "pro_editor_text_backgroundMode": "Background mode", "pro_editor_text_smallScreenMoreTooltip": "More", "pro_editor_crop_rotate_bottomNavigationBarText": "Crop/ Rotate", "pro_editor_crop_rotate_rotate": "Rotate", "pro_editor_crop_rotate_ratio": "<PERSON><PERSON>", "pro_editor_crop_rotate_back": "Back", "pro_editor_crop_rotate_done": "Done", "pro_editor_crop_rotate_cancel": "Cancel", "pro_editor_crop_rotate_prepareImageDialogMsg": "Please wait", "pro_editor_crop_rotate_applyChangesDialogMsg": "Please wait", "pro_editor_crop_rotate_smallScreenMoreTooltip": "More", "pro_editor_crop_rotate_reset": "Reset", "pro_editor_filter_bottomNavigationBarText": "Filter", "pro_editor_filter_applyFilterDialogMsg": "Filter is being applied.", "pro_editor_filter_back": "Back", "pro_editor_filter_done": "Done", "pro_editor_blur_applyBlurDialogMsg": "Blur is being applied.", "pro_editor_blur_bottomNavigationBarText": "Blur", "pro_editor_blur_back": "Back", "pro_editor_blur_done": "Done", "pro_editor_emoji_bottomNavigationBarText": "<PERSON><PERSON><PERSON>", "pro_editor_emoji_search": "Search", "pro_editor_emoji_noRecents": "No recent emojis", "pro_editor_sticker_bottomNavigationBarText": "<PERSON>er", "pro_editor_cancel": "Cancel", "pro_editor_undo": "Undo", "pro_editor_redo": "Redo", "pro_editor_done": "Done", "pro_editor_remove": "Remove", "pro_editor_doneLoadingMsg": "Please wait", "building_your_digital_fortress": "LinkSay - Building Your Digital Fortress", "comprehensive_protection_for_your_peace_of_mind_online": "Comprehensive Protection for Your Peace of Mind Online", "your_privacy_our_mission": "Your Privacy, Our Mission", "take_control_of_your_personal_data_with_linksay": "Take Control of Your Personal Data with LinkSay", "unleash_digital_freedom_enjoy_boundless_experiences": "Unleash Digital Freedom, <PERSON><PERSON>s", "breaking_barries_unlocking_possibilities_with_linksay": "Breaking Barriers, Unlocking Possibilities with LinkSay", "channel_recommed": "Recommendation", "channel_hot": "Popular", "channel_eco": "Ecology", "channel_tech": "Technology", "channel_fund": "Fund", "channel_other": "Others", "group_owner_transfer": "Group Owner Transfer", "group_owner_transfer_confirm": "Confirm Transfer of Group Owner", "group_owner_transfer_desc": "You are about to transfer the ownership of the group. Once the transfer is complete, all your administrative privileges in this group will be transferred to the new owner’s account.", "group_owner_transfer_success": "The group owner has been successfully transferred", "group_owner_transfer_failed": "Group Owner Transfer Failed", "new_group_owner": "became the new group owner", "channel_moment_title": "Moments", "channel_moment_desc": "Announcement", "channel_moment_desc_empty": "Op<PERSON>! We do not have an introduction yet!", "channel_moment_empty": "Stay tuned for more updates!", "view_more_comments": "View More Comments", "create_post": "Create Post", "see_more": "See More", "post": "Post", "create_post_hint": "What do you like to share today?", "member": "Member", "maximum_nine_images": "Maximum 9 images only.", "upload_success": "Upload Success", "update": "Update", "delete_success": "Delete Success", "delete_post_confirmation_desc": "Are you sure you want to delete this post?", "upload_fail": "Upload Failed", "delete_fail": "Delete Failed", "update_description_dialog_title": "Update Description", "update_description_dialog_hint": "Enter your description here", "update_fail": "Update Failed", "update_success": "Update Success", "moment_comment_hint": "Write a comment here...", "success": "Success", "failed": "Failed", "delete_comment_confirmation_desc": "Are you sure you want to delete this comment?", "smart_refresh_no_data": "No more data", "smart_refresh_loading": "Loading", "smart_refresh_can_loading": "Release to load more", "smart_refresh_idle": "Pull up load", "smart_refresh_fail": "Load failed", "exceed_file_size": "images have exceeded max file size.", "select_image": "Select Image", "select_video": "Select Video", "video_exceed_file_size": "Video has exceeded max file size.", "uploading": "Uploading...", "processing": "Processing...", "video_processing_failed": "Video processing failed. Please try again with a different video.", "image_upload_failed": "Image upload failed. Please try again.", "video_upload_failed": "Video upload failed. Please check your network and try again.", "video_url": "Video Url", "video_not_support": "The video is not supported for playback", "share_now": "Share Now", "new_update_available": "New Update is Available!", "new_update_available_desc": "Please update our app for an improved experience!\nThis version is no longer supported.", "confirm_language": "Confirm Language", "log_off_title_new": "What you need to know before leaving us", "i_prefer_to_stay": "I prefer to stay", "log_off_desc_1_1": "Before you start the logoff process, please ensure that you are", "log_off_desc_1_2": "of the following", "log_off_desc_1_keyword_1": "aware", "log_off_desc_2_1": "Account logoff is", "log_off_desc_2_keyword_1": "irreversible.", "log_off_desc_3_1": "Once successfully logged off, personal data, avatars, purchase records, etc will be", "log_off_desc_3_2": "and", "log_off_desc_3_keyword_1": "cleared", "log_off_desc_3_keyword_2": "cannot be retrieved.", "log_off_desc_4_1": "You will", "log_off_desc_4_2": "be able to log in and use the service of this App with this account.", "log_off_desc_4_keyword_1": "no longer", "copy_invitation_code": "Copy Invitation Code", "backup_data_title": "About data backup and recovery", "backup_data_recovery_password_title": "What is data recovery password", "note_this_operation_only_backs_up_close_friend_data_label": "Note:", "all_terms": " all terms within, in particular:", "thank_you_for_choosing_linksay_app": "Thank you for choosing Linksay APP!", "export_mnemonic_step_1": "Create", "export_mnemonic_step_2": "Backup", "node_switch_desc_1_1": "Before you start the node switching process, please make sure you are", "node_switch_desc_1_2": "of the following precautions:", "node_switch_desc_1_keyword_1": "aware", "node_switch_desc_2_1": "Node switching is an", "node_switch_desc_2_2": "operation.", "node_switch_desc_2_keyword_1": "irreversible", "node_switch_desc_3_1": "Once the node is successfully switched, the individual chat and private group chat data under the original node will be", "node_switch_desc_3_keyword_1": "deleted.", "node_switch_desc_4_1": "Please prepare", "node_switch_desc_4_keyword_1": "data backup.", "node_switch_title": "What you need to know before switching node", "node_switching_title": "We are switching to...", "node_switching_desc": "Node switching in progres.\nPlease stay with us while we are shifting.", "label_address": "Address", "label_text": "Text", "label_audio": "Audio", "label_meeting": "Meeting", "clear_now": "Clear Now", "delete_contact_desc": "Are you sure you want to delete this contact?", "token": "Token", "money_packet": "Red Packet", "money_packet_details": "Red Packet DAO", "equal": "Equal", "random": "Random", "amount_per_packet": "Packet Amount", "money_packet_type": "Red Packet Type", "max": "Max", "service_fee": "Service Fee", "what_is_money_packet": "What Is Red Packet", "best_wishes": "Best Wishes", "address_copied": "Address Copied", "total_amount": "Total Amount", "amount_of_packet": "Quantity", "of_money_packet": " Of Red Packet", "congratulations": "Congratulations", "you_received": "You Received", "open": "Open Now", "try_again_later": "Sorry, all gone!", "money_packet_title": "Wish you good fortune and prosperity", "has_been_snatched": "has been snatched", "total": "Total", "max_number_sent": "The maximum number of red packets that can be sent is", "token_balance_not_enough": "Token balance is not enough", "snatch_red_envelopes": "Snatch Now", "swap": "<PERSON><PERSON><PERSON>", "swap_from": "From", "swap_to": "To", "swap_fee": "Fee", "swap_history": "Swap History", "swap_more": "More", "swap_select_token": "Select Token", "swap_search_token": "Search Token", "swap_no_related_token": "No related token", "swap_same_token": "Cannot select the same token on the same chain", "swap_no_more": "No more data", "no_transaction_history": "No transaction history", "swap_success": "Success", "swap_fail": "Fail", "gas_fee_not_enough": "Gas fee not enough", "packet_from_whom": "Red Packet from @name", "meeting": "Meeting", "host": "Host", "upcomings": "Upcomings", "past": "Past", "create_meeting_2_cap": "Create Meeting", "upcoming_meeting_empty": "You have no Upcoming meetings", "past_meeting_empty": "You have no Past meetings", "date": "Date", "start_time": "Start Time", "end_time": "End Time", "passcode": "Passcode", "meeting_link": "Meeting Link", "start_meeting": "Start Meeting", "join": "Join", "meeting_detail": "Meeting Details", "recorded_meeting": "Recorded Meeting", "view": "View", "here": "Here", "create_new_meeting": "Create New Meeting", "meeting_title": "Meeting Title", "meeting_description": "Meeting Description", "time_1_cap": "Time", "submit_1_cap": "Submit", "passcode_desc": "Set a passcode for meeting?\nOnly individuals with passcode can enter the meeting.", "hint_title": "Title", "hint_description": "Description", "meeting_time_invalid": "Please select a valid time.", "meeting_title_empty": "Title should not be empty.", "meeting_date_required": "Date is required.", "meeting_start_time_required": "Start time is required.", "meeting_end_time_required": "End time is required.", "please_enter_passcode": "Please enter passcode.", "meeting_passcode_limit": "Passcode required @limit characters.", "start_time_less_than_10_err_msg": "Start time cannot be less than 10 minutes from now.", "any_user_can_open_meeting": "Any user can open meeting?\nIf any member logs in and leaves immediately, causing the number of people in the conference to become 0, the conference will be automatically closed.", "meeting_host_owner": "You", "edit_meeting": "Edit Meeting", "cancel_meeting": "Cancel Meeting", "yesterday_1_cap": "Yesterday", "today_1_cap": "Today", "tomorrow_1_cap": "Tomorrow", "share_meeting_msg_1": "@sharer invites you to join the upcoming online conference!", "share_meeting_msg_2": "\n\n🖥 Subject: @title", "share_meeting_msg_3": "\n📅 Date: @date", "share_meeting_msg_4": "\n🕒 Time: @start_time", "share_meeting_msg_5": "\n🔗 URL: @url", "share_meeting_msg_6": "\n🔑 Passcode：@passcode", "share_meeting_msg_7": "\n\nAgenda:\n@desc", "any_user_can_open_meeting_indicator": "Any user can open meeting.", "cancel_meeting_confirmation": "Are you sure you want to cancel this meeting?", "you_have_been_invited_to_join": "You have been invited to join", "click_to_join": "Click to Join", "generate_meeting_passcode": "Generate Passcode", "meeting_created_by": "Meeting created by @host", "host_meeting_url": "Host's Meeting Url", "host_meeting_url_note": "** Please note that this is a host's meeting url, do not share with others.", "meeting_date_invalid": "Please select a valid date.", "invitation": "Invitation", "mission": "MISSION", "download_and_explore_now": "Download & Explore Now", "label_video_call": "Video", "btn_invitation_code": "Invitation Code", "btn_download_code": "Download Code", "download": "Download", "select_type": "Select Type", "add_token_title": "Add <PERSON>", "add_token_desc": "Adding a token to a wallet lets you view and manage it by importing its contract address.", "add_nft_title": "Add NFT", "add_nft_desc": "Adding an NFT to a wallet enables viewing and management via its contract address.", "add_customized_nft_title": "Couldn't find something in your mind?", "add_customized_nft_desc": "Add your own customised NFT", "hot_nft_collections": "HOT NFT Collections", "nft_contract_title": "NFT Contract", "nft_contract_hint": "Enter contract address or scan", "contract_address_empty": "Contract address is empty", "add_customized_nft": "Add Customised NFT", "add_1_cap": "Add", "assets_1_cap": "Assets", "add_asset_title": "Add <PERSON>", "add_asset_desc": "Please enter the Token ID you want to add. Separate multiple tokens with a comma.", "token_id": "Token ID", "token_id_required": "Token ID is required.", "info": "Info", "contract": "Contract", "nft_standard": "NFT Standard", "chain": "Chain", "amount_1_cap": "Amount", "transfer_1_cap": "Transfer", "transfer_nft": "Transfer NFT", "recipient_address_label": "Recipient Address", "recipient_address_hint": "Please enter the recipient address", "send_nft_1_cap": "Send NFT", "gas_fee": "Gas Fee", "estimated": "Estimated", "nft_asset_search_bar_hint": "Name or ID within collection", "search": "Search", "add_failed": "Add Failed", "nft_already_exists": "NFT already exists", "nft_token_id_incorrect": "Token id invalid", "delete_token": "Delete Token", "delete_token_detail": "Are you sure you want to delete this token?", "goodnet_auth_fail": "Account Binding Failed", "goodnet_auth_success": "Account Binding Successful", "create_web3_dao": "Create Web3 DAO", "name_of_token": "Name of Token", "contract_address_1_cap": "Contract Address", "minimum_number_of_token": "Minimum number of Token", "introduction_of_dao": "Introduction of DAO", "introduction_of_dao_hint": "Tell us more about DAO", "create_dao_intro": "By holding a certain amount of the corresponding tokens, one can join the DAO organization.", "required": "required", "create_dao_failed": "Create DAO Failed", "dao_exist_btm_sheet_title": "You've Joined this DAO", "dao_exist_btm_sheet_content": "You are an existing member in this DAO.", "join_now_1": "Join Now", "try_again": "Try Again", "trade_now": "Trade Now", "dao_not_meet_condition_hint": "Your assets do not meet the minimum holding requirements set by the DAO organization: @token > @condition. Please meet the requirements and try again.", "role_management": "Role Management", "add_new": "Add New", "add_new_role": "Add New Role", "role": "Role", "default_color": "Default Color", "assign_to": "Assign To", "assign_role_to": "Assign Role To", "who_is_in_charge": "Who's In Charge", "role_assigned": "Role Assigned", "currently_assigned_to": "Currently Assigned To", "other_members": "Other Members", "search_contact": "Search Contact", "poll": "Poll", "create_poll": "Create Poll", "general_vote": "General <PERSON><PERSON>", "proposal_vote": "Proposal Vote", "dao_vote": "DAO Vote", "what_is_going_on": "What's Going On?", "vote_list_tab_all": "All", "vote_list_tab_ongoing": "Ongoing", "vote_list_tab_expired": "Expired", "vote_action_btn_voted": "Voted", "vote_action_btn_vote_now": "Vote Now", "vote_action_btn_expired": "Expired", "vote_action_btn_participate": "Apply", "vote_action_btn_participated": "Applied", "poll_is_empty": "Poll is empty", "create_general_vote": "Create General <PERSON>", "create_proposal_vote": "Create Proposal Vote", "create_dao_vote": "Create DAO Vote", "general_vote_title": "General Vote Title", "proposal_vote_title": "Proposal Vote Title", "dao_vote_title": "DAO Vote Title", "description_optional": "Description(Optional)", "create_1_cap": "Create", "hint_what_is_this_vote_about": "What is this vote about", "option_up_to": "Options(Up to @limit)", "voting_method": "Voting Method", "voting_method_option_1": "One person, One vote", "voting_method_option_2": "Shareholding Voting", "duration": "Duration", "duration_option_hour": "@num Hour", "duration_option_day": "@num Day", "duration_option_days": "@num Days", "title_empty": "Title should not be empty.", "dao_vote_title_minimum_joining_requirement": "Minimum Joining Requirement", "days_1_cap": "Days", "hour_1_cap": "Hour", "min_1_cap": "Min", "sec_1_cap": "Sec", "submit_vote": "Submit <PERSON><PERSON>", "vote_detail_title": "Detail", "vote_results": "Vote Results", "this_voting": "This Voting", "valid_votes": "<PERSON><PERSON>", "valid_cap": "VALID", "invalid_cap": "INVALID", "vote_validity_reason": "Has exceeded the minimum requirement of @requirement for this vote.", "vote_validity_reason_2": "Has not exceed the minimum requirement of @requirement for this vote.", "vote_validity_reason_3": "A tie vote renders the result invalid.", "you_voted_for": "You voted for \"@result\"", "vote_result": "Vote Result", "leader_vote": "Leader <PERSON><PERSON>", "view_more": "View More", "view_less": "View Less", "leader_poll_desc": "Before applying for the position of Leader, please be sure to carefully read and understand the rights and responsibilities associated with the role of Leader, to ensure that you can still make a prudent decision about being able to dedicate the time required to fulfill the corresponding duties of the Leader.", "leader_poll_desc_duty": "Duty:\n1. The Leader has the duty to maintain the order of the DAO and create a clean, comfortable communication environment.\n2. The Leader has the duty to promptly update and upload the DAO's milestone events and articles.\n3. The Leader has the duty to promptly establish sub-DAO chat groups and handle all matters that are beneficial to the development of the DAO community.", "leader_poll_desc_right": "Rights:\n1. The Leader has the right to delete chat records that are unrelated to the DAO.\n2. The Leader has the right to block addresses that intentionally disrupt the DAO environment.\n3. The Leader has the right to manage the manager role.", "leader_poll_desc_regulation": "Regulations:\n1. The creator is the first Leader.\n2. On the first day of every month at 00:00 (Greenwich Mean Time), the system automatically initiates an election vote. Users can apply for the Leader position, with the application period lasting 24 hours and the voting period lasting 48 hours.\n3. Any address holding tokens can apply to participate in the vote, and the system will distribute the corresponding number of votes based on the current token holdings.\n4. The person with the highest number of votes becomes the new Leader.", "leader_poll_title": "Who's the Next Leader", "candidates": "Candidates", "apply_to_be_the_next_leader": "Apply to be the NEXT LEADER", "participated_candidates": "Participated Candidates", "next_leader": "Next Leader", "leaderboard": "Leaderboard", "time_left": "Time Left", "at_least_2_options_are_required": "At least 2 options are required.", "token_not_added": "Please make sure the token is added to your wallet.", "join_failed": "Join Failed", "leader": "Leader", "manager": "Manager", "delete_role": "Delete Role", "delete_role_detail": "Are you sure you want to delete this role?", "apply_dao_leader_confirmation_title": "Apply as Leader", "apply_dao_leader_confirmation_detail": "Are you sure you want to apply as Leader?", "dao_vote_confirmation_title": "Vote Confirmation", "dao_vote_confirmation_detail": "Are you sure you want to vote for this option?", "dao_vote_option_invalid": "The option must be a number", "maximum_managers_hint": "You can select up to 5 managers at most", "the_poll_info": "[Poll]", "dao_poll_chat_bubble_status_ongoing": "On Going", "dao_poll_chat_bubble_status_other": "No Status", "tip_of_does_not_meet_min_requirement_of_dao": "Token balance does not meet minimum requirement of DAO. Temporarily removed from the group.", "dao_vote_option_non_zero": "Option must be non-zero", "load_failed_tip": "<PERSON>ad failed, please try again later!", "insufficient_number_of_tokens": "Insufficient number of tokens", "goodnet_already_auth": "Already Connected", "share_image": "Share Image", "share_link": "Share Link", "enter_your_message_here": "Enter your message here...", "memo": "Memo", "escape": "Escape", "on_chain_data": "Data To Chain", "on_chain_data_hint": "Data will be written to the blockchain", "choose_address": "<PERSON><PERSON> Address", "recent_transfers": "Recent Transfers", "wallet_list": "Wallet List", "current": "Current", "address_book": "Address Book", "tap_to_add": "Tap to Add", "add_address": "Add Address", "set_address_name": "Set Name", "hint_input_address": "Please enter address", "hint_input_name": "Please enter name", "address_cannot_be_empty": "Address cannot be empty", "name_cannot_be_empty": "Name cannot be empty", "address_book_already_exist": "Address already exists in address book", "added_successfully": "Added successfully.", "loading_ads": "Loading Ads...", "ads_load_failed": "Ads load failed, please try again later.", "done": "Done", "drag_on_the_right_to_sort_1": "Drag", "drag_on_the_right_to_sort_2": "on the right to sort", "USD": "United States Dollar", "MYR": "Malaysian Ringgit", "SGD": "Singapore Dollar", "CNY": "Chinese Yuan", "HKD": "Hong Kong Dollar", "currency_unit": "Currency Unit", "detail_2": "Details", "not_activate_wallet_yet": "Wallet not activated yet", "chart_load_failed": "Chart load failed, please try again later.", "log_off_desc_5_1": "Please ", "log_off_desc_5_2": "you have securely ", "log_off_desc_5_3": "your ", "log_off_desc_5_keyword_1": "make sure ", "log_off_desc_5_keyword_2": "backed up ", "log_off_desc_5_keyword_3": "mnemonic phrase."}