
import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/widgets/app_bar_cus.dart';
import 'package:flutter_metatel/core/task/time_task.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:flutter_metatel/core/utils/util.dart';
import 'package:flutter_metatel/core/values/colors.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../core/languages/l.dart';
import '../../../../r.dart';
import '../../../widgets/data_time_picker_bottom_scroll/custom_picker.dart';
import '../../../widgets/data_time_picker_bottom_scroll/flutter_datetime_picker.dart';
import '../../../widgets/data_time_picker_bottom_scroll/i18n_model.dart';
import '../../../widgets/divider_cus.dart';
import '../../../widgets/radio_list_title_cus.dart';

///界面返回CusTimePickerFinalModel对象
class MemberMuteTalkPage extends StatefulWidget {
  const MemberMuteTalkPage({Key? key, this.appBarTitle,this.title}) : super(key: key);
  final String? appBarTitle; ///appbar标题
  final String? title; ///提示标题

  @override
  State<StatefulWidget> createState() => _MemberMuteTalkPageState();
}

class _MemberMuteTalkPageState extends State<MemberMuteTalkPage> {
  String groupValue = "10 ${L.minutes.tr}";

  ///默认禁言时间 毫秒
  final CusTimePickerFinalModel defaultMuteTime = CusTimePickerFinalModel(minute: 10);
  final String valueTenMinute = "10 ${L.minutes.tr}";
  final String valueOneHour = "1 ${L.hour.tr}";
  final String valueTwelveHour = "12 ${L.hour.tr}";
  final String valueOneDay = "1 ${L.day.tr}";
  final String valueCus = L.customize.tr;
  String? timeCus;

  ///禁言时间 毫秒
  CusTimePickerFinalModel? resultData;

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBarCommon().build(context, title: widget.appBarTitle??L.mute_time.tr),
      body: Container(
        color: Theme.of(context).primaryColor,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            const DividerCus(),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.only(top: 15, left: 10, bottom: 10).r,
              child: Text(
                widget.title??L.choose_the_duration_of_the_mute.tr,
                style: TextStyle(color: AppColors.txtColor, fontSize: 12.sp),
              ),
            ),
            RadioListTitleCus<dynamic>(
              title: valueOneDay,
              value: valueOneDay,
              groupValue: groupValue,
              onChanged: (value) {
                AppLogger.d("_MemberMuteTalkPageState RadioListTitleCus");
                setState(() {
                  groupValue = value as String;
                  resultData = CusTimePickerFinalModel(day: 1);
                });
              },
            ),
            RadioListTitleCus<dynamic>(
              title: valueTwelveHour,
              value: valueTwelveHour,
              groupValue: groupValue,
              onChanged: (value) {
                setState(() {
                  groupValue = value as String;
                  resultData = CusTimePickerFinalModel(hour: 12);
                });
              },
            ),
            RadioListTitleCus<dynamic>(
              title: valueOneHour,
              value: valueOneHour,
              groupValue: groupValue,
              onChanged: (value) {
                setState(() {
                  groupValue = value as String;
                  resultData = CusTimePickerFinalModel(hour: 1);
                });
              },
            ),
            RadioListTitleCus<dynamic>(
              title: valueTenMinute,
              value: valueTenMinute,
              groupValue: groupValue,
              onChanged: (value) {
                setState(() {
                  groupValue = value as String;
                  resultData = CusTimePickerFinalModel(minute: 10);
                });
              },
            ),
            RadioListTitleCus<dynamic>(
              title: valueCus,
              value: valueCus,
              groupValue: groupValue,
              trailing: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Text(timeCus ?? ""),
                  SizedBox(
                    width: 15.w,
                  ),
                  Image.asset(
                    R.nextArrowGrey,
                    width: 6.w,
                    height: 10.h,
                  ),
                ],
              ),
              onChanged: (value) {
                DatePicker.showPicker(context, showTitleActions: true,
                    onConfirm: (date, jsonStr) {
                      setState(() {
                        CusTimePickerFinalModel model = CusTimePickerFinalModel.fromJsonStr(jsonStr);
                        groupValue = value as String;
                        timeCus = model.toString().isEmpty?"0":model.toString();
                        resultData = model;
                        AppLogger.d(
                            'confirm $date resultStr==$jsonStr  resultData==$resultData');
                      });
                    },
                    pickerModel: CustomPicker(currentTime: TimeTask.instance.getNowDateTime()),
                    locale: currentLanguageIsSimpleChinese()
                        ? LocaleType.zh
                        : LocaleType.en);
              },
            ),
            const Spacer(),
            ElevatedButton(
              onPressed: () {
                if(timeCus!=null&&timeCus=="0"){
                  toast(L.the_length_of_the_silence_is_not_allowed_0.tr);
                }else{
                  CusTimePickerFinalModel result = resultData == null
                      ? defaultMuteTime
                      :resultData!;
                  Get.back(result: result);
                }

              },
              child: Text(
                L.confirm.tr,
              ),
            ),
            const Spacer(),
          ],
        ),
      ),
    );
  }

}
