import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/data/providers/db/database.dart';
import 'package:flutter_metatel/core/languages/l.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:flutter_metatel/r.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../data/enums/enum.dart';
import '../../../data/services/channel_service.dart';
import '../../../widgets/mavatar_circle_avatar.dart';
import '../details/dao_details_controller.dart';
import '../details/group_details_base_controller.dart';

class MemberPage extends StatefulWidget {
  const MemberPage({Key? key, required this.groupID}) : super(key: key);
  final String groupID;

  @override
  State<MemberPage> createState() => _MemberPageState();
}

class _MemberPageState extends State<MemberPage> {
  final _controller = Get.find<GroupDetailsBaseController>();
  final _gridController = ScrollController();
  final _localMembers = <String, GroupMemberData>{}.obs;
  String? key;

  /// 文本变化监听
  void _onTextChange(String? text) {
    if (text?.isNotEmpty ?? false) {
      key = text;
      _localMembers.value = _controller.searchMemberDatas(key ?? '');
      AppLogger.d("_localMembers.value=${_localMembers.length}");
    } else {
      _localMembers.value = _controller.rxMemberDatas;
    }
  }

  @override
  void initState() {
    super.initState();
    _onTextChange(null);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: Text(L.group_member_group_member.tr),
        centerTitle: true,
      ),
      body: Column(
        children: [
          Container(
            height: 64.r,
            padding: const EdgeInsets.only(
              left: 15,
              right: 15,
              top: 10,
              bottom: 10
            ).r,
            child: TextField(
              autofocus: false,
              onChanged: (text) => _onTextChange(text),
              textAlign: TextAlign.start,
              style: TextStyle(fontSize: 16.sp),
              textAlignVertical: TextAlignVertical.center,
              decoration: InputDecoration(
                hintText: L.searbar_hint_search.tr,
                // 设置后，提升文本居中
                contentPadding: EdgeInsets.zero,
                prefixIcon: const Icon(
                  Icons.search,
                  color: Color.fromARGB(255, 89, 90, 90),
                ),
                filled: true,
                fillColor: const Color(0xffF7F7F7),
                border: const OutlineInputBorder(
                  borderSide: BorderSide.none,
                  borderRadius: BorderRadius.all(Radius.circular(25)),
                ),
              ),
            ),
          ),
          Expanded(
            child: NotificationListener(
              onNotification: (ScrollNotification notification) {
                if (notification is ScrollEndNotification) {
                  _controller.requestMemberInfo();
                }
                return false;
              },
              child: Obx(
                () {
                  var memberDatas = _localMembers.values.toList();
                  return GridView.builder(
                    controller: _gridController,
                    itemCount: memberDatas.length,
                    padding: const EdgeInsets.only(
                        left: 10, right: 10, top: 2, bottom: 6),
                    gridDelegate:
                        const SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 5,
                      // crossAxisSpacing: 5,
                      // mainAxisSpacing: 5,
                      childAspectRatio: 0.85,
                    ),
                    itemBuilder: (context, index) {
                      String userName = memberDatas[index].username;

                      return GetBuilder<GroupDetailsBaseController>(
                        id: userName,
                        builder: (control) {
                          MemberInfo info =
                              control.getMemberData(userName, request: true);
                          var role = control.getMemberRole(userName);
                          List<String> tags = [];
                          if(control.isDaoChannel() && control is DaoDetailsController) {
                            tags = control.getMemberTags(userName);
                          }

                          return GestureDetector(
                            onTap: () =>
                                _controller.onGotoDetail(info, widget.groupID),
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                SizedBox(
                                  height: 50.r,
                                  width: 50.r,
                                  child: Stack(
                                    children: [
                                      MAvatarCircle(
                                        diameter: 45,
                                        text: info.nickname,
                                        imagePath: info.avatarPath,
                                      ),
                                      Positioned(
                                        right: .0,
                                        bottom: .0,
                                        child: Visibility(
                                          visible:
                                              role == ChannelMemberRole.owner || 
                                                role == ChannelMemberRole.administrator || tags.isNotEmpty
                                                  ? true
                                                  : false,
                                          child: control.isDaoChannel() 
                                            ? role == ChannelMemberRole.ordinary && tags.isNotEmpty && control is DaoDetailsController
                                                ? Container(
                                                    width: 20.w,
                                                    height: 20.w,
                                                    decoration: BoxDecoration(
                                                      color: control.getTagColor(tags[0]) == null 
                                                        ? null 
                                                        : Color(control.getTagColor(tags[0])!),
                                                      borderRadius: BorderRadius.circular(10.w),
                                                      border: Border.all(
                                                        color: Colors.white,
                                                        width: 2.r,
                                                      ),
                                                    ),
                                                    child: Image.asset(
                                                      R.icoGroupDaoTag,
                                                      width: 20.w,
                                                      height: 20.w,
                                                    ),
                                                  )
                                                : Image.asset(
                                                    role == ChannelMemberRole.administrator
                                                        ? R.icoGroupDaoManager
                                                        : R.icoGroupDaoLeader,
                                                    width: 20.w,
                                                    height: 20.w,
                                                  )
                                            : Image.asset(
                                                role == ChannelMemberRole.administrator
                                                    ? R.icoGroupAdmin
                                                    : R.icoGroupOwner,
                                                width: 20.w,
                                                height: 20.w,
                                              ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                const SizedBox(height: 5),
                                Flexible(
                                  child: Text(
                                    info.nickname ?? '',
                                    overflow: TextOverflow.ellipsis,
                                    style: TextStyle(
                                      color: const Color(0xff808080),
                                      fontSize: 12.sp,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          );
                        },
                      );
                    },
                  );
                },
              ),
            ),
          )
        ],
      ),
    );
  }
}
