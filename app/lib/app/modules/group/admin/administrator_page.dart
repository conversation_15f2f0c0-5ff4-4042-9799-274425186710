import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/data/enums/enum.dart';
import 'package:flutter_metatel/r.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../core/languages/l.dart';
import '../../../data/services/channel_service.dart';
import '../../../widgets/divider_cus.dart';
import '../../../widgets/mavatar_circle_avatar.dart';
import '../details/group_details_base_controller.dart';

class AdministratorPage extends StatefulWidget {
  const AdministratorPage({Key? key}) : super(key: key);

  @override
  State<AdministratorPage> createState() => _AdministratorPageState();
}

class _AdministratorPageState extends State<AdministratorPage> {
  final _controller = Get.find<GroupDetailsBaseController>();

  /// 构建群管理员添加或删除按钮
  Widget _buildMemberAddOrDelete(bool add) {
    return GestureDetector(
      onTap: () => _controller.onAddOrRemoveAdmin(context, add),
      child: Container(
        alignment: Alignment.topCenter,
        child: MAvatarCircle(
          imagePath: add
              ? 'assets/images/group_add.png'
              : 'assets/images/group_delete.png',
          chatType: ChatType.channelChat,
        ),
      ),
    );
  }

  Widget _buildMembers() {
    return Obx(
      () {
        var memberDatas = _controller.rxAdminDatas.values.toList();
        int itemCount = memberDatas.length + 2;

        return GridView.builder(
          itemCount: itemCount,
          padding:
              const EdgeInsets.only(left: 10, right: 10, top: 2, bottom: 6),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 5,
          ),
          itemBuilder: (context, index) {
            Widget child;
            if (index == itemCount - 2) {
              child = _buildMemberAddOrDelete(true);
            } else if (index == itemCount - 1) {
              child = _buildMemberAddOrDelete(false);
            } else {
              String userName = memberDatas[index].username;

              return GetBuilder<GroupDetailsBaseController>(
                id: userName,
                builder: (control) {
                  MemberInfo info = control.getMemberData(userName);

                  return GestureDetector(
                    onTap: () {},
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        MAvatarCircle(
                          text: info.nickname,
                          imagePath: info.avatarPath,
                        ),
                        Flexible(
                          child: Text(
                            info.nickname ?? '',
                            overflow: TextOverflow.ellipsis,
                            style: TextStyle(
                              color: const Color(0xff808080),
                              fontSize: 12.sp,
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                },
              );
            }

            return child;
          },
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(L.group_admin.tr),
        centerTitle: true,
      ),
      body: Column(
        children: [
          // 顶部部分
          SizedBox(
            height: 167.h,
            child: Column(
              children: [
                SizedBox(height: 35.h),
                Image.asset(R.groupAdmin, width: 81.w, height: 81.h),
                SizedBox(height: 14.h),
                Text(
                  L.group_admin.tr,
                  style: TextStyle(fontSize: 16.sp, color: Colors.black),
                ),
              ],
            ),
          ),
          const DividerCus(),
          SizedBox(height: 15.h),
          // 成员部分
          Expanded(child: _buildMembers())
        ],
      ),
    );
  }
}
