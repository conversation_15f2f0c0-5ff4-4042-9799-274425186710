import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:get/get.dart';

import '../../../../core/languages/l.dart';
import '../../../../core/utils/util.dart';
import '../../../../core/values/colors.dart';
import '../../../../r.dart';
import '../../../data/enums/enum.dart';
import 'join_controller.dart';

class JoinView extends GetView<JoinController> {
  const JoinView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final double avatarDiameter = 138;
    final _upperHeight = 0.51.sh;
    final _contentHeight = 226.r;
    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Colors.transparent,
        iconTheme: IconThemeData(
          color: AppColors.white,
        ),
      ),
      body: Obx(() {
        String avatarPath =
            appSupporAbsolutePath(controller.iconPath.value) ?? '';
        var info = controller.rxGroupInfoData.value;
        return SingleChildScrollView(
          physics: const ClampingScrollPhysics(),
          child: Column(
            children: [
              /// 背景图，头像
              SizedBox(
                height: _upperHeight,
                child: Stack(
                  children: [
                    /// 背景图
                    Container(
                      height: _upperHeight - (avatarDiameter / 2).r,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            AppColors.primaryBgColor2,
                            AppColors.primaryBgColor1,
                          ],
                          begin: Alignment(-1.2, -1.2),
                          end: Alignment(1, 1),
                        ),
                        borderRadius: BorderRadius.only(
                          bottomLeft: Radius.circular(30.r),
                          bottomRight: Radius.circular(30.r),
                        ),
                      ),
                    ),
          
          
                    /// 头像
                    Align(
                      alignment: Alignment.bottomCenter,
                      child: Container(
                        width: avatarDiameter + 13.r,
                        height: avatarDiameter + 13.r,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: AppColors.white,
                          // border:
                          //     Border.all(color: AppColors.white, width: 4.r),
                        ),
                        child: Center(
                          child: buildChatAvatarWithAttr(
                            ChatType.channelChat.index,
                            controller.channelId ?? "",
                            diameter: avatarDiameter,
                            imagePath: avatarPath,
                            channelAttribute:
                                controller.rxGroupInfoData.value.attribute,
                            iconSize: 30.r,
                            tokenAddress: controller.rxGroupInfoData.value.tokenAddress,
                            chainId: controller.rxGroupInfoData.value.chain,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(height: 40.r),
              Container(
                width: 1.sw,
                height: _contentHeight,
                padding: EdgeInsets.symmetric(horizontal: 30.r),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    /// 群组名
                    Text(
                      info.title ?? '',
                      style: TextStyle(fontSize: 19.sp, color: Colors.black),
                    ),
                        
                    /// 群人数
                    Row(
                      children: [
                        SizedBox(width: 6.r),
                        Image.asset(
                          R.iconGroupNumber,
                          width: 10.r,
                          height: 10.r,
                        ),
                        SizedBox(width: 4.r),
                        Visibility(
                          visible: info.memberCount != null,
                          child: Text(
                            "${info.memberCount}",
                            // L.channel_member
                            //     .trParams({'number': '${info.memberCount ?? ''}'}),
                            style: TextStyle(
                              fontSize: 12.sp,
                              color: Colors.black,
                              fontWeight: FontWeight.w300,
                            ),
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 20.h),
                        
                    /// 群描述
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          L.chat_info_group_describe.tr,
                          style:
                              TextStyle(fontSize: 16.sp, color: Colors.black),
                        ),
                        SizedBox(height: 8.h),
                        ConstrainedBox(
                          constraints: BoxConstraints(
                              minHeight: 40.r, maxHeight: 100.r),
                          child: SingleChildScrollView(
                            child: SizedBox(
                              width: double.infinity,
                              child: Text(
                                info.describe ?? '',
                                textAlign: TextAlign.left,
                                style: TextStyle(
                                    fontSize: 12.sp,
                                    color: AppColors.colorFF999999),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                    
                  ],
                ),
              ),
              SizedBox(height: 40.r),
                        
              if(controller.isLoading.value)
                Center(
                  child: SizedBox(
                    width: 20.r,
                    height: 20.r,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      color: AppColors.primaryBgColor1,
                    ),
                  ),
                ),
                  
              /// Channel加入按钮
              if (!controller.isLoading.value 
                && (controller.rxGroupInfoData.value.attribute == null 
                    || controller.rxGroupInfoData.value.attribute != ChannelAttribute.dao.index
                    || (controller.rxGroupInfoData.value.attribute == ChannelAttribute.dao.index
                        && controller.isDaoRequirementMeet.value)))
                Container(
                  width: 1.sw,
                  padding: EdgeInsets.symmetric(horizontal: 30.r),
                  child: _buildButton(
                    onPressed: controller.join.value
                        ? () {
                            if(controller.rxGroupInfoData.value.attribute == ChannelAttribute.dao.index){
                              controller.joinDao();
                            } else {
                              controller.joinOrSubmit();
                            }
                          }
                        : null,
                    text: L.join_now_1.tr,
                  ),
                ),
                  
              /// 当DAO加入条件未满足
              /// 显示DAO重试 & 立即交易按钮
              if (!controller.isLoading.value 
                && controller.rxGroupInfoData.value.attribute == ChannelAttribute.dao.index && !controller.isDaoRequirementMeet.value)
                SizedBox(
                  width: 1.sw,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Container(
                        width: 1.sw,
                        padding: EdgeInsets.symmetric(horizontal: 30.r),
                        child: _buildButton(
                          onPressed: controller.initJoinChannelInfo,
                          text: L.try_again.tr,
                        ),
                      ),
                      SizedBox(height: 10.r),
                      TextButton(
                        onPressed: controller.onTradeNowTap,
                        child: Text(
                          L.trade_now.tr,
                          style: TextStyle(
                            fontSize: 14.sp,
                            color: AppColors.primaryBgColor1,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              SizedBox(height: 20.r),
            ],
          ),
        );
      }),
    );
  }

  Widget _buildButton({Function()? onPressed, required String text}) {
    return ElevatedButton(
      onPressed: onPressed,
      // style: ElevatedButton.styleFrom(
      //     backgroundColor: controller.join.value
      //         ? AppColors.appDefault
      //         : AppColors.colorFF999999),
      child: Text(
        text,
        style: TextStyle(
          fontSize: 16.sp,
          color: AppColors.white,
        ),
      ),
    );
  }
}
