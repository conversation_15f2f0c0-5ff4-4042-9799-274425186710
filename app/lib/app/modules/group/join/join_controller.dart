import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/data/services/config_service.dart';
import 'package:flutter_metatel/app/modules/home/<USER>';
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:flutter_metatel/core/utils/util.dart';
import 'package:flutter_metatel/core/values/colors.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_web3/app/core/open_wallet_helper.dart';
import 'package:get/get.dart';

import '../../../../core/languages/l.dart';
import '../../../../core/task/channel_task.dart';
import '../../../../core/utils/events_bus.dart';
import '../../../../core/utils/jump.dart';
import '../../../../core/values/code.dart';
import '../../../../core/values/config.dart';
import '../../../data/enums/enum.dart';
import '../../../data/events/events.dart';
import '../../../data/models/channel_info_model_data.dart';
import '../../../data/models/user_message_model.dart';
import '../../../data/providers/api/channel.dart';
import '../../../data/providers/db/database.dart';
import '../../../data/services/channel_service.dart';
import '../../daoCommunity/authorization/dao_signature_autorization.dart';

class JoinController extends GetxController {
  final rxGroupInfoData = ChannelInfoModelData().obs;
  String? channelId;
  String? action;
  String? inviteeUser;
  RxString iconPath = ''.obs;
  RxBool join = false.obs;
  bool isApprove = true;
  int maxCount = 10000;
  ShareCurrentType _shareCurrentType = ShareCurrentType.none;
  RxBool isDaoRequirementMeet = false.obs;
  RxBool isLoading = false.obs;

  @override
  void onInit() async {
    super.onInit();
    channelId = Get.arguments["channelId"];
    action = Get.arguments["action"] ?? '';
    inviteeUser = Get.arguments["invitee_user"] ?? '';
    initJoinChannelInfo();

    AppLogger.d('JoinController=$channelId');
    AppLogger.d('JoinController action=$action');
  }

  initJoinChannelInfo() async {
    if (channelId != null) {
      try {
        isLoading.value = true;
        var modelData = await getChannelInfoRequest(channelId!, dsc: true);      
        AppLogger.d('JoinController modelData=${modelData.toJson()}');

        if (modelData.code != Code.code200) {
          isLoading.value = false;
          toast(L.this_group_is_invalid.tr);
          Get.back();
        } else {
          rxGroupInfoData.value = modelData;
          isApprove = modelData.approve ?? true;
          maxCount = modelData.limit ?? (modelData.attribute == 2 ? 10000 : 1000);
          bool isAdminJoin = false;
          if (inviteeUser == rxGroupInfoData.value.owner ||
              (rxGroupInfoData.value.admins != null &&
                  rxGroupInfoData.value.admins!.contains(inviteeUser))) {
            isAdminJoin = true;
            _shareCurrentType = action == ChannelCardAction.join
                ? ShareCurrentType.leaderJoin
                : ShareCurrentType.none;
          }
          downLoadPath();
          List<ChannelInfoModelData>? list = await getChannelsRequest();        
          ChannelInfoModelData currentDate = ChannelInfoModelData(id: channelId);

          if ((list != null && list.contains(currentDate))) {
            _shareCurrentType = ShareCurrentType.joined;
          } else if (!join.value) {
            //toast(L.cannot_join_this_group.tr);
          }
          join.value = _shareCurrentType != ShareCurrentType.none ||
              (modelData.share ?? false) ||
              isAdminJoin;
          /// 检测Dao群最低门槛是否达标
          if(rxGroupInfoData.value.attribute == ChannelAttribute.dao.index) {
            var chainId = rxGroupInfoData.value.chain;
            var tokenAddress = rxGroupInfoData.value.tokenAddress;
            if(chainId == null || int.tryParse(chainId) == null || tokenAddress == null) {
              isLoading.value = false;
              toast(L.this_group_is_invalid.tr);
              Get.back();
              return;
            }
            var tokens = await getTokens(chainId: int.parse(chainId));
            if(tokens == null || tokens.isEmpty) {
              isLoading.value = false;
              toast(L.token_not_added.tr);
              Get.back();
              return;
            }
            var token = tokens.firstWhereOrNull((token) => token.address == tokenAddress);
            if(token == null) {
              isLoading.value = false;
              toast(L.token_not_added.tr);
              Get.back();
              return;
            }
            if(token.balance != null && modelData.minNumToken != null) {
              if(token.balance != 0 && token.balance! >= modelData.minNumToken!) {
                isDaoRequirementMeet.value = true;
                join.value = true;
              }
            }                        
            if(!isDaoRequirementMeet.value) {
              _showRequirementNotMeetSnackbar(token.symbol ?? '', rxGroupInfoData.value.minNumToken.toString());
            }
          }
          isLoading.value = false;
        }
      } catch (e) {
        isLoading.value = false;
        AppLogger.e('JoinController error=$e');
      }
      
    }
  }

  void downLoadPath() async {
    var db = Get.find<AppDatabase>();
    var oldChannelData = await db
        .oneChannelInfo(rxGroupInfoData.value.id ?? '')
        .getSingleOrNull();
    String? newAvatarPath;
    String url = rxGroupInfoData.value.avatar ?? '';
    String path = oldChannelData?.avatarPath ?? '';
    if (url.isNotEmpty &&
        (url != oldChannelData?.avatarUrl ||
            path.isEmpty ||
            !File(appSupporAbsolutePath(path) ?? '').existsSync())) {
      var savePath = avatarSavePath(
        userName: channelId ?? '',
        fileName: url,
      );
      newAvatarPath =
          await downloadFile(url, savePath: savePath, isComPressImageJPG: true);
      newAvatarPath = appSupporAbsolutePathToPath(newAvatarPath);
      iconPath.value = newAvatarPath ?? '';
      ChannelTask.updateInfo(channelId ?? '',
          avatarPath: appSupporAbsolutePathToPath(newAvatarPath));
    } else {
      iconPath.value = path;
    }
  }

  bool isSend = false;
  void joinOrSubmit() async {
    showLoadingDialog();
    if (_shareCurrentType != ShareCurrentType.none) {
      bool joinSuccess = false;
      if (_shareCurrentType == ShareCurrentType.leaderJoin) {
        if ((rxGroupInfoData.value.memberCount ?? 0) >= maxCount) {
          toast(L.channel_number_count.tr);
          dismissLoadingDialog();
          return;
        }
        joinSuccess = await joinRequest(channelId!);
        if (joinSuccess && !isSend) {
          isSend = true;
          var body =
              "${Get.find<AppConfigService>().getMySelfDisplayName()} ${L.other_join_group_chat.tr}";
          Get.find<ChannelService>().sendJoinChannel(channelId!, body);
          Get.find<EventBus>()
              .fire(MiningTaskEvent(MiningTaskType.MINING_TASK_ADD_CHANNEL));
        }
      }
      if (joinSuccess || _shareCurrentType == ShareCurrentType.joined) {
        ChannelTask.channels([rxGroupInfoData.value]);
        await Future.delayed(const Duration(milliseconds: 500), () {
          var userMessage = UserMessage(
              chatType: ChatType.channelChat.index,
              displayName: rxGroupInfoData.value.title,
              userName: rxGroupInfoData.value.id,
              avatarPath: iconPath.value);
          userMessage.isGroupValid = true;
          Get.back();
          JumpPage.messgae(userMessage);
        });
      }
    } else {
      if ((rxGroupInfoData.value.memberCount ?? 0) >= maxCount) {
        toast(L.channel_number_count.tr);
        dismissLoadingDialog();
        return;
      }
      bool success = await applyJoinRequest(channelId!);
      if (success) {
        if (isApprove) {
          toast(success
              ? L.request_sub_channel.tr
              : L.request_sub_channel_error.tr);
          Get.back();
        } else {
          if (!isSend) {
            isSend = true;
            var body =
                "${Get.find<AppConfigService>().getMySelfDisplayName()} ${L.other_join_group_chat.tr}";
            Get.find<ChannelService>().sendJoinChannel(channelId!, body);
          }
          ChannelTask.channels([rxGroupInfoData.value]);
          await Future.delayed(const Duration(milliseconds: 500), () {
            var userMessage = UserMessage(
                chatType: ChatType.channelChat.index,
                displayName: rxGroupInfoData.value.title,
                userName: rxGroupInfoData.value.id,
                avatarPath: iconPath.value);
            userMessage.isGroupValid = true;
            Get.back();
            JumpPage.messgae(userMessage);
          });
        }
        Get.find<EventBus>()
            .fire(MiningTaskEvent(MiningTaskType.MINING_TASK_ADD_CHANNEL));
      }
    }
    dismissLoadingDialog();
  }

  /// 加入Dao群组
  void joinDao() async {
    AppLogger.d('JoinController joinDao channelId=$channelId');
    try {
      showLoadingDialog();
      AppLogger.d('JoinController joinDao _shareCurrentType=$_shareCurrentType');
      if(_shareCurrentType != ShareCurrentType.joined) {
        if ((rxGroupInfoData.value.memberCount ?? 0) >= maxCount) {
          toast(L.channel_number_count.tr);
          dismissLoadingDialog();
          return;
        }
        /// 获取balance
        var chainId = rxGroupInfoData.value.chain;
        var tokenAddress = rxGroupInfoData.value.tokenAddress;
        if(chainId == null || int.tryParse(chainId) == null || tokenAddress == null) {
          toast(L.join_failed.tr);
          dismissLoadingDialog();
          return;
        }
        var tokens = await getTokens(chainId: int.parse(chainId));
        if(tokens == null || tokens.isEmpty) {
          toast(L.join_failed.tr);
          dismissLoadingDialog();
          return;
        }
        var token = tokens.firstWhereOrNull((token) => token.address == tokenAddress);
        if(token == null) {
          toast(L.join_failed.tr);
          dismissLoadingDialog();
          return;
        }
        dismissLoadingDialog();
        var balance = token.balance ?? 0;
        AppLogger.d('JoinController joinDao balance=$balance');
        // var walletAddress = Get.find<WalletController>().wallet.value.address ?? "";
        var result = await showModalBottomSheet(
          context: Get.context!,
          isScrollControlled: true,
          isDismissible: true,
          backgroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.vertical(top: const Radius.circular(12).r),
          ),
          builder: (context) {
            return DaoSignatureAutorization(
              chainId: int.tryParse(rxGroupInfoData.value.chain ?? ''),
              imagePath: rxGroupInfoData.value.avatar,
              daoName: rxGroupInfoData.value.title,
              tokenAddress: rxGroupInfoData.value.tokenAddress,
            );
          },
        );
        if (result != 'ok') {
          return;
        };
        showLoadingDialog();
        /// 调用加入Dao接口
        bool success = await joinDaoRequest(channelId!, balance);
        if(!success) {
          toast(L.join_failed.tr);
          dismissLoadingDialog();
          return;
        }
        /// 处理成功
        var body = "${Get.find<AppConfigService>().getMySelfDisplayName()} ${L.other_join_group_chat.tr}";
        Get.find<ChannelService>().sendJoinChannel(channelId!, body);
        Get.find<EventBus>().fire(MiningTaskEvent(MiningTaskType.MINING_TASK_ADD_CHANNEL));
      }

      /// 成功 或 已经加入      
      ChannelTask.channels([rxGroupInfoData.value]);
      await Future.delayed(const Duration(milliseconds: 500), () {
        var userMessage = UserMessage(
            chatType: ChatType.channelChat.index,
            displayName: rxGroupInfoData.value.title,
            userName: rxGroupInfoData.value.id,
            avatarPath: iconPath.value);
        userMessage.isGroupValid = true;
        Get.back();
        JumpPage.messgae(userMessage);
      });    
      dismissLoadingDialog();
    } catch (e) {
      AppLogger.e('JoinController joinDao error=$e');
      dismissLoadingDialog();
    }    
  }

  void onTradeNowTap() {
    Get.until((route) => route.isFirst);
    Get.find<HomeController>().openBrowserTab();
  }

  /// Dao群最低门槛未达标，提示
  void _showRequirementNotMeetSnackbar(String token, String condition) {   
    Get.snackbar(
      "",
      "",
      titleText: SizedBox.shrink(),
      messageText: Text(
        "${L.dao_not_meet_condition_hint.trParams({"token": token, "condition": condition})}",
        style: TextStyle(
          fontSize: 14.sp,
          color: AppColors.colorFFC52929,
        ),
      ),      
      icon: Container(
        width: 26.r,
        height: 26.r,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          border: Border.all(
            color: AppColors.colorFFC52929,
            width: 1,
          ),
        ),
        child: Center(
          child: Text(
            "!",
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 20.sp,
              color: AppColors.colorFFC52929,
              fontWeight: FontWeight.bold,
              height: 1.3,
            ),
          ),
        ),
      ),
      backgroundColor: AppColors.colorFFFFF2F2,      
      duration: const Duration(seconds: 3),
    );
  }

  
}
