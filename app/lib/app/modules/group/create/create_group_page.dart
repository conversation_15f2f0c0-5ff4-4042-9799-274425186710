import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/data/models/select_contact_model.dart';
import 'package:flutter_metatel/app/data/models/user_message_model.dart';
import 'package:flutter_metatel/app/data/providers/db/database.dart';
import 'package:flutter_metatel/app/widgets/select_contact_page.dart';
import 'package:flutter_metatel/core/languages/l.dart';
import 'package:flutter_metatel/core/values/config.dart';
import 'package:flutter_metatel/routes/pages.dart';

import 'package:get/get.dart';

import 'create_group_controller.dart';

class CreateGroupPage extends GetView<CreateGroupController> {
  const CreateGroupPage({Key? key}) : super(key: key);

  /// 创建群聊
  void _onSelectedContacts(SelectContactInfo info) {
    if (info.contacts.isEmpty) {
      return;
    }

    if (info.contacts.length == 1) {
      var userMessage = UserMessage(
          chatType: info.type.index,
          displayName: info.contacts.first.localName,
          userName: info.contacts.first.userName,
          avatarPath: info.contacts.first.avatarPath);
      Future.delayed(const Duration(milliseconds: 10),
          () => Get.toNamed(Routes.MESSAGE, arguments: userMessage));
      return;
    }

    controller.createGroup(info.contacts).then((value) {
      if (value) return;
      showDialog(
        context: Get.context!,
        barrierDismissible: false,
        builder: (_) {
          return AlertDialog(
            title: Text(L.tips.tr),
            content: Text(L.create_group_failed.tr),
            actions: <Widget>[
              TextButton(
                child: Text(L.backup_confirm.tr),
                onPressed: () {
                  Get.back();
                },
              ),
            ],
          );
        },
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
      future: controller.getAllFriendContact(),
      builder: (_, snapshot) {
        List<ContactData>? contactDatas;
        if (snapshot.connectionState == ConnectionState.done) {
          contactDatas = snapshot.data as List<ContactData>;
          contactDatas
              .removeWhere((element) => element.type == ContactType.fileHelper);
        }

        return SelectContactPage(
          type: SelectContactType.multiple,
          contactDatas: contactDatas,
          onSelectedContacts: _onSelectedContacts,
          maxSelectCount:
              controller.isPrivateGroup ? (Config.privateGroupMaxNum - 1) : 0,
        );
      },
    );
  }
}
