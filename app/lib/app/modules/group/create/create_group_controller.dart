import 'package:flutter_metatel/app/data/enums/enum.dart';
import 'package:flutter_metatel/app/data/models/channel_card_model.dart';

import 'package:flutter_metatel/app/data/providers/db/database.dart';
import 'package:flutter_metatel/app/data/services/channel_service.dart';
import 'package:flutter_metatel/app/data/services/config_service.dart';
import 'package:flutter_metatel/core/task/channel_task.dart';
import 'package:flutter_metatel/core/task/time_task.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:flutter_metatel/routes/pages.dart';
import 'package:get/get.dart' hide Response;

import '../../../../core/utils/jump.dart';
import '../../../../core/values/config.dart';
import '../../../data/models/select_contact_model.dart';
import '../../../data/models/user_message_model.dart';
import '../../../data/providers/api/channel.dart';
import '../../../data/services/group_service.dart';

class CreateGroupController extends GetxController {
  bool _isPrivateGroup = false;
  bool get isPrivateGroup => _isPrivateGroup;

  @override
  void onInit() {
    super.onInit();
    _isPrivateGroup = Get.arguments['isPrivateGroup'] ?? false;
    AppLogger.d('_isPrivateGroup=$_isPrivateGroup');
  }

  Future<List<ContactData>> getAllFriendContact() async {
    return Get.find<AppDatabase>().allFriendContact().get();
  }

  Future<bool> _createChannel(List<ContactInfo> contacts) async {
    var config = Get.find<AppConfigService>();

    List<String> titles = [];
    titles.add(config.getMySelfDisplayName());

    List<String> members = [];
    for (var element in contacts) {
      members.add(element.userName);

      if (titles.length <= 3) {
        titles.add(element.displayName ?? '');
      }
    }
    String title = titles.join(',');
    title='#$title';
    if (title.length > 30) {
      title = title.substring(0, 30);
    }
    var id = await createChannelRequest(title, '', '', members);
    if (id == null) return false;

    Get.find<ChannelService>().getAllChannelMemberInfos(
        id, Get.find<AppConfigService>().getUserName() ?? '');

    String? userName = Get.find<AppConfigService>().getUserName();
    ChannelTask.create(
        ChannelInfoData(id: 0, channelId: id, title: title, owner: userName));

    var userMessage = UserMessage(
        chatType: ChatType.channelChat.index,
        displayName: title,
        userName: id,
        avatarPath: null);
    Get.toNamed(Routes.MESSAGE, arguments: userMessage);

    Get.find<ChannelService>().sendInvite(
        members,
        id,
        ChannelCard(
          channelId: id,
          title: title,
          inviteeUser: userName,
          action:ChannelCardAction.join,
          expiresTime:
          TimeTask.instance.getNowTime()~/ 1000 + 7 * 24 * 60 * 60,
        ));

    return true;
  }

  Future<bool> _createGroup(List<ContactInfo> contacts) async {
    String? result = await Get.find<GroupService>().createGroup(contacts);
    if (result?.isNotEmpty ?? false) {
      var userMessage = UserMessage(
          chatType: ChatType.groupChat.index,
          userName: result,
          displayName: '',
          avatarPath: '');
      JumpPage.messgae(userMessage);
    }
    return result?.isNotEmpty ?? false;
  }

  /// 创建群聊
  Future<bool> createGroup(List<ContactInfo> contacts) async {
    if (contacts.isEmpty) {
      logger.w('创建群聊，没有选中联系人');
      return false;
    }
    if (_isPrivateGroup) {
      return _createGroup(contacts);
    } else {
      return _createChannel(contacts);
    }
  }
}
