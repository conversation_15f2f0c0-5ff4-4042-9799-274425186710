import 'dart:async';
import 'dart:convert';

import 'package:async_task/async_task_extension.dart';
import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/data/enums/enum.dart';
import 'package:flutter_metatel/app/data/events/events.dart';
import 'package:flutter_metatel/app/data/services/channel_service.dart';
import 'package:flutter_metatel/app/widgets/select_contact_page.dart';
import 'package:flutter_metatel/core/languages/l.dart';
import 'package:flutter_metatel/core/task/channel_task.dart';
import 'package:flutter_metatel/core/task/session_task.dart';
import 'package:flutter_metatel/core/task/time_task.dart';
import 'package:flutter_metatel/core/utils/events_bus.dart';
import 'package:flutter_metatel/core/utils/util.dart';
import 'package:flutter_metatel/core/values/code.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

import '../../../../core/task/message_task.dart';
import '../../../../core/utils/app_log.dart';
import '../../../../core/utils/file_util.dart';
import '../../../../core/values/colors.dart';
import '../../../../core/values/config.dart';
import '../../../data/models/channel_card_model.dart';
import '../../../data/models/channel_info_model_data.dart';
import '../../../data/models/channel_operation_model.dart';
import '../../../data/models/role.dart';
import '../../../data/models/select_contact_model.dart';
import '../../../data/providers/api/channel.dart';
import '../../../data/providers/api/oss.dart';
import '../../../data/providers/db/database.dart';
import '../../../data/services/chatio_service.dart';
import '../../../data/services/config_service.dart';
import '../../../data/services/event_service.dart';
import '../../../widgets/data_time_picker_bottom_scroll/custom_picker.dart';
import '../member/member_mute_talk_page.dart';
import 'group_details_base_controller.dart';

class ChannelDetailsController extends GroupDetailsBaseController {
  final rxGroupInfoData = const ChannelInfoData(id: 0, channelId: '').obs;

  /// 获取频道信息
  @override
  void getChannelInfo(String groupID, int type) async {
    super.getChannelInfo(groupID, type);
    var modelData = await getChannelInfoRequest(groupID);
    if (modelData.code == Code.code11401 ||
        modelData.code == Code.code11404 ||
        modelData.code == Code.code11430 ||
        modelData.code == Code.code11431) {
      if (modelData.code == Code.code11401) {
        toast(L.other_remove_by_group_owner.tr);
      }
      Get.find<ChannelService>().channelInvalidDbDataUpdate(groupID);
      Get.back();
      return;
    }
    if (modelData.code != Code.code200) return;
    rxApplyCount.value = modelData.applyCount ?? 0;
    //
    rxMaxCount.value  = '${modelData.limit??'500'}';
    memberCount.value = modelData.memberCount??0;
    ChannelTask.channels([modelData]);
    Get.find<ChannelService>().getAllChannelMemberInfos(groupID, myName);
  }

  /// 解散或退出频道点击
  @override
  void onDismissOrLeave() async {
    /// 只有普通频道的owner才有解散，Dao没有
    bool isDismiss = isOrdinaryChannel() && isMyOwner();
    await _createHintsMsg(
        isDismiss ? ChannelAction.dismiss : ChannelAction.leave, []);

    showLoading();
    var success = await channelDismissOrLeaveRequest(
        rxGroupInfoData.value.channelId, isDismiss);
    dismissDialog();
    if (success) {
      ChannelTask.dismissOrLeave(rxGroupInfoData.value.channelId);
      /// 清除聊天入口 & draft if any
      SessionTask.delete(rxGroupInfoData.value.channelId, null,null);
      Get.find<AppConfigService>().removeDraft(rxGroupInfoData.value.channelId);
      Get.back();
      Get.back();
    }
  }

  /// 修改标题
  @override
  void updateTitle(String? title) {
    if (title == null) return;
    showLoading();    
    /// 普通频道标题带 '#', Dao不需要
    if (isOrdinaryChannel() && !title.startsWith('#')) {
      title = '#$title';
    }
    if (title.length > 30) {
      title = title.substring(0, 30);
    }
    _updateChannel(
      title,
      rxGroupInfoData.value.avatarUrl,
      rxGroupInfoData.value.describe,
      rxGroupInfoData.value.backgroundUrl,
      rxGroupInfoData.value.announcement,
      rxGroupInfoData.value.options,
    ).then((value) {
      dismissDialog();
      if (value) {
        ChannelTask.updateInfo(rxGroupInfoData.value.channelId, title: title);
      }
    });
  }

  /// 修改描述
  @override
  void updateDescribe(String? describe) {
    if (describe == null) return;
    showLoading();
    _updateChannel(rxGroupInfoData.value.title, rxGroupInfoData.value.avatarUrl,
            describe, rxGroupInfoData.value.backgroundUrl,rxGroupInfoData.value.announcement,rxGroupInfoData.value.options)
        .then((value) {
      dismissDialog();
      if (value) {
        ChannelTask.updateInfo(rxGroupInfoData.value.channelId,
            describe: describe);
      }
    });
  }

  /// 修改群公告
  @override
  void updateAnnouncement(String? announcement) {
    if (announcement == null) return;
    showLoading();
    var an=encodeAnnouncement(announcement);
    _updateChannel(rxGroupInfoData.value.title, rxGroupInfoData.value.avatarUrl,
        rxGroupInfoData.value.describe, rxGroupInfoData.value.backgroundUrl,an,rxGroupInfoData.value.options)
        .then((value) {
      dismissDialog();
      if (value) {
        ChannelTask.updateInfo(rxGroupInfoData.value.channelId, announcement: an);
      }
    });
  }

  /// 修改群头像
  @override
  void updateAvatar(String? avatarPath) async {
    if (avatarPath == null) return;
    showLoading();
    var url = await OssProvider().upload(uuid(), avatarPath,isLastDir: Config.isOversea&&true);
    if (url.isEmpty) {
      dismissDialog();
      return;
    }

    var success = await _updateChannel(rxGroupInfoData.value.title, url,
        rxGroupInfoData.value.describe, rxGroupInfoData.value.backgroundUrl,rxGroupInfoData.value.announcement,rxGroupInfoData.value.options);
    dismissDialog();
    if (success) {
      Get.find<AppConfigService>().saveReUploadChannelAvartar(rxGroupInfoData.value.channelId, true);
      ChannelTask.updateInfo(
        rxGroupInfoData.value.channelId,
        avatarUrl: url,
        avatarPath: appSupporAbsolutePathToPath(avatarPath),
      );
    } else if (rxGroupInfoData.value.avatarPath !=
        appSupporAbsolutePathToPath(avatarPath)) {
      deleteFileIsExists(avatarPath);
    }
  }

  /// 修改群壁纸
  @override
  void updateWallpaper(String? wallpaperPath) async {
    if (wallpaperPath == null) return;
    var wallpaperAbsolutePathToPath =
        appSupporAbsolutePathToPath(wallpaperPath);
    showLoading();
    var url = await OssProvider().upload(uuid(), wallpaperPath);
    if (url.isEmpty) {
      dismissDialog();
      return;
    }

    var success = await _updateChannel(rxGroupInfoData.value.title,
        rxGroupInfoData.value.avatarUrl, rxGroupInfoData.value.describe, url,rxGroupInfoData.value.announcement,rxGroupInfoData.value.options);
    dismissDialog();
    if (success) {
      // String? filePath = await mtCopyFile(File(avatarPath), appSupporDir);
      ChannelTask.updateInfo(rxGroupInfoData.value.channelId,
          backgroundPath: wallpaperAbsolutePathToPath, backgroundUrl: url);
      Get.find<EventBus>().fire(ChatBackgroundUpdateEvent());
      clearWallPaperFile(rxGroupInfoData.value.backgroundPath);
    } else if (rxGroupInfoData.value.backgroundPath !=
        wallpaperAbsolutePathToPath) {
      clearWallPaperFile(wallpaperAbsolutePathToPath);
    }
  }

  /// 恢复默认群壁纸
  @override
  void restoreWallpaper() async {
    var data = await Get.find<AppDatabase>()
        .oneChannelInfo(groupId())
        .getSingleOrNull();
    AppLogger.d("restoreWallpaper data==$data");
    if (data != null) {
      String? wallPaperPath = appSupporAbsolutePath(data.backgroundPath);
      if (wallPaperPath != null) {
        var success = await _updateChannel(
            rxGroupInfoData.value.title,
            rxGroupInfoData.value.avatarUrl,
            rxGroupInfoData.value.describe,
            "",rxGroupInfoData.value.announcement,rxGroupInfoData.value.options);
        if (success) {
          ChannelTask.updateInfo(rxGroupInfoData.value.channelId,
              backgroundPath: "", backgroundUrl: "");
          Get.find<EventBus>().fire(ChatBackgroundUpdateEvent());
          clearWallPaperFile(data.backgroundPath);
        }
      }
    }
  }

  /// 更新我的群昵称
  @override
  void updateNickName(String? nickName) {
    if (nickName == null) return;
    showLoading();
    updateNicknameRequest(rxGroupInfoData.value.channelId, nickName)
        .then((value) {
      dismissDialog();
      if (value) {
        ChannelTask.updateNickname(rxGroupInfoData.value.channelId, nickName);
      }
    });
  }

  /// 清除聊天记录
  @override
  void clearChatHistory(bool check,{bool isToast=true}) {
    ChannelTask.updateChannelMinUuid(rxGroupInfoData.value.channelId);
    Get.find<EventBus>()
        .fire(ClearChatHistoryEvent(rxGroupInfoData.value.channelId));
    SessionTask.clear(rxGroupInfoData.value.channelId);
    Get.find<AppDatabase>().topMsgIdByUserNameAdmin(rxGroupInfoData.value.channelId).get().then((value){
      deleteMessageAndFile(rxGroupInfoData.value.channelId,clearTop: check,noDelMsgIds: value).then((value) {
        Get.find<EventBus>()
            .fire(ClearChatHistoryEvent(rxGroupInfoData.value.channelId));
        if(isToast)
          toast(L.chat_history_cleared_successfully.tr);
      });
    });
  }
  @override
  void changeContactLimit(bool can){
    showLoading();
    ChannelOptions options=ChannelOptions(contactLimit: can);
    // String optionsJsonString = options.toJson().toString();
    String optionsJsonString = jsonEncode(options);
    AppLogger.d('channel controller changeContactLimit:$optionsJsonString');
    rxGroupInfoData.value=rxGroupInfoData.value.copyWith(options:ofNullable(optionsJsonString));
    _updateChannel(rxGroupInfoData.value.title, rxGroupInfoData.value.avatarUrl,
        rxGroupInfoData.value.describe, rxGroupInfoData.value.backgroundUrl,rxGroupInfoData.value.announcement,optionsJsonString)
        .then((value) {
      dismissDialog();
      if (value) {
        ChannelTask.updateInfo(rxGroupInfoData.value.channelId, options: optionsJsonString);
      }
    });
  }
  /// 全体禁言/邀请限制设置
  @override
  void allMuteOrinviteLimit(ChannelAction ac, bool value) async {
    bool result = false;
    showLoading();
    if (ac == ChannelAction.allMute) {
      result =
          await muteRequest(rxGroupInfoData.value.channelId, myName, value);
    } else if (ac == ChannelAction.inviteLimit) {
      result =
          await shareRequest(rxGroupInfoData.value.channelId, myName, value);
    }
    dismissDialog();
    if (!result) {
      toast(L.the_operation_failed.tr, textColor: Colors.red);
      return;
    }

    String event = '';
    String action = '';
    String? body;
    bool? allMute, inviteLimit;

    if (ac == ChannelAction.allMute) {
      allMute = value;
      action = ChannelOption.mute;

      if (value) {
        event = ChannelOptionEv.mute_all;
        body = L.open_all_mute.tr;
      } else {
        event = ChannelOptionEv.remove_mute_all;
        body = L.close_all_mute.tr;
      }
    } else {
      inviteLimit = value;
      action = ChannelOption.invite;

      if (value) {
        event = ChannelOptionEv.remove_invite_all;
      } else {
        event = ChannelOptionEv.invite_all;
      }
    }

    Get.find<AppDatabase>().insertOrUpdateChannelInfoData([
      ChannelInfoCompanion.insert(
        channelId: rxGroupInfoData.value.channelId,
        allMute: ofNullable(allMute),
        inviteLimit: ofNullable(inviteLimit),
        updateTime:
            ofNullable(TimeTask.instance.getNowTime().toDouble()),
      )
    ]);

    var mapData = ChannelOperation(
      action: action,
      event: event,
      time: TimeTask.instance.getNowTime(),
      chatType: ChatType.channelChat.index,
      type: MessageType.channelOpera.index,
      msgId: uuid(),
      owner: rxGroupInfoData.value.channelId,
      body: body,
    ).toJson();
    sendMessageRequest(rxGroupInfoData.value.channelId, json.encode(mapData));
  }

  /// 进频道审核变化
  @override
  void approvalChange(bool value) async {
    showLoading();
    var result = await approvalRequest(rxGroupInfoData.value.channelId, value);
    dismissDialog();
    if (!result) {
      toast(L.the_operation_failed.tr, textColor: Colors.red);
      return;
    }

    Get.find<AppDatabase>().insertOrUpdateChannelInfoData([
      ChannelInfoCompanion.insert(
        channelId: rxGroupInfoData.value.channelId,
        joinVerify: ofNullable(value),
        updateTime:
            ofNullable(TimeTask.instance.getNowTime().toDouble()),
      )
    ]);

    var mapData = ChannelOperation(
      action: ChannelOption.approval,
      event: value
          ? ChannelOptionEv.approval_all
          : ChannelOptionEv.remove_approval_all,
      time: TimeTask.instance.getNowTime(),
      chatType: ChatType.channelChat.index,
      type: MessageType.channelOpera.index,
      msgId: uuid(),
      owner: rxGroupInfoData.value.channelId,
    ).toJson();
    sendMessageRequest(rxGroupInfoData.value.channelId, json.encode(mapData));
  }

  /// 添加或移除管理员
  @override
  void onAddOrRemoveAdmin(BuildContext context, bool isAdd) async {
    rxAdminDatas.values.toList();

    List<GroupMemberData> datas = [];
    if (isAdd) {
      for (var element in rxMemberDatas.values) {
        if (rxGroupInfoData.value.owner == element.username ||
            rxAdminDatas.containsKey(element.username)) {
          continue;
        }

        datas.add(element);
      }
    } else {
      datas = rxAdminDatas.values.toList();
    }

    List<ContactData> contactDatas = [];
    for (var element in datas) {
      MemberInfo info = getMemberData(element.username);
      contactDatas.add(ContactData(
        id: 0,
        username: element.username,
        displayname: info.nickname,
        localname: info.nickname,
        avatarPath: appSupporAbsolutePathToPath(info.avatarPath),
      ));
    }
    contactDatas
        .removeWhere((element) => element.type == ContactType.fileHelper);
    var resultData = await showDialog(
      context: context,
      builder: (_) {
        return SelectContactPage(
            type: SelectContactType.multiple, contactDatas: contactDatas);
      },
    );
    if (resultData == null) {
      return;
    }

    var contacts = resultData as SelectContactInfo;
    List<String> users = [];
    List<String> targetDisplayNames = [];
    for (var element in contacts.contacts) {
      users.add(element.userName);
      targetDisplayNames.add(element.displayName ?? "");
    }

    showLoading();
    bool result = await Get.find<ChannelService>().addOrRemoveAdmin(
        rxGroupInfoData.value.channelId, users, targetDisplayNames, isAdd);
    dismissDialog();
    if (!result) {
      toast(L.the_operation_failed.tr, textColor: Colors.red);
    }
  }

  /// 获取加入申请列表
  @override
  void getApplyList(ValueChanged<List<MemberInfo>?> callback) async {
    var usernames = await applyListRequest(rxGroupInfoData.value.channelId);
    rxApplyCount.value = usernames?.length ?? 0;
    if (usernames == null || usernames.isEmpty) {
      return;
    }

    Get.find<ChannelService>().getOwnInfos(usernames, callback: callback);
  }
  /// 获取黑名单列表
  @override
  void getBlackList(ValueChanged<List<MemberInfo>?> callback) async {
    var usernames = await getBlackListRequest(rxGroupInfoData.value.channelId);
    AppLogger.d('getBlackList usernames=${usernames?.length}');
    if (usernames == null || usernames.isEmpty) {
      return;
    }

    Get.find<ChannelService>().getOwnInfos(usernames, callback: callback);
  }
  /// 审批加入
  @override
  Future<bool> onApproveJoin(String? username) async {
    if (username == null) return false;

    var result =
        await approveJoinRequest(rxGroupInfoData.value.channelId, username);
    if (!result) {
      toast(L.the_operation_failed.tr, textColor: Colors.red);
      return false;
    }

    int applyCount = rxApplyCount.value;
    if (applyCount > 0) {
      rxApplyCount.value = applyCount - 1;
    }

    ChannelTask.members(
      rxGroupInfoData.value.channelId,
      [
        GroupMemberCompanion.insert(
          groupId: rxGroupInfoData.value.channelId,
          username: username,
          memberUuid: rxGroupInfoData.value.channelId + username,
          createTime:
              ofNullable(TimeTask.instance.getNowTime().toDouble()),
          updateTime:
              ofNullable(TimeTask.instance.getNowTime().toDouble()),
        )
      ],
      false,
    );

    String body = '';
    Get.find<ChannelService>().getOwnInfos([username], callback: (values) {
      if (values?.isNotEmpty == true) {
        body = "${values!.first.displayname ?? ''} ${L.other_join_group_chat.tr}";
      }
    }).then((value) => Get.find<ChannelService>()
        .sendJoinChannel(rxGroupInfoData.value.channelId, body));

    return result;
  }
  /// 移除黑名单
  @override
  Future<bool> onRemoveBlackList(String? username) async {
    if (username == null) return false;
    var result =
    await removeBlackListRequest(rxGroupInfoData.value.channelId, username);
    if (!result) {
      toast(L.the_operation_failed.tr, textColor: Colors.red);
      return false;
    }
    return result;
  }
  Future<bool> _updateChannel(
    String? title,
    String? avatar,
    String? describe,
    String? wallpaper,
    String? announcement,
    String? options,
  ) async {
    return updateChannelInfoRequest(ChannelInfoModelData(
        id: rxGroupInfoData.value.channelId,
        title: title,
        describe: describe,
        avatar: avatar,
        announcement: announcement,
        options: options,
        wallpaper: wallpaper));
  }

  Future<void> _createHintsMsg(ChannelAction ac,
      [List<String>? members]) async {
    List<String> names = [myName, ...?members];

    var resultData = await Get.find<ChannelService>()
        .getDBMemberData(rxGroupInfoData.value.channelId, names);
    if (resultData.isEmpty) return;

    String selfNickname = '';
    String otherNickname = '';
    List<String> nicknames = [];

    for (var element in resultData.values) {
      String? nickname = element.nickname?.isNotEmpty == true
          ? element.nickname
          : element.displayname;
      nickname = nickname?.isNotEmpty == true ? nickname : element.username;

      if (element.username == myName) {
        selfNickname = nickname ?? '';
      } else {
        nicknames.add(nickname ?? '');
      }
    }
    otherNickname = nicknames.join('、');

    String body = '';
    switch (ac) {
      case ChannelAction.invite:
        body = selfNickname +
            L.other_invite_2.tr +
            otherNickname +
            L.other_join_group_chat.tr;
        break;
      case ChannelAction.kick:
        body = otherNickname +
            L.other_has.tr +
            selfNickname +
            L.other_remove_from_group.tr;
        break;
      case ChannelAction.dismiss:
        body = selfNickname + L.disband_the_group.tr;
        break;
      case ChannelAction.leave:
        body = selfNickname + L.quit_group.tr;
        break;
      default:
    }
    if (body.isEmpty) return;

    /// 发送消息
    String msgID = uuid();
    MessageEvent event = MessageEvent(
      msgID,
      owner: rxGroupInfoData.value.channelId,
      chatType: ChatType.channelChat,
      dateTime: TimeTask.instance.getNowDateTime(),
      body: body,
      type: MessageType.tip,
    );
    await sendMessage(null, event);
    Get.find<AppDatabase>().oneMessage(msgID).getSingle().then((value) {
      Get.find<EventBus>().fire(MessageRecvEvent(false, [value]));
    });
  }

  @override
  void shar(BuildContext context,{bool invite=false,List<ContactData>? cs}) {
    if (rxMyRole.value == ChannelMemberRole.ordinary &&
        rxGroupInfoData.value.inviteLimit == false) {
      toast(L.notShare.tr);
      return;
    }
    if(invite){
      sharController?.shareChannel(context, isSaveDb:false,channelInfoData:rxGroupInfoData.value,successHint:L.invitation_sent.tr,cs: cs,onlyContact: true);
    }else{
      sharController?.showShareWidget(context, rxGroupInfoData.value);
    }

  }

  @override
  void inviteOrKick(SelectContactInfo info, bool invite) async {
    List<String> users = [];
    List<String> displayNames = [];

    for (var element in info.contacts) {
      users.add(element.userName);
      displayNames.add(element.displayName ?? "");
    }

    showLoading();
    var success = await memberInviteOrKickRequest(
        rxGroupInfoData.value.channelId, users, invite);
    dismissDialog();
    if (success && !invite) {
      ChannelTask.memberInviteOrKick(
          rxGroupInfoData.value.channelId, users, invite);
      Get.find<ChannelService>().sendKick(rxGroupInfoData.value.channelId,
          users, displayNames, rxMyRole.value, false);
    }

    if (success && invite) {
      Get.find<ChannelService>().sendInvite(
        users,
        rxGroupInfoData.value.channelId,
        ChannelCard(
          channelId: rxGroupInfoData.value.channelId,
          description: rxGroupInfoData.value.describe,
          title: rxGroupInfoData.value.title,
          inviteeUser: myName,
          avatarUrl: rxGroupInfoData.value.avatarUrl,
          action:ChannelCardAction.join,
          expiresTime:
          TimeTask.instance.getNowTime() ~/ 1000 + 7 * 24 * 60 * 60,
          tokenAddress: rxGroupInfoData.value.tokenAddress,
          channelAttribute: rxGroupInfoData.value.attribute,
          chainId: rxGroupInfoData.value.chain,
        ),
      );
      toast(L.invitation_sent.tr);
    }
  }
   @override
  StreamSubscription? getDetailInfo() {
    return db.oneChannelInfo(groupId()).watchSingleOrNull().listen((event) {
      AppLogger.d('getDetailInfo event ${event.toString()}');
      if (event != null) {
        rxGroupInfoData.value = event;
        title.value = event.title ?? '';
        avatarPath.value = appSupporAbsolutePath(event.avatarPath) ?? "";
        describe.value = event.describe ?? '';
        rxMaxCount.value="${event.limit??0}";
        memberCount.value = event.memberCount??0;
        if(event.announcement?.isNotEmpty??false){
          announcement.value=decodeAnnouncement(event.announcement!);

        }

        if (event.state == ChannelState.invalid.index) {
          toast(L.group_invalid.tr);
        }

        Get.find<EventBus>().fire(ChannelOrGroupInfoUpdateEvent(
            id: groupId(),
            type: chatType.index,
            title: event.title,
            avatarPath: event.avatarPath,
            announcement: event.announcement,
            invalid: event.state == ChannelState.normal.index));
      }
    });
  }

  @override
  bool isInvalid() {
    return rxGroupInfoData.value.state == ChannelState.normal.index;
  }

  @override
  bool allMute() {
    return rxGroupInfoData.value.allMute ?? false;
  }

  @override
  bool inviteLimit() {
    return rxGroupInfoData.value.inviteLimit ?? false;
  }

  @override
  bool joinVerify() {
    return rxGroupInfoData.value.joinVerify ?? false;
  }
  ///群/频道撤回
  @override
  repeal() async {
    CusTimePickerFinalModel? model = await Get.to(MemberMuteTalkPage(
      title: L.undo_time.tr,
      appBarTitle: L.select_undo_time.tr,
    ));
    if (model == null) return;

    var value = model.getTime() ~/ 1000;
    bool result = await messageUndoRequest(
        groupId(), myName, value);
    if (!result) {
      return;
    }

    int time = TimeTask.instance.getNowTime();
    // String nickname = Get.find<AppConfigService>().getMySelfDisplayName();
    var dateTime = DateTime.fromMillisecondsSinceEpoch(time);
    String timeText = DateFormat("yyyy-MM-dd HH:mm:ss").format(dateTime);
    String bodyText = '$timeText ${L.the_message_was_recall.tr}';

    var mapData = ChannelOperation(
      action: ChannelOption.recall,
      time: time,
      chatType: ChatType.channelChat.index,
      type: MessageType.channelOpera.index,
      targetId: [myName],
      targetTime: time - (value * 1000),
      msgId: uuid(),
      owner: groupId(),
      body: bodyText,
    ).toJson();
    sendMessageRequest(groupId(), json.encode(mapData));
  }

  @override
  onGotoDetail(MemberInfo data, String groupID){
    if(rxMyRole.value==ChannelMemberRole.ordinary&&contactLimit()){
      return;
    }
    super.onGotoDetail(data, groupID);
  }
  @override
  bool contactLimit() {
    bool contactLimit=ChannelTask.contactLimit(rxGroupInfoData.value.options);
    return contactLimit;
  }


// 群主转让
@override
void selectTransferOwner() async {
  List<ContactData> contactDatas = [];
  for (var element in rxMemberDatas.values) {
    var role = ChannelMemberRole.values[element.role ?? 0];
    if (element.username == myName ||
        (rxMyRole.value != ChannelMemberRole.owner &&
            role != ChannelMemberRole.ordinary)) {
      continue;
    }

    MemberInfo info = getMemberData(element.username);

    contactDatas.add(ContactData(
      id: 0,
      username: element.username,
      displayname: info.nickname,
      localname: info.nickname,
      avatarPath: appSupporAbsolutePathToPath(info.avatarPath),
    ));
  }

  var resultData = await showDialog(
    context: Get.context!,
    builder: (_) {
      return SelectContactPage(
        type: SelectContactType.multiple,
        contactDatas: contactDatas,
        maxSelectCount: 1,
      );
    },
  );

  if (resultData == null) {
    return;
  }

  var contacts = resultData as SelectContactInfo;
  if (contacts.contacts.isEmpty) {
    return;
  }

  var selectedContact = contacts.contacts.first.userName;

  AppLogger.d('Selected contact: $selectedContact');

  // 在选择联系人后，显示确认对话框
  showBottomDialogCommon(Get.context!, selectedContact);
}

void showBottomDialogCommon(BuildContext context, String selectedContact) async {
  Timer? timer;
  var logOfCountTime = 5.obs;
  timer = Timer.periodic(const Duration(seconds: 1), (t) {
    if (logOfCountTime.value > 0) {
      --logOfCountTime.value;
    } else {
      t.cancel();
      timer = null;
    }
  });
  List<Widget> widgets = [_logOffWidget(logOfCountTime, selectedContact)];
  await showModalBottomSheet(
    context: context,
    isScrollControlled: true,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.vertical(
          top: const Radius.circular(12).r,
          bottom: const Radius.circular(12).r),
    ),
    builder: (context) {
      return Column(
        mainAxisSize: MainAxisSize.min,
        children: widgets,
      );
    },
  );
  AppLogger.d("群主转让对话框已关闭。");
  timer?.cancel();
  timer = null;
}

Widget _logOffWidget(Rx<int> time, String selectedContact) {
  return Listener(
    onPointerUp: (e) {
      //Get.back();
    },
    behavior: HitTestBehavior.deferToChild,
    child: Column(
      children: [
        Container(
          color: Colors.transparent,
          height: 28.h,
        ),
        Center(
          child: Text(
            L.group_owner_transfer_confirm.tr,
            style: TextStyle(fontSize: 21.sp, color: Colors.black),
          ),
        ),
        Container(
          color: Colors.transparent,
          height: 30.h,
        ),
        Center(
          child: Padding(
            padding: EdgeInsets.all(16.sp),
            child: Text(
              L.group_owner_transfer_desc.trParams({'user': selectedContact}),
              style: TextStyle(fontSize: 18.sp, color: Colors.red),
            ),
          ),
        ),
        Container(
          color: Colors.transparent,
          height: 85.h,
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            SizedBox(
              width: 16.w,
            ),
            Expanded(
              flex: 1,
              child: Obx(
                () => IgnorePointer(
                  ignoring: time.value > 0,
                  child: TextButton(
                    style: ButtonStyle(
                      fixedSize: MaterialStateProperty.all(Size.fromHeight(40.h)),
                      shape: MaterialStateProperty.all(
                        RoundedRectangleBorder(
                          borderRadius:
                              BorderRadius.all(Radius.circular(5.r).r).r,
                        ),
                      ),
                      overlayColor:
                          MaterialStateProperty.all(Colors.transparent),
                      foregroundColor:
                          MaterialStateProperty.resolveWith((states) {
                        return states.contains(MaterialState.pressed)
                            ? Colors.black54
                            : Colors.black38;
                      }),
                      backgroundColor: time.value == 0
                          ? MaterialStateProperty.resolveWith((states) {
                              return states.contains(MaterialState.pressed)
                                  ? Colors.redAccent
                                  : Colors.red;
                            })
                          : MaterialStateProperty.all(AppColors.contactColor),
                    ),
                    onPressed: () async {
                      if (time.value == 0) {
                        AppLogger.d("开始转让群主权限...");
                        Get.back(); // 关闭对话框
                        showLoading();
                        try {
                          await updateOwner(groupId(), selectedContact);
                          dismissDialog();
                          toast(L.group_owner_transfer_success.tr);
                          Get.back(); // 返回上一层
                        } catch (e) {
                          dismissDialog();
                          toast(L.group_owner_transfer_failed.tr);
                        }
                      } else {
                        AppLogger.d("倒计时尚未结束，无法转让。当前倒计时：${time.value}秒");
                      }
                      updateOwnerMessage(groupId(), selectedContact);

                    },
                    child: Container(
                      height: 40.h,
                      alignment: Alignment.center,
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            L.confirm.tr,
                            style: TextStyle(
                              color: time.value > 0
                                  ? Colors.grey
                                  : AppColors.white,
                              fontSize: 16.sp,
                            ),
                          ),
                          Visibility(
                            visible: time.value > 0,
                            child: Text(
                              "(${time.value})",
                              style: TextStyle(
                                color: time.value > 0
                                    ? Colors.grey
                                    : AppColors.white,
                                fontSize: 16.sp,
                              ),
                            ),
                          )
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
            SizedBox(
              width: 8.w,
            ),
            Expanded(
              flex: 1,
              child: TextButton(
                style: ButtonStyle(
                  fixedSize:
                      MaterialStateProperty.all(Size.fromHeight(40.h)),
                  shape: MaterialStateProperty.all(
                    RoundedRectangleBorder(
                      borderRadius:
                          BorderRadius.all(Radius.circular(5.r).r).r,
                    ),
                  ),
                  overlayColor: MaterialStateProperty.all(Colors.transparent),
                  foregroundColor:
                      MaterialStateProperty.resolveWith((states) {
                    return states.contains(MaterialState.pressed)
                        ? Colors.black54
                        : Colors.black38;
                  }),
                  backgroundColor:
                      MaterialStateProperty.all(AppColors.appDefault),
                ),
                onPressed: () {
                  Get.back();
                },
                child: Container(
                  height: 40.h,
                  alignment: Alignment.center,
                  child: Text(
                    L.cancel.tr,
                    style: TextStyle(
                      color: AppColors.white,
                      fontSize: 16.sp,
                    ),
                  ),
                ),
              ),
            ),
            SizedBox(
              width: 16.w,
            ),
          ],
        ),
        SizedBox(
          height: 20.h,
        ),
        Container(
          color: Colors.transparent,
          height: 40.h,
        ),
      ],
    ),
  );
}

//发送群主转让消息
void updateOwnerMessage(String groupID, String newOwner) async {
  var channelService = Get.find<ChannelService>();
  getChannelInfo(groupID, ChatType.channelChat.index);
  bool isSuccess = await channelService.sendTransfer(
    groupID,
    (getMemberData(newOwner).displayname ?? newOwner) + 
    "${L.new_group_owner.tr}",
  );
    if (isSuccess) {
      var action = ChannelOption.transfer;
      Get.find<EventBus>().fire(ChannelOptionEvent(action, groupID));
      print('Transfer message sent and event fired successfully.');
    } else {
      print('Transfer message failed to send. Event not fired.');
    }}

  List<String> getMemberTags(String username) {
    if (rxMemberDatas.containsKey(username)) {
      if(rxMemberDatas[username]!.tags != null && rxMemberDatas[username]!.tags != "null") {
        List<dynamic> dynamicList = json.decode(rxMemberDatas[username]!.tags!);
        List<String> stringList = dynamicList.map((e) => e.toString()).toList();
        return stringList;
      }
    }
    return <String>[];
  }

  int? getTagColor(String tag) {
    if(rxGroupInfoData.value.tags == null) return null;
    List<dynamic> dynamicList = json.decode(rxGroupInfoData.value.tags!);
    List<Role> tags = dynamicList.map((e) => Role.fromJson(e)).toList();
    for(var t in tags) {
      if(t.tagId == tag) {
        return t.color;
      }
    }
    return null;
  }

}
