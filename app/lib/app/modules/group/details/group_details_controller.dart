import 'dart:async';
import 'dart:io';

import 'package:async_task/async_task_extension.dart';
import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/data/events/events.dart';
import 'package:flutter_metatel/core/languages/l.dart';
import 'package:flutter_metatel/core/task/session_task.dart';
import 'package:flutter_metatel/core/utils/events_bus.dart';
import 'package:flutter_metatel/core/utils/util.dart';
import 'package:flutter_metatel/core/values/channel.dart';
import 'package:get/get.dart';

import '../../../../core/task/message_task.dart';
import '../../../../core/utils/app_log.dart';
import '../../../data/models/select_contact_model.dart';
import '../../../data/providers/db/database.dart';
import '../../../data/services/group_service.dart';
import 'group_details_base_controller.dart';

class GroupDetailsController extends GroupDetailsBaseController {
  final rxGroupInfoData = GroupInfoData(id: 0, groupId: '1').obs;

  /// 解散或退出频道点击
  @override
  void onDismissOrLeave() async {
    await _createHintsMsg(
        isMyOwner() ? ChannelAction.dismiss : ChannelAction.leave, []);
  }

  /// 修改标题
  @override
  void updateTitle(String? title) async {
    if (title != null) {
      showLoading();
      await Get.find<GroupService>().updateTitle(groupId(), title);
      dismissDialog();
    }
  }

  /// 修改描述
  @override
  void updateDescribe(String? describe) async {
    if (describe != null) {
      showLoading();
      await Get.find<GroupService>().updateDescribe(groupId(), describe);
      dismissDialog();
    }
  }
  /// 修改公告
  @override
  void updateAnnouncement(String? announcement)async {
    if (announcement != null) {
      showLoading();
      var an=encodeAnnouncement(announcement);
      await Get.find<GroupService>().updateAnnouncement(groupId(), an);
      dismissDialog();
    }
  }
  /// 修改群头像
  @override
  void updateAvatar(String? avatarPath) async {
    if (avatarPath != null && File(avatarPath).existsSync()) {
      showLoading();
      await Get.find<GroupService>().updateAvatar(groupId(), avatarPath);
      dismissDialog();
    }
  }

  /// 修改群壁纸
  @override
  void updateWallpaper(String? wallpaperPath) async {}

  /// 恢复默认群壁纸
  @override
  void restoreWallpaper() async {}

  /// 更新我的群昵称
  @override
  void updateNickName(String? nickName) {
    if (nickName == null) return;
  }

  /// 清除聊天记录
  @override
  void clearChatHistory(bool check,{bool isToast=true}) {
    SessionTask.clear(groupId());
    deleteMessageAndFile(groupId(),clearTop: check).then((value) {
      Get.find<EventBus>().fire(ClearChatHistoryEvent(groupId()));
      toast(L.chat_history_cleared_successfully.tr);
    });
  }

  /// 添加或移除管理员
  @override
  void onAddOrRemoveAdmin(BuildContext context, bool isAdd) async {}

  Future<void> _createHintsMsg(ChannelAction ac,
      [List<String>? members]) async {
    showLoading();
    if (isMyOwner()) {
      await Get.find<GroupService>().disbandGroup(groupId());
    } else {
      await Get.find<GroupService>().quitGroup(groupId());
    }
    dismissDialog();
  }

  @override
  void shar(BuildContext context,{bool invite=false,List<ContactData>? cs}) {}

  @override
  void inviteOrKick(SelectContactInfo info, bool invite) async {
    if (info.contacts.isNotEmpty) {
      showLoading();
      if (invite) {
        await Get.find<GroupService>().addMember(groupId(), info.contacts);
      } else {
        await Get.find<GroupService>()
            .removeMember(groupId(), info.contacts);
      }
      dismissDialog();
    }
  }

  @override
  StreamSubscription? getDetailInfo() {
    return db.oneGroupInfo(groupId()).watchSingleOrNull().listen((event) {
      rxMaxCount.value = Channel.maxMemberLimitGroup;
      if (event != null) {
        rxGroupInfoData.value = event;
        title.value = event.title ?? '';
        avatarPath.value = appSupporAbsolutePath(event.avatarPath) ?? '';
        AppLogger.d('getDetailInfo avatarPath=${avatarPath.value}');
        describe.value = event.describe ?? '';
        if(event.announcement?.isNotEmpty??false){
          announcement.value=decodeAnnouncement(event.announcement!);
        }
        AppLogger.d('getDetailInfo announcement.value=${announcement.value.toJson()}');

        // Get.find<EventBus>().fire(ChannelOrGroupInfoUpdateEvent(
        //     id: groupId(),
        //     type: chatType.index,
        //     title: event.title,
        //     avatarPath: event.avatarPath,
        //     invalid: isInvalid()));
        if (!(event.invalid ?? false)) {
          toast(L.group_invalid.tr);
          Get.back();
        }
      }
    });
  }

  @override
  bool isInvalid() {
    return rxGroupInfoData.value.invalid ?? true;
  }

  @override
  bool allMute() {
    return false;
  }

  @override
  bool inviteLimit() {
    return false;
  }

  @override
  bool joinVerify() {
    return false;
  }

  @override
  bool contactLimit() {
    return false;
  }
  
  @override
  void selectTransferOwner() {}

}
