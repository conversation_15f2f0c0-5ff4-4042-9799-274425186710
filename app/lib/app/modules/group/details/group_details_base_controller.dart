import 'dart:async';
import 'dart:convert';

import 'package:async_task/async_task_extension.dart';
import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/data/enums/enum.dart';
import 'package:flutter_metatel/app/data/services/channel_service.dart';
import 'package:flutter_metatel/app/data/services/config_service.dart';
import 'package:flutter_metatel/app/data/services/down_loads_service.dart';
import 'package:flutter_metatel/app/widgets/select_contact_page.dart';
import 'package:flutter_metatel/core/task/time_task.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:flutter_metatel/core/utils/util.dart';
import 'package:flutter_metatel/core/values/config.dart';
import 'package:get/get.dart';

import '../../../../core/languages/l.dart';
import '../../../../core/utils/events_bus.dart';
import '../../../data/events/events.dart';
import '../../../data/models/annoucement_model.dart';
import '../../../data/models/select_contact_model.dart';
import '../../../data/providers/db/database.dart';
import '../../../data/services/network_connect_service.dart';
import '../../../widgets/loading_view.dart';
import '../../home/<USER>/detail/contact_detail.dart';
import '../../home/<USER>/session_controller.dart';
import 'share_controller.dart';

enum ChannelAction {
  invite, // 邀请成员
  kick, // 踢出
  dismiss, // 解散
  leave, // 退出
  allMute, // 全体禁言
  inviteLimit, // 邀请限制
}

abstract class GroupDetailsBaseController extends GetxController {
  final rxMemberDatas = <String, GroupMemberData>{}.obs;
  final rxAdminDatas = <String, GroupMemberData>{}.obs;

  final _id = ''.obs; //频道或者群id
  ChatType chatType = ChatType.channelChat;

  final avatarPath = ''.obs;
  final title = ''.obs;
  final describe = ''.obs;
  final Rx<AnnouncementMode> announcement = AnnouncementMode().obs;

  /// 联系人数据
  final _mapDBContactData = <String, ContactData>{};

  /// 我的群昵称
  final rxMyNickname = ''.obs;

  /// 自己角色
  final rxMyRole = ChannelMemberRole.ordinary.obs;

  /// 待审批个数
  final rxApplyCount = 0.obs;

  final rxMaxCount = ''.obs;
  final memberCount = 0.obs;
  final List<StreamSubscription?> _subscriptions = [];

  /// 请求过的缓存
  final Map<String, MemberInfo?> memberAvatarCache = {};

  /// 自己账号ID
  String myName = '';

  /// 初次最大请求个数
  final int _initMaxRequestCount = 100;

  /// 需要请求的列表
  final List<String> _requestNames = [];
  SharController? sharController;
  late AppDatabase db;

  // 请求全部成员数据，记录时间
  int _requestAllMemberTime = 0;

  StreamSubscription? _subscription;

  void initP() {
    _subscription = Get.find<EventBus>().on<TaskData>().listen((event) {
      if (event.datas.isEmpty || event.own != groupId()) {
        return;
      }

      List<String> ids = [];
      for (var element in event.datas) {
        if (memberAvatarCache.containsKey(element.username)) {
          memberAvatarCache[element.username]?.avatarPath = element.savePath;
          ids.add(element.username);
        }
      }

      if (ids.isNotEmpty) {
        update(ids);
      }
      Get.find<ChannelService>().updateAvatar(event);
    });
  }

  void disposeP() {
    _subscription?.cancel();
    Get.find<DownLoadService>().quit(groupId());
  }

  StreamSubscription? getDetailInfo();

  void inviteOrKick(SelectContactInfo info, bool invite);

  void updateTitle(String? title) async {
    if (title == null) return;
    if (!await Get.find<NetWorkConnectService>().networkConnected()) {
      toast(L.check_for_updates_failed.tr);
      return;
    }
  }

  void updateDescribe(String? describe) async {
    if (describe == null) return;
    if (!await Get.find<NetWorkConnectService>().networkConnected()) {
      toast(L.check_for_updates_failed.tr);
      return;
    }
  }

  void updateAnnouncement(String? announcement) async {
    if (announcement == null) return;
    if (!await Get.find<NetWorkConnectService>().networkConnected()) {
      toast(L.check_for_updates_failed.tr);
      return;
    }
  }

  void updateAvatar(String? avatarPath) async {
    if (avatarPath == null) return;
    if (!await Get.find<NetWorkConnectService>().networkConnected()) {
      toast(L.check_for_updates_failed.tr);
      return;
    }
  }

  void updateWallpaper(String? wallpaperPath) async {
    if (!await Get.find<NetWorkConnectService>().networkConnected()) {
      toast(L.check_for_updates_failed.tr);
      return;
    }
  }

  void restoreWallpaper();

  void updateNickName(String? nickName) async {
    if (nickName == null) return;
    if (!await Get.find<NetWorkConnectService>().networkConnected()) {
      toast(L.check_for_updates_failed.tr);
      return;
    }
  }

  bool allMute();
  bool inviteLimit();
  bool joinVerify();
  bool contactLimit();
  repeal() {}
  void allMuteOrinviteLimit(ChannelAction ac, bool value) {}
  void changeContactLimit(bool can) {}
  void approvalChange(bool value) async {}
  void getApplyList(ValueChanged<List<MemberInfo>?> callback) {}
  void getBlackList(ValueChanged<List<MemberInfo>?> callback) {}

  /// 更新我的群昵称
  void clearChatHistory(bool check,{bool isToast=true}); //清除聊天记录

  /// 解散或退出频道点击
  void onDismissOrLeave();

  void onAddOrRemoveAdmin(BuildContext context, bool isAdd);

  void selectTransferOwner();

  bool isInvalid(); //群是否合法

  Future<bool> onApproveJoin(String? username) async {
    return true;
  }

  Future<bool> onRemoveBlackList(String? username) async {
    return true;
  }

  bool isChannel() {
    return chatType == ChatType.channelChat;
  }

  bool isOrdinaryChannel() {
    return chatType == ChatType.channelChat && Get.find<SessionController>().channelAttribute(_id.value) == ChannelAttribute.ordinary;
  }

  bool isDaoChannel() {
    return chatType == ChatType.channelChat && Get.find<SessionController>().channelAttribute(_id.value) == ChannelAttribute.dao;
  }

  /// 添加或移除管理员

  void shar(BuildContext context,
      {bool invite = false, List<ContactData>? cs}) {}

  @override
  void onClose() {
    for (var element in _subscriptions) {
      element?.cancel();
    }
    super.onClose();
  }

  /// 获取频道信息
  void getChannelInfo(String groupID, int type) async {
    sharController = SharController();
    chatType = type == ChatType.channelChat.index
        ? ChatType.channelChat
        : ChatType.groupChat;
    _id.value = groupID;
    if (myName.isEmpty) {
      myName = Get.find<AppConfigService>().getUserName() ?? '';
    }

    db = Get.find<AppDatabase>();
    StreamSubscription? subscription;

    /// 频道信息
    subscription = getDetailInfo();
    _subscriptions.add(subscription);

    /// 成员信息
    subscription = db.allGroupMember(groupID).watch().listen((event) async {
      _requestNames.clear();
      rxMemberDatas.clear();
      rxAdminDatas.clear();
      _mapDBContactData.clear();

      var contacts = await db.contactByGroupMember(groupID).get();
      for (var element in contacts) {
        _mapDBContactData[element.username] = element;
      }

      for (int i = 0; i < event.length; i++) {
        var element = event[i];
        if (element.username == myName) {
          rxMyNickname.value = element.displayname ?? '';
          rxMyRole.value = ChannelMemberRole.values[element.role ?? 0];
        }

        rxMemberDatas[element.username] = element;
        var data = toMemberInfo(element);

        if (element.role == ChannelMemberRole.administrator.index) {
          rxAdminDatas[element.username] = element;
        }

        if (i < _initMaxRequestCount || element.username == myName) {
          if (memberAvatarCache.containsKey(element.username)) {
            if (element.displayname != null &&
                element.displayname!.isNotEmpty) {
              memberAvatarCache[element.username]!.nickname =
                  element.displayname;
            }
          } else {
            memberAvatarCache[element.username] = data;
            _requestNames.add(element.username);
          }
        }
      }
      requestMemberInfo();
    });
    _subscriptions.add(subscription);
  }

  /// 成员数据
  MemberInfo getMemberData(String id, {bool request = false}) {
    MemberInfo data = MemberInfo(id, nickname: id);

    if (memberAvatarCache.containsKey(id)) {
      data = memberAvatarCache[id]!;
    } else {
      if (rxMemberDatas.containsKey(id)) {
        data = toMemberInfo(rxMemberDatas[id]!);
      }
    }

    if (request && !_requestNames.contains(id)) {
      _requestNames.add(id);
    }
    if(data.name==myName){
      data.nickname=Get.find<AppConfigService>().getMySelfDisplayName();
    }
    return data;
  }

  bool _strContains(String src, String key) {
    var srcLow = src.toLowerCase();
    var keyLow = key.toLowerCase();
    return srcLow.contains(keyLow);
  }

  Map<String, GroupMemberData> searchMemberDatas(String data) {
    if (data.isEmpty || data.trim().isEmpty) {
      return {};
    }

    Map<String, GroupMemberData> map = {};

    memberAvatarCache.forEach((key, value) {
      if (_strContains(value!.nickname ?? '', data)) {
        map[key] =
            GroupMemberData(id: 1, groupId: '', username: key, memberUuid: '');
      }
    });

    rxMemberDatas.forEach((key, value) {
      var info = toMemberInfo(value);
      if (!map.containsKey(key) && _strContains(info.nickname ?? '', data)) {
        map[key] = value;
      }
    });

    return map;
  }

  /// 请求全部成员数据
  void requestAllMemberData() {
    var nowTime = DateTime.now().millisecondsSinceEpoch;
    int intervals = 60 * 1000; // 1分钟
    bool isReuest =
        (nowTime - _requestAllMemberTime) > intervals ? true : false;

    /// 避免频繁请求，浪费流量
    if (!isReuest) {
      return;
    }
    _requestAllMemberTime = DateTime.now().millisecondsSinceEpoch;

    rxMemberDatas.forEach((key, value) {
      if (!memberAvatarCache.containsKey(key)) {
        _requestNames.add(key);
      }
    });
    requestMemberInfo(downloadAvatar: false);
  }

  /// 自己是否是群主
  bool isMyOwner() {
    return rxMyRole.value == ChannelMemberRole.owner;
  }

  bool isAdminOrOwner() {
    return rxMyRole.value != ChannelMemberRole.ordinary;
  }

  ChannelMemberRole getMemberRole(String username) {
    ChannelMemberRole role = ChannelMemberRole.ordinary;
    if (rxMemberDatas.containsKey(username)) {
      role = ChannelMemberRole.values[rxMemberDatas[username]!.role ?? 0];
    }
    return role;
  }

  /// 群成员邀请或踢出点击
  void onMemberInviteOrKick(BuildContext context, bool invite) async {
    var db = Get.find<AppDatabase>();
    List<ContactData> contactDatas = [];
    if (invite) {
      if (chatType == ChatType.groupChat &&
          rxMemberDatas.length >= Config.privateGroupMaxNum) {
        toast(L.group_num_max.tr);
        return;
      }

      contactDatas = await db.allFriendContact().get();
      contactDatas.removeWhere((item) => (item.type == ContactType.fileHelper));

      for (var element in rxMemberDatas.values) {
        contactDatas.removeWhere((item) => item.username == element.username);
      }
      if (rxMyRole.value == ChannelMemberRole.ordinary) {
        shar(Get.context!, invite: true, cs: contactDatas);
        return;
      }
    } else {
      for (var element in rxMemberDatas.values) {
        var role = ChannelMemberRole.values[element.role ?? 0];
        if (element.username == myName ||
            (rxMyRole.value != ChannelMemberRole.owner &&
                role != ChannelMemberRole.ordinary)) {
          continue;
        }

        MemberInfo info = getMemberData(element.username);

        contactDatas.add(ContactData(
          id: 0,
          username: element.username,
          displayname: info.nickname,
          localname: info.nickname,
          avatarPath: appSupporAbsolutePathToPath(info.avatarPath),
        ));
      }
    }

    var resultData = await showDialog(
      context: Get.context!,
      builder: (_) {
        return SelectContactPage(
            type: SelectContactType.multiple, contactDatas: contactDatas);
      },
    );
    if (resultData == null) {
      return;
    }

    var contacts = resultData as SelectContactInfo;
    if (contacts.contacts.isEmpty) {
      return;
    }

    inviteOrKick(
      contacts,
      invite,
    );
  }

  String groupId() {
    return _id.value;
  }

  /// 消息免打扰变化
  void msgMuteChange(bool value) async {
    Get.find<AppConfigService>().saveUserMessageSilenceState(groupId(), value);
    await Get.find<AppDatabase>().updateSessionSilence(value, groupId());
    Get.find<EventBus>().fire(UpdateMuteChangeEvent(groupId()));
  }

  /// 跳转联系人详情页面
  void onGotoDetail(MemberInfo data, String groupID) async {
    if (data.name == myName) return;

    var contactData =
        await Get.find<AppDatabase>().oneContact(data.name).getSingleOrNull();
    if (contactData == null) {
      Get.to(
        ContactDetailView(
          channelId: groupID,
        ),
        arguments: ContactData(
          id: -1,
          username: data.name,
          displayname: data.nickname,
        ),
      );
    } else {
      Get.to(
          ContactDetailView(
            channelId: groupID,
          ),
          arguments: contactData);
    }
  }

  /// 向服务器获取最新数据
  void requestMemberInfo({bool downloadAvatar = true}) {
    if (_requestNames.isEmpty) {
      return;
    }

    Get.find<ChannelService>().getMemberInfo(
      groupId(),
      _requestNames,
      callback: (values) {
        if (values == null || values.isEmpty) {
          return;
        }

        List<String> updates = [];
        for (var element in values) {
          memberAvatarCache[element.name] = element;
          updates.add(element.name);
        }
        update(updates);
      },
      downloadAvatar: downloadAvatar,
      avatarAsyncNotify: true,
    );
    _requestNames.clear();
  }

  MemberInfo toMemberInfo(GroupMemberData memberData) {
    ContactData? contact = _mapDBContactData[memberData.username];

    String? nickname = memberData.displayname;
    if (nickname == null || nickname.isEmpty) {
      nickname = contact?.state == ContactState.friend.index
          ? contact?.localname ?? contact?.displayname
          : contact?.displayname;
    }

    if (nickname == null || nickname.isEmpty) {
      nickname = memberData.username;
    }

    var data = MemberInfo(
      memberData.username,
      nickname: nickname,
    );

    return data;
  }

  bool? isShowing;
  void showLoading() {
    if (Get.context != null && !isShow()) {
      isShowing = true;
      showDialog(
          context: Get.context!,
          barrierDismissible: false,
          builder: (context) {
            return WillPopScope(
                child: const SimpleDialog(
                  backgroundColor: Colors.transparent,
                  elevation: 0,
//shape: RoundedRectangleBorder(borderRadius: BorderRadius.all(Radius.circular(6))),
                  children: [
                    Center(
                      child: LoadingView(),
                    ),
                  ],
                ),
                onWillPop: () async {
                  isShowing = false;
                  return true;
                });
          });
    }
  }

  bool isShow() {
    return isShowing ?? false;
  }

  AnnouncementMode decodeAnnouncement(String data) {
    AppLogger.d('decodeAnnouncement data=$data');
    AnnouncementMode? mode;
    try {
      mode = AnnouncementMode.fromJson(json.decode(data));
    } catch (e) {
      AppLogger.d('decodeAnnouncement error ${e.toString()}');
    }
    return mode ?? AnnouncementMode();
  }

  String encodeAnnouncement(String data) {
    return json.encode(AnnouncementMode(
            id: _id.value,
            userName: myName,
            time: TimeTask.instance.getNowTime(),
            body: data,
            displayName: rxMyNickname.value)
        .toJson());
  }

  AnnouncementMode getAnnouncementMode() {
    if (announcement.value.userName?.isNotEmpty ?? false) {
      var member = getMemberData(announcement.value.userName!);
      if (member.displayname != member.name) {
        announcement.value.displayName = member.nickname ?? member.displayname;
        announcement.value.avatarPath = member.avatarPath;
      }
      announcement.value.id = _id.value;
    }
    return announcement.value;
  }

  void dismissDialog() {
    if (isShow()) {
      isShowing = false;
      Get.back();
    }
  }

  /// Dao跳转角色管理页面
  void onGotoRoleManagement(){}
}
