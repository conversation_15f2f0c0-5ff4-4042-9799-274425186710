import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/data/enums/enum.dart';
import 'package:flutter_metatel/app/data/services/config_service.dart';
import 'package:flutter_metatel/app/modules/group/admin/administrator_page.dart';
import 'package:flutter_metatel/app/modules/group/details/dao_details_controller.dart';
import 'package:flutter_metatel/app/modules/group/member/member_page.dart';
import 'package:flutter_metatel/app/modules/group/other/join_approval_page.dart';
import 'package:flutter_metatel/app/modules/group/other/modify_page.dart';
import 'package:flutter_metatel/app/widgets/app_bar_cus.dart';
import 'package:flutter_metatel/app/widgets/text_middle_overlow.dart';
import 'package:flutter_metatel/core/languages/l.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:flutter_metatel/core/utils/util.dart';
import 'package:flutter_metatel/core/values/config.dart';
import 'package:flutter_metatel/r.dart';
import 'package:flutter_metatel/routes/pages.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../core/task/group_avatar_task.dart';
import '../../../../core/values/colors.dart';
import '../../../data/services/channel_service.dart';
import '../../../widgets/divider_cus.dart';
import '../../../widgets/mavatar_circle_avatar.dart';
import '../../home/<USER>/session_controller.dart';
import '../../home/<USER>/mine_view.dart';
import '../../mediaDocDetail/media_doc_detail_page.dart';
import '../other/announcement_detail_page.dart';
import '../other/modify_text_page.dart';
import 'channel_details_controller.dart';
import 'group_details_base_controller.dart';
import 'group_details_controller.dart';

class GroupDetailsPage extends StatefulWidget {
  const GroupDetailsPage(
      {Key? key,
      required this.groupID,
      this.avatarPath,
      required this.chatType})
      : super(key: key);
  final String groupID;
  final String? avatarPath;
  final int chatType;

  @override
  State<GroupDetailsPage> createState() => _GroupDetailsPageState();
}

class _GroupDetailsPageState extends State<GroupDetailsPage> {
  late GroupDetailsBaseController _controller;

  bool _msgMute = false;
  final double headAreaHeight = 390.r;
  final double actionAreaHeight = 95.r;
  final double screenPaddingHorizontal = 28.r;
  EdgeInsetsGeometry _defaltPaddding = EdgeInsets.symmetric(horizontal: 23.r);

  @override
  void initState() {
    super.initState();
    _controller = widget.chatType == ChatType.channelChat.index
        ? Get.find<SessionController>().channelAttribute(widget.groupID) ==
                ChannelAttribute.dao
            ? Get.put(DaoDetailsController())
            : Get.put(ChannelDetailsController())
        : Get.put(GroupDetailsController());
    _controller.initP();
    _controller.getChannelInfo(widget.groupID, widget.chatType);
    _controller.avatarPath.value = widget.avatarPath ?? '';
    _msgMute =
        Get.find<AppConfigService>().getUserMessageSilenceState(widget.groupID);
  }

  @override
  void dispose() {
    _controller.disposeP();
    super.dispose();
  }

  void _onShowModalBottomSheet(String sureText,
      {BoolCallBack? onSure, bool checkBox = false, String? checkBoxTitle}) {
    checkBoxTitle ??= L.also_clear_pinned_messages.tr;
    bool check = false;
    showBottomDialogCommonWithCancel(
      context,
      widgets: [
        getBottomSheetItemSimple(
          context,
          sureText,
          radius: 12,
          textColor: Colors.red,
          itemCallBack: () {
            onSure?.call(check);
          },
        ),
        Visibility(
          visible: checkBox,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              StatefulBuilder(
                builder: (_, setState) {
                  return Checkbox(
                    value: check,
                    onChanged: (value) {
                      setState(() {
                        check = value ?? false;
                      });
                    },
                  );
                },
              ),
              Text(
                checkBoxTitle,
                style: TextStyle(
                  fontSize: 12.sp,
                  color: AppColors.colorFF249ED9,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// 构建头像、操作按钮部分
  Widget _buildAvatarButton() {
    /// 头部分
    return Obx(() {
      // bool state = false;
      bool isOwner = false;
      if (_controller.isInvalid()) {
        isOwner = _controller.isAdminOrOwner();
        // state = isOwner && _controller.isChannel();
      }
      Widget? attrIcon = _buildChannelAttrIcon();
      return SizedBox(
        height: headAreaHeight,
        child: Stack(
          children: [
            /// 颜色背景部分
            Container(
              height: headAreaHeight - (actionAreaHeight / 2),
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: [
                    AppColors.primaryBgColor2,
                    AppColors.primaryBgColor1,
                  ],
                  begin: Alignment(-1.5, -0.7),
                  end: Alignment(1, 0.5),
                ),
                borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(35.r),
                  bottomRight: Radius.circular(35.r),
                ),
              ),
            ),
            Column(
              children: [
                SizedBox(
                    height:
                        kToolbarHeight + MediaQuery.of(context).padding.top),

                // 间隔
                // SizedBox(height: 8.r),
                // 群标题部分
                Expanded(
                  child: Center(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // 头像
                        GestureDetector(
                          onTap: isOwner
                              ? () {
                                  if (_controller.isChannel()) {
                                    showBottomDialogCommonWithCancel(context,
                                        widgets: _buildItemWidgets(context));
                                  } else {
                                    // showBottomDialogCommonWithCancel(context,
                                    //     widgets: _buildItemWidgets(context));
                                  }
                                }
                              : null,
                          child: buildChatAvatarWithAttr(
                              widget.chatType, widget.groupID,
                              diameter: 105,
                              imagePath: _controller.avatarPath.value,
                              iconSize: 25.r),
                        ),
                        SizedBox(height: 8.r),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            // 标题
                            Flexible(
                              child: Text(
                                _controller.title.value,
                                style: TextStyle(
                                  fontSize: 16.sp,
                                  color: Colors.white,
                                ),
                              ),
                            ),
                            if (attrIcon != null) attrIcon,
                            // 编辑按钮
                            Visibility(
                              visible: isOwner,
                              child: GestureDetector(
                                onTap: () {
                                  Get.to(ModifyPage(
                                          text: _controller.title.value,
                                          op: Options.title))
                                      ?.then((value) {
                                    _controller.updateTitle(value);
                                  });
                                },
                                child: Padding(
                                  padding: EdgeInsets.only(left: 5.w),
                                  child: Image.asset(
                                    'assets/images/edit.png',
                                    width: 17.w,
                                    height: 14.h,
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
                // const Spacer(),
                _buildActionButton(_controller.isMyOwner())
              ],
            ),
          ],
        ),
      );
    });
  }

  Widget? _buildChannelAttrIcon() {
    Widget? w;
    // switch(_controller.channelAttr.value){
    //   case ChannelAttribute.vip:
    //     w = Image.asset(
    //       R.icoChannelVip,
    //       width: 20.r,
    //       height: 20.r,
    //     );
    //     break;
    //   case ChannelAttribute.ordinary:
    //     break;
    //   case ChannelAttribute.dao:
    //     w = Image.asset(
    //       R.icoDao,
    //       width: 20.r,
    //       height: 20.r,
    //     );
    //     break;
    // }
    return w;
  }

  List<Widget> _buildItemWidgets(BuildContext context) {
    List<Widget> widgets = [
      getBottomSheetItemSimple(context, L.capture_picture.tr,
          itemCallBack: () =>
              GroupAvatarTask.getImageTakePhoto(_controller.updateAvatar)),
      getBottomSheetItemSimple(context, L.get_picture_from_phone.tr,
          itemCallBack: () =>
              GroupAvatarTask.chooseImage(_controller.updateAvatar, context)),
    ];
    return widgets;
  }

  /// 构建操作按钮部分
  Widget _buildActionButton(bool owner) {
    String text = L.quit_group.tr;
    if (owner) {
      text = L.dissolution_group.tr;
    }

    return Padding(
      padding: _defaltPaddding,
      child: Material(
        elevation: 2,
        borderRadius: BorderRadius.circular(25.r),
        child: Container(
          height: actionAreaHeight,
          decoration: BoxDecoration(
            color: AppColors.white,
            borderRadius: BorderRadius.circular(25.r),
          ),
          child: Row(
            // mainAxisAlignment: _controller.isChannel()
            //     ? MainAxisAlignment.spaceEvenly
            //     : MainAxisAlignment.spaceAround,
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              // 聊天按钮
              _buildChildButton(
                R.icoGroupMsg,
                L.chat.tr,
                onTap: () => Get.back(),
              ),
              // 语音按钮
              _buildChildButton(
                R.icoGroupPhone,
                L.audio.tr,
                onTap: () => toast(L.not_yet_develop.tr),
              ),
              // 视频按钮
              _buildChildButton(
                R.icoGroupVideo,
                L.label_meeting.tr,
                onTap: () => toast(L.not_yet_develop.tr),
              ),
              // 退出/解散按钮
              if (!_controller.isChannel())
                _buildChildButton(
                  owner ? R.icoGroupDissolution : R.icoGroupQuit,
                  text,
                  onTap: _controller.isInvalid()
                      ? () {
                          _onShowModalBottomSheet(text,
                              onSure: (check) =>
                                  _controller.onDismissOrLeave());
                        }
                      : null,
                ),
              // 分享按钮
              if (_controller.isChannel())
                _buildChildButton(
                  R.icoGroupShare,
                  L.share_share_to.tr,
                  onTap: () => _controller.shar(context),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildChildButton(
    String image,
    String text, {
    GestureTapCallback? onTap,
  }) {
    // Widget child = Container(
    //   width: 60.r,
    //   height: 60.r,
    //   padding: EdgeInsets.all(4.r),
    //   decoration: BoxDecoration(
    //     borderRadius: BorderRadius.circular(12.r),
    //     color: Colors.white,
    //   ),
    //   child: Column(
    //     mainAxisSize: MainAxisSize.min,
    //     children: [
    //       Image.asset(
    //         image,
    //         width: 26.w,
    //         height: 26.h,
    //       ),
    //       Text(
    //         text,
    //         overflow: TextOverflow.ellipsis,
    //         style: TextStyle(
    //           fontSize: 12.sp,
    //         ),
    //       ),
    //     ],
    //   ),
    // );
    Widget child = Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        Image.asset(
          image,
          width: 26.r,
          height: 26.r,
        ),
        Text(
          text,
          overflow: TextOverflow.ellipsis,
          style: TextStyle(
            fontSize: 12.sp,
          ),
        ),
      ],
    );
    return GestureDetector(
      onTap: onTap,
      child: child,
    );
  }

  /// 构建群成员邀请或踢出按钮
  Widget _buildMemberInviteOrKick(bool invite, bool isOrdinary) {
    return GestureDetector(
      onTap: () => _controller.onMemberInviteOrKick(context, invite),
      child: Container(
        alignment: Alignment.topCenter,
        child: MAvatarCircle(
          imagePath: invite
              ? 'assets/images/group_add.png'
              : 'assets/images/group_delete.png',
          chatType: ChatType.other,
        ),
      ),
    );
  }

  /// 成员部分
  Widget _buildMembers() {
    return Obx(() {
      var memberDatas = _controller.rxMemberDatas.values.toList();

      int crossAxisCount = 5;
      int maxRow = 1;
      int maxItemCount = crossAxisCount * maxRow;

      bool state = _controller.isInvalid();

      bool showButton = false;
      bool isOrdinary =
          _controller.rxMyRole.value == ChannelMemberRole.ordinary;
      bool inviteLimit = _controller.inviteLimit();
      if (!isOrdinary || (inviteLimit)) {
        showButton = true;
      }

      int itemCount = memberDatas.length;
      int addCount = isOrdinary
          ? 1
          : _controller.isDaoChannel()
              ? 1
              : 2;
      if (state && itemCount > 0 && showButton) {
        itemCount += addCount;
      }
      AppLogger.d(
          '_buildMembers   isOrdinary = $isOrdinary inviteLimit =$inviteLimit itemCount=$itemCount');

      itemCount = itemCount > maxItemCount ? maxItemCount : itemCount;

      var totalNumber = _controller.isChannel()
          ? _controller.memberCount.value
          : memberDatas.length;
      return Container(
        padding: _defaltPaddding,
        color: Colors.white,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 成员人数部分
            GestureDetector(
              onTap: () {
                if (_controller.contactLimit() && isOrdinary) {
                  return;
                }
                _controller.requestAllMemberData();
                Get.to(MemberPage(groupID: _controller.groupId()));
              },
              child: Container(
                color: Colors.white,
                height: 54.r,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Text(
                          L.group_member_group_member.tr,
                          style: TextStyle(
                            fontSize: 16.sp,
                          ),
                        ),
                        const Spacer(),
                        Visibility(
                            visible: totalNumber > 0,
                            child: Text(
                              L.members_num.trParams(
                                {'num': '$totalNumber'},
                              ),
                              style: TextStyle(fontSize: 14.sp),
                            )),
                        const Icon(Icons.chevron_right, size: 20),
                      ],
                    ),
                    Obx(() {
                      return Text(
                        '(${L.up_to_people.trParams(
                          {'number': _controller.rxMaxCount.value},
                        )})',
                        style: TextStyle(
                          color: AppColors.txtColor,
                          fontSize: 14.sp,
                        ),
                      );
                    }),
                  ],
                ),
              ),
            ),
            const SizedBox(
              height: 10,
            ),
            // 成员网格部分
            SizedBox(
              height: 70.r,
              child: ListView.separated(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                scrollDirection: Axis.horizontal,
                itemBuilder: (BuildContext context, int index) {
                  Widget child;
                  if (state && showButton && index == itemCount - addCount) {
                    child = _buildMemberInviteOrKick(true, isOrdinary);
                  } else if (state &&
                      showButton &&
                      !isOrdinary &&
                      index == itemCount - 1 &&
                      !_controller.isDaoChannel()) {
                    child = _buildMemberInviteOrKick(false, isOrdinary);
                  } else {
                    String userName = memberDatas[index].username;

                    return GetBuilder<GroupDetailsBaseController>(
                      id: userName,
                      builder: (control) {
                        MemberInfo info = control.getMemberData(userName);
                        var role = control.getMemberRole(userName);
                        List<String> tags = [];
                        if (control.isDaoChannel() &&
                            control is DaoDetailsController) {
                          tags = control.getMemberTags(userName);
                        }

                        return GestureDetector(
                          onTap: () =>
                              _controller.onGotoDetail(info, widget.groupID),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              SizedBox(
                                height: 50.r,
                                width: 50.r,
                                child: Stack(
                                  children: [
                                    MAvatarCircle(
                                      diameter: 45,
                                      text: info.nickname,
                                      imagePath: info.avatarPath,
                                    ),
                                    Positioned(
                                      right: .0,
                                      bottom: .0,
                                      child: Visibility(
                                        visible:
                                            role == ChannelMemberRole.owner ||
                                                    role ==
                                                        ChannelMemberRole
                                                            .administrator ||
                                                    tags.isNotEmpty
                                                ? true
                                                : false,
                                        child: control.isDaoChannel()
                                            ? role ==
                                                        ChannelMemberRole
                                                            .ordinary &&
                                                    tags.isNotEmpty &&
                                                    control
                                                        is DaoDetailsController
                                                ? Container(
                                                    width: 25.w,
                                                    height: 25.w,
                                                    decoration: BoxDecoration(
                                                      color: control
                                                                  .getTagColor(
                                                                      tags[
                                                                          0]) ==
                                                              null
                                                          ? null
                                                          : Color(control
                                                              .getTagColor(
                                                                  tags[0])!),
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              12.5.w),
                                                      border: Border.all(
                                                        color: Colors.white,
                                                        width: 2.r,
                                                      ),
                                                    ),
                                                    child: Image.asset(
                                                      R.icoGroupDaoTag,
                                                      width: 25.w,
                                                      height: 25.w,
                                                    ),
                                                  )
                                                : Image.asset(
                                                    role ==
                                                            ChannelMemberRole
                                                                .administrator
                                                        ? R.icoGroupDaoManager
                                                        : R.icoGroupDaoLeader,
                                                    width: 25.w,
                                                    height: 25.w,
                                                  )
                                            : Image.asset(
                                                role ==
                                                        ChannelMemberRole
                                                            .administrator
                                                    ? R.icoGroupAdmin
                                                    : R.icoGroupOwner,
                                                width: 25.w,
                                                height: 25.w,
                                              ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              Flexible(
                                child: Container(
                                  constraints: BoxConstraints(maxWidth: 55.r),
                                  child: MiddleText(
                                    info.nickname ?? '',
                                    WXTextOverflow.ellipsisMiddle,
                                    style: TextStyle(
                                      fontSize: 12.sp,
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        );
                      },
                    );
                  }
                  return child;
                },
                itemCount: itemCount,
                separatorBuilder: (BuildContext context, int index) {
                  return const SizedBox(
                    width: 20,
                  );
                },
              ),
            ),
          ],
        ),
      );
    });
  }

  /// 构建群信息部分
  Widget _buildInfo() {
    double defaultInfoHeight = 50.r;
    return Container(
      color: Colors.white,
      padding:
          EdgeInsets.only(left: 25.w, right: 25.w, top: 10.h, bottom: 10.h),
      child: Obx(() {
        var myNickname = _controller.rxMyNickname.value;
        // var owner = _controller.isMyOwner();
        var owner = _controller.isAdminOrOwner();

        bool state = _controller.isInvalid();

        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const DividerCus(),
            // 我的群昵称
            Visibility(
              visible: _controller.isChannel(),
              child: GestureDetector(
                onTap: state
                    ? () {
                        Get.to(ModifyPage(
                                text: myNickname, op: Options.myGroupNickname))
                            ?.then((value) {
                          _controller.updateNickName(value);
                        });
                      }
                    : null,
                child: Container(
                  color: Colors.white,
                  height: 50.r,
                  child: Row(
                    children: [
                      Text(
                        L.chat_info_my_group_nickname.tr,
                        style: TextStyle(
                          color: Colors.black,
                          fontSize: 16.sp,
                        ),
                      ),
                      // const Spacer(),
                      const SizedBox(
                        width: 10,
                      ),
                      Expanded(
                          child: MiddleText(
                        myNickname,
                        WXTextOverflow.ellipsisMiddle,
                        textAlign: TextAlign.right,
                        style: TextStyle(
                          color: AppColors.colorFF656565,
                          fontSize: 14.sp,
                        ),
                      )),
                      const Icon(Icons.chevron_right, size: 20),
                    ],
                  ),
                ),
              ),
            ),
            if (_controller.isChannel()) const DividerCus(),
            // 群描述
            GestureDetector(
              onTap: owner && state
                  ? () {
                      Get.to(ModifyPage(
                              text: _controller.describe.value,
                              op: Options.describe))
                          ?.then((value) {
                        _controller.updateDescribe(value);
                      });
                    }
                  : () {
                      if (_controller.describe.value.isNotEmpty) {
                        Get.to(ModifyTextPage(
                                text: _controller.describe.value,
                                op: Options.describe))
                            ?.then((value) {});
                      }
                    },
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Expanded(
                    child: Container(
                      color: Colors.white,
                      padding: const EdgeInsets.only(top: 10, bottom: 10),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            L.chat_info_group_describe.tr,
                            style: TextStyle(
                              color: Colors.black,
                              fontSize: 16.sp,
                            ),
                          ),
                          const SizedBox(height: 10),
                          Flexible(
                            child: Text(
                              _controller.describe.value,
                              maxLines: 4,
                              overflow: TextOverflow.ellipsis,
                              style: TextStyle(
                                color: AppColors.colorFF7F7F7F,
                                fontSize: 13.sp,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  Visibility(
                    visible: owner,
                    child: const Icon(Icons.chevron_right, size: 20),
                  )
                ],
              ),
            ),
            if (_controller.isChannel()) const DividerCus(),
            // 群公告
            Flexible(
              child: GestureDetector(
                onTap: () {
                  var isAdmin = _controller.isAdminOrOwner();
                  if (isAdmin ||
                      (_controller.announcement.value.body?.isNotEmpty ??
                          false)) {
                    Get.to(AnnouncementDetailPage(
                      mode: _controller.getAnnouncementMode(),
                      op: Options.announcement,
                      isAdmin: isAdmin,
                    ))?.then((value) {
                      _controller.updateAnnouncement(value);
                    });
                  }
                },
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Expanded(
                      child: Container(
                        color: Colors.white,
                        padding: const EdgeInsets.only(top: 10, bottom: 10),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              L.chat_info_group_announcement.tr,
                              style: TextStyle(
                                color: Colors.black,
                                fontSize: 16.sp,
                              ),
                            ),
                            const SizedBox(height: 10),
                            Flexible(
                              child: Text(
                                _controller.announcement.value.body ?? '',
                                maxLines: 4,
                                overflow: TextOverflow.ellipsis,
                                style: TextStyle(
                                  color: AppColors.colorFF7F7F7F,
                                  fontSize: 13.sp,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    Visibility(
                      visible: owner,
                      child: const Icon(Icons.chevron_right, size: 20),
                    )
                  ],
                ),
              ),
            ),
          ],
        );
      }),
    );
  }

  Widget _buildSetRow(String text,
      {Widget? right, GestureTapCallback? onTap, Color? textColor}) {
    Rx<Color> backColor = AppColors.transparent.obs;

    return Obx(() => Material(
        color: backColor.value,
        child: InkWell(
          radius: 1,
          onTap: () {
            ItemClickFunction(startFunction: () {
              backColor.value = AppColors.colorFFF2F2F2;
            }, endFunction: () {
              if (onTap != null) {
                onTap.call();
              }
              backColor.value = AppColors.white;
            });
          },
          child: Container(
            height: 50.r,
            padding: const EdgeInsets.only(
              left: 25,
              right: 25,
            ).r,
            child: Row(
              children: [
                Text(
                  text,
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: textColor ?? Colors.black,
                  ),
                ),
                const Spacer(),
                right ?? const Spacer(),
              ],
            ),
          ),
        )));
  }

  /// 构建群管理
  Widget _buildManager() {
    return Obx(() {
      var role = _controller.rxMyRole.value;
      var mute = _controller.allMute();
      var inviteLimit = _controller.inviteLimit();
      var contactLimit = _controller.contactLimit();
      var joinVerify = _controller.joinVerify();
      var applyCount = _controller.rxApplyCount.value;
      double horizontalPadding = 25;

      double size = 16;
      String applyCountText = '$applyCount';
      if (applyCount > 99) {
        applyCountText = '99+';
        size = 20;
      }

      return (role == ChannelMemberRole.ordinary || !_controller.isChannel())
          ? SizedBox.shrink()
          : _controller.isDaoChannel()
              ? Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    /// Dao角色管理
                    _buildSetRow(
                      L.role_management.tr,
                      right: const Icon(Icons.chevron_right, size: 20),
                      onTap: () {
                        _controller.onGotoRoleManagement();
                      },
                    ),
                    const DividerCus(),
                  ],
                )
              : Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const SizedBox(height: 10),
                    Container(
                      decoration: const BoxDecoration(color: AppColors.white),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Container(
                            alignment: Alignment.centerLeft,
                            padding: EdgeInsets.only(
                                left: 25.w, right: 25.w, top: 10.h),
                            child: Text(
                              L.group_management.tr,
                              style: TextStyle(
                                fontSize: 13.sp,
                              ),
                            ),
                          ),
                          settingsWidgetItemSimple(
                            L.all_mute.tr,
                            horizontalPadding: horizontalPadding,
                            onTapEnable: false,
                            rightWidget: CupertinoSwitch(
                              value: mute,
                              onChanged: (value) =>
                                  _controller.allMuteOrinviteLimit(
                                      ChannelAction.allMute, value),
                            ),
                          ),
                          const DividerCus(),
                          settingsWidgetItemSimple(
                            L.invite_limit.tr,
                            horizontalPadding: horizontalPadding,
                            onTapEnable: false,
                            rightWidget: CupertinoSwitch(
                              value: !inviteLimit,
                              onChanged: (value) =>
                                  _controller.allMuteOrinviteLimit(
                                      ChannelAction.inviteLimit, !value),
                            ),
                          ),
                          const DividerCus(),
                          settingsWidgetItemSimple(
                            L.contact_limit.tr,
                            horizontalPadding: horizontalPadding,
                            onTapEnable: false,
                            rightWidget: CupertinoSwitch(
                              value: contactLimit,
                              onChanged: (value) =>
                                  _controller.changeContactLimit(value),
                            ),
                          ),
                          const DividerCus(),
                          settingsWidgetItemSimple(
                            L.join_group_audit.tr,
                            onTapEnable: false,
                            horizontalPadding: horizontalPadding,
                            rightWidget: CupertinoSwitch(
                              value: joinVerify,
                              onChanged: (value) =>
                                  _controller.approvalChange(value),
                            ),
                          ),
                          const DividerCus(),
                          if (role == ChannelMemberRole.owner)
                            settingsWidgetItemSimple(
                              L.admin.tr,
                              onTapEnable: true,
                              horizontalPadding: horizontalPadding,
                              rightWidget:
                                  const Icon(Icons.chevron_right, size: 20),
                              onTapCallBack: () {
                                Get.to(const AdministratorPage());
                              },
                            ),
                          if (role == ChannelMemberRole.owner)
                            const DividerCus(),
                          settingsWidgetItemSimple(
                            L.join_channel_apply.tr,
                            horizontalPadding: horizontalPadding,
                            onTapEnable: true,
                            rightWidget: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Visibility(
                                  visible: applyCount > 0 ? true : false,
                                  child: SizedBox(
                                    width: size,
                                    height: size,
                                    child: CircleAvatar(
                                      backgroundColor: AppColors.colorFFCD3A3A,
                                      child: Text(
                                        applyCountText,
                                        style: const TextStyle(
                                          color: Colors.white,
                                          fontSize: 10,
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                                const Icon(Icons.chevron_right, size: 20),
                              ],
                            ),
                            onTapCallBack: () {
                              Get.to(const JoinApprovalPage());
                            },
                          ),
                          if (role == ChannelMemberRole.owner) ...[
                            const DividerCus(),
                            settingsWidgetItemSimple(L.group_owner_transfer.tr,
                                onTapEnable: true,
                                horizontalPadding: horizontalPadding,
                                rightWidget: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    const Icon(Icons.chevron_right, size: 20),
                                  ],
                                ), onTapCallBack: () {
                              _controller.selectTransferOwner();
                            }),
                          ],
                          if (role == ChannelMemberRole.owner &&
                              Config.isOversea)
                            const DividerCus(),
                          settingsWidgetItemSimple(
                            L.blacklist.tr,
                            onTapEnable: true,
                            horizontalPadding: horizontalPadding,
                            rightWidget:
                                const Icon(Icons.chevron_right, size: 20),
                            onTapCallBack: () {
                              Get.toNamed(Routes.ChannelBlackListPage,
                                  arguments: _controller.groupId());
                            },
                          ),
                          if (role == ChannelMemberRole.owner)
                            const DividerCus(),
                          settingsWidgetItemSimple(
                            L.other_repeal.tr,
                            horizontalPadding: horizontalPadding,
                            onTapEnable: true,
                            rightWidget:
                                const Icon(Icons.chevron_right, size: 20),
                            onTapCallBack: () {
                              _controller.repeal();
                            },
                          ),
                          const DividerCus(),
                        ],
                      ),
                    ),
                  ],
                );
    });
  }

  /// 构建设置部分
  Widget _buildSet() {
    return Container(
      color: Colors.white,
      padding: EdgeInsets.only(left: 0.w, bottom: 10.h, top: 10.h),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (_controller.isOrdinaryChannel())
            Container(
              alignment: Alignment.centerLeft,
              padding: const EdgeInsets.only(
                left: 25,
                right: 25,
              ).r,
              child: Text(
                L.personalization_and_others.tr,
                style: TextStyle(
                  fontSize: 14.sp,
                ),
              ),
            ),
          // 消息免打扰
          _buildSetRow(
            L.chat_info_message_no_disturbing.tr,
            onTap: () {},
            right: StatefulBuilder(
              builder: (context, setState) {
                return CupertinoSwitch(
                  value: _msgMute,
                  onChanged: (value) {
                    setState(() {
                      _msgMute = value;
                      _controller.msgMuteChange(value);
                    });
                  },
                );
              },
            ),
          ),
          const DividerCus(),
          // 媒体和文档
          _buildSetRow(
            L.media_links_and_docs.tr,
            right: const Icon(Icons.chevron_right, size: 20),
            onTap: () {
              Get.to(MediaDocDetailPage(
                userName: widget.groupID,
                dispName: _controller.title.value,
                chatType: _controller.isChannel()
                    ? ChatType.channelChat.index
                    : ChatType.groupChat.index,
              ));
            },
          ),
          const DividerCus(),
          if (_controller.isMyOwner() && _controller.isChannel())
            Padding(
              padding: const EdgeInsets.only(
                left: 25,
                right: 25,
              ).r,
              child: const DividerCus(),
            ),
          // 清除聊天记录
          _buildSetRow(
            L.clear_dialog.tr,
            textColor: AppColors.colorFFCD3A3A,
            onTap: () {
              showBottomDialogCommonWithCancel(
                context,
                widgets: _buildBottomSheetClearChatWidgets(),
              );
            },
          ),
          // 解散或退出私密群或频道
          if (_controller.isChannel())
            Padding(
              padding: const EdgeInsets.only(
                left: 25,
                right: 25,
              ).r,
              child: const DividerCus(),
            ),
          if (_controller.isChannel())
            Obx(
              () => _buildSetRow(
                _controller.isOrdinaryChannel() && _controller.isMyOwner()
                    ? L.dissolution_group.tr
                    : L.quit_group.tr,
                textColor: AppColors.colorFFCD3A3A,
                onTap: _controller.isInvalid()
                    ? () {
                        _onShowModalBottomSheet(
                          _controller.isOrdinaryChannel() &&
                                  _controller.isMyOwner()
                              ? L.dissolution_group.tr
                              : L.quit_group.tr,
                          onSure: (check) {
                            _controller.clearChatHistory(check, isToast: false);
                            _controller.onDismissOrLeave();
                          },
                        );
                      }
                    : null,
              ),
            ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      backgroundColor: Colors.transparent,
      appBar: AppBarCommon().build(
        context,
        title: L.chat_info_group_chat_setting.tr,
        backgroundColor: Colors.transparent,
        titleColor: AppColors.white,
        elevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: AppColors.white,
          ),
          onPressed: () {
            Get.back();
          },
        ),
      ),
      body: Container(
        color: AppColors.white,
        width: 1.sw,
        height: 1.sh,
        child: ListView(
          padding: EdgeInsets.zero,
          physics: const ClampingScrollPhysics(),
          children: [
            _buildAvatarButton(),
            SizedBox(height: 30.r),
            _buildMembers(),
            SizedBox(height: 20.r),
            _buildInfo(),
            _buildManager(),
            SizedBox(height: 20.r),
            _buildSet(),
          ],
        ),
      ),
    );
  }

  List<Widget> _buildBottomSheetClearChatWidgets() {
    bool check = false;
    return [
      SizedBox(height: 32.r),
      Text(
        L.clear_dialog.tr,
        style: TextStyle(
          fontSize: 17.sp,
          fontWeight: FontWeight.bold,
          color: Colors.black,
        ),
      ),
      SizedBox(height: 18.r),
      Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          StatefulBuilder(
            builder: (_, setState) {
              return Checkbox(
                value: check,
                onChanged: (value) {
                  setState(() {
                    check = value ?? false;
                  });
                },
              );
            },
          ),
          Text(
            L.also_clear_pinned_messages.tr,
            style: TextStyle(
              fontSize: 14.sp,
              color: Colors.black,
            ),
          ),
        ],
      ),
      SizedBox(height: 36.r),
      ElevatedButton(
        onPressed: () {
          Get.back();
          _controller.clearChatHistory(check);
        },
        child: Text(L.clear_now.tr),
      ),
    ];
  }
}
