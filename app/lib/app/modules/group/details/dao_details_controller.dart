
import 'package:flutter_metatel/app/modules/group/details/channel_details_controller.dart';
import 'package:get/get.dart';

import '../../../../routes/pages.dart';


class DaoDetailsController extends ChannelDetailsController {
  
  /// 跳转角色管理页面
  @override
  void onGotoRoleManagement() {
    Get.toNamed(Routes.DaoRoleManagementView);
  }

  // @override
  // void onDismissOrLeave() async {
  //   super.clearChatHistory(true,isToast: false);
  //   super.onDismissOrLeave();
  // }

  /// Dao目前没有这些设置,返回默认关闭 ------
  /// 全体禁言
  @override
  bool allMute() {
    return false;
  }

  /// 邀请限制
  @override
  bool inviteLimit() {
    return false;
  }

  /// 进群审核
  @override
  bool joinVerify() {
    return false;
  }

  /// 加好友限制
  @override
  bool contactLimit() {
    return false;
  }

  /// 修改群壁纸
  @override
  void updateWallpaper(String? wallpaperPath) async {}

  /// 恢复默认群壁纸
  @override
  void restoreWallpaper() async {}

  /// 群主转让
  @override
  void selectTransferOwner() async {}

  /// Dao目前没有这些设置 ------
  /// 加好友限制
  // @override
  // void changeContactLimit(bool can){
  // }

  /// 全体禁言/邀请限制设置
  // @override
  // void allMuteOrinviteLimit(ChannelAction ac, bool value) async {
  // }

  /// 进频道审核变化
  // @override
  // void approvalChange(bool value) async {
  // }

  /// 获取加入申请列表
  // @override
  // void getApplyList(ValueChanged<List<MemberInfo>?> callback) async {
  // }

  /// 审批加入
  // @override
  // Future<bool> onApproveJoin(String? username) async {
  //   return true;
  // }

  /// 获取黑名单列表
  // @override
  // void getBlackList(ValueChanged<List<MemberInfo>?> callback) async {
  // }

  /// 移除黑名单
  // @override
  // Future<bool> onRemoveBlackList(String? username) async {
  //   return true;
  // }

  /// 邀请和踢除成员
  // @override
  // void inviteOrKick(SelectContactInfo info, bool invite) async {
  // }

  ///群撤回
  // @override
  // repeal() async {
  // }

}
