import 'package:flutter/material.dart';
import 'package:flutter_metatel/core/languages/l.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../core/values/colors.dart';
import '../../../widgets/divider_cus.dart';
import 'modify_page.dart';

class ModifyTextPage extends StatefulWidget {
  const ModifyTextPage({Key? key, this.text, required this.op})
      : super(key: key);
  final String? text;
  final Options op;

  @override
  State<ModifyTextPage> createState() => _ModifyTextPageState();
}

class _ModifyTextPageState extends State<ModifyTextPage> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    String appBarText = '';
    // String bottomHintText = '';
    // int? maxLength;

    switch (widget.op) {
      case Options.title:
        appBarText = L.chat_info_group_chat_name.tr;
        // bottomHintText = L
        //     .other_members_of_the_group_are_notified_when_the_group_name_is_changed
        //     .tr;
        // maxLength = 30;
        break;
      case Options.describe:
        appBarText = L.chat_info_group_chat_describe.tr;
        // bottomHintText = L
        //     .other_members_of_the_group_are_notified_when_the_group_describe_is_changed
        //     .tr;
        // maxLength = 240;
        break;
      case Options.myGroupNickname:
        appBarText = L.chat_info_my_group_nickname.tr;
        // maxLength = 20;
        break;
      case Options.announcement:
        appBarText = L.chat_info_group_chat_announcement.tr;
        // bottomHintText = L
        //     .other_members_of_the_group_are_notified_when_the_group_announcement_is_changed
        //     .tr;
        // maxLength = 240;
        break;
      default:
    }

    return Scaffold(
      appBar: AppBar(
        title: Text(appBarText),
        centerTitle: true,
      ),
      body: Container(
        color: Colors.white,
        child: Column(
          children: [
            const SizedBox(height: 20,),
            Container(
              height: 0.7.sh,
              width: 1.sw,
              padding: EdgeInsets.all(16.r),
              child: SingleChildScrollView(
                physics: const BouncingScrollPhysics(),
                child: Text(
                  widget.text ?? '',
                  style: TextStyle(
                      fontSize: 16.sp,
                      height: 1.5),
                ),
              ),
            ),
            const DividerCus(),
          ],
        ),
      ),
    );
  }
}
