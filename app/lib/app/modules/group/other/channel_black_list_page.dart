import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/data/providers/db/database.dart';
import 'package:flutter_metatel/app/modules/group/other/join_approval_page.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../core/languages/l.dart';
import '../../../widgets/mavatar_circle_avatar.dart';
import '../details/group_details_base_controller.dart';

class ChannelBlackListPage extends StatefulWidget {
  const ChannelBlackListPage({Key? key}) : super(key: key);

  @override
  State<ChannelBlackListPage> createState() => _ChannelBlackListPageState();
}

class _ChannelBlackListPageState extends State<ChannelBlackListPage> {
  final _controller = Get.find<GroupDetailsBaseController>();
  final _mapInfo = <String, ApprovalInfo>{}.obs;
  final _currentMapInfo = <String, ApprovalInfo>{};
  bool _currentLoaded = false;
  @override
  void initState() {
    super.initState();
    _controller.getBlackList((values) {
      if (values?.isNotEmpty == true) {
        for (var element in values!) {
          if (_currentMapInfo.containsKey(element.name)) {
            _currentMapInfo[element.name]!.data = element;
          } else {
            _currentMapInfo[element.name] = ApprovalInfo(data: element);
          }
        }
        _currentLoaded = true;
        _onTextChange(key);
       }
    });
  }

  Widget _buildItem(ApprovalInfo info) {
    var text =
        L.added_blacklist.trParams({'name': '${info.data?.nickname}'});

    String sure = info.state ? L.removed.tr : L.remove.tr;

    return ListTile(
      onTap: () {
        if (info.data != null) {
          _controller.onGotoDetail(info.data!, _controller.groupId());
        }
      },
      leading: MAvatarCircle(
        diameter: 49,
        imagePath: info.data?.avatarPath,
        text: info.data?.nickname ?? info.data?.displayname,
      ),
      title: Text(
        text,
        overflow: TextOverflow.ellipsis,
      ),
      trailing: TextButton(
        style: ButtonStyle(
          //圆角
          shape: MaterialStateProperty.all(
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(13))),
          //边框
          side: MaterialStateProperty.all(
              const BorderSide(color: Color(0xffF2F2F2), width: 1)),
        ),
        child: Text(
          sure,
          style: TextStyle(
            fontSize: 12.sp,
            color: Colors.black,
          ),
        ),
        onPressed: () {
          if (info.state) return;

          _controller.onRemoveBlackList(info.data?.name).then((value) {
            if (value) {
              _mapInfo[info.data?.name]?.state = true;
              _currentMapInfo[info.data?.name]?.state = true;
              setState(() {});
            }
          });
        },
      ),
    );
  }
  String? key;
  /// 文本变化监听
  void _onTextChange(String? text) {
    if (text?.isNotEmpty ?? false) {
      key = text;
      if(_currentLoaded){
        _mapInfo.value = searchMemberDatas(key??'');
      }
    } else {
      key = null;
      _mapInfo.value = _currentMapInfo;
     }
  }
  bool _strContains(String src, String key,String name) {
    var srcLow = src.toLowerCase();
    var keyLow = key.toLowerCase();
    bool isSameName = false;
    if(key.length>=44){
      isSameName = name.toLowerCase().contains(keyLow);
      AppLogger.d('searchMemberDatas _currentMapInfo=${name.toLowerCase()}');
      AppLogger.d('searchMemberDatas _currentMapInfo1=${keyLow}');

    }
    return srcLow.contains(keyLow) || isSameName;
  }

  Map<String, ApprovalInfo> searchMemberDatas(String data) {
    if (data.isEmpty || data.trim().isEmpty) {
      return {};
    }
    Map<String, ApprovalInfo> map = {};
    _currentMapInfo.forEach((key, value) {
      if (_strContains(value.data?.nickname ?? '', data,value.data?.name ?? '')) {
        map[key] =value;
      }
    });

    return map;
  }

  Widget _buildBody() {
    var datas = _mapInfo.values.toList();
    return Column(
      children: [
        Container(
          height: 64.r,
          padding:
              const EdgeInsets.only(left: 15, right: 15, top: 10, bottom: 10).r,
          child: TextField(
            autofocus: false,
            onChanged: (text) => _onTextChange(text),
            textAlign: TextAlign.start,
            style: TextStyle(fontSize: 16.sp),
            textAlignVertical: TextAlignVertical.center,
            decoration: InputDecoration(
              hintText: L.searbar_hint_search.tr,
              // 设置后，提升文本居中
              contentPadding: EdgeInsets.zero,
              prefixIcon: const Icon(
                Icons.search,
                color: Color.fromARGB(255, 89, 90, 90),
              ),
              filled: true,
              fillColor: const Color(0xffF7F7F7),
              border: const OutlineInputBorder(
                borderSide: BorderSide.none,
                borderRadius: BorderRadius.all(Radius.circular(25)),
              ),
            ),
          ),
        ),
        Expanded(
            child: ListView.builder(
          itemCount: datas.length,
          itemBuilder: (_, index) {
            return _buildItem(datas[index]);
          },
        ))
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(L.blacklist.tr),
        centerTitle: true,
      ),
      body: Obx(()=> _buildBody()),
    );
  }
}
