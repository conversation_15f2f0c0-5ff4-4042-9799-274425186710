import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../core/languages/l.dart';
import '../../../data/services/channel_service.dart';
import '../../../widgets/mavatar_circle_avatar.dart';
import '../details/group_details_base_controller.dart';

class ApprovalInfo {
  ApprovalInfo({this.data, this.state = false});

  /// 申请人数据
  MemberInfo? data;

  /// 确认状态  true：已确认
  bool state;
}

class JoinApprovalPage extends StatefulWidget {
  const JoinApprovalPage({Key? key}) : super(key: key);

  @override
  State<JoinApprovalPage> createState() => _JoinApprovalPageState();
}

class _JoinApprovalPageState extends State<JoinApprovalPage> {
  final _controller = Get.find<GroupDetailsBaseController>();
  final _mapInfo = <String, ApprovalInfo>{};

  @override
  void initState() {
    super.initState();

    _controller.getApplyList((values) {
      if (values?.isNotEmpty == true) {
        for (var element in values!) {
          if (_mapInfo.containsKey(element.name)) {
            _mapInfo[element.name]!.data = element;
          } else {
            _mapInfo[element.name] = ApprovalInfo(data: element);
          }
        }
        setState(() {});
      }
    });
  }

  Widget _buildItem(ApprovalInfo info) {
    var text =
        L.join_channel_apply_text.trParams({'name': '${info.data?.nickname}'});

    String sure = info.state ? L.confirmed.tr : L.confirm.tr;

    return ListTile(
      onTap: () {
        if (info.data != null) {
          _controller.onGotoDetail(info.data!, _controller.groupId());
        }
      },
      leading: MAvatarCircle(
        diameter: 49,
        imagePath: info.data?.avatarPath,
        text: info.data?.nickname ?? info.data?.displayname,
      ),
      title: Text(
        text,
        overflow: TextOverflow.ellipsis,
      ),
      trailing: TextButton(
        style: ButtonStyle(
          //圆角
          shape: MaterialStateProperty.all(
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(13))),
          //边框
          side: MaterialStateProperty.all(
              const BorderSide(color: Color(0xffF2F2F2), width: 1)),
        ),
        child: Text(
          sure,
          style: TextStyle(
            fontSize: 13.sp,
            color: Colors.black,
          ),
        ),
        onPressed: () {
          if (info.state) return;

          _controller.onApproveJoin(info.data?.name).then((value) {
            if (value) {
              _mapInfo[info.data?.name]?.state = true;
              setState(() {});
            }
          });
        },
      ),
    );
  }

  Widget _buildBody() {
    var datas = _mapInfo.values.toList();
    return ListView.builder(
      itemCount: datas.length,
      itemBuilder: (_, index) {
        return _buildItem(datas[index]);
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(L.join_channel_apply.tr),
        centerTitle: true,
      ),
      body: _buildBody(),
    );
  }
}
