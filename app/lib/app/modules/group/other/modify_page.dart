import 'package:flutter/material.dart';
import 'package:flutter_metatel/core/languages/l.dart';
import 'package:flutter_metatel/core/utils/util.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../core/values/colors.dart';
import '../../../widgets/divider_cus.dart';

enum Options {
  title, // 标题
  describe, // 描述
  myGroupNickname, // 我的群昵称
  announcement,//公告
}

class ModifyPage extends StatefulWidget {
  const ModifyPage({Key? key, this.text, required this.op}) : super(key: key);
  final String? text;
  final Options op;

  @override
  State<ModifyPage> createState() => _ModifyPageState();
}

class _ModifyPageState extends State<ModifyPage> {
  final _textController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _textController.text = widget.text ?? '';
  }

  void _onPressed() {
    String text = _textController.text;
    if (text.trim().isEmpty && widget.op != Options.myGroupNickname) {
      String hintText = '';
      switch (widget.op) {
        case Options.title:
          hintText = L.chat_info_group_name.tr;
          break;
        case Options.describe:
          hintText = L.chat_info_group_describe.tr;
          break;
        default:
      }
      hintText = L.chat_info_not_blank_or_empty.trParams({'text': hintText});
      toast(hintText);
      return;
    }

    String? title;
    if (text != widget.text) {
      title = text;
    }
    Get.back(result: title);
  }

  @override
  Widget build(BuildContext context) {
    String appBarText = '';
    String hintText = '';
    String bottomHintText = '';
    int? maxLength;

    switch (widget.op) {
      case Options.title:
        appBarText = L.chat_info_group_chat_name.tr;
        hintText = L.chat_info_group_name.tr;
        bottomHintText = L
            .other_members_of_the_group_are_notified_when_the_group_name_is_changed
            .tr;
        maxLength = 30;
        break;
      case Options.describe:
        appBarText = L.chat_info_group_chat_describe.tr;
        hintText = L.chat_info_group_describe.tr;
        bottomHintText = L
            .other_members_of_the_group_are_notified_when_the_group_describe_is_changed
            .tr;
        maxLength = 240;
        break;
      case Options.myGroupNickname:
        appBarText = L.chat_info_my_group_nickname.tr;
        hintText = L.chat_info_my_group_nickname.tr;
        maxLength = 20;
        break;
      case Options.announcement:
        appBarText = L.chat_info_group_chat_announcement.tr;
        hintText = L.chat_info_group_announcement.tr;
        bottomHintText = L
            .other_members_of_the_group_are_notified_when_the_group_announcement_is_changed
            .tr;
        maxLength = 240;
        break;
      default:
    }

    return Scaffold(
      appBar: AppBar(
        title: Text(appBarText),
        centerTitle: true,
        actions: [
          TextButton(
            onPressed: _onPressed,
            child: Text(L.backup_confirm.tr),
          ),
        ],
      ),
      body: Container(
        color: Colors.white,
        child: Column(
          children: [
            const SizedBox(height: 20,),
            Flexible(
              child: TextField(
                controller: _textController,
                maxLength: maxLength,
                maxLines: null,
                keyboardType: TextInputType.multiline,
                style: TextStyle(
                    fontSize: 16.sp,
                    color: AppColors.colorFF333333,
                    height: 1.5),
                decoration: InputDecoration(
                  hintText: hintText,
                  contentPadding: EdgeInsets.only(left: 14.r, top:15.r,right: 15.r),
                  hintStyle: TextStyle(
                    fontSize: 13.sp,
                    color: AppColors.colorFF656565,
                  ),
                  focusedBorder: const UnderlineInputBorder(
                    borderSide: BorderSide(color: AppColors.colorFF338dcc),
                  ),
                  enabledBorder: const UnderlineInputBorder(
                      borderSide: BorderSide(color: AppColors.colorFFCBCBCB)),
                ),
              ),
            ),
            SizedBox(height: 20,),
            const DividerCus(),
            Container(
              padding: const EdgeInsets.only(left: 15, right: 15, top: 15,bottom: 15),
              alignment: Alignment.topLeft,
              child: Text(
                bottomHintText,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
