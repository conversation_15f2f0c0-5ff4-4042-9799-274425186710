import 'package:extended_text_field/extended_text_field.dart';
import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/widgets/at_widget/my_special_text_span_builder.dart';
import 'package:flutter_metatel/core/languages/l.dart';
import 'package:flutter_metatel/core/utils/util.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../core/values/colors.dart';
import '../../../data/models/annoucement_model.dart';
import '../../../widgets/divider_cus.dart';
import '../../../widgets/mavatar_circle_avatar.dart';
import 'announcement_detail_controller.dart';
import 'modify_page.dart';

class AnnouncementDetailPage extends StatefulWidget {
  const AnnouncementDetailPage(
      {Key? key, required this.mode, required this.op, required this.isAdmin})
      : super(key: key);
  final AnnouncementMode mode;
  final Options op;
  final bool isAdmin;

  @override
  State<AnnouncementDetailPage> createState() => _AnnouncementDetailPageState();
}

class _AnnouncementDetailPageState extends State<AnnouncementDetailPage> {
  int? maxLength;
  final _textController = TextEditingController();
  final controller = Get.put(AnnouncementDetailController());

  @override
  void initState() {
    super.initState();
    controller.setData(widget.mode);
    _textController.text = widget.mode.body ?? '';
  }

  void _onPressed() {
    String text = _textController.text;
    if (text.trim().isEmpty && widget.op != Options.myGroupNickname) {
      String hintText = '';
      switch (widget.op) {
        case Options.title:
          hintText = L.chat_info_group_name.tr;
          break;
        case Options.describe:
          hintText = L.chat_info_group_describe.tr;
          break;
        case Options.announcement:
          hintText = L.chat_info_group_announcement.tr;
          break;
        default:
      }
      hintText = L.chat_info_not_blank_or_empty.trParams({'text': hintText});
      toast(hintText);
      return;
    }

    String? title;
    if (text != widget.mode.body) {
      title = text;
    }
    Get.back(result: title);
  }

  @override
  Widget build(BuildContext context) {
    String appBarText = '';
    String hintText = '';
    switch (widget.op) {
      case Options.announcement:
        appBarText = L.chat_info_group_chat_announcement.tr;
        maxLength = 240;
        hintText = L.chat_info_group_announcement.tr;
        break;
      default:
    }

    return Scaffold(
      appBar: AppBar(title: Text(appBarText), centerTitle: true, actions: [
        if (widget.isAdmin)
          TextButton(
            onPressed: _onPressed,
            child: Text(L.backup_confirm.tr),
          ),
      ]),
      body: Container(
        color: Colors.white,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const DividerCus(),
            Visibility(
              visible: (widget.mode.body?.isNotEmpty ?? false),
              child: Container(
                padding: EdgeInsets.all(16.r),
                child: Obx(() {
                  var mode = controller.get().value;
                  return Row(
                    children: [
                      MAvatarCircle(
                        text: mode.displayName ?? '',
                        imagePath: controller.avatarPath.value,
                      ),
                      SizedBox(
                        width: 11.r,
                      ),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          SizedBox(
                            width: 0.75.sw,
                            child: Text(
                              controller.displayName.value,
                              maxLines: 1,
                              // overflow: TextOverflow.ellipsis,
                              style:
                                  TextStyle(fontSize: 16.sp, color: Colors.blue),
                            ),
                          ),
                          SizedBox(
                            height: 3.r,
                          ),
                          Text(msgTimeHHHH_MM_DD_hh_mm_Format(mode.time),
                              style: TextStyle(
                                  fontSize: 12.sp,
                                  color: AppColors.colorFF666666))
                        ],
                      ),
                    ],
                  );
                }),
              ),
            ),
            Visibility(
              visible: (widget.mode.body?.isNotEmpty ?? false),
              child: Container(
                padding: EdgeInsets.only(
                  left: 15.5.r,
                  right: 15.5.r,
                ),
                child: const DividerCus(),
              ),
            ),
            Visibility(
              visible: (widget.mode.body?.isNotEmpty ?? false),
              child: SizedBox(height: 10.r),
            ),
            Container(
              padding: EdgeInsets.only(left: 15.5.r, right: 15.5.r),
              child: widget.isAdmin
                  ? TextField(
                      autofocus: true,
                      controller: _textController,
                      minLines: 2,
                      maxLines: 9,
                      maxLength: maxLength,
                      style: TextStyle(
                          fontSize: 16.sp,
                          color: AppColors.colorFF333333,
                          height: 1.5),
                      decoration: InputDecoration(
                        hintText: hintText,
                        contentPadding: EdgeInsets.only(left: 14.r, top:15.r,right: 15.r,bottom: 5.r),
                        hintStyle: TextStyle(
                          fontSize: 13.sp,
                          color: AppColors.colorFF656565,
                        ),
                        focusedBorder: const UnderlineInputBorder(
                          borderSide: BorderSide(color: AppColors.colorFF338dcc),
                        ),
                        enabledBorder: const UnderlineInputBorder(
                            borderSide: BorderSide(color: AppColors.colorFFCBCBCB)),
            
                      ),
                    )
                  : Container(
                    width: 1.sw,
                    height: 0.5.sh,
                    child: SingleChildScrollView(
                      physics: const BouncingScrollPhysics(),
                      child: ExtendedSelectableText(
                          widget.mode.body ?? '',
                          style: TextStyle(
                              fontSize: 16.sp,
                              color: AppColors.colorFF333333,
                              height: 1.5),
                          specialTextSpanBuilder: MySpecialTextSpanBuilder(
                            showAtBackground: false,
                            showLinkColor: true,
                          ),
                        ),
                    ),
                  ),
            ),
            Visibility(
              visible: (widget.mode.body?.isNotEmpty ?? false),
              child: SizedBox(height: 10.r),
            ),
            Visibility(
              visible: (widget.mode.body?.isNotEmpty ?? false),
              child: Container(
                padding: EdgeInsets.only(
                  left: 15.5.r,
                  right: 15.5.r,
                ),
                child: const DividerCus(),
              ),
            ),
            const Spacer(
              flex: 5,
            ),
            Center(
              child: Text(
                  L.other_members_of_the_group_are_notified_when_the_group_announcement_is_changed
                      .tr,
                  textAlign: TextAlign.center,
                  style: TextStyle(
                      fontSize: 12.sp, color: AppColors.colorFF666666)),
            ),
            const Spacer(
              flex: 1,
            )
          ],
        ),
      ),
    );
  }
}
