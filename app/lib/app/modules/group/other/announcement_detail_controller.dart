//FileName AnnouncementDetailController
// <AUTHOR>
//@Date 2023/1/13 11:50

import 'package:flutter_metatel/app/data/services/config_service.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:get/get.dart';

import '../../../data/models/annoucement_model.dart';
import '../../../data/services/channel_service.dart';

class AnnouncementDetailController extends GetxController {
  Rx<AnnouncementMode> mode = AnnouncementMode().obs;

  Rx<AnnouncementMode> get() => mode;
  var displayName=''.obs;
  var avatarPath=''.obs;
  void setData(AnnouncementMode mode) {
    this.mode.value = mode;
    displayName.value=mode.displayName??'';
    if (mode.userName?.isNotEmpty ?? false) {
      Get.find<ChannelService>().getMemberInfo(
        mode.id!,
        [mode.userName!],
        callback: (value) {
          _updateOwnInfo(value);
        },
      );
    }
  }

  void _updateOwnInfo(List<MemberInfo>? memberInfos) {
    if (memberInfos == null) {
      return;
    }
    var user = Get.find<AppConfigService>().getUserName();
    for (var i = 0; i < memberInfos.length; i++) {
      var item = memberInfos[i];
      var username = item.name;
      if (username == mode.value.userName) {
        var isMine = user == username;
        displayName.value=isMine ? (item.displayname??'' ) : item.nickname ?? item.displayname??'';
        avatarPath.value = item.avatarPath ?? '';
      }
    }
  }
}
