/*
 * <AUTHOR> <PERSON><PERSON>
 * @Date         : 2022-04-21 10:03:44
 * @Description  : 启动页
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2022-04-29 17:55:12
 * @FilePath     : \flutter_metatel\lib\app\modules\splash\splash_page.dart
 */
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_metatel/app/modules/splash/splash_controller.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../core/languages/l.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/values/config.dart';
import '../../../r.dart';
import '../../data/services/share_sevice.dart';
import '../base/base_view.dart';

class SplashLanguagePage extends StatefulWidget {
  const SplashLanguagePage({Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _SplashState();
  }
}

class _SplashState extends State<SplashLanguagePage> {
  @override
  void initState() {
    super.initState();
    AppLogger.d('SplashLanguagePage...');
    WidgetsFlutterBinding.ensureInitialized().addPostFrameCallback((_) {
      Future.delayed((const Duration(milliseconds: 1000)), () {
        AppLogger.d('SplashLanguagePage222...');
        Get.back();
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return BaseView(
      isDark: false,
      WillPopScope(
        onWillPop: () async {
          return true;
        },
        child: Scaffold(
            extendBody: true,
            extendBodyBehindAppBar: true,
            body: ConstrainedBox(
                constraints: const BoxConstraints.expand(),
                child: Stack(
                  children: [
                    _createBgView(),
                    if (Config.isOversea)
                      Positioned(
                        left: 0,
                        right: 0,
                        bottom: 0,
                        child: Container(
                          color: Colors.black,
                          child: Image.asset(
                            R.bgSplashHwEarth,
                            fit: BoxFit.cover,
                          ),
                        ),
                      ),
                    Column(
                      children: [
                        const Spacer(
                          flex: Config.isOversea ? 7 : 13,
                        ),
                        Center(
                          child: Image.asset(
                            R.splashLogo,
                            height: 128.w,
                          ),
                        ),
                        Spacer(
                          flex: 1,
                        ),
                        Center(
                          heightFactor: 2,
                          child: Text(
                                  L.app_name.tr.toUpperCase(),
                                  style: TextStyle(
                                    fontSize: 24.sp,
                                    fontWeight: FontWeight.bold,
                                    color:
                                        Config.isOversea ? Colors.white : null,
                                  ),
                                ),
                        ),
                        const Spacer(
                          flex: 10,
                        ),
                      ],
                    )
                  ],
                ))),
      ),
    );
  }

  Widget _createBgView() {
    if (Config.isOversea) {
      return Positioned(
        left: 0,
        right: 0,
        top: 0,
        bottom: 0,
        child: Container(
          color: Colors.black,
          child: Image.asset(
            R.bgSplashHw,
            fit: BoxFit.cover,
          ),
        ),
      );
    } else {
      return Positioned(
          left: 0,
          right: 0,
          top: 0,
          bottom: 0,
          child: Image.asset(
            R.bgSplash,
            fit: BoxFit.fill,
          ));
    }
  }
}
