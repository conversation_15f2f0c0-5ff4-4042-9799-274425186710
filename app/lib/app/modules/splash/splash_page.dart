/*
 * <AUTHOR> <PERSON><PERSON>
 * @Date         : 2022-04-21 10:03:44
 * @Description  : 启动页
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2022-04-29 17:55:12
 * @FilePath     : \flutter_metatel\lib\app\modules\splash\splash_page.dart
 */
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_metatel/app/modules/splash/splash_controller.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../core/languages/l.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/values/colors.dart';
import '../../../core/values/config.dart';
import '../../../r.dart';
import '../../data/services/share_sevice.dart';
import '../base/base_view.dart';

class SplashPage extends StatefulWidget {
  const SplashPage({Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _SplashState();
  }
}

class _SplashState extends State<SplashPage> {
  @override
  void initState() {
    super.initState();
    Get.putAsync(() => ShareService().init());
  }

  @override
  Widget build(BuildContext context) {
    // ScreenUtil.init(context, designSize: const Size(375, 667));
    AppLogger.d('SplashController build ${Get.deviceLocale?.languageCode}');
    return BaseView(isDark:false, WillPopScope(
          onWillPop: () async {
            return false;
          },
          child: Scaffold(
            extendBody: true,
            extendBodyBehindAppBar: true,
            body: GetBuilder<SplashController>(builder: (controller) {
              return ConstrainedBox(
                  constraints: const BoxConstraints.expand(),
                  child: Stack(
                    children: [
                      // _createBgView(),
                      if (Config.isOversea)
                        Positioned(
                          left: 0,
                          right: 0,
                          top: 0,
                          bottom: 0,
                          child: Container(
                            decoration: const BoxDecoration(
                              boxShadow: [
                                BoxShadow(
                                    color: Color(0xff00abff), blurRadius: 10.0, spreadRadius: 10.0),
                              ],
                          gradient: LinearGradient(
                              colors: [Color(0xffB33D84), Color(0xff02A3F3)],
                                begin: Alignment.topLeft,
                                end: Alignment.topRight,
                              ),
                            ),
                            // color: Colors.black,
                          ),
                        ),
                      Column(
                        children: [
                          const Spacer(
                            flex: 7,
                          ),
                          Center(
                            child: Image.asset(
                              R.splashLogo,
                              height: 128.w,
                            ),
                          ),
                          Spacer(
                            flex: 1,
                          ),
                          const Spacer(
                            flex: 10,
                          ),
                          if(Config.isOversea)
                            Center(child: Text(L.splash_bottom.tr,style: TextStyle(fontSize: 13.sp,color: AppColors.colorFFB2B2B2),),),
                          if(Config.isOversea)
                            const Spacer(
                              flex: 2,
                            )
                        ],
                      )
                    ],
                  ));
            }),
          ),
        ));
  }

  Widget _createBgView() {
    if (Config.isOversea) {
      return Positioned(
        left: 0,
        right: 0,
        top: 0,
        bottom: 0,
        child: Container(
          color: Colors.black,
          child: Image.asset(
            R.bgSplashHw,
            fit: BoxFit.cover,
          ),
        ),
      );
    } else {
      return Positioned(
          left: 0,
          right: 0,
          top: 0,
          bottom: 0,
          child: Image.asset(
            R.bgSplash,
            fit: BoxFit.fill,
          ));
    }
  }
}
