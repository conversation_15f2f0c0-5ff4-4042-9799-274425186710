import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/modules/home/<USER>/appbar.dart';
import 'package:flutter_metatel/app/modules/home/<USER>/home_appbar_base.dart';
import 'package:flutter_metatel/core/languages/l.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';

import '../../../core/utils/util.dart';
import '../../../core/values/colors.dart';
import '../../data/enums/enum.dart';
import '../../data/models/channel_info_model_data.dart';
import 'banner/square_banner_view.dart';
import 'square_controller.dart';
import 'square_item.dart';

class SquareView extends StatefulWidget {
  const SquareView({Key? key}) : super(key: key);

  @override
  State<SquareView> createState() {
    return _SquareViewState();
  }
}

class _SquareViewState extends State<SquareView>{
  final SquareController controller = Get.put(SquareController(), permanent: true);
  final _pageController = PageController(viewportFraction: 1.1);

  @override
  Widget build(BuildContext context) {
    return createBody();
  }

  Widget createBody() {
    return createAppBar(
      title: L.channel.tr,
      type: SearchType.square,
      body: _buildBody(),
    );
  }

  Widget _buildBody(){
    return Scaffold(
      body: SafeArea(
        child: RefreshIndicator(
          displacement: 5,
          onRefresh: () async {
            await controller.refreshData();
          },
          notificationPredicate: (_) {
            return true;
          },
          child: Container(
            color: AppColors.white,
            child: Obx(
              () {
                if (controller.isLoading()) {
                  return loadingView();
                } else if (controller.isLoadError()) {
                  return loadErrorView(reloading:controller.reloading);
                }
                if(controller.isLoadSuccess()){
                  return loadSuccessView();
                }
                return SizedBox.shrink();
              },
            ),
          ),
        ),
      ),
    );
  }

  Widget loadSuccessView() {
    return NestedScrollView(
      headerSliverBuilder: (context, innerBoxIsScrolled) {
        return [
          _buildSliverBanner(),
          _buildSliverHorizontalScrollView(),
          _buildSliverHeader(),
        ];
      },
      body: _buildSliverBody(),
    );
  }

  Widget _buildSliverBanner() {
    return SliverToBoxAdapter(
      child: SquareBannerWidget(key: Key(uuid())),
    );
  }

  Widget _buildSliverHorizontalScrollView() {
    return SliverToBoxAdapter(
        child: controller.slicedChannelRecommeds.isEmpty 
          ?SizedBox.shrink()
          :SizedBox(
            height: 165.r,
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 8.r),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(height: 15.r),
                  // 标题
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 8.r),
                    child: Text(
                      L.channel_recommed.tr,
                      style: TextStyle(
                        fontSize: 15.sp,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                  SizedBox(height: 15.r),
                  // 横向滚动列表
                  SizedBox(
                    height: 90.r,
                    child: PageView(
                      controller: _pageController,
                      physics: BouncingScrollPhysics(),
                      children: [
                        for(var page in controller.slicedChannelRecommeds)
                        FractionallySizedBox(
                          widthFactor: 1/_pageController.viewportFraction,
                          child: Row(                        
                            mainAxisAlignment: MainAxisAlignment.spaceAround,
                            children: [
                              for(var item in page)
                              _buildRecommendItem(item),
                              if(_addSizedBoxIfNeeded(page.length) != null)
                              ..._addSizedBoxIfNeeded(page.length)! // 添加空的占位，当一页不足5个时
                            ],
                          ),
                        ),                    
                      ],
                    ),
                  ),
                  // Paginator
                  Center(
                    child: SmoothPageIndicator(
                      controller: _pageController, 
                      count: controller.slicedChannelRecommeds.length,
                      effect: ExpandingDotsEffect(
                        dotWidth: 6.r,
                        dotHeight: 6.r,
                        expansionFactor: 2,
                        spacing: 4.r,
                        activeDotColor: AppColors.colorFF249ED9,
                        dotColor: AppColors.colorFF249ED9.withOpacity(0.2),
                      ),
                    ),
                  ),
                  SizedBox(height: 15.r),
                ],
              ),
            ),
          ),
      );
  }

  Widget _buildSliverHeader() {
    return SliverPersistentHeader(
      pinned: true,
      delegate: _sliverHeaderDelegate(
        minHeight: 45.r, 
        maxHeight: 45.r, 
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            TabBar(
              controller: controller.tabController, 
              isScrollable: true,
              tabAlignment: TabAlignment.start,
              indicatorColor: AppColors.colorFF249ED9,  
              indicatorPadding: EdgeInsets.symmetric(horizontal: 8.r),  
              labelStyle: TextStyle(
                fontSize: 13.sp,
                fontWeight: FontWeight.w800,
                color: AppColors.colorFF249ED9,
              ),
              unselectedLabelStyle: TextStyle(
                fontSize: 13.sp,
                color: Colors.black,
                fontWeight: FontWeight.w400,
              ),
              padding: EdgeInsets.symmetric(horizontal: 16.r),
              labelPadding: EdgeInsets.symmetric(vertical: 10.r, horizontal: 16.r),
              tabs: [
                for(var item in controller.categorizedDao.keys)
                ConstrainedBox(
                  constraints: BoxConstraints(minWidth: 40.r),
                  child: Center(
                    child: Text(
                      item.tr,
                      style: TextStyle(fontSize: 14.sp),
                    ),
                  ),
                ),
              ],
            ),
            Container(
              width: double.infinity,
              height: 1.r,
              color: AppColors.colorFFd9d9d9.withOpacity(0.5),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSliverBody() {
    return TabBarView(
      controller: controller.tabController, 
      physics: BouncingScrollPhysics(),
      children: [
        for(var item in controller.categorizedDao.values)
        ListView.builder(
          padding: EdgeInsets.only(bottom: 5.r),
          physics: BouncingScrollPhysics(),
          shrinkWrap: true,
          itemCount: item.length,
          itemBuilder: (context, index) {
            var sessionData = item[index];
            return GetBuilder<SquareController>(
              id: sessionData.id,
              builder: (control) {
                return SquareItemView(
                  data: sessionData,
                  onPressed: controller.itemClick,
                );
              },
            );
          },
        ),
      ],
    );
  }

  Widget _buildRecommendItem(ChannelInfoModelData data) {
    return GestureDetector(
      onTap: (){
        controller.itemClick(data);
      },
      child: Column(
        children: [
          buildChatAvatarWithAttr(
            ChatType.channelChat.index,
            data.id ?? "",
            diameter: 46,
            text: data.title,
            imagePath: data.avatar,
            isNet: true,
            channelAttribute: data.attribute ?? 0,
          ),
          SizedBox(height: 7.h),
          SizedBox(
            width: 45.r,
            child: Text(
              data.title ?? '',
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 9.sp,
              ),
            ),
          ),
        ],
      ),
    );
  }

  List<Widget>? _addSizedBoxIfNeeded(int length) {
    bool needToAdd = length % 5 != 0;
    if(needToAdd){
      int numToAdd = 5 - (length % 5);
      List<Widget> _list = [];
      for(var i=0; i<numToAdd; i++){
        _list.add(SizedBox(width: 45.r));
      }
      return _list;
    }
    return null;
  }

}

class _sliverHeaderDelegate extends SliverPersistentHeaderDelegate {
  _sliverHeaderDelegate({required this.minHeight, required this.maxHeight, required this.child});
  final double minHeight;
  final double maxHeight;
  final Widget child;

  @override
  Widget build(BuildContext context, double shrinkOffset, bool overlapsContent) {
    return SizedBox.expand(
      child: Container(
        color: AppColors.white,
        child: child,
      ),
    );
  }

  @override
  double get maxExtent => max(maxHeight, minHeight);

  @override
  double get minExtent => minHeight;

  @override
  bool shouldRebuild(_sliverHeaderDelegate oldDelegate) {
    return oldDelegate.maxHeight != maxHeight || oldDelegate.minHeight != minHeight || oldDelegate.child != child;
  }
}