//FileName dao_config
// <AUTHOR>
//@Date 2022/11/16 15:15

import 'package:flutter_metatel/app/data/services/config_service.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:get/get.dart';

import 'bean/square_banner_bean.dart';

class DaoConfig{
  static String? daoBaseUrl;
  static String? daoAndroidUrl;
  static String? daoIosUrl;
  static List<SquareBannerBean>?bannerList;

  static String getDaoBaseUrl(){
    if(daoBaseUrl==null){
      var nodeConf = Get.find<AppConfigService>().getNodeConfFromLocal();
      daoBaseUrl = nodeConf?.data?.daoConf?.daoBaseUrl;
    }
   return daoBaseUrl??"http://frp.metatel.com.cn:18099";
    // return 'http://192.168.138.110:8083';

  }
  static String getDaoAndroidUrl(){
    if(daoAndroidUrl==null){
      var nodeConf = Get.find<AppConfigService>().getNodeConfFromLocal();
      daoAndroidUrl = nodeConf?.data?.daoConf?.daoAndroidUrl;
    }
    return daoAndroidUrl??'http://frp.metatel.com.cn:18121/?node=';
  }
  static String getDaoIosUrl(){
    if(daoIosUrl==null){
      var nodeConf = Get.find<AppConfigService>().getNodeConfFromLocal();
      daoIosUrl = nodeConf?.data?.daoConf?.daoIosUrl;
    }
    return daoIosUrl??'http://frp.metatel.com.cn:18120/?node=';
  }

  static List<SquareBannerBean>? getDaoBanner() {
    if (bannerList == null) {
      bannerList =[];
      bannerList?.add(SquareBannerBean());
    }
    AppLogger.d('getDaoBanner ${bannerList.toString()}');
    return bannerList;
  }
}