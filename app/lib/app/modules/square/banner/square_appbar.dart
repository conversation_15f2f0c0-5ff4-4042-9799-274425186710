import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/modules/dao/dao_config.dart';
import 'package:flutter_metatel/app/widgets/popupmutu_item.dart' as p;
import 'package:flutter_metatel/core/languages/l.dart';
import 'package:flutter_metatel/core/values/colors.dart';
import 'package:flutter_metatel/r.dart';
import 'package:flutter_metatel/routes/pages.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';


/// 构建弹出菜单Item
p.KYPopupMenuItem _buildPopupMenuItem(
  String title,
  String imageName,
  int position,
) {
  return p.KYPopupMenuItem(
    value: position,
    height: 40.h,
    child: Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Image.asset(imageName, width: 20.w, height: 20.h),
        SizedBox(width: 8.w),
        Padding(
          padding: const EdgeInsets.only(right: 20).r,
          child: Text(
            title,
            style: TextStyle(fontSize: 14.sp, color: AppColors.colorFF333333),
          ),
        ),
      ],
    ),
  );
}

Widget buildMenu() {
  return p.KYPopupMenuButton(
    icon: Image.asset(
      R.icoDaoAdd,
      width: 19.r,
      height: 19.r,
    ),
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(12),
    ),
    // 在按钮下方显示
    offset: const Offset(0, 50),
    itemBuilder: (context) {
      return <p.KYPopupMenuItem>[
        _buildPopupMenuItem(L.dao_create_personal.tr, R.icoDaoPersonalMenu,
            DaoJoinWebViewType.personDao),
        _buildPopupMenuItem(L.dao_create_company.tr, R.icoDaoCompanyMenu,
            DaoJoinWebViewType.enterpriseDao),
      ];
    },
    onSelected: (value) {

    },
  );
}
Widget buildSearchIcon() {
  return IconButton(
    onPressed: () {
      Get.toNamed(Routes.SearchSquare);
    },
    icon: Image.asset(
      R.icoSearch,
      width: 19.r,
      height: 19.r,
    ),
  );
}
Widget buildSearch({
  required double height,
  String? hintText,
  Color? fillColor,
}) {
  return SizedBox(
    height: height,
    child: TextField(
      onTap: () {
        Get.toNamed(Routes.SearchDao);
      },
      readOnly: true,
      textAlign: TextAlign.start,
      textAlignVertical: TextAlignVertical.center,
      decoration: InputDecoration(
        hintText: hintText,
        hintStyle: const TextStyle(color: AppColors.colorFF808080),
        // 设置后，提升文本居中
        contentPadding: EdgeInsets.zero,
        prefixIcon: const Icon(
          Icons.search,
          color: Color.fromARGB(255, 89, 90, 90),
        ),
        filled: true,
        fillColor: fillColor,
        border: const OutlineInputBorder(
          borderSide: BorderSide.none,
          borderRadius: BorderRadius.all(Radius.circular(25)),
        ),
      ),
    ),
  );
}

AppBar getAppBar() {
  return AppBar(
    title: Text(
      'DAO',
      style: TextStyle(fontSize: 18.sp),
    ),
    centerTitle: true,
    backgroundColor: AppColors.backgroundGray,
    actions: [
      Padding(
        padding:  EdgeInsets.only(right: 0.r),
        child: buildSearchIcon(),
      ),

    ],
  );
}
