//FileName banner_controller
// <AUTHOR>
//@Date 2022/10/26 11:39
import 'package:flutter/material.dart';
import 'package:flutter_metatel/r.dart';
import 'package:get/get.dart';

import '../../../../core/utils/app_log.dart';
import '../../../../core/values/config.dart';
import '../../../data/providers/api/channel.dart';
import '../bean/square_banner_bean.dart';

class SquareBannerController extends GetxController {
  late ScrollController scrollController;
  final RxList<SquareBannerBean> bannerList = <SquareBannerBean>[].obs;

  @override
  void onInit() {
    super.onInit();
    scrollController = ScrollController();
  }

  void loadBanner() async {
    if(!Config.isBrowser){//ios上架屏蔽
      return;
    }
    var res = await getBannerList();
    if (res.statusCode == 200) {
      var body = res.data;
      if (body != null && body.code == 200) {
        bannerList.clear();
        if (body.data?.isNotEmpty ?? false) {
          bannerList.addAll(body.data!);
        }
      }
    }

    // if (bannerList.isEmpty) {
    //   bannerList.add(SquareBannerBean(
    //     type: 2,
    //     image: R.icChannelBanner1,
    //     title: '3Q量子钱包中文社区',
    //     id: '6587e3dd09d386e7aea3b73d',
    //     content: '2',
    //   ));
    //   bannerList.add(SquareBannerBean(
    //     type: 2,
    //     image: R.icChannelBanner2,
    //     title: '3Q量子钱包中文社区',
    //     id: '6587e3dd09d386e7aea3b73d',
    //     content: '2',
    //   ));
    //   bannerList
    //       .add(SquareBannerBean(type: 1, image: R.icChannelBanner3, id: ''));
    //   bannerList
    //       .add(SquareBannerBean(type: 1, image: R.icChannelBanner4, id: ''));
    // }
    // }
  }

  @override
  void dispose() {
    scrollController.dispose();
    super.dispose();
  }
}
