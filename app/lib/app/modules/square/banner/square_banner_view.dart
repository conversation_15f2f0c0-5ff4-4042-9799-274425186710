//FileName banner_view
// <AUTHOR>
//@Date 2022/10/25 16:00
import 'dart:async';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:extended_image/extended_image.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/data/models/user_message_model.dart';
import 'package:flutter_metatel/app/modules/square/square_controller.dart';
import 'package:flutter_metatel/core/languages/l.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:flutter_metatel/core/utils/util.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_web3/app/core/utils/util.dart' as web3_util;
import 'package:flutter_web3/app/modules/home/<USER>';
import 'package:get/get.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';

import '../../../../core/utils/jump.dart';
import '../../../../core/values/colors.dart';
import '../../../../core/values/config.dart';
import '../../../../routes/pages.dart';
import '../../../data/enums/enum.dart';
import '../../../widgets/webview_page.dart';
import 'square_banner_controller.dart';

class SquareBannerWidget extends StatefulWidget {
  SquareBannerWidget({super.key});

  @override
  State<SquareBannerWidget> createState() {
    return _BannerState();
  }
}

class _BannerState extends State<SquareBannerWidget> {
  final SquareBannerController controller =
      Get.put(SquareBannerController(), permanent: true);

  @override
  void initState() {
    super.initState();
    controller.loadBanner();
  }

  @override
  void didUpdateWidget(covariant SquareBannerWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Obx(() {
          var list = controller.bannerList;
          return Container(
            height: list.isNotEmpty ? 150.r : 0,
            margin: EdgeInsets.only(top: 10.r),
            padding: EdgeInsets.only(left: 16.r, right: 16.r),
            child: ListView.builder(
              itemCount: list.length,
              itemBuilder: (context, i) {
                if (i == 0) {
                  return SizedBox(
                    key: Key(uuid()),
                    child: Container(
                      height: 140.r,
                      clipBehavior: Clip.hardEdge,
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(5.r)),
                      child: _BannerView(
                        switchDuration: const Duration(seconds: 3),
                        children:  createBannerView()),

                    ),
                  );
                } else {
                  return Container();
                }
              },
            ),
          );
        }),
        // UnconstrainedBox(
        //   child: Container(
        //     margin: EdgeInsets.only(left: 16.r),
        //     height: 50.r,
        //     alignment: Alignment.centerLeft,
        //     decoration: BoxDecoration(
        //         border: Border(
        //             bottom: BorderSide(
        //                 width: 1.r, color: AppColors.colorFF374FD0))),
        //     child: Text(
        //       L.popular.tr,
        //       style: TextStyle(
        //         fontSize: 16.sp,
        //         color: AppColors.colorFF374FD0,
        //         fontWeight: FontWeight.bold,
        //       ),
        //     ),
        //   ),
        // ),
        // Container(
        //   width: double.infinity,
        //   height: 1.r,
        //   color: AppColors.colorFFd9d9d9,
        // ),
        // SizedBox(
        //   height: 10.r,
        // )
      ],
    );
  }

  List<Widget> createBannerView() {
    var banners = controller.bannerList;
    AppLogger.d('loadBanner list.length2 = ${banners.length}');
    var views = <Widget>[];
    if (banners.isNotEmpty) {
      for (var b in banners) {
        bool isNet = false;
        if (b.image != null) {
          isNet = b.image!.startsWith('http:') || b.image!.startsWith('https:');
        }
        views.add(GestureDetector(
            onTap: () {
              if(b.content!=null && b.content!.isNotEmpty && b.content == 'ads') {
                _loadInterstitialAd();  
                return;
              }
              switch (b.type) {
                case 0:
                  if (b.content != null &&
                      (b.content!.startsWith('http://') ||
                          b.content!.startsWith('https://'))) {
                    AppLogger.d('banner click ${b.toJson()}');
                    web3_util.jumpToBrowserMethod(b.content!,);
                  }
                  break;
                case 1:
                  String id = b.id ?? '';
                  if (id.isNotEmpty) {
                    Get.toNamed(Routes.ChannelJoin,
                        arguments: {"channelId": id});
                  }
                  break;
                case 2:
                  String id = b.id ?? '';
                  if (id.isNotEmpty) {
                    var userMessage = UserMessage(
                        chatType: ChatType.singleChat.index,
                        displayName: b.title,
                        userName: id,
                        avatarPath: '');
                    JumpPage.messgae(userMessage);
                  }
                  break;
              }
            },
            child: Container(
              height: 140.r,
              clipBehavior: Clip.hardEdge,
              decoration:
                  BoxDecoration(borderRadius: BorderRadius.circular(5.r)),
              child: isNet
                  ? CachedNetworkImage(
                      imageUrl: b.image ?? '', fit: BoxFit.cover)
                  : ExtendedImage.asset(b.image ?? '', fit: BoxFit.cover),
            )));
      }
    }
    AppLogger.d('loadBanner views.length = ${views.length}');

    return views;
  }

  // 加载插页式广告
  void _loadInterstitialAd() {
    Get.find<SquareController>().isLoadingAds.value = true;
    InterstitialAd.load(
      adUnitId:kReleaseMode 
        ? Config.interstitialAdUnitId
        : Config.interstitialAdTestUnitId,
      request: const AdRequest(),
      adLoadCallback: InterstitialAdLoadCallback(
        onAdLoaded: (InterstitialAd ad) {
          // Called when an ad is successfully received.
          Get.find<SquareController>().isLoadingAds.value = false;
          
          ad.fullScreenContentCallback = FullScreenContentCallback(
            onAdShowedFullScreenContent: (ad) {
              // Called when the ad showed the full screen content.
            },
            onAdFailedToShowFullScreenContent: (ad, err) {
              // Called when the ad failed to show full screen content.
              // Dispose the ad here to free resources.
              ad.dispose();
            },
            onAdDismissedFullScreenContent: (ad) {
              // Called when the ad dismissed full screen content.
              // Dispose the ad here to free resources.
              ad.dispose();
            },
            onAdImpression: (ad) {
              // Called when an impression occurs on the ad.
            },
            onAdClicked: (ad) {
              // Called when a click is recorded for an ad.
            },
          );
          ad.show();
        },
        onAdFailedToLoad: (LoadAdError error) {
          // Called when an ad request failed.
          Get.find<SquareController>().isLoadingAds.value = false;
          toast(L.ads_load_failed.tr);
        },
      ),
    );
  }
}

class _BannerView extends StatefulWidget {
  final List<Widget> children;

  final Duration switchDuration;

  const _BannerView({
    this.children = const <Widget>[],
    this.switchDuration = const Duration(seconds: 3),
  });

  @override
  State<_BannerView> createState() {
    return _BannerViewState();
  }
}

class _BannerViewState extends State<_BannerView>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  late PageController _pageController;
  Timer? _timer;
  int _curPageIndex = 0;

  List<Widget> children = [];

  @override
  void initState() {
    super.initState();
    _curPageIndex = 0;

    _tabController = TabController(length: widget.children.length, vsync: this);
    AppLogger.d('widget.children.length==${widget.children.length}');

    /// 添加所有的widget到新建的list中
    children.addAll(widget.children);

    /// 定时器完成自动翻页-只有在大于1时才会有翻页
    if (children.length > 1) {
      children.insert(0, widget.children.last);
      children.add(widget.children.first);

      ///如果大于一页，则会在前后都加一页， 初始页要是 1
      _curPageIndex = 1;
      _timer = Timer.periodic(widget.switchDuration, _nextBanner);
    }

    ///初始页面 指定
    _pageController = PageController(initialPage: _curPageIndex);
  }

  /// 进行翻页的动画
  _nextBanner(Timer timer) {
    _curPageIndex++;
    _curPageIndex = _curPageIndex == children.length ? 0 : _curPageIndex;

    //curve:和android一样 动画插值
    _pageController.animateToPage(_curPageIndex,
        duration: const Duration(milliseconds: 500), curve: Curves.linear);
  }

  @override
  void dispose() {
    /// 页面销毁时进行回收
    _pageController.dispose();
    _tabController.dispose();
    _timer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Listener(

            /// 点击时取消timer效果
            onPointerDown: (_) => _timer?.cancel(),
            onPointerUp: (_) {
              ///重新开启timer
              if (children.length > 1) {
                _timer = Timer.periodic(widget.switchDuration, _nextBanner);
              }
            },
            child: NotificationListener(
              onNotification: (notification) {
                if (notification is ScrollUpdateNotification) {
                  ScrollUpdateNotification n = notification;

                  /// 判断是否是一次完整的翻页
                  if (n.metrics.atEdge) {
                    if (_curPageIndex == children.length - 1) {
                      /// 如果是最后一页，那么就跳到第一页
                      _pageController.jumpToPage(1);
                    } else if (_curPageIndex == 0) {
                      /// 如果是第一页，再往前滑动，因为原来的list前后都加了一条数据，所以 -2
                      _pageController.jumpToPage(children.length - 2);
                    }
                  }
                }
                return true;
              },
              child: PageView.builder(
                itemCount: children.length,
                itemBuilder: (context, index) {
                  /// banner设置点击监听
                  return InkWell(
                    child: children[index],
                    onTap: () {
                      if (kDebugMode) {
                        print("点击Item");
                      }
                    },
                  );
                },
                onPageChanged: (index) {
                  _curPageIndex = index;
                  if (index == children.length - 1) {
                    /// 如果是最后一页，那么下面的指示器设置为0的位置
                    _tabController.animateTo(0);
                  } else if (index == 0) {
                    ///如果是第一页再往左滑，那么久设置为指示器最后的位置
                    _tabController.animateTo(_tabController.length - 1);
                  } else {
                    _tabController.animateTo(index - 1);
                  }
                },
                controller: _pageController,
              ),
            )),
        Positioned.fill(
          bottom: 8.r,
          child: Align(
            alignment: Alignment.bottomCenter,
            child: SmoothPageIndicator(
              controller: _pageController, 
              count: _tabController.length,
              effect: ExpandingDotsEffect(
                dotWidth: 6.r,
                dotHeight: 6.r,
                expansionFactor: 2,
                spacing: 4.r,
                activeDotColor: AppColors.colorFF249ED9,
                dotColor: AppColors.white,
              ),
            ),
          ),
        ),
        // Visibility(
        //     visible: children.length > 1,
        //     child: Positioned(
        //       bottom: 8.0,
        //       right: 8.0,
        //       child: TabPageSelector(
        //         controller: _tabController,
        //         indicatorSize: 9,
        //         color: Colors.white,
        //         selectedColor: Colors.blue,
        //       ),
        //     )),
      ],
    );
  }
}
