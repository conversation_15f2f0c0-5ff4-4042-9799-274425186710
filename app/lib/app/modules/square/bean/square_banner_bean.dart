//FileName market_bean
// <AUTHOR>
//@Date 2022/10/26 12:04
class SquareBannerReq{
  int? code;
  String? message;
  List<SquareBannerBean>? data;

  SquareBannerReq({this.code, this.message, this.data});

  SquareBannerReq.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    message = json['message'];
    if (json['data'] != null) {
      data = <SquareBannerBean>[];
      json['data'].forEach((v) {
        data?.add(SquareBannerBean.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['code'] = code;
    map['message'] = message;
    map['data'] = data?.map((v) => v.toJson()).toList();
    return map;
  }
}

class SquareBannerBean {
  SquareBannerBean({this.type, this.content,this.image, this.id, this.title});
  int? type;
  String? content;
  String? image;
  String? id;
  String? title;
  SquareBannerBean.fromJson(Map<String, dynamic> json) {
    type = json['type'];
    content = json['content'];
    image = json['image'];
    id = json['id'];
    title = json['title'];
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['type'] = type;
    data['content'] = content;
    data['image'] = image;
    data['id'] = id;
    data['title'] = title;
    return data;
  }
}
