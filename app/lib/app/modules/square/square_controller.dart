import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_metatel/app/data/models/channel_info_model_data.dart';
import 'package:flutter_metatel/app/data/providers/api/channel.dart';
import 'package:flutter_metatel/app/data/services/network_connect_service.dart';
import 'package:flutter_metatel/routes/pages.dart';
import 'package:get/get.dart';
import '../../../core/languages/l.dart';
import '../../../core/values/config.dart';

class SquareController extends GetxController with GetTickerProviderStateMixin{
  List<ChannelInfoModelData> allDaoObs = [];
  RxMap<String, List<ChannelInfoModelData>> categorizedDao = <String, List<ChannelInfoModelData>>{}.obs; // 除推荐外的其他列表
  RxList<List<ChannelInfoModelData>> slicedChannelRecommeds = RxList<List<ChannelInfoModelData>>();  // 推荐列表, 每5个，做分割
  RxInt loadType = LoadType.defaultState.obs;  
  RxInt _currentTabIndex = 0.obs;
  TabController? tabController;
  List<String> _channelCategories = [
    ChannelCategory.CHANNEL_RECOMMED, // 单独一个列表
    ChannelCategory.CHANNEL_HOT,
    ChannelCategory.CHANNEL_ECO,
    ChannelCategory.CHANNEL_TECH,
    ChannelCategory.CHANNEL_FUND,
    ChannelCategory.CHANNEL_OTHER,
  ];
  Rx<bool> isLoadingAds = false.obs;

  @override
  void onInit() {
    super.onInit();
    _onLoadMore(first: true);
    isLoadingAds.listen((value) {
      if (value) {
        EasyLoading.show(
          status: L.loading_ads.tr,
          maskType: EasyLoadingMaskType.black,
        );
      } else {
        EasyLoading.dismiss();
      }
    });
  }

  @override
  void dispose() {
    tabController?.dispose();
    isLoadingAds.close();
    super.dispose();
  }

  _onLoadMore({bool first = false}) async {
    var net = await Get.find<NetWorkConnectService>().networkConnected();
    if (!net) {
      loadType.value = LoadType.error;
    }
    var res = await getSquareChannelsRequest(appCategory:'linksay');
    if (first) {
      allDaoObs.clear();
    }
    if (res?.isNotEmpty ?? false) {
      allDaoObs.addAll(res!);
      allDaoObs.sort((a, b) => a.on.compareTo(b.on));
      categorizedDao.value = _channelCategorize(allDaoObs); // 分类
      _initTabController();
      loadType.value = LoadType.success;
    } else {
      if (allDaoObs.isEmpty) {
        loadType.value = LoadType.error;
      }
    }
  }

  Future<void> refreshData() async {
     // loadType.value = LoadType.loading;
    await _onLoadMore(first: true);
  }

  reloading() {
    refreshData();
  }

  void itemClick(ChannelInfoModelData data) {
    Get.toNamed(Routes.ChannelJoin, arguments: {"channelId": data.id});
  }

  void _initTabController() {
    tabController?.dispose();  
    tabController = null; 
    tabController = TabController(initialIndex: _currentTabIndex.value ,length: categorizedDao.keys.length, vsync: this);  
    tabController?.addListener(() { 
      _currentTabIndex.value = tabController?.index ?? 0;
    });
  }

  Map<String, List<ChannelInfoModelData>> _channelCategorize(List<ChannelInfoModelData> data) {
    var categorized = data.fold<Map<String, List<ChannelInfoModelData>>>({}, (Map<String, List<ChannelInfoModelData>> previousValue, element) {
      // type为空 或 不存在于默认的分类列表内的 都为“其他”
      String type = (element.type?.isEmpty ?? true) || (!_channelCategories.contains(element.type)) ? "channel_other" : element.type!;
      previousValue.putIfAbsent(type, () => []).add(element);
      return previousValue;
    });
    // 另外存入"channelRecommed"分类, 并每5个做分割    
    var channelRecommeds = categorized[ChannelCategory.CHANNEL_RECOMMED]??[];
    slicedChannelRecommeds.clear();
    if(channelRecommeds.length>5){
      channelRecommeds.slices(5).forEach((element) {
        slicedChannelRecommeds.add(element);
      });  // 每5个，做分割
    } else{
      slicedChannelRecommeds.add(channelRecommeds);
    }
    // 总列表中移除"channelRecommed"分类  
    var filtered = Map<String, List<ChannelInfoModelData>>.from(categorized)..removeWhere((key, value) => key == ChannelCategory.CHANNEL_RECOMMED);
    return filtered;
  }

  bool isLoading() {
    return loadType.value == LoadType.loading;
  }

  bool isLoadError() {
    return loadType.value == LoadType.error;
  }

  bool isLoadSuccess() {
    return loadType.value == LoadType.success;
  }

}
