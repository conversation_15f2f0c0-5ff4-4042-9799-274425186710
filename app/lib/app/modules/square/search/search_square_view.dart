import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/data/models/channel_info_model_data.dart';
import 'package:flutter_metatel/app/widgets/divider_cus.dart';
import 'package:flutter_metatel/routes/pages.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../core/languages/l.dart';
import '../../../../core/values/colors.dart';
import '../square_item.dart';
import 'search_square_controller.dart';

class SearchSquareView extends GetView<SearchSquareController> {
  final FocusNode focusNode = FocusNode();

  SearchSquareView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      resizeToAvoidBottomInset: false,
      backgroundColor: AppColors.white,
      body: GetBuilder<SearchSquareController>(builder: (controller) {
        return Padding(
          padding: const EdgeInsets.only(top: kToolbarHeight),
          child: Column(
            children: [
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Expanded(
                    child: Container(
                      height: 36.r,
                      padding: const EdgeInsets.only(
                        left: 15,
                      ).r,
                      child: TextField(
                        autofocus: true,
                        focusNode: focusNode,
                        onChanged: (text) => _onTextChange(text, context),
                        textAlign: TextAlign.start,
                        style: TextStyle(fontSize: 16.sp),
                        textAlignVertical: TextAlignVertical.center,
                        decoration: InputDecoration(
                          hintText: L.searbar_hint_search.tr,
                          hintStyle: TextStyle(color: AppColors.colorFF7F7F7F,fontSize: 12.sp,fontWeight: FontWeight.w400),
                          // 设置后，提升文本居中
                          contentPadding: EdgeInsets.zero,
                          prefixIcon: const Icon(
                            Icons.search,
                            color: Color.fromARGB(255, 89, 90, 90),
                          ),
                          filled: true,
                          fillColor: const Color(0xffF7F7F7),
                          border: const OutlineInputBorder(
                            borderSide: BorderSide.none,
                            borderRadius: BorderRadius.all(Radius.circular(25)),
                          ),
                        ),
                      ),
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.only(),
                    child: TextButton(
                      onPressed: () {
                        FocusManager.instance.primaryFocus?.unfocus();
                        Future.delayed(const Duration(milliseconds: 100), () {
                          Get.back();
                        });
                      },
                      child: Text(
                        L.chat_contact_cancel.tr,
                        style: TextStyle(
                            fontSize: 14.sp, fontWeight: FontWeight.w500),
                      ),
                    ),
                  ),
                ],
              ),
              Expanded(
                child: Listener(
                  onPointerDown: (down) {
                    focusNode.unfocus();
                  },
                  onPointerMove: (down) {
                    focusNode.unfocus();
                  },
                  child: Obx(
                    () => ListView.separated(
                      padding: EdgeInsets.only(bottom: 7.r),
                      controller: controller.scrollController,
                      itemCount: controller.get().length,
                      itemBuilder: (BuildContext context, int index) {
                        var model = controller.get()[index];
                        return SquareItemView(data: model,isWhiteBg : true, onPressed: _onPressed);
                      }, separatorBuilder: (context, index) {
                      var padding = EdgeInsets.only(left: 18.r, right: 0.r);
                      if (index == 0) {
                        padding = const EdgeInsets.only();
                      }
                      return Container(
                        padding: padding,
                        child: const DividerCus(
                          thickness: 0.5,
                        ),
                      );
                    },
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      }),
    );
  }

  void _onPressed(ChannelInfoModelData model) {
    Get.toNamed(Routes.ChannelJoin, arguments: {"channelId": model.id});

  }

  /// 文本变化监听
  void _onTextChange(String text, BuildContext context) {
    controller.setKey(text, newSearch: true);
  }
}
