//FileName dao_item
// <AUTHOR>
//@Date 2022/10/26 15:48
import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/data/models/channel_info_model_data.dart';
import 'package:flutter_metatel/core/utils/util.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../core/languages/l.dart';
import '../../../core/values/colors.dart';
import '../../../r.dart';
import '../../data/enums/enum.dart';

class SquareItemView extends StatelessWidget {
  final ChannelInfoModelData data;
  final bool isWhiteBg;

  /// 点击
  final ValueChanged<ChannelInfoModelData>? onPressed;

  const SquareItemView(
      {required this.data, this.isWhiteBg = false, this.onPressed, super.key});

  @override
  Widget build(BuildContext context) {
    String desc = (data.describe?.isEmpty ?? true)
        ? L.introduction_yet.tr
        : data.describe ?? '';
    Rx<Color> backColor = AppColors.white.obs;
    return Obx(() => Container(
          padding: isWhiteBg?null: EdgeInsets.only(left: 22, right: 22, top: 5, bottom: 5).r,
          child: Ink(
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(isWhiteBg?0:10),
                color: backColor.value,
                boxShadow: [
                  BoxShadow(
                      color: backColor.value,
                      offset: const Offset(2.0, 2.0),
                      blurRadius: 0.r,
                      blurStyle: BlurStyle.normal),
                ]),
            child: InkWell(
              borderRadius: BorderRadius.circular(isWhiteBg?0:10),
              radius: 1,
              onTap: () => ItemClickFunction(startFunction: () {
                backColor.value = AppColors.colorFFF2F2F2;
              }, endFunction: () {
                onPressed?.call(data);
                backColor.value = AppColors.white;
              }),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    margin: const EdgeInsets.only(
                            left: 12, right: 12, top: 5, bottom: 5)
                        .r,
                    alignment: Alignment.centerLeft,
                    height: 80.r,
                    child: Row(
                      children: [
                        buildChatAvatarWithAttr(
                          ChatType.channelChat.index,
                          data.id ?? "",
                          diameter: 61,
                          text: data.title,
                          imagePath: data.avatar,
                          isNet: true,
                          channelAttribute: data.attribute ?? 0,
                        ),
                        // 间隔
                        SizedBox(width: 13.r),
                        Expanded(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Row(
                                children: [
                                  // 名称
                                  Expanded(
                                    child: Text(
                                      data.title ?? '',
                                      overflow: TextOverflow.ellipsis,
                                      style: TextStyle(
                                        color: Colors.black,
                                        fontSize: 14.sp,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                              // 间隔
                              SizedBox(height: 1.h),
                              Row(
                                children: [
                                  Image.asset(
                                    R.iconGroupNumber,
                                    width: 11.r,
                                    height: 11.r,
                                    color: AppColors.colorFF249ED9,
                                  ),
                                  SizedBox(
                                    width: 3.r,
                                  ),
                                  Text(
                                    '${data.memberCount}',
                                    style: TextStyle(
                                      color: Colors.black,
                                      fontSize: 12.sp,
                                      fontWeight: FontWeight.w300,
                                    ),
                                  ),
                                ],
                              ),

                              // SizedBox(height: 2.r),
                              Text(
                                desc,
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                                style: TextStyle(
                                  fontSize: 10.sp,
                                  color: Colors.black,
                                  fontWeight: FontWeight.w300,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ));
  }
}
