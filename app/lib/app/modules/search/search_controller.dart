import 'package:async/async.dart';
import 'package:flutter_metatel/app/data/models/search_model.dart';
import 'package:flutter_metatel/core/languages/l.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:flutter_metatel/r.dart';
import 'package:get/get.dart';
import '../../../core/utils/util.dart';
import '../../../core/values/config.dart';
import '../../data/enums/enum.dart';
import '../../data/providers/db/database.dart';

class SearchController extends GetxController {
  final RxList _searchList = [].obs;
  String? _key;
  late int type;
  List<SearchChatUserBean> _currentSearchBeanList = [];
  getKey() => _key;

  get() => _searchList;
  int searchIndex = 1;
  @override
  void onInit() {
    AppLogger.i('type===onInit _currentSearchBeanList =${_currentSearchBeanList.length}');
    super.onInit();
    type = Get.arguments['type'] ?? SearchResultType.all;
    AppLogger.i('type===$type');
  }

  void setKey(String? key) async{
    searchIndex++;
    _key = key;
    _searchList.clear();
    await _myCancelableFuture?.cancel();
    if (key?.isNotEmpty ?? false) {
      _myCancelableFuture = CancelableOperation.fromFuture(
        _search({'key':_key,'index':searchIndex}),
        onCancel: () => 'Future has been canceld',
      );
    }

  }
  bool keyLegalVerification(int index) {
    var isEmpty = _key?.isEmpty ?? true;
    if (searchIndex!= index || isEmpty) {
      if(isEmpty){
        _searchList.clear();
      }
      return false;
    }
    return true;
  }
  CancelableOperation? _myCancelableFuture;

   _search(Map map) async {
    var list = [];
     String? key = map['key'];
     int index = map['index'];
    if ((type & SearchResultType.contactor) == SearchResultType.contactor) {
      var contact =
          await Get.find<AppDatabase>().searchContact(key ?? '').get();
      if (!keyLegalVerification(index)) {
        return;
      }
       var meetingRobot =
          await Get.find<AppDatabase>().oneContact(Config.meetingRobot).get();
      AppLogger.d("messages=== meetingRobot=== $meetingRobot");
      if (meetingRobot.isNotEmpty && (key?.isNotEmpty == true)) {
        var robot = meetingRobot.first;
        String name = robot.localname ?? robot.displayname ?? '';
        if (name.toUpperCase().contains(key!.toUpperCase())) {
          list.add(SearchModel(
              title: name,
              userName: robot.username,
              avatarPath: robot.avatarPath,
              type: SearchResultType.contactor,
              obj: robot));
        }
      }
      if (contact.isNotEmpty) {
        list.add(SearchModel(
            type: (SearchResultType.contactor | SearchResultType.item)));
        for (var item in contact) {
          if (!keyLegalVerification(index)) {
            return;
          }
          list.add(SearchModel(
              title: item.localname ?? item.displayname,
              userName: item.username,
              avatarPath: item.avatarPath,
              isTid: item.isTid,
              displayName: item.displayname,
              chatType: 0,
              type: item.type==ContactType.fileHelper?SearchResultType.function:SearchResultType.contactor,
              obj: item));
        }
      }
    }
    _searchList.clear();
    _searchList.addAll(list);
    list = [];
    if ((type & SearchResultType.group) == SearchResultType.group) {
      if (!keyLegalVerification(index)) {
        return;
      }
      var channelInfo =
      await Get.find<AppDatabase>().searchChannel(key ?? '').get();
      AppLogger.d("messages=== channelInfo.length ${channelInfo.length}");

      if (channelInfo.isNotEmpty) {
        list.add(SearchModel(
            type: (SearchResultType.group | SearchResultType.item)));
        for (var item in channelInfo) {
          if (!keyLegalVerification(index)) {
            return;
          }
          if (item.owner?.isEmpty ?? true) {
            continue;
          }
          list.add(SearchModel(
              title: item.title,
              userName: item.channelId,
              avatarPath: item.avatarPath,
              content: item.describe,
              chatType: ChatType.channelChat.index,
              type: SearchResultType.group,
              obj: item));
        }
      }
      if (!keyLegalVerification(index)) {
        return;
      }
      var groupInfo =
      await Get.find<AppDatabase>().searchGroup(key ?? '').get();
      AppLogger.d("messages=== groupInfo length=${channelInfo.length}");

      if (groupInfo.isNotEmpty) {
        if (channelInfo.isEmpty) {
          list.add(SearchModel(
              type: (SearchResultType.group | SearchResultType.item)));
        }
        for (var item in groupInfo) {
          if (!keyLegalVerification(index)) {
            return;
          }
          if (item.owner?.isEmpty ?? true) {
            continue;
          }
          list.add(SearchModel(
              title: item.title,
              userName: item.groupId,
              avatarPath: item.avatarPath,
              content: item.describe,
              chatType: ChatType.groupChat.index,
              type: SearchResultType.group,
              obj: item));
        }
      }
      if (!keyLegalVerification(index)) {
        return;
      }
      _searchList.addAll(list);
      AppLogger.d("messages=== _searchList length=${_searchList.length}");

    }
    list = [];
    if ((type & SearchResultType.chat) == SearchResultType.chat) {
      if (!keyLegalVerification(index)) {
        return;
      }
      var messages =
          await Get.find<AppDatabase>().searchMessage(key ?? '').get();
      AppLogger.d("messages=== messages length =${messages.length} _key=$_key key=$key");
      if (!keyLegalVerification(index)) {
        return;
      }
      if (messages.isNotEmpty) {
        if (!keyLegalVerification(index)) {
          return;
        }
        list.add(
            SearchModel(type: (SearchResultType.chat | SearchResultType.item)));

        for (var item in messages) {
          if (item.owner?.isEmpty ?? true) {
            continue;
          }
          String? title, avatarPath;
          bool? isTid;
          var userBean = _currentSearchBeanList
              .firstWhereOrNull((e) => e.owner == item.owner);
          if (userBean != null && userBean.owner.isNotEmpty) {
            title = userBean.title;
            avatarPath = userBean.avatarPath;
            isTid = userBean.isTid;
            AppLogger.d(
                "messages=== _currentSearchBeanList ${_currentSearchBeanList.length} ");
          } else {
            if (item.chatType == ChatType.singleChat.index) {
              if (item.owner == Config.fileHelperOwner) {
                title = L.chat_contact_file_transfer_assist.tr;
                avatarPath = R.icFileHelper;
              } else {
                var tmpCurrentUser = await Get.find<AppDatabase>()
                    .oneContact(item.owner!)
                    .getSingleOrNull();
                title =
                    tmpCurrentUser?.localname ?? tmpCurrentUser?.displayname;
                avatarPath = tmpCurrentUser?.avatarPath;
                isTid = tmpCurrentUser?.isTid;
              }
            } else if (item.chatType == ChatType.groupChat.index) {
              var tmpCurrentUser = await Get.find<AppDatabase>()
                  .oneGroupInfo(item.owner!)
                  .getSingleOrNull();
              title = tmpCurrentUser?.title;
              avatarPath = tmpCurrentUser?.avatarPath;
            } else if (item.chatType == ChatType.channelChat.index) {
              var tmpCurrentUser = await Get.find<AppDatabase>()
                  .oneChannelInfo(item.owner!)
                  .getSingleOrNull();
              title = tmpCurrentUser?.title;
              avatarPath = tmpCurrentUser?.avatarPath;
            } else if (item.chatType == ChatType.officialChat.index) {
              title = getOfficeDisplayName();
            }
            _currentSearchBeanList
                .add(SearchChatUserBean(item.owner!, title, avatarPath, isTid));
          }

          list.add(SearchModel(
              title: title,
              userName: item.owner!,
              avatarPath: avatarPath,
              content: item.body,
              chatType: item.chatType,
              type: SearchResultType.chat,
              id: item.msgId,
              isTid: isTid,
              obj: item));
        }
      }
    }
    _searchList.addAll(list);
  }
}
class SearchChatUserBean {
  SearchChatUserBean(this.owner,this.title,this.avatarPath,this.isTid);
  String owner;
  String? title;
  String? avatarPath;
  bool? isTid;
}
