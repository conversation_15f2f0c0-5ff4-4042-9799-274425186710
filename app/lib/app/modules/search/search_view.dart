import 'package:extended_text/extended_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/data/models/search_model.dart';
import 'package:flutter_metatel/app/data/providers/db/database.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:flutter_metatel/core/utils/string_util.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../core/languages/l.dart';
import '../../../core/utils/util.dart';
import '../../../core/values/colors.dart';
import '../../../core/values/config.dart';
import '../../../r.dart';
import '../../../routes/pages.dart';
import '../../data/enums/enum.dart';
import '../../data/models/user_message_model.dart';
import '../../widgets/at_widget/my_special_text_span_builder.dart';
import '../../widgets/divider_cus.dart';
import 'search_controller.dart' as my_search;

class SearchView extends GetView<my_search.SearchController> {
  final FocusNode focusNode = FocusNode();

  SearchView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      resizeToAvoidBottomInset: false,
      backgroundColor: Colors.white,
      body: GetBuilder<my_search.SearchController>(builder: (controller) {
        return Padding(
          padding: const EdgeInsets.only(top: kToolbarHeight),
          child: Column(
            children: [
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Expanded(
                    child: Container(
                      height: 36.r,
                      padding: const EdgeInsets.only(
                        left: 15,
                      ).r,
                      child: TextField(
                        autofocus: true,
                        focusNode: focusNode,
                        style: TextStyle(fontSize: 16.sp),
                        onChanged: (text) => _onTextChange(text, context),
                        textAlign: TextAlign.start,
                        textAlignVertical: TextAlignVertical.center,
                        decoration: InputDecoration(
                          hintText: L.searbar_hint_search.tr,
                          hintStyle: TextStyle(color: AppColors.colorFF7F7F7F,fontSize: 12.sp,fontWeight: FontWeight.w400),

                          // 设置后，提升文本居中
                          contentPadding: EdgeInsets.zero,
                          prefixIcon: const Icon(
                            Icons.search,
                            color: Color.fromARGB(255, 89, 90, 90),
                          ),
                          filled: true,
                          fillColor: const Color(0xffF7F7F7),
                          border: const OutlineInputBorder(
                            borderSide: BorderSide.none,
                            borderRadius: BorderRadius.all(Radius.circular(25)),
                          ),
                        ),
                      ),
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.only(),
                    child: TextButton(
                      onPressed: () {
                        FocusManager.instance.primaryFocus?.unfocus();
                        Future.delayed(const Duration(milliseconds: 100), () {
                          Get.back();
                        });
                      },
                      child: Text(L.chat_contact_cancel.tr,
                          style: TextStyle(
                              fontSize: 14.sp, fontWeight: FontWeight.w500)),
                    ),
                  ),
                ],
              ),
              Expanded(
                child: Listener(
                    onPointerDown: (down) {
                      focusNode.unfocus();
                    },
                    onPointerMove: (down) {
                      focusNode.unfocus();
                    },
                    child: Obx(
                      () => ListView.builder(
                          padding: const EdgeInsets.only(),
                          itemCount: controller.get().length,
                          itemBuilder: (BuildContext context, int index) {
                            var model = controller.get()[index];
                            return createWight(model);
                          }),
                    )),
              ),
            ],
          ),
        );
      }),
    );
  }

  createWight(SearchModel model) {
    if ((model.type! & SearchResultType.item) == SearchResultType.item) {
      return _buildItemTypeWidget(model.type);
    }
    switch (model.type) {
      case SearchResultType.contactor:
      case SearchResultType.session:
      case SearchResultType.chat:
    }
    return _buildDataWidget(model);
  }

  void _onPressed(SearchModel model) {
    if (model.type == SearchResultType.chat ||
        model.type == SearchResultType.group ||
        model.type == SearchResultType.function) {
      UserMessage userMessage;
      userMessage = UserMessage(
          chatType: model.chatType,
          displayName: model.title,
          userName: model.userName,
          msgId: model.id,
          avatarPath: model.avatarPath);
      Get.back();
      Get.toNamed(Routes.MESSAGE, arguments: userMessage);
    } else if (model.type == SearchResultType.contactor &&
        model.obj is ContactData) {
      if (model.userName == Config.meetingRobot) {
        var userMessage = UserMessage(
            chatType:  ChatType.singleChat.index,
            displayName: model.displayName,
            userName: model.userName,
            avatarPath: model.avatarPath);
        Get.back();
        Get.toNamed(Routes.MESSAGE, arguments: userMessage);
      } else {
        Get.back();
        Get.toNamed(Routes.CONTACT_DETAIL, arguments: model.obj);
      }
    }
  }

  TextEditingController controller2 = TextEditingController()
    ..text =
        '[33]Extended text field help you to build rich text quickly. any special text you will have with extended text field. this is demo to show how to create special text with widget span.'
            '\n\nIt\'s my pleasure to invite you to join \$FlutterCandies\$ if you want to improve flutter .[36]'
            '\n\nif you meet any problem, please let me konw @zmtzawqlp .[44]';
  Widget _buildDataWidget(SearchModel model) {
    var padding =
        const EdgeInsets.only(left: 12, right: 0, top: 5, bottom: 0).r;
    if(model.chatType == 0){
      var userName = Config.userNameWithoutDomain;
      if(model.userName!.contains(userName)){
        model.title = L.metatel_file_assistant.tr;
      }
    }
    bool showTitleIc = model.chatType == 3 || (model.isTid??false);
    Widget titleIc = Container();
    if(showTitleIc){
      if(model.chatType == 3){
        titleIc = Image.asset(
          R.officialSymbol,
          width: 15.r,
          height: 15.r,
        );
      } else {
        titleIc = Image.asset(
          R.icTid,
          width: 21.r,
        );
      }
    }
    Rx<Color> backColor = Colors.white.obs;
    return Obx(() => Material(
      color: backColor.value,
      child: InkWell(
        radius: 1,
        onTap: () {
          ItemClickFunction(startFunction: () {
            backColor.value = AppColors.colorFFF2F2F2;
          }, endFunction: () {
            _onPressed(model);
            backColor.value = AppColors.white;
          });
        },
        child: Container(
          height: 50.h,
          padding: padding,
          child: Row(
            children: [
              // 头像
              createHeader(model.chatType ?? 0,
                  appSupporAbsolutePath(model.avatarPath), model.title,model.userName,model.isTid),
              // 间隔
              SizedBox(width: 10.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.max,
                  children: [
                    // 名称
                    if (model.type == SearchResultType.contactor)
                      const Spacer(),
                    Expanded(
                      child: Row(
                        children: [
                          RichText(
                              text: TextSpan(
                                children: StringUtil.getTextSpanList(
                                    model.title ?? '',
                                    searchContent:
                                    controller.getKey() ?? '',
                                    fontSize: 14.sp),
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis),
                          Visibility(
                              visible: showTitleIc,
                              child: Container(
                                padding: const EdgeInsets.only(left: 5),
                                child: titleIc,
                              )),
                        ],
                      ),
                    ),
                    // const Spacer(),
                    if (model.type == SearchResultType.contactor)
                      const Spacer(),
                    Visibility(
                      visible: model.content?.isNotEmpty ?? false,
                      child: Expanded(
                        child: ExtendedText(
                          model.content ?? '',
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          specialTextSpanBuilder: MySpecialTextSpanBuilder(
                            showAtBackground: false,
                          ),
                          style: TextStyle(
                              fontSize: 14.sp, color: Colors.grey),
                        ),
                      ),
                    ),
                    const DividerCus(),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    ));
  }

  Widget createHeader(int chatType, String? avatar, String? displayName, String? userName,bool? isTbs) {
    AppLogger.d('createHeader displayName=$displayName');
    return buildChatAvatarWithAttr(
      chatType,
      userName ?? "",
      diameter: 35.r,
      imagePath: avatar,
      text: displayName,
      textStyle: const TextStyle(fontSize: 16, color: Colors.white),
    );
  }

  Widget _buildItemTypeWidget(int? type) {
    return Container(
      height: 30,
      padding: const EdgeInsets.only(left: 15.0),
      width: double.infinity,
      alignment: Alignment.centerLeft,
      child: Text(
        getTypeValue(type ?? SearchResultType.contactor),
        textScaleFactor: 1.3,
      ),
    );
  }

  String getTypeValue(int type) {
    String ret = "";
    if ((type & SearchResultType.contactor) == SearchResultType.contactor) {
      ret = L.main_contact.tr;
    } else if ((type & SearchResultType.session) == SearchResultType.session) {
      ret = L.search_chat.tr;
    } else if ((type & SearchResultType.group) == SearchResultType.group) {
      ret = L.search_group_chat.tr;
    } else if ((type & SearchResultType.function) ==
        SearchResultType.function) {
      ret = L.search_fumc.tr;
    } else if ((type & SearchResultType.chat) == SearchResultType.chat) {
      ret = L.search_chat.tr;
    }
    return ret;
  }

  /// 文本变化监听
  void _onTextChange(String text, BuildContext context) {
    controller.setKey(text);
  }
}
