//FileName qr_info
// <AUTHOR>
//@Date 2024/4/9 14:21
/*
 * @Author: l<PERSON><PERSON> <EMAIL>
 * @Date: 2022-05-06 16:40:04
 * @LastEditors: luoyuan <EMAIL>
 * @LastEditTime: 2022-05-06 17:17:27
 * @FilePath: \flutter_metatel\lib\app\modules\home\settings\qrcode\my_qrcode_page.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AEqt
 */

import 'package:extended_text/extended_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/widgets/app_bar_cus.dart';
import 'package:flutter_metatel/app/widgets/at_widget/my_special_text_span_builder.dart';
import 'package:flutter_metatel/app/widgets/at_widget/selection_area.dart';
import 'package:flutter_metatel/core/languages/l.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../../core/values/colors.dart';

class QrInfoView extends StatelessWidget {
  QrInfoView({Key? key, required this.data}) : super(key: key);
  final String data;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.backgroundGray,
      appBar: AppBarCommon().build(context,
          title: L.qr_code_info.tr, backgroundColor: AppColors.backgroundGray),
      body: Container(
        margin: const EdgeInsets.only(top: 20),
        child: Center(
        heightFactor: 1,
        child: CommonSelectionArea(
          joinZeroWidthSpace:true,
          child: ExtendedText(
            data,
            textAlign: TextAlign.left,
            specialTextSpanBuilder: MySpecialTextSpanBuilder(
                showAtBackground: false, size: Size(15.r, 15.r)
            ),
            style: TextStyle(
                fontSize: 14.sp,
                color: AppColors.colorFF666666,
                fontWeight: FontWeight.bold),
          ),
        ),
      ),),
    );
  }

}
