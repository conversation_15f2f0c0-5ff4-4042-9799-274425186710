/*
 * @Author: your name
 * @Date: 2022-04-25 16:41:18
 * @LastEditTime: 2022-04-28 17:41:00
 * @LastEditors: Please set LastEditors
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \flutter_metatel\lib\app\modules\home\session\session_view.dart
 */
import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/data/providers/db/database.dart';
import 'package:flutter_metatel/app/data/services/config_service.dart';
import 'package:flutter_metatel/app/modules/home/<USER>/appbar.dart';
import 'package:flutter_metatel/core/task/ads_task.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:flutter_metatel/core/values/colors.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:get/get.dart';
import 'package:flutter_metatel/core/languages/l.dart';

import '../../../../core/utils/util.dart';
import '../../../data/services/chatio_service.dart';
import '../appbar/home_appbar_base.dart';
import 'session_controller.dart';
import 'session_list_item.dart';

class SessionPage extends StatefulWidget {
  const SessionPage({Key? key}) : super(key: key);

  @override
  State<SessionPage> createState() => _SessionPageState();
}

class _SessionPageState extends State<SessionPage> {
  final SessionController controller =
      Get.put(SessionController(), permanent: true);

  Widget _buildTips() {
    return Obx(
      () {
        bool showWarn = false;
        GestureTapCallback? func;
        String text = '';

        // 1
        if (controller.isChatioMsg || controller.isChannelMsg) {
          text = L.receive_tips.tr;
          func = null;
          showWarn = false;
        }

        // 2
        var exception = controller.rxExceptionHint.value;
        if (exception.isNotEmpty) {
          text = exception;
          func = reConnect;
          showWarn = true;
        }

        // 3
        if (controller.isNoNetwork) {
          text = L.other_connect_not_avail.tr;
          func = null;
          showWarn = true;
        }

        // 4
        if (controller.isReconnect) {
          text = L.connect_tips.tr;
          func = null;
          showWarn = false;
        }

        return showWarn
            ? Visibility(
                visible: text.isNotEmpty,
                child: GestureDetector(
                  onTap: func,
                  child: Container(
                    height: 30.h,
                    color: const Color.fromARGB(200, 205, 58, 58),
                    padding: EdgeInsets.only(left: 16.w, right: 16.w),
                    child: Row(
                      children: [
                        Image.asset(
                          'assets/images/warn.png',
                          width: 15.5.w,
                          height: 15.5.h,
                        ),
                        SizedBox(width: 5.w),
                        Expanded(
                          child: Text(
                            text,
                            overflow: TextOverflow.ellipsis,
                            style: TextStyle(
                              fontSize: 12.sp,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              )
            : Visibility(
                visible: text.isNotEmpty,
                child: SizedBox(
                  height: 30.h,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SizedBox(
                        width: 16.sp,
                        height: 16.sp,
                        child: const CircularProgressIndicator(strokeWidth: 2),
                      ),
                      const SizedBox(width: 5),
                      Text(
                        text,
                        style: TextStyle(
                          fontSize: 16.sp,
                          color: Colors.black,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              );
      },
    );
  }


  Widget _buildBody() {
    return Container(
      child: Column(
        children: [
          // 提示
          _buildTips(),
          Obx(() {
            var index = controller.adsIndex.value;
            AppLogger.d('adsIndex =$index');
            return AdsTask().createMarquee();
          }),
          Expanded(
            child: RefreshIndicator(
              displacement: 5,
              onRefresh: () async {
                controller.onRefresh();
              },
              notificationPredicate: (_) {
                return controller.isConnect();
              },
              child: SlidableAutoCloseBehavior(
                child: Obx(
                  () {
                    var allData = controller.allSessionDataObs;
      
                    return allData.isEmpty
                        ? buildNoData()
                        : _createDateView();
                  },
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _createDateView() {
    return Column(
      children: [
        TabBar(
          controller: controller.tabController,
          isScrollable: true,
          tabAlignment: TabAlignment.start,
          indicatorColor: AppColors.colorFF249ED9,
          indicatorPadding: EdgeInsets.symmetric(horizontal: 8.r),
          labelStyle: TextStyle(
            fontSize: 13.sp,
            fontWeight: FontWeight.w800,
            color: AppColors.colorFF249ED9,
          ),
          unselectedLabelStyle: TextStyle(
            fontSize: 13.sp,
            color: Colors.black,
            fontWeight: FontWeight.w400,
          ),
          padding: EdgeInsets.symmetric(horizontal: 16.r),
          labelPadding: EdgeInsets.symmetric(vertical: 10.r, horizontal: 16.r),
          tabs: [
            for (var item in controller.sessionType)
              ConstrainedBox(
                constraints: BoxConstraints(minWidth: 40.r),
                child: Center(
                  child: Text(
                    item.tr,
                    style: TextStyle(fontSize: 14.sp),
                  ),
                ),
              ),
          ],
        ),
        Container(
          width: double.infinity,
          height: 1.r,
          color: AppColors.colorFFd9d9d9.withValues(alpha: 0.5),
        ),
        Expanded(child: _buildSessionBody()),
      ],
    );
  }
  Widget _buildSessionBody() {
    var allData = controller.allSessionDataObs;
    var privateData = controller.privateSessionDataObs;
    var channelData = controller.channelSessionDataObs;
    var daoData = controller.daoSessionDataObs;
    var privateGroupData = controller.privateGroupSessionDataObs;

    return TabBarView(
      controller: controller.tabController,
      physics: const BouncingScrollPhysics(),
      children: [
        _createDataView(allData,key: controller.listViewKey,scontroller: controller.scrollController),
        _createDataView(privateData),
        _createDataView(channelData),
        _createDataView(daoData),
        _createDataView(privateGroupData),
      ],
    );
  }


  Widget _createDataView(List<SessionData> allData,
      {Key? key, ScrollController? scontroller}) {
    return ListView.builder(
      physics: const AlwaysScrollableScrollPhysics(),
      itemCount: allData.length,
      controller: scontroller,
      key: key,
      itemBuilder: (context, index) {
        var sessionData = allData[index];
        return GetBuilder<SessionController>(
          id: sessionData.username,
          builder: (control) {
            String? draft =
                Get.find<AppConfigService>().getDraft(sessionData.username);
            Rx<Color> backColor = (sessionData.top ?? false
                    ? const Color.fromARGB(255, 239, 238, 238)
                    : const Color(0xFFFFFFFF))
                .obs;
            return SessionListItem(
              sessionData: sessionData,
              backColor: backColor,
              onTopChanged: controller.topChange,
              onReadChanged: controller.readChange,
              onDelete: controller.delete,
              onPressed: controller.pressed,
              draft: draft,
            );
          },
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    AppLogger.d('SessionPage build');
    return createAppBar(
      title: L.main_chat.tr,
      type: SearchType.sessions,
      body: _buildBody(),
    );
  }
}
