/*
 * <AUTHOR> <PERSON><PERSON>
 * @Date         : 2022-05-18 15:13:34
 * @Description  : 
 * @LastEditors  : <PERSON><PERSON>
 * @LastEditTime : 2022-06-14 19:02:23
 * @FilePath     : /flutter_metatel/lib/app/modules/home/<USER>/contact_view.dart
 */
import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/modules/home/<USER>/appbar.dart';
import 'package:flutter_metatel/core/values/config.dart';

import 'package:get/get.dart';

import '../../../../core/languages/l.dart';
import '../../../../core/utils/app_log.dart';
import '../../../data/enums/enum.dart';
import '../../../data/models/v_contact_model.dart';
import '../appbar/home_appbar_base.dart';
import 'comm_contact/comm_contact_view.dart';
import 'contact_controller.dart';

class ContactView extends StatefulWidget {
  const ContactView({Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _ContactStatePage();
  }
}

class _ContactStatePage extends State<ContactView> {
  ContactController controller = ContactController();

  @override
  void initState() {
    controller.loadData();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return createAppBar(
      title: L.main_contact.tr,
      type: SearchType.contacts,
      canSearch: !Config.isOversea,
      showTitle: Config.isOversea,
      body: CommContactView(
        key: mainContact,
        commentContactState: CommentContactState.NONE,
        changedCallback: _changedCallback,
        isShowGroup: true,
      ),
    );
  }

  void _changedCallback(VContactDate? date) {
    AppLogger.d('_changedCallback ${date.toString()}');
  }
}
