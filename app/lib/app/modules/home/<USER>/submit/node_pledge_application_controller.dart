import 'package:async_task/async_task_extension.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_metatel/app/data/services/config_service.dart';
import 'package:flutter_metatel/app/modules/home/<USER>/submit/set_pin_node_pledge_application_view.dart';
import 'package:flutter_metatel/app/modules/wallet/wallet_manage.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:flutter_metatel/core/utils/events_bus.dart';
import 'package:flutter_metatel/core/utils/util.dart';
import 'package:flutter_web3/app/data/models/wallet_model.dart';
import 'package:get/get.dart';

import '../../../../../core/languages/l.dart';
import '../../../../../core/values/config.dart';
import '../../../../../routes/pages.dart';
import '../../../../data/events/events.dart';
import '../../../../data/models/res/keybox_info_res.dart';
import '../../../../data/models/res/staking_list_model_res.dart';
import '../../../../data/models/res/staking_node_info_res.dart';
import '../../../../data/models/res/staking_service_list_res.dart';
import '../../../../data/providers/api/staking_api.dart';
import '../detail/node_staking_detail_view.dart';
import '../staking_helper.dart';
import 'node_pledge_application_view.dart';
import 'node_pledge_application_view_1_2.dart';

class NodePledgeApplicationController extends GetxController
    with GetTickerProviderStateMixin {
  TextEditingController editControllerDomain = TextEditingController();
  TextEditingController editControllerDesc = TextEditingController();

  TextEditingController editPinController = TextEditingController();
  TextEditingController editPinAgainController = TextEditingController();

  TextEditingController editControllerMinerName = TextEditingController();
  TextEditingController editControllerStakingValue = TextEditingController();

  String? key = '';
  RxString node = ''.obs; //dev.linksay.site
  RxString mainWallet = ''.obs;
  RxString serviceWallet = ''.obs;
  String address = '';
  RxInt keyBoxType = 0.obs;
  KeyBoxInfoRes? _keyBoxInfoRes;
  StakingNodeInfoData? stakingNodeInfoData;
  List<ServiceListData>? serviceListData; //已经提交到服务器的节点信息
  RxBool mainWalletCanChange = true.obs;
  RxBool serviceWalletCanChange = true.obs;
  List<StakingListData>? stakingListAll;
  RxBool obscureTextOld = true.obs;
  RxBool obscureTextAgain = true.obs;
  RxBool showNewPinLength = true.obs;
  RxBool showNewAgainPinLength = true.obs;
  RxInt serviceType = 1.obs; //节点类型 2 : 区块节点，1 : 普通节点
  RxString chainId = ''.obs; //链ID
  bool useDefaultKeyBoxType = false;
  RxInt minStakingNum = 0.obs; //最低质押数量
  String minerName = '';
  RxString currentStatusValue = ''.obs;
  RxDouble totalBalance = 0.0.obs;
  Timer? _timer;

  @override
  void onInit() {
    super.onInit();
    mainWallet.value = Get.find<AppConfigService>().readMainWallet() ?? '';
    stakingListAll = Get.arguments;
    getNodeTypeInfo();
  }

  onChangeServiceType(int index) {
    serviceType.value = index;
  }

  onDomainTextChange(String? text) {
    if (text != node.value) {
      chainId.value = '';
    }
    node.value = text ?? '';
  }

  onMinerNameTextChange(String? text) {
    text = text?.trimLeft();
    if(text?.isNotEmpty ?? false){
      var first = text?.substring(0,1).toUpperCase();
      var other = text?.replaceFirst(first!, '');
      text = '$first$other';
    }
    minerName = text ?? '';
  }

  onStakingValueTextChange(String? text) {}

  onNewPinTextChange(String? text) {
    showNewPinLength.value = (text?.length ?? 0) < 6;
  }

  onNewPinAgainTextChange(String? text) {
    showNewAgainPinLength.value =
        editPinController.text.trim() != editPinAgainController.text.trim();
  }

  getNodeTypeInfo() async {
    var res = await Get.find<StakingApi>().getStakingConf();
    stakingNodeInfoData = res.data?.data;
  }

  setAvailableNodeIsChecked(AvailableNode node) {
    if (stakingNodeInfoData?.availableNode?.isEmpty ?? true) {
      return;
    }
    if (node.value != null) {
      keyBoxType.value = node.value!.toInt();
    }
    for (var n in stakingNodeInfoData!.availableNode!) {
      if (node == n) {
        n.setSelected(true);
      } else {
        n.setSelected(false);
      }
    }
  }

  bool legalityCheckPin(String pin1, String pin2) {
    if (pin1.isEmpty) {
      toast(L.the_pin_codes_can_not_empty.tr);
      return false;
    } else if (pin1 != pin2) {
      toast(L.the_pin_codes_entered_twice_are_inconsistent.tr);
      return false;
    } else if (pin1.length < 6) {
      toast(L.the_pin_code_cannot_be_less_than_6_digits.tr);
      return false;
    }
    if ((key?.isEmpty ?? true) || (key?.length ?? 0) < 10) {
      toast(L.the_sevice_wallet_error.tr);
      return false;
    }
    if (node.value.isEmpty) {
      toast(L.the_node_can_not_empty.tr);
      return false;
    }
    return true;
  }

  //上传密钥信息
  submitPin() async {
    if (key?.isEmpty ?? true) {
      var k = WalletManage().getWalletKeyByAddress(serviceWallet.value);
      if (k != null && !k.startsWith('0x')) {
        key = '0x$k';
      }
    }
    var pin1 = editPinController.text.trim();
    var pin2 = editPinAgainController.text.trim();
    bool lega = legalityCheckPin(pin1, pin2);
    if (!lega) {
      return;
    }
    showLoadingDialog();
    Map<String, dynamic> map = {
      "pin": pin2,
      "key": key,
      "node":node.value /*'testnet.linksay.site'*/
    };
    if (serviceType.value == 2) {
      var stakingValue = editControllerStakingValue.text;
      map['amount'] = int.parse(stakingValue);
      map['desc'] = minerName;
    }
    var res = await Get.find<StakingApi>().submitUpdate(node.value, map);
    _keyBoxInfoRes = res.data;
    if (res.data?.code == 200) {
      if (serviceType.value == 1) {
        _submitNodeInfoToService();
        dismissLoadingDialog();
      } else {
        _submitNodeInfoToService();
        Get.back();
        showLoadingDialog(isBack: false);
        startTimer();
      }

    } else {
      toast('${res.data?.code} error -1');
      dismissLoadingDialog();
    }
    Future.delayed(const Duration(seconds: 3), () {
      _getKeyBoxInfoReq();
    });
  }

  //将主控钱包，服务钱包上传到服务器做记录
  _submitNodeInfoToService() async {
    AppConfigService conf = Get.find<AppConfigService>();
    if (mainWallet.value.isNotEmpty &&
        serviceWallet.value.isNotEmpty &&
        node.value.isNotEmpty) {
      Map<String, dynamic> m = {
        'account': conf.readUserNameWithoutDomain(),
        'main_address': mainWallet.value,
        'service_address': serviceWallet.value,
        'server_domain': node.value,
      };
      var r = await Get.find<StakingApi>().addWalletInfo(m);
      if (r.data?.code == 200) {
        if (serviceType.value == 1) {
          Get.to(NodePledgeApplicationView());
        }
      } else {
        // toast('service error 0001');
      }
    }
  }

  bool isSameMainWallet(){
    var main = Get.find<AppConfigService>().readMainWallet();
    return mainWallet.value.toUpperCase() == main?.toUpperCase() && mainWallet.value.isNotEmpty;
  }

  Future<KeyBoxInfoRes?> _getKeyBoxInfoReq() async {
    var keyBoxres = await Get.find<StakingApi>().getKeyBoxInfo(node.value);
    if (keyBoxres.data != null) {
      _keyBoxInfoRes = keyBoxres.data;
      //也可以通过节点信息获取是否是区块节点
      serviceType.value = (_keyBoxInfoRes?.blockHight ?? 0) > 0 ? 2 : 1;
    }
    return keyBoxres.data;
  }

  //获取服务器上有没有节点相关的信息
  getServiceList() async {
    serviceWallet.value = '';
    if (node.value.isEmpty) {
      toast(L.the_node_can_not_empty.tr);
      return;
    }
    showLoadingDialog();
    var stakingData =
    stakingListAll?.firstWhereOrNull((e) => e.serverDomain == node.value);
    if (stakingData != null && stakingData.id != null) {
      Get.back();
      Get.to(NodeStakingDetailView(), arguments: stakingData);
    } else {
      var r = await Get.find<StakingApi>().getServiceList();
      if (r.data?.code == 200) {
        serviceListData = r.data?.data;
        if (node.value.isNotEmpty) {
          var data =
          serviceListData?.firstWhereOrNull((e) => e.domain == node.value);
          if (data != null) {
            if (data.mainAddress?.isNotEmpty ?? false) {
              mainWalletCanChange.value = false;
              mainWallet.value = data.mainAddress ?? '';
            }
            if (data.serviceAddress?.isNotEmpty ?? false) {
              serviceWalletCanChange.value = false;
              serviceWallet.value = data.serviceAddress ?? '';
            }
          }
        }
        var res = await _getKeyBoxInfoReq();
        await getDomainInfo();
        if (res?.code == 602) {
          // if (mainWallet.value.isNotEmpty && serviceWallet.value.isNotEmpty) {
          //   Get.to(NodePledgeApplicationView_12());
          // } else {
          //     Get.to(NodePledgeApplicationView_12());
          // }
          Get.to(NodePledgeApplicationView_12());
          if (res?.blockHight != null && (res?.blockHight ?? 0) > 0) {
            //区块节点
            getBalance();
          }
        } else if (res?.code == 200) {
          if (res?.blockHight != null && (res?.blockHight ?? 0) > 0) {
            //区块节点
            // if (res?.role == 6) {
            //   Get.to(NodePledgeApplicationView_12());
            // }
            if (res?.address?.toUpperCase() == serviceWallet.value.toUpperCase() && serviceWallet.value.isNotEmpty) {
              Get.find<AppConfigService>().saveMainWallet(mainWallet.value);
              Get.to(NodePledgeApplicationView());
            } else {
              toast(L
                  .no_corresponding_wallet_address_found_please_import_it_to_the_wallet_first
                  .tr);
            }
          } else {//普通节点
            //已经提交过keystore到服务器上面，直接提交质押申请
            if (res?.address?.toUpperCase() == serviceWallet.value.toUpperCase() && serviceWallet.value.isNotEmpty) {
              Get.find<AppConfigService>().saveMainWallet(mainWallet.value);
              Get.to(NodePledgeApplicationView());
            } else {
              //keybox上面的钱包地址跟本地的钱包地址是否一致
              var address = res?.address;
              var exit = await getAddressExistInWallets(address ?? '');
              if (exit ?? false) {
                Get.to(SetPinNodePledgeApplicationView());
              } else {
                toast(L
                    .no_corresponding_wallet_address_found_please_import_it_to_the_wallet_first
                    .tr);
              }
            }
          }
        } else {
          toast('Node error ${res?.code}');
        }
      }
    }
    dismissLoadingDialog();
  }

  //获取keybox信息
  getKeyBoxInfo() async {
    if (mainWallet.value.isEmpty) {
      toast(L.main_wallet_can_not_empty.tr);
      return;
    }
    if (serviceWallet.value.isEmpty) {
      toast(L.server_wallet_can_not_empty.tr);
      return;
    }
    if (serviceType.value == 2) {//区块节点
      minerName = editControllerMinerName.text;
      if (minerName.length < 5) {
        toast(L.miner_name_info.tr);
        return;
      }
      var stakingValue = editControllerStakingValue.text;
      if (stakingValue.isEmpty) {
        toast(L.the_pledge_amount_cannot_be_empty.tr);
        return;
      }
      try {
        var balance = int.parse(stakingValue);
        if (balance < minStakingNum.value) {
          toast(L.the_pledge_amount_cannot_be_less_than_the_minimum_pledge_amount.tr);
          return;
        }
      } catch (e) {
        toast(L.the_pledge_amount_cannot_be_illegal.tr);
        return;
      }
    }
    showLoadingDialog(isBack: false);
    var res = await _getKeyBoxInfoReq();
    if (res?.blockHight != null && (res?.blockHight ?? 0) > 0) {//区块节点
      if (res?.code == 602) {
        Get.to(SetPinNodePledgeApplicationView());
      } else if (res?.code == 200) {
        if (res?.role == 6) {
          Get.to(SetPinNodePledgeApplicationView());
        } else {
          startTimer();
        }
      } else {
        toast('Node error ${res?.code}');
      }
    } else {//普通节点
      if (res?.code == 602) {
        Get.to(SetPinNodePledgeApplicationView());

      } else if (res?.code == 200) {
        Get.find<AppConfigService>().saveMainWallet(mainWallet.value);
        Get.to(NodePledgeApplicationView());
      } else {
        toast(' ${res?.code}');
      }
    }

    dismissLoadingDialog();
  }

  //提交申请
  submitStakingInfo() async {
    if (serviceType.value == 1 &&
        (_keyBoxInfoRes == null ||
            (_keyBoxInfoRes?.keyboxPublicKey?.isEmpty ?? true) ||
            (_keyBoxInfoRes?.nodePublickKey?.isEmpty ?? true))) {
      _getKeyBoxInfoReq();
      toast('param error');
      return;
    }

    if (keyBoxType.value <= 0) {
      toast(L.please_select_the_server_type.tr);
      return;
    }

    if (editControllerDesc.text.isEmpty) {
      toast(L.desc_wallet_can_not_empty.tr);
      return;
    }

    AppConfigService conf = Get.find<AppConfigService>();

    Map<String, dynamic> map = {
      'nickname': conf.getMySelfDisplayName(),
      'msg_account': conf.readUserNameWithoutDomain(),
      'main_address': mainWallet.value,
      'service_address': serviceWallet.value,
      'server_domain': node.value,
      'server_public_key': _keyBoxInfoRes?.nodePublickKey ?? '',
      'keybox_public_key': _keyBoxInfoRes?.keyboxPublicKey ?? '',
      'node_type': keyBoxType.value,
      'description': editControllerDesc.text
    };
    _submitDefaultNodeStaking(map);
  }

  //提交默认节点数据到服务器
  _submitDefaultNodeStaking(Map<String, dynamic> map) async {
    showLoadingDialog();
    var res = await Get.find<StakingApi>().getStakingAddResult(map);
    dismissLoadingDialog();

    if (res.data?.code == 200) {
      while (Get.currentRoute != Routes.NodeStakingListView) {
        Get.back();
      }
      Get.find<EventBus>().fire(UpdateStakingEvent());
    } else {
      toast(res.data?.msg ?? res.data?.message ?? '');
    }
  }

  //获取主钱包
  getMainWallet(BuildContext context) async {
    WalletManage().changeWallet(context, reqCallBack: (wallet) {
      if (wallet is WalletModel) {
        if (serviceWallet.value == wallet.address) {
          toast(L
              .the_main_wallet_and_service_wallet_cannot_be_the_same_wallet.tr);
          return;
        }
        mainWallet.value = wallet.address ?? '';
        AppLogger.d('getMainWallet wallet=${wallet.toJson()}');
      }
    }, isEth: true);
  }

  //获取服务钱包
  getServiceWallet(BuildContext context) async {
    WalletManage().changeWallet(context, reqCallBack: (wallet) {
      if (wallet is WalletModel) {
        if (mainWallet.value == wallet.address) {
          toast(L
              .the_main_wallet_and_service_wallet_cannot_be_the_same_wallet.tr);
          return;
        }
        serviceWallet.value = wallet.address ?? '';
        if (serviceType.value == 2) {
          getBalance();
        }
        key = '';
        var k = WalletManage().getWalletKeyByAddress(serviceWallet.value);
        if (k != null && !k.startsWith('0x')) {
          key = '0x$k';
        }
        AppLogger.d('getServiceWallet key=$key');
        AppLogger.d('getServiceWallet wallet=${wallet.toJson()}');
      }
    }, isEth: true);
  }

  //通过域名获取域名相关的信息
  getDomainInfo() async {
    if (node.value.isNotEmpty) {
      keyBoxType.value = 0;
      var res = await Get.find<StakingApi>().getDomainInfo(node.value);
      var data = res.data;
      if (data != null) {
        if (data.code == 200) {
          chainId.value = data.data?.chanid ?? '';
          var type = data.data?.nodeType;
          if (type != null) {
            useDefaultKeyBoxType = true;
            keyBoxType.value = type;
          }
          serviceType.value = chainId.value.isNotEmpty ? 2 : 1;
        } else {
          toast(data.msg ?? 'domain info error');
        }
        if (stakingNodeInfoData == null) {
          await getNodeTypeInfo();
        }
        var nods = stakingNodeInfoData?.nodes
            ?.firstWhereOrNull((e) => e.value == keyBoxType.value);
        if (nods?.number != null) {
          minStakingNum.value = nods!.number ?? 0;
        }
        AppLogger.d('getDomainInfo=${minStakingNum.value}');
      } else {
        toast('domain info error 2');
      }
    }
  }

  //查询可用余额
  getBalance() async {
    //'testnet.linksay.site'
    if (node.value.isNotEmpty && serviceWallet.value.isNotEmpty) {
      var balance = await WalletManage().getBalance(
           node.value, serviceWallet.value);
      AppLogger.d('getBalance balance=$balance');
      if (balance != null) {
        if (balance <= 0) {
          toast(L.insufficient_available_balance_please_switch_to_another_wallet
              .tr);
        }
        totalBalance.value = balance;
      } else {
        toast(L
            .there_is_an_error_in_obtaining_tokens_please_reselect_your_wallet
            .tr);
      }
    }
  }

  startTimer() {
    _timer?.cancel();
    _timer = Timer.periodic(const Duration(seconds: 2), (t) {
      getCreateValidatorState();
    });
  }

  //获取密钥初装状态
  getCreateValidatorState() async {
    var res = await Get.find<StakingApi>().getCreateValidatorState(node.value);
    if (res.data != null) {
      currentStatusValue.value = '${res.data?.status ?? ''}';
      AppLogger.d('currentStatusValue = ${currentStatusValue.value}');
      if (res.data?.code == 200 && res.data?.status == 7) {
        resetPinData();
        stopTimer();
        dismissLoadingDialog();
        Get.to(NodePledgeApplicationView());
      } else if (res.data?.code == 601) {
        stopTimer();
        dismissLoadingDialog();
        if (res.data?.status == 6) {
          toast(L.transaction_to_create_miner_failed.tr);
        }
      }
    }
  }

  stopTimer() {
    _timer?.cancel();
  }

  refresh12Data() {
    serviceWallet.value = '';
    currentStatusValue.value = '';
    minStakingNum.value = 0;
    minerName = '';
    totalBalance.value = 0;
    editControllerStakingValue.text = '';
    editControllerMinerName.text = '';
    resetPinData();
  }

  String getKeyBoxStatue() {
    String status = '';
    switch (currentStatusValue.value) {
      case '1':
      case '2':
        // status = L.loading_the_keystore.tr;
        // break;
      case '3':
        status = L.create_a_bls_account.tr;
        break;
      case '6':
        status = L.create_absenteeism.tr;
        break;
      case '7':
        status = L.start_the_blockchain_node.tr;
        break;
    }
    return status;
  }

  resetPinData() {
    editPinAgainController.text = '';
    editPinController.text = '';
  }
}
