import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_metatel/core/values/colors.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_web3/app/widgets/text_middle_overlow.dart';
import 'package:get/get.dart';

import '../../../../../core/languages/l.dart';
import '../../../../../r.dart';
import '../../../../widgets/app_bar_cus.dart';
import '../../../../widgets/divider_cus.dart';
import 'node_pledge_application_controller.dart';

class NodePledgeApplicationView_12 extends StatefulWidget {
  @override
  State<StatefulWidget> createState() {
    return _NodePledgeApplicationView_12();
  }
}

class _NodePledgeApplicationView_12
    extends State<NodePledgeApplicationView_12> {
  NodePledgeApplicationController controller =
  Get.find<NodePledgeApplicationController>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: AppColors.colorFFF2F2F2,
        appBar:
        AppBarCommon().build(context, leading: BackButton(onPressed: () {
          Get.back();
          controller.refresh12Data();
        }), title: L.node_initialization.tr),
        body: SingleChildScrollView(
          child: Container(
            padding: EdgeInsets.only(top: 16.r, left: 16.r, right: 16.r),
            child: Column(
              children: [
                Container(
                    width: double.infinity,
                    padding: EdgeInsets.all(16.r),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(16.r),
                      color: Colors.white,
                    ),
                    child: createWalletInfo(context)),
                SizedBox(
                  height: 20.r,
                ),
                ElevatedButton(
                  onPressed: () async {
                    controller.getKeyBoxInfo();

                  },
                  child: Text(
                    L.submit.tr,
                    style: TextStyle(fontSize: 16.sp),
                  ),
                ),
                SizedBox(
                  height: 30.r,
                ),
              ],
            ),
          ),
        ));
  }

  Widget createWalletInfo(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          L.wallet_info.tr,
          style: TextStyle(fontSize: 16.sp, color: Colors.black),
        ),
        SizedBox(
          height: 16.r,
        ),
        const DividerCus(),
        SizedBox(
          height: 16.r,
        ),
        Text(
          L.master_wallet.tr,
          style: TextStyle(fontSize: 12.sp, color: Colors.black),
        ),
        SizedBox(
          height: 10.r,
        ),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(10),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(5.r),
            color: AppColors.colorFFF2F2F2,
          ),
          child: Row(children: [
            SizedBox(
              width: 250.r,
              child: Obx(() => MiddleText(
                controller.mainWallet.value,
                MiddleTextOverflow.ellipsisMiddle,
              )),
            ),
            const Spacer(),
            Visibility(
              visible: controller.mainWalletCanChange.value &&
                  !controller.isSameMainWallet(),
              child: Row(
                children: [
                  Container(
                    height: 20.r,
                    width: 1,
                    color: AppColors.txtColor,
                  ),
                  const SizedBox(
                    width: 5,
                  ),
                  GestureDetector(
                    onTap: () {
                      controller.getMainWallet(context);
                    },
                    child: Container(
                      padding: EdgeInsets.all(5.r),
                      child: Obx(() => Image.asset(
                        controller.mainWallet.value.isNotEmpty
                            ? R.icResetWallet
                            : R.icAddWallet,
                        width: 16.r,
                        height: 16.r,
                      )),
                    ),
                  )
                ],
              ),
            ),
          ]),
        ),
        SizedBox(
          height: 16.r,
        ),
        Text(
          controller.serviceType.value == 1 ? L.service_wallet.tr : L.miner.tr,
          style: TextStyle(fontSize: 12.sp, color: Colors.black),
        ),
        SizedBox(
          height: 10.r,
        ),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(10),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(5.r),
            color: AppColors.colorFFF2F2F2,
          ),
          child: Row(children: [
            SizedBox(
              width: 250.r,
              child: Obx(() => MiddleText(
                controller.serviceWallet.value,
                MiddleTextOverflow.ellipsisMiddle,
              )),
            ),
            const Spacer(),
            Visibility(
                visible: controller.serviceWalletCanChange.value,
                child: Row(
                  children: [
                    Container(
                      height: 20.r,
                      width: 1,
                      color: AppColors.txtColor,
                    ),
                    const SizedBox(
                      width: 5,
                    ),
                    GestureDetector(
                      onTap: () {
                        controller.getServiceWallet(context);
                      },
                      child: Container(
                        padding: EdgeInsets.all(5.r),
                        child: Obx(() => Image.asset(
                          controller.serviceWallet.value.isNotEmpty
                              ? R.icResetWallet
                              : R.icAddWallet,
                          width: 16.r,
                          height: 16.r,
                        )),
                      ),
                    )
                  ],
                )),
          ]),
        ),
        Visibility(
            visible: controller.serviceType.value == 2,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(
                  height: 5,
                ),
                Obx(() => Text(
                  L.balance_wallet.tr.trParams(
                      {'num': '${controller.totalBalance.value}'}),
                  style: TextStyle(fontSize: 12.sp, color: Colors.black),
                )),
                const SizedBox(
                  height: 16,
                ),
                const DividerCus(),
                const SizedBox(
                  height: 16,
                ),
                Row(
                  children: [
                    Text(
                      L.pledge_quantity.tr,
                      style: TextStyle(fontSize: 12.sp, color: Colors.black),
                    ),
                    Obx(() {
                      return Text(
                        ' (${L.min_pledge_quantity.trParams({
                          'num': '${controller.minStakingNum.value}'
                        })})',
                        style: TextStyle(
                            fontSize: 12.sp, color: AppColors.colorFF808080),
                      );
                    }),
                  ],
                ),
                SizedBox(
                  height: 10.r,
                ),
                SizedBox(
                  height: 40.r,
                  child: TextField(
                    controller: controller.editControllerStakingValue,
                    readOnly: false,
                    onChanged: (text) =>
                        controller.onStakingValueTextChange(text),
                    textAlign: TextAlign.start,
                    textAlignVertical: TextAlignVertical.center,
                    keyboardType: TextInputType.number,
                    inputFormatters: [
                      FilteringTextInputFormatter.allow(RegExp(r'[0-9]'))
                      //设置只允许输入数字
                    ],
                    decoration: InputDecoration(
                      // 设置后，提升文本居中
                      filled: true,
                      contentPadding: EdgeInsets.all(10.r),
                      fillColor: AppColors.colorFFF2F2F2,
                      suffixStyle:
                      TextStyle(fontSize: 14.sp, color: Colors.black),
                      border: OutlineInputBorder(
                        borderSide: BorderSide.none,
                        borderRadius: BorderRadius.all(Radius.circular(5.r)),
                      ),
                    ),
                  ),
                ),
                SizedBox(
                  height: 10.r,
                ),
                Row(
                  children: [
                    Text(
                      L.miner_name.tr,
                      style: TextStyle(fontSize: 12.sp, color: Colors.black),
                    ),
                    Text(
                      ' (${L.miner_name_info.tr})',
                      style: TextStyle(
                          fontSize: 12.sp, color: AppColors.colorFF808080),
                    ),
                  ],
                ),
                SizedBox(
                  height: 10.r,
                ),
                SizedBox(
                  height: 40.r,
                  child: TextField(
                    controller: controller.editControllerMinerName,
                    readOnly: false,
                    onChanged: (text) => controller.onMinerNameTextChange(text),
                    textAlign: TextAlign.start,
                    textCapitalization : TextCapitalization.words,//大小写
                    textAlignVertical: TextAlignVertical.center,
                    inputFormatters: [
                      FilteringTextInputFormatter(RegExp("^[a-z0-9A-Z]+"),
                          allow: true), //只允许输入数字，字母
                    ],
                    decoration: InputDecoration(
                      // 设置后，提升文本居中
                      filled: true,
                      contentPadding: EdgeInsets.all(10.r),
                      fillColor: AppColors.colorFFF2F2F2,
                      suffixStyle:
                      TextStyle(fontSize: 14.sp, color: Colors.black),
                      border: OutlineInputBorder(
                        borderSide: BorderSide.none,
                        borderRadius: BorderRadius.all(Radius.circular(5.r)),
                      ),
                    ),
                  ),
                ),
                SizedBox(
                  height: 10.r,
                ),
                const DividerCus(),
                SizedBox(
                  height: 16.r,
                ),
                Row(
                  children: [
                    Text(
                      L.current_status.tr,
                      style: TextStyle(fontSize: 12.sp, color: Colors.black),
                    ),
                    Obx(() => Text(
                      controller.getKeyBoxStatue(),
                      style: TextStyle(
                          fontSize: 12.sp, color: AppColors.colorFF808080),
                    )),
                  ],
                ),
              ],
            )),
      ],
    );
  }
}
