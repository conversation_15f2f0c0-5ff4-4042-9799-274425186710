import 'package:flutter/material.dart';
import 'package:flutter_metatel/core/values/colors.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../../core/languages/l.dart';
import '../../../../../core/utils/util.dart';
import '../../../../../core/values/config.dart';
import '../../../../../r.dart';
import '../../../../data/models/res/staking_node_info_res.dart';
import '../../../../widgets/app_bar_cus.dart';
import '../../../../widgets/divider_cus.dart';
import 'node_pledge_application_controller.dart';

class NodePledgeApplicationView extends StatefulWidget {
  @override
  State<StatefulWidget> createState() {
    return _NodePledgeApplicationView();
  }
}

class _NodePledgeApplicationView extends State<NodePledgeApplicationView> {
  NodePledgeApplicationController controller =
  Get.find<NodePledgeApplicationController>();

  @override
  void initState() {
    super.initState();

  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar:
      AppBarCommon().build(context, title: L.node_pledge_application.tr),
      body: Stack(children: [SingleChildScrollView(
        child: Container(
          color: AppColors.white,
          padding: EdgeInsets.only(top: 16.r, left: 16.r, right: 16.r),
          child: Column(
            children: [
              Container(
                  width: double.infinity,
                  padding: EdgeInsets.all(16.r),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(16.r),
                    color: Colors.white,
                  ),
                  child: createWalletInfo()),
              SizedBox(
                height: 10.r,
              ),
              Container(
                width: double.infinity,
                padding: EdgeInsets.all(16.r),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(16.r),
                  color: Colors.white,
                ),
                child: createServiceInfo(context),
              ),
              SizedBox(
                height: 120.r,
              ),
            ],
          ),
        ),
      ),
        Positioned(
        left: 16.r,
        right: 16.r,
        bottom: 30.r,
        child:  ElevatedButton(
          onPressed: () async {
            controller.submitStakingInfo();
          },
          child: Text(
            L.submit_your_application.tr,
            style: TextStyle(fontSize: 16.sp),
          ),
        ),),
      ],),
    );
  }

  Widget createWalletInfo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          L.wallet_info.tr,
          style: TextStyle(fontSize: 16.sp, color: Colors.black),
        ),
        SizedBox(
          height: 16.r,
        ),
        const DividerCus(),
        SizedBox(
          height: 16.r,
        ),
        Text(
          L.master_wallet.tr,
          style: TextStyle(fontSize: 12.sp, color: Colors.black),
        ),
        SizedBox(
          height: 10.r,
        ),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(10),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(5.r),
            color: AppColors.colorFFF2F2F2,
          ),
          child: Obx(() => Text(controller.mainWallet.value)),
        ),
        SizedBox(
          height: 16.r,
        ),
        Text(
          L.service_wallet.tr,
          style: TextStyle(fontSize: 12.sp, color: Colors.black),
        ),
        SizedBox(
          height: 10.r,
        ),
        GestureDetector(
          onTap: () {},
          child: Container(
            width: double.infinity,
            padding: EdgeInsets.all(10),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(5.r),
              color: AppColors.colorFFF2F2F2,
            ),
            child: Obx(() => Text(controller.serviceWallet.value)),
          ),
        ),
      ],
    );
  }

  Widget createServiceInfo(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          L.service_info.tr,
          style: TextStyle(fontSize: 16.sp, color: Colors.black),
        ),
        SizedBox(
          height: 16.r,
        ),
        const DividerCus(),
        SizedBox(
          height: 10.r,
        ),
        Text(
          L.server_domain_name.tr,
          style: TextStyle(fontSize: 12.sp, color: Colors.black),
        ),
        SizedBox(
          height: 16.r,
        ),
        Obx(() => Text(controller.node.value)),
        SizedBox(
          height: 16.r,
        ),
        Text(
          L.service_type.tr,
          style: TextStyle(fontSize: 12.sp, color: Colors.black),
        ),
        SizedBox(
          height: 10.r,
        ),
        GestureDetector(
          onTap: controller.useDefaultKeyBoxType
              ? null
              : () {
            if (controller.stakingNodeInfoData?.availableNode?.isEmpty ?? true) {
              controller.getNodeTypeInfo();
            } else {
              showBottomDialogCommonWithCancel(context,
                  widgets: [_buildBoxTypeList()]);
            }
          },
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.all(10),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(5.r),
              color: AppColors.colorFFF2F2F2,
            ),
            child: Row(
              children: [
                Obx(() {
                  var index = controller.keyBoxType.value;
                  return Text(
                    getKeyBoxTypeStr(index),
                    style: TextStyle(fontSize: 12.sp, color: Colors.black),
                  );
                }),
                const Spacer(),
                Image.asset(
                  R.icServiceTypeMore,
                  width: 14.r,
                  height: 8.r,
                ),
                SizedBox(
                  width: 10.r,
                ),
              ],
            ),
          ),
        ),
        SizedBox(
          height: 16.r,
        ),
        Text(
          L.Application_description_1.tr,
          style: TextStyle(fontSize: 12.sp, color: Colors.black),
        ),
        SizedBox(
          height: 10.r,
        ),
        SizedBox(
          height: 120.r,
          child: TextField(
            controller: controller.editControllerDesc,
            // onChanged: (text) => controller.onTextChange(text, context),
            textAlign: TextAlign.start,
            textAlignVertical: TextAlignVertical.center,
            minLines: 5,
            maxLines: 5,
            decoration: InputDecoration(
              hintText: '',
              // 设置后，提升文本居中
              filled: true,
              contentPadding: EdgeInsets.all(10.r),
              fillColor: AppColors.colorFFF2F2F2,
              suffixStyle: TextStyle(fontSize: 14.sp, color: Colors.black),
              enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.all(Radius.circular(5.r)),
                  borderSide: const BorderSide(color: AppColors.colorFFF2F2F2)),
              focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.all(Radius.circular(5.r)),
                  borderSide: const BorderSide(color: AppColors.colorFF3474d1)),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildBoxTypeList() {
    if (controller.stakingNodeInfoData?.availableNode == null) {
      return Container();
    }
    var list = controller.stakingNodeInfoData!.availableNode!;
    return Container(
      width: double.infinity,
      constraints: BoxConstraints(maxHeight: (list.length * 55).r),
      child: ListView.separated(
        padding: EdgeInsets.only(top: 0, bottom: 0, left: 0.r, right: 0.r),
        controller: ScrollController(),
        itemBuilder: (context, index) {
          var node = list[index];
          return _createLanguageView(node, callBack: (node) {
            controller.setAvailableNodeIsChecked(node);
            Get.back();
          });
        },
        itemCount: list.length,
        separatorBuilder: (BuildContext context, int index) =>
            Divider(height: 0.5.h, color: AppColors.colorFFF8F8F8),
      ),
    );
  }

  Widget _createLanguageView(
      AvailableNode node, {
        WidgetItemClick? callBack,
      }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        borderRadius: null,
        onTap: () {
          callBack?.call(node);
        },
        child: Container(
          height: 55.r,
          padding: const EdgeInsets.only(
            left: 16,
            right: 16,
          ).r,
          child: Row(
            children: [
              Text(
                node.desc ?? '',
                style: TextStyle(
                  fontSize: 16.sp,
                  color: AppColors.colorFF040404,
                ),
              ),
              const Spacer(),
              Visibility(
                  visible: node.isSelecked ?? false,
                  child: Image.asset(
                    R.contactorChecked,
                    width: 20.r,
                    height: 20.r,
                  )),
            ],
          ),
        ),
      ),
    );
  }
}
