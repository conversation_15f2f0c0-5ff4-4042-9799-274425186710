import 'package:flutter/material.dart';
import 'package:flutter_metatel/core/values/colors.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../../core/languages/l.dart';
import '../../../../widgets/app_bar_cus.dart';
import '../../../../widgets/divider_cus.dart';
import 'node_pledge_application_controller.dart';
import 'node_pledge_application_view_1_2.dart';

class NodePledgeApplicationView_11
    extends GetView<NodePledgeApplicationController> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBarCommon()
            .build(context, elevation: 0, title: L.node_initialization.tr),
        body: Container(
          color: AppColors.white,
          padding: EdgeInsets.only(
            top: 36.r,
          ),
          child: Column(
            children: [
              Container(
                width: double.infinity,
                padding: EdgeInsets.all(2.5.r),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8.r),
                  color: Colors.white,
                ),
                child: createControView(),
              ),
              SizedBox(
                height: 10.r,
              ),
              Container(
                width: double.infinity,
                padding: EdgeInsets.all(16.r),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(16.r),
                  color: Colors.white,
                ),
                child: createServiceInfo(),
              ),
              SizedBox(
                height: 20.r,
              ),
              ElevatedButton(
                onPressed: () async {
                  // Get.to(NodePledgeApplicationView_12());
                  controller.getServiceList();
                },
                child: Text(
                  L.submit.tr,
                  style: TextStyle(fontSize: 16.sp),
                ),
              ),
              SizedBox(
                height: 30.r,
              ),
            ],
          ),
        ));
  }

  Widget createControView() {
    return Obx(() => Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Flexible(
              flex: 1,
              child: GestureDetector(
                  onTap: () {
                    controller.onChangeServiceType(1);
                  },
                  child: SizedBox(
                    height: 30.r,
                    child: Column(
                      children: [
                        Center(
                          child: Text(
                            L.regular_node.tr,
                            style: TextStyle(
                                fontSize: 16.sp,
                                color: controller.serviceType.value == 1
                                    ? AppColors.colorFF249ED9
                                    : AppColors.colorFF000000),
                          ),
                        ),
                        const Spacer(),
                        Divider(
                          height:
                              controller.serviceType.value == 1 ? 3.r : 3.r,
                          color: controller.serviceType.value == 1
                              ? AppColors.colorFF249ED9
                              : AppColors.colorFF000000,
                        )
                      ],
                    ),
                  )),
            ),
            Flexible(
              flex: 1,
              child: GestureDetector(
                onTap: () {
                  controller.onChangeServiceType(2);
                },
                child: SizedBox(
                  height: 30.r,
                  child: Column(
                    children: [
                      Center(
                        child: Text(
                          L.block_node.tr,
                          style: TextStyle(
                            fontSize: 16.sp,
                            color: controller.serviceType.value == 2
                                ? AppColors.colorFF249ED9
                                : AppColors.colorFF000000,
                          ),
                        ),
                      ),
                      const Spacer(),
                      Divider(
                        height: controller.serviceType.value == 2 ? 3.r : 3.r,
                        color: controller.serviceType.value == 2
                            ? AppColors.colorFF249ED9
                            : AppColors.colorFF000000,
                      )
                    ],
                  ),
                ),
              ),
            ),
          ],
        ));
  }

  Widget createServiceInfo() {
    return Obx(() {
      var title =
          controller.serviceType.value == 1 ? L.service_info.tr : L.node_rpc.tr;
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: TextStyle(fontSize: 17.sp, color: Colors.black),
          ),
          SizedBox(
            height: 20.r,
          ),
          SizedBox(
            height: 10.r,
          ),
          Text(
            L.server_domain_name.tr,
            style: TextStyle(fontSize: 16.sp, color: Colors.black),
          ),
          SizedBox(
            child: TextField(
              controller: controller.editControllerDomain,
              readOnly: false,
              onChanged: (text) => controller.onDomainTextChange(text),
              textAlign: TextAlign.start,
              textAlignVertical: TextAlignVertical.center,
              decoration: InputDecoration(
                hintText: 'xxx.yyy.zzz',
                // 设置后，提升文本居中
                contentPadding: EdgeInsets.only(left:10.r,bottom: 5,top: 0.5),
                hintStyle: TextStyle(
                  fontSize: 13.sp,
                  color: AppColors.hintTextColor,
                ),
                focusedBorder: const UnderlineInputBorder(
                  borderSide: BorderSide(color: AppColors.colorFF338dcc),
                ),
                enabledBorder: const UnderlineInputBorder(
                    borderSide: BorderSide(color: AppColors.colorFFCBCBCB)),
              ),
            ),
          ),
          SizedBox(
            height: 30.r,
          ),
          Visibility(
              visible: controller.serviceType.value == 2,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    L.chain_ID.tr,
                    style: TextStyle(fontSize: 16.sp,),
                  ),
                  SizedBox(
                    height: 10.r,
                  ),

                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(10),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(5.r),
                      color: AppColors.colorFFF2F2F2,
                    ),
                    child: Obx(() => Text(controller.chainId.value)),
                  ),
                ],
              )),
        ],
      );
    });
  }
}
