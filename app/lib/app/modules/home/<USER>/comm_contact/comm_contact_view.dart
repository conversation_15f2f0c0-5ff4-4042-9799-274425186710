import 'package:badges/badges.dart' as badges;
import 'package:extended_text/extended_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/data/models/own_info_model.dart';
import 'package:flutter_metatel/app/data/models/user_message_model.dart';
import 'package:flutter_metatel/app/data/providers/api/own.dart';
import 'package:flutter_metatel/app/modules/home/<USER>/group_contact/group_contact_view.dart';
import 'package:flutter_metatel/app/widgets/loading_view.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:flutter_metatel/core/utils/jump.dart';
import 'package:flutter_metatel/core/utils/util.dart';
import 'package:flutter_metatel/core/values/colors.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../../core/languages/l.dart';
import '../../../../../core/values/config.dart';
import '../../../../../r.dart';
import '../../../../data/enums/enum.dart';
import '../../../../data/models/v_contact_model.dart';
import '../../../../data/providers/db/database.dart';
import '../../../../widgets/at_widget/my_special_text_span_builder.dart';
import '../../../../widgets/azlist/az_listview.dart';
import '../../../../widgets/azlist/index_bar.dart';
import '../../../../widgets/divider_cus.dart';
import '../../../../widgets/mavatar_circle_avatar.dart';
import '../detail/contact_detail.dart';
import 'comm_contact_controller.dart';

GlobalKey<CommContactStatePage> mainContact = GlobalKey<CommContactStatePage>();

GlobalKey<CommContactStatePage> detailContact =
    GlobalKey<CommContactStatePage>();

GlobalKey<CommContactStatePage> forwardingContact =
    GlobalKey<CommContactStatePage>();

class CommContactView extends StatefulWidget {
  final CommentContactState commentContactState;
  final ValueChanged<VContactDate>? changedCallback;
  final List<ContactData>? totalContact;
  final int maxSelectCount;
  final bool isShowGroup;

  @override
  const CommContactView({
    Key? key,
    required this.commentContactState,
    this.totalContact,
    this.changedCallback,
    this.maxSelectCount = 0,
    this.isShowGroup = false,
  }) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return CommContactStatePage();
  }
}

class CommContactStatePage extends State<CommContactView> {
  CommContactController controller = Get.put(CommContactController());

  void loadData(List<ContactData> list) {
  List<ContactData> filteredList = list.toList();
  controller.loadData(filteredList, isShowTop: widget.isShowGroup);
    AppLogger.d('loadData ${filteredList.length}');
}



  void search(String? key) {
    controller.setSearch(key);
  }

  List<ContactData>? getSelectContact() {
    return controller.getSelectContacts();
  }

  @override
  void initState() {
    super.initState();
    if (widget.totalContact != null) {
    loadData(widget.totalContact!.toList());
  }
    controller.setParam(widget.commentContactState, widget.totalContact,
        widget.changedCallback, widget.maxSelectCount);
  }

@override
void didUpdateWidget(covariant CommContactView oldWidget) {
  super.didUpdateWidget(oldWidget);
  if (widget.totalContact != oldWidget.totalContact) {
    controller.setParam(
      widget.commentContactState,
      widget.totalContact,
      widget.changedCallback,
      widget.maxSelectCount,
    );
    if (widget.totalContact != null) {
      controller.loadData(widget.totalContact!);
    }
  }
}

  @override
  void dispose() {
    Get.delete<CommContactController>();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      body: Stack(
        children: [
          Container(
            padding: const EdgeInsets.only(),
            color: Colors.white,
            child: Obx(() {
            var vContact = controller.get();
            AppLogger.d('Filtered ContactData count: ${vContact.length}');
            AppLogger.d('vContact: ${vContact.map((e) => e.contactData?.username).toList()}');
              return controller.loading.value
                  ? const SizedBox.shrink()
                  : vContact.isEmpty
                      ? buildNoData()
                      : AzListView(
                          data: vContact,
                          itemCount: vContact.length,
                       itemBuilder: (BuildContext context, int index) {
                          var model = vContact[index];
                          AppLogger.d('Building list item for contact: ${model.contactData?.username}');
                          if (index == 0 && widget.isShowGroup)
                          return _buildHeader();
                          return _buildListItem(
                            widget.isShowGroup ? index - 1 : index);
                        },
                          physics: const BouncingScrollPhysics(),
                          indexBarData: Config.indexBarData,
                          // indexHintBuilder: (context, hint) {
                          //   return Container(
                          //     alignment: Alignment.center,
                          //     width: 60.0,
                          //     height: 60,
                          //     decoration: BoxDecoration(
                          //       color: Colors.grey[700]!.withAlpha(200),
                          //       shape: BoxShape.circle,
                          //     ),
                          //     child: Text(hint,
                          //         style:
                          //             const TextStyle(color: Colors.white, fontSize: 15.0)),
                          //   );
                          // },
                          indexBarAlignment: Alignment.topRight,
                          indexBarOptions: IndexBarOptions(
                            needRebuild: true,
                            color: Colors.transparent,
                            textStyle: TextStyle(
                              color: const Color(0xff666666),
                              fontSize: 10.sp,
                              wordSpacing: 15.w,
                            ),
                          ), 
                        );
            }),
          ),
          Obx(
            () => Visibility(
              visible: controller.loading.value,
              child: Container(
                width: double.infinity,
                height: double.infinity,
                alignment: Alignment.center,
                decoration: const BoxDecoration(
                  color: AppColors.transparent,
                ),
                child: const LoadingView(
                  color: AppColors.defaultMaskColor,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildListItem(int index) {
    List<VContactDate> contas = controller.get();
    VContactDate model = contas[index];
    String susTag = model.getSuspensionTag();
    bool showLine = true;
    if (index < contas.length - 1) {
      VContactDate nextModel = contas[index + 1];
      if (nextModel.tagIndex != model.tagIndex) {
        showLine = false;
      }
    }

    return Column(
      children: <Widget>[
        Offstage(
          offstage: model.isShowSuspension != true,
          child: _buildSusWidget(susTag),
        ),
        _buildDataWidget(model, showLine)
      ],
    );
  }

  void _onPressed(VContactDate data) {
    if (controller.isNotToDetail()) {
      controller.updateData(data);
    } else {
      if (data.contactData?.type == ContactType.fileHelper) {
        var userMessage = UserMessage(
            chatType: ChatType.singleChat.index,
            displayName: data.contactData?.displayname,
            userName: data.contactData?.username,
            isFriend: true,
            avatarPath: data.contactData?.avatarPath);

        JumpPage.messgae(userMessage);
      } else {
        Get.to(const ContactDetailView(), arguments: data.contactData);
      }
    }
  }

  Widget _buildDataWidget(VContactDate model, bool showLine) {
    var padding = EdgeInsets.only(left: 16.w, right: 16.w, top: 0, bottom: 0);
    var data = model.contactData;
    if (data == null) {
    AppLogger.e('ContactData is null for model: ${model.toString()}');
    return SizedBox.shrink();
  }
    AppLogger.d('Avatar Path: ${data.avatarPath}, Display Name: ${data.displayname}');

    double avatarDiameter = 40.w;
    String name = data?.localname ?? data!.displayname ?? "";
    if (name.isEmpty) {
      AppLogger.d('_buildDataWidget name.isEmpty');
      name = displayNameProcess(name, data?.username ?? "");
      data = data?.copyWith(
          localname: ofNullable(name), displayname: ofNullable(name));
    }
    name = getDisplayNameOffTid(name, isTid: data?.isTid, user: data?.username);

    Rx<Color> backColor = Colors.white.obs;
    return Obx(() => Material(
          color: backColor.value,
          child: InkWell(
            radius: 1,
            onTap: () {
              ItemClickFunction(
                startFunction: () {
                  backColor.value = AppColors.colorFFF2F2F2;
                },
                endFunction: () {
                  _onPressed(model);
                  backColor.value = AppColors.white;
                },
              );
            },
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  height: 49.h,
                  padding: padding,
                  child: Row(
                    children: [
                      Visibility(
                          visible: controller.isVisible(),
                          child: Container(
                            padding: EdgeInsets.only(right: 12.w),
                            width: 32.w,
                            height: 20.h,
                            child: Center(
                                child: Image.asset(
                              model.isChecked
                                  ? R.contactorChecked
                                  : R.contactorDefault,
                              width: 20.w,
                              height: 20.h,
                            )),
                          )),
                      // 头像
                      Padding(
                        padding: const EdgeInsets.only(bottom: 0).r,
                        child: MAvatarCircle(
                          diameter: avatarDiameter,
                          text: name,
                          textStyle: TextStyle(
                            fontSize: 20.sp,
                            color: Colors.white,
                            fontWeight: FontWeight.normal,
                          ),
                          imagePath:
                              model.contactData?.type == ContactType.fileHelper
                                  ? R.icFileHelper
                                  : (data != null ? appSupporAbsolutePath(data.avatarPath) : null),
                        ),
                      ),
                      // 间隔
                      SizedBox(width: 10.w),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisSize: MainAxisSize.max,
                          children: [
                            //const Spacer(),
                            // 名称
                            Expanded(
                              child: Row(
                                children: [
                                  badges.Badge(
                                    position: badges.BadgePosition.topEnd(),
                                    badgeStyle: badges.BadgeStyle(
                                      padding: const EdgeInsets.only(
                                              left: 5,
                                              top: 30,
                                              bottom: 0,
                                              right: 2)
                                          .r,
                                      badgeColor: AppColors.colorFFCD3A3A,
                                    ),
                                    badgeAnimation:
                                        const badges.BadgeAnimation.fade(
                                      animationDuration: Duration(seconds: 1),
                                      toAnimate: true,
                                    ),
                                    badgeContent: const Text(""),
                                    showBadge: !(data?.read ?? true),
                                    child: Container(
                                      alignment: Alignment.centerLeft,
                                      child: ConstrainedBox(
                                        constraints:
                                            BoxConstraints(maxWidth: 240.w),
                                        child: ExtendedText(
                                          name,
                                          overflow: TextOverflow.ellipsis,
                                          style: TextStyle(
                                              fontSize: 16.sp,
                                              color: Colors.black),
                                          softWrap: true,
                                          specialTextSpanBuilder:
                                              MySpecialTextSpanBuilder(
                                                  showAtBackground: false,
                                                  size: Size(12.r, 12.r)),
                                          maxLines: 1,
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),

                            const Visibility(
                              visible: true,
                              child: DividerCus(),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ));
  }

  Widget _buildSusWidget(String susTag) {
    return Container(
      color: AppColors.backgroundGray,
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      height: 25.h,
      width: double.infinity,
      alignment: Alignment.centerLeft,
      child: Row(
        children: <Widget>[
          Text(
            susTag,
            textScaleFactor: 1.3,
            style: TextStyle(fontSize: 10.sp, color: const Color(0xFF666666)),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    double avatarDiameter = 40.w;
    return Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
      _buildSusWidget('※'),
      // 我的群聊
      _buildHeaderItem(
        L.main_my_channel.tr,
        avatar: Image.asset(
          R.groupDefaultAvatar,
          width: avatarDiameter,
          height: avatarDiameter,
        ),
        onTap: () {
          Get.to(() => GroupContactView());
        },
      ),
      // 我的私密群
      _buildHeaderItem(
        L.main_my_group.tr,
        avatar: Image.asset(
          R.privateGroupDefaultAvatar,
          width: avatarDiameter,
          height: avatarDiameter,
        ),
        onTap: () {
          Get.to(() => GroupContactView(isGroup: true));
        },
      ),
      // _buildSusWidget('official_account'.tr),
      Divider(
        height: 0.5.h,
        thickness: 1,
        indent: 20.w,
        endIndent: 20.w,
        color: AppColors.colorFFE5E5E5,
      ),
      // Linksay Official
      _buildHeaderItem(
        getOfficeDisplayName(),
        avatar: Image.asset(
          R.officialImg,
          width: avatarDiameter + 2,
          height: avatarDiameter + 2,
        ),
        onTap: () {
          var userMessage = UserMessage(
            chatType: ChatType.officialChat.index,
            displayName: getOfficeDisplayName(),
            userName: Config.officialAccount,
            isFriend: true,
            avatarPath: null,
          );

          JumpPage.messgae(userMessage);
        },
      ),
      // Linksay AI
      _buildHeaderItem(
        'Linksay-ai',
        avatar: FutureBuilder<List<OwnInfoModelData>?>(
          future: getOtherInfo([Config.meetingRobot]),
          builder: ((context, snapshot) {
            if (snapshot.hasData) {
              return buildChatAvatarWithAttr(
                ChatType.singleChat.index,
                Config.meetingRobot,
                diameter: avatarDiameter,
                imagePath: snapshot.data?.first.avatar,
                isNet: true,
              );
            }
            return SizedBox.shrink();
          }),
        ),
        onTap: () {
          var userMessage = UserMessage(
            chatType: ChatType.singleChat.index,
            displayName: 'Linksay-ai',
            userName: Config.meetingRobot,
            isFriend: true,
            avatarPath: null,
          );

          JumpPage.messgae(userMessage);
        },
      ),
      FutureBuilder<ContactData?>(
        future: getFileHelperContact(),
        builder: ((context, snapshot) {
          if (snapshot.hasData) {
            return _buildHeaderItem(
              L.metatel_file_assistant.tr,
              avatar: buildChatAvatarWithAttr(
                ChatType.singleChat.index,
                snapshot.data!.username,
                diameter: avatarDiameter,
                imagePath: snapshot.data!.avatarPath,
                isNet: false,
              ),
              onTap: () {
                var userMessage = UserMessage(
                  chatType: ChatType.singleChat.index,
                  displayName: L.metatel_file_assistant.tr,
                  userName: snapshot.data!.username,
                  isFriend: true,
                  avatarPath: snapshot.data!.avatarPath,
                );

                JumpPage.messgae(userMessage);
              },
            );
          }
          return SizedBox.shrink();
        }),
      ),
      // 电脑端

      SizedBox(height: 10.h),
    ]);
  }

  _buildHeaderItem(String title, {Function()? onTap, Widget? avatar}) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 10.h),
        child: Row(
          children: [
            avatar ?? SizedBox.shrink(),
            SizedBox(width: 10.w),
            ExtendedText(
              title,
              overflow: TextOverflow.ellipsis,
              style: TextStyle(
                color: Colors.black,
                fontSize: 16.sp,
              ),
              specialTextSpanBuilder: MySpecialTextSpanBuilder(
                  showAtBackground: false, size: Size(15.r, 15.r)),
            ),
          ],
        ),
      ),
    );
  }
}
