import 'package:async_task/async_task_extension.dart';
import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/data/services/config_service.dart';
import 'package:flutter_metatel/app/widgets/azlist/az_config.dart';
import 'package:flutter_metatel/core/languages/l.dart';
// ignore: unused_import
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:flutter_metatel/core/utils/util.dart';
import 'package:get/get.dart';
import 'package:lpinyin/lpinyin.dart';
import 'package:worker_manager/worker_manager.dart';

import '../../../../../core/values/config.dart';
import '../../../../data/enums/enum.dart';
import '../../../../data/models/v_contact_model.dart';
import '../../../../data/providers/db/database.dart';
import '../../../../widgets/azlist/az_common.dart';

class CommContactController extends GetxController {
  final RxList<VContactDate> _contacts = <VContactDate>[].obs;
  CommentContactState _commentContactState = CommentContactState.NONE;
  ValueChanged<VContactDate>? _changedCallback;
  VContactDate? currentVContactDate;
  String? key;
  Set<VContactDate> selectedContacts = {};
  final List<VContactDate> _currentContact = [];
  int _maxSelectCount = 0;

  RxList<VContactDate> get() => _contacts;

  List<ContactData>? datasWidget;
  var loading = true.obs;

  List<ContactData>? getSelectContacts() {
    if (selectedContacts.isEmpty) {
      return null;
    }
    List<ContactData> contacts = <ContactData>[];
    for (final VContactDate item in selectedContacts) {
      contacts.add(item.contactData!);
    }
    return contacts;
  }

  @override
  void onReady() {
    if (datasWidget?.isNotEmpty ?? false) {
      Future.delayed(const Duration(milliseconds: 200), () {
        loadData(datasWidget, isShowTop: false);
      });
    } else {
      loading.value = false;
    }
  }

  void setParam(CommentContactState state, List<ContactData>? datas,
      ValueChanged<VContactDate>? callback, int maxSelectCount) {
    _commentContactState = state;
    _changedCallback = callback;
    _maxSelectCount = maxSelectCount;
    datasWidget = datas;
  }

  bool isVisible() {
    return _commentContactState != CommentContactState.NONE &&
        _commentContactState != CommentContactState.BACK;
  }

  bool isNotToDetail() {
    return _commentContactState != CommentContactState.NONE;
  }

  void setSearch(String? key) {
    this.key = key;
    searchContact();
  }

  void searchContact() async {
    _contacts.value = _currentContact.where((e) {
      String name = e.contactData?.state == ContactState.friend.index
          ? e.contactData?.localname ?? e.contactData?.displayname ?? ''
          : e.contactData?.displayname ?? '';

      return name.contains(key ?? '');
    }).toList();
  }

  void loadData(List<ContactData>? datas, {bool isShowTop = true}) async {
    if (datas == null || datas.isEmpty) {
      return;
    }

    /// 有全体at，默认在第一个
    ContactData atData = datas[0];
    if (Config.atAll == atData.username) {
      datas.removeAt(0);
    }

    loading.value = true;
    _currentContact.clear();

    /// 添加※处理分类
    if (isShowTop) {
      _currentContact.add(VContactDate(contactData: null, tagIndex: '※'));
    }
    for (var item in datas) {
      _currentContact.add(VContactDate(contactData: item));
      _currentContact.add(VContactDate(contactData: null, tagIndex: '#'));
    }
    var list = await _handleList(_currentContact);

    /// 保留排序好的数据
    _currentContact.clear();
    if (Config.atAll == atData.username) {
      var atRes =
          await _handleList([VContactDate(contactData: atData)], sort: false);
      _currentContact.addAll(atRes);
    }
    _currentContact.addAll(list);

    _contacts.value = List.from(_currentContact);
    loading.value = false;
  }

  void updateData(VContactDate date) {
    if (!date.isChecked &&
        _maxSelectCount > 0 &&
        selectedContacts.length >= _maxSelectCount) {
      toast(L.exceeded_selected.trParams({'num': '$_maxSelectCount'}),
          textColor: Colors.red);
      return;
    }

    if (_commentContactState == CommentContactState.BACK) {
    } else if (_commentContactState == CommentContactState.MULTIPLE) {
      if (_contacts.contains(date)) {
        date.isChecked = !date.isChecked;
        _contacts[_contacts.indexOf(date)] = date;
      }
    } else {
      if (currentVContactDate != null &&
          !identical(currentVContactDate, date)) {
        if (_contacts.contains(currentVContactDate)) {
          currentVContactDate!.isChecked = false;
          _contacts[_contacts.indexOf(currentVContactDate)] =
              currentVContactDate!;
          currentVContactDate = null;
        }
      }
      if (_contacts.contains(date)) {
        date.isChecked = !date.isChecked;
        _contacts[_contacts.indexOf(date)] = date;
        if (date.isChecked) {
          currentVContactDate = date;
        }
      }
    }

    if (date.isChecked) {
      selectedContacts.add(date);
    } else {
      selectedContacts.remove(date);
    }
    _changedCallback?.call(date);
  }

  Future<List<VContactDate>> _handleList(List<VContactDate> list,
      {bool sort = true}) async {
    if (list.isEmpty) return [];

    var owner = await Get.find<AppConfigService>().getUserName();
    list.removeWhere((e) => e.contactData?.username == owner);
    for (int i = 0; i < list.length; i++) {
      String name = list[i].contactData?.localname ??
          list[i].contactData?.displayname ??
          '';
      if (name.isEmpty) {
        name = displayNameProcess(name, list[i].contactData?.username ?? "");
        list[i].contactData = list[i].contactData?.copyWith(
            displayname: ofNullable(name), localname: ofNullable(name));
      }
      String pinyin = '';
      if (name.isNotEmpty) {
        pinyin = AzConfig.getDefaultTag(name.substring(0, 1)) ?? '';
      }
      if (pinyin.isEmpty) {
        pinyin = PinyinHelper.getPinyinE(name);
      }
      if (list[i].contactData?.username == Config.atAll ||
          list[i].contactData?.username == owner) {
        pinyin = 'A';
        list[i].tagIndex = 'A';
      } else {
        String tag = '#';
        if (pinyin.isNotEmpty) {
          tag = pinyin.substring(0, 1).toUpperCase();
        }

        if (RegExp("[A-Z]").hasMatch(tag)) {
          list[i].tagIndex = tag;
        } else {
          list[i].tagIndex = "#";
        }
      }
      list[i].pinyin = pinyin;
    }

    if (sort) {
      // A-Z sort.
      try {
        list = await Executor()
            .execute(
                arg1: list, fun1: SuspensionUtil.sortListBySuspensionTagWork)
            .then((value) => value, onError: (e) => {throw e});
        // list= SuspensionUtil.sortListBySuspensionTag(list);
        // show sus tag.
        SuspensionUtil.setShowSuspensionStatus(list);
      } catch (e) {
        e.printError();
      }
    }
    return list;
  }
}
