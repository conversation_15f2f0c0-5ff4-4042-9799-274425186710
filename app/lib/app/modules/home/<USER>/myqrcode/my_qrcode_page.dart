/*
 * @Author: <PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2022-05-06 16:40:04
 * @LastEditors: luoyuan <EMAIL>
 * @LastEditTime: 2022-05-06 17:17:27
 * @FilePath: \flutter_metatel\lib\app\modules\home\settings\qrcode\my_qrcode_page.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AEqt
 */

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_metatel/app/modules/home/<USER>/myqrcode/my_qr_code_controller.dart';
import 'package:flutter_metatel/app/widgets/app_bar_cus.dart';
import 'package:flutter_metatel/core/languages/l.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:qr_flutter/qr_flutter.dart';

import '../../../../../core/utils/app_log.dart';
import '../../../../../core/utils/save_image_to_photo.dart';
import '../../../../../core/utils/util.dart';
import '../../../../../core/values/colors.dart';
import '../../../../../r.dart';
import '../../../../widgets/mavatar_circle_avatar.dart';

class MyQrcodePage extends GetView<MyQrcodeController> {
  MyQrcodePage({Key? key}) : super(key: key);
  final TextEditingController _textEditControllerFirstName =
      TextEditingController();
  final TextEditingController _textAddressController = TextEditingController();
  @override
  final MyQrcodeController controller =
      Get.put(MyQrcodeController(), permanent: false);

  GlobalKey? _qrKey;

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      _qrKey = GlobalKey();
      String userName = controller.userName.value.split('@').first;
      _textEditControllerFirstName.value = TextEditingValue(
          text: controller.userLocalName.value,
          selection: TextSelection.fromPosition(TextPosition(
              affinity: TextAffinity.downstream,
              offset: (controller.userLocalName.value).length)));
      return controller.isEdit.value
          ? WillPopScope(
              child: _buildScaffold(context, userName),
              onWillPop: () async {
                String editDisplayName =
                    _textEditControllerFirstName.text.trim();
                if (editDisplayName == controller.userLocalName.value) {
                  return true;
                } else {
                  showDialog(
                    barrierDismissible: false,
                    context: context,
                    useSafeArea: false,
                    builder: (_) {
                      return AlertDialog(
                        content: Text(L
                            .nickname_has_been_modified_but_not_saved_OK_to_exit
                            .tr),
                        actions: <Widget>[
                          TextButton(
                            child: Text(L.chat_contact_cancel.tr),
                            onPressed: () => Get.back(result: false), //关闭对话框
                          ),
                          TextButton(
                            child: Text(L.confirm.tr),
                            onPressed: () => Get.back(result: true), //关闭对话框,
                          ),
                        ],
                      );
                    },
                  ).then((value) {
                    if (value) {
                      Get.back();
                    }
                  });
                  return false;
                }
              },
            )
          : _buildScaffold(context, userName);
    });
  }

  Widget _buildScaffold(BuildContext context, String userName) {
    _textAddressController.text == userName;
    return Scaffold(
        resizeToAvoidBottomInset: false,
        appBar: AppBarCommon().build(context, actions: [
          Visibility(
              visible: controller.isEdit.value,
              child: Padding(
                padding: const EdgeInsets.only(right: 20, top: 20),
                child: GestureDetector(
                    onTap: () {
                      String firstName =
                          _textEditControllerFirstName.text.trim();
                      // String lastName = _textEditControllerLastName.text.trim();
                      // String localName = currentLanguageIsSimpleChinese()
                      //     ? (lastName + firstName)
                      //     : firstName + lastName;
                      String displayName = firstName;
                      if (displayName.isEmpty) {
                        toast(
                            L.chat_info_nickname_not_allowed_empty_or_blank.tr);
                      } else {
                        controller.updateMyself(displayName, firstName, "");
                        controller.isEdit.value = false;
                      }
                    },
                    child: Text(
                      L.save.tr,
                      style: TextStyle(
                          fontSize: 14,
                          color: Theme.of(context).colorScheme.primary),
                    )),
              ))
        ]),
        body: Container(
            padding: const EdgeInsets.only(top: 5).r,
            color: Theme.of(context).primaryColor,
            child: Center(
              heightFactor: 1,
              child: ListView(
                children: [
                  RepaintBoundary(
                    key: _qrKey,
                    child: Container(
                      color: Theme.of(context).primaryColor,
                      child: Column(
                        children: [
                          GestureDetector(
                            onTap: () {
                              if (controller.userIconPath.value.isEmpty) {
                                return;
                              }
                              showBottomDialogCommonWithCancel(context,
                                  widgets: controller
                                      .buildSetHeadBottomSheetItemWidgets(
                                          context));
                            },
                            child: Padding(
                              padding: const EdgeInsets.only(top: 30).r,
                              child: Center(
                                  heightFactor: 1,
                                  child: Obx(() {
                                    return Stack(
                                      children: [
                                        MAvatarCircle(
                                            diameter: 70,
                                            text:
                                                controller.userLocalName.value,
                                            imagePath:
                                                controller.userIconPath.value),
                                        /*  Positioned(
                                      left: 50.w,
                                      top: 35.h,
                                      bottom: 0,
                                      right: 0,
                                      child: Image.asset(
                                        R.icoEditHead,
                                      ),
                                    )*/
                                      ],
                                    );
                                  })),
                            ),
                          ),
                          SizedBox(
                            height: 15.h,
                          ),
                          Padding(
                            padding: const EdgeInsets.only(
                              left: 15,
                              right: 15,
                            ).r,
                            child: GestureDetector(
                                onTap: () {
                                  controller.isEdit.value =
                                      !controller.isEdit.value;
                                  AppLogger.d("nickName change!!!");
                                },
                                child: Stack(
                                    alignment: AlignmentDirectional.centerEnd,
                                    children: [
                                      TextField(
                                        autofocus: true,
                                        textAlign: controller.isEdit.value
                                            ? TextAlign.start
                                            : TextAlign.center,
                                        readOnly: controller.isEdit.value
                                            ? false
                                            : true,
                                        controller:
                                            _textEditControllerFirstName,
                                        decoration: const InputDecoration(
                                          counterText: '',
                                          border: OutlineInputBorder(
                                            borderSide: BorderSide.none,
                                          ),
                                        ),
                                        style: TextStyle(
                                          fontSize: 20.sp,
                                          color: AppColors.colorFF333333,
                                          fontWeight: FontWeight.bold,
                                        ),
                                        maxLength: 20,
                                      ),
                                      // 编辑按钮
                                      Padding(
                                        padding: EdgeInsets.only(left: 5.w),
                                        child: Image.asset(
                                          R.icoEditHead,
                                          width: 25.w,
                                          height: 25.h,
                                        ),
                                      ),
                                    ])),
                          ),
                          // Container(
                          //   constraints: BoxConstraints(
                          //     maxWidth: 200.w,
                          //   ),
                          //   child: Text(
                          //     controller?.userLocalName.value??"",
                          //     maxLines: 1,
                          //     overflow: TextOverflow.ellipsis,
                          //     style: const TextStyle(fontSize: 16),
                          //   ),
                          // ),
                          SizedBox(
                            height: 20.h,
                          ),
                          Center(
                            child: Text(
                              L.scaner_hint.tr,
                              style: const TextStyle(
                                  fontSize: 14, color: Colors.grey),
                            ),
                          ),
                          SizedBox(
                            height: 20.h,
                          ),
                          Center(
                            child: QrImageView(
                              size: 180.r,
                              data: controller.qrcode.value,
                              gapless: false,
                              errorStateBuilder: (context, error) {
                                return Container(
                                  color: Colors.red,
                                );
                              },
                            ),
                          ),
                          // FutureBuilder(
                          //   future: _qrCodeData(),
                          //   builder: (context, snapshot) {
                          //     if (snapshot.connectionState == ConnectionState.done) {
                          //       return QrImage(
                          //         size: 180.r,
                          //         data: snapshot.data as String,
                          //         gapless: false,
                          //       );
                          //     } else {
                          //       return Container(
                          //         color: Colors.red,
                          //       );
                          //     }
                          //   },
                          // ),
                          SizedBox(
                            height: 20.r,
                          ),
                          Container(
                              margin: const EdgeInsets.all(15).r,
                              padding: const EdgeInsets.only(left: 15,right: 15,bottom: 15).r,
                              decoration: BoxDecoration(
                                color: AppColors.backgroundGray,
                                borderRadius: BorderRadius.all(
                                        const Radius.circular(12).r)
                                    .r,
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    children: [
                                      Text(
                                        L.address.tr,
                                        style: const TextStyle(
                                            fontWeight: FontWeight.bold),
                                      ),
                                      const Spacer(),
                                      IconButton(
                                        iconSize: 20.r,
                                          onPressed: () async {
                                            await Clipboard.setData(
                                                ClipboardData(text: userName));
                                            toast(
                                              L.chat_has_copy_to_shear_plate.tr,
                                            );
                                          },
                                          icon: Image.asset(
                                            R.icoCopy,
                                            width: 10.r,
                                            height: 10.r,
                                          )),
                                    ],
                                  ),

                                  SelectableText(userName),
                                ],
                              ) /*WXText(
                        L.qr_code_number.tr + userName,
                        WXTextOverflow.ellipsisMiddle,
                      ),*/
                              ),
                        ],
                      ),
                    ),
                  ),
                  SizedBox(
                    height: 20.h,
                  ),
                  Padding(
                    padding: const EdgeInsets.only(left: 15, right: 15).r,
                    child: ElevatedButton(
                      onPressed: () async {
                        _saveQr();
                      },
                      child: Text(
                        L.save.tr,
                      ),
                    ),
                  ),
                  SizedBox(
                    height: 20.h,
                  ),
                ],
              ),
            )));
  }

  void _saveQr() async {
    var imageBytes = await RepaintBoundaryUtils.savePhotoByGlobalKey(_qrKey!);
    RepaintBoundaryUtils.savePhoto(imageBytes,
        toastSuccess: L.save_success.tr, toastError: L.save_failed.tr);
  }
}
