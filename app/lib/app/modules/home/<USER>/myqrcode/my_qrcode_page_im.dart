/*
 * @Author: <PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2022-05-06 16:40:04
 * @LastEditors: luoyuan <EMAIL>
 * @LastEditTime: 2022-05-06 17:17:27
 * @FilePath: \flutter_metatel\lib\app\modules\home\settings\qrcode\my_qrcode_page.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AEqt
 */

import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_metatel/app/data/services/config_service.dart';
import 'package:flutter_metatel/app/modules/home/<USER>/mine_controller.dart';
import 'package:flutter_metatel/app/widgets/text_middle_overlow.dart';
import 'package:flutter_metatel/core/languages/l.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:qr_flutter/qr_flutter.dart';

import '../../../../../core/utils/save_image_to_photo.dart';
import '../../../../../core/utils/util.dart';
import '../../../../../core/values/colors.dart';
import '../../../../../r.dart';
import '../../../../widgets/app_bar_cus.dart';
import '../../../../widgets/mavatar_circle_avatar.dart';

class MyQrcodePageIm extends StatelessWidget {
  MyQrcodePageIm({Key? key, required this.controller}) : super(key: key);
  final MineController controller;
  final GlobalKey _qrKey = GlobalKey();

  Future<String> _qrCodeData() async {
    AppConfigService conf = Get.find();

    String? userName = conf.getUserName();
    controller.userName.value = userName ?? "";
    Map<String, dynamic> mapJson = {};
    mapJson['firstName'] = '';
    mapJson['lastName'] = '';
    mapJson['nick'] = conf.getMySelfDisplayName();
    mapJson['number'] = userName ?? '';
    mapJson['type'] = '4';

    String qrcode = json.encode(mapJson);
    return qrcode;
  }

  @override
  Widget build(BuildContext context) {
    final _bgHeight = 820.r;
    final _containerHeight = 631.r;
    final _containerTopSpacing = 85.r;
    final _avatarOuterSize = 130.r;
    final _avatarInnerSize = 116.r;
    return Obx(() {
      String userName = controller.userName.value.split('@').first;
      return Scaffold(
        // backgroundColor: AppColors.backgroundGray,
        appBar: AppBarCommon().build(context,
            title: L.my_qrcode.tr, backgroundColor: AppColors.white),
        body: SingleChildScrollView(
          physics: const ClampingScrollPhysics(),
          child: Container(
            height: _bgHeight,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  AppColors.primaryBgColor2,
                  AppColors.primaryBgColor1,                            
                ],
                begin: Alignment(-1,-1),
                end: Alignment(1,1),      
              ),
            ),
            child: Stack(
              children: [
                RepaintBoundary(
                  key: _qrKey,
                  child: Stack(
                    children: [
                      /// Content的部分
                      Padding(
                        padding: EdgeInsets.only(left: 16.r, right: 16.r, top: _containerTopSpacing),
                        child: Container(
                          width: double.infinity, 
                          height: _containerHeight,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(62).r,
                            color: Colors.white,
                          ),
                          padding: const EdgeInsets.only(left: 8, right: 8).r,
                          child: Column(
                            children: [
                              SizedBox(
                                height: _containerHeight*0.8,
                                child: Column(
                                  children: [
                                    SizedBox(height: (_avatarOuterSize/2) + 13.r),
                                    SizedBox(
                                      width: 0.7.sw,
                                      child: Center(
                                        child: Text(
                                          controller.userLocalName.value,
                                          maxLines: 1,
                                          overflow: TextOverflow.ellipsis,
                                          style: TextStyle(
                                              fontSize: 22.sp,
                                              fontWeight: FontWeight.bold),
                                        ),
                                      ),
                                    ),
                                    SizedBox(
                                      height: 27.r,
                                    ),
                                    Text(
                                      L.scaner_hint.tr,
                                      textAlign: TextAlign.center,
                                      style: const TextStyle(
                                          fontSize: 14, color: Colors.grey),
                                    ),
                                    SizedBox(
                                      height: 27.r,
                                    ),
                                    FutureBuilder(
                                      future: _qrCodeData(),
                                      builder: (context, snapshot) {
                                        if (snapshot.connectionState ==
                                            ConnectionState.done) {
                                          return QrImageView(
                                            size: 195.r,
                                            data: snapshot.data as String,
                                            gapless: false,
                                          );
                                        } else {
                                          return Container(
                                            color: Colors.red,
                                          );
                                        }
                                      },
                                    ),
                                    // const Spacer(
                                    //   flex: 1,
                                    // ),
                                    SizedBox(height: 18.r),
                                    Padding(
                                      padding:
                                          const EdgeInsets.only(left: 16, right: 16).r,
                                      child: Column(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          Text(
                                            L.address.tr,
                                            style: TextStyle(
                                              color: Colors.black,
                                              fontSize: 13.sp,
                                              fontWeight: FontWeight.w500,
                                            ),
                                          ),
                                          SizedBox(height: 3.r),
                                          MiddleText(
                                            userName,
                                            WXTextOverflow.ellipsisMiddle,
                                            style: TextStyle(
                                              color: AppColors.colorFF656565,
                                              fontSize: 12.sp,
                                              fontWeight: FontWeight.w300,
                                            ),
                                          ),
                                          SizedBox(height: 10.r),
                                          GestureDetector(
                                            onTap: () async {
                                              await Clipboard.setData(
                                                  ClipboardData(text: userName));
                                              toast(
                                                L.chat_has_copy_to_shear_plate.tr,
                                              );
                                            },
                                            child: Row(
                                              mainAxisAlignment: MainAxisAlignment.center,
                                              children: [
                                                Image.asset(R.iconCopyNewFill,
                                                    height: 12.5.r, width: 12.5.r),
                                                SizedBox(
                                                  width: 5.r,
                                                ),
                                                Text(
                                                  L.copy_address.tr,
                                                  style: TextStyle(
                                                      fontSize: 14.sp, color: AppColors.appDefault),
                                                )
                                              ],
                                            ),
                                          ),                                   
                                        ],
                                      ),
                                    ),
                                    // const Spacer(
                                    //   flex: 1,
                                    // ),
                                  ],
                                ),
                              ),
                              /// 预留按钮位置
                              SizedBox(
                                height: _containerHeight*0.2,                              
                              ),
                            ],
                          ),
                        ),
                      ),
                      /// 头像部分
                      Padding(
                        padding: const EdgeInsets.only(top: 25).r,
                        child: Center(
                          heightFactor: 1,
                          child: Container(
                            width: _avatarOuterSize,
                            height: _avatarOuterSize,
                            padding: const EdgeInsets.all(7).r,
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius:
                                  BorderRadius.all(Radius.circular(_avatarOuterSize/2)),
                            ),
                            child: Obx(
                              () {
                                return MAvatarCircle(
                                  diameter: _avatarInnerSize,
                                  text: controller.userLocalName.value,
                                  imagePath: controller.userIconPath.value,
                                );
                              },
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                /// 按钮部分
                SizedBox(
                  height: _containerTopSpacing + _containerHeight,
                  child: Column(
                    children: [
                      const Spacer(),
                      SizedBox(
                        height: _containerHeight*0.2,
                        child: Center(
                          child: Padding(
                            padding: EdgeInsets.symmetric(horizontal: 70.r),
                            child: ElevatedButton(
                              onPressed: () async {
                                _saveQr();
                              },
                              child: Text(
                                L.save.tr,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    });
  }

  void _saveQr() async {
    var imageBytes = await RepaintBoundaryUtils.savePhotoByGlobalKey(_qrKey);
    RepaintBoundaryUtils.savePhoto(imageBytes,
        toastSuccess: L.save_success.tr, toastError: L.save_failed.tr);
  }
}
