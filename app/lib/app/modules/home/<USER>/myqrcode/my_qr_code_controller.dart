import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../../core/languages/l.dart';
import '../../../../../core/task/avatar_task.dart';
import '../../../../../core/utils/events_bus.dart';
import '../../../../../core/utils/util.dart';
import '../../../../data/events/events.dart';
import '../../../../data/models/avatar_model.dart';
import '../../../../data/providers/api/own.dart';
import '../../../../data/services/config_service.dart';
import '../../../../widgets/chat_widget/photos_video_view.dart';

class MyQrcodeController extends GetxController {
  var isEdit = false.obs;
  var userName = "".obs;
  var userIconPath = "".obs;
  var userLocalName = "".obs;
  var qrcode = "".obs;

  @override
  void onInit() async {
    super.onInit();

    AppConfigService conf = Get.find<AppConfigService>();
    userName.value = await conf.getUserName() ?? "";
    String mySelfDisplayName = conf.getMySelfDisplayName().isEmpty
        ? conf.getPhone()
        : conf.getMySelfDisplayName();
    AvatarModel avatar = AvatarModel.fromJson(conf.getMySelfAvatarInfo() ?? {});
    userLocalName.value = mySelfDisplayName;
    userIconPath.value = appSupporAbsolutePath(avatar.path) ?? "";
    _mySelfCodeDataUpdate();
  }

  List<Widget> buildSetHeadBottomSheetItemWidgets(BuildContext context) {
    List<Widget> widgets = [
      /*getBottomSheetItemSimple(context, L.capture_picture.tr,
        itemCallBack: () => getImageTakePhoto()),*/
      /*getBottomSheetItemSimple(context, L.get_picture_from_phone.tr,
          itemCallBack: () => chooseImage()),*/
      getBottomSheetItemSimple(context, L.view_Avatar.tr, itemCallBack: () {
        showDialog(
          barrierDismissible: false,
          context: context,
          useSafeArea: false,
          builder: (_) {
            return PhotosView(
                    pathList: [userIconPath.value],
                    currentIndex: 0,
                  );
          },
        );
      }),
    ];
    if (userIconPath.value.isNotEmpty) {
      widgets.add(getBottomSheetItemSimple(context, L.save_photo.tr,
          itemCallBack: () => AvatarTask.saveAvatar(userIconPath.value)));
    }
    return widgets;
  }

  void updateMyself(String disPlayName, String fistName, String lastName) {
    submitOwnInfo(nickName: disPlayName).then((value){
      if(value){
        AppConfigService conf = Get.find<AppConfigService>();
        conf.saveMySelfDisplayName(disPlayName);
        conf.saveMyselfFirstName(fistName);
        conf.saveMyselfLastName(lastName);
        Get.find<EventBus>().fire(ContactDataMyselfUpdateEvent());
        userLocalName.value = disPlayName;
        _mySelfCodeDataUpdate();
      }else{
        toast(L.fail_to_edit.tr);
      }
    });
  }

  void _mySelfCodeDataUpdate() async {
    // AppConfigService conf = Get.find();
    Map<String, dynamic> mapJson = {};
    mapJson['firstName'] = '';
    mapJson['lastName'] = '';
    mapJson['nick'] = userLocalName.value;
    mapJson['number'] = userName.value;
    mapJson['type'] = '4';
    qrcode.value = json.encode(mapJson);
  }
}
