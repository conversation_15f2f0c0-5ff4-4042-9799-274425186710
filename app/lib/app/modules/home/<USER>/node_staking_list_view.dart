import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_metatel/core/values/colors.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../core/languages/l.dart';
import '../../../../core/utils/util.dart';
import '../../../../r.dart';
import '../../../../routes/pages.dart';
import '../../../data/models/res/staking_list_model_res.dart';
import '../../../widgets/app_bar_cus.dart';
import '../../../widgets/divider_cus.dart';
import 'detail/node_staking_detail_view.dart';
import 'node_staking_list_controller.dart';

class NodeStakingListView extends GetView<NodeStakingListController> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: Colors.white,
        appBar: AppBar<PERSON>ommon().build(context, title: L.node_staking_list.tr),
        body: Container(
          padding: EdgeInsets.only(top: 21.r, left: 21.r, right: 21.r),
          child: Column(
            children: [
              GestureDetector(
                onTap: () {
                  Get.toNamed(Routes.NodePledgeApplicationView,
                      arguments: controller.stakingListAll);
                },
                child: Container(
                  width: double.infinity,
                  height: 0.11.sh,
                  decoration: const BoxDecoration(
                    borderRadius: BorderRadius.all(Radius.circular(52)),
                    gradient: LinearGradient(
                      colors: [
                        AppColors.colorFF249ED9,
                        AppColors.colorFF3474D0,
                      ],
                      begin: Alignment(-1.5, 0),
                      end: Alignment(1, 0),
                    ),
                  ),
                  child: Row(
                    children: [
                      SizedBox(width: 10.r,),
                      Container(
                        width: 0.077.sh,
                        height: 0.077.sh,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(100),
                        ),
                        child: Center(child: Image.asset(R.icStakingAdd,width: 36.r,height: 36.r,),),
                      ),
                      SizedBox(
                        width: 16.r,
                      ),
                      Expanded(
                          child: Text(
                        L.pledge_application.tr,
                        style: TextStyle(fontSize: 18.sp, color: Colors.white),
                      )),
                      Image.asset(
                        R.icStakingMore,
                        width: 8.r,
                        height: 15.r,
                      ),
                      SizedBox(
                        width: 16.r,
                      ),
                    ],
                  ),
                ),
              ),
              SizedBox(
                height: 10.r,
              ),
              Expanded(
                  child: Container(
                width: double.infinity,
                padding: EdgeInsets.only(top:16.r),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(16.r),
                  color: Colors.white,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      L.node_list.tr,
                      style: TextStyle(fontSize: 16.sp),
                    ),
                    SizedBox(
                      height: 16.r,
                    ),
                    const DividerCus(),
                    Container(
                      height: 30.r,
                      margin: EdgeInsets.only(top: 15.r),
                      padding: const EdgeInsets.only(left: 0, right: 15).r,
                      child: TabBar(
                        padding: EdgeInsets.zero,
                        // isScrollable: true,
                        labelPadding:
                            const EdgeInsets.only(left: 12, right: 12).r,
                        indicatorColor: Theme.of(context).colorScheme.primary,
                        indicatorWeight: 2.h,
                        isScrollable: true,
                        indicatorSize: TabBarIndicatorSize.label,
                        labelColor: AppColors.colorFF249ED9,
                        labelStyle: TextStyle(
                          fontSize: 14.sp,
                          fontWeight: FontWeight.w400,
                        ),
                        unselectedLabelColor: AppColors.colorFF656565,
                        controller: controller.tabController,
                        tabs: _createTableView(),
                      ),
                    ),
                    Expanded(
                      child: RefreshIndicator(
                        displacement: 5,
                        onRefresh: () async {
                          await controller.refreshData();
                        },
                        notificationPredicate: (_) {
                          return true;
                        },
                        child: loadSuccessView(),
                      ),
                    ),
                  ],
                ),
              )),
            ],
          ),
        ));
  }

  Widget loadSuccessView() {
    return Container(
      color: AppColors.white,
      margin: const EdgeInsets.only(left: 15, right: 15, top: 5).r,
      child: TabBarView(
        controller: controller.tabController,
        children: [
          Obx(() => _loadAllListView()),
          Obx(() => _loadPressListView()),
          Obx(() => _loadVerifyingListView()),
          Obx(() => _loadFailedListView()),
        ],
      ),
    );
  }

  List<Tab> _createTableView() {
    var listTab = <Tab>[];
    listTab
      ..add(Tab(
        text: L.all.tr,
      ))
      ..add(Tab(
        text: L.passed.tr,
      ))
      ..add(Tab(
        text: L.verifying.tr,
      ))
      ..add(Tab(
        text: L.authentication_failed.tr,
      ));
    return listTab;
  }

  Widget _loadAllListView() {
    var allData = controller.stakingListAll;
    Widget w;
    if (controller.isLoadError()) {
      w = loadErrorView(reloading: controller.getStakingList);
    }
    if (controller.isLoading()) {
      w = loadingView();
    } else {
      w = Stack(
        children: [
          Visibility(
            visible: allData.isEmpty,
            child: buildNoData(),
          ),
          ListView.builder(
            padding: EdgeInsets.only(bottom: 5.r),
            physics: const AlwaysScrollableScrollPhysics(),
            itemCount: allData.length,
            itemBuilder: (context, index) {
              var sessionData = allData[index];

              return GetBuilder<NodeStakingListController>(
                builder: (control) {
                  return createItem(sessionData);
                },
              );
            },
          ),
        ],
      );
    }
    return w;
  }

  Widget _loadPressListView() {
    var allData = controller.stakingListPassed;
    Widget w;
    if (controller.isLoadError()) {
      w = loadErrorView(reloading: controller.getStakingList);
    }
    if (controller.isLoading()) {
      w = loadingView();
    } else {
      w = Stack(
        children: [
          Visibility(
            visible: allData.isEmpty,
            child: buildNoData(),
          ),
          ListView.builder(
            padding: EdgeInsets.only(bottom: 5.r),
            physics: const AlwaysScrollableScrollPhysics(),
            itemCount: allData.length,
            itemBuilder: (context, index) {
              var sessionData = allData[index];

              return GetBuilder<NodeStakingListController>(
                builder: (control) {
                  return createItem(sessionData);
                },
              );
            },
          ),
        ],
      );
    }
    return w;
  }

  Widget _loadVerifyingListView() {
    var allData = controller.stakingListVerifying;
    Widget w;
    if (controller.isLoadError()) {
      w = loadErrorView(reloading: controller.getStakingList);
    }
    if (controller.isLoading()) {
      w = loadingView();
    } else {
      w = Stack(
        children: [
          Visibility(
            visible: allData.isEmpty,
            child: buildNoData(),
          ),
          ListView.builder(
            padding: EdgeInsets.only(bottom: 5.r),
            physics: const AlwaysScrollableScrollPhysics(),
            itemCount: allData.length,
            itemBuilder: (context, index) {
              var sessionData = allData[index];

              return GetBuilder<NodeStakingListController>(
                builder: (control) {
                  return createItem(sessionData);
                },
              );
            },
          ),
        ],
      );
    }
    return w;
  }

  Widget _loadFailedListView() {
    var allData = controller.stakingListFailed;
    Widget w;
    if (controller.isLoadError()) {
      w = loadErrorView(reloading: controller.getStakingList);
    }
    if (controller.isLoading()) {
      w = loadingView();
    } else {
      w = Stack(
        children: [
          Visibility(
            visible: allData.isEmpty,
            child: buildNoData(),
          ),
          ListView.builder(
            padding: EdgeInsets.only(bottom: 5.r),
            physics: const AlwaysScrollableScrollPhysics(),
            itemCount: allData.length,
            itemBuilder: (context, index) {
              var sessionData = allData[index];

              return GetBuilder<NodeStakingListController>(
                builder: (control) {
                  return createItem(sessionData);
                },
              );
            },
          ),
        ],
      );
    }
    return w;
  }

  Widget createItem(StakingListData data) {
    Color statusColor;
    String statusText = '';
    if (data.reviewStatus == 5) {
      statusColor = AppColors.colorFF18B68F;
      statusText = L.passed.tr;
    } else if (data.reviewStatus == 4) {
      statusColor = AppColors.colorFFFF0000;
      statusText = L.authentication_failed.tr;
    } else {
      statusColor = AppColors.colorFF3474d1;
      statusText = L.verifying.tr;
    }
    double defH = 8.r;
    double defW = 3.r;
    var titleStyle = TextStyle(fontSize: 13.sp,color: AppColors.colorFF656565);
    var descStyle = TextStyle(fontSize: 12.sp);

    String keyBoxStatus =
        data.keyboxStatus == true ? L.activated.tr : L.not_activated.tr;

    return GestureDetector(
      onTap: () {
        Get.to(NodeStakingDetailView(), arguments: data);
      },
      child: Container(
        height: 175.r,
        padding: const EdgeInsets.only(left:20,right:20,top:15,bottom: 13).r,
        margin: EdgeInsets.only(top: 10.r, bottom: 10.r,left: 3.r,right: 5.r),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16.r),
          color: AppColors.white,
          boxShadow: [
            BoxShadow(
              offset: const Offset(0,1),
                color: AppColors.color29000000, blurRadius: 2.r, spreadRadius: 0.0),
          ],
        ),
        child: Column(
          children: [
            Row(
              children: [
                Text(
                  L.node_status.tr,
                  style: TextStyle(
                      fontSize: 16.sp,fontWeight: FontWeight.bold),
                ),
                const Spacer(),
                Text(
                  statusText,
                  style: TextStyle(fontSize: 13.sp, color: statusColor),
                )
              ],
            ),
            const Divider(),
            const Spacer(),
            Row(
              children: [
                Text(
                  L.domain_name.tr,
                  style: titleStyle,
                ),
                const Spacer(),
                Text(
                  data.serverDomain ?? '',
                  style: descStyle,
                )
              ],
            ),
            const Spacer(),
            Row(
              children: [
                Text(
                  L.type.tr,
                  style: titleStyle,
                ),
                const Spacer(),
                Text(
                  getKeyBoxTypeStr(data.nodeType),
                  style: descStyle,
                )
              ],
            ),
            const Spacer(),
            Row(
              children: [
                Text(
                  L.Application_description.tr,
                  style: titleStyle,
                ),
                const Spacer(),
                Text(
                  data.description ?? '',
                  maxLines: 1,
                  style: descStyle,
                )
              ],
            ),
            const Spacer(),
            Row(
              children: [
                Text(
                  L.Application_time.tr,
                  style: titleStyle,
                ),
                const Spacer(),
                Text(
                  msgTimeHHHH_MM_DD_hh_mm_Format(data.createAt),
                  style: descStyle,
                )
              ],
            ),
            const Spacer(),
            Row(
              children: [
                Text(
                  L.k_server.tr,
                  style: titleStyle,
                ),
                const Spacer(),
                Text(
                  keyBoxStatus,
                  style: descStyle,
                )
              ],
            ),
            const Spacer()
          ],
        ),
      ),
    );
  }
}
