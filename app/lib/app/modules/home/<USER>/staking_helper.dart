import 'package:flutter_metatel/app/data/services/config_service.dart';
import 'package:flutter_web3/app/data/models/wallet_node.dart';
import 'package:flutter_web3/wallet.dart';
import 'package:get/get.dart';

import '../../../../core/utils/app_log.dart';
import '../../../data/models/res/staking_list_model_res.dart';
import '../../../data/providers/api/staking_api.dart';
import '../../wallet/wallet_manage.dart';

List<WalletModel> allWalletList = [];

String? getWalletNameByAddress(String? address) {
  if (allWalletList.isEmpty) {
    updateWalletList();
  }
  var wallet = allWalletList
      .firstWhereOrNull((w) => w.address?.toLowerCase() == address?.toLowerCase());
  return wallet?.name;
}

//查询钱包是否有该地址
Future<bool?> getAddressExistInWallets(String address) async {
  if (allWalletList.isEmpty) {
    await updateWalletList();
  }
  var wallet = allWalletList
      .firstWhereOrNull((w) => w.address?.toLowerCase() == address.toLowerCase());
  return wallet != null;
}

updateWalletList() async {
  AppLogger.d('allWalletList');

  await WalletManage().getAllWallet(isEth: true, (data) {
    if (data is List) {
      allWalletList.clear();
      for (var w in data) {
        allWalletList.add(w);
      }
    }
    AppLogger.d('allWalletList =${allWalletList.length}');
  });
}

updateWalletNodeName(List<StakingListData> list) {
  List<WalletNode>walletNodeList = [];
  for(var d in list) {
    var node = WalletNode(address:d.mainAddress,nodeId: d.keyboxId,type: WalletBindType.mainWallet);
    if(!walletNodeList.contains(node)){
      walletNodeList.add(node);
    }
    node = WalletNode(address:d.serviceAddress,nodeId: d.keyboxId,type: WalletBindType.nodeWallet);
    if(!walletNodeList.contains(node)){
      walletNodeList.add(node);
    }
  }
  WalletManage().updateWalletNodeName(walletNodeList);
  Get.find<AppConfigService>().saveFirstWalletUpdate(true);
}

updateFirstWalletNodeName() async{
  var updated = Get.find<AppConfigService>().readFirstWalletUpdate();
  AppLogger.d('updateFirstWalletNodeName updated=$updated');
  if (updated != true) {
    var account = Get.find<AppConfigService>().readUserNameWithoutDomain();
    Map<String, dynamic> map = {'account': account};
    var res = await Get.find<StakingApi>().getStakingApplyList(map);
    var stakingListAll= <StakingListData>[];
    if (res.data != null && res.data?.code == 200) {
      if (res.data?.data?.isNotEmpty ?? false) {
        stakingListAll = res.data!.data!;
      }
      if(stakingListAll.isNotEmpty){
        updateWalletNodeName(stakingListAll);
      } else {
        Get.find<AppConfigService>().saveFirstWalletUpdate(true);
      }
    }
  }
}
