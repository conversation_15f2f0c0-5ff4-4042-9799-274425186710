import 'dart:io';
import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/modules/home/<USER>/mine_controller.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:flutter_metatel/core/utils/device_util.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:vibration/vibration.dart';

import '../../../../core/values/colors.dart';

// class MySliverView extends StatefulWidget {
//   const MySliverView({super.key});
//
//   @override
//   State<StatefulWidget> createState() {
//     return _MySliverView();
//   }
// }
//
// class _MySliverView extends State<MySliverView> with TickerProviderStateMixin {
//   double extraPicHeight = 0.0; //初始化要加载到图片上的高度
//   BoxFit fitType = BoxFit.cover; //图片填充类型（刚开始滑动时是以宽度填充，拉开之后以高度填充）
//   double? prevDy; //前一次手指所在处的y值
//   AnimationController? animationController;
//   Animation<double>? anim;
//
//   @override
//   void initState() {
//     // TODO: implement initState
//     super.initState();
//     animationController = AnimationController(
//         vsync: this, duration: const Duration(milliseconds: 300));
//     anim = Tween(begin: 0.0, end: 0.0).animate(animationController!);
//   }
//
//   @override
//   void dispose() {
//     // TODO: implement dispose
//     super.dispose();
//     animationController?.dispose();
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     // TODO: implement build
//     return Scaffold(
//         body: Listener(
//       onPointerMove: (result) {
//         //手指的移动时
//         updatePicHeight(result.position.dy); //自定义方法，图片的放大由它完成。
//       },
//       onPointerUp: (_) {
//         //当手指抬起离开屏幕时
//         runAnimate(); //动画执行
//         animationController?.forward(from: 0.0); //重置动画
//       },
//       child: CustomScrollView(
//         slivers: <Widget>[
//           SliverAppBar(
//             leading: IconButton(
//               //标题左侧的控件（一般是返回上一个页面的箭头）
//               icon: const Icon(Icons.arrow_back),
//               onPressed: () {
//                 Navigator.pop(context);
//               },
//             ),
//             floating: false,
//             pinned: true,
//             snap: false,
//             //pinned代表是否会在顶部保留AppBar
//             //floating代表是否会发生下拉立即出现SliverAppBar
//             //snap必须与floating:true联合使用，表示显示SliverAppBar之后，如果没有完全拉伸，是否会完全神展开
//             expandedHeight: 236 + extraPicHeight,
//             //顶部控件所占的高度,跟随因手指滑动所产生的位置变化而变化。
//             flexibleSpace: FlexibleSpaceBar(
//                 title: null, //标题
//                 background: //背景图片所在的位置
//                     SliverAppBarCus(
//                   extraPicHeight: extraPicHeight,
//                   fitType: fitType!,
//                 ) //自定义Widget
//                 ),
//           ),
//           SliverList(//列表
//               delegate: SliverChildBuilderDelegate(
//             (context, i) {
//               return Container(
//                 padding: const EdgeInsets.all(16.0),
//                 child: Text(
//                   "This is item $i",
//                   style: const TextStyle(fontSize: 20.0),
//                 ),
//                 color: Colors.white70,
//               );
//             },
//           ))
//         ],
//       ),
//     ));
//   }
//
//   updatePicHeight(changed) {
//     if (prevDy == 0.0) {
//       //如果是手指第一次点下时，图片大小不直接变化，所以进行一个判定。
//       prevDy = changed;
//     }
//     if (extraPicHeight >= 45.0) {
//       //当我们加载到图片上的高度大于某个值的时候，改变图片的填充方式，让它由以宽度填充变为以高度填充，从而实现了图片视角上的放大。
//       fitType = BoxFit.fitHeight;
//     } else {
//       fitType = BoxFit.fitWidth;
//     }
//     extraPicHeight += changed - prevDy; //新的一个y值减去前一次的y值然后累加，作为加载到图片上的高度。
//     setState(() {
//       //更新数据
//       prevDy = changed;
//       extraPicHeight = extraPicHeight;
//       fitType = fitType;
//     });
//   }
//
//   runAnimate() {
//     //设置动画让extraPicHeight的值从当前的值渐渐回到 0
//     setState(() {
//       anim =
//           Tween(begin: extraPicHeight, end: 0.0).animate(animationController!)
//             ..addListener(() {
//               if (extraPicHeight >= 45.0) {
//                 //同样改变图片填充类型
//                 fitType = BoxFit.fitHeight;
//               } else {
//                 fitType = BoxFit.fitWidth;
//               }
//               setState(() {
//                 extraPicHeight = anim!.value;
//                 fitType = fitType;
//               });
//             });
//       prevDy = 0.0; //同样归零
//     });
//   }
// }
//
// class SliverAppBarCus extends StatelessWidget {
//   const SliverAppBarCus(
//       {Key? key, required this.extraPicHeight, required this.fitType})
//       : super(key: key);
//   final double extraPicHeight; //传入图片加载的高度
//   final BoxFit fitType; //传入的填充方式
//
//   @override
//   Widget build(BuildContext context) {
//     // TODO: implement build
//     return Stack(
//       children: <Widget>[
//         Column(
//           children: <Widget>[
//             SizedBox(
//               //缩放的图片
//               width: MediaQuery.of(context).size.width,
//               child: Image.asset(R.bgMineTop,
//                   height: 180 + extraPicHeight, fit: fitType),
//             ),
//             Container(
//               height: 80.0,
//               width: MediaQuery.of(context).size.width,
//               color: Colors.white,
//               child: Column(
//                 children: <Widget>[
//                   Container(
//                     padding: const EdgeInsets.only(left: 16.0, top: 10.0),
//                     child: const Text("QQ：777777777"),
//                   ),
//                   Container(
//                     padding: const EdgeInsets.only(left: 16.0, top: 8.0),
//                     child: const Text("女：日本人"),
//                   )
//                 ],
//               ),
//             ),
//           ],
//         ),
//         Positioned(
//           left: 30.0,
//           top: 130.0 + extraPicHeight,
//           child: SizedBox(
//             width: 100.0,
//             height: 100.0,
//             child: CircleAvatar(
//               backgroundImage: AssetImage(R.bgMineTop),
//             ),
//           ),
//         )
//       ],
//     );
//   }
// }
//
// class CustomHeroAnimation extends StatefulWidget {
//   const CustomHeroAnimation({Key? key}) : super(key: key);
//
//   @override
//   _CustomHeroAnimationState createState() => _CustomHeroAnimationState();
// }
//
// class _CustomHeroAnimationState extends State<CustomHeroAnimation>
//     with SingleTickerProviderStateMixin {
//   late AnimationController _controller;
//
//   bool _animating = false;
//   AnimationStatus? _lastAnimationStatus;
//   late Animation _animation;
//
//   //两个组件在Stack中的rect
//   Rect? child1Rect;
//   Rect? child2Rect;
//
//   @override
//   void initState() {
//     _controller = AnimationController(
//         vsync: this, duration: const Duration(milliseconds: 1200));
//     _animation = CurvedAnimation(
//       parent: _controller,
//       curve: Curves.easeIn,
//     );
//
//     _controller.addListener(() {
//       if (_controller.isCompleted || _controller.isDismissed) {
//         if (_animating) {
//           setState(() {
//             _animating = false;
//           });
//         }
//       } else {
//         _lastAnimationStatus = _controller.status;
//       }
//     });
//     super.initState();
//   }
//
//   @override
//   void dispose() {
//     _controller.dispose();
//     super.dispose();
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     //小头像
//     final Widget child1 = wChild1();
//     //大头像
//     final Widget child2 = wChild2();
//
//     //是否展示小头像；只有在动画执行时、初始状态或者刚从大图变为小图时才应该显示小头像
//     bool showChild1 =
//         !_animating && _lastAnimationStatus != AnimationStatus.forward;
//
//     // 执行动画时的目标组件；如果是从小图变为大图，则目标组件是大图；反之则是小图
//     Widget targetWidget;
//     if (showChild1 || _controller.status == AnimationStatus.reverse) {
//       targetWidget = child1;
//     } else {
//       targetWidget = child2;
//     }
//
//     return LayoutBuilder(builder: (context, constraints) {
//       return Container(
//         color: Theme.of(context).primaryColor,
//         //我们让Stack 填满屏幕剩余空间
//         width: constraints.maxWidth,
//         height: constraints.maxHeight,
//         child: Stack(
//           alignment: AlignmentDirectional.topCenter,
//           children: [
//             if (showChild1)
//               AfterLayout(
//                 //获取小图在Stack中占用的Rect信息
//                 callback: (value) => child1Rect = _getRect(value),
//                 child: child1,
//               ),
//             if (!showChild1)
//               AnimatedBuilder(
//                 animation: _animation,
//                 builder: (context, child) {
//                   //求出 rect 插值
//                   final rect = Rect.lerp(
//                     child1Rect,
//                     child2Rect,
//                     _animation.value,
//                   );
//                   // 通过 Positioned 设置组件大小和位置
//                   return Positioned.fromRect(rect: rect!, child: child!);
//                 },
//                 child: targetWidget,
//               ),
//             // 用于测量 child2 的大小，设置为全透明并且不能响应事件
//             IgnorePointer(
//               child: Center(
//                 child: Opacity(
//                   opacity: 0,
//                   child: AfterLayout(
//                     //获取大图在Stack中占用的Rect信息
//                     callback: (value) => child2Rect = _getRect(value),
//                     child: child2,
//                   ),
//                 ),
//               ),
//             ),
//           ],
//         ),
//       );
//     });
//   }
//
//   Widget wChild1() {
//     //点击后执行正向动画
//     return GestureDetector(
//       onTap: () {
//         setState(() {
//           _animating = true;
//           _controller.forward();
//         });
//       },
//       child: MAvatarCircle(
//         diameter: 91,
//         text: "22",
//         imagePath: R.icoQrCodeTrans,
//       ),
//     );
//   }
//
//   Widget wChild2() {
//     // 点击后执行反向动画
//     return GestureDetector(
//       onTap: () {
//         setState(() {
//           _animating = true;
//           _controller.reverse();
//         });
//       },
//       child: Image.asset(R.icoQrCodeTrans),
//     );
//   }
//
//   Rect _getRect(RenderAfterLayout renderAfterLayout) {
//     //我们需要获取的是AfterLayout子组件相对于Stack的Rect
//     return renderAfterLayout.localToGlobal(
//           Offset.zero,
//           //找到Stack对应的 RenderObject 对象
//           ancestor: context.findRenderObject(),
//         ) &
//         renderAfterLayout.size;
//   }
// }

class DecoratedBoxTransitionPage extends StatefulWidget {
  const DecoratedBoxTransitionPage({Key? key, required this.imagePath, required this.controllerScale, required this.settingsController}) : super(key: key);
  final String imagePath;
  final AnimationController controllerScale;
  final MineController settingsController;
  @override
  State<StatefulWidget> createState() => _DecoratedBoxTransitionPageState();
}

class _DecoratedBoxTransitionPageState extends State<DecoratedBoxTransitionPage>
     {
  late  AnimationController _controllerScale;

  /// 缩小至 0.2倍大小，放大至3倍大小 非线性动画
  late  Animation<double> _animationScale;


  bool isBig = false;
  int count = 0;
  double screenWidth=0;
  double radius=100.0;

  @override
  void dispose() {
    _controllerScale.dispose();
    super.dispose();
  }

  @override
  void initState() {
    _controllerScale=widget.controllerScale;
    _controllerScale..addStatusListener((status) {
      if (status == AnimationStatus.completed ||
          status == AnimationStatus.dismissed) {
        if(!DeviceUtil.isIOS()){
          Vibration.vibrate(duration:40,pattern:const []);
        }
        AppLogger.d("MediaQueryData.fromWindow(window).padding.top==${MediaQueryData.fromWindow(window).padding.top}");
        AppLogger.d("MediaQueryDatakToolbarHeight==$kToolbarHeight");
        count=0;
        setState(() {
          isBig = !isBig;
          radius=isBig?0:100;
          widget.settingsController.expandHeight.value=isBig?280:200;
        });
      }
    })..addListener(() {
      AppLogger.d("count==${count++}");
      AppLogger.d("_animationScale.value==${_animationScale.value}");
      setState(() {
        count=count>18?18:count;
        if(isBig){//变小
          radius=count*(100.0/18);
          widget.settingsController.expandHeight.value=280-count*(100.0/18);
        }else{//变大
          radius=((18-count)*(100.0/18));
          widget.settingsController.expandHeight.value=200+count*(100.0/18);
        }
      });
    });

    screenWidth=ScreenUtil().screenWidth;
    AppLogger.d("screenWidth==$screenWidth");
    AppLogger.d("screenWidth.h==${screenWidth.h}");
    _animationScale =
        Tween<double>(begin: 1, end: screenWidth/(90.h)).animate(
          CurvedAnimation(
            parent: _controllerScale,
            curve: Curves.easeInCirc,
          ),
        );
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.backgroundGray,
      body: GestureDetector(
        onTap: () {
          count = 0;
          if (isBig) {
            _controllerScale.reverse();
          } else {
            _controllerScale.forward();
          }
        },
        child: Center(
          child: ScaleTransition(

            scale: _animationScale,
            child: Container(
              width: 90.h,
              height: 90.h,
              decoration: BoxDecoration(
                  image: DecorationImage(
                      image: FileImage(
                        File(widget.imagePath),
                      ),
                      fit: BoxFit.cover),
                  borderRadius: BorderRadius.circular(radius)),
              child: const Text(""),
            ),
          ),
        ),
      ),
    );
  }
}
