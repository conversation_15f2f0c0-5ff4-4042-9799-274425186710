/*
 * <AUTHOR> <PERSON><PERSON>
 * @Date         : 2022-05-13 15:30:03
 * @Description  : TODO: Add description
 * @LastEditors  : <PERSON><PERSON>
 * @LastEditTime : 2022-06-08 18:51:41
 * @FilePath     : /flutter_metatel/lib/app/modules/home/<USER>/notifyset/notify_set_controller.dart
 */
import 'package:get/get.dart';

import '../../../../data/services/config_service.dart';

class NotifySetController extends GetxController {
  var isVibration = false.obs;
  var isSound = true.obs;
  AppConfigService configService = Get.find();
  @override
  void onInit() {
    super.onInit();
    isVibration.value = configService.getMessageVibrateEnabled();
    isSound.value = configService.getMessageRingEnabled();
  }

  void setIsVibration() {
    configService.saveMessageVibrateEnabled(isVibration.value);
  }

  void setSound() {
    configService.saveMessageRingEnabled(isSound.value);
  }
}
