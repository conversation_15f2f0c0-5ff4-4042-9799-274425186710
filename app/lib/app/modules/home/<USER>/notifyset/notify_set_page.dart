import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/modules/home/<USER>/mine_view.dart';
import 'package:flutter_metatel/app/widgets/app_bar_cus.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../../core/languages/l.dart';
import 'notify_set_controller.dart';

class NotifySetView extends GetView<NotifySetController> {
  NotifySetView({Key? key}) : super(key: key);
  @override
  final NotifySetController controller =
      Get.put(NotifySetController(), permanent: true);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBarCommon().build(
          context,
          title: L.setting_notification.tr,
        ),
        body: Container(
          color: Theme.of(context).primaryColor,
          child: Column(
            children: [
              settingsWidgetItemSimple(L.setting_vibrate_tip.tr,
                  fontSize: 15.sp,
                  onTapEnable: false,
                  rightWidget: SizedBox(
                    height: 30.r,
                    child: FittedBox(
                        fit: BoxFit.fill,
                        child: StatefulBuilder(builder: (context, setState) {
                          return CupertinoSwitch(
                            activeColor: Theme.of(context).colorScheme.primary,
                            value: controller.isVibration.value,
                            onChanged: (value) {
                              setState(() {
                                controller.isVibration.value =
                                    !controller.isVibration.value;
                                controller.setIsVibration();
                              });
                            },
                          );
                        })),
                  )),
              settingsWidgetItemSimple(
                L.setting_message_voice_tip.tr,
                fontSize: 15.sp,
                onTapEnable: false,
                rightWidget: SizedBox(
                    height: 30.r,
                    child: FittedBox(
                        fit: BoxFit.fill,
                        child: StatefulBuilder(builder: (context, setState) {
                          return CupertinoSwitch(
                            activeColor: Theme.of(context).colorScheme.primary,
                            value: controller.isSound.value,
                            onChanged: (value) {
                              setState(() {
                                controller.isSound.value =
                                    !controller.isSound.value;
                                controller.setSound();
                              });
                            },
                          );
                        }))),
              ),
            ],
          ),
        ));
  }
}
