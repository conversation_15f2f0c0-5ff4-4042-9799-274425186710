import 'package:flutter/material.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:get/get.dart';

import '../../../../../core/languages/l.dart';
import '../../../../../core/utils/events_bus.dart';
import '../../../../../core/utils/util.dart';
import '../../../../../routes/pages.dart';
import '../../../../data/events/events.dart';
import '../../../../data/models/res/staking_list_model_res.dart';
import '../../../../data/providers/api/staking_api.dart';

class NodeStakingDetailController extends GetxController
    with GetTickerProviderStateMixin {
  Rx<StakingListData> stakingListData = StakingListData().obs;
  TextEditingController editControllerPin = TextEditingController(); //激活用的pin

  TextEditingController updateOldControllerPin = TextEditingController(); //
  TextEditingController updateNewControllerPin = TextEditingController(); //
  TextEditingController updateNewAgainControllerPin = TextEditingController(); //

  RxBool showPinLength = true.obs;
  RxBool showNewPinLength = true.obs;
  RxBool showNewAgainPinLength = true.obs;
  RxBool obscureTextOld = true.obs;
  RxBool obscureTextNew = true.obs;
  RxBool obscureTextNewAgain = true.obs;

  @override
  void onInit() {
    super.onInit();
    stakingListData.value = Get.arguments;
    AppLogger.d('stakingListData ${stakingListData.value.toJson()}');
  }

  activeKeyBox() async {
    var pin1 = editControllerPin.text.trim();
    if (pin1.isEmpty) {
      toast(L.the_pin_codes_can_not_empty.tr);
      return;
    } else if (pin1.length < 6) {
      toast(L.the_pin_code_cannot_be_less_than_6_digits.tr);
      return;
    }
    showLoadingDialog();
    Map<String, dynamic> map = {
      "pin": pin1,
      "keyboxid": stakingListData.value.keyboxId
    };
    var res = await Get.find<StakingApi>()
        .keyBoxActive(stakingListData.value.serverDomain ?? '', map);
    if (res.data?.code == 200) {
      var keyBoxres = await Get.find<StakingApi>()
          .getKeyBoxInfo(stakingListData.value.serverDomain ?? '');
      editControllerPin.text = '';
      if ((keyBoxres.data?.active ?? false)) {
        //添加激活到服务器
        var m = {"id": stakingListData.value.id, "status": true};
        var r = await Get.find<StakingApi>().updateKeyBoxStatus(m);
        if (r.data?.code == 200) {
          Get.find<EventBus>().fire(UpdateStakingEvent());
          while (Get.currentRoute != Routes.NodeStakingListView) {
            Get.back();
          }
        }
      }
    } else {
      toast(res.data?.message ?? '');
    }
    dismissLoadingDialog();
  }

  onPinTextChange(String? text) {
    showPinLength.value = (text?.length ?? 0) < 6;
  }

  onOldPinTextChange(String? text) {
    showPinLength.value = (text?.length ?? 0) < 6;
  }

  onNewPinTextChange(String? text) {
    showNewPinLength.value = (text?.length ?? 0) < 6;
  }

  onNewPinAgainTextChange(String? text) {
    showNewAgainPinLength.value = updateNewControllerPin.text.trim() !=
        updateNewAgainControllerPin.text.trim();
  }

  bool legalityCheckPin(String pin1, String pin2, String oldPin) {
    if (pin1.isEmpty) {
      toast(L.the_pin_codes_can_not_empty.tr);
      return false;
    } else if (pin1 != pin2) {
      toast(L.the_pin_codes_entered_twice_are_inconsistent.tr);
      return false;
    } else if (pin1.length < 6) {
      toast(L.the_pin_code_cannot_be_less_than_6_digits.tr);
      return false;
    }

    return true;
  }

  submitPin() async {
    var oldPin = updateOldControllerPin.text.trim();
    var pin1 = updateNewControllerPin.text.trim();
    var pin2 = updateNewAgainControllerPin.text.trim();
    bool lega = legalityCheckPin(pin1, pin2, oldPin);
    if (!lega) {
      return;
    }
    Map<String, dynamic> map = {"pin": oldPin, "newpin": pin2};
    var res = await Get.find<StakingApi>()
        .keyBoxResetPin(stakingListData.value.serverDomain ?? '', map);
    if (res.data?.code == 200) {
      refreshPinEdit();

      Get.back();
    } else {
      toast(res.data?.message ?? ' error');
    }
  }

  void refreshPinEdit() {
    updateOldControllerPin.text = '';
    updateNewControllerPin.text = '';
    updateNewAgainControllerPin.text = '';
  }
}
