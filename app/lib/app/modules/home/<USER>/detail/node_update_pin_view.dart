import 'package:flutter/material.dart';
import 'package:flutter_metatel/core/values/colors.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../../core/languages/l.dart';
import '../../../../../r.dart';
import '../../../../widgets/app_bar_cus.dart';
import '../../../../widgets/divider_cus.dart';
import 'node_staking_detail_controller.dart';

class NodeUpdatePinView extends StatefulWidget {
  @override
  State<StatefulWidget> createState() {
    return _NodeUpdatePinView();
  }
}

class _NodeUpdatePinView extends State<NodeUpdatePinView> {
  NodeStakingDetailController controller =
      Get.find<NodeStakingDetailController>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBarCommon().build(context, title: L.update_pin_code.tr),
        body: Container(
          color: AppColors.colorFFF2F2F2,
          padding: EdgeInsets.only(top: 16.r, left: 16.r, right: 16.r),
          child: Column(
            children: [
              Container(
                  width: double.infinity,
                  padding: EdgeInsets.all(16.r),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(16.r),
                    color: Colors.white,
                  ),
                  child: createOldPinWidget()),
              SizedBox(
                height: 10.r,
              ),
              Container(
                  width: double.infinity,
                  padding: EdgeInsets.all(16.r),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(16.r),
                    color: Colors.white,
                  ),
                  child: createPinWidget()),
              SizedBox(
                height: 20.r,
              ),
              ElevatedButton(
                onPressed: () async {
                  controller.submitPin();
                },
                child: Text(
                  L.submit.tr,
                  style: TextStyle(fontSize: 16.sp),
                ),
              ),
              SizedBox(
                height: 30.r,
              ),
            ],
          ),
        ));
  }

  Widget createOldPinWidget() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          L.old_pin_code.tr,
          style: TextStyle(fontSize: 16.sp, color: Colors.black),
        ),
        SizedBox(
          height: 16.r,
        ),
        SizedBox(
          height: 40.r,
          child: Obx(() => TextField(
                controller: controller.updateOldControllerPin,
                readOnly: false,
                onChanged: (text) => controller.onOldPinTextChange(text),
                textAlign: TextAlign.start,
                textAlignVertical: TextAlignVertical.center,
                obscureText: controller.obscureTextOld.value,
                decoration: InputDecoration(
                  hintText: '',
                  filled: true,
                  suffixIcon: IconButton(
                    icon: Obx(() => Icon(controller.obscureTextOld.value
                        ? Icons.visibility_off
                        : Icons.visibility)),
                    onPressed: () {
                      controller.obscureTextOld.value =
                          !controller.obscureTextOld.value;
                    },
                  ),
                  suffixIconConstraints: const BoxConstraints(maxWidth: 50),
                  contentPadding:
                      EdgeInsets.only(left: 10.r, bottom: 10.r, top: 10.r),
                  fillColor: AppColors.colorFFF2F2F2,
                  suffixStyle: TextStyle(fontSize: 14.sp, color: Colors.black),
                  enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.all(Radius.circular(5.r)),
                      borderSide:
                          const BorderSide(color: AppColors.colorFFF2F2F2)),
                  focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.all(Radius.circular(5.r)),
                      borderSide:
                          const BorderSide(color: AppColors.colorFF3474d1)),
                ),
              )),
        ),
      ],
    );
  }

  Widget createPinWidget() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          L.new_pin_code.tr,
          style: TextStyle(fontSize: 16.sp, color: Colors.black),
        ),
        SizedBox(
          height: 10.r,
        ),
        SizedBox(
          height: 40.r,
          child: Obx(() => TextField(
            controller: controller.updateNewControllerPin,
            readOnly: false,
            onChanged: (text) => controller.onNewPinTextChange(text),
            textAlign: TextAlign.start,
            textAlignVertical: TextAlignVertical.center,
            obscureText: controller.obscureTextNew.value,
            decoration: InputDecoration(
              hintText: '',
              filled: true,
              suffixIcon: IconButton(
                icon: Obx(() => Icon(controller.obscureTextNew.value
                    ? Icons.visibility_off
                    : Icons.visibility)),
                onPressed: () {
                  controller.obscureTextNew.value =
                  !controller.obscureTextNew.value;
                },
              ),
              suffixIconConstraints: const BoxConstraints(maxWidth: 50),
              contentPadding: EdgeInsets.all(10.r),
              fillColor: AppColors.colorFFF2F2F2,
              suffixStyle: TextStyle(fontSize: 14.sp, color: Colors.black),
              enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.all(Radius.circular(5.r)),
                  borderSide: const BorderSide(color: AppColors.colorFFF2F2F2)),
              focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.all(Radius.circular(5.r)),
                  borderSide: const BorderSide(color: AppColors.colorFF3474d1)),
            ),
          )),
        ),
        Obx(() => Visibility(
            visible: controller.showNewPinLength.value,
            child: Text(
              L.the_pin_code_cannot_be_less_than_6_digits.tr,
              style: TextStyle(fontSize: 10.sp, color: AppColors.colorFFFF0000),
            ))),
        SizedBox(
          height: 16.r,
        ),
        const DividerCus(),
        SizedBox(
          height: 16.r,
        ),
        Text(
          L.please_enter_a_new_pin_code_again.tr,
          style: TextStyle(fontSize: 16.sp, color: Colors.black),
        ),
        SizedBox(
          height: 10.r,
        ),
        SizedBox(
          height: 40.r,
          child: Obx(() => TextField(
            controller: controller.updateNewAgainControllerPin,
            readOnly: false,
            onChanged: (text) => controller.onNewPinAgainTextChange(text),
            textAlign: TextAlign.start,
            textAlignVertical: TextAlignVertical.center,
            obscureText: controller.obscureTextNewAgain.value,
            decoration: InputDecoration(
              hintText: '',
              filled: true,
              suffixIcon: IconButton(
                icon: Obx(() => Icon(controller.obscureTextNewAgain.value
                    ? Icons.visibility_off
                    : Icons.visibility)),
                onPressed: () {
                  controller.obscureTextNewAgain.value =
                  !controller.obscureTextNewAgain.value;
                },
              ),
              suffixIconConstraints: const BoxConstraints(maxWidth: 50),
              contentPadding: EdgeInsets.all(10.r),
              fillColor: AppColors.colorFFF2F2F2,
              suffixStyle: TextStyle(fontSize: 14.sp, color: Colors.black),
              enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.all(Radius.circular(5.r)),
                  borderSide: const BorderSide(color: AppColors.colorFFF2F2F2)),
              focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.all(Radius.circular(5.r)),
                  borderSide: const BorderSide(color: AppColors.colorFF3474d1)),
            ),
          ))
    ,
        ),
        Obx(() => Visibility(
            visible: controller.showNewAgainPinLength.value,
            child: Text(
              L.the_pin_codes_entered_twice_are_inconsistent.tr,
              style: TextStyle(fontSize: 10.sp, color: AppColors.colorFFFF0000),
            ))),
      ],
    );
  }
}
