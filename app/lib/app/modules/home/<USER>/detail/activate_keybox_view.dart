import 'package:flutter/material.dart';
import 'package:flutter_metatel/core/values/colors.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../../core/languages/l.dart';
import '../../../../widgets/app_bar_cus.dart';
import '../../../../widgets/divider_cus.dart';
import 'node_staking_detail_controller.dart';

class ActivateKeyBoxView extends StatefulWidget {
  @override
  State<StatefulWidget> createState() {
    return _ActivateKeyBoxView();
  }
}

class _ActivateKeyBoxView extends State<ActivateKeyBoxView> {
  NodeStakingDetailController controller =
      Get.find<NodeStakingDetailController>();

  @override
  void initState() {
    super.initState();
    controller.editControllerPin.text = '';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBarCommon().build(context, title: L.activate_keybox.tr),
        body: Container(
          color: AppColors.colorFFF2F2F2,
          padding: EdgeInsets.only(top: 16.r, left: 16.r, right: 16.r),
          child: Column(
            children: [
              Container(
                  width: double.infinity,
                  padding: EdgeInsets.all(16.r),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(16.r),
                    color: Colors.white,
                  ),
                  child: createPinWidget()),
              SizedBox(
                height: 10.r,
              ),
              SizedBox(
                height: 20.r,
              ),
              ElevatedButton(
                onPressed: () async {
                  controller.activeKeyBox();
                },
                child: Text(
                  L.activate.tr,
                  style: TextStyle(fontSize: 16.sp),
                ),
              ),
              SizedBox(
                height: 30.r,
              ),
            ],
          ),
        ));
  }

  Widget createPinWidget() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'keybox-id',
          style: TextStyle(fontSize: 16.sp, color: Colors.black),
        ),
        SizedBox(
          height: 16.r,
        ),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(10),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(5.r),
            color: AppColors.colorFFF2F2F2,
          ),
          child:
              Obx(() => Text(controller.stakingListData.value.keyboxId ?? '')),
        ),
        SizedBox(
          height: 10.r,
        ),
        const DividerCus(),
        SizedBox(
          height: 16.r,
        ),
        Text(
          L.please_enter_a_pin_code.tr,
          style: TextStyle(fontSize: 16.sp, color: Colors.black),
        ),
        SizedBox(
          height: 16.r,
        ),
        SizedBox(
          height: 40.r,
          child: Obx(() => TextField(
            controller: controller.editControllerPin,
            readOnly: false,
            onChanged: (text) => controller.onPinTextChange(text),
            textAlign: TextAlign.start,
            textAlignVertical: TextAlignVertical.center,
            obscureText: controller.obscureTextOld.value,
            decoration: InputDecoration(
              hintText: '',
              filled: true,
              suffixIcon: IconButton(
                icon: Obx(() => Icon(controller.obscureTextOld.value
                    ? Icons.visibility_off
                    : Icons.visibility)),
                onPressed: () {
                  controller.obscureTextOld.value =
                  !controller.obscureTextOld.value;
                },
              ),
              suffixIconConstraints: const BoxConstraints(maxWidth: 50),
              contentPadding: EdgeInsets.all(10.r),
              fillColor: AppColors.colorFFF2F2F2,
              suffixStyle: TextStyle(fontSize: 14.sp, color: Colors.black),
              enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.all(Radius.circular(5.r)),
                  borderSide: const BorderSide(color: AppColors.colorFFF2F2F2)),
              focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.all(Radius.circular(5.r)),
                  borderSide: const BorderSide(color: AppColors.colorFF3474d1)),
            ),
          )),
        ),
        Obx(() => Visibility(
            visible: controller.showPinLength.value,
            child: Text(
              L.the_pin_code_cannot_be_less_than_6_digits.tr,
              style: TextStyle(fontSize: 10.sp, color: AppColors.colorFFFF0000),
            ))),
      ],
    );
  }
}
