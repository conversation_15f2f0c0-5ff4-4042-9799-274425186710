/*
 * <AUTHOR> <PERSON><PERSON>
 * @Date         : 2022-05-12 16:14:56
 * @Description  : 
 * @LastEditors  : <PERSON><PERSON>
 * @LastEditTime : 2022-07-20 11:47:18
 * @FilePath     : /flutter_metatel/lib/app/modules/home/<USER>/detail/contact_detail_controller.dart
 */
import 'dart:convert';
import 'dart:io';

import 'package:async_task/async_task_extension.dart';
import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/data/enums/enum.dart';
import 'package:flutter_metatel/app/data/events/events.dart';
import 'package:flutter_metatel/app/data/models/user_message_model.dart';
import 'package:flutter_metatel/app/data/providers/db/database.dart';
import 'package:flutter_metatel/app/data/services/config_service.dart';
import 'package:flutter_metatel/core/task/session_task.dart';
import 'package:flutter_metatel/core/task/time_task.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:flutter_metatel/core/utils/events_bus.dart';
import 'package:get/get.dart';

import '../../../../../core/languages/l.dart';
import '../../../../../core/task/channel_task.dart';
import '../../../../../core/task/message_task.dart';
import '../../../../../core/utils/util.dart';
import '../../../../../core/values/code.dart';
import '../../../../data/models/channel_info_model_data.dart';
import '../../../../data/models/channel_member_info_model.dart';
import '../../../../data/models/contact_card_model.dart';
import '../../../../data/providers/api/channel.dart';
import '../../../../data/services/channel_service.dart';
import '../../../../data/services/chatio_service.dart';
import 'package:drift/drift.dart' as dr;

import '../../../../data/services/event_service.dart';

class ContactDetailController extends GetxController {
  late Rx<ContactData> contactData;

  /************************************个人相关************************************/
  ///单聊消息免打扰
  late Rx<bool> isMuteMessage;
  late Rx<bool> isBlock=false.obs;
  late Rx<String> contactLocalName;

  ///

  /************************************群相关************************************/
  ///群/频道禁言
  Rx<bool> isMuteTalkGroupOrChannel = false.obs;

  ///群/频道管理员权限
  Rx<bool> adminRightsGroupOrChannel = false.obs;

  ///自己是否群/频道主
  /// 自己角色
  final rxMyRole = ChannelMemberRole.ordinary.obs;

  ///联系人在群里的角色
  final rxContactRole = ChannelMemberRole.ordinary.obs;
  String? _channelId;
  String? _mySelf;
  Timer? _muteTimer;
  bool? isTid;

  ///
  RxBool isDao = false.obs;
  

  AppConfigService configService = Get.find();
  var database = Get.find<AppDatabase>();
  StreamSubscription? subscription;
  final List<StreamSubscription?> _subscriptions = [];
  get avatarPath => contactData.value.avatarPath;

  @override
  void onClose() {
    for (var element in _subscriptions) {
      element?.cancel();
    }
    super.onClose();
  }

  @override
  void onInit() {
    super.onInit();
    for (var element in _subscriptions) {
      element?.cancel();
    }
    if (Get.arguments.runtimeType == ContactData) {
      contactData = (Get.arguments as ContactData).obs;
    } else if (Get.arguments.runtimeType == UserMessage) {
      contactData = ContactData(
              id: -1, username: (Get.arguments as UserMessage).userName ?? "",displayname: (Get.arguments as UserMessage).displayName )
          .obs;
    }
    contactLocalName = (contactData.value.localname ??
            contactData.value.displayname ??
            contactData.value.mobile ??
            "")
        .obs;
    isMuteMessage = configService
        .getUserMessageSilenceState((contactData.value).username)
        .obs;
    _getContactFromDb();
    Get.find<ChannelService>()
        .getOwnInfo(contactData.value.username, server: true);
  }

  @override
  void dispose() {
    _muteTimer?.cancel();
    super.dispose();
  }

  ///从数据库监听并获取联系人数据
  void _getContactFromDb() async {
    _updateContactToHasRead();
    subscription = database
        .oneContact(contactData.value.username)
        .watchSingleOrNull()
        .listen((value) {
      AppLogger.d('getContactFromDb ${value.toString()}');
      if (value != null) {
        isTid = value.isTid;
        String curtName=value.localname ?? value.displayname??value.username;
        String? curtAvatar=value.avatarPath;
        isBlock.value=value.isBlack??false;
        SessionTask.name(value.username,
            displayname: curtName);
        if(curtName!=contactLocalName.value||contactData.value.avatarPath!=curtAvatar){
          AppLogger.d('getContactFromDb curtName:$curtName curtAvatar：$curtAvatar');
          Get.find<EventBus>().fire(ContactUpdateEvent(value.username,curtName, appSupporAbsolutePath(curtAvatar)));
        }
        contactLocalName.value=curtName;
        contactData.value = value;
      }
    });
    _subscriptions.add(subscription);
  }

  ///设置个人免打扰
  void changeIsMuteMessage() async{
    configService.saveUserMessageSilenceState(
        contactData.value.username, isMuteMessage.value);
    await Get.find<AppDatabase>().updateSessionSilence(isMuteMessage.value,contactData.value.username);

    Get.find<EventBus>().fire(UpdateMuteChangeEvent(contactData.value.username));

  }

  ///清空聊天记录
  void clearChatHistory(bool clearTop) {
    SessionTask.clear(contactData.value.username);
    deleteMessageAndFile(contactData.value.username,clearTop: clearTop).then((value) {
      Get.find<EventBus>()
          .fire(ClearChatHistoryEvent(contactData.value.username));
      toast(L.chat_history_cleared_successfully.tr);
    });
  }

  ///更新联系人已读
  void _updateContactToHasRead() {
    database
        .oneContact(contactData.value.username)
        .getSingleOrNull()
        .then((data) {
      if (data == null) {
        AppLogger.d("updateContactor==contact==null!!!");
      } else {
        database.updateContactData(ContactCompanion.insert(
          username: contactData.value.username,
          read: const dr.Value(true),
          updateTime:
              ofNullable(TimeTask.instance.getNowTime().toDouble()),
        ));
      }
    });
  }

  ///分享联系人已读
  void shareContactor(BuildContext context) {
    ContactBean contactBean = ContactBean(
      contactData.value.username,
      contactData.value.displayname,
    );
    var srcFilePath = appSupporAbsolutePath(avatarPath);
    String msgId = uuid();
    File? srcFile;
    int? srcFileSize;
    String? srcFileName;
    if (srcFilePath != null) {
      srcFile = File(srcFilePath);
      if (srcFile.existsSync()) {
        srcFileSize = srcFile.lengthSync();
        srcFileName = srcFilePath.split('/').last;
      }
    }
    var messageEvent = MessageEvent(msgId,
        owner: contactData.value.username,
        type: MessageType.contactCard,
        chatType: ChatType.singleChat,
        // 转发时会根据选择对象修改，daen
        body: jsonEncode(contactBean.toJson()),
        // filePath: filePath,
        fileName: srcFileName,
        fileSize: srcFileSize,
        // thumbnailPath: thumbnailPath,
        direction: 1,
        dateTime: TimeTask.instance.getNowDateTime());
    List<MessageEvent> m = <MessageEvent>[messageEvent];
    forwardMessage(m, blockUsername: contactBean.userName);
  }

  ///初始化频道信息 从数据库获取并监听
  void initChannelInfo(String? channelId) {
    if (channelId == null || channelId.isEmpty) {
      return;
    }
    _channelId = channelId;
    getChannelInfo(channelId);
    subscription =
        Get.find<EventBus>().on<ChannelOptionEvent>().listen((event) {
      AppLogger.d('memberInfoRequest=${event.channelId}');
      if (event.channelId == channelId) {
        getChannelInfo(channelId);
      }
    });
    _subscriptions.add(subscription);
  }

  ///从网络获取频道信息
  getChannelInfo(String channelId) async {
    ChannelInfoModelData? modelData =
        await getChannelInfoRequest(channelId, dsc: true);
    _mySelf = configService.getUserName();    
    if (modelData.code == Code.code200) {
      isDao.value = modelData.attribute == ChannelAttribute.dao.index;
      var admins = modelData.admins;
      if (_mySelf == modelData.owner) {
        rxMyRole.value = ChannelMemberRole.owner;
      } else if (admins != null && admins.contains(_mySelf)) {
        rxMyRole.value = ChannelMemberRole.administrator;
      } else {
        rxMyRole.value = ChannelMemberRole.ordinary;
      }
      if (contactData.value.username == modelData.owner) {
        rxContactRole.value = ChannelMemberRole.owner;
      } else if (admins != null &&
          admins.contains(contactData.value.username)) {
        rxContactRole.value = ChannelMemberRole.administrator;
      } else {
        rxContactRole.value = ChannelMemberRole.ordinary;
      }
      adminRightsGroupOrChannel.value =
          admins != null && admins.contains(contactData.value.username);
    }
    await getGroupMemberInfo(channelId);
  }

  ///从网络获取群成员信息
  getGroupMemberInfo(String channelId) async {
    List<ChannelMemberInfoData>? list = await memberInfoRequest(
        channelId, _mySelf ?? "", [contactData.value.username]);
    _muteTimer?.cancel();
    if (list != null) {
      for (ChannelMemberInfoData data in list) {
        int curTime = TimeTask.instance.getNowTime() ~/ 1000;
        if (data.nickname != null && data.nickname!.isNotEmpty) {
          contactLocalName.value = data.nickname!;
        }
        int muteExpire = data.mute ?? 0;
        var timeLeft = muteExpire - curTime;

        ///禁言到期剩余时间
        if (timeLeft > 0) {
          isMuteTalkGroupOrChannel.value = true;
          _muteTimer = Timer(Duration(seconds: timeLeft), () {
            getGroupMemberInfo(channelId);
          });
        } else {
          isMuteTalkGroupOrChannel.value = false;
        }
      }
    }
  }

  /// 自己是否是群主
  bool isMyOwner() {
    return rxMyRole.value == ChannelMemberRole.owner;
  }

  ///是否显示群管理功能模块
  bool isVisibleChannelOrGroupManage() {
    return isMyOwner() ||
        rxMyRole.value == ChannelMemberRole.administrator &&
            rxContactRole.value == ChannelMemberRole.ordinary;
  }

  ///是否可以群管理禁言
  bool isVisibleChannelOrGroupMute() {
    // return rxContactRole.value != ChannelMemberRole.owner;
    return true;
  }

  ///设置群/频道禁言  默认解除禁言
  void changeChannelInnerIsMuteTalk(String? channelId) async {
    if (channelId == null) {
      return;
    }
    ChannelTask.muteMember(
      channelId: channelId,
      mute: !isMuteTalkGroupOrChannel.value,
      memberUserName: contactData.value.username,
      memberNickName: contactLocalName.value,
      bCallBack: (result, time) {
        if (result) {
          isMuteTalkGroupOrChannel.value = !isMuteTalkGroupOrChannel.value;
          if (time > 0) {
            _muteTimer = Timer(
              Duration(seconds: time),
              () {
                getGroupMemberInfo(channelId);
              },
            );
          }
        }
      },
    );
  }

  ///设置群/频道管理员权限
  void changeAdminRights() async {
    if (_channelId == null || _channelId!.isEmpty) {
      return;
    }
    List<String> tagId = List<String>.empty(growable: true);
    tagId.add(contactData.value.username);
    bool result = await Get.find<ChannelService>().addOrRemoveAdmin(
      _channelId!,
      tagId,
      [contactLocalName.value],
      !adminRightsGroupOrChannel.value,
    );
    if (!result) {
    } else {
      adminRightsGroupOrChannel.value = !adminRightsGroupOrChannel.value;
    }
  }

  ///群/频道撤回
  repeal() async {
    if(_channelId!=null){
      ChannelTask.repealMemberMsg(channelId: _channelId!, memberUserName: contactData.value.username);
    }
  }

  ///群/频道 踢出
  kick(String channelId, bool undo,{bool addBlackList = false}) async {
    if (channelId.isEmpty) {
      return;
    }
    if(addBlackList){
      var success = await addBlackListRequest(
          channelId,contactData.value.username);
    }
    var success = await memberInviteOrKickRequest(
        channelId, [contactData.value.username], false,
        time: undo ? -1 : 0);
    if (success) {
      ChannelTask.memberInviteOrKick(
          channelId, [contactData.value.username], false);
      Get.find<ChannelService>().sendKick(
          channelId,
          [contactData.value.username],
          [contactLocalName.value],
          rxMyRole.value,
          undo);
      Get.back();
    }
  }

  ///恢复默认壁纸
  restoreWallpaper() async {
    String path = contactData.value.chatBackgroundPath ?? "";
    if (path.isNotEmpty) {
      await Get.find<AppDatabase>().updateContactData(ContactCompanion.insert(
        username: contactData.value.username,
        chatBackgroundPath: ofNullable(""),
        chatBackgroundUrl: ofNullable(""),
        updateTime:
            ofNullable(TimeTask.instance.getNowTime().toDouble()),
      ));
      Get.find<EventBus>().fire(ChatBackgroundUpdateEvent());
      clearWallPaperFile(path);
    }
  }
  onBlock(bool isBlock) async {
    AppLogger.d("isBlock=$isBlock");
    var contact = contactData.value;
    var contactCompanion = ContactCompanion.insert(username: contact.username,isBlack: ofNullable(isBlock));
    await Get.find<AppDatabase>().updateContactData(contactCompanion);
    this.isBlock.value=isBlock;
    Get.find<EventBus>().fire(BlacklistEvent(isBlock));
  }

}
