// import 'dart:io';

import 'package:extended_text/extended_text.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_metatel/app/modules/home/<USER>/add/add_contact_page.dart';
import 'package:flutter_metatel/app/modules/home/<USER>/edit/edit_contact_page.dart';
import 'package:flutter_metatel/app/modules/mediaDocDetail/media_doc_detail_page.dart';
import 'package:flutter_metatel/app/widgets/at_widget/selection_area.dart';
import 'package:flutter_metatel/app/widgets/mavatar_circle_avatar.dart';
import 'package:flutter_metatel/app/widgets/text_middle_overlow.dart';
import 'package:flutter_metatel/core/task/contact_task.dart';
import 'package:flutter_metatel/core/utils/jump.dart';
import 'package:flutter_metatel/core/utils/util.dart';
import 'package:flutter_metatel/core/values/colors.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../../core/languages/l.dart';
import '../../../../../r.dart';
import '../../../../../webrtc/core/webrtc_call_helper.dart';
import '../../../../data/enums/enum.dart';
import '../../../../data/models/user_message_model.dart';
import '../../../../widgets/app_bar_cus.dart';
import '../../../../widgets/at_widget/my_special_text_span_builder.dart';
// import '../../../../widgets/divider_cus.dart';
import '../../settings/mine_view.dart';
import 'contact_detail_controller.dart';

typedef RowFunctionCommonCallTap = Function();

class ContactDetailView extends StatefulWidget {
  const ContactDetailView({Key? key, this.channelId}) : super(key: key);

  final String? channelId;

  @override
  State<StatefulWidget> createState() {
    return _ContactDetailImView();
  }
}


class _ContactDetailImView extends State<ContactDetailView> {
  final ContactDetailController controller = Get.put(ContactDetailController());
  final double headAreaHeight = 390.r;
  final double actionAreaHeight = 95.r;
  final double screenPaddingHorizontal = 28.r;

  @override
  void initState() {
    super.initState();
    controller.initChannelInfo(widget.channelId);
  }

  Widget _buildKickSure() {
    bool check = false;
    bool addBlackList = false;
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.5.r),
      child: Column(
        children: [
          SizedBox(height: 35.h),
          Text(
            L.remove_group_member.tr,
            style: TextStyle(
              fontSize: 16.sp,
              color: AppColors.colorFF333333,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 25.h),
          Text(
            L.confirm_remove_group_member.tr,
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 16.sp,
              color: const Color(0xff525252),
            ),
          ),
          SizedBox(height: 10.h),      
          Column(
            crossAxisAlignment:CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                mainAxisSize: MainAxisSize.min,
                children: [
                  StatefulBuilder(
                    builder: (_, setState) {
                      return Checkbox(
                        value: check,
                        onChanged: (value) {
                          setState(() {
                            check = value ?? false;
                          });
                        },
                      );
                    },
                  ),
                  Text(
                    L.undo_all_msg.tr,
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: const Color(0xff525252),
                    ),
                  ),
                ],
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                mainAxisSize: MainAxisSize.min,
                children: [
                  StatefulBuilder(
                    builder: (_, setState) {
                      return Checkbox(
                        value: addBlackList,
                        onChanged: (value) {
                          setState(() {
                            addBlackList = value ?? false;
                          });
                        },
                      );
                    },
                  ),
                  Text(
                    L.add_blacklist.tr,
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: const Color(0xff525252),
                    ),
                  ),
                ],
              ),
            ],
          ),
          SizedBox(height: 20.h),
          Center(
            child: ElevatedButton(
              onPressed: () {
                Get.back(result: check);
                controller.kick(widget.channelId ?? "", check,addBlackList: addBlackList);
              },
              style: ButtonStyle(
                shape: MaterialStateProperty.all(
                  RoundedRectangleBorder(
                    borderRadius: BorderRadius.all(Radius.circular(22.r).r).r,
                  ),
                ),
              ),
              child: Text(
                L.backup_confirm.tr,
                style: TextStyle(fontSize: 16.sp, color: AppColors.white),
              ),
            ),
          ),
          SizedBox(height: 20.r),
        ],
      ),
    );
  }

  Future<T?> showBottomDialog<T>(BuildContext context,
      {required List<Widget> widgets}) {
    return showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: const Radius.circular(12).r,
        ),
      ),
      builder: (context) {
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: widgets,
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return Scaffold(
        extendBodyBehindAppBar: true,
        appBar: AppBarCommon().build(
          context,
          title: widget.channelId == null && !controller.isDao.value
              ? L.contact_info.tr
              : L.group_member_details.tr,
          titleColor: AppColors.white,
          elevation:0,
          leading: IconButton(
            icon: Icon(Icons.arrow_back, color: AppColors.white,),
            onPressed: () {
              Get.back();
            },
          ),    
          actions: [
            Visibility(
              visible: (widget.channelId == null && !controller.isDao.value) ||
                  controller.contactData.value.state ==
                      ContactState.notFriend.index,
              child: Container(
                alignment: Alignment.center,
                margin: const EdgeInsets.only(
                  right: 20,
                ).r,
                child: GestureDetector(
                  onTap: () {
                    if (controller.contactData.value.state == 0) {
                      Get.to(EditContactView(type: EditType.contacts),
                          arguments: controller.contactData.value);
                    } else {
                      Get.to(AddContactPage(
                        userName: controller.contactData.value.username,
                        displayName: controller.contactData.value.displayname??"",
                        avatarPath: controller.contactData.value.avatarPath,
                      ));
                    }
                  },
                  child: Image.asset(
                    controller.contactData.value.state == 0
                        ? R.icoEditFriendAppbarIm
                        : R.icoAddFriendAppbarIm,
                    width: 23.r,
                    height: 23.r,
                    color: AppColors.white,
                  ),
                ),
              ),
            ),
          ],
          backgroundColor: Colors.transparent,
        ),
        body: Container(
          // color: Theme.of(context).primaryColor,       
          color: AppColors.white,
          width: 1.sw,
          height: 1.sh,
          child: SingleChildScrollView(
            child: Column(
              children: [
                /// 头部分
                Container(
                  height: headAreaHeight,
                  child: Stack(
                    children: [
                      /// 颜色背景部分
                      Container(
                        height: headAreaHeight - (actionAreaHeight/2),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [
                              AppColors.primaryBgColor2,
                              AppColors.primaryBgColor1,                            
                            ],
                            begin: Alignment(-1.5,-0.7),
                            end: Alignment(1,0.5),      
                          ),
                          borderRadius: BorderRadius.only(
                            bottomLeft: Radius.circular(35.r),
                            bottomRight: Radius.circular(35.r),
                          ),
                        ),
                      ),
                      /// 头内容部分
                      Column(
                        children: [
                          SizedBox(height: kToolbarHeight + MediaQuery.of(context).padding.top),                        
                          Expanded(
                            child: Center(
                              child: Column(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  /// 用户头像
                                  MAvatarCircle(
                                    diameter: 105,
                                    text: controller.contactLocalName.value.isNotEmpty
                                      ? controller.contactLocalName.value
                                          .substring(0, 1)
                                      : "",
                                    imagePath: appSupporAbsolutePath(controller.avatarPath),
                                  ), 
                                  SizedBox(height: 8.r),
                                  /// 用户名
                                  CommonSelectionArea(
                                    joinZeroWidthSpace:true,
                                    child: ExtendedText(
                                      getDisplayNameOffTid(controller.contactLocalName.value,isTid:controller.isTid,user: controller.contactData.value.username),
                                      textAlign: TextAlign.center,
                                      specialTextSpanBuilder: MySpecialTextSpanBuilder(
                                        showAtBackground: false, size: Size(15.r, 15.r)
                                      ),
                                      style: TextStyle(
                                          fontSize: 15.sp,
                                          color: AppColors.white,
                                          fontWeight: FontWeight.w400,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                          /// Action 部分：text, audio, meeting
                          Padding(
                            padding: EdgeInsets.symmetric(horizontal: 23.r),
                            child: Material(
                              elevation: 2,
                              borderRadius: BorderRadius.circular(25.r),
                              child: Container(
                                height: actionAreaHeight,
                                decoration: BoxDecoration(
                                  color: AppColors.white,
                                  borderRadius: BorderRadius.circular(25.r),
                                ),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                                  children: [
                                    Expanded(
                                      flex: 1,
                                      child: rowFunctionCommon(
                                        R.iconMsg, 
                                        title: L.label_text.tr, 
                                        onTap: () {
                                          var userMessage = UserMessage(
                                              chatType: ChatType.singleChat.index,
                                              displayName: controller.contactData.value.displayname,
                                              userName: controller.contactData.value.username,
                                              avatarPath: controller.contactData.value.avatarPath);
                                          JumpPage.messgae(userMessage);
                                        },
                                      ),
                                    ),                                  
                                    Expanded(
                                      flex: 1,
                                      child: rowFunctionCommon(
                                        R.iconAudio, 
                                        title: L.label_audio.tr, 
                                        onTap: () {
                                          String name = controller.contactData.value.localname ??
                                              controller.contactData.value.displayname ??
                                              controller.contactData.value.mobile ??
                                              '';
                                          WebRtcCallHelper.instance.mackCall(
                                              controller.contactData.value.username, false, name);
                                        },
                                      ),
                                    ),
                                    Expanded(
                                      flex: 1,
                                      child: rowFunctionCommon(
                                        R.iconMeeting,
                                        title: L.label_video_call.tr, 
                                        onTap: () {
                                          String name = controller.contactData.value.localname ??
                                              controller.contactData.value.displayname ??
                                              controller.contactData.value.mobile ??
                                              '';
                                          WebRtcCallHelper.instance.mackCall(
                                              controller.contactData.value.username, true, name);
                                        },
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                // Expanded(
                //   child: ListView(
                //     padding: EdgeInsets.zero,
                //     physics: BouncingScrollPhysics(),
                //     children: [                  
                      SizedBox(height: 18.r),
                      /// 地址
                      Container(
                        width: 1.sw,
                        padding: EdgeInsets.symmetric(horizontal: screenPaddingHorizontal),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              L.label_address.tr, 
                              style: TextStyle(
                                fontSize: 15.sp,
                                color: Colors.black,
                              ),
                            ),
                            SizedBox(height: 3.r),                   
                            MiddleText(
                              controller.contactData.value.username
                                  .split("@")
                                  .first,
                              WXTextOverflow.ellipsisMiddle,
                              style: TextStyle(
                                color: AppColors.colorFF656565,
                                fontSize: 12.sp,
                                fontWeight: FontWeight.w300,
                              ),
                            ),
                            SizedBox(height: 8.r),
                            GestureDetector(
                              onTap: () async {
                                await Clipboard.setData(
                                    ClipboardData(text: controller.contactData.value.username
                                  .split("@")
                                  .first));
                                toast(
                                  L.chat_has_copy_to_shear_plate.tr,
                                );
                              },
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Image.asset(R.iconCopyNewFill,
                                      height: 11.r, width: 11.r),
                                  SizedBox(
                                    width: 5.r,
                                  ),
                                  Text(
                                    L.copy_address.tr,
                                    style: TextStyle(
                                        fontSize: 11.sp, color: AppColors.appDefault),
                                  )
                                ],
                              ),
                            ),                    
                          ],
                        ),
                      ),
                      SizedBox(height: 22.r),
                      ///群管理
                      Visibility(
                        visible: widget.channelId != null &&
                            controller.isVisibleChannelOrGroupManage(),
                        child: Column(
                          children: [
                            // Container(
                            //   width: double.infinity,
                            //   padding:
                            //       EdgeInsets.only(left: screenPaddingHorizontal, top: 16.h, bottom: 2.h, right: screenPaddingHorizontal),
                            //   child: Text(
                            //     L.group_management.tr,
                            //     style: TextStyle(
                            //         color: AppColors.txtColor, fontSize: 12.sp),
                            //   ),
                            // ),                    
                            Visibility(
                              visible: controller.isMyOwner() && !controller.isDao.value,
                              ///管理员权限
                              child: settingsWidgetItemSimple(
                                L.admin_rights.tr,
                                onTapEnable: false,
                                horizontalPadding: screenPaddingHorizontal,
                                fontSize: 15.sp,
                                rightWidget: SizedBox(
                                  height: 30.r,
                                  child: CupertinoSwitch(
                                    activeColor:
                                        Theme.of(context).colorScheme.primary,
                                    value: controller
                                        .adminRightsGroupOrChannel.value,
                                    onChanged: (value) {
                                      controller.changeAdminRights();
                                    },
                                  ),
                                ),
                              ),
                            ),
                            const Divider(),
                            ///禁言
                            Visibility(
                              visible: controller.isVisibleChannelOrGroupMute(),
                              child: settingsWidgetItemSimple(
                                L.mute_talk.tr,
                                onTapEnable: false,
                                horizontalPadding: screenPaddingHorizontal,
                                fontSize: 15.sp,
                                rightWidget: Obx(() {
                                  return SizedBox(
                                    height: 30.r,
                                    child: CupertinoSwitch(
                                      activeColor:
                                          Theme.of(context).colorScheme.primary,
                                      value:
                                          controller.isMuteTalkGroupOrChannel.value,
                                      onChanged: (value) {
                                        controller.changeChannelInnerIsMuteTalk(
                                            widget.channelId);
                                      },
                                    ),
                                  );
                                }),
                              ),
                            ),
                            const Divider(),
                            ///撤回
                            settingsWidgetItemSimple(
                              L.other_repeal.tr,
                              horizontalPadding: screenPaddingHorizontal,
                              fontSize: 15.sp,
                              onTapCallBack: () {
                                controller.repeal();
                              },
                            ),
                            const Divider(),
                            ///踢出                          
                            Visibility(
                              visible: !controller.isDao.value,
                              child: settingsWidgetItemSimple(
                                L.other_remove_from_group.tr,
                                horizontalPadding: screenPaddingHorizontal,
                                fontSize: 15.sp,
                                onTapCallBack: () {
                                  showBottomDialog(context, widgets: [_buildKickSure()])
                                      .then((value) {
                                              
                                  });
                                },
                              ),
                            ),
                          ],
                        ),
                      ),
                      ///个人管理
                      Visibility(
                        visible: widget.channelId == null && !controller.isDao.value,
                        child: Column(
                          children: [
                            settingsWidgetItemSimple(                            
                              L.media_links_and_docs.tr,
                              horizontalPadding: screenPaddingHorizontal,
                              fontSize: 15.sp,
                              onTapCallBack: () {
                                Get.to(
                                  MediaDocDetailPage(
                                    userName: controller.contactData.value.username,
                                    dispName: controller.contactData.value.localname,
                                    chatType: ChatType.singleChat.index,
                                  ),
                                );
                              },
                            ),
                            Divider(),
                            settingsWidgetItemSimple(
                              L.share_contactor.tr,
                              horizontalPadding: screenPaddingHorizontal,
                              fontSize: 15.sp,
                              onTapCallBack: () {
                                controller.shareContactor(context);
                              },
                            ),
                            Divider(),
                            // settingsWidgetItemSimple(
                            //   L.background.tr,
                            //   leftWidget:R.icoChatBgSetLabelIm==null?null:Image.asset(
                            //     R.icoChatBgSetLabelIm!,
                            //     width: 29.w,
                            //     height: 29.w,
                            //   ),
                            //   onTapCallBack: () {
                            //     showBottomDialogCommonWithCancel(
                            //       context,
                            //       widgets: [
                            //         getBottomSheetItemSimple(
                            //           context,
                            //           L.choose_from_the_collection.tr,
                            //           itemCallBack: () => Get.to(
                            //             MyCollectionWallpaper(
                            //                 CollectionSetType.singChatWallPaper,
                            //                 controller.contactData.value.username),
                            //           ),
                            //         ),
                            //         getBottomSheetItemSimple(
                            //           context,
                            //           L.restore_default_background.tr,
                            //           itemCallBack: () => controller.restoreWallpaper(),
                            //         ),
                            //       ],
                            //     );
                            //   },
                            // ),
                            settingsWidgetItemSimple(
                              L.mute.tr,
                              onTapEnable: false,
                              horizontalPadding: screenPaddingHorizontal,
                              fontSize: 15.sp,
                              rightWidget: SizedBox(
                                height: 30.r,
                                child: CupertinoSwitch(
                                  activeColor: Theme.of(context).colorScheme.primary,
                                  value: controller.isMuteMessage.value,
                                  onChanged: (value) {
                                    controller.isMuteMessage.value =
                                        !controller.isMuteMessage.value;
                                    controller.changeIsMuteMessage();
                                  },
                                ),
                              ),
                            ),
                            Divider(),
                            Obx(() {
                              return
                                settingsWidgetItemSimple(
                                  controller.isBlock.value?L.unblock.tr:L.block.tr,
                                  horizontalPadding: screenPaddingHorizontal,
                                  fontSize: 15.sp,
                                  onTapEnable: false,
                                  rightWidget: SizedBox(                            
                                    height: 30.r,
                                    child: CupertinoSwitch(
                                      activeColor: Theme.of(context).colorScheme.primary,
                                      value: controller.isBlock.value,
                                      onChanged: (value) {
                                        controller.onBlock(value);
                                      },
                                    ),
                                  ),
                                );
                            }),
                            Divider(),
                            settingsWidgetItemSimple(
                              L.clear_dialog.tr,
                              horizontalPadding: screenPaddingHorizontal,
                              fontSize: 15.sp,
                              rightColor: AppColors.colorFFCD3A3A, 
                              onTapCallBack: () {
                                showBottomDialogCommonWithCancel(
                                  context,
                                  widgets: _buildBottomSheetClearChatWidgets(),
                                );
                              },
                            ),
                            ////拉黑联系人
                            // settingsWidgetItemSimple(L.block_a_close_friend.tr,rightColor: AppColors.colorFFCD3A3A, onTapCallBack: () {
                            //   showBottomDialogCommonWithCancel(context,
                            //       widgets: _buildBottomSheetWidgets());
                            // }),
                            // Divider(
                            //   height: 1.h,
                            //   color: AppColors.colorDivider,
                            // ),
                            Divider(),
                            Visibility(
                              visible: controller.contactData.value.state ==
                                  ContactState.friend.index,
                              child: settingsWidgetItemSimple(
                                L.delete_contact.tr,
                                horizontalPadding: screenPaddingHorizontal,
                                fontSize: 15.sp,
                                rightColor: AppColors.colorFFCD3A3A, onTapCallBack: () {
                                  showBottomDialogCommonWithCancel(
                                    context, 
                                    widgets: _buildBottomSheetDeleteContactWidgets(),
                                    
                                    // [
                                      
                                    //   getBottomSheetItemSimple(
                                    //     context, 
                                    //     L.delete_contact.tr,
                                    //     textColor: AppColors.colorFFCD3A3A,
                                    //     radius: 12, 
                                    //     itemCallBack: () {
                                    //       ContactTask.delContactor(
                                    //           controller.contactData.value.username);
                                    //     },
                                    //   ),
                                    // ],
                                  );
                                },
                              ),
                            ),
                          ],
                        ),
                      ),
                      SizedBox(height: 20.r),    
                    ],
                  ),
                ),
          //     ],
          //   ),
          // ),
        ),
      );
    });
  }

  // List<Widget> _buildBottomSheetWidgets() {
  //   return [
  //     getBottomSheetItemSimple(
  //       context,
  //       L.clear_dialog.tr,
  //       textColor: AppColors.colorFFCD3A3A,
  //       radius: 12,
  //       itemCallBack: () {
  //         controller.clearChatHistory();
  //       },
  //     ),
  //   ];
  // }

  Widget rowFunctionCommon(String icon,
      {String? title, RowFunctionCommonCallTap? onTap}) {
    return Ink(
      child: InkWell(
        onTap: () {
          if (onTap != null) {
            onTap();
          }
        },
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            Image.asset(
              icon,
              width: 26.w,
              height: 26.h,
            ),
            title == null
                ? Container()
                : Text(
                    title,
                    style: TextStyle(
                        fontSize: 11.sp, color: Colors.black),
                  ),
          ],
        ),
      ),
    );
  }
  List<Widget> _buildBottomSheetClearChatWidgets() {
    bool check=false;
    return [
      // getBottomSheetItemSimple(
      //   context,
      //   L.clear_dialog.tr,
      //   textColor: AppColors.colorFFCD3A3A,
      //   radius: 12,
      //   itemCallBack: () {
      //     controller.clearChatHistory(check);
      //   },
      // ),
      SizedBox(height: 32.r),
      Text(
        L.clear_dialog.tr,
        style: TextStyle(
          fontSize: 17.sp,
          fontWeight: FontWeight.bold,
          color: Colors.black,
        ),
      ),
      SizedBox(height: 18.r),
      Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          StatefulBuilder(
            builder: (_, setState) {
              return Checkbox(
                value: check,
                onChanged: (value) {
                  setState(() {
                    check = value ?? false;
                  });
                },
              );
            },
          ),
          Text(
            L.also_clear_pinned_messages.tr,
            style: TextStyle(
              fontSize: 14.sp,
              color: Colors.black,
            ),
          ),

        ],
      ),
      SizedBox(height: 36.r),
      ElevatedButton(
        onPressed: (){
          controller.clearChatHistory(check);
        }, 
        child: Text(L.clear_now.tr),
      ),

    ];
  }

  List<Widget> _buildBottomSheetDeleteContactWidgets() {
    return [     
      SizedBox(height: 32.r),
      Text(
        L.delete_contact.tr,
        style: TextStyle(
          fontSize: 17.sp,
          fontWeight: FontWeight.bold,
          color: Colors.black,
        ),
      ),
      SizedBox(height: 12.r),
      Text(
        L.delete_contact_desc.tr,
        style: TextStyle(
          fontSize: 14.sp,
          color: Colors.black,
        ),
      ),      
      SizedBox(height: 36.r),
      ElevatedButton(
        onPressed: (){
          ContactTask.delContactor(controller.contactData.value.username);
        }, 
        child: Text(L.delete.tr),
      ),

    ];
  }
}
