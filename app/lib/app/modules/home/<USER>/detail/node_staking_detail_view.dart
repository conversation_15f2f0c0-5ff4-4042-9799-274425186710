import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_metatel/core/values/colors.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../../core/languages/l.dart';
import '../../../../../core/utils/util.dart';
import '../../../../../r.dart';
import '../../../../widgets/app_bar_cus.dart';
import '../../../../widgets/divider_cus.dart';
import '../staking_helper.dart';
import 'activate_keybox_view.dart';
import 'node_staking_detail_controller.dart';
import 'node_update_pin_view.dart';

class NodeStakingDetailView extends StatefulWidget {
  @override
  State<StatefulWidget> createState() {
    return _NodeStakingDetailView();
  }
}

class _NodeStakingDetailView extends State<NodeStakingDetailView> {
  NodeStakingDetailController controller =
  Get.put<NodeStakingDetailController>(NodeStakingDetailController());

  @override
  void dispose() {
    super.dispose();
    Get.delete<NodeStakingDetailController>();
  }

  @override
  Widget build(BuildContext context) {
    var showActive = false;
    var data = controller.stakingListData.value;

    if (data.keyboxStatus != true) {
      showActive = true;
    }
    bool canClick = showActive && data.reviewStatus == 5;

    return Scaffold(
        appBar: AppBarCommon().build(context, title: L.node_staking_details.tr,elevation: 0),
        body: Container(
          color: AppColors.white,
          padding: EdgeInsets.only(top: 16.r, left: 16.r, right: 16.r),
          child: Stack(
            children: [
              Column(
                children: [
                  Container(
                      width: double.infinity,
                      padding: EdgeInsets.all(16.r),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(16.r),
                        color: AppColors.white,
                        boxShadow: [
                          BoxShadow(
                              offset: const Offset(0,1),
                              color: AppColors.color29000000, blurRadius: 2.r, spreadRadius: 0.0),
                        ],
                      ),
                      child: createKeyBoxStatus(context)),
                  SizedBox(
                    height: 20.r,
                  ),
                  Container(
                    height: 400.r,
                    padding: EdgeInsets.all(16.r),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(16.r),
                      color: AppColors.white,
                      boxShadow: [
                        BoxShadow(
                            offset: const Offset(0,1),
                            color: AppColors.color29000000, blurRadius: 2.r, spreadRadius: 0.0),
                      ],
                    ),
                    child: createItem(),
                  ),
                  SizedBox(
                    height: 60.r,
                  ),
                ],
              ),
              Positioned(
                left: 10,
                right: 10,
                bottom: 30.r,
                child: Visibility(
                  visible: showActive,
                  child: ElevatedButton(
                    onPressed: canClick
                        ? () async {
                      Get.to(ActivateKeyBoxView());
                    }
                        : null,
                    style: ButtonStyle(
                      shape: MaterialStateProperty.all(
                        RoundedRectangleBorder(
                          borderRadius:
                          const BorderRadius.all(Radius.circular(25)).r,
                        ),
                      ),
                      backgroundColor: MaterialStateProperty.all(canClick
                          ? AppColors.appDefault
                          : AppColors.colorFF999999),
                    ),
                    child: Text(
                      L.activate_keybox.tr,
                      style: TextStyle(fontSize: 16.sp),
                    ),
                  ),
                ),
              )
            ],
          ),
        ));
  }

  Widget createKeyBoxStatus(BuildContext context) {
    return Obx(() {
      var data = controller.stakingListData.value;
      Color statusColor;
      String statusText = '';
      String statusIc = R.icStakingStatus;
      if (data.reviewStatus == 5) {
        statusColor = AppColors.colorFF18B68F;
        statusText = L.passed.tr;
        statusIc = R.icKeyBoxStatusSuccess;
      } else if (data.reviewStatus == 4) {
        statusColor = AppColors.colorFFFF0000;
        statusText = L.authentication_failed.tr;
        statusIc = R.icKeyBoxStatusFailed;
      } else {
        statusColor = AppColors.colorFF3474d1;
        statusText = L.verifying.tr;
      }

      return Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Row(
            children: [
              Text(
                L.node_status.tr,
                style: TextStyle(fontSize: 16.sp, color: Colors.black),
              ),
              const Spacer(),
              GestureDetector(
                onTap: () {
                  Get.to(NodeUpdatePinView());
                },
                child: Row(
                  children: [
                    Image.asset(
                      R.icPinEdit,
                      width: 13.r,
                      height: 13.r,
                    ),
                    SizedBox(
                      width: 5.r,
                    ),
                    Text(
                      L.edit_pin.tr,
                      style: TextStyle(
                          fontSize: 14.sp, color: AppColors.colorFF3474d1),
                    )
                  ],
                ),),

            ],
          ),
          SizedBox(
            height: 10.r,
          ),
          const DividerCus(),
          SizedBox(
            height: 16.r,
          ),
          Center(
            child: Image.asset(
              statusIc,
              width: 72.r,
              height: 72.r,
            ),
          ),
          const SizedBox(
            height: 10,
          ),
          Center(
            child: Text(
              statusText,
              style: TextStyle(fontSize: 20.sp, color: statusColor),
            ),
          ),
        ],
      );
    });
  }

  Widget createItem() {
    return Obx(() {
      var data = controller.stakingListData.value;
      double defH = 3.r;
      String keyBoxStatus =
      data.keyboxStatus == true ? L.activated.tr : L.not_activated.tr;
      var titleStyle =
      TextStyle(fontSize: 12.sp, color: AppColors.colorFF808080);
      var titleDescStyle =
      TextStyle(fontSize: 14.sp, color: AppColors.colorFF000000);
      var padding = const EdgeInsets.only(
          top: 5, bottom: 10, left: 10, right: 30)
          .r;
      var mainWalletName = getWalletNameByAddress(data.mainAddress ?? '') ?? '';
      if (mainWalletName.isEmpty) {
        mainWalletName = L.un_know.tr;
      }
      var serviceWalletName =
          getWalletNameByAddress(data.serviceAddress ?? '') ?? '';
      if (serviceWalletName.isEmpty) {
        serviceWalletName = L.un_know.tr;
      }
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            height: 10.r,
          ),
          Text(
            L.node_pledge_application_info.tr,
            style: TextStyle(fontSize: 16.sp, color: Colors.black),
          ),
          SizedBox(
            height: 10.r,
          ),
          const DividerCus(),
          const Spacer(),
          Row(
            children: [
              Text(
                L.master_wallet.tr,
                style: titleStyle,
              ),
              Container(
                padding: const EdgeInsets.only(left: 5, top: 1, right: 5, bottom: 1).r,
                margin: EdgeInsets.only(left: 10.r),
                decoration: BoxDecoration(
                  color: AppColors.colorFFE5F0FF,
                  borderRadius: BorderRadius.circular(10.r),
                  border: Border.all(color: AppColors.colorFF3474d1, width: 1),
                ),
                child: Text(
                  mainWalletName,
                  style: TextStyle(
                      fontSize: 10.sp, color: AppColors.colorFF3474d1),
                ),
              ),
            ],
          ),
          SizedBox(height: defH,),

          RichText(
              text: TextSpan(children: [
                TextSpan(
                    text: data.mainAddress,
                    style: titleDescStyle,
                    recognizer: TapGestureRecognizer()
                      ..onTap = () {
                        _copyData(data.mainAddress ?? '');
                      }),
                WidgetSpan(
                  //对齐方式
                  alignment: PlaceholderAlignment.middle,
                  //这里就是中间显示的图片了也可以是其他任意的 Widget
                  child: GestureDetector(
                    onTap: () async {
                      _copyData(data.mainAddress??'');
                    },
                    child: Padding(
                      padding: padding,
                      child: Image.asset(
                        R.icoCopy,
                        width: 14.r,
                        height: 14.r,
                      ),
                    ),
                  ),
                ),
              ])),

          const Spacer(),
          Row(
            children: [
              Text(
                L.service_wallet.tr,
                style: titleStyle,
              ),
              Container(
                padding:
                const EdgeInsets.only(left: 5, top: 1, right: 5, bottom: 1).r,
                margin: EdgeInsets.only(left: 10.r),
                decoration: BoxDecoration(
                  color: AppColors.colorFFE5F0FF,
                  borderRadius: BorderRadius.circular(10.r),
                  border: Border.all(color: AppColors.colorFF3474d1, width: 1),
                ),
                child: Text(
                  serviceWalletName,
                  style: TextStyle(
                      fontSize: 10.sp, color: AppColors.colorFF3474d1),
                ),
              ),
            ],
          ),
          SizedBox(height: defH,),
          RichText(
              text: TextSpan(children: [
                TextSpan(
                  text: data.serviceAddress,
                  style: titleDescStyle,
                  recognizer: TapGestureRecognizer()
                    ..onTap = () {
                      _copyData(data.serviceAddress ?? '');
                    },
                ),
                WidgetSpan(
                  //对齐方式
                  alignment: PlaceholderAlignment.middle,
                  //这里就是中间显示的图片了也可以是其他任意的 Widget
                  child: GestureDetector(
                    onTap: () async {
                      _copyData(data.serviceAddress??'');
                    },
                    child: Padding(
                      padding: padding,
                      child: Image.asset(
                        R.icoCopy,
                        width: 14.r,
                        height: 14.r,
                      ),
                    ),
                  ),
                ),
              ])),
          const Spacer(),
          Text(
            L.server_domain_name.tr,
            style: titleStyle,
          ),
          RichText(
              text: TextSpan(children: [
                TextSpan(
                  text: data.serverDomain,
                  style: titleDescStyle,
                  recognizer: TapGestureRecognizer()
                    ..onTap = () {
                      _copyData(data.serverDomain ?? '');
                    },
                ),
                WidgetSpan(
                  //对齐方式
                  alignment: PlaceholderAlignment.middle,
                  //这里就是中间显示的图片了也可以是其他任意的 Widget
                  child: GestureDetector(
                    onTap: () async {
                      _copyData(data.serverDomain??'');
                    },
                    child: Padding(
                      padding: padding,
                      child: Image.asset(
                        R.icoCopy,
                        width: 14.r,
                        height: 14.r,
                      ),
                    ),
                  ),
                ),
              ])),
          const Spacer(),
          Text(
            L.service_type.tr,
            style: titleStyle,
          ),
          SizedBox(height: defH,),
          Text(
            getKeyBoxTypeStr(data.nodeType),
            style: TextStyle(fontSize: 12.sp, color: AppColors.colorFF000000),
          ),
          const Spacer(),
          Text(
            L.Application_description_1.tr,
            style: titleStyle,
          ),
          SizedBox(height: defH,),
          Text(
            data.description ?? '',
            maxLines: 1,
            style: TextStyle(fontSize: 12.sp, color: AppColors.colorFF000000),
          ),
          const Spacer(),
          Text(
            L.Application_time.tr,
            style: titleStyle,
          ),
          SizedBox(height: defH,),
          Text(
            msgTimeHHHH_MM_DD_hh_mm_Format(data.createAt),
            style: TextStyle(fontSize: 12.sp, color: AppColors.colorFF000000),
          ),
          const Spacer(),
          Text(
            L.k_server.tr,
            style: titleStyle,
          ),
          SizedBox(height: defH,),
          Text(
            keyBoxStatus,
            style: TextStyle(fontSize: 12.sp, color: AppColors.colorFF000000),
          ),
          const Spacer(),
        ],
      );
    });
  }
  _copyData(String data) async {
    await Clipboard.setData(ClipboardData(text: data));
    toast(data);
  }
}
