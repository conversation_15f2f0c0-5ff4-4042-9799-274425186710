import 'dart:async';
import 'dart:convert';

import 'package:async_task/async_task_extension.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_app_badger/flutter_app_badger.dart';
import 'package:flutter_metatel/app/data/providers/api/invite.dart';
import 'package:flutter_metatel/app/data/providers/db/database.dart';
import 'package:flutter_metatel/app/data/services/channel_service.dart';
import 'package:flutter_metatel/app/data/services/chatio_service.dart';
import 'package:flutter_metatel/app/data/services/emoji_manage_service.dart';
import 'package:flutter_metatel/app/modules/home/<USER>/session_page.dart';
import 'package:flutter_metatel/app/modules/home/<USER>/invite/invite_bind_recommender.dart';
import 'package:flutter_metatel/app/modules/home/<USER>/mine_controller.dart';
import 'package:flutter_metatel/app/modules/home/<USER>/mine_view_oversea.dart';
import 'package:flutter_metatel/app/modules/mining/mining_pwd_util.dart';
import 'package:flutter_metatel/app/modules/square/square_view.dart';
import 'package:flutter_metatel/app/modules/wallet/wallet_manage.dart';
import 'package:flutter_metatel/core/task/email_task.dart';
import 'package:flutter_metatel/core/task/time_task.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:flutter_metatel/core/utils/language_util.dart';
import 'package:flutter_metatel/core/utils/proxy_util.dart';
import 'package:flutter_metatel/core/values/colors.dart';
import 'package:flutter_metatel/pop_notification.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:flutter_web3/app/core/values/config.dart';
import 'package:flutter_web3/app/data/enums/enum.dart';
import 'package:flutter_web3/app/data/models/wallet_model.dart';
import 'package:flutter_web3/app/data/services/wallet_server.dart';
import 'package:flutter_web3/app/modules/home/<USER>';
import 'package:flutter_web3/app/modules/wallet/wallet_main.dart';
import 'package:flutter_web3/app/modules/wallet/wallet_main_controller.dart';
import 'package:get/get.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:time_change_detector/time_change_detector.dart';

import '../../../core/languages/l.dart';
import '../../../core/task/avatar_task.dart';
import '../../../core/task/channel_task.dart';
import '../../../core/task/official_account_task.dart';
import '../../../core/utils/device_util.dart';
import '../../../core/utils/events_bus.dart';
import '../../../core/utils/screen.dart';
import '../../../core/utils/util.dart';
import '../../../core/values/config.dart';
import '../../../r.dart';
import '../../../routes/pages.dart';
import '../../data/events/events.dart';
import '../../data/models/avatar_model.dart';
import '../../data/services/config_service.dart';
import '../../data/services/network_connect_service.dart';
import '../../data/services/probe_service.dart';
import '../../data/services/share_sevice.dart';
import '../../data/services/webrtc_service.dart';
import '../../widgets/bottom_navigation_bar_item_badge.dart';
import '../base/base_view.dart';
import '../message/file_help.dart';
import 'meeting_util.dart';
import 'staking/staking_helper.dart';

class HomeController extends GetxController {
  final RxInt _currentIndex = 0.obs;
  static RxString userIconPath = "".obs;
  final _pages = List<Widget>.empty(growable: true);
  final list = List<BottomNavigationBarItem>.empty(growable: true);
  final _unRead = 0.obs;
  int currentTime = 0;
  StreamSubscription? subscription;
  final List<StreamSubscription> _subscriptions = [];
  int? bigNumber;
  late int myTabIndex;
  int sessionIndex = 0;
  int browserIndex = -1;
  late Worker worker;
  final RxInt _current = 0.obs;
  final RxInt _frontOrBackground = 0.obs;

  // Uri? _initialUri;
  bool isUploading = false;

  var isWalletOpen = (Get.find<AppConfigService>().isWalletOpen() ?? false).obs;

  // bool _initialUriIsHandled=false;
  RxInt getCurrentIndex() {
    return _currentIndex;
  }

  final _mainCurrentIndex = 0.obs;

  void initWalletService() {
    Get.put(WalletMainController(), permanent: true);
    WalletConfig.packageName = 'flutter_web3';
    Get.put(WalletService().init(
      requestCallback: (param) async {
        return await getUserNames();
      },
      onPayPwdIsSetting: (s) async {
        return await WalletManage().checkAllConditionIsSetting();
      },
      onDispositBack: (w) {
        WalletManage().getDisposit();
        return null;
      },
      onAddBrowserClick: (w) {
        WalletManage().setAddBrowserClick();
        return null;
      },
      privateKeyBack: (m) async {
        var key = await Get.find<AppConfigService>().getPrivateKey();
        return base64.decode(key);
      },
      browserOrWalletCallback: (index, {dynamic m}) async {
        if (index == WalletOrBrowserBackType.getUserInfo.index) {
          return WalletManage().getUserInfo();
        } else if (index == WalletOrBrowserBackType.getUserInfoCer.index) {
          return await WalletManage().getUserInfoCer();
        } else if (index == WalletOrBrowserBackType.signData.index) {
          return await WalletManage().signData(m);
        } else if (index == WalletOrBrowserBackType.imgByPath.index) {
          return await WalletManage().getImgByPath(m);
        } else if (index == WalletOrBrowserBackType.verifyHuman.index) {
          return await WalletManage().verifyHuman();
        } else if (index == WalletOrBrowserBackType.walletIsOpend.index) {
          return  Get.find<AppConfigService>().isWalletOpen() ?? false;
        } else if (index == WalletOrBrowserBackType.jumpView.index) {
          Get.find<AppConfigService>().isWalletOpen() ?? false;
        } else if (index == WalletOrBrowserBackType.walletIsOpend.index) {
          return Get.find<AppConfigService>().isWalletOpen() ?? false;
        } else if (index == WalletOrBrowserBackType.binding.index) {
          bool isbinged = Get.find<AppConfigService>().isBindRecommender();
          AppLogger.d('isbinged =$isbinged');
          return isbinged;
        } else if (index == WalletOrBrowserBackType.isJoinedChannel.index) {
          return WalletManage().isJoinedChanel(m);
        }
        else if (index == WalletOrBrowserBackType.getRandom.index) {
          return WalletManage().getRandom(m);
        }
        WalletManage().setBrowserBack(index, m: m);
        return null;
      },
    ),permanent: true);
    Get.find<WalletMainController>().initData(
      mnemonic: Config.mnemonic,
      isPackage: true,
      onPayCallBack: (WalletModel value) async {
        var result = await WalletManage().pay(value);
        return result;
      },
      onAddDappCallBack: () async {
        Get.find<ProbeService>()
            .sendTask(MiningTaskType.MINING_TASK_LOGIN_DAPP);
        return '';
      },
      onInputPwdCallBack: () async {
        var result = await WalletManage().checkPwd();
        return result;
      },
      onShareWalletAddress: (wallet) {
        AppLogger.d('wallet ===${wallet.runtimeType}');
        if (wallet.runtimeType == WalletModel) {
          var walletModel = wallet as WalletModel;
          WalletManage().sendWalletAddress(walletModel);
        } else {
          WalletManage().sendImg(wallet as ByteData);
        }
        return null;
      },
      onCardSignCallBack: (int paymentType, String address, int index) async {
        return await WalletManage().showCardSign(paymentType, address, index);
      },
    );
  }

  void stopWalletService() {
    Get.delete<WalletMainController>(force: true);
    Get.delete<WalletService>(force: true);
  }

  void rebuildHomeNavigation(bool isOpen) async {
    isWalletOpen.value = isOpen;
    Get.find<EventBus>().fire(HomePageUpdate()); // 通知页面更新
  }

  RxInt getMainCurrentIndex() {
    return _mainCurrentIndex;
  }

  void setMeeting(String? url) {
    _mainCurrentIndex.value = (url?.isEmpty ?? true) ? 0 : 1;
    MeetingUtil.instance.setMeetings(url);
  }

  void changeShow(bool showMain) {
    _mainCurrentIndex.value = showMain ? 0 : 1;
  }

  void setCurrentIndex(int value) {
    AppLogger.d('setCurrentIndex myTabIndex =$myTabIndex value=$value');
    if (value == sessionIndex) {
      int t = TimeTask.instance.getNowTime();
      if (_currentIndex.value == sessionIndex && t - currentTime < 500) {
        Get.find<EventBus>().fire(NotReadEvent());
      } else {
        sendChanelEvent();
      }
      currentTime = t;
    }

    if (value == myTabIndex) {
      Get.find<MineController>().getMySelfAvatarBySer();
      Get.find<MineController>().getAdvanceNumber();
      Get.find<MineController>().updateSbt();
      Get.find<MineController>().updateBiometricsType();
    }
    _currentIndex.value = value;
    if (Config.isSupportWallet && isWalletOpen.value && list.length > value) {
      var item = list[value];
      var isWallet = item.label == L.wallet.tr;
      if (isWallet) {
        WalletConfig.rxWalletClicked.value++;
      }
    }
  }

  void openBrowserTab() {
    if(browserIndex == -1) return;
    _currentIndex.value = browserIndex;
  }

  initHomeTab() {
    _pages.clear();
    myTabIndex = Config.isSupportWallet && isWalletOpen.value
        ? (Config.isBrowser ? 4 : 3)
        : (Config.isBrowser ? 3 : 2);
    sessionIndex = 0;
    browserIndex = Config.isSupportWallet && isWalletOpen.value
        ? Config.isBrowser
            ? 3
            : -1
        : Config.isBrowser
            ? 2
            : -1;
    _currentIndex.value = 0;
    AppLogger.d("wallet Config.mnemonic ==${Config.mnemonic}");
    _pages.add(const SessionPage());
    if (Config.isSquare) {
      _pages.add(const SquareView());
    }
    if (Config.isSupportWallet && isWalletOpen.value) {
      _pages.add(const BaseView(
        isDark: false,
        WalletMainView(),
      ));
    }
    if (Config.isBrowser) {
      _pages.add(const BaseView(
          isDark: false,
          BrowserPage(
            isHome: true,
            setOrientations: [
              DeviceOrientation.portraitUp,
            ],
            defaultOrientationsPortrait: [
              DeviceOrientation.portraitUp,
            ],
          )));
    }
    _pages.add(const MineViewImOverSea());
  }

  void updateFrountOrBack({bool clean = false}) {
    if (clean) {
      _frontOrBackground.value = 0;
    } else {
      _frontOrBackground.value = _frontOrBackground.value + 1;
    }
  }

  bool isSend = false;

  @override
  void onInit() async {
    super.onInit();
    ProxyUtil.instance.insertInfo();
    EmojiManageService service = Get.find();
    service.load();
    deleteMessageByChannelId();
    initHomeTab();
    OfficialAccountTask.instance.createSession();

    /// 文档预览插件初始化

    _searChUnRendNumber();
    subscription =
        Get.find<EventBus>().on<MySelfAvatarUpdateEvent>().listen((event) {
      _updateMySelfAvatar(event);
    });
    _subscriptions.add(subscription!);
    subscription = Get.find<EventBus>().on<ShareEvent>().listen((event) async {
      AppLogger.d('UpdateLanguageEvent 0');
      isSend = true;
      await Get.find<ShareService>().sendForward(Get.context!);
      isSend = false;
      AppLogger.d('UpdateLanguageEvent 1');
    });
    _subscriptions.add(subscription!);
    subscription = Get.find<EventBus>().on<MiningTaskEvent>().listen((event) {
      Get.find<ProbeService>().sendTask(event.type);
    });
    _subscriptions.add(subscription!);
    subscription =
        Get.find<EventBus>().on<UpdateLanguageEvent>().listen((event) async {
      await Future.delayed(const Duration(milliseconds: 500));
      if (!DeviceUtil.isIOS() && !isSend) {
        AppLogger.d('UpdateLanguageEvent isSend=$isSend');
        Get.toNamed(Routes.SplashLanguagePage);
      }
      LanguageUtil.updateLanguageMain();
    });
    _subscriptions.add(subscription!);
    Get.find<ShareService>().getMedia(Get.context!);
    Future.delayed(
        const Duration(
          seconds: 3,
        ), () {
      judgePopInvited();
    });
    _updateMyself();
    changeIosSpeaker();
    if (!await Get.find<NetWorkConnectService>().networkConnected()) {
      Get.find<EventBus>()
          .fire(SessionNoticeEvent(SessionNoticeType.noNetwork, false));
    }
    sendChanelEvent();
    updateTime(isStart: true);
    _initWatcher();
    // await Get.find<ApiProvider>().getNodeConf();
    // Get.find<ChatioService>().initConnection();
    FileHelperProcess().updateFileHelper();
    AvatarTask.reUploadImageAvatar();
    ChannelTask.updateInfoAvartar();
    Future.delayed(const Duration(seconds: 3), () {
      updateFirstWalletNodeName();
    });
    AppLogger.d("home initToken complete!!!");
  }

  judgePopInvited() {
    AppLogger.d("judgePopInvited !!!");

    judgePopInvite().then((value) async {
      if (Config.isOversea) {
        AppLogger.d("judgePopInvited 1");

        if (!await Permission.notification.request().isGranted &&
            !DeviceUtil.isIOS()) {
          if (needSetNotification) {
            SmartDialog.show(
              builder: (c) {
                return const NotificationSetPopDialog();
              },
              keepSingle: true,
              useAnimation: false,
            );
          }
          needSetNotification = false;
        }
      }
    });
  }

  updateToken() async {
    initToken(needUpdateNode: true);
  }

  void _updateMyself() {
    AppConfigService conf = Get.find();
    // String mySelfDisplayName = conf.getMySelfDisplayName().isEmpty
    //     ? conf.getPhone()
    //     : conf.getMySelfDisplayName();
    AvatarModel avatar = AvatarModel.fromJson(conf.getMySelfAvatarInfo() ?? {});
    //userLocalName.value = mySelfDisplayName;
    userIconPath.value = appSupporAbsolutePath(avatar.path) ?? "";
  }

  void _updateMySelfAvatar(MySelfAvatarUpdateEvent event) {
    var avatarModel = event.avatarModel;
    AppLogger.d("AvatarModel.path==${avatarModel.path}");
    userIconPath.value = appSupporAbsolutePath(avatarModel.path) ?? "";
  }

  bool needSetNotification = true;

  @override
  void onReady() {
    // judgePopInvite().then((value) async {
    //   if (Config.isOversea) {
    //     if (!await Permission.notification.request().isGranted&&!DeviceUtil.isIOS()) {
    //       if(needSetNotification){
    //         SmartDialog.show(
    //           builder: (c) {
    //             return const NotificationSetPopDialog();
    //           },
    //           keepSingle: true,
    //           useAnimation: false,
    //         );
    //       }
    //       needSetNotification=false;
    //     }
    //   }
    // });

    super.onReady();
  }

  @override
  void dispose() {
    worker.dispose();
    for (var element in _subscriptions) {
      element.cancel();
    }
    _subscriptions.clear();
    super.dispose();
  }

  void sendChanelEvent() {
    var bus = Get.find<EventBus>();
    bus.fire(SyncChannelsEvent());
    bus.fire(SyncChannelMessageEvent());
    OfficialAccountTask.instance.getFirstNoticeList();
  }

  void _searChUnRendNumber() async {
    worker = interval(
      _current,
      (_) {
        _updateBadgeCount();
      },
      time: const Duration(milliseconds: 500),
      condition: () => true,
    );
    subscription =
        Get.find<AppDatabase>().countNotReadMsg().watch().listen((event) {
      var count = event;
      var first = count.first;
      if (count.isNotEmpty && first != null) {
        _unRead.value = first;
        _current.value++;
        if (_current.value > 1000000) {
          _current.value = 0;
        }
      }
    });
    _subscriptions.add(subscription!);
  }

  _updateBadgeCount() {
    AppLogger.d('_searChUnRendNumber _unRead=${_unRead.value}');
    FlutterAppBadger.updateBadgeCount(_unRead.value);
  }

  List<Widget> getWidgetItem() {
    return _pages;
  }

  void clipText() {
    Future.delayed(const Duration(seconds: 1), () {
      clip();
    });
    // CenterProxyTask.instance.isGoogleConnectedApi();
  }

  void clip() async {
    var text = await androidBelowTengetClipboardContent();
    AppLogger.d('_frontOrBackground===${_frontOrBackground.value}');
    if (text != null &&
        text.isNotEmpty &&
        text != 'null' &&
        (text.endsWith(sharEndFilter) && text.contains(sharStartFilter))) {
      var sharCipherTxt = text.substring(
          text.lastIndexOf(sharStartFilter) + sharStartFilter.length,
          text.length - 1);
      var sharTxt = utf8.decode(base64.decode(sharCipherTxt));
      Map<String, dynamic> mapJson = json.decode(sharTxt);
      String? type = mapJson['type'];
      if (type != null && type == '5') {
        int? sharTime = mapJson['time'];
        int now = TimeTask.instance.getNowTime() ~/ 1000;
        if (sharTime != null && now < sharTime + Config.shareTime) {
          _intoChannel(mapJson);
        } else {
          saveClipboard(null);
          toast(L.invitation_has_expired.tr);
        }
      }
    }
  }

  void _intoChannel(Map<String, dynamic> mapJson) async {
    String? channelId = mapJson['id'];
    if (channelId == null || channelId.isEmpty) {
      saveClipboard(null);
    } else {
      String id = utf8.decode(base64.decode(channelId));
      Get.toNamed(Routes.ChannelJoin, arguments: {"channelId": id});
      saveClipboard(null);
    }
  }

  Timer? _timer;

  void recvMsg() {
    Get.find<ChatioService>().recvMessage();
    // bool agent = Get.find<AppConfigService>().getLocalAgent();
    _timer?.cancel();
    int t = 3; //agent ? DeviceUtil.isIOS()?7:5:3;
    _timer = Timer.periodic(Duration(seconds: t), (timer) {
      if (Config.appIsBackground) {
        timer.cancel();
      }
      Get.find<ChatioService>().recvMessage();
    });
    EmailTask.instance.sendEmail();
  }

  void checkVersion() async {
    // if (!Get.find<WebRtcService>().isCalling() && !isUploading) {
    //   isUploading = true;
    //   await OTAUtil().checkVersion(UpdateType.main);
    //   isUploading = false;
    // }
  }

  void callWait() async {
    if (DeviceUtil.isIOS() && Get.find<WebRtcService>().isConnected()) {
      if ((bigNumber ?? 0) < 16) {
        Get.find<WebRtcService>()
            .setSpeaker(Get.find<WebRtcService>().getSpeakEnable());
      }
    }
  }

  Future<void> changeIosSpeaker() async {
    if (!DeviceUtil.isIOS()) {
      return;
    }
    DeviceInfoPlugin info = DeviceInfoPlugin();
    var code = await info.iosInfo;
    String c = code.systemVersion;
    if (c.isNotEmpty) {
      var codeChat = c.split('.');
      if (codeChat.isNotEmpty) {
        bigNumber = int.parse(codeChat[0]);
      }
    }
  }

  List<BottomNavigationBarItem> getItems() {
    var showSession = _unRead.value > 0;
    var sessionValue =
        !showSession ? '' : (_unRead.value > 100 ? '99+' : '${_unRead.value}');
    List<BottomNavigationBarItem> list = [];

    list.add(BottomNavigationBarBadgeItem.create(
        R.mainSessionIcon, R.mainSessionActiveIcon, L.main_message.tr,
        showSession: showSession, badgeContentText: sessionValue));

      list.add(BottomNavigationBarBadgeItem.create(
          R.mainChannelIcon, R.mainChannelActiveIcon, L.channel.tr,
          showSession: false, badgeContentText: ''));
    if (Config.isSupportWallet && isWalletOpen.value) {
      list.add(BottomNavigationBarBadgeItem.create(
          R.mainWalletIcon, R.mainWalletActiveIcon, L.wallet.tr,
          showSession: false, badgeContentText: ''));
    }
    if (Config.isBrowser) {
      list.add(BottomNavigationBarBadgeItem.create(
          R.mainBrowserIcon, R.mainBrowserActiveIcon, L.main_browser.tr,
          showSession: false, badgeContentText: ''));
    }
    list.add(BottomNavigationBarBadgeItem.create(
        R.mainSettingIcon,
        R.mainSettingActiveIcon,
        Config.isOversea ? L.main_mine.tr : L.main_setting.tr,
        showSession: false,
        badgeContentText: ''));
    this.list.addAll(list);
    return list;
  }

  Future<bool> onKeyBack() async {
    Screen.toSystemHome();
    return false;
  }

  retryAuth() {
    if (Get.find<ChatioService>().isUnauthorizedCallBack) {
      reConnect();
    }
  }

  Future judgePopInvite() async {
    AppLogger.d(
        "Response data url Config.inviteCodeMust=${Config.inviteCodeMust}");
    if (Config.inviteCodeMust) {
      var userNameWithoutDomain =
          Get.find<AppConfigService>().getUserNameWithoutDomain();
      var response =
          await Get.find<InviteApi>().inviteInfo(userNameWithoutDomain);
      var status = response.data?.data?.status;
      AppLogger.d(
          "Response data url status=${status}");
      if (status == 0) {
        var condeEdit = TextEditingController();
        var ret = await SmartDialog.show(
            useAnimation: false,
            keepSingle:true,
            clickMaskDismiss: false,
            backDismiss:false,
            builder: (ctx) {
              return Scaffold(
                backgroundColor: AppColors.transparent,
                body: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 30).r,
                  child: InviteBindRecommender(
                    controller: condeEdit,
                    canCancel: false,
                  ),
                ),
              );
            });
        if (ret == true) {
          showLoadingDialog();
          var bool = await bindRecommender(bindUserName: condeEdit.text);
          if (bool) {
            SmartDialog.dismiss();
          }
        }
      }
    } else {
      var needPopInviteDialog =
          Get.find<AppConfigService>().readNeedPopInviteDialog();
      AppLogger.d(
          "Response data url needPopInviteDialog=${needPopInviteDialog}");
      if (needPopInviteDialog) {
          var userNameWithoutDomain =
              Get.find<AppConfigService>().getUserNameWithoutDomain();
          var response =
              await Get.find<InviteApi>().inviteInfo(userNameWithoutDomain);
          var status = response.data?.data?.status;
          if (status == 0) {
            var condeEdit = TextEditingController();
            var ret = await SmartDialog.show(
                useAnimation: false,
                builder: (ctx) {
                  return Scaffold(
                    backgroundColor: AppColors.transparent,
                    body: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 30).r,
                      child: InviteBindRecommender(
                        controller: condeEdit,
                      ),
                    ),
                  );
                });
            if (ret == true) {
              showLoadingDialog();
              var bool = await bindRecommender(bindUserName: condeEdit.text);
              if (bool) {
                SmartDialog.dismiss();
              }
            }
        }
        Get.find<AppConfigService>().saveNeedPopInviteDialog(true);
      }
    }
  }

  updateTime({bool isStart = false}) {
    TimeTask.instance.freshTime();
    Get.find<AppDatabase>().clearAllChannelMsgDataMoreThen1000();
  }

  Stream<bool>? _controller;

  _initWatcher() {
    _controller = TimeChangeDetector().listener(calendarDayChanged: false);
    subscription = _controller?.listen((event) {
      AppLogger.e('_initWatcher:...');
      TimeTask.instance.updateTime();
    },
        onError: (error) => AppLogger.e('_initWatcher: $error'),
        onDone: () => AppLogger.e('_initWatcher: STREAM_COMPLETE'));
    _subscriptions.add(subscription!);
  }

  Future<String> getUserNames() async {
    String userNameWithoutDomain =
        Get.find<AppConfigService>().getUserNameWithoutDomain();
    String dataEn =
        await MiningPwdUtil.encryptForData(userNameWithoutDomain) ?? '';
    Map<String, dynamic> map = {
      "address": userNameWithoutDomain,
      "body": dataEn,
      "node": Config.nodeUrl(),
    };
    AppLogger.d('getUserNames map=$map');
    return json.encode(map);
  }

  updateDomain() async {
    if (Config.domain.isEmpty) {
      Get.find<ChatioService>().updateDomain();
    }
    Get.find<ProbeService>().submitUserInfo();
  }

  deleteMessageByChannelId() {
    Get.find<ChannelService>().deleteMessageByChannelId(true,'666d3cef8f8d7e281479340c');
  }
}
