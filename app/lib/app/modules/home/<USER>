/*
 * <AUTHOR> <PERSON><PERSON>
 * @Date         : 2022-04-21 10:07:06
 * @Description  : TODO: Add description
 * @LastEditors  : <PERSON><PERSON>
 * @LastEditTime : 2022-08-26 11:14:33
 * @FilePath     : /flutter_metatel/lib/app/modules/home/<USER>
 */

import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/data/services/network_connect_service.dart';
import 'package:flutter_metatel/app/modules/dao/dao_config.dart';
import 'package:flutter_metatel/app/modules/home/<USER>';
import 'package:flutter_metatel/app/modules/home/<USER>/mine_controller.dart';
import 'package:flutter_metatel/app/modules/home/<USER>/mine_view.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:flutter_metatel/core/utils/util.dart';
import 'package:flutter_metatel/core/values/config.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_web3/app/data/services/wallet_server.dart';
import 'package:flutter_web3/app/modules/wallet/wallet_main_controller.dart';
import 'package:get/get.dart';

import '../../../core/languages/l.dart';
import '../../../core/utils/events_bus.dart';
import '../../../core/values/colors.dart';
import '../../../r.dart';
import '../../../routes/pages.dart';
import '../../data/events/events.dart';
import '../../data/providers/api/own.dart';
import '../../data/services/config_service.dart';
import '../../data/services/notification_service.dart';
import '../../data/services/push_service.dart';
import '../base/base_view.dart';
import '../dao/daoBrowser/dao_browser_view.dart';
import 'meeting_util.dart';

GlobalKey<HomeState> homePageState = GlobalKey<HomeState>();

class HomePage extends StatefulWidget {
  const HomePage({Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return HomeState();
  }
}

class HomeState extends State<HomePage> with WidgetsBindingObserver {
  HomeController controller = Get.put(HomeController());

  StreamSubscription<HomePageUpdate>? subscription;

  @override
  void initState() {
    super.initState();
    
    controller.initWalletService();
    Get.find<NotificationService>().initNotification();
    Get.find<NotificationService>().requestPermissions();
    Get.find<PushService>().clearAllNotifications();

    WidgetsBinding.instance.addObserver(this);

    /// 稍微延迟下，让session界面能收到消息
    Future.delayed(const Duration(milliseconds: 300), () {
      Get.find<EventBus>()
          .fire(SessionNoticeEvent(SessionNoticeType.chatioMsg, false));
      controller.recvMsg();
    });
    ///更新界面
    subscription =
        Get.find<EventBus>().on<HomePageUpdate>().listen((event) {
          controller.initHomeTab();

          setState(() {
          });
        });
  }

  @override
  void dispose() {
    // 移除生命周期监听
    subscription?.cancel();
    WidgetsBinding.instance.removeObserver(this);
    Get.delete<HomeController>();
    controller.stopWalletService();
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    AppLogger.d(':didChangeAppLifecycleState:$state');
    switch (state) {
      // 处于这种状态的应用程序应该假设他们可能在任何时候暂停
      case AppLifecycleState.inactive:
        break;
      case AppLifecycleState.resumed: // 从后台切前台，界面可见
      Get.find<NetWorkConnectService>().checkNetwork();
      controller.updateDomain();
        Get.find<EventBus>().fire(SessionNoticeEvent(
            SessionNoticeType.chatioMsg | SessionNoticeType.channelMsg, false));
        controller.sendChanelEvent();
        controller.deleteMessageByChannelId();
        Config.appIsBackground = false;
        controller.updateFrountOrBack();
        controller.checkVersion();
        controller.callWait();
        controller.updateTime();
        if (Get.find<AppConfigService>().mySelfInfoNeedUpload()) {
          submitOwnInfo(isShowLoading: false);
        }
        controller.recvMsg();
        controller.judgePopInvited();
        controller.clipText();
        controller.retryAuth();
        Get.find<PushService>().clearAllNotifications();
        try {
          Get.find<MineController>().updateBiometricsType();
        } catch (e) {
          AppLogger.e("MineController not exist!!!");
        }
        break;

      case AppLifecycleState.hidden:
      case AppLifecycleState.paused: // 界面不可见，后台
      case AppLifecycleState.detached: // APP 结束时调用
        Config.appIsBackground = true;
        controller.updateFrountOrBack(clean: true);
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    return BaseView(WillPopScope(
      onWillPop: () async {
        return await controller.onKeyBack();
      },
      child: Obx(() {
        return IndexedStack(
          alignment: Alignment.center,
          index: controller
              .getMainCurrentIndex()
              .value,
          children: [
            _createMainView(),
            MeetingUtil.instance.createMeetingView(context),
          ],
        );
      }),
    ));
  }
  _createMainView() {
    return Scaffold(
      resizeToAvoidBottomInset: true,
      body: Obx(() {
        return IndexedStack(
          alignment: Alignment.center,
          index: controller
              .getCurrentIndex()
              .value,
          children: controller.getWidgetItem(),
        );
      }),
      floatingActionButtonLocation: FloatingActionButtonLocation
          .miniCenterDocked,
      bottomNavigationBar: SafeArea(
        child: Container(
          color: AppColors.backgroundGray,
          height: 53.r,
          child: Obx(
                () {
              return BottomNavigationBar(
                elevation: 0,
                type: BottomNavigationBarType.fixed,
                currentIndex: controller
                    .getCurrentIndex()
                    .value,
                items: controller.getItems(),
                selectedFontSize: 11.sp,
                unselectedFontSize: 10.sp,
                selectedItemColor: AppColors.appDefault,
                unselectedItemColor: Colors.black,
                backgroundColor: AppColors.backgroundGray,
                onTap: (value) {
                  controller.setCurrentIndex(value);
                },
              );
            },
          ),
        ),
      ),
    );
  }

}

