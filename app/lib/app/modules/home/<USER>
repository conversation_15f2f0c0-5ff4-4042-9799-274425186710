//FileName meeting_helper
// <AUTHOR>
//@Date 2023/8/30 11:54
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:flutter_metatel/app/modules/home/<USER>';
import 'package:flutter_metatel/core/languages/l.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:flutter_metatel/core/utils/device_util.dart';
import 'package:flutter_metatel/core/utils/util.dart';
import 'package:flutter_metatel/core/values/colors.dart';
import 'package:flutter_metatel/r.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:wakelock_plus/wakelock_plus.dart';

import '../../../meeting/meeting_floating_helper.dart';

class MeetingUtil {
  final tag = 'MeetingUtil';
  static MeetingUtil instance = MeetingUtil();
  InAppWebViewController? _webViewController;
  final RxString _url = ''.obs;

  setMeetings(String? url) {
    MeetingFloatingHelper.instance.closeFloating();
    _url.value = url ?? '';
    AppLogger.d('$tag setMeetings _url.value=${_url.value}');
    if (url?.isEmpty ?? true) {
      WakelockPlus.disable();
      dispose();
    } else {
      _dispose = false;
      WakelockPlus.enable();
    }
  }

  RxDouble _progress = 0.0.obs;
  bool _dispose = false;

  Widget createMeetingView(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Padding(
                padding: EdgeInsets.only(
                  left: 16.r,
                ),
                child: GestureDetector(
                  onTap: () {
                    MeetingFloatingHelper.instance.showFloating(context);
                  },
                  child: Image.asset(
                    R.icMeetingZoom,
                    width: 18.r,
                    height: 18.r,
                  ),
                )),
          ],
        ),
        actions: [
          Padding(
            padding: EdgeInsets.only(right: 16.r),
            child: GestureDetector(
              onTap: () {

                _closeMeeting(context);
              },
              child: Image.asset(
                R.icMeetingClose,
                width: 20.r,
                height: 20.r,
              ),
            ),
          )
        ],
      ),
      resizeToAvoidBottomInset: false,
      body: Obx(() {
        var url = _url.value;
        AppLogger.d('$tag createMeetingView url=$url');
        return Stack(
          children: [
            Container(
              color: Colors.black,
            ),
            if (url.isNotEmpty)
              Center(
                  child: InAppWebView(
                initialUrlRequest: URLRequest(url: WebUri(url)),
                initialSettings: InAppWebViewSettings(
                  javaScriptCanOpenWindowsAutomatically: true,
                  // supportMultipleWindows: true,
                  isFraudulentWebsiteWarningEnabled: true,
                  safeBrowsingEnabled: true,
                  mediaPlaybackRequiresUserGesture: false,
                  allowsInlineMediaPlayback: true,
                ),
                onWebViewCreated: (controller) async {
                  _webViewController = controller;
                  if (defaultTargetPlatform == TargetPlatform.android) {
                    await controller.startSafeBrowsing();
                  }
                },
                onReceivedHttpAuthRequest: (controller, challenge) async {
                  HttpAuthResponse res = HttpAuthResponse();
                  return res;
                },
                onConsoleMessage: (controller, msg) {
                  AppLogger.d('$tag onConsoleMessage msg=${msg.toJson()}');
                },
                onProgressChanged: (controller, progress) {
                  if (!_dispose) {
                    _progress.value = progress / 100;
                  }
                  AppLogger.d('$tag onProgressChanged _progress=${_progress}');
                },
                onLoadStart: (controller, url) async {},
                onLoadStop: (controller, url) async {},
                onPermissionRequest: (controller, permissionRequest) async {
                  AppLogger.d(
                      '$tag permissionRequest =$permissionRequest.resources');
                  return PermissionResponse(
                      resources: permissionRequest.resources,
                      action: PermissionResponseAction.GRANT);
                },
                onUpdateVisitedHistory: (controller, url, isReload) {
                  AppLogger.d('$tag history url=$url');
                },
                onTitleChanged: (controller, title) {
                  AppLogger.d('$tag history url title=$title');
                },
                onCreateWindow: (controller, createWindowAction) async {
                  return true;
                },
                onCloseWindow: (controller) {},
                onReceivedError: (contr, request, error) {
                  AppLogger.d(
                      '$tag onReceivedError request=${request.toJson()}');
                  AppLogger.d('$tag onReceivedError error=${error.toJson()}');
                },
                onReceivedHttpError: (contr, request, error) {
                  AppLogger.d(
                      '$tag onReceivedHttpError request=${request.toJson()}');
                  AppLogger.d(
                      '$tag onReceivedHttpError error=${error.toJson()}');
                },
              )),
            Obx(() {
              return _progress < 1.0
                  ? LinearProgressIndicator(
                      value: _progress.value,
                    )
                  : Container();
            }),
          ],
        );
      }),
    );
  }

  _closeMeeting(BuildContext context) {
    showBottomDialogCommonWithCancel(
      context,
      widgets: [
        const SizedBox(
          height: 20,
        ),
        Text(
          L.exit_meeting.tr,
          style: TextStyle(fontSize: 18.sp, fontWeight: FontWeight.bold),
        ),
        GestureDetector(
          child: Container(
            color: Colors.transparent,
            padding: const EdgeInsets.only(top: 30, bottom: 20,left: 50,right: 50),
            width: 200,
            child: Center(
              child: Text(L.backup_confirm.tr,
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: AppColors.colorFFFF0000
                  )),
            ),
          ),
          onTap: () {
            Navigator.pop(context);
            Get.find<HomeController>().setMeeting(null);
            MeetingFloatingHelper.instance.closeFloating();
          },
        ),
      ],
    );
  }

  dispose() {
    _dispose = true;
    _webViewController?.dispose();
    _webViewController = null;
    _progress.value = 0;
  }
}
