/*
 * <AUTHOR> <PERSON><PERSON>
 * @Date         : 2022-07-18 17:40:44
 * @Description  : 
 * @LastEditors  : <PERSON><PERSON>
 * @LastEditTime : 2022-07-20 11:48:35
 * @FilePath     : /flutter_metatel/lib/app/modules/home/<USER>/personalinfo/personal_info_head_controller.dart
 */
import 'package:flutter_metatel/app/data/models/avatar_model.dart';
import 'package:flutter_metatel/app/data/services/config_service.dart';
import 'package:flutter_metatel/core/utils/util.dart';
import 'package:get/get.dart';

class PersonalInfoHeadController extends GetxController {
  var userIconPath = "".obs;

  final AppConfigService conf = Get.find<AppConfigService>();
  @override
  void onInit() {
    super.onInit();
    AvatarModel avatarModel =
        AvatarModel.fromJson(conf.getMySelfAvatarInfo() ?? {});
    userIconPath.value = appSupporAbsolutePath(avatarModel.path) ?? "";
  }
}
