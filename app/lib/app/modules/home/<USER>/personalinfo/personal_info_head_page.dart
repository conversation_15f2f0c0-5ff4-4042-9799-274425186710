// import 'dart:io';
//
// import 'package:flutter/material.dart';
// import 'package:flutter_metatel/app/modules/home/<USER>/personalinfo/personal_info_head_controller.dart';
// import 'package:flutter_metatel/core/utils/app_log.dart';
// import 'package:flutter_metatel/core/utils/util.dart';
// import 'package:get/get.dart';
//
// import '../../../../../core/languages/l.dart';
// import '../../../../../core/task/avatar_task.dart';
// import '../../../../../r.dart';
// import '../../../../widgets/app_bar_cus.dart';
//
// class PersonalInfoHeadView extends GetView<PersonalInfoHeadController> {
//   PersonalInfoHeadView({Key? key}) : super(key: key);
//   @override
//   final PersonalInfoHeadController controller =
//       Get.put(PersonalInfoHeadController());
//
//
//   @override
//   Widget build(BuildContext context) {
//     AppLogger.d(
//         "PersonalInfoHeadView---controller.userIconPath.value==${controller.userIconPath.value}");
//     return Scaffold(
//       appBar:
//           AppBarCommon().build(context, title: L.edit_user_icon.tr, actions: [
//         Padding(
//             padding: const EdgeInsets.all(0),
//             child: TextButton(
//               child: Text(L.edit.tr,
//                   style: const TextStyle(fontSize: 14, color: Colors.blue)),
//               onPressed: () {
//                 showBottomDialogCommonWithCancel(context, widgets: [
//                   getBottomSheetItemSimple(context, L.capture_picture.tr,
//                       itemCallBack: () => AvatarTask.getImageTakePhoto(context)),
//                   getBottomSheetItemSimple(context, L.get_picture_from_phone.tr,
//                       itemCallBack: () => AvatarTask.chooseImage(context)),
//                   getBottomSheetItemSimple(context, L.save_photo.tr),
//                 ]);
//               },
//             )),
//       ]),
//       body: Obx(() {
//         return Container(
//           color: Colors.white,
//           alignment: Alignment.center,
//           child: controller.userIconPath.value.isEmpty?Image.asset(R.defaultAvatar):Image.file(
//             File(controller.userIconPath.value),
//             errorBuilder: (BuildContext context, Object exception,
//                 StackTrace? stackTrace) {
//               // Appropriate logging or analytics, e.g.
//               // myAnalytics.recordError(
//               //   'An error occurred loading "https://example.does.not.exist/image.jpg"',
//               //   exception,
//               //   stackTrace,
//               // );
//               return Image.asset(R.defaultAvatar);
//             },
//             fit: BoxFit.fitWidth,
//           ),
//         );
//       }),
//     );
//   }
//
//   /// 显示头像编辑弹出框
//   List<Widget> buildBottomSheetItem(BuildContext context) {
//     return [];
//   }
// }
