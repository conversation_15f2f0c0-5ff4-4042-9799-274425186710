// import 'package:flutter/material.dart';
// import 'package:flutter_metatel/app/data/providers/db/database.dart';
// import 'package:flutter_metatel/app/modules/home/<USER>/personalinfo/personal_info_controller.dart';
// import 'package:flutter_metatel/app/widgets/app_bar_cus.dart';
// import 'package:get/get.dart';
//
// import '../../../../../core/languages/l.dart';
// import '../../../../../core/task/avatar_task.dart';
// import '../../../../../core/utils/util.dart';
// import '../../../../../routes/pages.dart';
// import '../../../../data/models/avatar_model.dart';
// import '../../../../widgets/divider_cus.dart';
// import '../../../../widgets/mavatar_circle_avatar.dart';
// import '../../contact/edit/edit_contact_page.dart';
// import '../mine_view.dart';
//
// class PersonalInfoView extends GetView<PersonalInfoController> {
//   PersonalInfoView({Key? key}) : super(key: key);
//   @override
//   final PersonalInfoController controller = Get.put(PersonalInfoController());
//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//         appBar: AppBarCommon().build(
//           context,
//           title: L.personal_data.tr,
//         ),
//         body: Container(
//           color: Theme.of(context).primaryColor,
//           child: ListView(children: [
//             settingsWidgetItemSimple(
//               L.personal_data_profile_photo.tr,
//               onTapCallBack: () {
//                 showBottomDialogCommonWithCancel(context,
//                     widgets: _buildItemWidgets(context));
//               },
//               rightWidget: Obx(() {
//                 return SizedBox(
//                   width: 100,
//                   child: Row(
//                     children: [
//                       const Spacer(),
//                       MAvatarCircle(
//                         text: controller.userLocalName.value,
//                         imagePath: controller.userIconPath.value,
//                       ),
//                       const SizedBox(
//                         width: 20,
//                       ),
//                       Image.asset(
//                         'assets/images/skin_next_icon.png',
//                         width: 8,
//                         height: 12,
//                       ),
//                     ],
//                   ),
//                 );
//               }),
//             ),
//             settingsWidgetItemSimple(
//               L.personal_data_name.tr,
//               onTapCallBack: () {
//                 Map<String, dynamic> mapAvatar =
//                     controller.conf.getMySelfAvatarInfo() ?? {};
//                 var avatarModel = AvatarModel.fromJson(mapAvatar);
//                 String? path = avatarModel.path;
//                 ContactData contactData = ContactData(
//                     id: -1,
//                     username: controller.userName.value,
//                     displayname: controller.conf.getMySelfDisplayName(),
//                     localname: controller.conf.getMySelfDisplayName(),
//                     fistname: controller.conf.getMySelfFirstName(),
//                     lastname: controller.conf.getMySelfLastName(),
//                     avatarPath: appSupporAbsolutePath(path) ?? "");
//                 Get.to(EditContactView(type: EditType.mySelf),
//                     arguments: contactData);
//               },
//               rightWidget: Obx(() {
//                 return SizedBox(
//                   width: 200,
//                   child: Row(
//                     children: [
//                       const Spacer(),
//                       Container(
//                         constraints: const BoxConstraints(maxWidth: 150),
//                         child: Text(
//                           controller.userLocalName.value,
//                           style: const TextStyle(fontSize: 16),
//                           overflow: TextOverflow.ellipsis,
//                         ),
//                       ),
//                       const SizedBox(
//                         width: 20,
//                       ),
//                       Image.asset(
//                         'assets/images/skin_next_icon.png',
//                         width: 8,
//                         height: 12,
//                       ),
//                     ],
//                   ),
//                 );
//               }),
//             ),
//             const DividerCus(),
//             settingsWidgetItemSimple(L.personal_data_my_qr_code.tr,
//                 onTapCallBack: () {
//               Get.toNamed(Routes.MyQrcodePage, arguments: controller);
//             }),
//           ]),
//         ));
//   }
//
//   List<Widget> _buildItemWidgets(BuildContext context) {
//     List<Widget> widgets = [
//       getBottomSheetItemSimple(context, L.capture_picture.tr,
//           itemCallBack: () => AvatarTask.getImageTakePhoto(context)),
//       getBottomSheetItemSimple(context, L.get_picture_from_phone.tr,
//           itemCallBack: () => AvatarTask.chooseImage(context)),
//       getBottomSheetItemSimple(context, L.view_Avatar.tr,
//           itemCallBack: () => Get.toNamed(Routes.PersonalInfoHead)),
//     ];
//     if (controller.userIconPath.value.isNotEmpty) {
//       widgets.add(getBottomSheetItemSimple(context, L.save_photo.tr,
//           itemCallBack: () =>
//               AvatarTask.saveAvatar(controller.userIconPath.value)));
//     }
//     return widgets;
//   }
// }
