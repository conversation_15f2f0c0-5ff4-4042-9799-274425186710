/*
 * <AUTHOR> <PERSON><PERSON>
 * @Date         : 2022-07-18 17:40:44
 * @Description  : 
 * @LastEditors  : <PERSON><PERSON>
 * @LastEditTime : 2022-07-20 11:48:23
 * @FilePath     : /flutter_metatel/lib/app/modules/home/<USER>/personalinfo/personal_info_controller.dart
 */
import 'package:get/get.dart';

import '../../../../../core/utils/util.dart';
import '../../../../data/models/avatar_model.dart';
import '../../../../data/services/config_service.dart';

class PersonalInfoController extends GetxController {
  final AppConfigService conf = Get.find<AppConfigService>();
  var userLocalName = "".obs;
  var userIconPath = "".obs;
  var userName = "".obs;
  @override
  void onInit() async {
    super.onInit();
    userLocalName.value = conf.getMySelfDisplayName().isEmpty
        ? conf.getPhone()
        : conf.getMySelfDisplayName();
    AvatarModel avatar = AvatarModel.fromJson(conf.getMySelfAvatarInfo() ?? {});
    userIconPath.value = appSupporAbsolutePath(avatar.path) ?? "";
    userName.value = await conf.getUserName() ?? "";
  }
}
