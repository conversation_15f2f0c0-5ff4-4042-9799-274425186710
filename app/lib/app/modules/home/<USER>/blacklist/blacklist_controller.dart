import 'package:flutter_metatel/app/data/providers/db/database.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:get/get.dart';

class BlacklistController extends GetxController{
  final RxList<ContactData> rxList=RxList();
  @override
  void onInit() {
    AppLogger.d("BlacklistController onInit!!!");
    Get.find<AppDatabase>().allBlackedContact().watch().listen((event) {
      rxList.value=event;
    });
    super.onInit();
  }
}