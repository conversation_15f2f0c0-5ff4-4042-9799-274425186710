import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/data/providers/db/database.dart';
import 'package:flutter_metatel/app/modules/home/<USER>/blacklist/blacklist_controller.dart';
import 'package:flutter_metatel/app/widgets/app_bar_cus.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../../core/languages/l.dart';
import '../../../../../core/utils/util.dart';
import '../../../../../core/values/colors.dart';
import '../../../../widgets/divider_cus.dart';
import '../../../../widgets/mavatar_circle_avatar.dart';
import 'package:badges/badges.dart' as badges;

import '../../contact/detail/contact_detail.dart';

class BlacklistPage extends GetView<BlacklistController> {
  const BlacklistPage({super.key});

  @override
  Widget build(BuildContext context) {
    Get.put(BlacklistController());
    return Scaffold(
      appBar: AppBarCommon().build(context, title: L.blacklist.tr),
      body: Container(
        color: AppColors.white,
        child: Obx(
          () => ListView.builder(
            itemCount: controller.rxList.length,
            itemBuilder: (ctx, index) {
              return _buildDataWidget(controller.rxList[index], true);
            },
          ),
        ),
      ),
    );
  }

  Widget _buildDataWidget(ContactData model, bool showLine) {
    var padding = EdgeInsets.only(left: 16.w, right: 16.w, top: 0, bottom: 0);
    double avatarDiameter = 40.w;
    String? name = model.localname ?? model.displayname ?? model.username;
    var data = model;
    return Material(
      color: Colors.white,
      child: InkWell(
        onTap: () {
          Get.to(const ContactDetailView(), arguments: data);
        },
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              height: 49.h,
              padding: padding,
              child: Row(
                children: [
                  // 头像
                  Padding(
                    padding: const EdgeInsets.only(bottom: 0).r,
                    child: MAvatarCircle(
                      diameter: avatarDiameter,
                      text: name,
                      textStyle: TextStyle(
                        fontSize: 20.sp,
                        color: Colors.white,
                        fontWeight: FontWeight.normal,
                      ),
                      imagePath: appSupporAbsolutePath(data.avatarPath),
                    ),
                  ),
                  // 间隔
                  SizedBox(width: 10.w),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.max,
                      children: [
                        //const Spacer(),
                        // 名称
                        Expanded(
                          child: Row(
                            children: [
                              badges.Badge(
                                position: badges.BadgePosition.topEnd(),
                                badgeStyle: badges.BadgeStyle(
                                  padding: const EdgeInsets.only(
                                      left: 5, top: 30, bottom: 0, right: 2)
                                      .r,
                                  badgeColor: AppColors.colorFFCD3A3A,
                                ),
                                badgeAnimation:
                                const badges.BadgeAnimation.fade(
                                  animationDuration: Duration(seconds: 1),
                                  toAnimate: true,
                                ),
                                badgeContent: const Text(""),
                                showBadge: !(data.read ?? true),
                                child: Container(
                                  alignment: Alignment.centerLeft,
                                  child: ConstrainedBox(
                                    constraints:
                                    BoxConstraints(maxWidth: 260.w),
                                    child: Text(
                                      name,
                                      overflow: TextOverflow.ellipsis,
                                      style: TextStyle(
                                          fontSize: 16.sp, color: Colors.black),
                                      softWrap: true,
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        const Visibility(
                          visible: true,
                          child: DividerCus(),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
