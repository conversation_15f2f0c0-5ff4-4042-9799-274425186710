import 'dart:async';

import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../core/utils/events_bus.dart';
import '../../../../core/values/config.dart';
import '../../../data/events/events.dart';
import '../../../data/models/res/staking_list_model_res.dart';
import '../../../data/providers/api/staking_api.dart';
import '../../../data/services/config_service.dart';
import 'staking_helper.dart';

class NodeStakingListController extends GetxController
    with GetTickerProviderStateMixin {
  late TabController tabController;
  RxList<StakingListData> stakingListAll = RxList<StakingListData>();
  RxList<StakingListData> stakingListPassed = RxList<StakingListData>();
  RxList<StakingListData> stakingListVerifying = RxList<StakingListData>();
  RxList<StakingListData> stakingListFailed = RxList<StakingListData>();

  RxInt loadType = LoadType.defaultState.obs;
  final List<StreamSubscription> _subscriptions = [];

  @override
  void onInit() {
    super.onInit();
    tabController = TabController(length: 4, vsync: this);
    StreamSubscription subscription;

    subscription =
        Get.find<EventBus>().on<UpdateStakingEvent>().listen((event) {
      getStakingList();
    });
    _subscriptions.add(subscription);
    getStakingList();
    updateWalletList();
  }

  @override
  void dispose() {
    for (var element in _subscriptions) {
      element.cancel();
    }
    _subscriptions.clear();
    super.dispose();
  }

  bool isLoading() {
    return loadType.value == LoadType.loading;
  }

  //"review_status": 1, //审核状态：1=授权服务，2=注册服务，3=绑定参数，4=已驳回，5=已完成
  getStakingList() async {
    var account = Get.find<AppConfigService>().readUserNameWithoutDomain();
    Map<String, dynamic> map = {'account': account};
    loadType.value = LoadType.loading;
    var res = await Get.find<StakingApi>().getStakingApplyList(map);
    if (res.data?.code == 200) {
      stakingListAll.clear();

      if (res.data?.data?.isNotEmpty ?? false) {
        stakingListAll.value = res.data!.data!;
        _updateAllListData();
      }
      loadType.value = LoadType.success;
      _saveMainWallet();
    } else {
      loadType.value = LoadType.error;
    }
    if (stakingListAll.isNotEmpty) {
      updateWalletNodeName(stakingListAll);
    }
    getKeyBoxState();
  }

  _updateAllListData() {
    stakingListPassed.clear();
    stakingListFailed.clear();
    stakingListVerifying.clear();
    for (var s in stakingListAll) {
      if (s.reviewStatus == 5) {
        stakingListPassed.add(s);
      } else if (s.reviewStatus == 4) {
        stakingListFailed.add(s);
      } else {
        stakingListVerifying.add(s);
      }
    }
  }

  _saveMainWallet() {
    var mainWallet = Get.find<AppConfigService>().readMainWallet();
    if ((mainWallet?.isEmpty ?? true) && stakingListAll.isNotEmpty) {
      var main = stakingListAll.first;
      if (main.serviceAddress?.isNotEmpty ?? false) {
        Get.find<AppConfigService>().saveMainWallet(main.mainAddress!);
      }
    }
  }

  bool isLoadError() {
    return loadType.value == LoadType.error;
  }

  getKeyBoxState() async {
    for (var data in stakingListAll) {
      if (data.reviewStatus == 5 && data.keyboxStatus == true) {
        var keyBoxres =
            await Get.find<StakingApi>().getKeyBoxInfo(data.serverDomain ?? '');
        if (keyBoxres.data != null) {
          // keyBoxres.data?.setActive(false);
          if (keyBoxres.data?.active != true) {
            data.setKeyboxStatus(false);
          }
        }
      }
    }
    _updateAllListData();
  }

  refreshData() {
    getStakingList();
  }
}
