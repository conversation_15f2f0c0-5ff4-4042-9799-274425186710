import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/modules/home/<USER>/home_appbar_oversea_view.dart';

import '../../../../core/values/config.dart';
import 'home_appbar_base.dart';
import 'home_appbar_meta_view.dart';
import 'home_appbar_view.dart';

Widget createAppBar({
  required String title,
  required SearchType type,
  Widget? body,
  Widget? titleWidget,
  bool? canSearch,
  bool? showTitle,
}) {
  return  HomeAppBarOverseaView(
    title: title,
    type: type,
    body: body,
    titleWidget: titleWidget,
    canSearch: canSearch,
    showTitle: showTitle,
  );
}
