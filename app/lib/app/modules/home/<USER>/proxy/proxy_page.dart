import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/modules/home/<USER>/proxy/proxy_detail_page.dart';
import 'package:flutter_metatel/core/languages/l.dart';
import 'package:flutter_metatel/core/values/colors.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import 'proxy_controller.dart';

class ProxyPage extends StatefulWidget {
  const ProxyPage({super.key});

  @override
  State<ProxyPage> createState() => _ProxyPageState();
}

class _ProxyPageState extends State<ProxyPage> {
  final _controller = Get.put(ProxyController());

  @override
  void dispose() {
    Get.delete<ProxyController>();
    super.dispose();
  }

  Widget _buildItem(int index) {
    var data = _controller.rxProxyDatas[index];
    return InkWell(
      onTap: () async{
       var back = await Get.to(ProxyDetailPage(
            type: ProxyType.edit,
            isNotRegister: _controller.isNotRegister.value,
            data: data));
        if (back != null) {
          _controller.addProxy(back);
        }
      },
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 16.w),
        height: 45.h,
        child: Row(
          children: [
            Expanded(
              child: Text(
                data.name,
                style: TextStyle(
                  fontSize: 16.sp,
                  color: AppColors.colorFF333333,
                ),
              ),
            ),
            CupertinoSwitch(
              value: data.enable ?? false,
              onChanged: (value) => _controller.onChanged(data, value),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: Text(L.proxy_settings.tr),
        centerTitle: true,
        actions: [
          Obx(() => Visibility(
              visible: _controller.showAdd(),
              child: Padding(
                padding: EdgeInsets.only(right: 5.w),
                child: TextButton(
                  onPressed: () async{
                    var back = await Get.to(ProxyDetailPage(
                      type: ProxyType.add,
                      isNotRegister: _controller.isNotRegister.value,
                    ));
                    if (back != null) {
                      _controller.addProxy(back);
                    }
                  },
                  child: Text(L.add.tr),
                ),
              ))),
        ],
      ),
      body: Obx(() {
        return ListView.builder(
          itemCount: _controller.rxProxyDatas.length,
          itemBuilder: (_, index) {
            return _buildItem(index);
          },
        );
      }),
    );
  }
}
