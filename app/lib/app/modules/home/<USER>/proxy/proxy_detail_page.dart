import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_metatel/app/data/providers/db/database.dart';
import 'package:flutter_metatel/app/modules/home/<USER>/proxy/proxy_controller.dart';
import 'package:flutter_metatel/core/languages/l.dart';
import 'package:flutter_metatel/core/utils/proxy_util.dart';
import 'package:flutter_metatel/core/utils/util.dart';
import 'package:flutter_metatel/core/values/colors.dart';
import 'package:flutter_metatel/r.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
typedef ItemOnTap = Function();
typedef ItemClick = Function(int);

enum ProxyType {
  add,
  edit,
}

class ProxyDetailPage extends StatefulWidget {
  const ProxyDetailPage({
    super.key,
    required this.type,
    required this.isNotRegister,
    this.data,
  });

  final ProxyType type;
  final ProxyInfoData? data;
  final bool isNotRegister;

  @override
  State<ProxyDetailPage> createState() => _ProxyDetailPageState();
}

class _ProxyDetailPageState extends State<ProxyDetailPage> {
  final _nameController = TextEditingController();
  final _hostController = TextEditingController();
  final _portController = TextEditingController();
  final _userController = TextEditingController();
  final _pwdController = TextEditingController();
  final valueVisible = false.obs;
  RxBool isHttp = true.obs;
  @override
  void initState() {
    super.initState();
    if (widget.type == ProxyType.edit) {
      _nameController.text = widget.data?.name ?? '';
      _hostController.text = widget.data?.host ?? '';
      _portController.text = widget.data?.port ?? '443';
      _userController.text = widget.data?.user ?? '';
      _pwdController.text = widget.data?.pwd ?? '';
      isHttp.value = widget.data?.isHttp ?? true;
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _hostController.dispose();
    _portController.dispose();
    _userController.dispose();
    _pwdController.dispose();
    super.dispose();
  }

  void _onConfirm() {
    String name = _nameController.text;
    String host = _hostController.text;
    String port = _portController.text;
    String user = _userController.text;
    String pwd = _pwdController.text;
    if (name.trim().isEmpty) {
      toast(L.proxy_name_empty.tr);
      return;
    }

    if (host.trim().isEmpty) {
      toast(L.proxy_host_empty.tr);
      return;
    }

    if (port.trim().isEmpty) {
      toast(L.proxy_port_empty.tr);
      return;
    }
    var proxyInfo = ProxyInfoCompanion.insert(
      uuid: widget.data?.uuid ?? uuid(),
      name: name,
      host: host,
      port: port,
      user: ofNullable(user),
      pwd: ofNullable(pwd),
      isHttp: ofNullable(isHttp.value),
      enable:ofNullable(widget.data?.enable),
      createTime: ofNullable(DateTime.now().millisecondsSinceEpoch.toDouble()),
      updateTime: ofNullable(DateTime.now().millisecondsSinceEpoch.toDouble()),
    );
    ProxyUtil.instance.addOrUpdate(
      proxyInfo,
      !widget.isNotRegister,
    );
    Get.back(
        result: widget.isNotRegister
            ? ProxyInfoData(
                id: 10,
                uuid: proxyInfo.uuid.value,
                name: proxyInfo.name.value,
                host: proxyInfo.host.value,
                port: proxyInfo.port.value,
                isHttp: proxyInfo.isHttp.value,
                pwd: proxyInfo.pwd.value)
            : null);
  }

  void _onDelete() {
    Get.back();
    Get.find<ProxyController>().deleteProxy(widget.data?.uuid ?? '');
  }

  Widget _buildItem(
    String text, {
    String? hintText,
    ValueChanged<String>? onChanged,
    List<TextInputFormatter>? inputFormatters,
    int? maxLength,
    TextEditingController? controller,
    TextInputType? keyboardType,
    bool? obscureText,
    Widget? suffixIcon,
  }) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          text,
          style: TextStyle(
            fontSize: 12.sp,
            color: AppColors.colorFF333333,
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 11.h),
        SizedBox(
          height: 54.h,
          child: TextField(
            obscureText: obscureText ?? false,
            controller: controller,
            onChanged: onChanged,
            inputFormatters: inputFormatters,
            keyboardType: keyboardType,
            maxLength: maxLength,
            decoration: InputDecoration(
              contentPadding: EdgeInsets.only(left: 11.w),
              hintText: hintText,
              hintStyle: TextStyle(
                fontSize: 14.sp,
                color: AppColors.colorFF999999,
              ),
              fillColor: AppColors.colorFFF7F7F7,
              filled: true,
              counterText: '',
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(6.r),
                borderSide: BorderSide.none,
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(6.r),
                borderSide: BorderSide.none,
              ),
              suffixIcon: suffixIcon,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildButton() {
    var buttonText =
        widget.type == ProxyType.add ? L.backup_confirm.tr : L.save.tr;

    Widget button1 = ElevatedButton(
      onPressed: _onConfirm,
      style: ElevatedButton.styleFrom(
        backgroundColor: AppColors.colorFF3776D6,
        minimumSize: Size(double.infinity, 44.h),
        shape:
            RoundedRectangleBorder(borderRadius: BorderRadius.circular(22.r)),
      ),
      child: Text(
        buttonText,
        style: TextStyle(
          fontSize: 16.sp,
          color: AppColors.colorFFF7F7F7,
        ),
      ),
    );

    Widget child;
    if (widget.type == ProxyType.add) {
      child = button1;
    } else {
      child = Row(
        children: [
          Expanded(
            child: ElevatedButton(
              onPressed: () {
                showDialog(
                  barrierDismissible: false,
                  context: context,
                  builder: (context) {
                    return AlertDialog(
                      title: Text(L.tips.tr),
                      content: Text(L.proxy_delete_tips.tr),
                      actions: [
                        TextButton(
                          onPressed: () => Get.back(),
                          child: Text(L.cancel.tr),
                        ),
                        TextButton(
                          onPressed: () {
                            Get.back();
                            _onDelete();
                          },
                          child: Text(L.backup_confirm.tr),
                        ),
                      ],
                    );
                  },
                );
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.colorFFF7F7F7,
                minimumSize: Size(double.infinity, 44.h),
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(22.r)),
              ),
              child: Text(
                L.other_del.tr,
                style: TextStyle(
                  fontSize: 16.sp,
                  color: AppColors.colorFF333333,
                ),
              ),
            ),
          ),
          SizedBox(width: 13.w),
          Expanded(child: button1),
        ],
      );
    }
    return child;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: Text(
            widget.type == ProxyType.add ? L.add_proxy.tr : L.proxy_detail.tr),
        centerTitle: true,
      ),
      body: ListView(
        padding: EdgeInsets.symmetric(horizontal: 16.w),
        children: [
          SizedBox(height: 10.h),
          _buildItem(
            L.proxy_name.tr,
            hintText: L.input_proxy_name.tr,
            controller: _nameController,
          ),
          SizedBox(height: 10.h),
          _buildItem(
            L.proxy_host.tr,
            hintText: L.input_proxy_host.tr,
            controller: _hostController,
            inputFormatters: [
              FilteringTextInputFormatter.allow(RegExp('[0-9|A-Z|a-z.]')),
            ],
          ),
          // SizedBox(height: 6.h),
          // proxyTypeView(
          //   L.protocol_type.tr,
          //   titleColor: AppColors.colorFF333333,
          //   rightColor: AppColors.colorFF666666,
          //   onTapCallBack: () {
          //     showBottomDialogCommonWithCancel(context, widgets: [
          //       _buildLanguageList(callBack: (index){
          //         isHttp.value = index == 0;
          //         Get.back();
          //
          //       })
          //     ]);
          //   },
          // ),
          SizedBox(height: 12.h),
          _buildItem(L.proxy_port.tr,
              hintText: L.input_proxy_port.tr,
              controller: _portController,
              inputFormatters: [
                FilteringTextInputFormatter.allow(RegExp('[0-9]')),
              ],
              keyboardType: TextInputType.number),
          SizedBox(height: 10.h),
          // _buildItem(
          //   L.user_name.tr,
          //   hintText: L.please_enter_user_name.tr,
          //   controller: _userController,
          // ),
          SizedBox(height: 10.h),
          // Obx(
          //   () => _buildItem(
          //     L.pwd.tr,
          //     hintText: L.please_enter_your_password.tr,
          //     controller: _pwdController,
          //     obscureText: !valueVisible.value,
          //     suffixIcon: IconButton(
          //       splashColor: Colors.transparent,
          //       highlightColor: Colors.transparent,
          //       onPressed: () {
          //         valueVisible.value = !valueVisible.value;
          //       },
          //       padding: EdgeInsets.zero,
          //       icon: Icon(
          //         valueVisible.value ? Icons.visibility : Icons.visibility_off,
          //         size: 20.r,
          //       ),
          //     ),
          //   ),
          // ),
          // SizedBox(height: 35.h),
          _buildButton(),
        ],
      ),
    );
  }
  Widget proxyTypeView(
      String title, {
        Widget? rightWidget,
        Color? rightColor,
        Color? titleColor,
        ItemOnTap? onTapCallBack,
        double? height,
      }) {
    return Material(
      color: AppColors.transparent,
      child: InkWell(
        onTap: () {
          if (onTapCallBack != null) {
            onTapCallBack();
          } else {
            toast(L.not_yet_develop.tr);
          }
        },
        child: SizedBox(
          height: height ?? 44.r,
          child: Row(
            children: [
              Text(
                title,
                style: TextStyle(
                  fontSize: 12.sp,
                  color: AppColors.colorFF333333,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Expanded(
                child: Container(
                  padding: const EdgeInsets.only(left: 15).r,
                  alignment: Alignment.centerRight,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      Obx(() => Text(
                        isHttp.value ? L.http.tr : L.socks5.tr,
                        style: TextStyle(fontSize: 12.sp, color: rightColor),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      )),
                      SizedBox(
                        width: 5.r,
                      ),
                      rightWidget ??
                          (R.nextArrowGrey.startsWith("assets")
                              ? Image.asset(
                            R.nextArrowGrey,
                            width: 6.w,
                            height: 10.h,
                          )
                              : Container())
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildLanguageList({
    ItemClick? callBack,
  }) {
    var agreement = [L.http.tr, L.socks5.tr];
    return Container(
      width: double.infinity,
      constraints: BoxConstraints(maxHeight: (agreement.length * 55).r),
      child: ListView.separated(
        padding: EdgeInsets.only(top: 0, bottom: 0, left: 0.r, right: 0.r),
        controller: ScrollController(),
        itemBuilder: (context, index) {
          var language = agreement[index];
          return _createLanguageView(index, language, callBack: callBack);
        },
        itemCount: agreement.length,
        separatorBuilder: (BuildContext context, int index) =>
            Divider(height: 0.5.h, color: AppColors.colorFFF8F8F8),
      ),
    );
  }

  Widget _createLanguageView(
      int index,
      String agreement, {
        ItemClick? callBack,
      }) {
     bool isChanged = isHttp.value && index == 0;

    return Material(
      color: Colors.transparent,
      child: InkWell(
        borderRadius: null,
        onTap: () {
          callBack?.call(index);
        },
        child: Container(
          height: 55.r,
          padding: const EdgeInsets.only(
            left: 16,
            right: 16,
          ).r,
          child: Row(
            children: [
              Text(
                agreement,
                style: TextStyle(
                  fontSize: 16.sp,
                  color: AppColors.colorFF040404,
                ),
              ),
              const Spacer(),
              Visibility(
                  visible: isChanged,
                  child: Image.asset(
                    R.contactorChecked,
                    width: 20.r,
                    height: 20.r,
                  )),
            ],
          ),
        ),
      ),
    );
  }
}
