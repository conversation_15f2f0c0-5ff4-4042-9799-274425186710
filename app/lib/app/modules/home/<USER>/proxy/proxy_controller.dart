import 'dart:async';

import 'package:flutter_metatel/app/data/providers/db/database.dart';
import 'package:flutter_metatel/core/utils/proxy_util.dart';
import 'package:flutter_metatel/core/utils/util.dart';
import 'package:get/get.dart';

class ProxyController extends GetxController {
  final rxProxyDatas = <ProxyInfoData>[].obs;
  StreamSubscription? _subscription;
  RxBool isNotRegister = false.obs;
  ProxyInfoData? _currEnableData;

  @override
  void onInit() {
    super.onInit();
    isNotRegister.value = Get.arguments['isNotRegister'];
    if (!isNotRegister.value) {
      _subscription =
          Get.find<AppDatabase>().allProxyInfo().watch().listen((event) {
            _currEnableData = null;
            rxProxyDatas.clear();
            rxProxyDatas.addAll(event);

            for (var element in event) {
              if (element.enable == true) {
                _currEnableData = element;
                break;
              }
            }
          });
    } else {
      var proxyInfo = ProxyUtil.instance.proxyInfoCompanion;
      if (proxyInfo != null) {
        var data = ProxyInfoData(
            id: 10,
            uuid: proxyInfo.uuid.value,
            name: proxyInfo.name.value,
            host: proxyInfo.host.value,
            port: proxyInfo.port.value,
            isHttp: proxyInfo.isHttp.value,
            enable: proxyInfo.enable.value,
            pwd: proxyInfo.pwd.value);
        addProxy(data);
      }
    }
  }

  bool showAdd(){
    return !isNotRegister.value || rxProxyDatas.isEmpty;
  }

  @override
  void onClose() {
    _subscription?.cancel();
    super.onClose();
  }


  void deleteProxy(String uuid) {
    if (isNotRegister.value) {
      rxProxyDatas.removeWhere((element) => element.uuid == uuid);
      ProxyUtil.instance.delete(uuid);
    } else {
      Get.find<AppDatabase>().deleteProxyInfo(uuid);
    }
  }

  void onChanged(ProxyInfoData data, bool value) {
    if (isNotRegister.value) {
      if (_currEnableData != null) {
        var index = rxProxyDatas.indexOf(data);
        _currEnableData = ProxyInfoData(
            id: 10,
            uuid: data.uuid,
            name: data.name,
            host: data.host,
            port: data.port,
            isHttp: data.isHttp,
            enable: value,
            pwd: data.pwd);
        ProxyUtil.instance.addOrUpdate(ProxyInfoCompanion.insert(
          uuid: _currEnableData?.uuid ?? uuid(),
          name: _currEnableData?.name ?? '',
          host: _currEnableData?.host ?? '',
          port: _currEnableData?.port ?? '',
          isHttp: ofNullable(_currEnableData?.isHttp ?? true),
          user: ofNullable(_currEnableData?.user),
          pwd: ofNullable(_currEnableData?.pwd),
          enable: ofNullable(_currEnableData?.enable),
          createTime: ofNullable(DateTime
              .now()
              .millisecondsSinceEpoch
              .toDouble()),
          updateTime: ofNullable(DateTime
              .now()
              .millisecondsSinceEpoch
              .toDouble()),
        ), false);
        if (index >= 0) {
          rxProxyDatas.remove(data);
          rxProxyDatas.insert(index, _currEnableData!);
        }
        ProxyUtil.instance.changeProxy(
          value ? data.host : null,
          value ? data.port : null,
          value ? data.user ?? "" : '',
          value ? data.pwd ?? "" : '',
          isHttp: value ? data.isHttp ?? true : true,
        );
      }
    } else {
      if (value && _currEnableData != null) {
        Get.find<AppDatabase>()
            .updateProxyInfoByUuid(false, _currEnableData!.uuid);
      }
      Get.find<AppDatabase>().updateProxyInfoByUuid(value, data.uuid);
    }
  }

  void addProxy(ProxyInfoData data) {
    var pro = rxProxyDatas.firstWhereOrNull((element) =>
    element.uuid == data.uuid);
    _currEnableData = data;
    if (pro == null) {
      rxProxyDatas.add(data);
    } else {
      rxProxyDatas.remove(pro);
      rxProxyDatas.add(data);
    }
  }
}
