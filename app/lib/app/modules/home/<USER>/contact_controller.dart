import 'dart:async';

import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:get/get.dart';

import '../../../data/providers/db/database.dart';
import 'comm_contact/comm_contact_view.dart';

class ContactController extends GetxController {
  StreamSubscription? subscription;

  void loadData() {
    var database = Get.find<AppDatabase>();
    subscription = database.allFriendContact().watch().listen((datas) {
      AppLogger.d('ContactController loadData=${datas.toString()}');
      mainContact.currentState?.loadData(datas);
    });
  }

  @override
  void onClose() {
    super.onClose();
    subscription?.cancel();
  }
}
