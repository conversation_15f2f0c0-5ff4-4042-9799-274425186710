import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/widgets/app_bar_cus.dart';
import 'package:flutter_metatel/core/languages/l.dart';
import 'package:flutter_metatel/core/values/colors.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../widgets/divider_cus.dart';
import 'home_appbar_base.dart';

class HomeAppBarOverseaView extends HomeAppBarBase {
  const HomeAppBarOverseaView({
    Key? key,
    required this.title,
    required SearchType type,
    this.body,
    this.titleWidget,
    bool? canSearch,
    bool? showTitle,
  }) : super(key: key, type: type,canSearch: canSearch,showTitle: showTitle);

  final String title;
  final Widget? body;
  final Widget? titleWidget;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: (showTitle??false)?AppBarCommon().build(context,title: title):null,
      backgroundColor: AppColors.white,
      body: Safe<PERSON>rea(
        child: Column(
          children: [
            Visibility(
                visible: canSearch ?? true,
                child: Container(
                  height: 49.h,
                  padding: EdgeInsets.only(left: 16.r, right: 10.r),
                  child: Row(
                    children: [
                      Expanded(
                        child: buildSearch(
                          height: 33.h,
                          hintText: type == SearchType.contacts
                              ? L.chat_contact_search_contact.tr
                              : type == SearchType.square? L.search_channel.tr : L.search_contact_msg.tr,
                          fillColor: AppColors.backgroundGray,
                        ),
                      ),
                      buildContact(),
                      buildMenu(),
                    ],
                  ),
                ),
            ),
            const DividerCus(thickness: 1,),
            Expanded(child: body ?? Container()),
          ],
        ),
      ),
    );
  }
}
