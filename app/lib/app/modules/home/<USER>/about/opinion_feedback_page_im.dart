import 'package:flutter/material.dart';
import 'package:flutter_metatel/core/languages/l.dart';
import 'package:flutter_metatel/core/values/colors.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../../core/utils/util.dart';

class OpinionFeedbackPageIm extends StatefulWidget {
  const OpinionFeedbackPageIm({Key? key}) : super(key: key);

  @override
  State<OpinionFeedbackPageIm> createState() => _OpinionFeedbackPageImState();
}

class _OpinionFeedbackPageImState extends State<OpinionFeedbackPageIm> {

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: Text(
          L.use_comments_or_feedback_suggestions.tr,
          style: TextStyle(
            fontSize: 16.sp,
            color: Colors.black,
          ),
        ),
        centerTitle: true,
      ),
      body:  Stack(children: [
        Positioned(
            top: 50.h,
            left: 30.w,
            right: 30.w,
          child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  L.opinion_feedback_tips.tr,
                  style: TextStyle(
                    fontSize: 15.sp,
                    color: AppColors.colorFF333333,
                  ),
                  textAlign: TextAlign.left,
                ),
                const SizedBox(height: 20,),
                GestureDetector(
                  onTap: () {
                    saveClipboard('<EMAIL>');
                    toast(L.copy_success.tr);
                  },
                  child:Row(children: [Text(
                    L.email.tr,
                    style: TextStyle(
                        fontSize: 18.sp),
                  ),
                    SizedBox(width: 5.w,),
                    Text(
                    '<EMAIL>',
                    style: TextStyle(
                        fontSize: 18.sp, decoration: TextDecoration.underline),
                  )],) ,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
