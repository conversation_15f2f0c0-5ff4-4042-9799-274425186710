import 'package:flutter/material.dart';
import 'package:flutter_metatel/core/languages/l.dart';
import 'package:flutter_metatel/core/utils/save_image_to_photo.dart';
import 'package:flutter_metatel/core/values/colors.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../widgets/divider_cus.dart';

class OpinionFeedbackPage extends StatefulWidget {
  const OpinionFeedbackPage({Key? key}) : super(key: key);

  @override
  State<OpinionFeedbackPage> createState() => _OpinionFeedbackPageState();
}

class _OpinionFeedbackPageState extends State<OpinionFeedbackPage> {
  final GlobalKey _qrKey = GlobalKey();

  void _saveQr() async {
    var imageBytes = await RepaintBoundaryUtils.savePhotoByGlobalKey(_qrKey);
    RepaintBoundaryUtils.savePhoto(imageBytes,
        toastSuccess: L.save_success.tr, toastError: L.save_failed.tr);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: Text(
          L.use_comments_or_feedback_suggestions.tr,
          style: TextStyle(
            fontSize: 16.sp,
            color: Colors.black,
          ),
        ),
        centerTitle: true,
      ),
      body: Column(
        children: [
          const DividerCus(thickness: 1,),
          SizedBox(height: 63.h),
          // 提示语
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 45.w),
            child: Text(
              L.opinion_feedback_tips.tr,
              style: TextStyle(
                fontSize: 14.sp,
                color: AppColors.colorFF333333,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          SizedBox(height: 10.h),
          // 二维码
          RepaintBoundary(
            key: _qrKey,
            child: Image.asset(
              'assets/images/wechat_qr.png',
              width: 183.w,
              height: 183.h,
            ),
          ),
          const Spacer(flex: 2),
          // 按钮
          ElevatedButton(
              onPressed: _saveQr,
              child: Text(
                L.save_qr.tr,
                style: TextStyle(
                  fontSize: 16.sp,
                  color: Colors.white,
                ),
              ),
            ),
          const Spacer(flex: 1),
        ],
      ),
    );
  }
}
