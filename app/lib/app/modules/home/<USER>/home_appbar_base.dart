import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_metatel/app/data/models/channel_info_model_data.dart';
import 'package:flutter_metatel/app/modules/home/<USER>/contact_view.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_web3/app/core/utils/util.dart';
import 'package:flutter_web3/app/data/models/token_model.dart';
import 'package:flutter_web3/app/modules/wallet/ethereum/wallet_controller.dart';
import 'package:get/get.dart';

import '../../../../core/utils/jump.dart';
import '../../../../core/values/channel.dart';
import '../../../../core/values/code.dart';
import '../../../../r.dart';
import '../../../data/enums/enum.dart';
import '../../../data/models/user_message_model.dart';
import '../../../data/providers/api/channel.dart';
import '../../../widgets/popupmutu_item.dart' as p;
import '../../../../core/languages/l.dart';
import '../../../../core/values/config.dart';
import '../../../../core/values/colors.dart';
import '../../../../routes/pages.dart';
import '../../daoCommunity/tokenSelection/token_selection_controller.dart';
import '../../daoCommunity/tokenSelection/token_selection_view.dart';
import '../../daoCommunity/widgets/dao_exist_btm_sheet.dart';

enum Options { createGroup, createWeb3Dao , createPrivateGroup, addFriend, scan,createMeeting,aiTid }

enum SearchType { sessions, contacts, square }

class HomeAppBarBase extends StatelessWidget {
  const HomeAppBarBase({
    Key? key,
    required this.type,
    this.canSearch,
    this.showTitle,
  }) : super(key: key);

  final SearchType type;
  final bool? canSearch;
  final bool? showTitle;

  /// 菜单选中项
  void _onMenuItemSelected(int value) async {
    Options options = Options.values[value];
    switch (options) {
      case Options.createGroup:
        Get.toNamed(Routes.CREATE_GROUP, arguments: {'isPrivateGroup': false});
        break;      
      case Options.createPrivateGroup:
        Get.toNamed(Routes.CREATE_GROUP, arguments: {'isPrivateGroup': true});
        break;
      case Options.createWeb3Dao:
        _onMenuCreateDaoTap();        
        break;
      case Options.addFriend:
        Get.toNamed(Routes.ADDFRIEND);
        break;
      case Options.scan:
        JumpPage.toScan();
        break;
      case Options.createMeeting:
        // JumpPage.toMeeting();
        Get.toNamed(Routes.MeetingView);
        break;
      case Options.aiTid:
        JumpPage.aiHosting();
        break;
      default:
    }
  }

  Widget buildSearchIcon() {
    return IconButton(
      onPressed: () {
        int searchType = SearchResultType.all;
        if (type == SearchType.sessions) {
          searchType = SearchResultType.chat |
              SearchResultType.function |
              SearchResultType.group |
              SearchResultType.contactor;
        } else if (type == SearchType.contacts) {
          searchType = SearchResultType.contactor;
        }
        Get.toNamed(Routes.SEARCH, arguments: {'type': searchType});
      },
      icon: Image.asset(
        R.icoSearch,
        width: 19.r,
        height: 19.r,
      ),
    );
  }

  Widget buildSearch({
    required double height,
    String? hintText,
    Color? fillColor,
  }) {
    return SizedBox(
      height: height,
      child: TextField(
        onTap: () {
          if (type == SearchType.square) {
            Get.toNamed(Routes.SearchSquare);
          } else {
            int searchType = SearchResultType.all;
            if (type == SearchType.sessions) {
              searchType = SearchResultType.chat |
                  SearchResultType.function |
                  SearchResultType.group |
                  SearchResultType.contactor;
            } else if (type == SearchType.contacts) {
              searchType = SearchResultType.contactor;
            }
            Get.toNamed(Routes.SEARCH, arguments: {'type': searchType});
          }
        },
        readOnly: true,
        textAlign: TextAlign.start,
        textAlignVertical: TextAlignVertical.center,
        decoration: InputDecoration(
          hintText: hintText,
          hintStyle: TextStyle(color: AppColors.colorFF7F7F7F,fontSize: 12.sp,fontWeight: FontWeight.w400),
          // 设置后，提升文本居中
          contentPadding: EdgeInsets.zero,
          prefixIcon: Icon(
            Icons.search,
            size:17.r,
            color: Color.fromARGB(255, 89, 90, 90),
          ),
          filled: true,
          fillColor: fillColor,
          border: const OutlineInputBorder(
            borderSide: BorderSide.none,
            borderRadius: BorderRadius.all(Radius.circular(25)),
          ),
        ),
      ),
    );
  }

  Widget buildMenu() {
    return p.KYPopupMenuButton(
      icon: Image.asset(
        R.icoDaoAdd,
        width: 19.r,
        height: 19.r,
      ),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      // 在按钮下方显示
      offset: const Offset(0, 50),
      itemBuilder: (context) {
        return  <p.KYPopupMenuItem>[
                _buildPopupMenuItem(
                  L.create_group_txt.tr,
                  'assets/images/create_group.png',
                  Options.createGroup.index,
                  hint: L.up_to_people.trParams(
                    {'number': Channel.maxMemberLimitChannel},
                  ),
                ),
                _buildPopupMenuItem(
                  L.create_private_group_txt.tr,
                  'assets/images/private_group.png',
                  Options.createPrivateGroup.index,
                  hint: L.up_to_people.trParams(
                    {'number': Channel.maxMemberLimitGroup},
                  ),
                ),
                _buildPopupMenuItem(
                  L.create_web3_dao.tr,
                  R.createWeb3Dao,
                  Options.createWeb3Dao.index,
                ),
                if(Config.meetingUrl.isNotEmpty)
                _buildPopupMenuItem(
                  L.create_meeting.tr,
                  'assets/images/create_meeting.png',
                  Options.createMeeting.index,
                ),
                // _buildPopupMenuItem(
                //   L.ai_hosting.tr,
                //   'assets/images/ai_tid.png',
                //   Options.aiTid.index,
                // ),
                _buildPopupMenuItem(
                  L.add_buddy.tr,
                  'assets/images/add_friend.png',
                  Options.addFriend.index,
                ),
                _buildPopupMenuItem(L.main_scan.tr, 'assets/images/scan.png',
                    Options.scan.index),
              ];
      },
      onSelected: (value) {
        _onMenuItemSelected(value as int);
      },
    );
  }

  Widget buildContact() {
    return GestureDetector(
      onTap: () {
        Get.to(const ContactView());
      },
      child: Container(
        padding: const EdgeInsets.only(left: 10, top: 10, bottom: 10).r,
        child: Image.asset(
          R.iconSearchRightContact,
          width: 19.r,
          height: 19.r,
        ),
      ),
    );
  }

  /// 构建弹出菜单Item
  p.KYPopupMenuItem _buildPopupMenuItem(
      String title, String imageName, int position,
      {String? hint}) {
    return p.KYPopupMenuItem(
      value: position,
      height: 42.r,
      child: Row(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment:CrossAxisAlignment.start,
      children: [
        Image.asset(imageName, width: 20.r, height: 20.r),
        SizedBox(width: 8.w),
        Padding(
          padding: const EdgeInsets.only(right: 20).r,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: TextStyle(
                    fontSize: 14.sp, color: AppColors.colorFF000000,fontWeight: FontWeight.w400),
              ),
              if (hint != null)
                Text(
                  hint,
                  style:
                  TextStyle(fontSize: 13.sp, color: AppColors.colorFF7F7F7F),
                ),
            ],
          ),
        ),
      ],
            ),
    );
  }

  void _onMenuCreateDaoTap() async {
    try {
      /// 代币token选择
      Get.put(TokenSelectionController());
      var result = await showModalBottomSheetUtil(
        widget: TokenSelectionView(),
        context: Get.context!,
      );
      Get.delete<TokenSelectionController>(force: true);
      if (result == null) return;
          
      EasyLoading.show(maskType: EasyLoadingMaskType.black);

      /// 通过token查询该Dao群是否已创建
      ChannelInfoModelData channelInfoData = await getDaoInfoByToken(
        (result as TokenDataModel).address,
        Get.find<WalletController>().netModel.value.chainid,
      );
      /// 该Dao群未被创建
      if(channelInfoData.code == 404) {
        EasyLoading.dismiss();
        /// 跳转创建Dao
        Get.toNamed(Routes.CreateDaoView, arguments: {'daoToken': result});
        return;
      }

      /// 该Dao群已创建
      if(channelInfoData.code == Code.code200) {        
        /// 查询用户是否已为该Dao成员
        var modelData = await getChannelInfoRequest(channelInfoData.id ?? '');
        EasyLoading.dismiss();        
        if(modelData.code == Code.code200) {
          await showModalBottomSheetUtil(
            widget: DaoExistBtmSheet(daoToken: result),
            context: Get.context!,
          );
          /// 已经是该Dao群成员, 跳转聊天界面
          var userMessage = UserMessage(
            chatType: ChatType.channelChat.index,
            displayName: modelData.title,
            userName: modelData.id,
            avatarPath: modelData.avatar,
            tokenAddress: modelData.tokenAddress,
          );
          Get.toNamed(Routes.MESSAGE, arguments: userMessage);
        } else {
          /// 该Dao群已创建，用户未加入
          /// 跳转加入页面
          Get.toNamed(
            Routes.ChannelJoin,
            arguments: {
              "channelId": channelInfoData.id,
            },
          );
        }
      } else {
        /// 其他情况
        toast("code : ${channelInfoData.code}", textColor: Colors.red);
        EasyLoading.dismiss();
      }
    } catch (e) {
      EasyLoading.dismiss();
      AppLogger.e("_onMenuCreateDaoTap $e");
    }
    
  }

  @override
  Widget build(BuildContext context) {
    return Container();
  }
}
