/*
 * @Author: your name
 * @Date: 2022-04-26 16:33:52
 * @LastEditTime: 2022-05-07 22:20:26
 * @LastEditors: luo<PERSON> <EMAIL>
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath     : /flutter_metatel/lib/app/modules/home/<USER>/session_list_item.dart
 */

import 'package:extended_text/extended_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/data/enums/enum.dart';
import 'package:flutter_metatel/app/data/providers/db/database.dart';
import 'package:flutter_metatel/app/modules/home/<USER>/session_controller.dart';
import 'package:flutter_metatel/core/languages/l.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:flutter_metatel/core/utils/language_util.dart';
import 'package:flutter_metatel/core/utils/util.dart';
import 'package:flutter_metatel/core/values/colors.dart';
import 'package:flutter_metatel/core/values/config.dart';
import 'package:flutter_metatel/r.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:flutter_web3/app/db/database.dart';
import 'package:flutter_web3/app/modules/wallet/ethereum/wallet_controller.dart';
import 'package:get/get.dart';

import '../../../widgets/at_widget/my_special_text_span_builder.dart';
import '../../../widgets/divider_cus.dart';

typedef ValueChangedCallback = void Function(String username, bool value);

class SessionListItem extends StatelessWidget {
  SessionListItem({
    Key? key,
    required this.sessionData,
    required this.backColor,
    this.onTopChanged,
    this.onReadChanged,
    this.onDelete,
    this.onPressed,
    this.draft,
  }) : super(key: key);

  final SessionData sessionData;

  /// 置顶变化
  final ValueChangedCallback? onTopChanged;

  /// 已读变化
  final ValueChangedCallback? onReadChanged;

  /// 删除
  final ValueChanged<SessionData>? onDelete;

  /// 点击
  final ValueChanged<SessionData>? onPressed;

  /// 草稿
  final String? draft;
  Rx<Color> backColor = Colors.white.obs;

  Size calculateTextSize({
    required String text,
    required TextStyle style,
    required BuildContext context,
  }) {
    final double textScaleFactor = MediaQuery.of(context).textScaleFactor;
    final TextDirection textDirection = Directionality.of(context);

    final TextPainter textPainter = TextPainter(
      locale: LanguageUtil.getCatheLanguageLocale(),
      maxLines: 1,
      text: TextSpan(text: text, style: style),
      textDirection: textDirection,
      textScaleFactor: textScaleFactor,
    )..layout(minWidth: 0, maxWidth: double.infinity);

    return textPainter.size;
  }

  void _onLongPressShowMenu(BuildContext context) {
    bool isTop = sessionData.top ?? false;
    int unreadCount = sessionData.unreadCount ?? 0;
    bool isRead = unreadCount > 0 ? false : true;

    String topText = isTop ? L.other_cancel_stick.tr : L.other_stick.tr;
    String readText =
        isRead ? L.other_reset_unread.tr : L.other_reset_has_read.tr;
    String delText = L.other_del_chat.tr;

    /// 计算最长文字宽高
    String maxText = topText.length > readText.length ? topText : readText;
    maxText = maxText.length > delText.length ? maxText : delText;
    bool isOffical = false; //sessionData.chatType==ChatType.officialChat.index;
    var textStyle = TextStyle(fontSize: 16.sp);
    var maxTextSize =
        calculateTextSize(text: maxText, style: textStyle, context: context);

    double maxWidth = maxTextSize.width + 40.0;
    maxWidth = maxWidth > 130.0 ? maxWidth : 130.0;

    var size =
        Size(maxWidth, (kMinInteractiveDimension * (isOffical ? 2 : 3) + 20));
    var position = Get.find<SessionController>().getMenuPosition(size);

    showMenu(
      context: context,
      position: position,
      color: Colors.white,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      constraints: BoxConstraints(
          minWidth: size.width,
          maxWidth: size.width,
          maxHeight: size.height,
          minHeight: size.height),
      items: <PopupMenuEntry>[
        PopupMenuItem(
          child: Text(topText, style: textStyle),
          onTap: () => onTopChanged?.call(sessionData.username, !isTop),
        ),
        PopupMenuItem(
          child: Text(readText, style: textStyle),
          onTap: () => onReadChanged?.call(sessionData.username, !isRead),
        ),
        if (!isOffical)
          PopupMenuItem(
            child: Text(delText, style: textStyle),
            onTap: () => onDelete?.call(sessionData),
          ),
      ],
    );
  }

  /// 构建滑块按钮
  List<Widget> _buildSlidableActions() {
    bool isTop = sessionData.top ?? false;

    int unreadCount = sessionData.unreadCount ?? 0;
    bool isRead = unreadCount > 0 ? false : true;

    String topImage = isTop
        ? 'assets/images/session_cancelTop.png'
        : 'assets/images/session_top.png';

    String readImage = isRead
        ? 'assets/images/session_unread.png'
        : 'assets/images/session_read.png';

    String deleteImage = 'assets/images/session_delete.png';

    if (!currentLanguageIsSimpleChinese()) {
      topImage = isTop
          ? 'assets/images/ic_left_untop_en.png'
          : 'assets/images/ic_left_top_en.png';

      readImage = isRead
          ? 'assets/images/ic_left_unread_en.png'
          : 'assets/images/ic_left_read_en.png';

      deleteImage = 'assets/images/ic_left_delete_en.png';
    }

    var widgets = <Widget>[
      _buildAction(topImage, const Color.fromARGB(255, 179, 179, 179),
          (context) {
        Slidable.of(context)?.close();
        onTopChanged?.call(sessionData.username, !isTop);
      }),
      _buildAction(readImage, const Color.fromARGB(255, 89, 128, 179),
          (context) {
        Slidable.of(context)?.close();
        onReadChanged?.call(sessionData.username, !isRead);
      }),
      _buildAction(deleteImage, const Color.fromARGB(255, 204, 41, 41),
          (context) {
        Slidable.of(context)?.close();
        onDelete?.call(sessionData);
      }),
    ];

    return widgets;
  }

  Widget _buildAction(
      String imageName, Color color, SlidableActionCallback onPressed) {
    return Builder(
      builder: (context) {
        return Expanded(
          child: GestureDetector(
            onTap: () => onPressed.call(context),
            child: Container(
              alignment: Alignment.center,
              color: color,
              child: Image.asset(imageName),
            ),
          ),
        );
      },
    );
  }

  Widget _body() {
    String text = sessionData.body ?? '';
    String redText = '';
    if (sessionData.type == MessageType.stickerDefault.index) {
      if (currentLanguageIsSimpleChinese()) {
        text = StickerDefault.mapNameCn[sessionData.body] ?? "";
      } else {
        text = StickerDefault.mapNameEn[sessionData.body] ?? "";
      }
    } else if (sessionData.type == MessageType.stickerDefaultRabbit.index) {
      if (currentLanguageIsSimpleChinese()) {
        text = StickerDefault.mapNameRabbitCn[sessionData.body] ?? "";
      } else {
        text = StickerDefault.mapNameRabbitEn[sessionData.body] ?? "";
      }
    } else if (sessionData.type == MessageType.stickerDefaultEmoji.index) {
      if (currentLanguageIsSimpleChinese()) {
        text = StickerDefault.mapNameEmojiRabbitCn[sessionData.body] ?? "";
      } else {
        text = StickerDefault.mapNameEmojiRabbitEn[sessionData.body] ?? "";
      }
    }

    if (sessionData.at == true) {
      redText = L.you_where_mentioned.tr;
    } else if (draft?.isNotEmpty == true) {
      redText = L.draft.tr;
      text = draft!;
    }
    if (sessionData.type == MessageType.msgMergeForward.index) {
      text = L.other_chat_history.tr;
    }
    if (sessionData.type == MessageType.moneyExchange.index) {
      text = "[${L.currency_exchange.tr}]";
    }
    if (sessionData.type == MessageType.walletTransaction.index) {
      text = "[${L.transfer_Notification.tr}]";
    }
    if (sessionData.type == MessageType.walletBill.index) {
      text = "[${L.bill.tr}]";
    }

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Visibility(
          visible: redText.isNotEmpty,
          child: Text(
            redText,
            style: TextStyle(
              color: Colors.red,
              fontSize: 13.sp,
            ),
          ),
        ),
        Flexible(
          child: ExtendedText(
            text,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            style: TextStyle(
              color: AppColors.colorFFB2B2B2,
              fontSize: 13.sp,
            ),
            specialTextSpanBuilder: MySpecialTextSpanBuilder(
              showAtBackground: false,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildGroupIcon() {
    var controller = Get.find<SessionController>();
    bool visible = false;
    Widget? child;

    if (sessionData.chatType == ChatType.groupChat.index ||
        sessionData.chatType == ChatType.channelChat.index) {
      visible = true;

      if (sessionData.chatType == ChatType.channelChat.index) {
        if (ChannelAttribute.dao ==
            controller.channelAttribute(sessionData.username)) {
          String? chainId = controller.daoChannelChainId("${sessionData.username}");
          var chainName;
          if (chainId != null && chainId.isNotEmpty) {
            var _chainId = int.tryParse(chainId);
            if(_chainId != null && Get.isRegistered<WalletController>()) {
              NetworkInfoData? network;
              try {
                network = Get.find<WalletController>().allListNet.firstWhereOrNull((network) => network.chainid == _chainId);
                if(network != null){
                  chainName = network.name;
                }
              } catch (e) {
                AppLogger.e("_buildGroupIcon Get.find<WalletController>() error: $e");
              }            
            }
          }
          child = chainName != null
            ? Text(
                "[$chainName]",
                style: TextStyle(
                  color: AppColors.colorFFB3B3B3,
                  fontWeight: FontWeight.bold,
                  fontSize: 9.sp,
                ),
              )
            : SizedBox.shrink();          
          // Image.asset(
          //   R.icoDao,
          //   width: 18,
          //   height: 14,
          // );
        }
      } else {
        child = Image.asset(
          R.icoPrivate,
          width: 18,
          height: 14,
        );
      }
    }

    return Visibility(
      visible: visible,
      child: child ?? Container(),
    );
  }

  Color defaultBgColor() {
    return sessionData.top ?? false ? const Color(0xfff5f7fa) : Colors.white;
  }

  @override
  Widget build(BuildContext context) {
    bool isTop = sessionData.top ?? false;
    bool isDisturb = sessionData.silence ?? false;
    int unreadCount = sessionData.unreadCount ?? 0;
    int direction = sessionData.direction ?? 0; //0-右边 1-左边
    bool isDraft = draft?.isEmpty ?? true;

    int msgTime = sessionData.time ?? 0;
    if (msgTime <= 0 && sessionData.createTime != null) {
      msgTime = sessionData.createTime!.toInt();
    }
    // double size = 16;
    String unReadText = '$unreadCount';
    if (unreadCount > 999) {
      unReadText = '999+';
      // size = 20;
    }
    // if (sessionData.chatType == ChatType.channelChat.index && sessionData.at != true) {
    //   size = 8;
    // }

    int state = sessionData.state ?? 0;
    String stateImage = '';
    switch (state) {
      case MessageStatus.succesfull: // 发送成功
        stateImage = 'assets/images/unread.png';
        break;
      case MessageStatus.error: // 发送失败
        stateImage = 'assets/images/sendFail.png';
        break;
      case MessageStatus.accepted: // 对方收到
        stateImage = 'assets/images/read.png';
        break;
      default:
        break;
    }

    String textName = sessionData.displayname ?? '';
    if (sessionData.chatType == ChatType.singleChat.index && textName.isEmpty) {
      textName = sessionData.username.substring(6);
    }
    if (sessionData.chatType == ChatType.officialChat.index) {
      textName = getOfficeDisplayName();
    } else if (sessionData.chatType == ChatType.singleChat.index) {
      textName = getDisplayNameOffTid(textName, isTid: sessionData.isTid,user: sessionData.username);
    }
    var path = sessionData.avatarPath;
    if ((path?.isNotEmpty ?? false) && !(path!.startsWith('/Avatar/'))) {
      if (path.contains('/Avatar/')) {
        path = path.substring(path.indexOf('/Avatar/'));
      }
    }
    bool isSlide = Config.isOversea;
    return Slidable(
      key: ValueKey(sessionData.id),
      groupTag: 'sessionGroup',
      // 滑块按钮部分
      endActionPane: isSlide
          ? ActionPane(
              extentRatio: 0.6,
              motion: const ScrollMotion(),
              children: _buildSlidableActions(),
            )
          : null,
      child: Obx(() {
        return Material(
          color: backColor.value,
          child: InkWell(
            radius: 1,
            onTap: () {
              ItemClickFunction(startFunction: () {
                backColor.value = AppColors.colorFFF2F2F2;
              }, endFunction: () {
                onPressed?.call(sessionData);
                backColor.value = defaultBgColor();
              });
            },
            onTapDown: (details) {
              Get.find<SessionController>().itemDetails = details;
            },
            onLongPress: isSlide ? null : () => _onLongPressShowMenu(context),
            child: Container(
              height: itemHeight.h,
              padding: EdgeInsets.only(left: 16.h),
              child: Row(
                children: [
                  buildChatAvatarWithAttr(
                    sessionData.chatType ?? ChatType.singleChat.index,
                    sessionData.username,
                    text: textName,
                    imagePath: sessionData.username == Config.fileHelperOwner
                        ? path
                        : appSupporAbsolutePath(path),
                    textStyle: const TextStyle(
                      color: Colors.white,
                      fontSize: 22,
                      fontWeight: FontWeight.normal,
                    ),
                  ),
                  // 间隔
                  const SizedBox(width: 10.0),
                  Expanded(
                    child: Column(
                      children: [
                        Expanded(
                          child: Padding(
                            padding: EdgeInsets.only(right: 16.h),
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                // 名称/时间行
                                Row(
                                  children: [
                                    // 名称
                                    Expanded(
                                      child: Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          ConstrainedBox(
                                            constraints: const BoxConstraints(
                                                    maxWidth: 180)
                                                .r,
                                            child: ExtendedText(
                                              textName,
                                              overflow: TextOverflow.ellipsis,
                                              style: TextStyle(
                                                color: Colors.black,
                                                fontWeight: FontWeight.bold,
                                                fontSize: 14.sp,
                                              ),
                                              specialTextSpanBuilder:
                                                  MySpecialTextSpanBuilder(
                                                      showAtBackground: false,
                                                      size: Size(15.r, 15.r)),
                                            ),
                                          ),
                                          const SizedBox(width: 5),
                                          _buildGroupIcon(),
                                        ],
                                      ),
                                    ),
                                    // 时间
                                    Visibility(
                                        visible: !isDisturb,
                                        child: Text(
                                          msgTimeFormat(msgTime),
                                          style: TextStyle(
                                            color: AppColors.colorFFB2B2B2,
                                            fontSize: 11.sp,
                                          ),
                                        )),
                                  ],
                                ),
                                // 间隔
                                const SizedBox(height: 2),
                                // 消息内容行
                                Row(
                                  children: [
                                    // 状态图标
                                    Visibility(
                                      visible: (direction == 1 &&
                                              stateImage.isNotEmpty &&
                                              isDraft)
                                          ? true
                                          : false,
                                      child: Image.asset(
                                        stateImage,
                                        width: 15,
                                        height: 15,
                                      ),
                                    ),
                                    // 消息内容
                                    Expanded(
                                      child: _body(),
                                    ),
                                    //免打扰
                                    Visibility(
                                      visible: isDisturb,
                                      child: Image.asset(
                                        'assets/images/ic_disturb.png',
                                        width: 16.w,
                                        height: 16.h,
                                      ),
                                    ),
                                    // 置顶图标
                                    Visibility(
                                      visible: isTop,
                                      child: Container(
                                        padding: EdgeInsets.only(
                                            left: isDisturb ? 9 : 0),
                                        child: Image.asset(
                                          R.icoTop,
                                          width: 16.w,
                                          height: 16.h,
                                        ),
                                      ),
                                    ),
                                    // 未读条数图标
                                    Visibility(
                                      visible: unreadCount > 0 ? true : false,
                                      child: Padding(
                                        padding: EdgeInsets.only(
                                            left: isTop || isDisturb ? 9.w : 0),
                                        child: Container(
                                          height: 16.r,
                                          padding: EdgeInsets.symmetric(
                                            horizontal: 6.r,
                                          ),
                                          decoration: BoxDecoration(
                                            borderRadius: BorderRadius.circular(8.r),
                                            color: AppColors.colorFFCD3A3A,
                                          ),
                                          child: Center(
                                            child: Text(
                                              unReadText,
                                              style: const TextStyle(
                                                color: Colors.white,
                                                fontSize: 10,
                                              ),
                                            ),
                                          ),
                                        ),
                                        // SizedBox(
                                        //   width: 8,
                                        //   height: 8,
                                        //   child: CircleAvatar(
                                        //     backgroundColor:
                                        //         AppColors.colorFFCD3A3A,
                                        //     child: sessionData.chatType ==
                                        //             ChatType.channelChat.index && sessionData.at != true
                                        //         ? null
                                        //         : Text(
                                        //             unReadText,
                                        //             style: const TextStyle(
                                        //               color: Colors.white,
                                        //               fontSize: 10,
                                        //             ),
                                        //           ),
                                        //   ),
                                        // ),
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ),
                        const DividerCus(
                          thickness: 0.5,
                          color: Color.fromARGB(255, 218, 218, 218),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      }),
    );
  }
}
