import 'dart:developer';
import 'dart:ffi';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/widgets/app_bar_cus.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';

import '../../../../../core/languages/l.dart';
import '../../../../../core/values/colors.dart';
import '../../../../../r.dart';
import '../../../../data/models/node_model.dart';
import 'node_switching_controller.dart';

class NodeSwitchingView extends StatefulWidget {
  NodeSwitchingView({Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() => _NodeSwitchingView();
}

class _NodeSwitchingView extends State<NodeSwitchingView> {
  NodeSwitchingController controller =
      Get.put(NodeSwitchingController(), permanent: false);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        centerTitle: true,
        title: Text(
          L.node_switching.tr, 
          style: TextStyle(
            color: AppColors.white,
            fontSize: 15.sp,
          ),
        ),
        leading: const BackButton(color: AppColors.white),
      ),
      // AppBarCommon().build(
      //   context,
      //   title: L.node_switching.tr,
      // ),
      body: Stack(
        children: [
          /// 背景部分
          Container(
            height: 1.sh,
            width: 1.sw,
            child: Column(
              children: [
                /// 颜色头部分
                Container(
                  height: 0.28.sh,
                  decoration: BoxDecoration(
                     gradient: LinearGradient(
                        colors: [
                          AppColors.primaryBgColor2,
                          AppColors.primaryBgColor1,                                                                                            
                        ],                              
                        begin: Alignment(-1.5,0),
                        end: Alignment(1,0),      
                      ),
                  ),
                ),
                /// 白色部分
                Expanded(
                  child: Container(
                    color: AppColors.white,
                  ),
                ),
              ],
            ),
          ),
          /// 内容部分
          Container(
            height: 1.sh,
            width: 1.sw,
            child: Column(
              children: [
                /// 头部
                Container(
                  height: 0.28.sh,
                  width: 1.sw,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SizedBox(height: kToolbarHeight,),
                      Text(
                        L.current_node.tr,
                        style: TextStyle(
                          fontSize: 12.sp,
                          color: AppColors.white,
                        ),
                      ),
                      SizedBox(height: 4.r),
                      Obx(
                        () => Text(
                          controller.netWorkNode.value,
                          style: TextStyle(
                            fontSize: 22.sp,
                            fontWeight: FontWeight.w500,
                            color: AppColors.white,
                          ),
                        ),
                      ),
                      // SizedBox(height: 35.r),
                    ],
                  ),
                ),
                /// 白色部分
                Expanded(
                  child: SizedBox(
                    width: 1.sw,
                    child: SingleChildScrollView(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          SizedBox(height: 30.r),
                          Padding(
                            padding: EdgeInsets.symmetric(horizontal: 26.r),
                            child: Text(
                              L.available_node.tr,
                              style: TextStyle(
                                color: Colors.black,
                                fontSize: 15.sp,
                              ),
                            ),
                          ),
                          SizedBox(height: 8.r),
                          Divider(),
                          SizedBox(height: 14.r),
                          Obx(() {
                            int count = controller.nodeList.value.length;
                            // double maxHeight = (count * 55);
                            // if (maxHeight == 0) {
                            //   maxHeight = 60;
                            // }
                            // if (maxHeight > 300) {
                            //   maxHeight = 300;
                            // }
                            return GetBuilder<NodeSwitchingController>(
                              id: controller.nodeKeyId,
                                builder: (contr) {
                              return Container(
                                  // constraints:
                                  //     BoxConstraints(maxHeight: maxHeight.r),
                                  child: count == 0
                                      ? SpinKitFadingCircle(
                                          color: Colors.black,
                                          size: 20.sp,
                                        )
                                      : ListView.separated(
                                          physics:
                                              const NeverScrollableScrollPhysics(),
                                          itemCount: count,
                                          padding: EdgeInsets.zero,
                                          shrinkWrap: true,
                                          itemBuilder: (context, index) {
                                            var node =
                                                controller.nodeList.value[index];
                                            return GetBuilder<
                                                NodeSwitchingController>(
                                              id: node.node,
                                              builder: (control) {
                                                return nodeView(node);
                                              },
                                            );
                                          },
                                          separatorBuilder: (context, index) {
                                            var padding = EdgeInsets.only(
                                                top: 10.r, right: 0.r);
                                            return Container(
                                              padding: padding,
                                            );
                                          },
                                        ));
                            });
                          }),
                          SizedBox(height: 30.r),
                          /// Notice
                          Padding(
                            padding: EdgeInsets.symmetric(horizontal: 30.r),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  "${L.notice.tr}:",
                                  style: TextStyle(
                                    color: AppColors.colorFFFF0000, 
                                    fontSize: 13.sp,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                SizedBox(height: 5.r),
                                Text(
                                  L.notice_info.tr,
                                  style: TextStyle(
                                    color: AppColors.colorFF7F7F7F,
                                    fontSize: 11.sp,
                                    fontWeight: FontWeight.w300,                                  
                                  ),
                                ),
                              ],
                            ),
                          ),
                          SizedBox(height: 40.r),
                          Center(
                            child: ElevatedButton(
                              onPressed: () async {
                                controller.submitNode(context);
                              },
                              child: Text(
                                L.confirm_switch.tr,
                                style: TextStyle(fontSize: 15.sp),
                              ),
                            ),
                          ),
                          SizedBox(height: 20.r),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget nodeView(ComNode node) {
    String icon = node.isSelect ? R.icNodeSelected : R.icNodeDefault;
    bool isUesd = controller.currentNode == node.node;
    Color bgColor = isUesd ? AppColors.colorFFF2F2F2 : AppColors.colorFFF7F7F7;
    if (isUesd) {
      icon = R.icNodeUsed;
      bgColor = AppColors.colorFFe6f1fa;
    }
    var nodeStr = node.node ?? '';
    if (node.node?.isNotEmpty ?? false) {
      var n = node.node!.split('.');
      if (n.isNotEmpty) {
        nodeStr = n.first;
      }
    }

    return GestureDetector(
      onTap: isUesd
          ? null
          : () {
              controller.updateNode(node);
            },
      child: Container(
        height: 44.r,
        width: double.infinity,
        padding: EdgeInsets.symmetric(horizontal: 30.r),
        // decoration: BoxDecoration(
        //   color: bgColor,
        //   borderRadius: const BorderRadius.all(
        //     Radius.circular(5),
        //   ).r,
        // ),
        child: Row(
          mainAxisSize: MainAxisSize.max,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(
              nodeStr,
              style: TextStyle(
                color: Colors.black,
                fontSize: 12.sp,
              ),
            ),
            const Spacer(),
            SignalIcon(
              pingMS: node.pingTime ?? 0,
            ),
            SizedBox(
              width: 6.w,
            ),
             Image.asset(
              icon,
              width: 20,
              height: 20,
            ),
          ],
        ),
      ),
    );
  }
}

// signal icon 4 lines horizontal
// ms rate
// 1-40ms green
// 41-100ms yellow
// 101-200ms orange
// >201ms red
class SignalIcon extends StatefulWidget {
  final int pingMS;
  const SignalIcon({Key? key, this.pingMS = 1}) : super(key: key);

  @override
  State<SignalIcon> createState() => _SignalIconState();
}

class _SignalIconState extends State<SignalIcon> {
  double maxHeight = 12.h;
  int count = 0;
  Color color = AppColors.colorFFB2B2B2;
  @override
  void initState() {
    super.initState();
    updateSignalIcon();
  }

  //调用updateSignalIcon() 当pingMS改变时
  @override
  void didUpdateWidget(covariant SignalIcon oldWidget) {
    super.didUpdateWidget(oldWidget);
    updateSignalIcon();
  }

  updateSignalIcon() {
    if (widget.pingMS <= 40) {
      count = 4;
      color = AppColors.colorFF2ABB7F;
    } else if (widget.pingMS <= 100) {
      count = 3;
      color = AppColors.colorFFF4BC28;
    } else if (widget.pingMS <= 200) {
      count = 2;
      color = AppColors.colorFFFF7D0C;
    } else if (widget.pingMS > 200) {
      count = 1;
      color = AppColors.colorFFCD3A3A;
    }
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          _buildSignalItem(0, maxHeight * 0.25, count >= 1 ? color : AppColors.colorFFB2B2B2),
          SizedBox(
            width: 2.w,
          ),
          _buildSignalItem(1, maxHeight * 0.5, count >= 2 ? color : AppColors.colorFFB2B2B2),
          SizedBox(
            width: 2.w,
          ),
          _buildSignalItem(2, maxHeight * 0.75, count >= 3 ? color : AppColors.colorFFB2B2B2),
          SizedBox(
            width: 2.w,
          ),
          _buildSignalItem(3, maxHeight, count >= 4 ? color : AppColors.colorFFB2B2B2),
          SizedBox(
            width: 2.w,
          ),
          Text(
            '${widget.pingMS}ms',
            style: TextStyle(
              color: AppColors.colorFFB2B2B2,
              fontSize: 8.sp,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

 Widget _buildSignalItem(int index, double height, Color color) {
    return AnimatedContainer(
      duration: Duration(milliseconds: 500),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.all(Radius.circular(2.r)),
      ),
      width: 2.w,
      height: height,
    );
  }
}
