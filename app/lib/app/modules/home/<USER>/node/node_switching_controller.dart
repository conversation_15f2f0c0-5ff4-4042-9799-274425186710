/*
 * <AUTHOR> <PERSON><PERSON>
 * @Date         : 2022-05-13 15:30:03
 * @Description  : TODO: Add description
 * @LastEditors  : <PERSON><PERSON>
 * @LastEditTime : 2022-06-08 18:51:41
 * @FilePath     : /flutter_metatel/lib/app/modules/home/<USER>/notifyset/notify_set_controller.dart
 */
import 'dart:async';
import 'dart:convert';

import 'package:dart_ping/dart_ping.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:flutter_metatel/core/utils/util.dart';
import 'package:flutter_metatel/core/values/colors.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../../core/languages/l.dart';
import '../../../../../core/values/config.dart';
import '../../../../data/models/node_model.dart';
import '../../../../data/providers/api/operation_center_api.dart';
import '../../../../data/services/chatio_service.dart';
import '../../../../data/services/config_service.dart';

class NodeSwitchingController extends GetxController {
  AppConfigService configService = Get.find();
  var netWorkNode = "".obs;
  var nodeList = RxList<ComNode>().obs;
  final String nodeKeyId = 'nodeKeyId';
  String currentNode = '';
  final List<StreamSubscription> _subscriptions = [];
  Timer? timer;

  @override
  void onInit() {
    super.onInit();
    getNetWorkNetNode();
    getNodeList();
    StreamSubscription<Object> subscription;

    subscription = Config.nodeDefault.listen((p0) {
      getNetWorkNetNode();
      update([nodeKeyId]);
    });
    _subscriptions.add(subscription);
    startCheckingNode();
  }
  @override
  void onClose() {
    for (var element in _subscriptions) {
      element.cancel();
    }
    _subscriptions.clear();
    stopCheckingNode();
    super.onClose();
  }
  void getNetWorkNetNode() {
    var tmp = Config.node();
    currentNode = tmp;
    var subStart = tmp.lastIndexOf("/");
    if (subStart > -1) {
      tmp = tmp.substring(subStart + 1);
    }
    var subEnd = tmp.indexOf(":");
    if (subStart > -1) {
      tmp = tmp.substring(0, subEnd);
    }
    var n = tmp.split('.');
    if (n.isNotEmpty) {
      netWorkNode.value = n.first;
    } else {
      netWorkNode.value = tmp;
    }
  }

  getNodeList() async {
    var response = await Get.find<OperationCenterApiProvider>()
        .getComNode(keyBoxID: 'n001');
    if (response.data != null &&
        (response.data?.nodeModelData?.publicNode?.isNotEmpty ?? false)) {
      nodeList.value.addAll(response.data!.nodeModelData!.publicNode!);
      checkAllNodePingTime();
    }
  }

  updateNode(ComNode node) {
    var list = nodeList.value.toList();
    for (var n in list) {
      n.isSelect = false;
      AppLogger.d('updateNode ${node.node}');
      if (node == n) {
        n.isSelect = true;
        AppLogger.d('updateNode n= ${n.node}');
      }
    }
    nodeList.value.clear();
    nodeList.value.addAll(list);
  }

  submitNode(BuildContext context) async {
    var node = nodeList.value.firstWhereOrNull((n) => n.isSelect);
    if (node != null) {
      Timer? timer;
      var logOfCountTime = 5.obs;
      timer = Timer.periodic(const Duration(seconds: 1), (t) {
        if (logOfCountTime.value > 0) {
          --logOfCountTime.value;
        } else {
          t.cancel();
          timer = null;
        }
      });
      await showBottomDialogCommonNew(context, widgets: [
        commeDialog02(
          logOfCountTime,
          rightText: L.cancel.tr,
          leftText: L.confirm.tr,
          title: L.node_switch_title.tr,
          leftCallBack: () {
            _submitNode(node);
            Get.back();
          },
          rightCallBack: () {
            Get.back();
          },
          // desc: L.switch_node_precautions.tr,
        )
      ]);
      timer?.cancel();
      timer = null;
    }
  }

  _submitNode(ComNode node) async {
    if (node.node?.isEmpty ?? true) {
      return;
    }
    showLoadingDialog(
        isBack:false,
        timeOutSecond: 60,
        text: L.switching_node.tr,
        dialog: SimpleDialog(
          backgroundColor: AppColors.white,
          elevation: 2,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.all(Radius.circular(18.r))),
          contentPadding: EdgeInsets.symmetric(horizontal: 12.r, vertical: 35.r),
          children: [
            Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  L.node_switching_title.tr,
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 16.r),
                Text(
                  L.node_switching_desc.tr,
                  textAlign: TextAlign.left,
                  style: TextStyle(
                    fontSize: 13.sp,
                    fontWeight: FontWeight.w300,
                  ),
                ),
                SizedBox(height: 20.r),
                CupertinoActivityIndicator(
                  radius: 10.r,
                  color: AppColors.primaryBgColor1,
                ),
              ],
            ),
          ],
        )
    );
    var response =
    await Get.find<OperationCenterApiProvider>().switchNode(node.node!);
    if (response.statusCode == 200 && response.data!=null) {
      var data = response.data;
      if(data?.isNotEmpty ?? false){
        Get.find<AppConfigService>().saveSwitchNodeInfo(data);
        Future.delayed(const Duration(seconds: 4),() async{
          var findModel = await Get.find<OperationCenterApiProvider>().findNodeSate(data!);
          if (findModel.data?.code == 200) {
            Get.find<AppConfigService>().saveSwitchNodeInfo(null);
            Get.find<AppConfigService>().saveNodeNEW('');

            initToken(needUpdateNode: true);
            await Future.delayed(const Duration(seconds: 5));
            dismissLoadingDialog();
          } else {
            dismissLoadingDialog();
            toast('find node state error ');
          }
        });
      }

    } else {
      toast('switch node error ');
      dismissLoadingDialog();

    }
  }

  startCheckingNode() async {
    timer = Timer.periodic(const Duration(seconds: 5), (t) {
      checkAllNodePingTime();
    });
  }

  stopCheckingNode() {
    timer?.cancel();
    timer = null;
  }

  checkAllNodePingTime() async {
    for (var node in nodeList.value) {
      var pingUrl =
      node.node?.replaceAll('http://', '').replaceAll('https://', '');
      // remove port
      var subStart = pingUrl?.lastIndexOf(":");
      if (subStart != null && subStart > -1) {
        pingUrl = pingUrl?.substring(0, subStart);
      }
      var ping = await Ping(pingUrl!, count: 1).stream.first;
      if (ping.response?.time != null) {
        final ms = ping.response!.time!.inMicroseconds /
            Duration.millisecondsPerSecond;
        node.pingTime = ms.toInt();
      }
    }
    // nodeList.value.sort((a, b) => (a.pingTime ?? 0).compareTo(b.pingTime ?? 0));
    nodeList.refresh();
  }
}
