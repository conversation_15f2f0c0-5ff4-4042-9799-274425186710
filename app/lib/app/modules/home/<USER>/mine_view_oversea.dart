import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/modules/home/<USER>/mine_view.dart';
import 'package:flutter_metatel/app/modules/home/<USER>/set/set_oversea.dart';
import 'package:flutter_metatel/app/modules/home/<USER>/setmyselfinfo/set_my_self_info.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:flutter_metatel/core/utils/device_util.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../core/languages/l.dart';
import '../../../../core/utils/util.dart';
import '../../../../core/values/colors.dart';
import '../../../../r.dart';
import '../../../../routes/pages.dart';
import '../../../data/providers/api/staking_api.dart';
import '../../../widgets/divider_cus.dart';
import '../../../widgets/mavatar_circle_avatar.dart';
import '../../../widgets/ota_update/update.dart';
import '../../base/base_view.dart';
import 'mine_controller.dart';
import 'myqrcode/my_qrcode_page_im.dart';

class MineViewImOverSea extends StatefulWidget {
  const MineViewImOverSea({super.key});

  @override
  State<StatefulWidget> createState() => _MineViewImOverSeaState();
}

class _MineViewImOverSeaState extends State<MineViewImOverSea> {
  final MineController controller = Get.put(MineController(), permanent: true);

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Obx(
          () {
        return BaseView(
            isDark: false,
            Scaffold(
              body: ListView(
                padding: EdgeInsets.zero,
                children: [
                  SizedBox(
                    height: 210.r,
                    child: Stack(
                      children: [
                        Container(
                          height: 210.r,
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: [
                                AppColors.primaryBgColor2,
                                AppColors.primaryBgColor1,                                                                                            
                              ],                              
                              begin: Alignment(-1.5,-1),
                              end: Alignment(1,1),      
                            ),
                            // image: DecorationImage(
                            //     image: AssetImage(R.bgMineOversea),
                            //     fit: BoxFit.cover),
                          ),
                        ),
                        Container(
                          alignment: Alignment.center,
                          height: 84.r,
                          margin: EdgeInsets.only(
                              top: 95.r, left: 20.w, right: 20.w, bottom: 20.r),
                          color: AppColors.transparent,
                          child: InkWell(
                            onTap: () {
                              Get.to(() => const SetMySelfInfoPage());
                            },
                            child: Row(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                GestureDetector(
                                  onTap: () {
                                    // controller.loadTest();
                                    if (controller.userIconPath.value.isEmpty) {
                                       Get.to(() => const SetMySelfInfoPage());
                                      return;
                                    }
                                    showBottomDialogCommonWithCancel(context,
                                        widgets: controller
                                            .buildSetHeadBottomSheetItemWidgets(
                                            context));
                                  },
                                  child: Container(
                                    width: 84.r,
                                    height: 84.r,
                                    decoration: BoxDecoration(
                                      shape: BoxShape.circle,
                                      color: AppColors.white,
                                      border: Border.all(color: AppColors.white,width: 2),
                                    ),
                                    child: Center(
                                      child: MAvatarCircle(
                                        diameter: 84,
                                        text: controller.userLocalName.value,
                                        imagePath: controller.userIconPath.value,
                                      ),
                                    ),
                                  ),
                                ),
                                SizedBox(
                                  width: 16.r,
                                ),
                                Expanded(
                                  child: Column(
                                    mainAxisAlignment:
                                    MainAxisAlignment.center,
                                    crossAxisAlignment:
                                    CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        controller.userLocalName.value,
                                        style: TextStyle(
                                            fontSize: 24.sp,
                                            fontWeight: FontWeight.w500,
                                            color: AppColors.colorFFF7F7F7),
                                        maxLines: 1,
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                      GestureDetector(
                                        onTap: () {
                                          if (controller.sbtMore.value) {
                                            // controller.gotoDidList();
                                          }
                                        },
                                        child: Container(
                                          color: Colors.transparent,
                                          padding: EdgeInsets.only(
                                              // top: 10.r,
                                              // bottom: 10.r,
                                              right: 50.r),
                                          child: Row(
                                            children: [
                                              // ExtendedText(
                                              //   getTid(),
                                              //   specialTextSpanBuilder:
                                              //   MySpecialTextSpanBuilder(
                                              //       showAtBackground: true,

                                              //       size: Size(25.r, 25.r)),
                                              //   style: TextStyle(
                                              //       fontSize: 18.sp,
                                              //       fontWeight: FontWeight.w500,
                                              //       color: AppColors
                                              //           .colorFFCD3A3A),
                                              // ),
                                              Container(
                                                width: 30.r,
                                                height: 30.r,
                                                decoration: BoxDecoration(
                                                  shape: BoxShape.circle,
                                                  color: AppColors.white,
                                                ),
                                                child: Center(
                                                  child: Image.asset(
                                                    R.iconDid, 
                                                    width: 18.r,
                                                    height: 18.r,                                                  
                                                  ),
                                                ),
                                              ),
                                              SizedBox(width: 4.r),
                                              Container(
                                                padding: EdgeInsets.only(
                                                    left: 14.r,
                                                    right: 14.r,
                                                    top: 1.r,
                                                    bottom: 1.r,
                                                ),
                                                decoration: BoxDecoration(
                                                  color: AppColors.white,
                                                    // gradient: LinearGradient(
                                                    //     begin:
                                                    //     Alignment.topLeft,
                                                    //     end:
                                                    //     Alignment.topCenter,
                                                    //     colors: [
                                                    //       Color(0xffFEFFFF),
                                                    //       Color(0xffB0E3FF),
                                                    //     ]),
                                                  borderRadius: BorderRadius.circular(10.r),
                                                  //  BorderRadius.only(
                                                  //     topRight: Radius.circular(10),
                                                  //     bottomRight: Radius.circular(10),
                                                  // ),
                                                ),
                                                child: Row(
                                                  children: [
                                                    Obx(() {
                                                      var v = controller.sbtId.value;
                                                      if(v.isNotEmpty){
                                                        return Text(
                                                          v,
                                                          style: TextStyle(
                                                              fontSize: 10.sp,
                                                              color: AppColors.primaryBgColor1,
                                                              fontWeight: FontWeight.w300,
                                                          ),
                                                          maxLines: 1,
                                                          overflow:
                                                          TextOverflow.ellipsis,
                                                          softWrap: true,
                                                        );
                                                      } else {
                                                        return Text(
                                                          L.nft_advance_number_no.tr,
                                                          style: TextStyle(
                                                              fontSize: 10.sp,
                                                              color: AppColors.primaryBgColor1,
                                                              fontWeight: FontWeight.w300,
                                                          ),
                                                          maxLines: 1,
                                                          overflow:
                                                          TextOverflow.ellipsis,
                                                          softWrap: true,
                                                        );
                                                      }
                                                    }),
                                                    // Text(
                                                    //   controller.sbtId.value,
                                                    //   style: TextStyle(
                                                    //       fontSize: 13.sp,
                                                    //       color: AppColors.colorFF3474d1),
                                                    //   maxLines: 1,
                                                    //   overflow:
                                                    //   TextOverflow.ellipsis,
                                                    //   softWrap: true,
                                                    // ),
                                                    Visibility(
                                                      visible: controller.sbtMore.value,
                                                      child: Image.asset(
                                                        R.icDidMore,
                                                        width: 12.r,
                                                        height: 12.r,
                                                      ),
                                                    )
                                                  ],
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),

                        ///set qr
                        Container(
                          color: AppColors.transparent,
                          margin: EdgeInsets.only(top: 41.h, left: 16.w),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: [
                              IconButton(
                                onPressed: () {
                                  Get.to(
                                    () =>
                                    MyQrcodePageIm(
                                      controller: controller,
                                    ),
                                  );
                                },
                                icon: Image.asset(
                                  R.iconMineOverseaQr,
                                  width: 20.r,
                                  height: 20.r,
                                ),
                              ),
                              IconButton(
                                onPressed: () {
                                  controller.resetWalletOpen();
                                  Get.to(() => SetOverSeaPage(controller));
                                },
                                icon: Image.asset(
                                  R.iconMineOverseaSet,
                                  width: 20.r,
                                  height: 20.r,
                                ),
                              )
                            ],
                          ),
                        ),

                      ],
                    ),
                  ),

                  Container(
                      margin:
                      const EdgeInsets.only(top: 10).r,
                      decoration: BoxDecoration(
                          color: Theme.of(context).primaryColor,
                          borderRadius:
                          const BorderRadius.all(Radius.circular(10)).r),
                      child: Column(
                        children: [
                          Obx(() => Visibility(
                              visible:StakingApi.stakingApiUrl.value.isNotEmpty,
                              child: settingsWidgetItemSimple(
                                height: 54.h,
                                horizontalPadding: 35,
                                fontSize: 15.sp,
                                L.node_staking.tr,
                                inWellBorderRadius: const BorderRadius.only(
                                    topRight: Radius.circular(10),
                                    topLeft: Radius.circular(10))
                                    .r,
                                // rightArrowColor: Colors.black,                                
                                leftWidget: Image.asset(
                                  R.icStaking,
                                  width: 26.w,
                                  height: 26.w,
                                ),
                                rightWidget: Icon(Icons.chevron_right, size: 20.r),
                                rightColor: AppColors.colorFF040404,
                                onTapCallBack: () {
                                  Get.toNamed(Routes.NodeStakingListView);
                                },
                              ))),
                          DividerCus(
                            indent: 60.w,
                          ),
                          settingsWidgetItemSimple(
                            height: 54.h,
                            horizontalPadding: 35,
                            fontSize: 15.sp,
                            L.share_promotion.tr,
                            inWellBorderRadius: const BorderRadius.only(
                                topRight: Radius.circular(10),
                                topLeft: Radius.circular(10))
                                .r,
                            // rightArrowColor: Colors.black,    
                            leftWidget: Image.asset(
                              R.iconMineOverseaShare,
                              width: 26.w,
                              height: 26.w,
                            ),
                            rightWidget: Icon(Icons.chevron_right, size: 20.r),
                            rightColor: AppColors.colorFF040404,
                            onTapCallBack: () {
                              Get.toNamed(Routes.InvitePage);
                            },
                          ),
                          Obx(
                            () => Visibility(
                              visible: controller.isWalletOpen.value,
                              child: DividerCus(
                                indent: 60.w,
                              ),
                            ),
                          ),
                          Obx(
                            () => Visibility(
                              visible: controller.isWalletOpen.value,
                              child: settingsWidgetItemSimple(
                                height: 54.h,
                                horizontalPadding: 35,
                                fontSize: 15.sp,
                                L.mnemonic_export.tr,
                                inWellBorderRadius: const BorderRadius.only(
                                    bottomLeft: Radius.circular(10),
                                    bottomRight: Radius.circular(10))
                                    .r,
                                // rightArrowColor: Colors.black,    
                                rightWidget: Icon(Icons.chevron_right, size: 20.r),
                                leftWidget: Image.asset(
                                  R.iconMineOverseaExport,
                                  width: 25.w,
                                  height: 25.w,
                                ),
                                onTapCallBack: () {
                                  controller.mnemonicExport();
                                },
                              ),
                            ),
                          ),
                        ],
                      )),
                  Container(
                    margin: const EdgeInsets.only(top: 10, bottom: 40).r,
                    decoration: BoxDecoration(
                        color: Theme.of(context).primaryColor,
                        borderRadius:
                        const BorderRadius.all(Radius.circular(10)).r),
                    child: Column(
                      children: [
                        settingsWidgetItemSimple(
                          height: 54.h,
                          horizontalPadding: 35,
                          fontSize: 15.sp,
                          L.blacklist.tr,
                          // rightArrowColor: Colors.black,
                          rightWidget: Icon(Icons.chevron_right, size: 20.r),
                          leftWidget: Image.asset(
                            R.iconMineOverseaBlock,
                            width: 26.w,
                            height: 26.w,
                          ),
                          inWellBorderRadius: const BorderRadius.only(
                            topRight: Radius.circular(10),
                            topLeft: Radius.circular(10),
                          ).r,
                          onTapCallBack: () => Get.toNamed(Routes.Blacklist),
                        ),
                        DividerCus(
                          indent: 60.w,
                        ),
                        settingsWidgetItemSimple(
                          height: 54.h,
                          horizontalPadding: 35,
                          fontSize: 15.sp,
                          L.data_backup_recover.tr,
                          // rightArrowColor: Colors.black,
                          rightWidget: Icon(Icons.chevron_right, size: 20.r),
                          leftWidget: Image.asset(
                            R.icoDataBackup,
                            width: 26.w,
                            height: 26.w,
                          ),
                          onTapCallBack: () {
                            controller.onBuckup();
                          },
                        ),
                        DividerCus(
                          indent: 60.w,
                        ),
                        settingsWidgetItemSimple(
                          height: 54.h,
                          horizontalPadding: 35,
                          fontSize: 15.sp,
                          L.network_node.tr,
                          leftWidget: Image.asset(
                            R.iconMineOverseaNet,
                            width: 26.w,
                            height: 26.w,
                          ),
                          rightWidget: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Text(
                                controller.netWorkNode.value,
                                style: TextStyle(
                                  fontSize: 15.sp,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                              SizedBox(width: 16.r),
                              Icon(Icons.chevron_right, size: 20.r),
                              // Image.asset(
                              //   R.nextArrowGrey,
                              //   width: 6.w,
                              //   height: 10.h,
                              // ),

                            ],
                          ),
                          onTapCallBack: () => Get.toNamed(Routes.NodeSwitchingPage),

                        ),
                        DividerCus(
                          indent: 60.w,
                        ),
                        settingsWidgetItemSimple(
                          height: 54.h,                          
                          horizontalPadding: 35,
                          fontSize: 15.sp,
                          L.main_version_update.tr,
                          widgetTitleRight: Obx(
                            () => Visibility(
                              visible: controller.hasNewVersion.value,
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  SizedBox(width: 10.r),
                                  Container(
                                    width: 6.r,
                                    height: 6.r,
                                    decoration: BoxDecoration(
                                      shape: BoxShape.circle,
                                      color: AppColors.colorFFC52929,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                          leftWidget: Image.asset(
                            R.iconMineOverseaVersion,
                            width: 26.w,
                            height: 26.w,
                          ),
                          inWellBorderRadius: const BorderRadius.only(
                              bottomLeft: Radius.circular(10),
                              bottomRight: Radius.circular(10))
                              .r,
                          right: controller.versionName.value,
                          onTapCallBack: () {
                            if(!controller.hasNewVersion.value) return;
                            if(DeviceUtil.isIOS()) {
                              try {
                                controller.openAppStore(); 
                              } catch (e) {
                                logger.d("onUpdateVersion Tap error: $e");
                              }
                            } else {
                              OTAUtil().checkVersion(UpdateType.setting);
                            }
                          },
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ));
      },
    );
  }
}
