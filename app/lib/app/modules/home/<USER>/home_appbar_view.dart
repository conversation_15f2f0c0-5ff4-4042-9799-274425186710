/*
 * @Author: your name
 * @Date: 2022-04-28 09:51:49
 * @LastEditTime: 2022-05-06 15:46:55
 * @LastEditors: luo<PERSON> <EMAIL>
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \flutter_metatel\lib\app\modules\home\appbar\home_appbar.dart
 */

import 'package:flutter/material.dart';
import 'package:flutter_metatel/core/values/colors.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'home_appbar_base.dart';

class HomeAppBarView extends HomeAppBarBase {
  const HomeAppBarView({
    Key? key,
    required this.title,
    required SearchType type,
    this.body,
    this.titleWidget,
  }) : super(key: key, type: type);

  final String title;
  final Widget? body;
  final Widget? titleWidget;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).primaryColor,
      appBar: AppBar(
        backgroundColor: AppColors.backgroundGray,
        centerTitle: true,
        // 标题
        title: titleWidget ??
            Text(
              title,
              style: TextStyle(
                fontSize: 16.sp,
                color: Colors.black,
                fontWeight: FontWeight.bold,
              ),
            ),
        // 右侧菜单按钮
        actions: [
          Visibility(
            visible: canSearch ?? true,
            child: buildSearchIcon(),
          ),
          Padding(
            padding: const EdgeInsets.only(right: 0).r,
            child: buildMenu(),
          ),
        ],
        // 底部搜索框
        // bottom: PreferredSize(
        //   preferredSize: Size(double.infinity, 50.h),
        //   child: Padding(
        //     padding: EdgeInsets.only(left: 16.w, right: 16.w, bottom: 10.h),
        //     child: buildSearch(
        //       height: 36.h,
        //       hintText: L.searbar_hint_search.tr,
        //       fillColor: Colors.white,
        //     ),
        //   ),
        // ),
      ),
      body: body,
    );
  }
}
