import 'package:flutter_metatel/app/modules/home/<USER>/pin/modify/pin_modify_page_detail.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:get/get.dart';

import '../../../../data/services/config_service.dart';

class PinSetController extends GetxController {
  var isSetPin = false.obs;

  @override
  void onInit() {
    AppLogger.d("onInit");
    isSetPin.value = Get.find<AppConfigService>().hasSetPin()??false;
    super.onInit();
  }

  onTapModify(){
    Get.to(const PinModifyDetailView());
  }
}
