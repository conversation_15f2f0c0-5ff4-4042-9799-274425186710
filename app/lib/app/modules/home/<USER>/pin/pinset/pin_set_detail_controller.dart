/*
 * <AUTHOR> <PERSON><PERSON>
 * @Date         : 2022-05-13 15:30:03
 * @Description  : TODO: Add description
 * @LastEditors  : <PERSON><PERSON>
 * @LastEditTime : 2022-06-08 18:51:41
 * @FilePath     : /flutter_metatel/lib/app/modules/home/<USER>/notifyset/notify_set_controller.dart
 */
import 'package:flutter/cupertino.dart';
import 'package:flutter_metatel/app/data/services/secure_store_service.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:flutter_secure_keyboard/flutter_secure_keyboard.dart';
import 'package:get/get.dart';

import '../../../../../../core/languages/l.dart';
import '../../../../../../core/utils/pwd_util.dart';
import '../../../../../../core/utils/util.dart';
import '../../../../../data/services/config_service.dart';
import '../../../../../widgets/verification/verification_box.dart';

class PinSetDetailController extends GetxController {
  String? errorInfo;

  final _secureKeyboardController = SecureKeyboardController();
  final TextEditingController textEditController = TextEditingController();
  final TextEditingController textEditingControllerAgain = TextEditingController();
  get secureKeyboardController => _secureKeyboardController;

  onTapPwdTextFiled(TextEditingController controller,GlobalKey<VerificationBoxState> state,{bool clean = true}){
    if(clean){
      state.currentState?.onValueChange("");
    }
    state.currentState?.setFocus(true);
    _secureKeyboardController.hide();
    showSecureKeyBoard(_secureKeyboardController, (value) {
      AppLogger.d('showSecureKeyBoard value =$value');

      state.currentState
          ?.onValueChange(String.fromCharCodes(value));
    });

  }

  onConfirm() async {
    String pwd=textEditController.text;
    String pwdAgain=textEditingControllerAgain.text;
    AppLogger.d('onConfirm pwd=$pwd pwdAgain=$pwdAgain');

    errorInfo=validator(pwd, false);
    errorInfo=validator(pwdAgain, true);
    if(errorInfo==null){
      if(pwd.isEmpty&&pwdAgain.isEmpty){
        errorInfo=L.pin_cannot_be_empty.tr;
      }else if(pwd!=pwdAgain){
        errorInfo=L.password_check_password_not_same.tr;
      }
    }
    AppLogger.d('errorInfo=$errorInfo');
    if (errorInfo == null ) {
        Get.find<AppConfigService>().saveHasSetPin(true);
        Get.find<SecureStoreService>().secureStoreSavePin(pwd);
        Get.back(result: true);
    } else {
      toast(errorInfo ?? "");
    }
  }
  String? validator(String value, bool isAgainInput){
    errorInfo = null;
    if (value.trim().length > 5) {
      if (isAgainInput &&
          textEditingControllerAgain.text != textEditController.text) {
        errorInfo = L.password_check_password_not_same.tr;
      }
    } else {
      errorInfo = L.must_be_a_6_digit_password.tr;
    }
    return errorInfo;
  }
}
