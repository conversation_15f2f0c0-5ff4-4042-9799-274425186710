/*
 * <AUTHOR> <PERSON><PERSON>
 * @Date         : 2022-05-13 15:30:03
 * @Description  : TODO: Add description
 * @LastEditors  : <PERSON><PERSON>
 * @LastEditTime : 2022-06-08 18:51:41
 * @FilePath     : /flutter_metatel/lib/app/modules/home/<USER>/notifyset/notify_set_controller.dart
 */
import 'package:flutter/cupertino.dart';
import 'package:flutter_metatel/app/data/services/secure_store_service.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:flutter_secure_keyboard/flutter_secure_keyboard.dart';
import 'package:get/get.dart';

import '../../../../../../core/languages/l.dart';
import '../../../../../../core/utils/pwd_util.dart';
import '../../../../../../core/utils/util.dart';
import '../../../../../widgets/verification/verification_box.dart';

class PinModifyDetailController extends GetxController {
  String? errorInfo;
  final _secureKeyboardController = SecureKeyboardController();

  final TextEditingController textEditControllerOld = TextEditingController();
  final TextEditingController textEditControllerNew = TextEditingController();
  final TextEditingController textEditingControllerAgain =
  TextEditingController();

  get secureKeyboardController => _secureKeyboardController;

  onTapPwdTextFiled(TextEditingController controller,GlobalKey<VerificationBoxState> state,{bool clean = true}){
    if(clean){
      state.currentState?.onValueChange("");
    }
    state.currentState?.setFocus(true);
    _secureKeyboardController.hide();
    showSecureKeyBoard(_secureKeyboardController, (value) {

      state.currentState
          ?.onValueChange(String.fromCharCodes(value));
    });

  }


  onConfirm(PwdType pwdType) async {
    String pwdNew = textEditControllerNew.text;
    String pwdAgain = textEditingControllerAgain.text;
    String oldPwd = textEditControllerOld.text;
    AppLogger.d('onConfirm oldPwd=$oldPwd  pwdNew=$pwdNew pwdAgain=$pwdAgain');

    String? info = validator(oldPwd,false);
    info ??= validator(pwdNew,false);
    info ??= validator(pwdAgain,false);
    if(info!=null){
      toast(info);
      return;
    }
    if(errorInfo == null){
      if(pwdNew.isEmpty){
        errorInfo=L.change_password_need_new_password.tr;
      }else if(!(pwdType==PwdType.annotative?await PwdUtil.isSamePinPwd(oldPwd):await PwdUtil.isSameDestructionPwd(oldPwd))){
        errorInfo=L.password_check_old_password_error.tr;
      }else if(pwdNew!=pwdAgain){
        errorInfo = L.password_check_password_not_same.tr;
      }else if(pwdNew==oldPwd){
        errorInfo = L.password_no_same_to_old.tr;
      }else if(await PwdUtil.isSamePinPwd(pwdNew)){
        errorInfo=L.self_destructing_password_and_pin_cannot_be_the_same.tr;
      }else if(await PwdUtil.isSameDestructionPwd(pwdNew)){
        errorInfo=L.pin_and_self_destructing_password_cannot_be_the_same.tr;
      }
    }
    if (errorInfo == null ) {
      showLoadingDialog(isBack: false);
      bool success=false;
      if(pwdType==PwdType.annotative){
        await Get.find<SecureStoreService>().secureStoreSavePin(pwdNew);
        success=true;
        // success = await PwdUtil.updateAnnotativeWords(oldPwd,pwdNew);
      }else if(pwdType==PwdType.destruction){
        success = await PwdUtil.saveDestructionPwd(pwdNew);
      }
      dismissLoadingDialog();
      if(success){
        Get.back(result: true);
      }else{

      }
    } else {
      toast(errorInfo ?? "");
    }
  }
  String? validator(String value, bool isAgainInput){
    errorInfo = null;
    if (value.trim().length > 5) {
      if (isAgainInput &&
          textEditingControllerAgain.text != textEditControllerNew.text) {
        errorInfo = L.password_check_password_not_same.tr;
      }
    } else {
      errorInfo = L.must_be_a_6_digit_password.tr;
    }
    return errorInfo;
  }


}
