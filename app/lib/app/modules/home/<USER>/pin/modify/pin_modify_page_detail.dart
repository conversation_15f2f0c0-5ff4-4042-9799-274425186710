import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/modules/home/<USER>/pin/modify/pin_modify_detail_controller.dart';
import 'package:flutter_metatel/app/widgets/app_bar_cus.dart';
import 'package:flutter_metatel/app/widgets/verification/verification_box.dart';
import 'package:flutter_metatel/core/utils/pwd_util.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_secure_keyboard/flutter_secure_keyboard.dart';
import 'package:get/get.dart';

import '../../../../../../core/languages/l.dart';
import '../../../../../../core/values/colors.dart';

class PinModifyDetailView extends StatefulWidget {
  const PinModifyDetailView({super.key, this.pwdTyp=PwdType.annotative,});
  final PwdType pwdTyp;

  @override
  State<StatefulWidget> createState() => _PinModifyDetailViewState();
}

class _PinModifyDetailViewState extends State<PinModifyDetailView> {
  final PinModifyDetailController _controller=Get.put(PinModifyDetailController());
  late String title;
  late String labelFirst;
  late String labelTwo;

  @override
  Widget build(BuildContext context) {
    return WithSecureKeyboard(
      keyboardHeight: kKeyboardDefaultHeight.r,
      controller: _controller.secureKeyboardController,
      child: Scaffold(
        backgroundColor: AppColors.colorFFF7F7F7,
        appBar: AppBarCommon().build(
          context,
          leading: BackButton(
            onPressed: () async {
              _controller.secureKeyboardController.hide();
              Get.back(result: false);
            },
          ),
          title: title,
        ),
        body: ListView(
          children: [
            Container(
              margin: EdgeInsets.only(top:16.r,left: 16.r,right: 16.r),
              padding: const EdgeInsets.only(
                left: 16,
                right: 16,
                top: 10,
                bottom: 10,
              ),
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10).r,
                  color: Colors.white,
                  border: Border.all(
                      color: AppColors.white, width: 1)),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    labelFirst,
                    style: TextStyle(fontSize: 14.sp, color: Colors.black),
                  ),
                  VerificationBox(
                    count: 6,
                    borderColor: Colors.grey,
                    borderWidth: 1.r,
                    key: verificationBoxKey,
                    focusBorderColor: Colors.lightBlue,
                    textStyle: TextStyle(color: Colors.grey, fontSize: 20.sp),
                    textEditingController: _controller.textEditControllerOld,
                    onSubmitted: (value, clean) async {
                      // _controller.onSubmit(value, widget.canDestruction, widget.isDialog);
                      _controller.onTapPwdTextFiled(
                          _controller.textEditingControllerAgain,
                          verificationBoxKey02,
                          clean: false);
                    },
                    onTap: (e) {
                      verificationBoxKey03.currentState?.setFocus(false);
                      verificationBoxKey02.currentState?.setFocus(false);
                      _controller.onTapPwdTextFiled(
                          _controller.textEditControllerOld, verificationBoxKey);
                    },
                  ),
                ],
              ),
            ),

            Container(
              margin: EdgeInsets.only(left:16.r,right: 16.r,top: 10.r),
              padding: const EdgeInsets.only(
                left: 16,
                right: 16,
                top: 10,
                bottom: 10,
              ),
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10).r,
                  color: Colors.white,
                  border: Border.all(
                      color: AppColors.white, width: 1)),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    labelTwo,
                    style: TextStyle(fontSize: 14.sp, color: Colors.black),
                  ),
                  VerificationBox(
                    count: 6,
                    borderColor: Colors.grey,
                    borderWidth: 1.r,
                    autoFocus: false,
                    key: verificationBoxKey02,
                    focusBorderColor: Colors.lightBlue,
                    textStyle: TextStyle(color: Colors.grey, fontSize: 20.sp),
                    textEditingController:
                    _controller.textEditControllerNew,
                    onSubmitted: (value, clean) async {
                      // _controller.onSubmit(value, widget.canDestruction, widget.isDialog);
                      _controller.onTapPwdTextFiled(
                          _controller.textEditControllerNew,
                          verificationBoxKey03,
                          clean: false);
                    },
                    onTap: (e) {
                      verificationBoxKey.currentState?.setFocus(false);
                      verificationBoxKey03.currentState?.setFocus(false);
                      _controller.onTapPwdTextFiled(
                          _controller.textEditControllerNew,
                          verificationBoxKey02);
                    },
                  ),
                ],
              ),
            ),
            Container(
              margin: EdgeInsets.only(left:16.r,right: 16.r,top: 10),
              padding: const EdgeInsets.only(
                left: 16,
                right: 16,
                top: 10,
                bottom: 10,
              ),
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10).r,
                  color: Colors.white,
                  border: Border.all(
                      color: AppColors.white, width: 1)),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    L.password_check_input_again.tr,
                    style: TextStyle(fontSize: 14.sp, color: Colors.black),
                  ),
                  VerificationBox(
                    count: 6,
                    borderColor: Colors.grey,
                    borderWidth: 1.r,
                    autoFocus: false,
                    key: verificationBoxKey03,
                    focusBorderColor: Colors.lightBlue,
                    textStyle: TextStyle(color: Colors.grey, fontSize: 20.sp),
                    textEditingController:
                    _controller.textEditingControllerAgain,
                    onSubmitted: (value, clean) async {
                      // _controller.onSubmit(value, widget.canDestruction, widget.isDialog);
                    },
                    onTap: (e) {
                      verificationBoxKey.currentState?.setFocus(false);
                      verificationBoxKey02.currentState?.setFocus(false);

                      _controller.onTapPwdTextFiled(
                          _controller.textEditingControllerAgain,
                          verificationBoxKey03);
                    },
                  ),
                ],
              ),
            ),
            SizedBox(
              height: 30.h,
            ),
            Container(
              width: double.infinity,
              margin: const EdgeInsets.only(left: 16, right: 16).r,
              child: ElevatedButton(
                onPressed: () => _controller.onConfirm(widget.pwdTyp),
                child: Text(
                  L.confirm.tr,
                ),
              ),
            ),
            SizedBox(
              height: 30.h,
            ),
          ],
        ),
      ),
    );
  }

  @override
  void initState() {
    title=widget.pwdTyp==PwdType.annotative?L.modify_pin.tr:L.update_destory_password.tr;
    labelFirst=widget.pwdTyp==PwdType.annotative?L.old_pin.tr:L.old_destory_prefix_password.tr;
    labelTwo=widget.pwdTyp==PwdType.annotative?L.new_pin.tr:L.new_destory_prefix_password.tr;
    super.initState();
  }
}
