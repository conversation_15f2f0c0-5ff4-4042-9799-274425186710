import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_metatel/app/modules/home/<USER>/pin/destory/set/destroy_set_detail_controller.dart';
import 'package:flutter_metatel/app/widgets/app_bar_cus.dart';
import 'package:flutter_metatel/app/widgets/verification/verification_box.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_secure_keyboard/flutter_secure_keyboard.dart';
import 'package:get/get.dart';

import '../../../../../../../core/languages/l.dart';
import '../../../../../../../core/values/colors.dart';

class DestroySetDetailView extends StatefulWidget {
  const DestroySetDetailView({super.key});

  @override
  State<StatefulWidget> createState() => _DestroySetDetailViewState();
}

class _DestroySetDetailViewState extends State<DestroySetDetailView> {
  final DestroySetDetailController _controller=Get.put(DestroySetDetailController());

  @override
  void dispose() {
    Get.delete<DestroySetDetailController>();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return WithSecureKeyboard(
      keyboardHeight: kKeyboardDefaultHeight.r,
      controller: _controller.secureKeyboardController,
      child: Scaffold(
        backgroundColor: AppColors.colorFFF7F7F7,
        appBar: AppBarCommon().build(
          context,
          leading: BackButton(
            onPressed: () async {
              _controller.secureKeyboardController.hide();
              Get.back(result: false);
            },
          ),
          title: L.set_destory_password.tr,
        ),
        body: Column(
          children: [
            Container(
              margin: EdgeInsets.all(16.r),
              padding: const EdgeInsets.only(
                left: 16,
                right: 16,
                top: 10,
                bottom: 10,
              ),
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10).r,
                  color: Colors.white,
                  border: Border.all(
                      color: AppColors.white, width: 1)),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    L.password_check_input_6_number.tr,
                    style: TextStyle(fontSize: 14.sp, color: Colors.black),
                  ),
                  VerificationBox(
                    count: 6,
                    borderColor: Colors.grey,
                    borderWidth: 1.r,
                    key: verificationBoxKey,
                    focusBorderColor: Colors.lightBlue,
                    textStyle: TextStyle(color: Colors.grey, fontSize: 20.sp),
                    textEditingController: _controller.textEditController,
                    onSubmitted: (value, clean) async {
                      // _controller.onSubmit(value, widget.canDestruction, widget.isDialog);
                      _controller.onTapPwdTextFiled(
                          _controller.textEditingControllerAgain,
                          verificationBoxKey02,
                          clean: false);
                    },
                    onTap: (e) {
                      verificationBoxKey02.currentState?.setFocus(false);
                      _controller.onTapPwdTextFiled(
                          _controller.textEditController, verificationBoxKey);
                    },
                  ),
                ],
              ),
            ),

            Container(
              margin: EdgeInsets.only(left:16.r,right: 16.r),
              padding: const EdgeInsets.only(
                left: 16,
                right: 16,
                top: 10,
                bottom: 10,
              ),
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10).r,
                  color: Colors.white,
                  border: Border.all(
                      color: AppColors.white, width: 1)),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    L.password_check_repeat_input_6_password.tr,
                    style: TextStyle(fontSize: 14.sp, color: Colors.black),
                  ),
                  VerificationBox(
                    count: 6,
                    borderColor: Colors.grey,
                    borderWidth: 1.r,
                    autoFocus: false,
                    key: verificationBoxKey02,
                    focusBorderColor: Colors.lightBlue,
                    textStyle: TextStyle(color: Colors.grey, fontSize: 20.sp),
                    textEditingController:
                    _controller.textEditingControllerAgain,
                    onSubmitted: (value, clean) async {
                      // _controller.onSubmit(value, widget.canDestruction, widget.isDialog);
                    },
                    onTap: (e) {
                      verificationBoxKey.currentState?.setFocus(false);
                      _controller.onTapPwdTextFiled(
                          _controller.textEditingControllerAgain,
                          verificationBoxKey02);
                    },
                  ),
                ],
              ),
            ),
            SizedBox(
              height: 40.h,
            ),
            Container(
              width: double.infinity,
              margin: const EdgeInsets.only(left: 16, right: 16).r,
              child: ElevatedButton(
                onPressed: () => _controller.onConfirm(),
                child: Text(
                  L.confirm.tr,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  void initState() {
    // SchedulerBinding.instance.addPostFrameCallback((_){
    //   var currentState = _controller.formKey.currentState as FormState;
    //   currentState.validate();
    // });
    super.initState();
  }
}
