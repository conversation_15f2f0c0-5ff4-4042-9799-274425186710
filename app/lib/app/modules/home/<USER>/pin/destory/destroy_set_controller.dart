import 'package:flutter_metatel/app/data/services/config_service.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:flutter_metatel/core/utils/pwd_util.dart';
import 'package:get/get.dart';

import '../modify/pin_modify_page_detail.dart';


class DestroySetController extends GetxController {
  var isSetDestroy = false.obs;

  @override
  void onInit() async {
    AppLogger.d("onInit");
    isSetDestroy.value = (await Get.find<AppConfigService>().getDestructionPwd()??"").isNotEmpty;
    super.onInit();
  }

  onTapModify(){
    Get.to(const PinModifyDetailView(pwdTyp: PwdType.destruction,));
  }
}
