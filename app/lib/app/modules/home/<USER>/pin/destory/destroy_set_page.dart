import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/data/services/config_service.dart';
import 'package:flutter_metatel/app/modules/home/<USER>/pin/destory/destroy_set_controller.dart';
import 'package:flutter_metatel/app/modules/home/<USER>/pin/destory/set/destroy_set_page_detail.dart';
import 'package:flutter_metatel/app/modules/home/<USER>/mine_view.dart';
import 'package:flutter_metatel/app/modules/password/pin/password_view.dart';
import 'package:flutter_metatel/app/widgets/app_bar_cus.dart';
import 'package:flutter_metatel/core/languages/l.dart';
import 'package:flutter_metatel/core/values/colors.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:get/get.dart';


class DestroySetView extends StatefulWidget{
  const DestroySetView({super.key});

  @override
  State<StatefulWidget> createState() =>_DestroySetView();
}
class _DestroySetView extends State<DestroySetView> {
  final DestroySetController controller =
      Get.put(DestroySetController());
  int onTapCount=0;
  Timer? timer;
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBarCommon().build(
        context,
        title: L.destory_setting.tr,
      ),
      body: Container(
        color: Theme.of(context).primaryColor,
        child: Obx(
          () => Column(
            children: [
              settingsWidgetItemSimple(
                L.destroy_switch.tr,
                onTapEnable: false,
                rightWidget: SizedBox(
                  child: FittedBox(
                      fit: BoxFit.fill,
                      child: CupertinoSwitch(
                        activeColor: Theme.of(context).colorScheme.primary,
                        value: controller.isSetDestroy.value,
                        onChanged: (value) async {
                          if(controller.isSetDestroy.value){
                            var result=await SmartDialog.show(
                              builder: (context){
                                return const PasswordView(
                                  canDestruction: false,
                                  isDialog: true,
                                  canBack: true,
                                );
                            },
                              useAnimation: false,
                              backDismiss: true,
                              clickMaskDismiss: false,
                              keepSingle: true,
                            );
                            if(result!=null){
                              Get.find<AppConfigService>().saveDestructionPwd("");
                              controller.isSetDestroy.value=false;
                            }
                          }else{
                            bool? setSuccess=await Get.to(const DestroySetDetailView());
                            if(setSuccess??false){
                              controller.isSetDestroy.value = true;
                            }
                          }
                        },
                      ),
                  ),
                ),
              ),

              Visibility(
                visible: controller.isSetDestroy.value,
                child: SizedBox(
                  height: 20.h,
                ),
              ),
              Visibility(
                visible: controller.isSetDestroy.value,
                child: settingsWidgetItemSimple(
                  L.update_destory_password.tr,
                  onTapCallBack: () =>controller.onTapModify(),
                ),
              ),
              Container(
                color: AppColors.white,
                padding: const EdgeInsets.only(left: 16,right: 16).r,
                child: Text(
                  L.destroy_describe.tr,
                  style: TextStyle(
                    fontSize: 16.sp,
                  ),
                ),
              )

            ],
          ),
        ),
      ),
    );
  }
  @override
  void dispose() {
    timer?.cancel();
    timer=null;
    super.dispose();
  }
}