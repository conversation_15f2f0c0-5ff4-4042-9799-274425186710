
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/modules/home/<USER>/pin/pin_set_controller.dart';
import 'package:flutter_metatel/app/modules/home/<USER>/pin/pinset/pin_set_page_detail.dart';
import 'package:flutter_metatel/app/modules/home/<USER>/mine_view.dart';
import 'package:flutter_metatel/app/widgets/app_bar_cus.dart';
import 'package:flutter_metatel/core/task/time_task.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:flutter_metatel/routes/pages.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:get/get.dart';

import '../../../../../core/languages/l.dart';
import '../../../../data/services/config_service.dart';
import '../../../../data/services/secure_store_service.dart';
import '../../../password/pin/password_view.dart';

class PinSetView extends StatefulWidget{
  const PinSetView({super.key});

  @override
  State<StatefulWidget> createState() =>_PinSetView();
}
class _PinSetView extends State<PinSetView> {
  final PinSetController controller =
      Get.put(PinSetController());
  int onTapCount=0;
  int curTapTime=0;
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBarCommon().build(
        context,
        title: L.pin_set.tr,
      ),
      body: Container(
        color: Theme.of(context).primaryColor,
        child: Obx(
          () => Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              settingsWidgetItemSimple(
                L.pin_set.tr,
                onTapEnable: controller.isSetPin.value,
                onTapCallBack: (){
                  int tapTime = TimeTask.instance.getNowTime();
                  if(curTapTime==0||tapTime-curTapTime<500){
                    curTapTime=tapTime;
                    onTapCount++;
                  }else{
                    curTapTime=0;
                    onTapCount=1;
                  }
                  if(onTapCount==8){
                    curTapTime=0;
                    onTapCount=0;
                    Get.toNamed(Routes.DestroySetPage);
                  }
                },
                rightWidget: SizedBox(
                  child: FittedBox(
                      fit: BoxFit.fill,
                      child: CupertinoSwitch(
                        activeColor: Theme.of(context).colorScheme.primary,
                        value: controller.isSetPin.value,
                        onChanged: (value) async {
                          if(controller.isSetPin.value){
                            var result=await SmartDialog.show(
                              builder: (context){
                                return const PasswordView(canDestruction: false,isDialog: true,canBack: true,);
                              },
                              useAnimation: false,
                              backDismiss: true,
                              clickMaskDismiss: false,
                              keepSingle: true,
                            );
                            if(result!=null){
                              Get.find<AppConfigService>().saveHasSetPin(false);
                              Get.find<SecureStoreService>().secureStoreSavePin("");
                              Get.find<AppConfigService>().saveDestructionPwd("");
                              controller.isSetPin.value=false;
                            }
                          }else{
                            bool? setSuccess=await Get.to(const PinSetDetailView());
                            if(setSuccess??false){
                              controller.isSetPin.value = true;
                            }
                          }
                        },
                      ),
                  ),
                ),
              ),
              /*     Visibility(
              visible: controller.isSetPin.value,
              child: */
              SizedBox(
                height: 20.h,
              ),
              /*  ),*/
              Visibility(
                visible: controller.isSetPin.value,
                child: settingsWidgetItemSimple(
                  L.modify_pin.tr,
                  onTapCallBack: () =>controller.onTapModify(),
                ),
              )
            ],
          ),
        ),
      ),
    );
  }
  @override
  void dispose() {
    super.dispose();
  }
}