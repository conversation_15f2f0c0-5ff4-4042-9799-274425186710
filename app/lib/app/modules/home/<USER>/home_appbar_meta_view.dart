import 'package:flutter/material.dart';
import 'package:flutter_metatel/core/languages/l.dart';
import 'package:flutter_metatel/core/values/colors.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../widgets/divider_cus.dart';
import 'home_appbar_base.dart';

class HomeAppBarMetaView extends HomeAppBarBase {
  const HomeAppBarMetaView({
    Key? key,
    required this.title,
    required SearchType type,
    this.body,
    this.titleWidget,
  }) : super(key: key, type: type);

  final String title;
  final Widget? body;
  final Widget? titleWidget;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.white,
      body: SafeArea(
        child: Column(
          children: [
            Visibility(
              visible: canSearch ?? true,
              child: Container(
                height: 49.h,
                padding: EdgeInsets.only(left: 16.r, right: 10.r),
                child: Row(
                  children: [
                    Expanded(
                      child: buildSearch(
                        height: 33.h,
                        hintText: type==SearchType.contacts?L.chat_contact_search_contact.tr:L.search_contact_msg.tr,
                        fillColor: AppColors.backgroundGray,
                      ),
                    ),
                    buildMenu(),
                  ],
                ),
              ),
            ),
            const DividerCus(thickness: 1,),
            Expanded(child: body ?? Container()),
          ],
        ),
      ),
    );
  }
}
