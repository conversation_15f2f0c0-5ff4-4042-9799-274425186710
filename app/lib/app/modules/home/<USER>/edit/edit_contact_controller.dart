import 'package:flutter/widgets.dart';
import 'package:flutter_metatel/app/data/providers/db/database.dart';
import 'package:flutter_metatel/app/data/services/config_service.dart';
import 'package:flutter_metatel/app/modules/home/<USER>/edit/edit_contact_page.dart';
import 'package:flutter_metatel/core/task/time_task.dart';

import 'package:get/get.dart';
import 'package:drift/drift.dart' as dr;

import '../../../../../core/languages/l.dart';
import '../../../../../core/task/pc_operate_task.dart';
import '../../../../../core/task/session_task.dart';
import '../../../../../core/utils/app_log.dart';
import '../../../../../core/utils/events_bus.dart';
import '../../../../../core/utils/util.dart';
import '../../../../data/events/events.dart';
import '../../../../data/providers/api/own.dart';

class EditContactController extends GetxController {
  void updateContactor(ContactData dataNew, EditType editType,BuildContext context) async {
    if (editType == EditType.mySelf) {
      /// 修改过的名称，提交到服务器
      var bool = await submitOwnInfo(nickName:dataNew.displayname ?? "");
      if(bool){
        AppConfigService conf = Get.find<AppConfigService>();
        conf.saveMySelfDisplayName(dataNew.displayname ?? "");
        conf.saveMyselfFirstName(dataNew.fistname ?? "");
        conf.saveMyselfLastName(dataNew.lastname ?? "");
        Get.find<EventBus>().fire(ContactDataMyselfUpdateEvent());
        Get.back();
        PcOperateTask().sendContactorToPc();

      }else{
        toast(L.fail_to_edit.tr);
      }
    } else {
      var database = Get.find<AppDatabase>();
      var localName =
          getCurrentLanguageNameFormat(dataNew.fistname, dataNew.lastname);
      database.oneContact(dataNew.username).getSingleOrNull().then((data) {
        AppLogger.d("updateContactor==dataNew.edit==${dataNew.edit}");

        if (data == null) {
          AppLogger.d("updateContactor==contact==null!!!");
        } else {
          database.updateContactData(ContactCompanion.insert(
            username: dataNew.username,
            localname: ofNullable(localName),
            fistname: ofNullable(dataNew.fistname),
            lastname: ofNullable(dataNew.lastname),
            edit: ofNullable(dataNew.edit),
            updateTime:
                ofNullable(TimeTask.instance.getNowTime().toDouble()),
          ));
          AppLogger.d("updateContactor==Yes!!!");
          database.oneContact(dataNew.username).getSingleOrNull().then((data) {
            if (data != null) {
              SessionTask.name(data.username, displayname: data.localname);
              Get.find<EventBus>().fire(ContactDataUpdateEvent(data));
              Get.back();
              PcOperateTask().sendContactorToPc();

            }
          });
        }
      });
    }
  }
}
