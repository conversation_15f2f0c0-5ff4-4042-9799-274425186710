import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_metatel/app/data/providers/db/database.dart';
import 'package:flutter_metatel/app/widgets/app_bar_cus.dart';
import 'package:flutter_metatel/core/task/contact_task.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:flutter_metatel/core/values/colors.dart';

import 'package:get/get.dart';

import '../../../../../core/languages/l.dart';
import '../../../../../core/utils/util.dart';
import 'edit_contact_controller.dart';

enum EditType { mySelf, contacts }

class EditContactView extends GetView<EditContactController> {
  EditContactView({
    Key? key,
    required this.type,
  }) : super(key: key);

  @override
  EditContactController get controller => Get.put(EditContactController());

  /// 编辑类型 0->自己 1->其他人
  final EditType type; //
  final TextEditingController _textEditControllerFirstName =
      TextEditingController();
  // final TextEditingController _textEditControllerLastName =
  //     TextEditingController();

  @override
  Widget build(BuildContext context) {
    ContactData contactData = Get.arguments;
    _textEditControllerFirstName.text =
        (contactData.localname ?? "");
    // _textEditControllerLastName.text = (contactData == null
    //     ? ""
    //     : (contactData.lastname ?? contactData.fistname ?? ""));
    return Scaffold(
        appBar: AppBarCommon().build(context,
            title: type == EditType.mySelf
                ? L.edit.tr
                : L.chat_contact_edit_contact.tr,
            actions: [
              Padding(
                padding: const EdgeInsets.only(right: 20, top: 20),
                child: GestureDetector(
                    onTap: () {
                      String firstName =
                          _textEditControllerFirstName.text.trim();
                      // String lastName = _textEditControllerLastName.text.trim();
                      // String localName = currentLanguageIsSimpleChinese()
                      //     ? (lastName + firstName)
                      //     : firstName + lastName;
                      String localName=firstName;
                      String displayname = type == EditType.mySelf
                          ? localName
                          : contactData.displayname ?? "";
                      if (localName.isEmpty) {
                        toast(
                            L.chat_info_nickname_not_allowed_empty_or_blank.tr);
                      } else {
                        ContactData tmp = ContactData(
                            id: contactData.id,
                            username: contactData.username,
                            displayname: displayname,
                            localname: localName,
                            fistname: firstName,
                            lastname: "",
                            avatarPath: contactData.avatarPath,
                            edit: displayname!=localName);
                        controller.updateContactor(tmp, type,context);
                      }
                    },
                    child: Text(
                      L.confirm.tr,
                      style: TextStyle(fontSize: 14, color: Theme.of(context).colorScheme.primary),
                    )),
              )
            ]),
        body: Container(
          color: Theme.of(context).primaryColor,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                padding: const EdgeInsets.only(bottom: 10),
                constraints: const BoxConstraints(maxHeight: 50),
                child: TextField(
                  autofocus: true,
                  textAlignVertical: TextAlignVertical.bottom,
                  controller: _textEditControllerFirstName,
                  decoration: InputDecoration(
                    hintText: L.nick_name.tr,
                    contentPadding:
                        const EdgeInsets.only(left: 10, top: 0, bottom: 0),
                    border: const OutlineInputBorder(
                      borderSide: BorderSide.none,
                    ),
                  ),
                  maxLines: 1,
                  maxLength: 20,
                  maxLengthEnforcement: MaxLengthEnforcement.enforced,
                ),
              ),
              // Container(
              //   padding: const EdgeInsets.only(
              //     left: 10,
              //   ),
              //   child: const Divider(height: 1, color: Color(0xffE6E6E6)),
              // ),
              // Container(
              //   padding: const EdgeInsets.only(bottom: 10),
              //   constraints: const BoxConstraints(maxHeight: 50),
              //   child: TextField(
              //     controller: _textEditControllerLastName,
              //     textAlignVertical: TextAlignVertical.bottom,
              //     decoration: InputDecoration(
              //       hintText: L.chat_contact_note_last_name.tr,
              //       contentPadding: const EdgeInsets.only(left: 10),
              //       border: const OutlineInputBorder(
              //         borderSide: BorderSide.none,
              //       ),
              //     ),
              //     maxLines: 1,
              //     maxLength: 20,
              //     maxLengthEnforcement: MaxLengthEnforcement.enforced,
              //   ),
              // ),
              Visibility(
                visible: type == EditType.contacts,
                child: Container(
                  padding: const EdgeInsets.only(left: 10),
                  color: AppColors.backgroundGray,
                  height: 30,
                ),
              ),
              Visibility(
                  visible: type == EditType.contacts,
                  child: Ink(
                    width: double.infinity,
                    child: InkWell(
                        onTap: () {
                          showBottomDialogCommonWithCancel(context, widgets:[
                            getBottomSheetItemSimple(context, L.delete_contact.tr,
                                textColor: Colors.red,
                                radius: 12, itemCallBack: () {
                              ContactTask.delContactor(contactData.username);
                            }),
                          ]);
                          // _showDelContactorDialog(context, contactData);
                        },
                        child: Container(
                          padding: const EdgeInsets.all(10),
                          child: Text(
                            L.delete_contact.tr,
                            textAlign: TextAlign.left,
                            style: const TextStyle(
                              fontSize: 16,
                              color: Colors.red,
                            ),
                          ),
                        )),
                  ))
            ],
          ),
        ));
  }
}
