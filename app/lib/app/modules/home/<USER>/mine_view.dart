import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../core/languages/l.dart';
import '../../../../core/utils/util.dart';
import '../../../../core/values/colors.dart';
import '../../../../r.dart';

typedef SettingsWidgetItemOnTap = Function();
typedef SettingsWidgetItemClick = Function(int);

Widget settingsWidgetItemSimple(
  String title, {
  bool onTapEnable = true,
  String? iconLeft,
  String? right,
  Widget? rightWidget,
  Widget? leftWidget,
  Widget? widgetTitleRight,
  Color? rightColor,
  Color? backgroundColor,
  SettingsWidgetItemOnTap? onTapCallBack,
  EdgeInsets? edg,
  FontWeight? fontWeight,
  BorderRadius? inWellBorderRadius,
  double? height,
  double? horizontalPadding,
  double? fontSize,
}) {
  Rx<Color> backColor = (backgroundColor ?? AppColors.transparent).obs;
  return Obx(() => Material(
    color: backColor.value,
    child: InkWell(
      borderRadius: inWellBorderRadius,
      radius: 1,
      onTap: !onTapEnable
          ? null
          : () {
        ItemClickFunction(startFunction: () {
          backColor.value = AppColors.colorFFF2F2F2;
        }, endFunction: () {
          if (onTapCallBack != null) {
            onTapCallBack();
          } else {
            toast(L.not_yet_develop.tr);
          }
          backColor.value = AppColors.white;
        });

      },
      child: Container(
        height: height ?? 50.r,
        padding: edg ??
            EdgeInsets.only(left: horizontalPadding ?? 16, top: 2, right: horizontalPadding ?? 16, bottom: 2).r,
        child: Row(
          children: [
            leftWidget ??
                (iconLeft == null
                    ? Container()
                    : Image.asset(
                  iconLeft,
                  width: 21.w,
                  height: 21.w,
                )),
            SizedBox(
              width: (leftWidget == null && iconLeft == null) ? 0 : 14.5.w,
            ),
            Text(
              title,
              style: TextStyle(
                  fontSize: fontSize ?? 16.sp, color: rightColor, fontWeight: fontWeight),
            ),
            if(widgetTitleRight != null)
              widgetTitleRight,
            Expanded(
              child: Container(
                padding: const EdgeInsets.only(left: 15).r,
                alignment: Alignment.centerRight,
                child: rightWidget ??
                    ((right ?? R.nextArrowGrey).startsWith("assets")
                        ? Image.asset(
                      right ?? R.nextArrowGrey,
                      width: 6.w,
                      height: 10.h,
                    )
                        : Text(
                      right ?? "",
                      style:
                      TextStyle(fontSize: fontSize ?? 16.sp, color: rightColor),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    )),
              ),
            ),
          ],
        ),
      ),
    ),
  ));
}
