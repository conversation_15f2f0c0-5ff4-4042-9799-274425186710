/*
 * @Author: your name
 * @Date: 2022-04-25 16:41:18
 * @LastEditTime: 2022-05-07 22:08:35
 * @LastEditors: l<PERSON><PERSON> <EMAIL>
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \flutter_metatel\lib\app\modules\home\session\session_controller.dart
 */
import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/data/events/events.dart';
import 'package:flutter_metatel/app/data/providers/db/database.dart';
import 'package:flutter_metatel/core/languages/l.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import '../../../../core/task/official_account_task.dart';
import '../../../../core/values/config.dart';
import '../../../data/enums/enum.dart';
import '../../../data/services/config_service.dart';
import 'package:flutter_metatel/core/task/session_task.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:flutter_metatel/core/utils/events_bus.dart';
import 'package:flutter_metatel/core/utils/util.dart';
import 'package:flutter_metatel/routes/pages.dart';
import 'package:get/get.dart';

import '../../../data/models/user_message_model.dart';
import '../../../data/services/chatio_service.dart';

const int itemHeight = 57;

class SessionController extends GetxController with GetTickerProviderStateMixin {
  late AppDatabase _database;

  var allSessionDataObs = <SessionData>[].obs;
  var privateSessionDataObs = <SessionData>[].obs;
  var channelSessionDataObs = <SessionData>[].obs;
  var daoSessionDataObs = <SessionData>[].obs;
  var privateGroupSessionDataObs = <SessionData>[].obs;

  final rxExceptionHint = ''.obs;
  final rxNoticeEvent = SessionNoticeType.none.obs;

  ScrollController? scrollController;
  int currentIndex = 0;
  var notRead = <int>[];
  TabController? tabController;
  RxInt _currentTabIndex = 0.obs;
  List<String> sessionType = [
    L.session_all.tr,
    L.session_private.tr,
    L.session_channel.tr,
    L.session_dao.tr,
    L.session_private_group_chat.tr
  ];


  final GlobalKey _listViewKey = GlobalKey();
  GlobalKey get listViewKey => _listViewKey;

  TapDownDetails? _itemDetails;
  set itemDetails(TapDownDetails details) => _itemDetails = details;

  final Map<String, ChannelAttribute> _mapChannelAttribute = {};
  final Map<String, int> _mapChannelAttributeInt = {};
  final Map<String, String?> _mapDaoChannelTokenAddress = {};
  final Map<String, String?> _mapDaoChannelChainId = {};
  late Worker worker;
  final _current=<SessionData>[].obs;
  RxInt adsIndex = 0.obs;
  StreamSubscription? subscription;
  final List<StreamSubscription> _subscriptions = [];
  setCurrentData() async {
    if(_current.isNotEmpty){
      await getChannelAttribute(_current);
      allSessionDataObs.clear();
      notRead.clear();
      int index = 0;
      for (var element in _current) {
        // AppLogger.d('setCurrentData .....${element.toJson()}');
        if ((element.unreadCount ?? 0) > 0) {
          notRead.add(index);
        }
        index++;
      }
      allSessionDataObs.addAll(_current);
      _updateDates();
    }

  }

  _updateDates() {
    privateSessionDataObs.clear();
    channelSessionDataObs.clear();
    daoSessionDataObs.clear();
    privateGroupSessionDataObs.clear();
    if (allSessionDataObs.isNotEmpty) {
      for (var item in allSessionDataObs) {
        switch (item.chatType) {
          case 0:
            privateSessionDataObs.add(item);
            break;
          case 1:
            privateGroupSessionDataObs.add(item);
            break;
          case 2:
            if(channelAttribute(item.username) == ChannelAttribute.dao) {
              daoSessionDataObs.add(item);
            } else {
              channelSessionDataObs.add(item);
            }            
            break;
        }
      }
    }
  }

  @override
  void onInit() {
    super.onInit();
    scrollController = ScrollController();
    var bus = Get.find<EventBus>();
    _database = Get.find<AppDatabase>();
    worker = interval(
      _current,
          (_) {
            setCurrentData();
      },
      time: const Duration(milliseconds: 200),
      condition: () => true,
    );
    /// 会话监听
    subscription =_database.allSession().watch().listen((value) async {
      _current.value=value;
      if(allSessionDataObs.isEmpty){
        setCurrentData();
      }
    });
    _subscriptions.add(subscription!);
    /// 未授权事件监听
    subscription = bus.on<UnauthorizedEvent>().listen((event) {
      event.hint;
      AppLogger.d("unauthorizedCallBack00 hint==${event.hint}");
      if (event.hint.isNotEmpty) {
        Get.find<ChatioService>().updateUnauthorized(true);
      } else {
        // Get.find<ChatioService>().updateUnauthorized(false);
      }
      rxExceptionHint.value = event.hint;
    });
    _subscriptions.add(subscription!);

    /// 草稿事件监听
    subscription = bus.on<MsgDraftChangeEvent>().listen((event) {
      update([event.name]);
    });
    _subscriptions.add(subscription!);

    /// 未读消息跳转事件
    subscription = bus.on<NotReadEvent>().listen((event) {
      if (notRead.isNotEmpty) {
        if (currentIndex >= notRead.length) {
          currentIndex = 0;
        }
        scrollController?.animateTo(notRead[currentIndex] * itemHeight.h,
            duration: const Duration(milliseconds: 100), curve: Curves.ease);
        currentIndex++;
      }
    });
    _subscriptions.add(subscription!);

    /// 通知事件
    subscription = bus.on<SessionNoticeEvent>().listen((event) {
      if (event.end && rxNoticeEvent.value == SessionNoticeType.none) {
        return;
      }

      if (event.end) {
        rxNoticeEvent.value = rxNoticeEvent.value & (~event.type);
      } else {
        rxNoticeEvent.value = rxNoticeEvent.value | event.type;
      }
    });
    _subscriptions.add(subscription!);
    subscription = bus.on<UpdateMuteChangeEvent>().listen((event) {
      update([event.userName]);
    });
    _subscriptions.add(subscription!);
    subscription = bus.on<AdsUpdatedEvent>().listen((event) {
      adsIndex.value++;
    });
    _subscriptions.add(subscription!);

    _initTabController();
  }

  void _initTabController() {
    tabController?.dispose();
    tabController = null;
    tabController = TabController(initialIndex: _currentTabIndex.value ,length: sessionType.length, vsync: this);
    tabController?.addListener(() {
      _currentTabIndex.value = tabController?.index ?? 0;
    });
  }
  @override
  void onClose() {}

  bool get isNone => rxNoticeEvent.value == SessionNoticeType.none;

  bool get isReconnect =>
      SessionNoticeType.reconnect ==
      (rxNoticeEvent.value & SessionNoticeType.reconnect);

  bool get isChatioMsg =>
      SessionNoticeType.chatioMsg ==
      (rxNoticeEvent.value & SessionNoticeType.chatioMsg);

  bool get isChannelMsg =>
      SessionNoticeType.channelMsg ==
      (rxNoticeEvent.value & SessionNoticeType.channelMsg);

  bool get isNoNetwork =>
      SessionNoticeType.noNetwork ==
      (rxNoticeEvent.value & SessionNoticeType.noNetwork);

  /// 置顶变化
  void topChange(String userName, bool value) {
    SessionTask.top(userName, value);
  }

  /// 已读变化
  void readChange(String userName, bool value) {
    SessionTask.read(userName, value);
  }

  /// 删除会话
  void delete(SessionData sessionData) async {
    var del=true;
    if(Config.isOversea){
      del = await _delWarnDialog()??false;
    }
    AppLogger.d("del==$del");
    if(del){
      String? userName=sessionData.username;
      int? chatType;
      for (var element in allSessionDataObs) {
        if (element.username == userName) {
          chatType = element.chatType;
          break;
        }
      }

      SessionTask.delete(userName, chatType,sessionData.avatarPath);
      Get.find<AppConfigService>().removeDraft(userName);
      allSessionDataObs.removeWhere((element) => element.username==userName);
    }
  }

  /// 点击会话
  void pressed(SessionData sessionData) async {
    SessionTask.msgReading(sessionData.username);
    var userMessage = UserMessage(
      chatType: sessionData.chatType,
      displayName: sessionData.displayname,
      userName: sessionData.username,
      isTid: sessionData.isTid,
      avatarPath: appSupporAbsolutePath(sessionData.avatarPath),
      tokenAddress: daoChannelTokenAddress(sessionData.username),
    );
    Get.toNamed(Routes.MESSAGE, arguments: userMessage);
  }

  /// 刷新
  void onRefresh() {
    var bus = Get.find<EventBus>();
    // bus.fire(SessionNoticeEvent(SessionNoticeType.channelMsg, false));
    bus.fire(SyncChannelsEvent());
    bus.fire(SyncChannelMessageEvent());
    OfficialAccountTask.instance.getFirstNoticeList();
    Get.find<ChatioService>().updateUnauthorized(false);

  }

  bool isConnect() {
    return !Get.find<ChatioService>().isUnauthorizedCallBack;
  }

  RelativeRect getMenuPosition(Size menu) {
    var renderObj = _listViewKey.currentContext?.findRenderObject();
    if (renderObj == null || _itemDetails == null) {
      return RelativeRect.fill;
    }

    RenderBox renderBox = renderObj as RenderBox;
    var viewOffset = renderBox.localToGlobal(Offset.zero);
    var maxViewdx = viewOffset.dx + renderBox.size.width;
    var maxViewdy = viewOffset.dy + renderBox.size.height;

    var itemdx = _itemDetails!.globalPosition.dx;
    var itemdy = _itemDetails!.globalPosition.dy;

    var dx = itemdx + menu.width;
    if (dx > maxViewdx) {
      dx = itemdx - menu.width;
      dx = dx < 0.0 ? 0.0 : dx;
    } else {
      dx = itemdx;
    }

    var dy = itemdy + menu.height;
    if (dy > maxViewdy) {
      dy = itemdy - menu.height;
      dy = dy < 0.0 ? 0.0 : dy;
    } else {
      dy = itemdy;
    }

    var left = dx;
    var top = dy;
    var right = Get.width - (dx + menu.width);
    var bottom = Get.height - (dy + menu.height);

    return RelativeRect.fromLTRB(left, top, right, bottom);
  }

  /// 获取频道属性
  Future<void> getChannelAttribute(List<SessionData> events) async {
    List<String> ids = [];
    for (var element in events) {
      if (element.chatType == ChatType.channelChat.index) {
        ids.add(element.username);
      }
    }

    List<ChannelInfoData> datas = [];
    if (ids.isNotEmpty) {
      datas = await Get.find<AppDatabase>().allChannelInfoByID(ids).get();
    }

    _mapChannelAttribute.clear();
    _mapDaoChannelTokenAddress.clear();
    _mapDaoChannelChainId.clear();
    for (var element in datas) {
      var attr = ChannelAttribute.ordinary;
      if (element.attribute == ChannelAttribute.dao.index) {
        attr = ChannelAttribute.dao;
        _mapDaoChannelTokenAddress[element.channelId] = element.tokenAddress;
        _mapDaoChannelChainId[element.channelId] = element.chain;
      }
      _mapChannelAttribute[element.channelId] = attr;
      _mapChannelAttributeInt[element.channelId]=element.attribute??0;
    }
  }

  ChannelAttribute? channelAttribute(String id) {
    return _mapChannelAttribute[id];
  }

  String? daoChannelTokenAddress(String id) {
    return _mapDaoChannelTokenAddress[id];
  }

  String? daoChannelChainId(String id) {
    return _mapDaoChannelChainId[id];
  }

  int? channelAttributeInt(String id) {
    var attribute = _mapChannelAttributeInt[id];
    return attribute;
  }
  @override
  void dispose() {
    worker.dispose();
    for (var element in _subscriptions) {
      element.cancel();
    }
    _subscriptions.clear();
    tabController?.dispose();

    super.dispose();
  }
  void testCode(String userName, int op) {
    /// 测试代码
    // int? chatType;
    // for (var element in allSessionDataObs) {
    //   if (element.username == userName) {
    //     chatType = element.chatType;
    //     break;
    //   }
    // }

    // if (chatType == ChatType.groupChat.index) {
    //   try {
    //     switch (op) {
    //       case 0:
    //         File(getGroupInfoFilePath(userName)).deleteSync();
    //         break;
    //       case 1:
    //         Get.find<AppDatabase>().deleteGroupInfo([userName]);
    //         break;
    //       case 2:
    //         File(getGroupInfoFilePath(userName)).deleteSync();
    //         Get.find<AppDatabase>().deleteGroupInfo([userName]);
    //         break;
    //       default:
    //     }
    //   } catch (e) {
    //     AppLogger.e('$e');
    //   }
    // }
  }
  Future<bool?> _delWarnDialog() async {
   return await SmartDialog.show(builder: (c){
      return AlertDialog(
        content: Text(L.session_del_toast.tr),
        actions: [
          TextButton(
            child: Text(L.cancel.tr),
            onPressed: () => SmartDialog.dismiss(result: false), //关闭对话框,
          ),
          TextButton(
            child: Text(L.chat_contact_del.tr),
            onPressed: () async {
              SmartDialog.dismiss(result: true);
            }, //关闭对话框,
          ),
        ],
        actionsAlignment: MainAxisAlignment.end,
      );
    });
  }
}
