/*
 * @Author: <PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2022-05-05 12:09:35
 * @LastEditors: luoyuan <EMAIL>
 * @LastEditTime: 2022-05-05 18:57:35
 * @FilePath: \flutter_metatel\lib\app\modules\home\appbar\menu\addFriend\add_friend_controller.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter_metatel/app/data/enums/enum.dart';
import 'package:flutter_metatel/app/data/providers/db/database.dart';
import 'package:flutter_metatel/app/data/services/config_service.dart';
import 'package:flutter_metatel/app/modules/home/<USER>/detail/contact_detail.dart';
import 'package:flutter_metatel/routes/pages.dart';
import 'package:get/get.dart';

import '../../../../../../core/languages/l.dart';
import '../../../../../../core/utils/util.dart';
import '../../../../../../core/values/config.dart';
import '../../../../../data/models/own_info_model.dart';
import '../../../../../data/providers/api/did.dart';
import '../../../../../data/providers/api/own.dart';
import '../../../contact/add/add_contact_page.dart';

class ContactInfo {
  ContactInfo(this.userName);
  final String userName;
  String? name;
  String? avatarPath;
  /// 是否已为好友
  bool? isFriend;
}

class AddFriendController extends GetxController {
  ContactData? _contactData;
  RxList<OwnInfoModelData> rxSearchDataList=RxList<OwnInfoModelData>();
  TextEditingController editController = TextEditingController();
  String? _key;
  getKey()=>_key;

  Timer? timer;

  // Future<ContactInfo?> searchContact(String phone) async {
  //   var response = await Get.find<ApiProvider>().searchUserByPhone(phone);
  //   String? userName = response.body?.data?.name;
  //   userName = userNameDomain(userName);
  //   if (response.hasError || userName == null) {
  //     return null;
  //   }
  //
  //   ContactInfo contactInfo = ContactInfo(userName);
  //   contactInfo.name = phone;
  //   contactInfo.isFriend = false;
  //
  //   _contactData =
  //       await Get.find<AppDatabase>().oneContact(userName).getSingleOrNull();
  //   if (_contactData != null &&
  //       _contactData!.state == ContactState.friend.index) {
  //     contactInfo.name = _contactData!.localname;
  //     contactInfo.avatarPath = appSupporAbsolutePath(_contactData!.avatarPath);
  //     contactInfo.isFriend = true;
  //   }
  //
  //   return contactInfo;
  // }
  /// 显示联系人详情
  void showContactDetail() {
    Get.offAll(
      () => const ContactDetailView(),
      predicate: (route) {
        return route.settings.name == Routes.HOME;
      },
      arguments: _contactData,
    );
  }
  /// 文本变化监听
  void onTextChange(String text, BuildContext context) async {
    if(timer!=null){
      timer!.cancel();
      timer=null;
    }
    // timer=Timer(const Duration(seconds: 2), () {
      setKey(text);
    // });
  }

  void searchTid() async{
    var text = editController.text.trim();
    if(text.isNotEmpty){
      rxSearchDataList.clear();
      var map = {'sbt':text};
      var res = await Get.find<DidApi>().searchAccountBySbt(map);
      if(res.body?.code==200&&(res.body?.data?.isNotEmpty??false)){
        var list = res.body?.data;
        if(list!=null&&list.length<=3){
          var names = list.map((e) => '${e.account}@');
          if(names.isEmpty){
            return;
          }
          var userInfos = await getOtherInfoNew(List.from(names)) ?? [];
          userInfos.removeWhere((element) => element.name?.endsWith('@')??true);
          if(userInfos.isNotEmpty){
            List<OwnInfoModelData> ownInfoModelDataList=[];
            String? myselfUserName= Get.find<AppConfigService>().getUserName();
            for(var b in userInfos){
              OwnInfoModelData o=OwnInfoModelData(name:b.name,nickname: b.nickname,avatar: b.avatar,);
              if(o.name!=myselfUserName){
                ownInfoModelDataList.add(o);
              }
            }
            // Get.back();
            if(ownInfoModelDataList.isEmpty){
              ownInfoModelDataList.add(OwnInfoModelData(isBottom: true));
            }
            rxSearchDataList.value=ownInfoModelDataList;
          }
        }
      }
    }
  }

  void setKey(String key) {
    _key = key;
    if (key.isEmpty) {
      // Get.back();
      rxSearchDataList.clear();
    } else {
      if(Config.isOversea){
        _search3TChat();
      } else{
        _search();
      }

    }
  }

  _search3TChat() async {
    // //List<OwnInfoModelData> ownInfoModelDataList=
    // await Get.find<DidApi>()searchDidApi(_key!)??[];
    // String? myselfUserName= Get.find<AppConfigService>().getUserName();
    // for(int i=0;i<(ownInfoModelDataList).length;i++){
    //   OwnInfoModelData o=ownInfoModelDataList[i];
    //   if(o.name==myselfUserName){
    //     ownInfoModelDataList.remove(o);
    //   }
    // }
    // // Get.back();
    // if(ownInfoModelDataList.isEmpty){
    //   ownInfoModelDataList.add(OwnInfoModelData(isBottom: true));
    // }
    // rxSearchDataList.value=ownInfoModelDataList;
  }
  _search() async {
    List<OwnInfoModelData> ownInfoModelDataList=await getOtherInfoByIoIID(_key!)??[];
    String? myselfUserName=await Get.find<AppConfigService>().getUserName();
    for(int i=0;i<(ownInfoModelDataList).length;i++){
      OwnInfoModelData o=ownInfoModelDataList[i];
      if(o.name==myselfUserName){
        ownInfoModelDataList.remove(o);
      }
    }
    // Get.back();
    if(ownInfoModelDataList.isEmpty){
      ownInfoModelDataList.add(OwnInfoModelData(isBottom: true));
    }
    rxSearchDataList.value=ownInfoModelDataList;
  }

  onPressSearchResult(OwnInfoModelData o) async{
    ContactData? data =
        await Get.find<AppDatabase>().oneContact(o.name??"").getSingleOrNull();

    String? avatarPath = data?.avatarPath;
    int? state = data?.state;
    String? mySelfUserName=await Get.find<AppConfigService>().getUserName();
    if(mySelfUserName==o.name){
      toast(L.sorry_can_not_add_myself_as_a_friend.tr);
      return;
    }
    if (state == null || state == ContactState.notFriend.index) {
      Get.to(() => AddContactPage(
          userName: o.name??"",
          displayName: o.nickname??"",
          avatarPath: avatarPath,
          toMessagePage: true,
        ),
      );
    } else if (state == ContactState.friend.index) {
      Get.to(
        () => const ContactDetailView(),
        arguments: data,
      );
    }
  }
}


