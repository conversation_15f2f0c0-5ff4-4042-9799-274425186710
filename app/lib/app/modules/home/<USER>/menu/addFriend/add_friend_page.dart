/*
 * @Author: your name
 * @Date: 2022-05-05 12:09:35
 * @LastEditTime: 2022-05-05 18:55:40
 * @LastEditors: l<PERSON><PERSON> <EMAIL>
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \flutter_metatel\lib\app\modules\home\appbar\menu\addFriend\addFriend\add_friend_view.dart
 */
import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/data/models/own_info_model.dart';
import 'package:flutter_metatel/app/modules/home/<USER>/mine_view.dart';
import 'package:flutter_metatel/app/widgets/app_bar_cus.dart';
import 'package:flutter_metatel/app/widgets/text_middle_overlow.dart';
import 'package:flutter_metatel/core/languages/l.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:get/get.dart';

import '../../../../../../core/utils/jump.dart';
import '../../../../../../core/utils/string_util.dart';
import '../../../../../../core/values/config.dart';
import '../../../../../../r.dart';
import '../../../../../widgets/divider_cus.dart';
import '../../../../../widgets/mavatar_circle_avatar.dart';
import 'add_friend_controller.dart';

class AddFriendPage extends GetView<AddFriendController> {
  AddFriendPage({Key? key}) : super(key: key);
  final FocusNode focusNode = FocusNode();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBarCommon().build(context, title: L.add_buddy.tr),
      body: Container(
        color: Colors.white,
        child: Column(
          children: [
            Container(
                    height: 40,
                    margin: EdgeInsets.only(left: 16.r),
                    child: Row(
                      children: [
                        Expanded(
                            child: TextField(
                          focusNode: focusNode,
                          readOnly: false,
                          controller: controller.editController,
                          textAlign: TextAlign.start,
                          textAlignVertical: TextAlignVertical.center,
                          decoration: InputDecoration(
                            hintText: L.searbar_hint_search_tid.tr,
                            // 设置后，提升文本居中
                            contentPadding: EdgeInsets.zero,
                            prefixIcon: const Icon(
                              Icons.search,
                              color: Color.fromARGB(255, 89, 90, 90),
                            ),
                            filled: true,
                            fillColor: const Color(0xffF7F7F7),
                            border: const OutlineInputBorder(
                              borderSide: BorderSide.none,
                              borderRadius:
                                  BorderRadius.all(Radius.circular(25)),
                            ),
                          ),
                        )),
                        TextButton(
                            onPressed: () {
                              focusNode.unfocus();
                              controller.searchTid();
                            },
                            child: Text(L.searbar_hint_search.tr))
                      ],
                    ),
                  ),
            Container(
                margin: const EdgeInsets.only(top: 15),
                height: 70,
                child: settingsWidgetItemSimple(L.scan_qr_code_to_add.tr,
                    iconLeft: R.icoScanBlue, onTapCallBack: () {
                  JumpPage.toScan();
                })),
            Expanded(
              child: Obx(
                () => ListView.builder(
                    padding: const EdgeInsets.only(),
                    itemCount: controller.rxSearchDataList().length,
                    itemBuilder: (BuildContext context, int index) {
                      var model = controller.rxSearchDataList[index];
                      return _buildDataWidget(model);
                    }),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDataWidget(OwnInfoModelData model) {
    var padding =
        const EdgeInsets.only(left: 12, right: 10, top: 5, bottom: 0).r;
    bool isBottom = model.isBottom ?? false;

    return Material(
      child: InkWell(
        onTap: () {
          if (!isBottom) {
            controller.onPressSearchResult(model);
          }
        },
        child: isBottom
            ? Container(
                color: Colors.white,
                padding: const EdgeInsets.all(10).r,
                alignment: Alignment.center,
                child: Text(
                  L.search_no_result.tr,
                  style: TextStyle(
                    fontSize: 16.sp,
                  ),
                ),
              )
            : Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    height: 50.h,
                    padding: padding,
                    child: Row(
                      children: [
                        // 头像
                        createHeader(model.avatar, model.nickname),
                        // 间隔
                        SizedBox(width: 10.w),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisSize: MainAxisSize.max,
                            children: [
                              SizedBox(height: 8.r),
                              // 名称
                              Expanded(
                                child: RichText(
                                    text: TextSpan(
                                        children: StringUtil.getTextSpanList(
                                            model.nickname ?? '',
                                            searchContent:
                                                controller.getKey() ?? '',
                                            fontSize: 14.sp))),
                              ),
                              Expanded(
                                child: Text(
                                        model.name ?? '',
                                        overflow: TextOverflow.ellipsis,
                                      ),
                              ),
                              const DividerCus(),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
      ),
    );
  }

  Widget createHeader(String? avatar, String? nickName) {
    return MAvatarCircle(
      diameter: 40.h,
      imagePath: avatar,
      isNet: (avatar != null && avatar.isNotEmpty),
      textStyle: const TextStyle(fontSize: 16, color: Colors.white),
      text: nickName,
    );
  }
}
