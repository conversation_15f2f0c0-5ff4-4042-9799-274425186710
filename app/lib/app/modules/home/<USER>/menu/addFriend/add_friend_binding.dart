/*
 * @Author: your name
 * @Date: 2022-05-05 12:09:35
 * @LastEditTime: 2022-05-05 12:10:58
 * @LastEditors: your name
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \flutter_metatel\lib\app\modules\home\appbar\menu\addFriend\addFriend\add_friend_binding.dart
 */
import 'package:get/get.dart';

import 'add_friend_controller.dart';

class AddFriendBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut<AddFriendController>(
      () => AddFriendController(),
    );
  }
}
