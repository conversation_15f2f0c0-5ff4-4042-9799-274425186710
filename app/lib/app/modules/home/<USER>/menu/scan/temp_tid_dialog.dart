import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/data/services/config_service.dart';
import 'package:flutter_metatel/app/modules/mining/mining_controller.dart';
import 'package:flutter_metatel/app/widgets/text_middle_overlow.dart';
import 'package:flutter_metatel/core/languages/l.dart';
import 'package:flutter_metatel/core/utils/util.dart';
import 'package:flutter_metatel/core/values/colors.dart';
import 'package:flutter_metatel/r.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:flutter_web3/app/widgets/text_number_formatter.dart';
import 'package:flutter_web3/web3dart/src/utils/typed_data.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:decimal/decimal.dart';

import '../../../../../../core/utils/app_log.dart';
import '../../../../../data/providers/api/did.dart';
import '../../../../../data/providers/api/invite.dart';

class TempTidDialog extends StatefulWidget {
  TempTidDialog({
    super.key,
    required this.mapJson,
  });

  Map<String, dynamic> mapJson;

  @override
  State<TempTidDialog> createState() => _TempTidDialogState();
}

class _TempTidDialogState extends State<TempTidDialog> {
  var loading = false.obs;
  var sucessfull = false.obs;

  RxString address = ''.obs;
  RxString userName = ''.obs;

  @override
  void initState() {
    super.initState();
    address.value = Get.find<AppConfigService>().readMiningMiningInfo();
    userName.value = Get.find<AppConfigService>().getUserNameWithoutDomain();
  }

  @override
  Widget build(BuildContext context) {
    loading.value = false;
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: Center(
        child: Stack(
          alignment: Alignment.center,
          children: [
            Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Container(
                  height: 380.r,
                  margin: const EdgeInsets.only(left: 16, right: 16).r,
                  decoration: BoxDecoration(
                      color: AppColors.white,
                      borderRadius:
                          const BorderRadius.all(Radius.circular(16)).r),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        margin: const EdgeInsets.only(
                          top: 25,
                        ).r,
                        alignment: Alignment.center,
                        child: Text(
                          L.receive_benefits.tr,
                          style: TextStyle(
                            color: AppColors.colorFF000000,
                            fontSize: 18.sp,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      Container(
                        height: 90.r,
                        margin:
                            EdgeInsets.only(top: 16.r, left: 20, right: 20).r,
                        child: Stack(
                          children: [
                            Image.asset(
                              R.bgEquity,
                              height: double.infinity,
                              width: double.infinity,
                            ),
                            Container(
                              padding: EdgeInsets.all(10.r),
                              child: Column(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceAround,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    L.receive_benefits_3.tr,
                                    style: TextStyle(
                                      fontSize: 16.sp,
                                      color: AppColors.colorFF333333,
                                    ),
                                  ),
                                  Text(
                                    L.temp_did_identification.tr,
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                    style: TextStyle(
                                      fontSize: 16.sp,
                                      color: AppColors.colorFF3474d1,
                                    ),
                                  ),
                                  Obx(() {
                                    return MiddleText(
                                      L.get_user_address.trParams(
                                          {'address': userName.value ?? ""}),
                                      WXTextOverflow.ellipsisMiddle,
                                      style: TextStyle(
                                        fontSize: 12.sp,
                                        color: AppColors.colorFF333333,
                                      ),
                                    );
                                  }),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                      const Spacer(),
                      Padding(
                        padding: const EdgeInsets.only(
                                left: 20, right: 20, bottom: 30)
                            .r,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Flexible(
                              child: TextButton(
                                style: ButtonStyle(
                                  fixedSize: MaterialStateProperty.all(
                                      Size.fromHeight(40.h)),
                                  shape: MaterialStateProperty.all(
                                    RoundedRectangleBorder(
                                      borderRadius: BorderRadius.all(
                                              Radius.circular(5.r).r)
                                          .r,
                                    ),
                                  ),
                                  backgroundColor: MaterialStateProperty.all(
                                      AppColors.colorFFF2F2F2),
                                ),
                                onPressed: () {
                                  SmartDialog.dismiss();
                                },
                                child: Container(
                                  height: 40.h,
                                  alignment: Alignment.center,
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Text(
                                        L.cancel.tr,
                                        style: TextStyle(
                                          color: AppColors.colorFF333333,
                                          fontSize: 14.sp,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                            SizedBox(
                              width: 10.w,
                            ),
                            Flexible(
                              child: TextButton(
                                style: ButtonStyle(
                                  fixedSize: MaterialStateProperty.all(
                                      Size.fromHeight(40.h)),
                                  shape: MaterialStateProperty.all(
                                    RoundedRectangleBorder(
                                      borderRadius: BorderRadius.all(
                                              Radius.circular(5.r).r)
                                          .r,
                                    ),
                                  ),
                                  backgroundColor: MaterialStateProperty.all(
                                      AppColors.appDefault),
                                ),
                                onPressed: () {
                                  _onWithdraw();
                                },
                                child: Container(
                                  height: 40.h,
                                  alignment: Alignment.center,
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Text(
                                        L.confirm.tr,
                                        style: TextStyle(
                                          color: AppColors.white,
                                          fontSize: 14.sp,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            Obx(() => Visibility(
                  visible: loading.value,
                  child: Container(
                    margin: const EdgeInsets.only(left: 16, right: 16).r,
                    decoration: BoxDecoration(
                        color: const Color.fromRGBO(0, 0, 0, 0.46),
                        borderRadius:
                            const BorderRadius.all(Radius.circular(16)).r),
                    height: 380.r,
                    child: loadingView(),
                  ),
                )),
          ],
        ),
      ),
    );
  }

  _onWithdraw() async {
    if (widget.mapJson.isEmpty) {
      return;
    }
    if (loading.value) {
      AppLogger.d("Withdrawal request in progress...");
      return;
    }
    loading.value = true;
    var res = await Get.find<InviteApi>().addTempStb(widget.mapJson);
    loading.value = false;
    SmartDialog.dismiss();

    AppLogger.d('_addTempSbt res=${res.data?.toJson()}');
    if(res.data?.code==200){
      var showData = L.temp_tbs_info.trParams({'number1':'${res.data?.data?.day??''}','number2':'${res.data?.data?.number??''}'});
      toast(showData,toastLength: Toast.LENGTH_LONG);
    } else if(res.data?.code!=200){
      toast(res.data?.message ??'error ${res.data?.code}',toastLength: Toast.LENGTH_LONG);
    }

  }
}
