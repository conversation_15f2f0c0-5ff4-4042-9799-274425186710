/*
 * @Author: your name
 * @Date: 2022-04-28 19:07:31
 * @LastEditTime: 2022-05-07 14:49:26
 * @LastEditors: luo<PERSON> <EMAIL>
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \flutter_application_16\lib\scan_view.dart
 */

import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_scanner/mobile_scanner.dart';

import 'scan_controller.dart';
import 'scan_frame.dart';

class ScanPage extends StatefulWidget {
  const ScanPage({Key? key,}) : super(key: key);
  @override
  State<ScanPage> createState() => _ScanPageState();
}

class _ScanPageState extends State<ScanPage> {
  final ScanController _controller = Get.find();

  /// 是否打开手电筒
  bool isOpenTorcg = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        // 返回按钮
        leading: IconButton(
          iconSize: 46,
          onPressed: () {
            Get.back();
          },
          icon: const Icon(Icons.chevron_left, color: Colors.blue),
        ),
        elevation: 0,
      ),
      body: Stack(
        children: [
          // 插件
          MobileScanner(
            controller: _controller.scanController,
            fit: BoxFit.fitHeight,
            onDetect: (capture) {
              Get.back();
              final List<Barcode> barcodes = capture.barcodes;
              var value = barcodes.map((e) => e.rawValue);
              _controller.scanResult(value.join());
            },
          ),
          // 扫码框/按钮部分
          Padding(
            padding: const EdgeInsets.only(left: 50, right: 50),
            child: Column(
              // 各自权重1/3
              children: [
                // 间隔
                const Spacer(),
                // 扫码框
                const Flexible(
                  child: ScanFrame(),
                ),
                Flexible(
                  child: Align(
                    child: Row(
                      children: [
                        // 手电筒
                        StatefulBuilder(
                          builder: (context, setState) {
                            return IconButton(
                              iconSize: 32,
                              onPressed: () {
                                _controller.scanController.toggleTorch();
                                setState(() {
                                  isOpenTorcg = !isOpenTorcg;
                                });
                              },
                              icon: isOpenTorcg
                                  ? const Icon(Icons.flashlight_off,
                                      color: Colors.white)
                                  : const Icon(Icons.flashlight_on,
                                      color: Colors.white),
                            );
                          },
                        ),
                        // 间隔
                        const Spacer(),
                        // 相册
                        IconButton(
                          iconSize: 32,
                          onPressed: _controller.onOpenAlbum,
                          icon: const Icon(Icons.image, color: Colors.white),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
