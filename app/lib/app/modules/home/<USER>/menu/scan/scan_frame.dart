/*
 * @Author: your name
 * @Date: 2022-04-28 18:33:27
 * @LastEditTime: 2022-04-29 15:09:55
 * @LastEditors: Please set LastEditors
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \flutter_application_16\lib\painter.dart
 */
import 'package:flutter/material.dart';

class ScanFrame extends StatefulWidget {
  const ScanFrame({Key? key}) : super(key: key);

  @override
  State<ScanFrame> createState() => _ScanFrameState();
}

class _ScanFrameState extends State<ScanFrame>
    with SingleTickerProviderStateMixin {
  late final Animation<double> _animation;
  late final AnimationController _controller;

  @override
  void initState() {
    super.initState();

    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    );

    _animation = Tween(begin: -0.1, end: 0.85)
        .chain(CurveTween(curve: Curves.linear))
        .animate(_controller);

    _animation.addListener(() {
      setState(() {});
    });

    _animation.addStatusListener((status) {
      if (status == AnimationStatus.completed ||
          status == AnimationStatus.dismissed) {
        _controller.repeat();
      }
    });

    _controller.repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return CustomPaint(
      size: Size.infinite,
      painter: ScanFramePainter(lineMoveValue: _animation.value),
    );
  }
}

class ScanFramePainter extends CustomPainter {
  ScanFramePainter({required this.lineMoveValue});
  final double topStrokeWidth = 3;
  final double lineStrokeWidth = 1;
  final double topLineWidth = 25;
  final Color lineColor = Colors.blue;
  final double lineMoveValue;

  @override
  void paint(Canvas canvas, Size size) {
    _drawScanBox(canvas, size);
    _drawScanLine(canvas, size);
  }

  void _drawScanBox(Canvas canvas, Size size) {
    Paint paint = Paint()
      ..color = Colors.transparent
      ..strokeWidth = topStrokeWidth
      ..style = PaintingStyle.fill;

    var rect = Rect.fromLTWH(0, 0, size.width, size.height);
    canvas.drawRect(rect, paint);

    // 离边框边距
    var margin = (topStrokeWidth - lineStrokeWidth) / 2;

    // 扫描框大小
    rect = Rect.fromLTWH(
        margin, margin, size.width - (margin * 2), size.height - (margin * 2));

    // 4个点的坐标
    Offset topLeft = rect.topLeft;
    Offset bottomLeft = rect.bottomLeft;
    Offset topRight = rect.topRight;
    Offset bottomRight = rect.bottomRight;

    // paint = Paint()
    //   ..color = lineColor
    //   ..isAntiAlias = true // 是否启动抗锯齿
    //   ..strokeCap = StrokeCap.square // 解决因为线宽导致交界处不是直角的问题
    //   ..strokeWidth = lineStrokeWidth
    //   ..style = PaintingStyle.stroke;

    // // 上边线
    // canvas.drawLine(topLeft, topRight, paint);
    // // 左边线
    // canvas.drawLine(topLeft, bottomLeft, paint);
    // // 下边线
    // canvas.drawLine(bottomLeft, bottomRight, paint);
    // // 右变形
    // canvas.drawLine(topRight, bottomRight, paint);

    paint
      ..color = lineColor
      ..isAntiAlias = true
      ..strokeWidth = topStrokeWidth
      ..strokeCap = StrokeCap.square
      ..style = PaintingStyle.stroke;

    // 左上角
    canvas.drawLine(
        topLeft, Offset(topLeft.dx + topLineWidth, topLeft.dy), paint);
    canvas.drawLine(
        topLeft, Offset(topLeft.dx, topLeft.dy + topLineWidth), paint);

    // 左下角
    canvas.drawLine(
        Offset(bottomLeft.dx, bottomLeft.dy - topLineWidth), bottomLeft, paint);
    canvas.drawLine(
        bottomLeft, Offset(bottomLeft.dx + topLineWidth, bottomLeft.dy), paint);

    // 右上角
    canvas.drawLine(
        Offset(topRight.dx - topLineWidth, topRight.dy), topRight, paint);
    canvas.drawLine(
        topRight, Offset(topRight.dx, topRight.dy + topLineWidth), paint);

    // 右下角
    canvas.drawLine(Offset(bottomRight.dx, bottomRight.dy - topLineWidth),
        bottomRight, paint);
    canvas.drawLine(bottomRight,
        Offset(bottomRight.dx - topLineWidth, bottomRight.dy), paint);

    // paint = Paint()
    //   ..color = lineColor
    //   ..isAntiAlias = true
    //   ..strokeCap = StrokeCap.square
    //   ..strokeWidth = lineStrokeWidth
    //   ..style = PaintingStyle.stroke;

    // // 中间线
    // var lineY = topLeft.dy + rect.height * lineMoveValue;
    // canvas.drawLine(
    //     Offset(topLeft.dx + 10, lineY), Offset(topRight.dx - 10, lineY), paint);
  }

  void _drawScanLine(Canvas canvas, Size size) {
    Paint paint = Paint()
      ..color = Colors.transparent
      ..strokeWidth = topStrokeWidth
      ..style = PaintingStyle.fill;

    var rect = Rect.fromLTWH(0, 0, size.width, size.height);
    canvas.drawRect(rect, paint);

    var gradient = const LinearGradient(
        begin: Alignment.topCenter,
        end: Alignment.bottomCenter,
        colors: [
          Color.fromARGB(5, 255, 235, 59),
          Color.fromARGB(30, 255, 235, 59),
          Color.fromARGB(50, 255, 235, 59),
        ]);

    double height = 40;

    var startY = rect.topLeft.dy + rect.height * lineMoveValue;
    if ((startY + height) >= rect.bottomLeft.dy) {
      startY = rect.bottomLeft.dy - height;
    }

    var rect55 = Rect.fromLTWH(rect.topLeft.dx, startY, rect.width, height);

    paint = Paint()
      ..isAntiAlias = true
      ..strokeCap = StrokeCap.square
      ..strokeWidth = lineStrokeWidth
      ..shader = gradient.createShader(rect55)
      ..style = PaintingStyle.fill;

    canvas.drawRect(rect55, paint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => true;
}
