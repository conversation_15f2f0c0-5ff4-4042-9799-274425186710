/*
 * @Author: your name
 * @Date: 2022-04-29 17:40:28
 * @LastEditTime: 2022-04-29 17:40:47
 * @LastEditors: Please set LastEditors
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \flutter_metatel\lib\app\modules\home\scan\session_binding.dart
 */
/*
 * @Author: your name
 * @Date: 2022-04-25 16:41:18
 * @LastEditTime: 2022-04-27 16:23:43
 * @LastEditors: Please set LastEditors
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \flutter_metatel\lib\app\modules\home\session\session_binding.dart
 */
import 'package:get/get.dart';

import 'scan_controller.dart';

class ScanBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut<ScanController>(
      () => ScanController(),
    );
  }
}
