import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:app_version_update/app_version_update.dart';
import 'package:app_version_update/data/models/app_version_result.dart';
import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/data/models/avatar_model.dart';
import 'package:flutter_metatel/app/data/providers/api/operation_center_api.dart';
import 'package:flutter_metatel/app/data/providers/api/own.dart';
import 'package:flutter_metatel/app/data/providers/db/database.dart';
import 'package:flutter_metatel/app/data/services/translation_service.dart';
import 'package:flutter_metatel/app/modules/backup/backup_manage.dart';
import 'package:flutter_metatel/app/modules/dao/daoBrowser/dao_browser_view.dart';
import 'package:flutter_metatel/app/modules/dao/dao_config.dart';
import 'package:flutter_metatel/app/modules/home/<USER>';
import 'package:flutter_metatel/app/modules/home/<USER>/pin/pinset/pin_set_page_detail.dart';
import 'package:flutter_metatel/app/modules/home/<USER>/setmyselfinfo/set_my_self_info.dart';
import 'package:flutter_metatel/app/modules/virtualcard/model/static_data.dart';
import 'package:flutter_metatel/app/widgets/chat_widget/photos_video_view.dart';
import 'package:flutter_metatel/app/widgets/ota_update/update.dart';
import 'package:flutter_metatel/core/task/avatar_task.dart';
import 'package:flutter_metatel/core/task/message_task.dart';
import 'package:flutter_metatel/core/utils/language_util.dart';
import 'package:flutter_metatel/core/utils/proxy_util.dart';
import 'package:flutter_metatel/core/values/colors.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:get/get.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../../core/languages/l.dart';
import '../../../../core/utils/app_log.dart';
import '../../../../core/utils/device_util.dart';
import '../../../../core/utils/events_bus.dart';
import '../../../../core/utils/util.dart';
import '../../../../core/values/config.dart';
import '../../../../r.dart';
import '../../../../routes/pages.dart';
import '../../../data/enums/enum.dart';
import '../../../data/enums/path.dart';
import '../../../data/events/events.dart';
import '../../../data/models/own_info_model.dart';
import '../../../data/models/res/did/seartch_did_info.dart';
import '../../../data/providers/api/did.dart';
import '../../../data/providers/api/oss.dart';
import '../../../data/services/biometrics_service.dart';
import '../../../data/services/config_service.dart';
import '../../../data/services/probe_service.dart';
import '../../../data/services/secure_store_service.dart';
import '../../account/account_service.dart';
import '../../did/did_list_view.dart';
import '../../message/file_help.dart';
import '../../password/pin/password_view.dart';

class MineController extends GetxController {
  var virtualCardVisible = VirtualCardStaticData.visible.obs;
  var versionName = "".obs;
  var netWorkNode = "".obs;
  var userIconPath = "".obs;
  var userLocalName = "".obs;
  var domain = ''.obs;
  var userName = "".obs;
  var expandHeight = 200.0.obs;
  var isBiometrics =
      (Get.find<AppConfigService>().biometricsOpen() ?? false).obs;
  var isLocalAgent =
      (Get.find<AppConfigService>().getLocalAgent() ?? false).obs;
  var rxBiometricsType = "".obs;
  RxString nftAdvanceNumber = Get.find<AppConfigService>().getIOIId().obs;
  final AppConfigService conf = Get.find();
  final List<StreamSubscription> _subscriptions = [];
  RxList<Widget> rxListViewDao = RxList<Widget>();
  RxString sbtId = ''.obs;
  RxBool sbtMore = false.obs;
  RxBool chatMiningOpened = true.obs;
  var isWalletOpen = (Get.find<AppConfigService>().isWalletOpen() ?? false).obs;
  RxInt tempSelectedLanguageIndex = 0.obs;
  RxBool hasNewVersion = false.obs;

  @override
  void onInit() async {
    super.onInit();
    AppLogger.d("onInit");
    _checkHasNewVersion();
    getNetWorkNetNode();
    userName.value = conf.getUserName() ?? '';
    _updateMyself();
    var d = Get.find<AppConfigService>().readSBTId();
    AppLogger.d('updateSbt d=$d');
    if (d?.isNotEmpty ?? false) {
      var data = <SearchBean>[];
      json.decode(d!).forEach((v) {
        data.add(SearchBean.fromJson(v));
      });
      if (data.isNotEmpty) {
        sbtId.value = data.first.sbt ?? Get.find<AppConfigService>().getSbtId();
        sbtMore.value = true;
        searchBeanList.value.addAll(data);
      }
    } else {
      sbtId.value = Get.find<AppConfigService>().getSbtId();
      sbtMore.value = false;
    }
    AppLogger.d('updateSbt sbtMore.value=${sbtMore.value}');
    StreamSubscription<Object> subscription;
    subscription = Config.nodeDefault.listen((p0) {
      getNetWorkNetNode();
    });
    _subscriptions.add(subscription);

    subscription =
        Get.find<EventBus>().on<ContactDataMyselfUpdateEvent>().listen((event) {
      _updateMyself();
    });
    _subscriptions.add(subscription);

    subscription =
        Get.find<EventBus>().on<MySelfAvatarUpdateEvent>().listen((event) {
      _updateMySelfAvatar(event);
    });
    _subscriptions.add(subscription);
    updateBiometricsType();

    subscription = Get.find<AppDatabase>()
        .proxyInfoByEnable(true)
        .watch()
        .listen((event) async {
      if (event.isNotEmpty) {
        ProxyUtil.instance.changeProxy(
          event.first.host,
          event.first.port,
          event.first.user ?? "",
          event.first.pwd ?? "",
          isHttp: event.first.isHttp ?? true,
        );
      } else {
        if(!ProxyUtil.instance.haveDefualtProxy){
          ProxyUtil.instance.changeProxy(
            null,
            null,
            "",
            "",
          );
        }

      }
    });
    _subscriptions.add(subscription);
    setDaoView();
    updateSbt();
  }

  @override
  void onClose() {
    AppLogger.d("onClose!!!");
    for (var element in _subscriptions) {
      element.cancel();
    }
    _subscriptions.clear();
    super.onClose();
  }

  String? isSpeak;

  // void loadTest() async {
    // AppLogger.d("isSpeak1=$isSpeak");
    // if (isSpeak!.isEmpty) {
    //   AppLogger.d("isSpeak2=$isSpeak");
    // }
    // AppLogger.d("isSpeak3=$isSpeak");
    // Get.toNamed(Routes.MiningPage);
  // }

  void _updateMyself() {
    String mySelfDisplayName = conf.getMySelfDisplayName().isEmpty
        ? conf.getPhone()
        : conf.getMySelfDisplayName();
    AvatarModel avatar = AvatarModel.fromJson(conf.getMySelfAvatarInfo() ?? {});
    AppLogger.d('_updateMyself==${avatar.path}');
    userLocalName.value = mySelfDisplayName;
    userIconPath.value = appSupporAbsolutePath(avatar.path) ?? "";
  }

  void _updateMySelfAvatar(MySelfAvatarUpdateEvent event) {
    var avatarModel = event.avatarModel;
    AppLogger.d("AvatarModel.path==${avatarModel.path}");
    userIconPath.value = appSupporAbsolutePath(avatarModel.path) ?? "";
    update();
  }

  // List<Widget> buildSetHeadBottomSheetItemWidgets(BuildContext context) {
  //   List<Widget> widgets = [
  //     /*getBottomSheetItemSimple(context, L.capture_picture.tr,
  //       itemCallBack: () => getImageTakePhoto()),*/
  //     /*getBottomSheetItemSimple(context, L.get_picture_from_phone.tr,
  //         itemCallBack: () => chooseImage()),*/
          
  //     getBottomSheetItemSimple(context, L.view_Avatar.tr, itemCallBack: () {
  //       showDialog(
  //         barrierDismissible: false,
  //         context: context,
  //         useSafeArea: false,
  //         // builder: (_) {
  //         //   return PhotosView(
  //         //     pathList: [userIconPath.value],
  //         //     currentIndex: 0,
  //         //   );
  //         // },
  //          builder: (_) {
  //      return Dialog(
  //         child: Stack(
  //             children: [
  //                    GestureDetector(
  //                 onDoubleTap: () {
  //                   Navigator.of(context).pop(); 
  //                 },
  //                 child: InteractiveViewer(
  //                   child: Image.file(
  //                     File(userIconPath.value),
  //                     fit: BoxFit.contain,
  //                   ),
  //                 ),
  //               ),
  //               Positioned(
  //                 top: 0,
  //                 right: 0,
  //                 child: IconButton(
  //                   color: Colors.grey, 
  //                   icon: Icon(Icons.close),
  //                   onPressed: () {
  //                     Navigator.of(context).pop(); 
  //                   },
  //                 ),
  //               ),
  //             ],
  //           ),
  //         );
  //         },
  //       );
  //     }),
    
    
  //   ];
  //    widgets.add(getBottomSheetItemSimple(context, L.edit_user_name.tr, itemCallBack: () {
  //       Get.to(const SetMySelfInfoPage());
  //   }));

 
  //   if (userIconPath.value.isNotEmpty) {
  //     widgets.add(getBottomSheetItemSimple(context, L.save_photo.tr,
  //         itemCallBack: () => AvatarTask.saveAvatar(userIconPath.value)));
  //   }
  //   return widgets;
  // }

  List<Widget> buildSetHeadBottomSheetItemWidgets(BuildContext context) {
  List<Widget> widgets = [];

  // 如果头像路径不为空，则添加“查看头像”和“保存头像”的选项
    widgets.add(
      getBottomSheetItemSimple(
        context, 
        L.view_Avatar.tr, 
        itemCallBack: () {
          showDialog(
            barrierDismissible: false,
            context: context,
            useSafeArea: false,
            // builder: (_) {
            //   return PhotosView(
            //     pathList: [userIconPath.value],
            //     currentIndex: 0,
            //   );
            // },
            builder: (_) {
              return Dialog(
                child: Stack(
                  children: [
                    GestureDetector(
                      onDoubleTap: () {
                        Navigator.of(context).pop();
                      },
                      child: InteractiveViewer(
                        child: ConstrainedBox(
                          constraints: BoxConstraints(
                            maxWidth: MediaQuery.of(context).size.width,
                            maxHeight: MediaQuery.of(context).size.height,
                          ),
                          child: Image.file(
                            File(userIconPath.value),
                            fit: BoxFit.contain,
                          ),
                        ),
                      ),
                    ),
                    Positioned(
                      top: 0,
                      right: 0,
                      child: IconButton(
                        color: Colors.grey,
                        icon: Icon(Icons.close),
                        onPressed: () {
                          Navigator.of(context).pop();
                        },
                      ),
                    ),
                  ],
                ),
              );
            },
          );
      }),
    );

  widgets.add(
    getBottomSheetItemSimple(context, L.edit_user_name.tr, itemCallBack: () {
      Get.to(const SetMySelfInfoPage());
    }),
  );

    widgets.add(
      getBottomSheetItemSimple(context, L.save_photo.tr, itemCallBack: () {
        AvatarTask.saveAvatar(userIconPath.value);
      }),
    );
  


  return widgets;
}


  getAdvanceNumber() {
    String number = Get.find<AppConfigService>().getIOIId();
    if (number.isNotEmpty) {
      nftAdvanceNumber.value = number;
    } else {
      nftAdvanceNumber.value = L.nft_advance_number_no.tr;
    }

  }

  getMySelfAvatarBySer() async {
    AvatarModel avatar = conf.getMySelfAvatarInfoModel();
    String? userName = await conf.getUserName();
    List<OwnInfoModelData>? ownList = await getOtherInfo([userName ?? ""]);
    if (ownList != null) {
      OwnInfoModelData own = ownList[0];
      String? avatarPath;
      if (own.avatar != null &&
              own.avatar != conf.getMySelfAvatarInfoModel().avatarUrl ||
          (avatar.path?.isEmpty ?? true) ||
          !File(appSupporAbsolutePath(avatar.path) ?? '').existsSync()) {
        String savePath = avatarSavePath(
            userName: conf.getUserName() ?? '',
            fileName: '${own.avatar?.split('/').last}');
        avatarPath = await downloadFile(own.avatar,
            savePath: savePath, isComPressImageJPG: true);
      }
      if (userLocalName.value != own.nickname) {
        conf.saveMySelfDisplayName(own.nickname ?? "");
      }
      if (avatarPath != null) {
        avatar.path = appSupporAbsolutePathToPath(avatarPath);
        avatar.avatarUrl = own.avatar;
        conf.saveMyselfAvatarInfo(avatar.toJson());
        userIconPath.value = avatarPath;
      }
      if (own.ioiID == null || own.ioiID!.isEmpty) {
        nftAdvanceNumber.value = L.nft_advance_number_no.tr;
      } else {
        nftAdvanceNumber.value = own.ioiID!;
      }
      if (own.nickname?.isNotEmpty ?? false) {
        userLocalName.value = own.nickname ?? "";
      }
      Get.find<AppConfigService>().saveIoiId(own.ioiID);
    }
  }

  double prevDy = 0.0;

  updatePicHeight(changed) {
    if (prevDy == 0.0) {
      //如果是手指第一次点下时，图片大小不直接变化，所以进行一个判定。
      prevDy = changed;
    }
  }

  void _checkHasNewVersion() async {
    if(DeviceUtil.isIOS()){
      AppVersionResult result = await AppVersionUpdate.checkForUpdates(appleId: Config.appleId);
      if(result.canUpdate ?? false){
        hasNewVersion.value = true;
      }
      return;
    }
    var appVersion = await DeviceUtil.getVersionCode();
    var latestVersion = await _getLatestAndroidVersion();
    if(latestVersion != null && latestVersion > appVersion){
      hasNewVersion.value = true;
    }
  }

  Future<int?> _getLatestAndroidVersion() async {
    try {
      var response = await Get.find<OperationCenterApiProvider>().updateAppVersion();
      if(response.statusCode != 200 || response.data == null) return null;
      if (response.data["code"] != 200) return null;
      var data = response.data["data"];
      if (data == null) return null;
      OTAUpdateEntity entity = OTAUpdateEntity.fromJson(response.data["data"]);
      return entity.versionCode;
    } catch (e) {
      AppLogger.e(e.toString());
      return null;
    }
  }

  Future<void> openAppStore() async {
    final Uri url = Uri.parse(Config.appStoreUrl);
    if (!await launchUrl(
      url,
      mode: LaunchMode.externalApplication, // 强制使用外部应用打开
    )) {
      throw Exception('无法打开 App Store 链接: $url');
    }
  }

  void getNetWorkNetNode() {
    DeviceUtil.getVersionNew().then((value) {
      versionName.value = value;
    });

    var tmp = Config.node();
    var subStart = tmp.lastIndexOf("/");
    if (subStart > -1) {
      tmp = tmp.substring(subStart + 1);
    }
    var subEnd = tmp.indexOf(":");
    if (subStart > -1) {
      tmp = tmp.substring(0, subEnd);
    }
    var n = tmp.split('.');
    if (n.isNotEmpty) {
      netWorkNode.value = n.first;
    } else {
      netWorkNode.value = tmp;
    }
    getDomain();
  }
  getDomain(){
    var dom = Config.getDomain();
    if(dom.isNotEmpty){
      var d = dom.split('.');
      if(d.isNotEmpty){
        domain.value = d.first;
      } else {
        domain.value = dom;
      }

    }
  }
  updateFirstNode () async {
    var myself = await geDomainInfo(Config.userNameWithoutDomain);
    AppLogger.d('updateDomain myself=$myself');
    if(myself?.name?.isNotEmpty??false) {
      var arrys = myself?.name?.split('@');
      if(arrys?.length==2){
        var d = arrys?.last;
        if (d != Config.getDomain()) {
          Get.find<AppConfigService>().saveDomain(d);
          getDomain();
          toast(L.node_update_success.tr);
          Future.delayed(const Duration(seconds: 2), () {
            exit(0);
          });
        } else {
          // toast('Nodes do not need to be updated');
        }

      }
    }

  }

  updateBiometricsType() async {
    await Get.find<BiometricsService>().configBiometricsType();
    rxBiometricsType.value = Get.find<BiometricsService>().biometricsType ?? "";
    AppLogger.d("rxBiometricsType.value==${rxBiometricsType.value}");
    if (rxBiometricsType.value.isEmpty) {
      setBiometricsOpen(false);
    }
  }

  setLocalAgent(bool isOpen) async {
    AppLogger.d(
        "getLocalAgent.value==${Get.find<AppConfigService>().getLocalAgent()}");
    ProxyUtil.instance.setDefaultProxy(clean: !isOpen);
    isLocalAgent.value = isOpen;
  }

  setBiometricsOpen(bool isOpen) async {
    if (isOpen) {
      String? walletPwd =
          await Get.find<SecureStoreService>().secureReadWalletPwd();
      if (walletPwd == null || walletPwd.isEmpty) {
        await SmartDialog.show(builder: (context) {
          return AlertDialog(
            content: Text(L.no_wallet_password_need_set_to_biometrics
                .trParams({"biometricsType": rxBiometricsType.value})),
            actions: [
              TextButton(
                child: Text(L.cancel.tr),
                onPressed: () => SmartDialog.dismiss(), //关闭对话框,
              ),
              TextButton(
                child: Text(L.module_activity_system_setting_button_text.tr),
                onPressed: () {
                  SmartDialog.dismiss();
                  Get.toNamed(Routes.WalletPwdReset);
                }, //关闭对话框,
              ),
            ],
            actionsAlignment: MainAxisAlignment.end,
          );
        });
      }
      /*else if(rxBiometricsType.value.isEmpty){
        Get.find<BiometricsService>().onBiometrics();
      }*/
      else {
        Get.find<AppConfigService>().saveBiometricsOpen(isOpen);
        isBiometrics.value = isOpen;
      }
    } else {
      Get.find<AppConfigService>().saveBiometricsOpen(isOpen);
      isBiometrics.value = isOpen;
    }
  }

  void setDaoView() {
    if (Config.daoViewConf == null) {
      return;
    }
    rxListViewDao.clear();
    if (Config.daoViewConf?.myOrder ?? false) {
      rxListViewDao.add(
        daoFunc(
          title: L.my_Order.tr,
          icon: R.icoDaoMyOrder,
          onTap: () {
            DaoBean bean = DaoBean(
              DaoJoinWebViewType.myOrder,
            );
            Get.toNamed(
              Routes.DaoBrowserView,
              arguments: bean,
            );
          },
        ),
      );
    }
    if (Config.daoViewConf?.createCenter ?? false) {
      rxListViewDao.add(
        daoFunc(
          title: L.create_center.tr,
          icon: R.icoDaoCreateCenter,
          onTap: () {
            DaoBean bean = DaoBean(
              DaoJoinWebViewType.createCenter,
            );
            Get.toNamed(
              Routes.DaoBrowserView,
              arguments: bean,
            );
          },
        ),
      );
    }
    if (Config.daoViewConf?.pointsManagement ?? false) {
      rxListViewDao.add(
        daoFunc(
          title: L.points_management.tr,
          icon: R.icoDaoPointsManagement,
          onTap: () {
            DaoBean bean = DaoBean(
              DaoJoinWebViewType.pointsManagement,
            );
            Get.toNamed(
              Routes.DaoBrowserView,
              arguments: bean,
            );
          },
        ),
      );
    }
    if (Config.daoViewConf?.pledge ?? false) {
      rxListViewDao.add(
        daoFunc(
          title: L.pledge.tr,
          icon: R.icoDaoPledge,
          onTap: () {
            DaoBean bean = DaoBean(
              DaoJoinWebViewType.pledge,
            );
            Get.toNamed(
              Routes.DaoBrowserView,
              arguments: bean,
            );
          },
        ),
      );
    }
    if (Config.daoViewConf?.myAccount ?? false) {
      rxListViewDao.add(
        daoFunc(
          title: L.my_account.tr,
          icon: R.icoDaoMyAccount,
          onTap: () {
            DaoBean bean = DaoBean(
              DaoJoinWebViewType.myAccount,
            );
            Get.toNamed(
              Routes.DaoBrowserView,
              arguments: bean,
            );
          },
        ),
      );
    }
  }

  Widget daoFunc(
      {required String title,
      required String icon,
      VoidCallback? onTap,
      bool isVisible = true}) {
    return Visibility(
      visible: isVisible,
      child: GestureDetector(
        onTap: () {
          onTap?.call();
        },
        child: Container(
          constraints: BoxConstraints(minWidth: 50.w),
          height: 70.h,
          color: Colors.transparent,
          alignment: Alignment.center,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              Image.asset(
                icon,
                width: 27.r,
                height: 27.r,
              ),
              SizedBox(
                height: 10.h,
              ),
              Text(
                title,
                style:
                    TextStyle(color: AppColors.colorFF333333, fontSize: 12.sp),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void onBuckup({bool isLocal = false}) async {
    // if (!rxFileHelperOnline.value) {
    //   toast(L.file_assistant_not_online.tr);
    //   return;
    // }
    BackupManage.showBackupWidget(isLocal: isLocal);
  }

  mnemonicExport() async {
    bool ret = false;
    var hasSetPin = Get.find<AppConfigService>().hasSetPin() ?? false;
    if (!hasSetPin) {
      bool? goSet = await showConfirmationDialog(L.no_pin_set_need_set.tr, L.module_activity_system_setting_button_text.tr);
      if (goSet ?? false) {
        ret = await Get.to(const PinSetDetailView()) ?? false;
      }
    } else {
      var result = await showEnterPinDialog();
      ret = result != null;
    }
    if (ret) {
      Map<String, dynamic> map = {};
      map[AccountService.mnemonicTAG] = Config.mnemonic;
      map[AccountService.mnemonicViewTypeTAG] = MnemonicViewType.export;
      Get.toNamed(Routes.MnemonicPageAfter, arguments: map);
    }
  }

  updateLocalProxy() {
    isLocalAgent.value = Get.find<AppConfigService>().getLocalAgent();
  }

  int? languageCode;

  String changeLanguage() {
    var languageList = TranslationService().languageNameList;
    languageCode = Get.find<AppConfigService>().readMultiLanguage();
    return languageList[languageCode ?? 0].tr;
  }

  void languageChanged(int index) async {
    Get.back();
    if (languageCode != index) {
      await Get.find<AppConfigService>().saveMultiLanguage(index);
      changeLanguage();
      Config.isLanguageChanged = true;
      LanguageUtil.updateLanguage();
      Future.delayed(const Duration(milliseconds: 1000), () {
        Config.isLanguageChanged = false;
      });
    }
  }

  void onSelectLanguage(int index) {    
    setTempSelectedLanguageIndex(index);    
  }

  void setTempSelectedLanguageIndex(int? index) {
    if(index == null) {
      tempSelectedLanguageIndex.value == 0;
    } else {
      tempSelectedLanguageIndex.value = index;
    }    
  }

  var searchBeanList = RxList<SearchBean>().obs;

  updateSbt() async {
    var res = await Get.find<DidApi>().searchSbtDidByAccount(
        {'account': Get.find<AppConfigService>().getUserNameWithoutDomain()});
    if (res.body?.code == 200) {
      searchBeanList.value.clear();
      if (res.body?.data?.isNotEmpty ?? false) {
        var list = res.body?.data;
        if (list?.isNotEmpty ?? false) {
          list?.removeWhere((element) {
            var s = (element.status ?? 0) >= 90;
            return s;
          });
        }
        sbtMore.value = list?.isNotEmpty ?? false;
        if (list?.isNotEmpty ?? false) {
          sbtId.value = list?.first.sbt ?? '';
          searchBeanList.value.addAll(list!);
        } else {
          sbtId.value = Get.find<AppConfigService>().getSbtId();
          sbtMore.value = false;
        }
      } else {
        sbtId.value = Get.find<AppConfigService>().getSbtId();
        sbtMore.value = false;
      }
    }
    if (sbtMore.value && searchBeanList.value.isNotEmpty) {
      await Get.find<AppConfigService>()
          .saveSBTId(json.encode(searchBeanList.value.toJson()));
    }
  }

  void setChatMiningOpened() {
    Get.find<AppConfigService>().saveChatMiningVerification(chatMiningOpened.value);
    Get.find<AppConfigService>().saveMiningValidityPeriod(null);
    Get.find<AppConfigService>().saveAuthenticationInterval(null);
  }

  void updateChatMiningOpened() {
    chatMiningOpened.value = Get.find<AppConfigService>().readChatMiningVerification() ?? true;
  }

  gotoDidList() {
    if (searchBeanList.value.isNotEmpty) {
      Get.toNamed(Routes.DidListPage);
    }
  }

  storageOptimization() async {
    showLoadingDialog(text: L.please_wait_later.tr);
    await deleteFileWhereNotMessageJoined();
    dismissLoadingDialog();
  }

  setHasActivateWallet(bool isActivate){
    Get.find<AppConfigService>().saveHasActivateWallet(isActivate);
  }

  void onActivateWalletChanged(bool value) async {
    var hasSetPin = Get.find<AppConfigService>().hasSetPin() ?? false;
    var hasActivateWallet = Get.find<AppConfigService>().hasActivateWallet() ?? false;
    if(hasActivateWallet){ // 曾经激活启用钱包      
      if (!hasSetPin) {
        bool? goSet = await showConfirmationDialog(L.no_pin_set_activate_wallet_need_set.tr, L.module_activity_system_setting_button_text.tr);
        if (!(goSet ?? false)) return;
        var setPinResult = await Get.to(const PinSetDetailView()) ?? false;
        if(!setPinResult) return;
      }
      var enterPinResult = await showEnterPinDialog(); // 新设置好PIN 或 已有设置PIN
      if(enterPinResult == null) return;
      if(!value) { // 关闭钱包需弹窗确认
        bool? confirmCloseWallet = await showConfirmationDialog(L.close_wallet_confirmation_desc.tr,L.confirm.tr);
        if (!(confirmCloseWallet ?? false)) return;
      }
    } else {  // 未曾激活启用钱包(第一次激活)      
      if(hasSetPin){  // 有设置PIN码，输入PIN码        
        var result = await showEnterPinDialog();
        if(result == null) return;        
      }
      // 没设置PIN码 或 密码正确，验证助记词
      Map<String, dynamic> map = {};
      map[AccountService.mnemonicTAG] = Config.mnemonic;
      map[AccountService.mnemonicViewTypeTAG] = MnemonicViewType.export;
      var result = (await Get.toNamed(Routes.MnemonicBackupView, arguments: map) as bool?) ?? false;      
      if(!result) return;
    }

    if(value && !hasActivateWallet) { // 第一次激活钱包
      Get.find<AppConfigService>().saveHasActivateWallet(true);
    }    
    Get.find<AppConfigService>().saveIsWalletOpen(value); // 本地保存“钱包开关”设置
    Get.find<HomeController>().rebuildHomeNavigation(value);  // 通知Home, 更新NavBar与页面       
    isWalletOpen.value = value; // 通知本页面，更新开关    
  }

  resetWalletOpen() {
    isWalletOpen.value = (Get.find<AppConfigService>().isWalletOpen() ?? false);
  }

  Future showEnterPinDialog() async {
    return await SmartDialog.show(
      builder: (context) {
        return const PasswordView(
          canDestruction: false,
          isDialog: true,
          canBack: true,
        );
      },
      useAnimation: false,
      backDismiss: true,
      clickMaskDismiss: false,
      keepSingle: true,
    );
  }

  Future<bool?> showConfirmationDialog(String dialogDesc, String positiveBtnText) async{
    return await SmartDialog.show(builder: (context) {
      return AlertDialog(
        content: Text(dialogDesc),
        actions: [
          TextButton(
            child: Text(L.cancel.tr),
            onPressed: () => SmartDialog.dismiss(), //关闭对话框,
          ),
          TextButton(
            child: Text(positiveBtnText),
            onPressed: () async {
              SmartDialog.dismiss(result: true);
            }, //关闭对话框,
          ),
        ],
        actionsAlignment: MainAxisAlignment.end,
      );
    });
  }

}
