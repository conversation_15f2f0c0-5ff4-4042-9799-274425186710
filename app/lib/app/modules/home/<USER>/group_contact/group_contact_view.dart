/*
 * <AUTHOR> <PERSON><PERSON>
 * @Date         : 2022-05-18 15:13:34
 * @Description  : 
 * @LastEditors  : <PERSON><PERSON>
 * @LastEditTime : 2022-06-14 19:02:23
 * @FilePath     : /flutter_metatel/lib/app/modules/home/<USER>/contact_view.dart
 */
import 'package:extended_text/extended_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/data/enums/enum.dart';
import 'package:flutter_metatel/app/data/models/channel_info_model_data.dart';
import 'package:flutter_metatel/app/data/models/user_message_model.dart';
import 'package:flutter_metatel/app/data/providers/api/channel.dart';
import 'package:flutter_metatel/app/data/providers/db/database.dart';
import 'package:flutter_metatel/app/modules/home/<USER>/appbar.dart';
import 'package:flutter_metatel/app/modules/home/<USER>/home_appbar_base.dart';
import 'package:flutter_metatel/app/widgets/at_widget/my_special_text_span_builder.dart';
import 'package:flutter_metatel/core/languages/l.dart';
import 'package:flutter_metatel/core/utils/jump.dart';
import 'package:flutter_metatel/core/utils/util.dart';
import 'package:flutter_metatel/core/values/config.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:get/get.dart';

class GroupContactView extends StatefulWidget {
  final bool isGroup;
  const GroupContactView({Key? key, this.isGroup = false}) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _GroupContactStatePage();
  }
}

class _GroupContactStatePage extends State<GroupContactView> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return createAppBar(
      title: widget.isGroup ? L.main_my_group.tr : L.main_my_channel.tr,
      type: SearchType.contacts,
      canSearch: !Config.isOversea,
      showTitle: Config.isOversea,
      body: widget.isGroup ? _buildGroupList() : _buildChannelList(),
    );
  }

  _buildChannelList() {
    double avatarDiameter = 40.w;
    return FutureBuilder<List<ChannelInfoModelData>?>(
      future: getChannelsRequest(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return Container(
            child: Text(L.load_one.tr),
          );
        }
        if (snapshot.hasError) {
          return Container(
            child: Text(L.help_load_fail_repeat_again.tr +
                ':' +
                snapshot.error.toString()),
          );
        }
        if (snapshot.hasData) {
          if (snapshot.data!.isEmpty) {
            return buildNoData();
          }
          return ListView.builder(
            itemCount: snapshot.data!.length,
            itemBuilder: (context, index) {
              var item = snapshot.data![index];
              return _buildHeaderItem(
                item.title ?? '',
                onTap: () {
                  var userMessage = UserMessage(
                    chatType: ChatType.channelChat.index,
                    displayName: item.title ?? '',
                    userName: item.id,
                    isFriend: true,
                    avatarPath: item.avatar,
                  );

                  JumpPage.messgae(userMessage);
                },
                avatar: buildChatAvatarWithAttr(
                  ChatType.channelChat.index,
                  item.id ?? '',
                  diameter: avatarDiameter,
                  imagePath: item.avatar,
                  isNet: true,
                ),
              );
            },
          );
        }
       return buildNoData();
      },
    );
  }

  _buildGroupList() {
    double avatarDiameter = 40.w;
    return FutureBuilder<List<GroupInfoData>?>(
      future: Get.find<AppDatabase>().allGroupInfo().get(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return Container(
            child: Text(L.load_one.tr),
          );
        }
        if (snapshot.hasError) {
          return Container(
            child: Text(L.help_load_fail_repeat_again.tr +
                ':' +
                snapshot.error.toString()),
          );
        }
        if (snapshot.hasData) {
          if (snapshot.data!.isEmpty) {
            return buildNoData();
          }
          return ListView.builder(
            itemCount: snapshot.data!.length,
            itemBuilder: (context, index) {
              var item = snapshot.data![index];
              return _buildHeaderItem(
                item.title ?? '',
                onTap: () {
                  var userMessage = UserMessage(
                    chatType: ChatType.groupChat.index,
                    displayName: item.title ?? '',
                    userName: item.groupId,
                    isFriend: true,
                    avatarPath: item.avatarPath,
                  );

                  JumpPage.messgae(userMessage);
                },
                avatar: buildChatAvatarWithAttr(
                  ChatType.groupChat.index,
                  item.groupId,
                  diameter: avatarDiameter,
                  imagePath: item.avatarPath,
                  isNet: true,
                ),
              );
            },
          );
        }
        return buildNoData();
      },
    );
  }

  _buildHeaderItem(String title, {Function()? onTap, Widget? avatar}) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 10.h),
        child: Row(
          children: [
            avatar ?? SizedBox.shrink(),
            SizedBox(width: 10.w),
            ExtendedText(
              title,
              overflow: TextOverflow.ellipsis,
              style: TextStyle(
                color: Colors.black,
                fontSize: 16.sp,
              ),
              specialTextSpanBuilder: MySpecialTextSpanBuilder(
                  showAtBackground: false, size: Size(15.r, 15.r)),
            ),
          ],
        ),
      ),
    );
  }
}
