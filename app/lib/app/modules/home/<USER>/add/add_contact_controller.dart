/*
 * @Author: <PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2022-05-06 18:21:18
 * @LastEditors: luoyuan <EMAIL>
 * @LastEditTime: 2022-05-07 14:42:46
 * @FilePath: \flutter_metatel\lib\app\modules\contactor\add_contact_page.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */

import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/data/providers/db/database.dart';
import 'package:flutter_metatel/core/task/time_task.dart';
import 'package:get/get.dart';

import '../../../../../core/languages/l.dart';
import '../../../../../core/task/pc_operate_task.dart';
import '../../../../../core/task/session_task.dart';
import '../../../../../core/utils/events_bus.dart';
import '../../../../../core/utils/util.dart';
import '../../../../../core/values/config.dart';
import '../../../../data/events/events.dart';

class AddContactController extends GetxController {
  /// 添加点击
  Future onAddPressed(String userName,String localName,String? displayName) async {
    if (localName.isEmpty) {
      toast(L.chat_info_nickname_not_allowed_empty_or_blank.tr,
          textColor: Colors.red);
      return;
    }
    var database = Get.find<AppDatabase>();
    ContactData? data=await database.oneContact(userName).getSingleOrNull();
    double time = TimeTask.instance.getNowTime().toDouble();

    double? createTime;
    if (data == null) {
      createTime = time;
    }
    bool edit = displayName != localName;
    var companion = ContactCompanion.insert(
      username: userName,
      displayname: ofNullable(displayName),
      localname: ofNullable(localName),
      read: ofNullable(false),
      state: ofNullable(0),
      edit: ofNullable(edit),
      createTime: ofNullable(createTime),
      updateTime: ofNullable(time),
    );
    await database.insertOrUpdateContactData(companion);
    ContactData tmp = ContactData(
        id: 0,
        username: userName,
        displayname: displayName,
        localname: localName,
        edit: edit,
        read: false,
        state: 0);
    SessionTask.name(userName, displayname: localName);
    Get.find<EventBus>().fire(ContactDataUpdateEvent(tmp));
    Get.find<EventBus>().fire(MiningTaskEvent(MiningTaskType.MINING_TASK_ADD_CONTACTOR));
    PcOperateTask().sendContactorToPc();

  }
}
