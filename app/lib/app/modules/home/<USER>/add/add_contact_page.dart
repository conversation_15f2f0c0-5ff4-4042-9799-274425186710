/*
 * @Author: <PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2022-05-06 18:21:18
 * @LastEditors: luoyuan <EMAIL>
 * @LastEditTime: 2022-05-07 14:42:46
 * @FilePath: \flutter_metatel\lib\app\modules\contactor\add_contact_page.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */

import 'package:flutter/material.dart';
import 'package:flutter_metatel/core/languages/l.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:flutter_metatel/core/utils/util.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../../core/utils/jump.dart';
import '../../../../../core/values/config.dart';
import '../../../../data/enums/enum.dart';
import '../../../../data/models/own_info_model.dart';
import '../../../../data/models/user_message_model.dart';
import '../../../../data/providers/api/own.dart';
import '../../../../widgets/divider_cus.dart';
import '../../../../widgets/mavatar_circle_avatar.dart';
import 'add_contact_controller.dart';

class AddContactPage extends StatefulWidget {
  const AddContactPage({
    Key? key,
    required this.userName,
    required this.displayName,
    this.avatarPath,
    this.toMessagePage, //添加完成是否进入其聊天界面
  }) : super(key: key);

  final String userName;
  final String displayName;
  final String? avatarPath;
  final bool? toMessagePage;

  @override
  State<AddContactPage> createState() => _AddContactPageState();
}

class _AddContactPageState extends State<AddContactPage> {
  final FocusNode _focusNode = FocusNode();
  final TextEditingController _localNameController = TextEditingController();
  final AddContactController controller=Get.put(AddContactController());

  @override
  void initState() {
    super.initState();
    _focusNode.addListener(() {
      if (!_focusNode.hasFocus) {
        AppLogger.d("!hasFocus");
      } else {
        AppLogger.d("hasFocus");
        if (_localNameController.text.isEmpty) {
          setState(() {
            _localNameController.text = widget.displayName;
          });
        }
      }
    });
  }

  @override
  void dispose() {
    _localNameController.dispose();
    _focusNode.unfocus();
    // _lastController.dispose();
    super.dispose();
  }



  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        // 标题
        title: Text(
          L.main_add_friend.tr,
          style: const TextStyle(
            color: Colors.black,
            fontSize: 16,
            fontWeight: FontWeight.w800,
          ),
        ),
        centerTitle: true,
        // 右侧按钮
        actions: [
          TextButton(
              onPressed: () async {
                String localName = (_localNameController.text.isEmpty?widget.displayName:_localNameController.text).trim();
                await controller.onAddPressed(widget.userName,localName,widget.displayName);
                Get.back();
                if(widget.toMessagePage??false){
                  var userMessage = UserMessage(
                      chatType: ChatType.singleChat.index,
                      displayName:widget.displayName,
                      userName: widget.userName,
                      avatarPath: null);
                  JumpPage.messgae(userMessage);
                }
              },
              child: Text(L.confirm.tr)),
        ],
      ),
      body: Container(
        color: Colors.white,
        child: Column(
          children: [
            // 头像名称部分
            Container(
              height: 75.h,
              padding: const EdgeInsets.only(left: 30, right: 30).r,
              child: Row(
                children: [
                  (widget.avatarPath == null || widget.avatarPath!.isEmpty)
                      ? FutureBuilder(
                          future: getOtherInfo([widget.userName]),
                          builder: (_, snapshot) {
                            if (snapshot.connectionState ==
                                ConnectionState.done) {
                              if (snapshot.data != null) {
                                List<OwnInfoModelData> data = snapshot.data!;
                                OwnInfoModelData own = data[0];
                                AppLogger.d("avatar url own==${own.avatar}");
                                return MAvatarCircle(
                                  diameter: 49.w,
                                  isNet: (own.avatar != null &&
                                          own.avatar!.isNotEmpty),
                                  imagePath: own.avatar,
                                  text: widget.displayName,
                                );
                              } else {
                                return MAvatarCircle(
                                  diameter: 49.w,
                                  imagePath:
                                      appSupporAbsolutePath(widget.avatarPath),
                                  text: widget.displayName,
                                );
                              }
                            } else {
                              return MAvatarCircle(
                                diameter: 49.w,
                                imagePath:
                                    appSupporAbsolutePath(widget.avatarPath),
                                text: widget.displayName,
                              );
                            }
                          },
                        )
                      : MAvatarCircle(
                          diameter: 49.w,
                          imagePath: appSupporAbsolutePath(widget.avatarPath),
                          text: widget.displayName,
                        ),
                  const SizedBox(width: 10),
                  Expanded(
                    child: Text(
                      widget.displayName,
                      style: TextStyle(fontSize: 16.sp),
                    ),
                  ),
                ],
              ),
            ),
            // 间隔
            Container(height: 20, color: const Color(0xffF3F3F3)),
            // 名输入框
            Container(
              height: 43.5.h,
              padding: const EdgeInsets.only(left: 30, right: 30).r,
              child: Row(
                children: [
                  Text(
                    '${L.nick_name.tr}:',
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w500,
                      color: const Color.fromARGB(155, 51, 51, 51),
                    ),
                  ),
                  Expanded(
                    child: TextField(
                      focusNode: _focusNode,
                      textAlignVertical: TextAlignVertical.center,
                      style: TextStyle(fontSize: 17.sp),
                      controller: _localNameController,
                      decoration: InputDecoration(
                          counterText: '',
                          contentPadding: const EdgeInsets.only(left: 5).r,
                          border: const OutlineInputBorder(
                            borderSide: BorderSide.none,
                          ),
                          hintText:widget.displayName,
                      ),
                      maxLength: 20,
                    ),
                  ),
                ],
              ),
            ),
            const DividerCus(),
            // 姓输入框
            // Container(
            //   height: 43.5.h,
            //   padding: const EdgeInsets.only(left: 30, right: 30),
            //   child: Row(
            //     children: [
            //       Text(
            //         L.chat_contact_note_last_name.tr + ':',
            //         style: TextStyle(
            //           fontSize: 16.sp,
            //           fontWeight: FontWeight.w500,
            //           color: const Color.fromARGB(155, 51, 51, 51),
            //         ),
            //       ),
            //       Expanded(
            //         child: TextField(
            //           controller: _lastController,
            //           textAlignVertical: TextAlignVertical.center,
            //           style: TextStyle(fontSize: 17.sp),
            //           decoration: const InputDecoration(
            //             counterText: '',
            //             contentPadding: EdgeInsets.only(left: 5),
            //             border: OutlineInputBorder(
            //               borderSide: BorderSide.none,
            //             ),
            //           ),
            //           maxLength: 20,
            //         ),
            //       ),
            //     ],
            //   ),
            // ),
            // const Divider(height: 1, color: Color(0xffE6E6E6)),
          ],
        ),
      ),
    );
  }
}
