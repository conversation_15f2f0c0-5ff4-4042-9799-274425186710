import 'dart:convert';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_metatel/app/data/services/config_service.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:pretty_qr_code/pretty_qr_code.dart';
import 'package:share_plus/share_plus.dart';

import '../../../../../core/languages/l.dart';

import '../../../../../core/task/time_task.dart';
import '../../../../../core/utils/save_image_to_photo.dart';
import '../../../../../core/utils/util.dart';
import '../../../../../core/values/colors.dart';
import '../../../../../r.dart';
import '../../../../data/providers/api/invite.dart';

class SharePoster extends StatefulWidget {
  const SharePoster({
    super.key,
    // required this.url,
    // required this.shareLink,
    // required this.onRefresh,
  });

  // final Rx<String> url;
  // final String shareLink;
  // final ValueChanged<dynamic> onRefresh;

  @override
  State<SharePoster> createState() => _SharePosterState();
}

class _SharePosterState extends State<SharePoster> {
  String inviteCode = '';
  final GlobalKey key = GlobalKey();
  ByteData? imageBytes;
  bool isReferrerQrShow = true;

  @override
  void initState() {
    getInviteCode();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      body: _buildBody(),
    );
  }

  AppBar _buildAppBar() {
    return AppBar(
      backgroundColor: AppColors.white,
      centerTitle: true,
      title: Text(
        isReferrerQrShow ? L.invitation.tr : L.download.tr,
        style: TextStyle(fontWeight: FontWeight.bold, fontSize: 15.sp),
      ),
      actions: [
        Row(
          children: [
            TextButton(
              onPressed: () {
                if(isReferrerQrShow) {
                  _share();
                } else {
                  showBottomDialogCommonWithCancel(
                    Get.context!,
                    widgets: [
                      getBottomSheetItemSimple(
                        Get.context!, 
                        L.share_link.tr,
                        itemCallBack: () => _shareDownloadUrl(),
                      ),
                      getBottomSheetItemSimple(
                        Get.context!, 
                        L.share_image.tr,
                        itemCallBack: () => _share(),
                      ),
                    ],
                  );
                }
              },
              child: Text(
                L.share.tr,
                style: TextStyle(
                  fontSize: 14.sp,
                  color: AppColors.primaryBgColor1,
                ),
              ),
            ),
            SizedBox(width: 10.r),
          ],
        ),
      ],
    );
  }

  Widget _buildBody() {
    return Container(
      width: 1.sw,
      height: 1.sh,
      color: AppColors.colorFFEFF4F7,
      child: SingleChildScrollView(
        physics: const BouncingScrollPhysics(),
        child: RepaintBoundary(
          key: key,
          child: Container(
            // height: 1.sh,
            width: 1.sw,
            constraints: BoxConstraints(minHeight: 1.sh),
            padding: EdgeInsets.symmetric(horizontal: 35.r),
            decoration: BoxDecoration(
              image: DecorationImage(
                image: AssetImage(R.invitationBg),
                fit: BoxFit.cover,
              ),
            ),
            child: Column(
              children: [
                SizedBox(height: 30.r),
                _buildInfoSection(),
                SizedBox(height: 30.r),
                _buildInvitationCodeSection(),
                SizedBox(height: 30.r),
                _buildDownloadInfoSection(),
                SizedBox(height: 20.r),
                _buildSwitchButton(),
                // _buildShareSection(),
                SizedBox(
                  height: 40.r,
                ),
                // _buildActionSection(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildInfoSection() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          "LINKSAY ${L.mission.tr}",
          style: TextStyle(
            fontSize: 13.sp,
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(
          width: 30.r,
          child: Divider(
            thickness: 2,
            color: Colors.black,
          ),
        ),
        SizedBox(height: 6.r),
        Text(
          "Create a decentralized autonomous economy that is fair, just, co-created, shared, and collaboratively governed.",
          textAlign: TextAlign.center,
          style: TextStyle(
            fontSize: 10.sp,
            fontWeight: FontWeight.w500,
            height: 1.2,
          ),
        ),
        SizedBox(height: 5.r),
        Text(
          "打造一个公平、公正、共创、共享、共治的去中心化自治经济体",
          textAlign: TextAlign.center,
          style: TextStyle(
            fontSize: 10.sp,
            fontWeight: FontWeight.w500,
            height: 1.2,
          ),
        ),
      ],
    );
  }

  Widget _buildInvitationCodeSection() {
    return Container(
      height: 380.r,
      decoration: BoxDecoration(
        image: DecorationImage(image: AssetImage(R.invitationBgTicket)),
      ),
      child: Center(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Image.asset(
              R.invitationLinksayLogo,
              height: 30.r,
            ),
            SizedBox(height: 20.r),
            Text(
              isReferrerQrShow ? "Invitation Code" : "Download",
              style: TextStyle(
                fontSize: 15.sp,
                fontWeight: FontWeight.w500,
                color: Colors.black,
              ),
            ),
            Text(
              isReferrerQrShow ? "邀请码" : "下载",
              style: TextStyle(
                fontSize: 14.sp,
                fontWeight: FontWeight.w600,
                color: Colors.black,
              ),
            ),
            SizedBox(height: 20.r),

            /// QR码
            _buildQr(qrData: isReferrerQrShow ? _generateInvitationQrData() : _generateDownloadQrData()),
            SizedBox(height: 3.r),

            /// 邀请码
            if (isReferrerQrShow)
              Text(
                inviteCode,
                style: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w500,
                  color: Colors.black,
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildDownloadInfoSection() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Text(
          "${L.download_and_explore_now.tr}!",
          style: TextStyle(
            fontSize: 14.sp,
            fontWeight: FontWeight.w500,
            color: Colors.black,
          ),
        ),
        SizedBox(height: 6.r),
        Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Image.asset(
              R.invitationAppleLogo,
              width: 23.r,
              height: 23.r,
            ),
            SizedBox(width: 6.r),
            Image.asset(
              R.invitationGoogleLogo,
              width: 23.r,
              height: 23.r,
            ),
            SizedBox(width: 6.r),
            Image.asset(
              R.invitationAndroidLogo,
              width: 23.r,
              height: 23.r,
            ),
            SizedBox(width: 6.r),
            Image.asset(
              R.invitationWindowsLogo,
              width: 23.r,
              height: 23.r,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildQr({required String qrData}) {
    return Container(
      width: 110.r,
      height: 110.r,
      child: PrettyQrView.data(
        data: qrData,
        decoration: const PrettyQrDecoration(
          shape: PrettyQrSmoothSymbol(
            roundFactor: 0,
            color: PrettyQrBrush.gradient(
              gradient: LinearGradient(
                colors: [
                  AppColors.primaryBgColor2,
                  AppColors.primaryBgColor1,
                ],
                begin: Alignment(-1, -1),
                end: Alignment(1, 1),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSwitchButton() {
    return ElevatedButton(
      onPressed: () {
        isReferrerQrShow = !isReferrerQrShow;
        setState(() {});
      },
      style: ElevatedButton.styleFrom(
        backgroundColor: AppColors.white,
        fixedSize: Size(0.6.sw, 30.r),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Image.asset(
            R.btnSwitchIcon,
            width: 15.r,
            height: 15.r,
          ),
          SizedBox(width: 10.r),
          Text(
            isReferrerQrShow ? L.btn_download_code.tr : L.btn_invitation_code.tr,
            style: TextStyle(
              fontSize: 13.sp,
              color: AppColors.primaryBgColor1,
            ),
          ),
        ],
      ),
    );
  }

  _share() async {
    imageBytes = await RepaintBoundaryUtils.savePhotoByGlobalKey(key);
    if (imageBytes == null) return;
    Uint8List image = imageBytes!.buffer.asUint8List(); // ByteData to List<int>
    var millisecondsSinceEpoch = TimeTask.instance.getNowDateTime().millisecondsSinceEpoch;
    var path = appTempAbsolutePath("${L.app_name.tr}-${L.share.tr}-$millisecondsSinceEpoch.png");
    if (path == null) return;
    final file = File(path);
    await file.writeAsBytes(image); // Write to temp file
    var result = await Share.shareXFiles([XFile(path)]);
    if (result.status == ShareResultStatus.success) {
      AppLogger.d('Thank you for sharing the picture!');
    }
  }

  _shareDownloadUrl() async {
    var result = await Share.share(_generateDownloadQrData());
  }

  void getInviteCode() async {
    final _appConfigService = Get.find<AppConfigService>();
    var readInviteCode = _appConfigService.readInviteCode();
    if (readInviteCode.isEmpty) {
      var userNameWithoutDomain = _appConfigService.getUserNameWithoutDomain();
      readInviteCode = (await Get.find<InviteApi>().getSharePoster(userNameWithoutDomain)).data?.data?.inviteCode ?? "";
    }
    inviteCode = readInviteCode;
    setState(() {});
  }

  String _generateDownloadQrData() {
    return 'https://linksaychat.com/download';
  }

  String _generateInvitationQrData() {
    AppConfigService conf = Get.find();

    String? userName = conf.getUserName();
    Map<String, dynamic> mapJson = {};
    mapJson['firstName'] = '';
    mapJson['lastName'] = '';
    mapJson['nick'] = conf.getMySelfDisplayName();
    mapJson['number'] = userName ?? '';
    mapJson['type'] = '4';

    String qrcode = json.encode(mapJson);
    return qrcode;
  }
}
