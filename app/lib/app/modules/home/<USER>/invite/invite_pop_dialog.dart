import 'package:extended_image/extended_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_metatel/core/utils/util.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:get/get.dart';

import '../../../../../core/languages/l.dart';
import '../../../../../core/values/Config.dart';
import '../../../../../core/values/colors.dart';
import '../../../../../r.dart';
import '../../../../../routes/pages.dart';
import 'invite_bind_recommender.dart';

class InvitePopDialog extends StatefulWidget {
  const InvitePopDialog({super.key,this.editingController, this.canCancel=true});
  final TextEditingController? editingController;
  final bool canCancel;

  @override
  State<InvitePopDialog> createState() => _InvitePopDialogState();
}

class _InvitePopDialogState extends State<InvitePopDialog> {
  var binding=false.obs;
  @override
  Widget build(BuildContext context) {
    return Container(
      color: AppColors.transparent,
      padding: EdgeInsets.only(left: 30.r, right: 30.r),
      alignment: Alignment.center,
      child: Center(
        child:Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            SizedBox(
              child: Stack(children: [InviteBindRecommender(canCancel: false,controller: widget.editingController!,),
                Positioned(
                  left: 0,
                  right: 0,
                  bottom: 15.h,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SizedBox(
                        width: 16.w,
                      ),
                      ElevatedButton(
                        onPressed: binding.value?null:() async {
                          if (widget.editingController != null) {
                            binding.value=true;

                          }
                        },
                        style: ElevatedButton.styleFrom(
                          fixedSize: Size(180.r,44.r),
                        ),
                        child: Text(
                          L.confirm.tr,
                          style: TextStyle(fontSize: 16.sp, color: AppColors.white),
                        ),
                      ),

                    ],
                  ),
                ),
              ]),
            ),
            SizedBox(height: 32.h,),
            Visibility(
              visible: widget.editingController == null||widget.canCancel,
              child: IconButton(
                onPressed: () {
                  SmartDialog.dismiss();
                },
                icon: Image.asset(R.icoCloseTrans,width: 25.r,height: 25.r,),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
