import 'dart:io';

import 'package:async_task/async_task_extension.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/data/providers/api/own.dart';
import 'package:flutter_metatel/app/data/providers/db/database.dart';
import 'package:flutter_metatel/app/data/services/config_service.dart';
import 'package:flutter_metatel/app/modules/home/<USER>/invite/share_poster.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:flutter_metatel/core/values/colors.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:get/get.dart';

import '../../../../../core/utils/util.dart';
import '../../../../../routes/pages.dart';
import '../../../../data/models/own_info_model.dart';
import '../../../../data/models/res/invite/invite_info_model_list_res.dart';
import '../../../../data/providers/api/invite.dart';
import 'invite_bind_recommender.dart';

class InviteController extends GetxController {
  var number = 0.obs;
  var status = (-1).obs;
  var recommender = OwnInfoModelData(name: "").obs;
  var inviteCode = "".obs;
  TextEditingController recommenderTec = TextEditingController();
  final api = Get.find<InviteApi>();
  final conf = Get.find<AppConfigService>();
  RxList<InviteInfoListDataModel> dataList = RxList();
  late int curPage;
  late int lastPage;
  late int pageSize;
  late int pageNumber;
  late ScrollController scrollController;
  var appDatabase = Get.find<AppDatabase>();
  @override
  void onInit() {
    scrollController = ScrollController();
    scrollController.addListener(() {
      AppLogger.d(
          'DaoController  pixels===${scrollController.position.pixels}');
      AppLogger.d(
          'DaoController  maxScrollExtent===${scrollController.position.maxScrollExtent}');
      if (scrollController.position.pixels ==
          scrollController.position.maxScrollExtent) {
        if (curPage <= lastPage) {
          pageNumber++;
          AppLogger.d(
              'DaoController  scrollController _onLoadMore pageNumber==$pageNumber lastpage==$lastPage');
          _onLoadMore();
        }
      }
    });
    _getInviteCode();
    super.onInit();
  }

  onRefresh() {
    AppLogger.d("onRefresh!!!");
    curPage = 1;
    lastPage = 1;
    pageSize = 10;
    pageNumber = 1;
    _getInviteInfo();
    _onLoadMore(isRefresh: true);
  }

  _getInviteInfo() async {
    var userNameWithoutDomain = conf.getUserNameWithoutDomain();
    var res = await api.inviteInfo(userNameWithoutDomain);
    dismissLoadingDialog();
    var inviteUserName = res.data?.data?.userName??"";
    String readRecommenderUserNameWithDomain = Get.find<AppConfigService>().readRecommenderUserNameWithDomain();
    if(readRecommenderUserNameWithDomain.isNotEmpty){
      inviteUserName=readRecommenderUserNameWithDomain;
    }
    number.value = res.data?.data?.number ?? 0;
    status.value = res.data?.data?.status ?? -1;
    if (inviteUserName.isNotEmpty && inviteUserName != recommender.value.name) {
      recommender.value = OwnInfoModelData(
        name: inviteUserName,
        nickname: inviteUserName.length > 7
            ? inviteUserName.substring(0, 7)
            : inviteUserName,
      );
    }
  }

 Future<InviteInfoListDataModel> getInviteInfoNew() async {
    var userNameWithoutDomain = conf.getUserNameWithoutDomain();
    var res = await api.inviteInfo(userNameWithoutDomain);
    dismissLoadingDialog();
    var inviteUserName = res.data?.data?.userName??"";
    String readRecommenderUserNameWithDomain = Get.find<AppConfigService>().readRecommenderUserNameWithDomain();
    if(readRecommenderUserNameWithDomain.isNotEmpty){
      inviteUserName=readRecommenderUserNameWithDomain;
    }
    number.value = res.data?.data?.number ?? 0;
    status.value = res.data?.data?.status ?? -1;
    if (inviteUserName.isNotEmpty) {
      var user = inviteUserName;
      if(!user.contains('@')){
        user = '$user@';
      }
      var list = await getOtherInfoNew([user]) ?? [];
      String? nickName ,avatar;
      if(list.isNotEmpty){
        nickName = list.first.nickname;
        avatar = list.first.avatar;
      }
      recommender.value = OwnInfoModelData(
        name: inviteUserName,
        userName:list.first.name,
        avatar: avatar,
        nickname: nickName??(inviteUserName.length > 7
            ? inviteUserName.substring(0, 7)
            : inviteUserName),
      );
    }

    return getRecommenderInviteInfoListDataModel();
  }

  _onLoadMore({bool isRefresh = false}) async {
    var userNameWithoutDomain = conf.getUserNameWithoutDomain();
    var response =
        await api.inviteInfoList(userNameWithoutDomain, pageSize, pageNumber);
    var list = response.data?.data?.list;
    var lastPage = response.data?.data?.lastPage;
    var currentPage = response.data?.data?.currentPage;
    // var total = response.body?.data?.total;
    if (lastPage != null) {
      this.lastPage = lastPage;
    }
    if (currentPage != null) {
      curPage = currentPage;
    }
    if (list != null) {
      for (InviteInfoListDataModel el in list) {
        el.nickname = (el.invitedUserName?.length ?? 0) > 7
            ? el.invitedUserName?.substring(0, 7)
            : el.invitedUserName;
      }
      AppLogger.d("list length==${list.length} dataList.");
      if (isRefresh) {
        dataList.clear();
      }
      if (list.isNotEmpty) {
        _updateUserInfo(list);
        dataList.addAll(list);
      }
    }

    ///测试
    // var userName = await conf.getUserName();
    // var nick = conf.getMySelfDisplayName();
    // var avatarUrl = conf.getMySelfAvatarInfoModel().avatarUrl;
    // recommender.value=OwnInfoModelData(nickname: nick,name: userName,avatar: avatarUrl);
    // for (int i = 0; i < 10; i++) {
    //   InviteInfoListDataModel d = InviteInfoListDataModel(
    //       invitedUserName: userName,
    //       nickname: nick,
    //       avatar: avatarUrl,
    //       createTime: "${DateTime.now().millisecondsSinceEpoch}");
    //   dataList.add(d);
    // }
  }

  _updateUserInfo(List<InviteInfoListDataModel>? models) async{
    if(models==null){
      return;
    }
    var names = models.map((e) => '${e.invitedUserName}@');
    // List<String> names  = [];//= models.map((e) => '${e.invitedUserName}@');
    // names.add('AFuhjN6VbGriiZvVyzkN29kt5yYt16zZe7bXDGyJAKCZ@');
    if(names.isEmpty){
      return;
    }
    var notNodeUsers = <String>[];
    var list = await getOtherInfoNew(List.from(names)) ?? [];
    for (var e in list) {
      var bean = dataList.firstWhereOrNull((d) =>  e.name?.contains('${d.invitedUserName}')??false);
      if (bean != null) {
        bean.nickname = e.nickname;
        bean.avatar = e.avatar;
        bean.userName = e.name;
        update([bean.invitedUserName??'']);
      }
      if(e.name?.endsWith('@')??false){
        notNodeUsers.add(e.name??'');
      }
    }
    if(notNodeUsers.isNotEmpty){
      // notNodeUsers = ['3LwtmsVeaNBmFqP74CfCPpmYkCzhCm7nqr3kFeMckeec@'];
      Get.find<AppDatabase>().contactInfoByName(notNodeUsers).get().then((value){
        for (var e in value) {
          var bean = dataList.firstWhereOrNull((d) =>  e.username.startsWith('${d.invitedUserName}'));
          if (bean != null) {
            bean.nickname = e.localname??e.displayname;
            bean.avatar = e.avatarUrl;
            bean.userName = e.username;
            update([bean.invitedUserName??'']);
          }
        }
      });
    }
    AppLogger.d('_updateUserInfo list=${list.length}');
  }
  ///https://api.ioi.chat/poster/2325d7d742f8c14a12454b7dd72e4cc5.png
  onShare(BuildContext context) async {
    // var posterPath = (Get.find<AppConfigService>().readInvitePosterPath()).obs;
    // var userNameWithoutDomain = conf.getUserNameWithoutDomain();
    // showLoadingDialog();
    // var response = await api.getSharePoster(userNameWithoutDomain);

    // var p = response.data?.data?.path ?? "";
    // String path = p;
    // if (!p.startsWith('http')) {
    //   path = InviteApi.apiUrl + p;
    // }
    // AppLogger.d("onSharePoster path---==${posterPath.value}");

    // String shareLink = (response.data?.data?.discourse ?? "");
    // if (posterPath.isEmpty ||
    //     appTempAbsolutePath(path.split('/').last) != posterPath.value ||
    //     !File(posterPath.value).existsSync()) {
    //   posterPath.value = await downloadFile(path) ?? posterPath.value;
    //   AppLogger.d("onSharePoster path0==${posterPath.value}");
    //   Get.find<AppConfigService>().saveInvitePosterPath(posterPath.value);
    // }
    // var inviteCodeT = response.data?.data?.inviteCode;
    // if (inviteCodeT?.isNotEmpty ?? false) {
    //   inviteCode.value = inviteCodeT!;
    // }
    // dismissLoadingDialog();
    // AppLogger.d("onSharePoster path==$posterPath");

    Get.to(
      () => SharePoster(
          // url: posterPath,
          // shareLink: shareLink,
          // onRefresh: (v) {
          //   if (posterPath.isNotEmpty) {
          //     File(posterPath.value ?? '').delete();
          //   }
          //   onShare(context);
          // },
          ),
    );
    // SmartDialog.show(
    //   useAnimation: false,
    //   builder: (ctx) {
    //     return SharePoster(
    //       url: posterPath,
    //       shareLink: shareLink,
    //       onRefresh: (v) {
    //         if (posterPath.isNotEmpty) {
    //           File(posterPath.value ?? '').delete();
    //         }
    //         onShare(context);
    //       },
    //     );
    //   },
    //   clickMaskDismiss: true,
    //   useSystem: true,
    // );
  }

  InviteInfoListDataModel getRecommenderInviteInfoListDataModel() {
    return InviteInfoListDataModel.copyFromOwnInfoModelData(recommender.value);
  }

  onBindRecommender() async {
    if (status.value == -1 || status.value == 1) {
      return;
    }
    var ret = await SmartDialog.show(
        useAnimation: false,
        builder: (ctx) {
          return Scaffold(
            backgroundColor: AppColors.transparent,
            body: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 30).r,
              child: InviteBindRecommender(
                controller: recommenderTec,
              ),
            ),
          );
        });
    if (ret == true) {
      showLoadingDialog();
      var bool = await bindRecommender(bindUserName: recommenderTec.text);
      if (bool) {
        onRefresh();
      }
    }
  }

  onScan() {
    Get.toNamed(Routes.SCAN, arguments: Routes.InvitePage);
  }

  _getInviteCode() async {
    var readInviteCode = Get.find<AppConfigService>().readInviteCode();
    if (readInviteCode.isEmpty) {
      var userNameWithoutDomain = conf.getUserNameWithoutDomain();
      readInviteCode =
          (await Get.find<InviteApi>().getSharePoster(userNameWithoutDomain))
                  .data
                  ?.data
                  ?.inviteCode ??
              "";
    }
    inviteCode.value = readInviteCode;
  }
}
