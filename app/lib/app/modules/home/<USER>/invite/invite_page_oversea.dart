import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_metatel/app/modules/home/<USER>/invite/invite_controller.dart';
// import 'package:flutter_metatel/app/widgets/app_bar_cus.dart';
import 'package:flutter_metatel/app/widgets/mavatar_circle_avatar.dart';
import 'package:flutter_metatel/app/widgets/text_middle_overlow.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:flutter_metatel/core/utils/util.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../../core/languages/l.dart';
import '../../../../../core/utils/jump.dart';
import '../../../../../core/values/colors.dart';
import '../../../../../r.dart';
import '../../../../data/models/res/invite/invite_info_model_list_res.dart';
import '../../../../data/models/user_message_model.dart';
import '../../../../widgets/divider_cus.dart';
import '../../../base/base_view.dart';

class InvitePageOversea extends StatefulWidget {
  const InvitePageOversea({super.key});

  @override
  State<StatefulWidget> createState() => _InvitePageOverseaState();
}

class _InvitePageOverseaState extends State<InvitePageOversea> {
  final InviteController _controller = Get.put(InviteController());
  final double _bgHeight = kToolbarHeight + 200.r;
  final double _containerHeight = 130.r;

  @override
  void dispose() {
    Get.delete<InviteController>();
    super.dispose();
  }

  @override
  void initState() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      showLoadingDialog();
      _controller.onRefresh();
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BaseView(
        isDark: false,
      Scaffold(
        resizeToAvoidBottomInset:false,
        extendBodyBehindAppBar: true,
        appBar: AppBar(
          backgroundColor: Colors.transparent,
          elevation: 0,
          centerTitle: true,
          title: Text(
            L.share_promotion.tr, 
            style: TextStyle(
              color: AppColors.white,
            ),
          ),
          leading: const BackButton(color: AppColors.white),
        ),
        body: RefreshIndicator(
          displacement: 5,
          onRefresh: () async {
            _controller.onRefresh();
          },
          notificationPredicate: (_) {
            return true;
          },
          child: Container(
            width: 1.sw,              
            height: 1.sh,
            color: AppColors.white,
            child: Column(
              children: [
                /// 头部
                SizedBox(
                  height: _bgHeight + (_containerHeight/2),
                  child: Stack(
                    children: [                    
                      /// 颜色背景
                      Container(
                        height: _bgHeight,
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [
                              AppColors.primaryBgColor2,
                              AppColors.primaryBgColor1,                                                                                            
                            ],                              
                            begin: Alignment(-1.5,0),
                            end: Alignment(1,0),      
                          ),
                          borderRadius: BorderRadius.only(
                            bottomLeft: Radius.circular(50.r),
                            bottomRight: Radius.circular(50.r),
                          ),
                        ),
                      ),
                      /// 我的推荐人 部分
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: 23.r),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            Text(
                              "${L.my_invite_code.tr}:",
                              style: TextStyle(color: AppColors.white, fontSize: 12.sp),
                            ),
                            SizedBox(height: 4.r),
                            /// 邀请码
                            Obx(
                              () => Text(
                                _controller.inviteCode.value,
                                style: TextStyle(
                                  color: AppColors.white,
                                  fontSize: 23.sp,
                                  fontWeight: FontWeight.w500
                                ),
                                maxLines: 1,
                              ),
                            ),
                            /// 复制邀请码按钮
                            GestureDetector(
                              onTap: () async {
                                await Clipboard.setData(
                                  ClipboardData(text: _controller.inviteCode.value));
                                toast(
                                  L.chat_has_copy_to_shear_plate.tr,
                                );
                              },
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Icon(Icons.copy_rounded, size: 12.r, color: AppColors.white),
                                  SizedBox(width: 3.r),
                                  Text(
                                    L.copy_invitation_code.tr,
                                    style: TextStyle(
                                      color: AppColors.white,
                                      fontSize: 10.sp,
                                    ),
                                  ),
                                ],
                              ),
                            ),                        
                            SizedBox(height: 20.r),
                            /// 绑定推荐人部分
                            Material(
                              elevation: 2,
                              borderRadius: BorderRadius.circular(25.r),
                              child: Container(                              
                                height: _containerHeight,
                                decoration: BoxDecoration(
                                  color: AppColors.white,
                                  borderRadius: BorderRadius.circular(25.r),
                                ),
                                padding: EdgeInsets.symmetric(horizontal: 38.r),
                                child: 
                                  ///我的推荐人
                                  Obx(
                                    () => Column(
                                      mainAxisAlignment: MainAxisAlignment.center,                                      
                                      children: [
                                        /// 标题，扫码按钮
                                        Row(
                                          children: [
                                            Text(
                                              L.my_recommender.tr,
                                              style: TextStyle(
                                                color: AppColors.primaryBgColor1,
                                                fontSize: 11.sp,
                                                fontWeight: FontWeight.w500,
                                              ),
                                            ),
                                            const Spacer(),
                                            Visibility(
                                              visible: _controller.status.value == 0,
                                              child: GestureDetector(
                                                onTap: () {
                                                  _controller.onScan();
                                                },
                                                child: Image.asset(R.iconScanRecommender,width: 15.r,color: AppColors.primaryBgColor1,),
                                              ),
                                            ),
                                          ],
                                        ),
                                        SizedBox(height: 16.r),
                                        /// 关联推荐人按钮
                                        _controller.status.value != 1
                                            ? _bindRecommender()
                                            :  FutureBuilder(
                                                future: _controller.getInviteInfoNew(),
                                                builder: (c, snapshot) {
                                                  InviteInfoListDataModel? data ;
                                                  if (snapshot.connectionState ==
                                                      ConnectionState.done) {
                                                    data = snapshot.data;
                                                    AppLogger.d('getInviteInfoNew data=${data?.nickname}');
                                                  }
                                                  data ??= _controller.getRecommenderInviteInfoListDataModel();
                                                  return buildItem(
                                                    data,
                                                    paddingTop: 0,
                                                    nickNameColor: Colors.black,
                                                    userNameColor: Colors.black,
                                                    fontWeight: FontWeight.w400,
                                                    nickNameSize: 12,
                                                    onPressed: (){
                                                      clickItem(data);
                                                    }
                                                  );
                                                },
                                              ),                                    
                                      ],
                                    ),
                                  ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                SizedBox(height: 20.r),
                /// 邀请人数，邀请列表，邀请按钮
                Expanded(
                  child: Padding(
                    padding: EdgeInsets.symmetric(horizontal: 27.r),
                    child: Column(
                      children: [
                        ///我的邀请
                        Obx(
                          () => Row(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Text(
                                L.invited_people.tr,
                                style: TextStyle(
                                  color: Colors.black,
                                  fontSize: 14.sp,
                                ),
                              ),
                              SizedBox(width: 12.r),
                              Text(
                                "${_controller.number.value}",
                                style: TextStyle(
                                  fontSize: 23.sp,
                                  color: Colors.black,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const Spacer(),
                              ElevatedButton(
                                style: TextButton.styleFrom(
                                  backgroundColor: AppColors.primaryBgColor1,
                                  fixedSize: Size(95.r, 30.r),
                                ),
                                onPressed: () {
                                  _controller.onShare(context);
                                },
                                child: Text(
                                  L.invite_now.tr,
                                  style: TextStyle(
                                    color: AppColors.white,                                  
                                    fontSize: 12.sp
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        SizedBox(height: 20.r),
                        const DividerCus(),
                        SizedBox(height: 16.r),
                        ///我的邀请列表
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [                              
                              Text(
                                L.invitation_list.tr,
                                style: TextStyle(
                                  color: Colors.black,
                                  fontSize: 14.sp,
                                ),
                              ),
                              SizedBox(height: 10.r),
                              buildInviteList(_controller),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),            
          ),
        ),
      ),
    );
  }

  // clickItem(InviteInfoListDataModel? data){
  //   if(data==null || data.userName==null ||!(data.userName!.contains('@')) || (data.userName!.endsWith('@')) ){
  //     return;
  //   }

  //   var userMessage = UserMessage(
  //       chatType: 0,
  //       displayName: data.nickname,
  //       userName: data.userName,
  //       avatarPath: data.avatar);
  //   // Get.toNamed(Routes.MESSAGE, arguments: userMessage);
  //   JumpPage.messgae(userMessage);
  // }

  Widget _bindRecommender() {
    return ElevatedButton(
      onPressed: () {
        _controller.onBindRecommender();
      },
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Image.asset(
            R.icoBindRecommender,
            width: 9.r,
            height: 9.r,
            color: AppColors.white,
          ),
          SizedBox(width: 5.w),
          Text(
            L.bind_recommender.tr,
            style: TextStyle(
                color: AppColors.white,
                fontSize: 14.sp,
            ),
          ),
        ],
      ),
    );
  }
}

Widget buildInviteList(InviteController controller) {
  return Expanded(
    child: Obx(
          () => ListView.builder(
          controller: controller.scrollController,
          shrinkWrap: true,
          padding: EdgeInsets.zero,
          physics: const AlwaysScrollableScrollPhysics(),
          itemBuilder: (ctx, index) {
            var data = controller.dataList[index];

            return buildItem(data,onPressed:(){ clickItem(data);});
          },
          itemCount: controller.dataList.length),
    ),
  );
}

clickItem(InviteInfoListDataModel? data){
  if(data==null || data.userName==null ||!(data.userName!.contains('@')) || (data.userName!.endsWith('@')) ){
    return;
  }

  var userMessage = UserMessage(
      chatType: 0,
      displayName: data.nickname,
      userName: data.userName,
      avatarPath: data.avatar);
  // Get.toNamed(Routes.MESSAGE, arguments: userMessage);
  JumpPage.messgae(userMessage);
}

Widget buildItem(
    InviteInfoListDataModel model, {
      double paddingTop = 20,
      double nickNameSize = 16,
      Color nickNameColor = AppColors.colorFF333333,
      Color userNameColor = AppColors.colorFF666666,
      FontWeight fontWeight = FontWeight.w500,
      VoidCallback? onPressed,
    }) {
  return Container(
    padding: EdgeInsets.only(top: paddingTop).r,
    // padding: padding,
    alignment: Alignment.center,
    child: Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onPressed,
        child: GetBuilder<InviteController>(
          id: model.invitedUserName,
          builder: (controller) {
            return Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                // 头像
                createHeader(model.avatar, model.nickname),
                // 间隔
                SizedBox(width: 10.w),
                Expanded(
                  child: SizedBox(
                    height: 40.h,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      mainAxisSize: MainAxisSize.max,
                      children: [
                        /// 名称
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                                model.nickname ?? "",
                                style: TextStyle(
                                    fontSize: nickNameSize.sp,
                                    color: nickNameColor,
                                    fontWeight: FontWeight.w500),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            SizedBox(
                              width: 10.w,
                            ),
                            Text(model.createTime ?? "",
                              style: TextStyle(
                                fontSize: 12.sp,
                                color: AppColors.colorFF999999,
                              ),
                            ),
                          ],
                        ),
                        SizedBox(height: 3.r),
                        Row(
                          children: [
                            Expanded(
                              child: MiddleText(
                                userNameDelDomain(model.invitedUserName) ?? "",
                                WXTextOverflow.ellipsisMiddle,
                                style: TextStyle(
                                    color: userNameColor,
                                    fontSize: 12.sp,
                                    fontWeight: FontWeight.w500),
                              ),
                            ),
                            SizedBox(
                              width: 10.w,
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            );
          },
        ),
      ),
    ),
  );
}
Widget createHeader(String? avatar, String? nickName) {
  return MAvatarCircle(
    diameter: 40.h,
    imagePath: avatar,
    isNet: (avatar != null && avatar.isNotEmpty),
    textStyle: const TextStyle(fontSize: 16, color: Colors.white),
    text: nickName,
  );
}
