import 'dart:async';

import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../../core/languages/l.dart';
import '../../../../../core/utils/events_bus.dart';
import '../../../../../core/utils/util.dart';
import '../../../../../core/values/config.dart';
import '../../../../data/events/events.dart';
import '../../../../data/models/avatar_model.dart';
import '../../../../data/providers/api/own.dart';
import '../../../../data/services/config_service.dart';

class SetMySelfController extends GetxController {
  var userIconPath = "".obs;
  var userLocalName = "".obs;
  final FocusNode focusNode = FocusNode();
  final List<StreamSubscription> _subscriptions = [];
  TextEditingController textEditController = TextEditingController();
  @override
  void onClose() {
    focusNode.removeListener(focusListener);
    for (var element in _subscriptions) {
      element.cancel();
    }
    _subscriptions.clear();
    super.onClose();
  }

  focusListener(){
    if (focusNode.hasFocus) {
      if (textEditController.text.isEmpty) {
        textEditController.text = userLocalName.value;
        update();
      }
    }
  }
  @override
  void onInit() async {
    super.onInit();
    focusNode.addListener(focusListener);
    AppConfigService conf = Get.find<AppConfigService>();
    String mySelfDisplayName = conf.getMySelfDisplayName().isEmpty
        ? conf.getPhone()
        : conf.getMySelfDisplayName();
    AvatarModel avatar = AvatarModel.fromJson(conf.getMySelfAvatarInfo() ?? {});
    userLocalName.value = mySelfDisplayName;
    userIconPath.value = appSupporAbsolutePath(avatar.path) ?? "";
    StreamSubscription<Object> subscription;
    subscription =
        Get.find<EventBus>().on<MySelfAvatarUpdateEvent>().listen((event) {
          _updateMySelfAvatar(event);
        });
    _subscriptions.add(subscription);
  }
  void _updateMySelfAvatar(MySelfAvatarUpdateEvent event) {
    var avatarModel = event.avatarModel;
    userIconPath.value = appSupporAbsolutePath(avatarModel.path) ?? "";
    update();
  }
  // List<Widget> buildSetHeadBottomSheetItemWidgets(BuildContext context) {
  //   List<Widget> widgets = [
  //     /*getBottomSheetItemSimple(context, L.capture_picture.tr,
  //       itemCallBack: () => getImageTakePhoto()),*/
  //     /*getBottomSheetItemSimple(context, L.get_picture_from_phone.tr,
  //         itemCallBack: () => chooseImage()),*/
  //     getBottomSheetItemSimple(context, L.view_Avatar.tr, itemCallBack: () {
  //       showDialog(
  //         barrierDismissible: false,
  //         context: context,
  //         useSafeArea: false,
  //         builder: (_) {
  //           return userIconPath.value.isEmpty
  //               ? Container(
  //                   color: Colors.black,
  //                   child: Image.asset(
  //                     R.defaultAvatar,
  //                   ),
  //                 )
  //               : PhotosView(
  //                   pathList: [userIconPath.value],
  //                   currentIndex: 0,
  //                 );
  //         },
  //       );
  //     }),
  //   ];
  //   if (userIconPath.value.isNotEmpty) {
  //     widgets.add(getBottomSheetItemSimple(context, L.save_photo.tr,
  //         itemCallBack: () => AvatarTask.saveAvatar(userIconPath.value)));
  //   }
  //   return widgets;
  // }

  void updateMyself(String disPlayName, String fistName, String lastName) {
    submitOwnInfo(nickName: disPlayName).then((value){
      if(value){
        AppConfigService conf = Get.find<AppConfigService>();
        conf.saveMySelfDisplayName(disPlayName);
        conf.saveMyselfFirstName(fistName);
        conf.saveMyselfLastName(lastName);
        Get.find<EventBus>().fire(ContactDataMyselfUpdateEvent());
        userLocalName.value = disPlayName;
        Get.find<EventBus>().fire(MiningTaskEvent(MiningTaskType.MINING_TASK_UPDATE_NICKNAME));
        Get.back();
      }else{
        toast(L.fail_to_edit.tr);
      }
    });
  }
}
