import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/data/services/chatio_service.dart';
import 'package:get/get.dart';

import '../../../../../../core/languages/l.dart';
import '../../../../../../core/utils/events_bus.dart';
import '../../../../../../core/utils/util.dart';
import '../../../../../../core/values/config.dart';
import '../../../../../data/events/events.dart';
import '../../../../../data/models/avatar_model.dart';
import '../../../../../data/providers/api/own.dart';
import '../../../../../data/services/config_service.dart';

class FirstMySelfController extends GetxController {
  var userIconPath = "".obs;
  var userLocalName = "".obs;
  final FocusNode focusNode = FocusNode();
  bool needUpdateName = true;
  final List<StreamSubscription> _subscriptions = [];
  TextEditingController textEditController = TextEditingController();
  @override
  void onClose() {
    // focusNode.removeListener(focusListener);
    for (var element in _subscriptions) {
      element.cancel();
    }
    _subscriptions.clear();
    super.onClose();
  }

  // focusListener(){
  //   if (focusNode.hasFocus) {
  //     if (textEditController.text.isEmpty) {
  //       textEditController.text = userLocalName.value;
  //       update();
  //     }
  //   }
  // }
  String getLocalName() {
    AppConfigService conf = Get.find<AppConfigService>();
    String mySelfDisplayName = conf.getMySelfDisplayName().isEmpty
        ? conf.getPhone()
        : conf.getMySelfDisplayName();
    return mySelfDisplayName;
  }

  String getAvatar() {
    AppConfigService conf = Get.find<AppConfigService>();
    AvatarModel avatar = AvatarModel.fromJson(conf.getMySelfAvatarInfo() ?? {});
    String userIconPath = appSupporAbsolutePath(avatar.path) ?? "";
    return userIconPath;
  }

  String defaultName() {
    AppConfigService conf = Get.find<AppConfigService>();
    userLocalName.value = getLocalName();
    var userName = conf.getUserName() ?? '';
    return 'u${userName.substring(0,6)}';
  }

  @override
  void onInit() async {
    super.onInit();
    // focusNode.addListener(focusListener);
    await Get.find<ChatioService>().getServiceApi();
    AppConfigService conf = Get.find<AppConfigService>();
    userLocalName.value = getLocalName();
    var userName = conf.getUserName() ?? '';
    textEditController.text = userLocalName.value;

    userIconPath.value = getAvatar();
    StreamSubscription<Object> subscription;
    subscription =
        Get.find<EventBus>().on<MySelfAvatarUpdateEvent>().listen((event) {
          _updateMySelfAvatar(event);
        });
    _subscriptions.add(subscription);
  }
  void _updateMySelfAvatar(MySelfAvatarUpdateEvent event) {
    var avatarModel = event.avatarModel;
    userIconPath.value = appSupporAbsolutePath(avatarModel.path) ?? "";
    update();
    if(!needUpdateName){
      Get.back();
    }
  }
  Future <void> updateMyself(String disPlayName, String fistName, String lastName) async {
    submitOwnInfo(nickName: disPlayName).then((value) {
      if (value) {
        AppConfigService conf = Get.find<AppConfigService>();
        conf.saveMySelfDisplayName(disPlayName);
        conf.saveMyselfFirstName(fistName);
        conf.saveMyselfLastName(lastName);

        // Fire event to update local data
        Get.find<EventBus>().fire(ContactDataMyselfUpdateEvent());

        userLocalName.value = disPlayName;
        needUpdateName = true;

        // Fire event to update mining task after updating the nickname
        Get.find<EventBus>().fire(MiningTaskEvent(MiningTaskType.MINING_TASK_UPDATE_NICKNAME));
      }
    });
  }

  // void updateMyself(String disPlayName, String fistName, String lastName) {
  //   submitOwnInfo(nickName: disPlayName).then((value){
  //     if(value){
  //       AppConfigService conf = Get.find<AppConfigService>();
  //       conf.saveMySelfDisplayName(disPlayName);
  //       conf.saveMyselfFirstName(fistName);
  //       conf.saveMyselfLastName(lastName);
  //       Get.find<EventBus>().fire(ContactDataMyselfUpdateEvent());
  //       userLocalName.value = disPlayName;
  //       needUpdateName = false;
  //       Get.find<EventBus>().fire(MiningTaskEvent(MiningTaskType.MINING_TASK_UPDATE_NICKNAME));
  //       if(userIconPath.value.isNotEmpty){
  //         Get.back();
  //       } else {
  //         toast(L.please_set_avatar.tr);
  //       }
  //     }else{
  //       toast(L.fail_to_edit.tr);
  //     }
  //   });
  // }
}
