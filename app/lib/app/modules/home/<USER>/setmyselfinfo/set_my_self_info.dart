import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/modules/home/<USER>/setmyselfinfo/set_my_sefl_info_controller.dart';
import 'package:flutter_metatel/app/widgets/app_bar_cus.dart';
import 'package:flutter_metatel/app/widgets/mavatar_circle_avatar.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../../core/languages/l.dart';
import '../../../../../core/task/avatar_task.dart';
import '../../../../../core/utils/util.dart';
import '../../../../../core/values/colors.dart';

class SetMySelfInfoPage extends StatefulWidget {
  const SetMySelfInfoPage({super.key});

  @override
  State<StatefulWidget> createState() => _SetMySelfInfoPage();
}

class _SetMySelfInfoPage extends State<SetMySelfInfoPage> {
  SetMySelfController controller = Get.put(SetMySelfController());

  @override
  void dispose() {
    Get.delete<SetMySelfController>();
    super.dispose();
  }
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.backgroundGray,
      appBar: AppBarCommon()
          .build(context, backgroundColor: AppColors.backgroundGray, actions: [
        IconButton(
          iconSize: 40.h,
          onPressed: () {
            String firstName = controller.textEditController.text.trim();
            // String lastName = _textEditControllerLastName.text.trim();
            // String localName = currentLanguageIsSimpleChinese()
            //     ? (lastName + firstName)
            //     : firstName + lastName;
            String displayName = firstName;
            if (displayName.isEmpty) {
              Get.back();
            } else {
              controller.updateMyself(displayName, firstName, "");
            }
          },
          icon: Text(
            L.save.tr,
            style: TextStyle(color: AppColors.bgButtonDefault, fontSize: 16.sp),
          ),
        ),
      ]),
      body: Obx(
        () => SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              IconButton(
                iconSize: 90.h,
                onPressed: () {
                  showBottomDialogCommonWithCancel(context,
                      widgets: _buildItemWidgets(context));
                },
                icon: MAvatarCircle(
                  diameter: 90,
                  imagePath: controller.userIconPath.value,
                  text: controller.userLocalName.value,
                ),
              ),
              Container(
                margin: const EdgeInsets.only(top: 19.5).r,
                child: Text(
                  L.set_new_avatar.tr,
                  style: TextStyle(
                      fontSize: 16.sp, color: AppColors.colorFF000000),
                ),
              ),
              Container(
                height: 44.h,
                width: double.infinity,
                margin: const EdgeInsets.only(top: 30.5, left: 16, right: 16).r,
                padding: const EdgeInsets.all(5).r,
                alignment: Alignment.center,
                decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: const BorderRadius.all(Radius.circular(6)).r),
                child: GetBuilder(
                  builder: (SetMySelfController c) {
                    return TextField(
                      focusNode: controller.focusNode,
                      autofocus: false,
                      controller: c.textEditController,
                      decoration: InputDecoration(
                        contentPadding: EdgeInsets.zero,
                        hintText: c.userLocalName.value.isEmpty
                            ? L.set_myself_display_name.tr
                            : c.userLocalName.value,
                        hintStyle: TextStyle(
                            fontSize: 16.sp, color: AppColors.colorFF999999),
                        counterText: '',
                        border: const OutlineInputBorder(
                          borderSide: BorderSide.none,
                        ),
                      ),
                      style: TextStyle(
                        fontSize: 16.sp,
                        color: AppColors.colorFF333333,
                      ),
                      maxLength: 20,
                    );
                  },
                ),
              ),
              Container(
                width: double.infinity,
                margin: const EdgeInsets.only(top: 8.5, left: 30).r,
                child: Text(
                  L.set_myself_display_name_hint.tr,
                  style: TextStyle(
                      fontSize: 12.sp, color: AppColors.colorFF333333),
                ),
              )
            ],
          ),
        ),
      ),
    );
  }

  List<Widget> _buildItemWidgets(BuildContext context) {
    List<Widget> widgets = [
      getBottomSheetItemSimple(context, L.capture_picture.tr,
          itemCallBack: () => AvatarTask.getImageTakePhoto(context)),
      getBottomSheetItemSimple(context, L.get_picture_from_phone.tr,
          itemCallBack: () => AvatarTask.chooseImage(context)),
    ];
    // if (controller.userIconPath.value.isNotEmpty) {
    //   widgets.add(getBottomSheetItemSimple(context, L.save_photo.tr,
    //       itemCallBack: () =>
    //           AvatarTask.saveAvatar(controller.userIconPath.value)));
    // }
    return widgets;
  }
}
