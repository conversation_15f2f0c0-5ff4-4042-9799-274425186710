import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/data/services/config_service.dart';
import 'package:flutter_metatel/app/data/services/translation_service.dart';
import 'package:flutter_metatel/app/modules/home/<USER>/mine_controller.dart';
import 'package:flutter_metatel/app/widgets/app_bar_cus.dart';
import 'package:flutter_metatel/app/widgets/pdf_view.dart';
import 'package:flutter_metatel/r.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../../core/languages/l.dart';
import '../../../../../core/utils/util.dart';
import '../../../../../core/values/colors.dart';
import '../../../../../core/values/config.dart';
import '../../../../../routes/pages.dart';
import '../../../../widgets/divider_cus.dart';
import '../mine_view.dart';

class SetOverSeaPage extends GetView {
  const SetOverSeaPage(this.controller, {super.key});

  @override
  final MineController controller;

  @override
  Widget build(BuildContext context) {
    Get.find<MineController>().updateBiometricsType();
    Get.find<MineController>().changeLanguage();
    Get.find<MineController>().updateLocalProxy();

    return Scaffold(
      appBar: AppBarCommon().build(context, title: L.set.tr),
      body: ListView(
        padding: EdgeInsets.zero,
        children: [
          Container(
              margin: const EdgeInsets.only(top: 10).r,
              decoration: BoxDecoration(
                  color: Theme.of(context).primaryColor,
                  borderRadius: const BorderRadius.all(Radius.circular(10)).r),
              child: Column(
                children: [
                  settingsWidgetItemSimpleMore(
                    L.multi_language.tr,
                    titleColor: Colors.black,
                    rightColor: AppColors.colorFF666666,
                    rightWidget: Icon(Icons.chevron_right, size: 20.r),
                    onTapCallBack: () {
                      int? languageCode = Get.find<AppConfigService>().readMultiLanguage();
                      controller.setTempSelectedLanguageIndex(languageCode);
                      showBottomDialogCommonWithCancel(context, widgets: [                      
                        _buildLanguageList(callBack: controller.setTempSelectedLanguageIndex),
                        SizedBox(height: 20.r),
                        SizedBox(
                          width: 0.9.sw,
                          child: ElevatedButton(
                            onPressed: (){
                              controller.languageChanged(controller.tempSelectedLanguageIndex.value);
                            }, 
                            child: Text(L.confirm_language.tr),
                          ),
                        ),
                      ]);
                    },
                  ),
                  if(Config.verifyHuman)
                    DividerCus(
                      indent: 16.w,
                    ),
                  Obx(() => settingsWidgetItemSimple(
                        L.initial_node_update.tr,
                        fontSize: 15.sp,
                        rightColor: Colors.black,
                        right: controller.domain.value,
                        onTapCallBack: () {
                          controller.updateFirstNode();
                        },
                      )),
                  // DividerCus(
                  //   indent: 16.w,
                  // ),
                  // Obx(() => settingsWidgetItemSimple(
                  //   L.setting_local_proxy.tr,
                  //   onTapEnable: false,
                  //   rightWidget: SizedBox(
                  //     child: FittedBox(
                  //       fit: BoxFit.fill,
                  //       child: CupertinoSwitch(
                  //         activeColor:
                  //         Theme.of(context).colorScheme.primary,
                  //         value: controller.isLocalAgent.value,
                  //         onChanged: (value) {
                  //           controller.setLocalAgent(value);
                  //         },
                  //       ),
                  //     ),
                  //   ),
                  // )),
                  // if(Config.verifyHuman)
                  // settingsWidgetItemSimple(L.chat_mining_switch.tr,onTapEnable:false,
                  //     rightWidget: SizedBox(
                  //       child: FittedBox(
                  //           fit: BoxFit.fill,
                  //           child: StatefulBuilder(builder: (context, setState) {
                  //             return Obx(() => CupertinoSwitch(
                  //               activeColor: Theme.of(context).colorScheme.primary,
                  //               value: controller.chatMiningOpened.value,
                  //               onChanged: (value) {
                  //                 controller.chatMiningOpened.value =
                  //                 !controller.chatMiningOpened.value;
                  //                 controller.setChatMiningOpened();
                  //               },
                  //             ));
                  //           })),
                  //     )
                  // ),

                ],
              )),
          Container(
            margin: const EdgeInsets.only(top: 10).r,
            decoration: BoxDecoration(
                color: Theme.of(context).primaryColor,
                borderRadius: const BorderRadius.all(Radius.circular(10)).r),
            child: Column(
              children: [
                settingsWidgetItemSimple(
                  L.set_up_a_pin.tr,
                  fontSize: 15.sp,
                  rightWidget: Icon(Icons.chevron_right, size: 20.r),
                  onTapCallBack: () {
                    Get.toNamed(Routes.PIN);
                  },
                ),
                DividerCus(
                  indent: 16.w,
                ),
                Obx(
                  () => controller.rxBiometricsType.value.isEmpty
                      ? const SizedBox.shrink()
                      : settingsWidgetItemSimple(
                          controller.rxBiometricsType.value + L.payment.tr,
                          fontSize: 15.sp,
                          onTapEnable: false,
                          rightWidget: SizedBox(
                            child: FittedBox(
                              fit: BoxFit.fill,
                              child: CupertinoSwitch(
                                activeColor:
                                    Theme.of(context).colorScheme.primary,
                                value: controller.isBiometrics.value,
                                onChanged: (value) {
                                  controller.setBiometricsOpen(value);
                                },
                              ),
                            ),
                          ),
                        ),
                ),
                DividerCus(
                  indent: 16.w,
                ),
                Obx(
                  () => settingsWidgetItemSimple(
                    L.activate_wallet.tr,
                    fontSize: 15.sp,
                    onTapEnable: false,
                    rightWidget: SizedBox(
                      child: FittedBox(
                        fit: BoxFit.fill,
                        child: CupertinoSwitch(
                          activeColor:
                              Theme.of(context).colorScheme.primary,
                          value: controller.isWalletOpen.value ,
                          onChanged: (value) {
                            controller.onActivateWalletChanged(value);                             
                          },
                        ),
                      ),
                    ),
                  ),
                ),
                Obx(
                  () => Visibility(
                    visible: controller.isWalletOpen.value,
                    child: DividerCus(
                      indent: 16.w,
                    ),
                  ),
                ),
                Obx(
                  () => Visibility(
                    visible: controller.isWalletOpen.value,
                    child: settingsWidgetItemSimple(
                      L.set_wallet_password.tr,
                      fontSize: 15.sp,
                      rightWidget: Icon(Icons.chevron_right, size: 20.r),
                      onTapCallBack: () {
                        Get.toNamed(Routes.WalletPwdReset);
                      },
                    ),
                  ),
                ),
              ],
            ),
          ),
          Container(
            margin: const EdgeInsets.only(top: 10).r,
            decoration: BoxDecoration(
                color: Theme.of(context).primaryColor,
                borderRadius: const BorderRadius.all(Radius.circular(10)).r),
            child: Column(
              children: [
                settingsWidgetItemSimple(
                  L.storage_optimization.tr,
                  fontSize: 15.sp,
                  rightWidget: Icon(Icons.chevron_right, size: 20.r),
                  onTapCallBack: () {
                    controller.storageOptimization();
                  },
                ),
                DividerCus(
                  indent: 16.w,
                ),
                settingsWidgetItemSimple(
                  L.setting_notification.tr,
                  fontSize: 15.sp,
                  rightWidget: Icon(Icons.chevron_right, size: 20.r),
                  onTapCallBack: () {
                    Get.toNamed(Routes.NotifySetView);
                  },
                ),
                DividerCus(
                  indent: 16.w,
                ),
                settingsWidgetItemSimple(
                  L.privacy_policy.tr,
                  fontSize: 15.sp,
                  rightWidget: Icon(Icons.chevron_right, size: 20.r),
                  onTapCallBack: () {
                    Get.to(
                      PdfView(
                        title: L.privacy_policy.tr,
                        url:Config.privacyPolicyUrlIm,
                      ),
                      // WebviewPage(
                      //   title: L.privacy_policy.tr,
                      //   url: Config.privacyPolicyUrlIm,
                      // ),
                    );
                  },
                ),
                DividerCus(
                  indent: 16.w,
                ),
                settingsWidgetItemSimple(
                  L.terms_of_use.tr,
                  fontSize: 15.sp,
                  rightWidget: Icon(Icons.chevron_right, size: 20.r),                
                  onTapCallBack: () {
                    Get.to(
                      PdfView(
                        title: L.terms_of_use.tr,
                        url:Config.termsOfUseUrlIm,
                      ),
                    );
                    // Get.to(
                    //   WebviewPage(
                    //     title: L.terms_of_use.tr,
                    //     url: Config.termsOfUseUrlIm,
                    //   ),
                    // );
                  },
                ),
                DividerCus(
                  indent: 16.w,
                ),
                // settingsWidgetItemSimple(
                //   L.use_comments_or_feedback_suggestions.tr,
                //   onTapCallBack: () {
                //     Get.to(
                //       WebviewPage(
                //         title: L.app_name.tr.toUpperCase(),
                //         url: Config.feedbackioiUrl,
                //       ),
                //     );
                //   },
                // ),
              ],
            ),
          ),
          Container(
            margin: const EdgeInsets.only(top: 10).r,
            decoration: BoxDecoration(
                color: Theme.of(context).primaryColor,
                borderRadius: const BorderRadius.all(Radius.circular(10)).r),
            child: settingsWidgetItemSimple(
              rightColor: Colors.red,
              L.main_logout.tr,
              fontSize: 15.sp,
              rightWidget: SizedBox.shrink(),
              onTapCallBack: () {
                showBottomDialogCommon(context);
              },
            ),
          )
        ],
      ),
    );
  }

  Widget settingsWidgetItemSimpleMore(
    String title, {
    Widget? rightWidget,
    Color? rightColor,
    Color? titleColor,
    SettingsWidgetItemOnTap? onTapCallBack,
    double? height,
  }) {
    return Material(
      color: AppColors.transparent,
      child: InkWell(
        onTap: () {
          if (onTapCallBack != null) {
            onTapCallBack();
          } else {
            toast(L.not_yet_develop.tr);
          }
        },
        child: Container(
          height: height ?? 44.h,
          padding:
              const EdgeInsets.only(left: 16, top: 2, right: 16, bottom: 2).r,
          child: Row(
            children: [
              Text(
                title,
                style: TextStyle(
                  fontSize: 15.sp,
                  color: titleColor ?? rightColor,
                ),
              ),
              Expanded(
                child: Container(
                  padding: const EdgeInsets.only(left: 15).r,
                  alignment: Alignment.centerRight,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      Text(
                        controller.changeLanguage(),
                        style: TextStyle(fontSize: 16.sp, color: rightColor),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      SizedBox(
                        width: 5.r,
                      ),
                      rightWidget ??
                          (R.nextArrowGrey.startsWith("assets")
                              ? Image.asset(
                                  R.nextArrowGrey,
                                  width: 6.w,
                                  height: 10.h,
                                )
                              : Container())
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildLanguageList({
    SettingsWidgetItemClick? callBack,
  }) {
    var languageList = TranslationService().languageList;
    return Container(
      width: double.infinity,
      constraints: BoxConstraints(maxHeight: (languageList.length * 55).r + 20.r + 20.r), // top padding: 20, space above button: 20
      child: ListView.separated(
        padding: EdgeInsets.only(top: 20.r, bottom: 0, left: 0.r, right: 0.r),
        controller: ScrollController(),
        itemBuilder: (context, index) {
          var language = languageList[index];
          return Obx(() => _createLanguageView(index, language, callBack: callBack));
        },
        itemCount: languageList.length,
        separatorBuilder: (BuildContext context, int index) =>
            Divider(height: 0.5.h, color: AppColors.colorFFF8F8F8),
      ),
    );
  }

  Widget _createLanguageView(
    int index,
    String language, {
    SettingsWidgetItemClick? callBack,
  }) {
    bool isSelected = controller.tempSelectedLanguageIndex == index;
    var languageName = TranslationService().languageNameList;
    return Material(
      color: Colors.transparent,
      child: InkWell(
        borderRadius: null,
        onTap: () {
          callBack?.call(index);
        },
        child: Container(
          height: 55.r,
          padding: const EdgeInsets.symmetric(
            horizontal: 30,         
          ).r,
          child: Row(
            children: [
              Text(
                languageName[index],
                style: TextStyle(
                  fontSize: 15.sp,
                  color: Colors.black,
                ),
              ),
              const Spacer(),
              _roundRadioBtn(isChecked: isSelected),              
            ],
          ),
        ),
      ),
    );
  }

  Widget _roundRadioBtn({bool isChecked =false}){
    if(isChecked)
    return Container(
      width: 17.r,
      height: 17.r,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: AppColors.primaryBgColor1,
      ),
      child: Icon(Icons.check_rounded, size: 15.r,color: AppColors.white,),
    );

    return Container(
      width: 17.r,
      height: 17.r,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        border: Border.all(
          color: AppColors.colorFF707070,
        ),
      ),
    );
  }
}
