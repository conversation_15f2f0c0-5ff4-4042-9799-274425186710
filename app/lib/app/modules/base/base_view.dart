//FileName base_view
// <AUTHOR>
//@Date 2023/3/31 18:57

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class BaseView extends StatelessWidget {
  const BaseView(this.child, {this.isDark = true,super.key});
  final Widget? child;
  final bool isDark;

  @override
  Widget build(BuildContext context) {
    var mySystemTheme = isDark
        ? SystemUiOverlayStyle.dark.copyWith(
            statusBarColor: Colors.transparent,
          )
        : SystemUiOverlayStyle.light.copyWith(
            statusBarColor: Colors.transparent,
          );
    return AnnotatedRegion<SystemUiOverlayStyle>(
        value: mySystemTheme, child: child ?? Container());
  }
}
