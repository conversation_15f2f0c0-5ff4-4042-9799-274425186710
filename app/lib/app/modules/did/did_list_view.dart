import 'package:flutter/material.dart';
import 'package:flutter_metatel/core/values/colors.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../core/languages/l.dart';
import '../../../r.dart';
import '../../widgets/app_bar_cus.dart';
import 'did_list_controller.dart';

class DidListView extends GetView<DidListController> {
  const DidListView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBarCommon().build(context, title: L.did_list.tr),
      backgroundColor: Colors.white,
      body: GetBuilder<DidListController>(builder: (controller) {
        return SingleChildScrollView(
            scrollDirection: Axis.vertical,
            child: Column(
              children: [
                Stack(
                  children: [
                    Container(
                      height: 140.r,
                      margin:
                          EdgeInsets.only(left: 16.w, right: 16.w, top: 10.h),
                      decoration: BoxDecoration(
                        image: DecorationImage(
                            image: AssetImage(R.bgListDid), fit: BoxFit.cover),
                        color: AppColors.transparent,
                        borderRadius: const BorderRadius.all(
                          Radius.circular(15),
                        ).r,
                      ),
                      padding: EdgeInsets.symmetric(horizontal: 18.w),
                      child: Column(
                        mainAxisSize: MainAxisSize.max,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          SizedBox(
                            height: 15.r,
                          ),
                          Text(
                            L.computing_power_did.tr,
                            style: TextStyle(
                              color: AppColors.white,
                              fontSize: 12.sp,
                            ),
                          ),
                          SizedBox(
                            height: 8.r,
                          ),
                          Text(
                            L.did_times.trParams({
                              'numbers1': '${controller.totalComputing.value}'
                            }),
                            style: TextStyle(
                              color: AppColors.colorFFd9e8ff,
                              fontSize: 16.sp,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          SizedBox(
                            height: 15.r,
                          ),
                          Row(
                            mainAxisSize: MainAxisSize.max,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Expanded(
                                  child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    L.did_number.tr,
                                    style: TextStyle(
                                      color: AppColors.colorFFd9e8ff,
                                      fontSize: 12.sp,
                                    ),
                                  ),
                                  SizedBox(
                                    height: 10.r,
                                  ),
                                  Text(
                                    controller.totlaHostingNum.value,
                                    style: TextStyle(
                                      color: AppColors.colorFFFFE400,
                                      fontSize: 20.sp,
                                    ),
                                  ),
                                ],
                              )),
                              Expanded(
                                  child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    L.hosting_number.tr,
                                    style: TextStyle(
                                      color: AppColors.colorFFd9e8ff,
                                      fontSize: 12.sp,
                                    ),
                                  ),
                                  SizedBox(
                                    height: 10.r,
                                  ),
                                  Text(
                                    controller.hostingNum.value,
                                    style: TextStyle(
                                      color: AppColors.colorFFFFE400,
                                      fontSize: 20.sp,
                                    ),
                                  ),
                                ],
                              )),
                            ],
                          ),
                        ],
                      ),
                    ),
                    Positioned(
                      right: 30.r,
                      top: 27.r,
                      child: TextButton(
                        style: ButtonStyle(
                          backgroundColor:
                              MaterialStateProperty.all(AppColors.white),
                          padding: MaterialStateProperty.all(
                              const EdgeInsets.only(
                                      left: 23, right: 23, top: 10, bottom: 10)
                                  .r),
                          shape: MaterialStateProperty.all(
                            RoundedRectangleBorder(
                              borderRadius:
                                  BorderRadius.all(Radius.circular(19.r).r).r,
                            ),
                          ),
                        ),
                        onPressed: () {
                          controller.aiHosting();
                        },
                        child: Text(
                          L.ai_hosting.tr,
                          style: TextStyle(
                              color: AppColors.colorFF3474d1, fontSize: 14.sp),
                        ),
                      ),
                    ),
                  ],
                ),
                createDidList(),
              ],
            ));
      }),
    );
  }

  Widget createDidList() {
    List<Widget> list = [];
    list.add(createTop());
    var listData = controller.searchBeanList.value;
    for (int i = 0; i < listData.length; i++) {
      var d = listData[i];
      var view = createListItem(d.sbt ?? '', d.status ?? 0, d.endTimes ?? '',
          isLast: i == listData.length - 1);
      list.add(view);
    }
    return Container(
        padding: EdgeInsets.symmetric(horizontal: 16.r, vertical: 15.r),
        child: IntrinsicHeight(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: list,
          ),
        ));
  }

  Widget createTop() {
    return Container(
        height: 39.r,
        decoration: BoxDecoration(
          color: AppColors.colorFF333333,
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(15),
            topRight: Radius.circular(15),
          ).r,
        ),
        padding: EdgeInsets.symmetric(horizontal: 18.w),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          mainAxisSize: MainAxisSize.max,
          children: [
            Expanded(
              child: Center(
                child: Text(
                  'TID',
                  style: TextStyle(
                    color: AppColors.white,
                    fontSize: 14.sp,
                  ),
                ),
              ),
            ),
            Expanded(
              child: Center(
                child: Text(
                  L.ai_hosting_state.tr,
                  style: TextStyle(
                    color: AppColors.white,
                    fontSize: 14.sp,
                  ),
                ),
              ),
            ),
            Expanded(
              child: Center(
                child: Text(
                  L.validity_period.tr,
                  style: TextStyle(
                    color: AppColors.white,
                    fontSize: 14.sp,
                  ),
                ),
              ),
            ),
          ],
        ));
  }

  Widget createListItem(String did, int state, String endTimes,
      {bool isLast = false}) {
    var bgColor =
        state == 1 ? AppColors.colorFFE6EBF2 : AppColors.colorFFF7F7F7;
    var borderRad = isLast
        ? const BorderRadius.only(
            bottomLeft: Radius.circular(15),
            bottomRight: Radius.circular(15),
          ).r
        : const BorderRadius.all(Radius.circular(0)).r;
    var statues =
        state == 1 ? L.ai_hosting_state_1.tr : L.ai_hosting_state_2.tr;
    var statuesColor =
        state == 1 ? AppColors.colorFF3474d1 : AppColors.colorFF999999;
    var time = endTimes.split(' ');
    var t = '';
    if(time.isNotEmpty){
      t = time.first;
    }
    return Container(
        height: 39.r,
        decoration: BoxDecoration(
          color: bgColor,
          border: Border.all(color: AppColors.colorFFEDEDED, width: 0.5),
          borderRadius: borderRad,
        ),
        padding: EdgeInsets.symmetric(horizontal: 18.w),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          mainAxisSize: MainAxisSize.max,
          children: [
            Expanded(
              child: Center(
                child: Text(
                  did,
                  style: TextStyle(
                    color: AppColors.colorFF333333,
                    fontSize: 14.sp,
                  ),
                ),
              ),
            ),
            Container(
              width: 0.5,
              height: double.infinity,
              color: AppColors.colorFFEDEDED,
            ),
            Expanded(
              child: Center(
                child: Text(
                  statues,
                  style: TextStyle(
                    color: statuesColor,
                    fontSize: 14.sp,
                  ),
                ),
              ),
            ),
            Container(
              width: 0.5,
              height: double.infinity,
              color: AppColors.colorFFEDEDED,
            ),
            Expanded(
              child: Center(
                child: Text(
                  t,
                  style: TextStyle(
                    color: AppColors.colorFF333333,
                    fontSize: 14.sp,
                  ),
                ),
              ),
            ),
          ],
        ));
  }
}
