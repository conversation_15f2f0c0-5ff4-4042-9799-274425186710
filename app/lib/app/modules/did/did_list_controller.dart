//FileName did_list_controller
// <AUTHOR>
//@Date 2023/12/18 12:20
import 'dart:ffi';

import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:get/get.dart';

import '../../../core/languages/l.dart';
import '../../../core/task/time_task.dart';
import '../../../core/utils/events_bus.dart';
import '../../../core/utils/jump.dart';
import '../../../core/utils/util.dart';
import '../../../core/values/config.dart';
import '../../data/enums/enum.dart';
import '../../data/models/res/did/seartch_did_info.dart';
import '../../data/models/user_message_model.dart';
import '../../data/services/chatio_service.dart';
import '../../data/services/event_service.dart';
import '../home/<USER>/mine_controller.dart';

class DidListController extends GetxController {
  var searchBeanList = RxList<SearchBean>().obs;
  RxString hostingNum = ''.obs;
  RxString totlaHostingNum = ''.obs;
  RxInt totalComputing = 0.obs;
  @override
  void onInit() {
    super.onInit();
    var controller = Get.find<MineController>();
    var data = controller.searchBeanList.value;
    if (data.isNotEmpty) {
      searchBeanList.value = data;
      var count1=0;
      var total = 0;
      for(var b in searchBeanList.value){
        if(b.status==1){
          count1++;
        }
        total+=b.powerUp??0;
      }
      totalComputing.value = total==0?10000:total;
      hostingNum.value = '$count1';
      totlaHostingNum.value = '${searchBeanList.value.length}';

    }
    controller.updateSbt();
  }

  void aiHosting() {
    JumpPage.aiHosting();
  }
}
