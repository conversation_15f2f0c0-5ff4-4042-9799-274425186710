import 'package:flutter/cupertino.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:get/get.dart';

import '../api/dao_api.dart';


class SearchDaoController extends GetxController {
  final RxList _searchList = [].obs;
  int _page=1;
  int _totalPage=-1;
  final urls=[ "https://www.wanandroid.com/blogimgs/50c115c2-cf6c-4802-aa7b-a4334de444cd.png","https://www.wanandroid.com/blogimgs/90c6cc12-742e-4c9f-b318-b912f163b8d0.png",
    "https://www.wanandroid.com/blogimgs/ab17e8f9-6b79-450b-8079-0f2287eb6f0f.png","https://www.wanandroid.com/blogimgs/fb0ea461-e00a-482b-814f-4faca5761427.png",
    "https://www.wanandroid.com/blogimgs/62c1bd68-b5f3-4a3c-a649-7ca8c7dfabe6.png","https://www.wanandroid.com/blogimgs/00f83f1d-3c50-439f-b705-54a49fc3d90d.jpg",
  ];
  String? _key;
  getKey()=>_key;
  get() => _searchList;
  late ScrollController scrollController;

  @override
  void onInit() {
    AppLogger.i('type===onInit');
    super.onInit();
    scrollController=ScrollController();
    scrollController.addListener(() {
      AppLogger.d('DaoController  pixels===${scrollController.position.pixels}');
      AppLogger.d('DaoController  maxScrollExtent===${scrollController.position.maxScrollExtent}');

      if (scrollController.position.pixels == scrollController.position.maxScrollExtent) {
        if(_totalPage>0&&_page<=_totalPage){
          _page++;
          setKey(_key);
        }
      }
    });
  }

  void setKey(String? key,{bool newSearch=false}) {
    _key = key;
    if(newSearch){
      _totalPage=-1;
      _page=1;
      _searchList.clear();
    }
    if (key?.isEmpty ?? true) {
      _searchList.clear();
    } else {
      _search(_key);
    }
  }

  void _search(String? key) async {
    var res=await Get.find<DaoApiProvider>().getDaoList(10, _page,key:key );
    AppLogger.d('_onLoadMore==${res.data?.toJson()}');
    if(res.statusCode==200){
      var body=res.data;
      if (_key != key) {
        _searchList.clear();
      } else {
        if(body!=null&&body.code==200){
          _totalPage=body.data?.total??0;
          if(body.data?.list?.isNotEmpty??false){
            _searchList.addAll(body.data!.list!);
          }
        }
      }

    }
  }

}
