import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../core/languages/l.dart';
import '../../../../core/values/colors.dart';
import '../../../../routes/pages.dart';
import '../bean/dao_model.dart';
import '../daoBrowser/dao_browser_view.dart';
import '../dao_config.dart';
import '../dao_item.dart';
import 'search_dao_controller.dart';

class SearchDaoView extends GetView<SearchDaoController> {
  final FocusNode focusNode = FocusNode();

  SearchDaoView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      resizeToAvoidBottomInset: false,
      backgroundColor: AppColors.white,
      body: GetBuilder<SearchDaoController>(builder: (controller) {
        return Padding(
          padding: const EdgeInsets.only(top: kToolbarHeight),
          child: Column(
            children: [
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Expanded(
                    child: Container(
                      height: 36.r,
                      padding: const EdgeInsets.only(
                        left: 15,
                      ).r,
                      child: TextField(
                        autofocus: true,
                        focusNode: focusNode,
                        onChanged: (text) => _onTextChange(text, context),
                        textAlign: TextAlign.start,
                        style: TextStyle(fontSize: 16.sp),
                        textAlignVertical: TextAlignVertical.center,
                        decoration: InputDecoration(
                          hintText: L.searbar_hint_search.tr,
                          // 设置后，提升文本居中
                          contentPadding: EdgeInsets.zero,
                          prefixIcon: const Icon(
                            Icons.search,
                            color: Color.fromARGB(255, 89, 90, 90),
                          ),
                          filled: true,
                          fillColor: const Color(0xffF7F7F7),
                          border: const OutlineInputBorder(
                            borderSide: BorderSide.none,
                            borderRadius: BorderRadius.all(Radius.circular(25)),
                          ),
                        ),
                      ),
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.only(),
                    child: TextButton(
                      onPressed: () {
                        FocusManager.instance.primaryFocus?.unfocus();
                        Future.delayed(const Duration(milliseconds: 100), () {
                          Get.back();
                        });
                      },
                      child: Text(
                        L.chat_contact_cancel.tr,
                        style: TextStyle(
                            fontSize: 14.sp, fontWeight: FontWeight.w500),
                      ),
                    ),
                  ),
                ],
              ),
              Expanded(
                child: Listener(
                  onPointerDown: (down) {
                    focusNode.unfocus();
                  },
                  onPointerMove: (down) {
                    focusNode.unfocus();
                  },
                  child: Obx(
                    () => ListView.builder(
                      padding: EdgeInsets.only(bottom: 7.r),
                      controller: controller.scrollController,
                      itemCount: controller.get().length,
                      itemBuilder: (BuildContext context, int index) {
                        var model = controller.get()[index];
                        return DaoItemView(data: model, onPressed: _onPressed);
                      },
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      }),
    );
  }

  void _onPressed(DaoModelData model) {
    DaoBean bean = DaoBean(DaoJoinWebViewType.daoDetail, id: model.id);
    Get.toNamed(
      Routes.DaoBrowserView,
      arguments: bean,
    );
  }

  /// 文本变化监听
  void _onTextChange(String text, BuildContext context) {
    controller.setKey(text, newSearch: true);
  }
}
