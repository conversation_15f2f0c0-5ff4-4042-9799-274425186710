//FileName dao_api
// <AUTHOR>
//@Date 2022/11/15 15:30


import 'dart:convert';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:flutter_metatel/app/data/models/add_collection_model.dart';
import 'package:flutter_metatel/app/data/models/get_dao_collection_model.dart';
import 'package:flutter_metatel/app/data/providers/api/base_dio_api.dart';

import '../../../../core/utils/app_log.dart';
import '../../../../core/values/config.dart';
import '../../../data/models/dao_list_model.dart';
import '../../../data/models/res/banner_res_model.dart';
import '../../../data/models/res/emoticon_res_model.dart';
import '../../../data/models/res/mex_get_ddc_list_res_model.dart';
import '../dao_config.dart';

class DaoApiProvider extends BaseDioClient {

  Dio _createDio(){
    BaseOptions options = BaseOptions(headers:{HttpHeaders.userAgentHeader:Config.userAgent,HttpHeaders.contentTypeHeader:'application/x-www-form-urlencoded'});
    options.connectTimeout = const Duration(seconds: 15);
    options.sendTimeout = const Duration(seconds: 15);
    options.contentType = 'application/x-www-form-urlencoded';

    var dio = super.createDio(options: options);
    return dio;
  }

  Future<Response<DaoList>> getDaoList(int size, int page,
      {String? key}) async {
    AppLogger.d("getDaoList url==/api/dao/list");
    String body = 'page_number=$page&page_size=$size';

    if (key != null && key.isNotEmpty) {
      body += '&name=$key';
    }
    AppLogger.d("getDaoList url==/api/dao/list=${body.toString()}");

    var response = await createDio().post(DaoConfig.getDaoBaseUrl()+Config.dao_list_api, data: body,);
    DaoList? data;
    if (response.statusCode == 200) {
      data = DaoList.fromJson(response.data);
    }
    return Response(
      statusCode: response.statusCode,
      statusMessage: response.statusMessage,
      data: data, requestOptions: response.requestOptions,
    );
  }
  //获取banner
  Future<Response<BannerResModel>> getDaoBannerList() async {
    AppLogger.d("getDaoList url==/api/banner/list");
    String body = 'type=3';
    var response = await _createDio().post(DaoConfig.getDaoBaseUrl()+Config.dao_banner_api, data: body,);
    BannerResModel? data;
    if (response.statusCode == 200) {
      data = BannerResModel.fromJson(response.data);
    }
    return Response(
      statusCode: response.statusCode,
      statusMessage: response.statusMessage,
      data: data, requestOptions: response.requestOptions,
    );
  }
  /// 添加藏品
  Future<Response<AddCollectionModel>> addCollection(
      String address, String code, int ddcid) async {
    address = Uri.encodeComponent(address.trim());
    code = Uri.encodeComponent(code.trim());
    var daoAddressUrlEncode = Uri.encodeComponent(Config.publicKeyBase64DaoAddress.trim());
    String body =
        'address=$address&code=$code&ddcid=$ddcid&dao_address=$daoAddressUrlEncode';
    var response = await _createDio().post(
      DaoConfig.getDaoBaseUrl()+Config.user_bind_api,
      data: body,
    );

    AddCollectionModel? data;
    if (response.statusCode == 200) {
      data = AddCollectionModel.fromJson(response.data);
    }
    return Response(
      statusCode: response.statusCode,
      statusMessage: response.statusMessage,
      data: data, requestOptions: response.requestOptions,
    );
  }

  /// 获取藏品
  Future<Response<GetDaoCollectionModel>> getCollection(
      { required int pageSize, required int  pageNumber,}) async {
    var daoAddress = Uri.encodeComponent(Config.publicKeyBase64DaoAddress.trim());
    String body =
        'pageNumber=$pageNumber&pageSize=$pageSize&address=$daoAddress';
    AppLogger.d("request body==$body");
    var response = await _createDio().post(
      DaoConfig.getDaoBaseUrl()+Config.getUserDdcDao,
     data: body,
    );
    AppLogger.d("response==${response.data}");
    GetDaoCollectionModel? data;
    if (response.statusCode == 200) {
      data = GetDaoCollectionModel.fromJson(response.data);
    }
    return Response(
      statusCode: response.statusCode,
      statusMessage: response.statusMessage,
      data: data, requestOptions: response.requestOptions,
    );
  }
  Future<Response<MexGetDdcListResModel>> getMexDdcList(
      String address, String tag) async {
    var body='yjs_address=$address&p_b=${Uri.encodeComponent(Config.publicKeyBase64Dao.trim())}&tag=$tag';
    var response = await _createDio().post(DaoConfig.getDaoBaseUrl()+Config.GET_DDC_LIST_MEX, data: body);
    MexGetDdcListResModel? data;
    AppLogger.d("response GET_DDC_LIST_MEX   body==$body");

    if (response.statusCode == 200) {
      if (response.data.runtimeType != String) {
        data = MexGetDdcListResModel.fromJson(response.data);
        AppLogger.d("response GET_DDC_LIST_MEX   data==${data.data?.length}");

      } else {
        var decode = json.decode(
          response.data,
        );
        data = MexGetDdcListResModel.fromJson(decode);
        AppLogger.d("response GET_DDC_LIST_MEX   data==${data.data?.length}");

      }
    }
    return Response(
      statusCode: response.statusCode,
      statusMessage: response.statusMessage,
      data: data, requestOptions: response.requestOptions,
    );
  }

  //获取EmojiType
  Future<Response<EmoticonResModel>> getEmotionList() async {
    AppLogger.d("getDaoList url==/api/emoticon/list");
    String body = 'key=${Uri.encodeComponent(Config.publicKeyBase64Dao.trim())}';
    var response = await _createDio().post(DaoConfig.getDaoBaseUrl()+Config.dao_emoticon_api, data: body,);
    EmoticonResModel? data;
    if (response.statusCode == 200) {
      data = EmoticonResModel.fromJson(response.data);
    }
    return Response(
      statusCode: response.statusCode,
      statusMessage: response.statusMessage,
      data: data, requestOptions: response.requestOptions,
    );
  }
}
