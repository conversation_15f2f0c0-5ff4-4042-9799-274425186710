//FileName dao_item
// <AUTHOR>
//@Date 2022/10/26 15:48
import 'package:extended_image/extended_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../core/languages/l.dart';
import '../../../core/values/colors.dart';
import '../../../core/values/config.dart';
import '../../../r.dart';
import '../../widgets/text_middle_overlow.dart';
import 'bean/dao_model.dart';

class DaoItemView extends StatelessWidget {
  final DaoModelData data;

  /// 点击
  final ValueChanged<DaoModelData>? onPressed;

  const DaoItemView({required this.data, this.onPressed, super.key});

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.white,
      child: InkWell(
        onTap: () => onPressed?.call(data),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              padding: EdgeInsets.only(left: 14.r, right: 14.r),
              height: 130.r,
              // decoration: BoxDecoration(
              //     borderRadius: BorderRadius.circular(10),
              //     color: Colors.white,
              //     //gradient: RadialGradient(colors: [Colors.white,Colors.white],center: Alignment.center,radius: .0),
              //     ),
              color: Colors.white,
              alignment: Alignment.topLeft,
              child: Row(
                children: [
                  Card(
                    shape: RoundedRectangleBorder(
                        borderRadius: BorderRadiusDirectional.circular(6.r)),
                    clipBehavior: Clip.antiAlias,
                    child: ExtendedImage.network(
                      data.logo ?? '',
                      width: 100.r,
                      height: 100.r,
                      maxBytes: Config.maxImageBytes,
                      fit: BoxFit.cover,
                    ),
                  ),
                  // 间隔
                  const SizedBox(width: 10.0),
                  Expanded(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            // 名称
                            Expanded(
                              child: Text(
                                data.name ?? '',
                                overflow: TextOverflow.ellipsis,
                                style: TextStyle(
                                  color: Colors.black,
                                  fontWeight: FontWeight.w500,
                                  fontSize: 18.sp,
                                ),
                              ),
                            ),
                            //
                            Visibility(
                                visible:data.identityType==2,
                                child: Image.asset(
                              R.icDaoEnterprise,
                              width: 15.5.r,
                              height: 20.r,
                            )),
                          ],
                        ),
                        // 间隔
                        SizedBox(height: 5.h),
                        // 消息内容行
                        Container(
                          padding:  const EdgeInsets.only(
                              left: 3, right: 3, top: 1, bottom: 1),
                          decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: const BorderRadius.all(
                                Radius.circular(2),
                              ),
                              border: Border.all(
                                  width: 0.5.r, color: AppColors.colorFFCC294C)),
                          child: Text(
                            L.hava_collections.trParams(
                                {'number': '${data.assetsNumber ?? ''}'}),
                            style: TextStyle(
                                color: AppColors.colorFFCC294C,
                                fontSize: 10.sp),
                          ),
                        ),
                        SizedBox(height: 5.h),
                        Text(
                          data.remark ?? '',
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          style: TextStyle(
                            fontSize: 13.sp,
                            color: AppColors.colorFF666666,
                          ),
                        ),
                        SizedBox(height: 15.r),
                        Row(
                          children: [
                            Image.asset(
                              R.iconDaoUserCounts,
                              width: 13.r,
                              height: 13.r,
                            ),
                            SizedBox(
                              width: 5.r,
                            ),
                            SizedBox(
                              width: 30.r,
                              child: Text(
                                '${data.memberNumber}',
                                style: TextStyle(
                                  color: AppColors.colorFF333333,
                                  fontSize: 11.sp,
                                ),
                              ),
                            ),
                            Image.asset(
                              R.icDaoAddress,
                              width: 13.r,
                              height: 13.r,
                            ),
                            SizedBox(width: 5.r),
                            Expanded(
                              child: MiddleText(
                                data.address ?? '',
                                WXTextOverflow.ellipsisMiddle,
                                style: TextStyle(
                                  fontSize: 11.sp,
                                  color: AppColors.colorFF333333,
                                ),
                              ),
                            ),
                          ],
                        )
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
