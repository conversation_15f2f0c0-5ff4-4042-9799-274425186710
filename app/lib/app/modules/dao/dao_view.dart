import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/modules/dao/dao_appbar.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:get/get.dart';

import '../../../core/languages/l.dart';
import '../../../core/utils/util.dart';
import '../../../core/values/colors.dart';
import '../../widgets/divider_cus.dart';
import '../../widgets/loading_view.dart';
import 'banner/banner_view.dart';
import 'dao_controller.dart';
import 'dao_item.dart';

class DaoView extends StatefulWidget {
  const DaoView({Key? key}) : super(key: key);

  @override
  State<DaoView> createState() {
    return _DaoViewState();
  }
}

class _DaoViewState extends State<DaoView> {
  final DaoController controller = Get.put(DaoController(), permanent: true);

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return createBody();
  }

  Widget createBody() {
    return Scaffold(
      appBar: getAppBar(),
      backgroundColor: AppColors.white,
      body: SafeArea(
        child: RefreshIndicator(
          displacement: 5,
          onRefresh: () async {
            await controller.refreshData();
          },
          notificationPredicate: (_) {
            return true;
          },
          child: Obx(
            () {
              if (controller.isLoading()) {
                return loadingView();
              } else if (controller.isLoadError()) {
                return loadErrorView(reloading:controller.reloading);
              }
              return loadSuccessView();
            },
          ),
        ),
      ),
    );
  }



  Widget loadSuccessView() {
    var allData = controller.allDaoObs;
    return ListView.separated(
      padding: EdgeInsets.only(bottom: 5.r),
      physics: const AlwaysScrollableScrollPhysics(),
      itemCount: allData.length,
      controller: controller.scrollController,
      itemBuilder: (context, index) {
        var sessionData = allData[index];
        if (index == 0) {
          return const BannerWidget();
        }
        return GetBuilder<DaoController>(
          id: sessionData.address,
          builder: (control) {
            return DaoItemView(
              data: sessionData,
              onPressed: controller.itemClick,
            );
          },
        );
      },
      separatorBuilder: (context, index) {
        var padding = EdgeInsets.only(left: 18.r, right: 0.r);
        if (index == 0) {
          padding = const EdgeInsets.only();
        }
        return Container(
          padding: padding,
          child: const DividerCus(
            thickness: 0.5,
          ),
        );
      },
    );
  }

}
