//FileName AppletItem
// <AUTHOR>
//@Date 2022/10/26 12:22
import 'package:flutter/cupertino.dart';

import '../bean/applet_bean.dart';

class AppletItem extends StatefulWidget{
  final AppletsBean bean;
  const AppletItem(this.bean, {super.key});

  @override
  State<StatefulWidget> createState() {
    return _AppletItemState();
  }

}
class _AppletItemState extends State<AppletItem>{
  @override
  Widget build(BuildContext context) {
    // TODO: implement build
    throw UnimplementedError();
  }

}