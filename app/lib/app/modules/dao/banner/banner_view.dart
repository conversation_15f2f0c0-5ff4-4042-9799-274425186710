//FileName banner_view
// <AUTHOR>
//@Date 2022/10/25 16:00
import 'dart:async';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../core/languages/l.dart';
import '../../../../core/values/colors.dart';
import '../../../../r.dart';
import '../../../../routes/pages.dart';
import '../../../widgets/webview_page.dart';
import '../bean/applet_bean.dart';
import '../daoBrowser/dao_browser_view.dart';
import '../dao_config.dart';
import 'banner_controller.dart';

class BannerWidget extends StatefulWidget {
  const BannerWidget({super.key});

  @override
  State<BannerWidget> createState() {
    return _BannerState();
  }
}

class _BannerState extends State<BannerWidget> {
  final BannerController controller = Get.put(BannerController(), permanent: true);

  @override
  void initState() {
    super.initState();
    controller.loadBanner();
  }

  @override
  void didUpdateWidget(covariant BannerWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    AppLogger.d('BannerWidget didUpdateWidget');
  }

  @override
  Widget build(BuildContext context) {
    var padding = EdgeInsets.only(left: 16.r, right: 16.r);
    return Container(
      decoration: const BoxDecoration(color: AppColors.white),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Obx(() => Container(
            height: controller.bannerList.isNotEmpty ? 160.r : 0,
            padding: EdgeInsets.only(left: 16.r, right: 16.r, top: 10.r),
            child: ListView.builder(
              itemCount: controller.bannerList.length,
              itemBuilder: (context, i) {
                if (i == 0) {
                  return SizedBox(
                    child: Container(
                      height: 150.r,
                      clipBehavior: Clip.hardEdge,
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(5.r)),
                      child: _BannerView(
                        switchDuration: const Duration(seconds: 3),
                        children: createBannerView(),
                      ),
                    ),
                  );
                } else {
                  return Container();
                }
              },
            ),
          )),
          Obx(
            () {
              var allData = controller.applets;
              return Container(
                height: 96,
                padding: padding,
                child: ListView.builder(
                  physics: const AlwaysScrollableScrollPhysics(),
                  itemCount: allData.length,
                  scrollDirection: Axis.horizontal,
                  controller: controller.scrollController,
                  itemBuilder: (context, index) {
                    var sessionData = allData[index];
                    return GetBuilder<BannerController>(
                      id: sessionData.id,
                      builder: (control) {
                        return createAppletView(sessionData);
                      },
                    );
                  },
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  List<Widget> createBannerView() {
    var banners = controller.bannerList;
    var views = <Widget>[];
    if (banners.isNotEmpty) {
      for (var b in banners) {
        views.add(GestureDetector(
            onTap: () {
              switch (b.isJump) {
                case 1:
                  if (b.jumpContent != null &&
                      (b.jumpContent!.startsWith('http://') ||
                          b.jumpContent!.startsWith('https://'))) {
                    AppLogger.d('banner click ${b.toJson()}');
                    if (b.jumpType == 1 || b.jumpType == 2) {
                      Get.to(
                        WebviewPage(
                          title: b.title ?? '',
                          url: b.jumpContent!,
                        ),
                      );
                    } else if (b.jumpType == 3) {
                      DaoBean bean = DaoBean(DaoJoinWebViewType.banner,
                          url: b.jumpContent);
                      Get.toNamed(
                        Routes.DaoBrowserView,
                        arguments: bean,
                      );
                    }
                  }
                  break;
              }
            },
            child: Container(
              height: 150.r,
              clipBehavior: Clip.hardEdge,
              decoration:
                  BoxDecoration(borderRadius: BorderRadius.circular(5.r)),
              child: CachedNetworkImage(imageUrl:b.img ?? '', fit: BoxFit.cover),
            )));
      }
    }
    return views;
  }

  Widget createAppletView(AppletsBean bean) {
    return Material(
      color: AppColors.white,
      child: InkWell(
        onTap: () {
          Get.toNamed(Routes.BrowserView);
        },
        child: Container(
          width: 70.r,
          height: 60.r,
          alignment: Alignment.center,
          padding: EdgeInsets.all(4.r),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Image.asset(
                bean.iconPath ?? R.defaultAvatar,
                width: 44.r,
                height: 44.r,
              ),
              SizedBox(
                height: 8.r,
              ),
              Text(
                bean.title ?? '',
                overflow: TextOverflow.ellipsis,
                style: TextStyle(
                  fontSize: 11.sp,
                  color: const Color(0xff333333),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class _BannerView extends StatefulWidget {
  final List<Widget> children;

  final Duration switchDuration;

  const _BannerView({
    this.children = const <Widget>[],
    this.switchDuration = const Duration(seconds: 3),
  });

  @override
  State<_BannerView> createState() {
    return _BannerViewState();
  }
}

class _BannerViewState extends State<_BannerView>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  late PageController _pageController;
  late Timer _timer;
  int _curPageIndex = 0;

  List<Widget> children = [];

  @override
  void initState() {
    super.initState();
    _curPageIndex = 0;

    _tabController = TabController(length: widget.children.length, vsync: this);
    AppLogger.d('widget.children.length==${widget.children.length}');

    /// 添加所有的widget到新建的list中
    children.addAll(widget.children);

    /// 定时器完成自动翻页-只有在大于1时才会有翻页
    if (children.length > 1) {
      children.insert(0, widget.children.last);
      children.add(widget.children.first);

      ///如果大于一页，则会在前后都加一页， 初始页要是 1
      _curPageIndex = 1;
      _timer = Timer.periodic(widget.switchDuration, _nextBanner);
    }

    ///初始页面 指定
    _pageController = PageController(initialPage: _curPageIndex);
  }

  /// 进行翻页的动画
  _nextBanner(Timer timer) {
    _curPageIndex++;
    _curPageIndex = _curPageIndex == children.length ? 0 : _curPageIndex;

    //curve:和android一样 动画插值
    _pageController.animateToPage(_curPageIndex,
        duration:const Duration(milliseconds: 500), curve: Curves.linear);
  }

  @override
  void dispose() {
    /// 页面销毁时进行回收
    _pageController.dispose();
    _tabController.dispose();
    _timer.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Listener(

            /// 点击时取消timer效果
            onPointerDown: (_) => _timer.cancel(),
            onPointerUp: (_) {
              ///重新开启timer
              if (children.length > 1) {
                _timer = Timer.periodic(widget.switchDuration, _nextBanner);
              }
            },
            child: NotificationListener(
              onNotification: (notification) {
                if (notification is ScrollUpdateNotification) {
                  ScrollUpdateNotification n = notification;

                  /// 判断是否是一次完整的翻页
                  if (n.metrics.atEdge) {
                    if (_curPageIndex == children.length - 1) {
                      /// 如果是最后一页，那么就跳到第一页
                      _pageController.jumpToPage(1);
                    } else if (_curPageIndex == 0) {
                      /// 如果是第一页，再往前滑动，因为原来的list前后都加了一条数据，所以 -2
                      _pageController.jumpToPage(children.length - 2);
                    }
                  }
                }
                return true;
              },
              child: PageView.builder(
                itemCount: children.length,
                itemBuilder: (context, index) {
                  /// banner设置点击监听
                  return InkWell(
                    child: children[index],
                    onTap: () {
                      if (kDebugMode) {
                        print("点击Item");
                      }
                    },
                  );
                },
                onPageChanged: (index) {
                  _curPageIndex = index;
                  if (index == children.length - 1) {
                    /// 如果是最后一页，那么下面的指示器设置为0的位置
                    _tabController.animateTo(0);
                  } else if (index == 0) {
                    ///如果是第一页再往左滑，那么久设置为指示器最后的位置
                    _tabController.animateTo(_tabController.length - 1);
                  } else {
                    _tabController.animateTo(index - 1);
                  }
                },
                controller: _pageController,
              ),
            )),
        Positioned(
          bottom: 8.0,
          right: 8.0,
          child: TabPageSelector(
            controller: _tabController,
            indicatorSize: 9,
            color: Colors.white,
            selectedColor: Colors.blue,
          ),
        ),
      ],
    );
  }
}
