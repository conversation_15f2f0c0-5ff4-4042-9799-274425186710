//FileName banner_controller
// <AUTHOR>
//@Date 2022/10/26 11:39
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../core/languages/l.dart';
import '../../../../core/utils/app_log.dart';
import '../../../../r.dart';
import '../api/dao_api.dart';
import '../bean/applet_bean.dart';
import '../bean/banner_bean.dart';

class BannerController extends GetxController {
  late ScrollController scrollController;
  var applets=RxList<AppletsBean>();
  var bannerList=RxList<BannerBean>();

  @override
  void onInit() {
    super.onInit();
    scrollController=ScrollController();
    for (int i = 0; i < 1; i++) {
      applets.add(AppletsBean(iconPath: R.icoTrend, title: L.trend.tr));
    }

  }

  void loadBanner() async{
    var res = await Get.find<DaoApiProvider>().getDaoBannerList();
    AppLogger.d('loadBanner==${res.data?.toJson()}');
    bannerList.clear();
    if (res.statusCode == 200) {
      var body = res.data;
      if (body != null && body.code == 200) {
        if (body.data?.isNotEmpty ?? false) {
          bannerList.addAll(body.data!);
        }
      }
    }
    // var banner = DaoConfig.getDaoBanner();
    // if (banner?.isNotEmpty ?? false) {
    //   bannerList.clear();
    //   bannerList.addAll(banner!);
    // }
  }

  @override
  void dispose() {
    scrollController.dispose();
    super.dispose();
  }
}
