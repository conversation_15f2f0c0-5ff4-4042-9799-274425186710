//FileName market_bean
// <AUTHOR>
//@Date 2022/10/26 12:04
class BannerBean {
  BannerBean({this.jumpType, this.jumpContent,this.img, this.id, this.title,this.isJump});
  int? jumpType;
  String? jumpContent;
  String? img;
  int? id;
  int? isJump;
  String? title;
  BannerBean.fromJson(Map<String, dynamic> json) {
    jumpType = json['jump_type'];
    jumpContent = json['jump_content'];
    img = json['img'];
    id = json['id'];
    isJump=json['is_jump'];
    title = json['title'];
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['jump_type'] = jumpType;
    data['jump_content'] = jumpContent;
    data['img'] = img;
    data['id'] = id;
    data['title'] = title;
    data['is_jump']=isJump;
    return data;
  }
}
