class DaoList {
  int? code;
  String? msg;
  Data? data;

  DaoList({this.code, this.msg, this.data});

  DaoList.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    msg = json['msg'];
    data = json['data'] != null ? Data?.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['code'] = code;
    map['msg'] = msg;
    if (data != null) {
      map['data'] = data?.toJson();
    }
    return map;
  }
}

class Data {
  List<DaoModelData>? list;
  int? currentPage;
  int? lastPage;
  int? total;

  Data({this.list, this.currentPage, this.lastPage, this.total});

  Data.fromJson(Map<String, dynamic> json) {
    if (json['list'] != null) {
      list = <DaoModelData>[];
      json['list'].forEach((v) {
        list?.add(DaoModelData.fromJson(v));
      });
    }
    currentPage = json['current_page'];
    lastPage = json['last_page'];
    total = json['total'];
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    if (list != null) {
      data['list'] = list?.map((v) => v.toJson()).toList();
    }
    data['current_page'] = currentPage;
    data['last_page'] = lastPage;
    data['total'] = total;
    return data;
  }
}

class DaoModelData {
  int? id;
  String? name;
  String? logo;
  String? gName;
  String? pName;
  int? userId;
  int? assetsNumber;
  int? memberNumber;
  String? userName;
  String? headerImg;
  String? address;
  int? redDot;
  String? remark;
  int? joinCondition;
  int?identityType;

  DaoModelData(
      {this.id,
        this.name,
        this.logo,
        this.gName,
        this.pName,
        this.userId,
        this.assetsNumber,
        this.memberNumber,
        this.userName,
        this.headerImg,
        this.address,
        this.redDot,
        this.remark,
        this.identityType,
        this.joinCondition});

  DaoModelData.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    logo = json['logo'];
    gName = json['g_name'];
    pName = json['p_name'];
    userId = json['user_id'];
    assetsNumber = json['assets_number'];
    memberNumber = json['member_number'];
    userName = json['user_name'];
    headerImg = json['header_img'];
    address = json['address'];
    redDot = json['red_dot'];
    remark = json['remark'];
    joinCondition = json['join_condition'];
    identityType=json['identity_type'];
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    data['logo'] = logo;
    data['g_name'] = gName;
    data['p_name'] = pName;
    data['user_id'] = userId;
    data['assets_number'] = assetsNumber;
    data['member_number'] = memberNumber;
    data['user_name'] = userName;
    data['header_img'] = headerImg;
    data['address'] = address;
    data['red_dot'] = redDot;
    data['remark'] = remark;
    data['join_condition'] = joinCondition;
    data['identity_type']=identityType;
    return data;
  }
}
