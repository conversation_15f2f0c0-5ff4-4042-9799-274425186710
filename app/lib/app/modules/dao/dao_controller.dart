import 'package:flutter/widgets.dart';
import 'package:flutter_metatel/app/data/services/network_connect_service.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:get/get.dart';

import '../../../core/values/config.dart';
import '../../../routes/pages.dart';
import 'api/dao_api.dart';
import 'bean/dao_model.dart';
import 'daoBrowser/dao_browser_view.dart';
import 'dao_config.dart';

class DaoController extends GetxController {
  RxList<DaoModelData> allDaoObs = RxList<DaoModelData>();
  int _page = 1;
  int _totalPage = -1;
  late ScrollController scrollController;
  RxInt loadType = LoadType.defaultState.obs;

  @override
  void onInit() {
    super.onInit();
    scrollController = ScrollController();
    scrollController.addListener(() {
      AppLogger.d(
          'DaoController  pixels===${scrollController.position.pixels}');
      AppLogger.d(
          'DaoController  maxScrollExtent===${scrollController.position.maxScrollExtent}');
      if (scrollController.position.pixels ==
          scrollController.position.maxScrollExtent) {
        if (_totalPage > 0) {
          _page++;
          _onLoadMore();
        }
      }
    });
    _onLoadMore(first: true);
  }

  _onLoadMore({bool first = false}) async {
    var net = await Get.find<NetWorkConnectService>().networkConnected();
    if (!net) {
      loadType.value = LoadType.error;
    }
    var res = await Get.find<DaoApiProvider>().getDaoList(10, _page);
    AppLogger.d('_onLoadMore==${res.data?.toJson()}');
    if (first) {
      allDaoObs.clear();
      allDaoObs.add(DaoModelData(
        id: -1,
        name: 'test',
      ));
    }
    if (res.statusCode == 200) {
      loadType.value = LoadType.success;
      var body = res.data;
      if (body != null && body.code == 200) {
        _totalPage = body.data?.total ?? 0;
        if (body.data?.list?.isNotEmpty ?? false) {
          allDaoObs.addAll(body.data!.list!);
        }
      }
    } else {
      loadType.value = LoadType.error;
    }
  }

  Future<void> refreshData() async {
    allDaoObs.clear();
    _totalPage = -1;
    _page = 1;
    loadType.value = LoadType.loading;
    _onLoadMore(first: true);
  }
  reloading(){
    refreshData();
  }
  void itemClick(DaoModelData data) {
    DaoBean bean = DaoBean(DaoJoinWebViewType.daoDetail, id: data.id);
    Get.toNamed(
      Routes.DaoBrowserView,
      arguments: bean,
    );
  }

  bool isLoading() {
    return loadType.value == LoadType.loading;
  }

  bool isLoadError() {
    return loadType.value == LoadType.error;
  }

  bool isLoadSuccess() {
    return loadType.value == LoadType.success;
  }

  @override
  void dispose() {
    scrollController.dispose();
    super.dispose();
  }
}
