import 'dart:io';
import 'dart:ui';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:flutter_metatel/core/values/colors.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:get/get.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../../core/utils/util.dart';
import '../../../../r.dart';
import 'dao_browser_controller.dart';

class DaoBrowserView extends GetView<DaoBrowserController> {
  DaoBrowserView({Key? key}) : super(key: key);

  final GlobalKey webViewKey = GlobalKey();

  InAppWebViewController? webViewController;
  void request()async{
    Map<Permission, PermissionStatus> statuses = await [
      Permission.microphone,
      Permission.camera,
      Permission.storage,
      Permission.photos,
    ].request();
    if (kDebugMode) {
      print('request${statuses[Permission.microphone]}');
    }
  }
  @override
  final DaoBrowserController controller =
      Get.put(DaoBrowserController(), permanent: true);

  @override
  Widget build(BuildContext context) {
    AppLogger.d('DaoBrowserView ...message');
    request();
    return WillPopScope(
        onWillPop: Platform.isIOS ? null : () => controller.goBack02(),
        child: Scaffold(
          appBar: PreferredSize(
            preferredSize:
                Size.fromHeight(MediaQueryData.fromView(View.of(context)).padding.top),
            child: const SafeArea(
              top: true,
              child: Offstage(),
            ),
          ),
          body: Obx(() {
            return controller.webInitUrl.value.isEmpty
                ? Container()
                : Center(
                    child: InAppWebView(
                      key:webViewKey,
                      initialUrlRequest: URLRequest(url:WebUri(controller.webInitUrl.value)),
                        initialSettings: InAppWebViewSettings(
                          javaScriptCanOpenWindowsAutomatically: true,
                          supportMultipleWindows: true,
                          isFraudulentWebsiteWarningEnabled: true,
                          safeBrowsingEnabled: true,
                          mediaPlaybackRequiresUserGesture: false,
                          allowsInlineMediaPlayback: true,
                        ),
                      onLoadStart: (c,url){
                        AppLogger.d('InAppWebView onLoadStart');
                       // controller.installListener(c,context);
                      },
                      onLoadError:(c,url,code,msg){
                        AppLogger.d('InAppWebView onLoadError msg=$msg');
                      },
                      onLoadHttpError:(c,url,code,msg){
                        AppLogger.d('InAppWebView onLoadHttpError msg=$msg');
                      },
                      shouldOverrideUrlLoading: (c,n) async{
                        AppLogger.d('InAppWebView shouldOverrideUrlLoading=====${n.request.url}');
                        //await controller.installListener(c,context);
                        jump(n.request.url);
                        return NavigationActionPolicy.ALLOW;
                      },
                      onWebViewCreated: (c) {
                        AppLogger.d('InAppWebView onWebViewCreated');
                        webViewController = c;
                        controller.setWebViewController(c);
                        controller.installListener(c,context);
                      },
                      onDownloadStartRequest: (controller, downloadStartRequest) async {
                        print("InAppWebView Download file ${downloadStartRequest.url}");
                        var fileUrl=downloadStartRequest.url.toString();
                        var filePath;
                        if (fileUrl.isNotEmpty) {
                          filePath = await downloadFile(
                            fileUrl,
                          );
                        }
                        print("InAppWebView Downloaded task $filePath");
                      },
                        androidOnPermissionRequest: (controller, origin, resources) async {
                        return PermissionRequestResponse(
                            resources: resources,
                            action: PermissionRequestResponseAction.GRANT);
                      },
                    onLoadStop: (cont,url) {
                    controller.webCurUrl = url?.toString();
                    AppLogger.d('InAppWebView.....controller.webCurUrl====${ controller.webCurUrl}');
                    },
                    onConsoleMessage: (c,msg){
                        AppLogger.d('onConsoleMessage $msg');
                      },
                  ));
          }),
          floatingActionButton: Row(
              mainAxisAlignment:MainAxisAlignment.center,
            children: [
              Padding(
                  padding: EdgeInsets.only(left: 16.r,top: 5.r),
                  child: BackButton(
                    color: Colors.black,
                    onPressed: () async {controller.goBack02();},
                  )),
              const Spacer(),
              Container(
                margin: const EdgeInsets.only(top: 9).r,
            padding: const EdgeInsets.only(left: 10, right: 10).r,
            width: 75.w,
            height: 25.h,
            decoration: BoxDecoration(
                color: AppColors.colorFFFFFFFB,
                borderRadius: BorderRadius.all(
                  const Radius.circular(16).r,
                ),
                border:
                Border.all(color: AppColors.colorFFd9d9d9, width: 0.5.w)),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                GestureDetector(
                  onTap: () {
                    controller.shareOrClean(context);
                  },
                  onLongPress:(){
                    controller.clearCacheData(context);
                  },
                  child: Image.asset(
                    R.icoShare,
                    width: 17.w,
                    height: 17.h,
                  ),
                ),
                Container(
                  height: 17.5.h,
                  width: 0.5.w,
                  color: AppColors.colorFFF8F8F8,
                ),
                GestureDetector(
                  onTap: () {
                    Get.back();
                  },
                  child: Image.asset(
                    R.icoClose,
                    width: 17.w,
                    height: 17.h,
                  ),
                ),
              ],
            ),
          )],) ,
          floatingActionButtonLocation: FloatingActionButtonLocation.endTop,
        )) /*)*/;
  }

  jump(Uri? url) async {
    if (url != null) {
      AppLogger.d("InAppWebView onLoadStart url:${url.toString()}");
    }

    if (url != null) {
      if (!["http", "https", "file", "chrome", "data", "javascript", "about"]
          .contains(url.scheme)) {
        if (await launchUrl(url)) {
          // Launch the App
          controller.launchURL(url);
        } else {
          if (url.scheme == "weixin") {
            AppLogger.d("onLoadStart 手机尚未安装微信");
          } else if (url.scheme == "alipays") {
            AppLogger.d("onLoadStart 手机尚未安装支付宝");
          } else {
            AppLogger.d("onLoadStart 启动支付失败。原因尚未支持 ${url.scheme},请联系客服");
          }
        }
      }
    }
  }


}

class DaoBean {
  int type = 0;
  int? id;
  int? mintId;
  String? url;
  DaoBean(this.type, {this.id,this.url});
  DaoBean.fromJson(Map<String,dynamic> sourceJson){
    type=sourceJson['type'];
    id=sourceJson["id"];
    url=sourceJson["url"];
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['type'] = type;
    data['id'] = id;
    data['mint_id'] = mintId;
    data['url'] = url;
    return data;
  }
}
