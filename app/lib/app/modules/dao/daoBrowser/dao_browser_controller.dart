import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:flutter_metatel/app/data/services/config_service.dart';
import 'package:flutter_metatel/core/values/config.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:share_plus/share_plus.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../../core/languages/l.dart';
import '../../../../core/utils/app_log.dart';
import '../../../../core/utils/device_util.dart';
import '../../../../core/utils/util.dart';
import '../../../../core/values/colors.dart';
import '../../../../r.dart';
import '../../../data/models/res/oauth2_authorize.model.dart';
import '../../../data/providers/api/api.dart';
import '../../../data/services/chatio_service.dart';
import '../dao_config.dart';
import 'dao_browser_view.dart';

class DaoBrowserController extends GetxController {
  InAppWebViewController? _webViewController;
  RxString webInitUrl = "".obs;
  String? webCurUrl;
  DaoBean? daoBean;

  get webViewController => _webViewController;

  void setWebViewController(InAppWebViewController webViewController) {
    _webViewController = webViewController;
  }

  @override
  void onInit() async {
    super.onInit();
    if (Get.arguments != null || Get.arguments.runtimeType == DaoBean) {
      daoBean = Get.arguments;
    }
    AppLogger.d('DaoBrowserController original url: ${daoBean?.toJson()}');
    String? userName = await Get.find<AppConfigService>().getUserName();
    if (userName != null) {
         // webInitUrl.value='http://192.168.139.66:9111?node=dev.metatel.com.cn';//Config.webViewStare;
         webInitUrl.value =DeviceUtil.isIOS()?'${DaoConfig.getDaoIosUrl()}${Config.node()}':'${DaoConfig.getDaoAndroidUrl()}${Config.node()}'; //Config.webViewStare;
         // webInitUrl.value='http://192.168.138.26:8087/#/pages/integral/dome';//Config.webViewStare;
        // webInitUrl.value = (daoBean?.type??0)==DaoJoinWebViewType.banner?'http://192.168.138.26:8086/#/pages/index/indexs?node=dev.metatel.com.cn':'http://192.168.138.26:8085?node=dev.metatel.com.cn';
    }
    AppLogger.d('DaoBrowserController original url: ${webInitUrl.value}');
  }
  ///是否直接回到上级界面
  goBack02() async {
    AppLogger.d('goBack..$_webViewController');
    if (_webViewController == null) {
      return;
    }
    var canBackWebView = await _webViewController!.canGoBack();
    if (canBackWebView) {
      AppLogger.d('goBack..2');
      _webViewController!.goBack();
    } else {
      AppLogger.d('goBack..3');
      Get.back();
    }
  }

  share() async {
    if (webCurUrl == null) {
      AppLogger.e("页面未加载完成无法分享！");
      return;
    }
    String? title =
        await _webViewController?.evaluateJavascript(source: "document.title");
    title = title ?? "".replaceAll("\"", "");
    String shareUrl = webCurUrl!.split("?token=").first;
    Share.share(webCurUrl!,
        subject: title);
  }

  void shareOrClean(BuildContext context) async{
    share();
  }

  clearCacheData(BuildContext ct) {
    showBottomDialogCommonWithCancel(
      ct,
      isCancel: false,
      widgets: [
        Container(
          alignment: Alignment.center,
          padding: const EdgeInsets.only(top: 35, bottom: 50).r,
          child: Text(
            L.whether_to_clear_th_cache.tr,
            style: TextStyle(
              fontSize: 20.sp,
            ),
          ),
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            SizedBox(
              width: 36.w,
            ),
            Expanded(
              flex: 1,
              child: TextButton(
                style: ButtonStyle(
                  fixedSize: MaterialStateProperty.all(Size.fromHeight(39.h)),
                  shape: MaterialStateProperty.all(
                    RoundedRectangleBorder(
                      borderRadius: BorderRadius.all(Radius.circular(5.r).r).r,
                    ),
                  ),
                  overlayColor: MaterialStateProperty.all(Colors.transparent),
                  foregroundColor: MaterialStateProperty.resolveWith((states) {
                    return states.contains(MaterialState.pressed)
                        ? Colors.black54
                        : Colors.black38;
                  }),
                  backgroundColor:
                      MaterialStateProperty.all(AppColors.appDefault),
                ),
                onPressed: () {
                  _webViewController?.clearCache();
                  Get.back();
                },
                child: Container(
                  height: 39.h,
                  alignment: Alignment.center,
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        L.confirm.tr,
                        style: TextStyle(
                          color: AppColors.white,
                          fontSize: 16.sp,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            SizedBox(
              width: 20.w,
            ),
            Expanded(
              flex: 1,
              child: TextButton(
                style: ButtonStyle(
                  fixedSize: MaterialStateProperty.all(Size.fromHeight(39.h)),
                  shape: MaterialStateProperty.all(
                    RoundedRectangleBorder(
                      borderRadius: BorderRadius.all(Radius.circular(5.r).r).r,
                    ),
                  ),
                  overlayColor: MaterialStateProperty.all(Colors.transparent),
                  foregroundColor: MaterialStateProperty.resolveWith((states) {
                    return states.contains(MaterialState.pressed)
                        ? Colors.black54
                        : Colors.black38;
                  }),
                  backgroundColor: MaterialStateProperty.all(Colors.grey),
                ),
                onPressed: () {
                  Get.back();
                },
                child: Container(
                  height: 40.h,
                  alignment: Alignment.center,
                  child: Text(
                    L.cancel.tr,
                    style: TextStyle(
                      color: AppColors.white,
                      fontSize: 16.sp,
                    ),
                  ),
                ),
              ),
            ),
            SizedBox(
              width: 36.w,
            ),
          ],
        ),
        SizedBox(
          height: 30.h,
        ),
      ],
    );
  }

  ///根据webView 传过来的 clientId 获取授权code 并给到webView
  getCode(String clientId) async {
    if (clientId.isEmpty) {
      toast(L.authorization_failed_please_try_again_late.tr);
      return;
    }
    var response =
        await Get.find<ApiProvider>().getAuth2Authorize(clientId, "all");
    Oauth2AuthorizeModelData? data = response.data?.data;
    if (data == null || _webViewController == null) {
      AppLogger.d(
          "getCode Oauth2AuthorizeModelData==$data  _webViewController=$_webViewController");
      toast(L.authorization_failed_please_try_again_late.tr);
      return;
    }
    data.publicKey = Config.publicKeyBase64Dao;
    data.privateKey = Config.privateKeyBase64Dao;
    data.address = Config.publicKeyBase64DaoAddress;
    var conf = Get.find<AppConfigService>();
    var userNameWithoutDomain = await conf.getUserNameWithoutDomain();
    data.userName=userNameWithoutDomain;
    data.avatarUrl= conf.getMySelfAvatarInfoModel().avatarUrl;
    data.displayName= conf.getMySelfDisplayName();
    String jsonData = json.encode(data.toJson());
    AppLogger.d("getCode jsonData==$jsonData");
    _webViewController!.evaluateJavascript(source: 'callJS($jsonData)');
    // Get.back();
  }

  void setJsMethodType() {
    var jsonData = json.encode(daoBean?.toJson());
    AppLogger.d("DaoBrowserController type==$jsonData $_webViewController");
    _webViewController!.evaluateJavascript(source: 'callDaoJS(${jsonData})');
    AppLogger.d("DaoBrowserController type222==");
  }

  void setJsMethodData() {
    String jsonData = json.encode(daoBean?.toJson());
    AppLogger.d("DaoBrowserController id==$jsonData $_webViewController");
    _webViewController!
        .evaluateJavascript(source: 'callDaoDataJS(${daoBean?.id})');
  }

  void realName() async {
    AppLogger.d("DaoBrowserController realName");
    initToken();
  }

  installListener(InAppWebViewController c, BuildContext context) {
    c.addJavaScriptHandler(
        handlerName: WebViewJsChannel.GetToken,
        callback: (args) {
          AppLogger.d("DaoBrowserController ClientID====${args.toString()}");
          if (args.isNotEmpty) {
            getCode(args[0]);
          }
        });
    c.addJavaScriptHandler(
        handlerName: WebViewJsChannel.GetAction,
        callback: (args) {
          setJsMethodType();
        });
    c.addJavaScriptHandler(
        handlerName: WebViewJsChannel.GetData,
        callback: (args) {
          setJsMethodData();
        });
    c.addJavaScriptHandler(
        handlerName: WebViewJsChannel.Real_name,
        callback: (args) {
          AppLogger.d("DaoBrowserController Real_name====${args.toString()}");
          realName();
        });
    c.addJavaScriptHandler(
        handlerName: WebViewJsChannel.OpenUrl,
        callback: (args) {
          AppLogger.d("DaoBrowserController Real_name====${args.toString()}");
          openUrl(args[0]);
        });
  }

  Widget buildBottomGetTokenWidget(BuildContext context, String clientId) {
    return Center(
      child: Column(
        children: [
          Container(
            margin: const EdgeInsets.only(top: 20).r,
            child: Image.asset(
              R.icoStare,
              width: 48.h,
              height: 48.h,
            ),
          ),
          Container(
            margin: const EdgeInsets.only(top: 20).r,
            child: Text(L.main_dao.tr),
          ),
          Container(
            margin: const EdgeInsets.only(top: 30).r,
            child: Text(
              L.apply_for_your_nickname_and_avatar.tr,
              style: TextStyle(fontSize: 16.sp, fontWeight: FontWeight.bold),
            ),
          ),
          Container(
            margin: const EdgeInsets.only(top: 43, bottom: 26).r,
            child: Row(
              children: [
                const Spacer(),
                TextButton(
                  style: ButtonStyle(
                    shape: MaterialStateProperty.all(
                      RoundedRectangleBorder(
                        borderRadius:
                            BorderRadius.all(Radius.circular(19.r).r).r,
                      ),
                    ),
                    side: MaterialStateProperty.all(
                      BorderSide(color: AppColors.colorFFd9d9d9, width: 0.5.w),
                    ),
                    fixedSize: MaterialStateProperty.all(Size(132.w, 39.h)),
                  ),
                  onPressed: () {
                    Get.back();
                  },
                  child: Text(
                    L.refuse.tr,
                    overflow: TextOverflow.ellipsis,
                    style: TextStyle(
                      fontSize: 16.sp,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                  ),
                ),
                SizedBox(
                  width: 27.w,
                ),
                TextButton(
                  style: ButtonStyle(
                    shape: MaterialStateProperty.all(
                      RoundedRectangleBorder(
                        borderRadius:
                            BorderRadius.all(Radius.circular(19.r).r).r,
                      ),
                    ),
                    side: MaterialStateProperty.all(
                      BorderSide(
                          color: Theme.of(context).colorScheme.primary,
                          width: 0.5.w),
                    ),
                    fixedSize: MaterialStateProperty.all(Size(132.w, 39.h)),
                    backgroundColor: MaterialStateProperty.all(
                      Theme.of(context).colorScheme.primary,
                    ),
                  ),
                  onPressed: () async {
                    getCode(clientId);
                  },
                  child: Text(
                    L.allow.tr,
                    overflow: TextOverflow.ellipsis,
                    style: TextStyle(fontSize: 16.sp, color: AppColors.white),
                  ),
                ),
                const Spacer(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void openUrl(String url) {
    launchURL(Uri.parse(url));
  }

  void launchURL(Uri url) async => await canLaunchUrl(url)
      ? await launchUrl(url)
      : AppLogger.d("onLoadStart 对不起，打不开链接地址：${url}");

  Future<void> downloadFile(String url, [String? filename]) async {
    // var hasStoragePermission = await Permission.storage.isGranted;
    // if (!hasStoragePermission) {
    //   final status = await Permission.storage.request();
    //   hasStoragePermission = status.isGranted;
    // }
    // if (hasStoragePermission) {
    //   final taskId = await FlutterDownloader.enqueue(
    //       url: url,
    //       headers: {},
    //       // optional: header send with url (auth token etc)
    //       savedDir: (await getTemporaryDirectory()).path,
    //       saveInPublicStorage: true,
    //       fileName: filename);
    // }
  }
}
