//FileName dao_config
// <AUTHOR>
//@Date 2022/11/16 15:15

import 'package:flutter_metatel/app/data/services/config_service.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:get/get.dart';

import 'bean/banner_bean.dart';

class DaoConfig{
  static String? daoBaseUrl;
  static String? daoAndroidUrl;
  static String? daoIosUrl;
  static List<BannerBean>?bannerList;

  static String getDaoBaseUrl(){
    if(daoBaseUrl==null){
      var nodeConf = Get.find<AppConfigService>().getNodeConfFromLocal();
      daoBaseUrl = nodeConf?.data?.daoConf?.daoBaseUrl;
    }
   return daoBaseUrl??"http://frp.metatel.com.cn:18099";
    // return 'http://192.168.138.110:8083';

  }
  static String getDaoAndroidUrl(){
    if(daoAndroidUrl==null){
      var nodeConf = Get.find<AppConfigService>().getNodeConfFromLocal();
      daoAndroidUrl = nodeConf?.data?.daoConf?.daoAndroidUrl;
    }
    return daoAndroidUrl??'http://frp.metatel.com.cn:18121/?node=';
  }
  static String getDaoIosUrl(){
    if(daoIosUrl==null){
      var nodeConf = Get.find<AppConfigService>().getNodeConfFromLocal();
      daoIosUrl = nodeConf?.data?.daoConf?.daoIosUrl;
    }
    return daoIosUrl??'http://frp.metatel.com.cn:18120/?node=';
  }

  static List<BannerBean>? getDaoBanner() {
    if (bannerList == null) {
      var nodeConf = Get.find<AppConfigService>().getNodeConfFromLocal();
      AppLogger.d('getDaoBanner 222=${nodeConf?.toJson()}');
      bannerList = nodeConf?.data?.daoConf?.banner;
      AppLogger.d('getDaoBanner 222=${nodeConf?.toJson()}');

    }
    AppLogger.d('getDaoBanner ${bannerList.toString()}');
    return bannerList;
  }
}
class DaoJoinWebViewType{
  static const personDao=0;
  static const enterpriseDao=1;
  static const daoDetail=2;
  static const pubDynamic=3;
  static const pubWork=4;
  static const myOrder=5;
  static const createCenter=6;
  static const pointsManagement=7;
  static const myAccount=8;
  static const ddcInfo=9;
  static const pledge=10;
  static const banner=11;

}
class DaoJoinWebViewAction{
  static const createPerson='createPerson';
  static const createEnterprise='createEnterprise';
  static const details='details';
}