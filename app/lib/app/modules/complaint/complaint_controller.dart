import 'package:flutter/widgets.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:flutter_metatel/core/utils/util.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:get/get.dart';

import '../../../core/languages/l.dart';
import '../../../core/task/complaint_message_task.dart';
import '../message/message_controller.dart';
import 'complaint_helper.dart';
import 'complaint_item_view.dart';

class ComplaintController extends GetxController {
  final TextEditingController _textEditingController = TextEditingController();
  MessageController? msgController;
  TextEditingController get textEditingController => _textEditingController;
  List<ComplaintItemBean> list = List<ComplaintItemBean>.empty(growable: true);

  @override
  void onInit() {
    list.add(ComplaintItemBean(type: 1, name: L.pornographic_vulgar.tr));
    list.add(ComplaintItemBean(type: 2, name: L.bloody_violence.tr));
    list.add(ComplaintItemBean(type: 3, name: L.false_promotional_links.tr));
    list.add(ComplaintItemBean(type: 4, name: L.malicious_fraud.tr));
    list.add(ComplaintItemBean(type: 5, name: L.disgusting_content.tr));
    list.add(ComplaintItemBean(type: 6, name: L.other));
    super.onInit();
  }

  void setMsgController(MessageController c){
    msgController=c;
  }
  @override
  void onClose() {
    _textEditingController.clear();
    super.onClose();
  }

  List<ComplaintItemBean> getItem() {
    return list;
  }

  void updateBean(ComplaintItemBean bean) {
    if(list.isEmpty){
      return;
    }
    var index=-1;
    for(var i=0;i<list.length;i++){
      var b=list[i];
      if(b.type==bean.type){
        index=i;
      }else{
        b.isChecked=false;
        update([b.type??'']);
      }
    }
    if(index>=0){
      list.removeAt(index);
      list.insert(index,bean);
      update([bean.type??'']);
    }
  }
  submit(BuildContext context) async{
    var desc=_textEditingController.text;
    AppLogger.d('submit desc=$desc');
    String value='';
    var ischecked=false;
    for(var d in list){
      if(d.isChecked??false){
        value+='${d.name}';
        ischecked=true;
      }
    }
    if(!ischecked){
      toast(L.please_select_content.tr);
      return;
    }
    AppLogger.d('submit value=$value');
    var msgs=msgController?.selectedMessageEventList;
    if(msgs?.isEmpty??true){
      toast('error');
      return;
    }
    AppLogger.d('submit msgs=${msgs?.length}');
    showLoadingDialog();
    var b=await ComplaintSendMsgTask(msgs!,body:desc,type: value,owner:getOwner()).process();
    if(b){
      dismissLoadingDialog();
      SmartDialog.dismiss();
      ComplaintHelper.saveComplaintTime();
    }
  }

  String getOwner() {
    return msgController?.currentUser?.userName ?? '';
  }
}
