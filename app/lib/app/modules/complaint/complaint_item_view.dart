//FileName complaint_item_view.dart
// <AUTHOR>
//@Date 2022/12/19 16:19
import 'package:flutter/cupertino.dart';
import 'package:flutter_metatel/core/values/colors.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../r.dart';

class ComplaintItemView extends StatelessWidget {
  final ComplaintItemBean? bean;
  final ValueChanged<ComplaintItemBean>? onPressed;

  const ComplaintItemView({Key? key,this.bean,this.onPressed}):super(key: key);
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: changeChecked,
      child:Padding(
        padding: EdgeInsets.only(top: 6.h,bottom: 6.h),
        child:Row(
        children: [
          Image.asset(
            bean?.isChecked ?? false ? R.contactorChecked : R.contactorDefault,
            width: 20.w,
            height: 20.h,
          ),
          SizedBox(width: 6.r,),
          Text(
            bean?.name ?? '',
            style: TextStyle(fontSize: 14.sp, color: AppColors.colorFF666666),
          )
        ],
      ) ,) ,);
  }
  void changeChecked(){
    bean?.isChecked=!(bean?.isChecked??false);
    onPressed?.call(bean!);
  }
}

class ComplaintItemBean {
  String? name;
  int? type;
  bool? isChecked;

  ComplaintItemBean({this.type, this.name, this.isChecked});
}
