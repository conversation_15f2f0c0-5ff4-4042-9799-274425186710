//FileName complaint_helper
// <AUTHOR>
//@Date 2022/12/28 16:55

import 'package:flutter_metatel/core/task/time_task.dart';
import 'package:get/get.dart';

import '../../../core/utils/app_log.dart';
import '../../data/services/config_service.dart';

class ComplaintHelper {
  static const totalTime = 5;

  static Future<bool> isComplaintTimes() async {
    DateTime dateTime = TimeTask.instance.getNowDateTime();
    var d = dateTime.day;
    var m = dateTime.month;
    var dm = '$m:$d';
    var time = count();
    bool haveTime = true;
    if (time?.containsKey(dm) ?? false) {
      var t = time?.putIfAbsent(dm, () => (0)) ?? 0;
      haveTime = t > 0 && t < totalTime;
    }
    return haveTime;
  }

  static Map<String, int>? count() {
    Map<String, int>? count = {};
    try {
      count = Get.find<AppConfigService>().readComplaintTime();
    } catch (e) {
      e.printInfo();
      AppLogger.e('error ${e.toString()}');
    }
    return count;
  }

  static void saveComplaintTime() async {
    DateTime dateTime = TimeTask.instance.getNowDateTime();
    var d = dateTime.day;
    var m = dateTime.month;
    var dm = '$m:$d';
    var time = count();
    bool haveTime = false;
    if (time?.containsKey(dm) ?? false) {
      var t = time?.putIfAbsent(dm, () => (0)) ?? 0;
      haveTime = t > 0 && t < totalTime;
      if (haveTime) {
        time?.update(dm, (value) => t + 1);
        await Get.find<AppConfigService>().saveComplaintTime(time!);
      }
    } else {
      var info = {dm: 1};
      await Get.find<AppConfigService>().saveComplaintTime(info);
    }
  }
}
