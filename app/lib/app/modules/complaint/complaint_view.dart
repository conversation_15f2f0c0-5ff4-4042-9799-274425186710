import 'package:flutter/material.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';

import 'package:get/get.dart';

import '../../../core/languages/l.dart';
import '../../../core/utils/util.dart';
import '../../../core/values/colors.dart';
import '../../../r.dart';
import '../../widgets/divider_cus.dart';
import '../message/message_controller.dart';
import 'complaint_controller.dart';
import 'complaint_item_view.dart';

class ComplaintView extends StatefulWidget {
  const ComplaintView(this.msgController,{Key? key}) : super(key: key);
  final MessageController msgController;
  @override
  State<StatefulWidget> createState() {

    return _ComplaintViewState();
  }
}

class _ComplaintViewState extends State<ComplaintView> {
  ComplaintController controller = Get.put(ComplaintController());
  final ThrottleUtil throttleUtil = ThrottleUtil();
  @override
  void initState() {
    super.initState();
    controller.setMsgController(widget.msgController);
  }
  @override
  void dispose() {
    Get.delete<ComplaintController>();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    TextStyle titleStyle =
        TextStyle(fontSize: 14.sp, color: AppColors.colorFF333333);
    return Scaffold(
      appBar: AppBar(
        title: Text(L.complaint.tr),
        automaticallyImplyLeading: true,
        leading: BackButton(
          onPressed: () async {
            SmartDialog.dismiss();
          },
        ),
        centerTitle: true,
      ),
      backgroundColor: Colors.white,
      body: Stack(
        children: [
          ListView(
            children: [
              Column(
                children: [
                  const DividerCus(),
                  Container(
                    height: 45.h,
                    color: Colors.white,
                    margin: EdgeInsets.only(top: 1.r),
                    padding: EdgeInsets.only(left: 16.r, right: 16.r),
                    child: GestureDetector(
                      onTap: (){
                        widget.msgController.setComplaintChange(true);
                        SmartDialog.dismiss();
                      },
                      child:Row(
                      children: [
                        Expanded(
                          child:
                          Text(L.complaint_message.tr, style: titleStyle),
                        ),
                        Text(
                          L.complaint_total_msg.trParams(
                            {'num': '${getTotalMsg()}'},
                          ),
                        ),
                        const SizedBox(width: 2,),
                        Image.asset(
                          R.nextArrowGrey,
                          width: 6.w,
                          height: 10.h,
                        )
                      ] ,),
                    ),
                  ),
                  SizedBox(
                    height: 10.r,
                  ),
                  Container(
                    height: 45.h,
                    padding:
                    EdgeInsets.only(left: 16.r, right: 16.r),
                    alignment: Alignment.centerLeft,
                    child: Text(L.reason_for_complaint.tr, style: titleStyle),
                  ),
                  Container(
                    padding:
                        EdgeInsets.only(left: 16.r, right: 16.r, bottom: 20.r),
                    constraints:
                        BoxConstraints(maxHeight: getMaxHeight(), minHeight: 150.h),
                    child: ListView.builder(
                      padding: EdgeInsets.only(bottom: 0.r),
                      itemCount: controller.getItem().length,
                      itemBuilder: (BuildContext context, int index) {
                        var model = controller.getItem()[index];

                        return GetBuilder<ComplaintController>(
                          id: model.type,
                          builder: (controller) {
                            return ComplaintItemView(
                                bean: model, onPressed: controller.updateBean);
                          },
                        );
                      },
                    ),
                  ),
                  Container(
                    margin: EdgeInsets.only(top: 5.r),
                    padding:
                        EdgeInsets.only(left: 16.r, right: 16.r, bottom: 15.r),
                    alignment: Alignment.centerLeft,
                    child: Text(L.complaints.tr, style: titleStyle),
                  ),
                  Container(
                    margin:
                        EdgeInsets.only(left: 16.r, right: 16.r, bottom: 20.r),
                    padding: const EdgeInsets.only(left: 10, right: 10),
                    constraints: const BoxConstraints(minHeight: 122),
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                            color: AppColors.colorFFF8F8F8, width: 1)),
                    child: TextField(
                      controller: controller.textEditingController,
                      textAlignVertical: TextAlignVertical.bottom,
                      keyboardType: TextInputType.multiline,
                      maxLines: null,
                      decoration: InputDecoration(
                          hintMaxLines: 4,
                          hintText: L.please_input.tr,
                          border: InputBorder.none,
                          hintStyle:
                              const TextStyle(color: AppColors.colorFFB3B3B3)),
                    ),
                  ),
                  ElevatedButton(
                    onPressed: throttleUtil.throttle(() async {
                      FocusScope.of(context).requestFocus(FocusNode());
                      controller.submit(context);

                    }),
                    child: Text(
                      L.submit.tr,
                    ),
                  ),
                  SizedBox(
                    height: 20.h,
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }
  double getMaxHeight(){
    var it=controller.list.length;
    var height=it*30.h;
    AppLogger.d('ComplaintController height=$height');
    if(height<150.h){
      return 200.h;
    }
    return height+30;
  }
  int getTotalMsg(){
    return widget.msgController.selectedMessageEventList.length;
  }
}
