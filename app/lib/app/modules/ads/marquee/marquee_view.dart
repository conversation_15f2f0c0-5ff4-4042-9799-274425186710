//FileName marquee_view
// <AUTHOR>
//@Date 2024/1/25 11:53
import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';
class MarqueeView extends StatefulWidget {
  const MarqueeView({required this.child, this.titel='',this.speed = 10, Key? key})
      : super(key: key);
  final Widget child;
  final String titel;
  final int speed;

  @override
  State createState() => _MarqueeState();
}

class _MarqueeState extends State<MarqueeView> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;
  late ScrollController _scrctrl;
  SingleChildScrollView? _scrollView;

  double _space = 0;
  bool isDispose = false;
  @override
  void initState() {
    super.initState();

    _scrctrl = ScrollController();

    _controller = AnimationController(
        vsync: this, duration: Duration(seconds: widget.speed));
    _animation = Tween(
      begin: 1.0,
      end: 100.0,
    ).animate(_controller);
    _animation.addListener(() {
      if (_scrctrl.hasClients) {
        if (_scrollView != null && _scrctrl.position.hasContentDimensions &&!isDispose) {
          var index = _animation.value / 100;
          _scrctrl.jumpTo(index * _scrctrl.position.maxScrollExtent);
        }
        if (_scrctrl.position.hasViewportDimension && _space == 0) {
          setState(() {
            if (!isDispose) {
              _space = _scrctrl.position.viewportDimension;
            }
          });
        }
      }
    });
    _controller.repeat();
  }

  @override
  Widget build(BuildContext context) {
    AppLogger.d('MarqueeView _space =$_space');

    _scrollView = SingleChildScrollView(
      controller: _scrctrl,
      scrollDirection: Axis.horizontal,
      child: _scrctrl.hasClients
          ? Row(
        children: [
          SizedBox(
            width: _space,
          ),
          widget.child,
          SizedBox(
            width: _space,
          ),
        ],
      )
          : const SizedBox(),
    );
    return _scrollView ?? const Column();
  }

  @override
  void dispose() {
    isDispose = true;
    _controller.dispose();
    super.dispose();
  }
}
