//FileName banner_view
// <AUTHOR>
//@Date 2022/10/25 16:00
import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/data/enums/enum.dart';
import 'package:flutter_metatel/app/data/models/ads_bean.dart';
import 'package:flutter_metatel/app/data/models/user_message_model.dart';
import 'package:flutter_metatel/app/modules/ads/marquee/marquee_view.dart';
import 'package:flutter_metatel/core/task/ads_task.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:flutter_web3/wallet.dart';
import 'package:get/get.dart';

import '../../../../core/utils/jump.dart';
import '../../../../core/values/colors.dart';
import '../../../../routes/pages.dart';
import 'switcher.dart';

class AdsMarqueeWidget extends StatefulWidget {
  const AdsMarqueeWidget({super.key, required this.adsList});

  final List<AdsBean> adsList;

  @override
  State<AdsMarqueeWidget> createState() {
    return _BannerState();
  }
}

class _BannerState extends State<AdsMarqueeWidget> {
  @override
  void initState() {
    super.initState();
  }

  @override
  void didUpdateWidget(covariant AdsMarqueeWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        SizedBox(
          height: 44.r,
          child: Switcher.vertical(delayedDuration:const Duration(seconds: 8),scrollDelta:6, children: createBannerView())/*_BannerView(
              switchDuration: const Duration(seconds: 6),
              children: createBannerView()),*/
        ),
        Container(
          height: 10.r,
          color: AppColors.colorFFF2F2F2,
        )
      ],
    );
  }

  closeDialog() {
    SmartDialog.dismiss();
    AdsTask().saveAdsTime();
  }

  List<Widget> createBannerView() {
    var banners = widget.adsList;
    var views = <Widget>[];
    if (banners.isNotEmpty) {
      for (var b in banners) {
        views.add(GestureDetector(
          onTap: () {
            switch (b.type) {
              case 0:
                if (b.content != null &&
                    (b.content!.startsWith('http://') ||
                        b.content!.startsWith('https://'))) {
                  AppLogger.d('banner click ${b.toJson()}');
                  closeDialog();
                  jumpToBrowserMethod(b.content!);
                }
                break;
              case 1:
                String id = b.id ?? '';
                if (id.isNotEmpty) {
                  closeDialog();
                  Get.toNamed(Routes.ChannelJoin, arguments: {"channelId": id});
                }
                break;
              case 2:
                String id = b.id ?? '';
                if (id.isNotEmpty) {
                  var userMessage = UserMessage(
                      chatType: ChatType.singleChat.index,
                      displayName: b.title,
                      userName: id,
                      avatarPath: '');
                  closeDialog();
                  JumpPage.messgae(userMessage);
                }
                break;
            }
          },
          child: Center(
            child: MarqueeView(
              speed: 12,
              titel:b.title ??'' ,
              child: Text(
                b.title ?? '',
                style: TextStyle(
                  fontSize: 14.sp,
                  // decoration: TextDecoration.underline,
                  decorationColor: AppColors.appDefault,
                  color: AppColors.appDefault,
                  decorationThickness: 2,
                ),
                maxLines: 1,
                overflow: TextOverflow.clip,
              ),
            ),
          ),
        ));
      }
    }
    return views;
  }
}

class _BannerView extends StatefulWidget {
  final List<Widget> children;

  final Duration switchDuration;

  const _BannerView({
    this.children = const <Widget>[],
    this.switchDuration = const Duration(seconds: 3),
  });

  @override
  State<_BannerView> createState() {
    return _BannerViewState();
  }
}

class _BannerViewState extends State<_BannerView>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  late PageController _pageController;
  Timer? _timer;
  int _curPageIndex = 0;

  List<Widget> children = [];

  @override
  void initState() {
    super.initState();
    _curPageIndex = 0;

    _tabController = TabController(length: widget.children.length, vsync: this);
    AppLogger.d('widget.children.length==${widget.children.length}');

    /// 添加所有的widget到新建的list中
    children.addAll(widget.children);

    /// 定时器完成自动翻页-只有在大于1时才会有翻页
    if (children.length > 1) {
      children.insert(0, widget.children.last);
      children.add(widget.children.first);

      ///如果大于一页，则会在前后都加一页， 初始页要是 1
      _curPageIndex = 1;
      _timer = Timer.periodic(widget.switchDuration, _nextBanner);
    }

    ///初始页面 指定
    _pageController = PageController(initialPage: _curPageIndex);
  }

  /// 进行翻页的动画
  _nextBanner(Timer timer) {
    _curPageIndex++;
    _curPageIndex = _curPageIndex == children.length ? 0 : _curPageIndex;

    //curve:和android一样 动画插值
    _pageController.animateToPage(_curPageIndex,
        duration: const Duration(milliseconds: 500), curve: Curves.linear);
  }

  @override
  void dispose() {
    /// 页面销毁时进行回收
    _pageController.dispose();
    _tabController.dispose();
    _timer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Listener(

        /// 点击时取消timer效果
        onPointerDown: (_) => _timer?.cancel(),
        onPointerUp: (_) {
          ///重新开启timer
          if (children.length > 1) {
            _timer = Timer.periodic(widget.switchDuration, _nextBanner);
          }
        },
        child: NotificationListener(
          onNotification: (notification) {
            if (notification is ScrollUpdateNotification) {
              ScrollUpdateNotification n = notification;

              /// 判断是否是一次完整的翻页
              if (n.metrics.atEdge) {
                if (_curPageIndex == children.length - 1) {
                  /// 如果是最后一页，那么就跳到第一页
                  _pageController.jumpToPage(1);
                } else if (_curPageIndex == 0) {
                  /// 如果是第一页，再往前滑动，因为原来的list前后都加了一条数据，所以 -2
                  _pageController.jumpToPage(children.length - 2);
                }
              }
            }
            return true;
          },
          child: PageView.builder(
            itemCount: children.length,
            scrollDirection: Axis.vertical,
            itemBuilder: (context, index) {
              /// banner设置点击监听
              return InkWell(
                child: children[index],
                onTap: () {
                  if (kDebugMode) {
                    print("点击Item");
                  }
                },
              );
            },
            onPageChanged: (index) {
              _curPageIndex = index;
              if (index == children.length - 1) {
                /// 如果是最后一页，那么下面的指示器设置为0的位置
                _tabController.animateTo(0);
              } else if (index == 0) {
                ///如果是第一页再往左滑，那么久设置为指示器最后的位置
                _tabController.animateTo(_tabController.length - 1);
              } else {
                _tabController.animateTo(index - 1);
              }
            },
            controller: _pageController,
          ),
        ));
  }
}
