//FileName banner_view
// <AUTHOR>
//@Date 2022/10/25 16:00
import 'dart:async';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/data/enums/enum.dart';
import 'package:flutter_metatel/app/data/models/ads_bean.dart';
import 'package:flutter_metatel/app/data/models/user_message_model.dart';
import 'package:flutter_metatel/core/task/ads_task.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:flutter_metatel/r.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:flutter_web3/wallet.dart';
import 'package:get/get.dart';

import '../../../../core/utils/jump.dart';
import '../../../../core/values/colors.dart';
import '../../../../routes/pages.dart';

class AdsWidget extends StatefulWidget {
  const AdsWidget({super.key, required this.adsList});

  final List<AdsBean> adsList;

  @override
  State<AdsWidget> createState() {
    return _BannerState();
  }
}

class _BannerState extends State<AdsWidget> {
  @override
  void initState() {
    super.initState();
  }

  @override
  void didUpdateWidget(covariant AdsWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        GestureDetector(
          onTap: () {
            SmartDialog.dismiss();
            AdsTask().saveAdsTime();
          },
          child: Image.asset(
            R.icAdsClose,
            width: 30.r,
            height: 30.r,
          ),
        ),
        Container(
          height: 480.r,
          margin: EdgeInsets.only(top: 15.r),
          padding: EdgeInsets.only(left: 35.r, right: 35.r),
          clipBehavior: Clip.hardEdge,
          decoration: BoxDecoration(borderRadius: BorderRadius.circular(30)),
          child: _BannerView(
              switchDuration: const Duration(seconds: 3),
              children: createBannerView()),
        )
      ],
    );
  }

  closeDialog() {
    SmartDialog.dismiss();
    AdsTask().saveAdsTime();
  }

  List<Widget> createBannerView() {
    var banners = widget.adsList;
    AppLogger.d('loadBanner list.length2 = ${banners.length}');
    var views = <Widget>[];
    if (banners.isNotEmpty) {
      for (var b in banners) {
        views.add(GestureDetector(
            onTap: () {
              switch (b.type) {
                case 0:
                  if (b.content != null &&
                      (b.content!.startsWith('http://') ||
                          b.content!.startsWith('https://'))) {
                    AppLogger.d('banner click ${b.toJson()}');
                    closeDialog();
                    jumpToBrowserMethod(b.content!);
                  }
                  break;
                case 1:
                  String id = b.id ?? '';
                  if (id.isNotEmpty) {
                    closeDialog();
                    Get.toNamed(Routes.ChannelJoin,
                        arguments: {"channelId": id});
                  }
                  break;
                case 2:
                  String id = b.id ?? '';
                  if (id.isNotEmpty) {
                    var userMessage = UserMessage(
                        chatType: ChatType.singleChat.index,
                        displayName: b.title,
                        userName: id,
                        avatarPath: '');
                    closeDialog();
                    JumpPage.messgae(userMessage);
                  }
                  break;
              }
            },
            child: Container(
              height: 400.r,
              clipBehavior: Clip.antiAlias,
              decoration: BoxDecoration(
                image: DecorationImage(
                  image: NetworkImage(b.image ?? ''),
                  fit: BoxFit.cover,
                ),
                shape: BoxShape.rectangle,
                borderRadius: BorderRadius.circular(30),
              ),
            )));
      }
    }
    AppLogger.d('loadBanner views.length = ${views.length}');
    return views;
  }
}

class _BannerView extends StatefulWidget {
  final List<Widget> children;

  final Duration switchDuration;

  const _BannerView({
    this.children = const <Widget>[],
    this.switchDuration = const Duration(seconds: 3),
  });

  @override
  State<_BannerView> createState() {
    return _BannerViewState();
  }
}

class _BannerViewState extends State<_BannerView>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  late PageController _pageController;
  Timer? _timer;
  int _curPageIndex = 0;

  List<Widget> children = [];

  @override
  void initState() {
    super.initState();
    _curPageIndex = 0;

    _tabController = TabController(length: widget.children.length, vsync: this);
    AppLogger.d('widget.children.length==${widget.children.length}');

    /// 添加所有的widget到新建的list中
    children.addAll(widget.children);

    /// 定时器完成自动翻页-只有在大于1时才会有翻页
    if (children.length > 1) {
      children.insert(0, widget.children.last);
      children.add(widget.children.first);

      ///如果大于一页，则会在前后都加一页， 初始页要是 1
      _curPageIndex = 1;
      _timer = Timer.periodic(widget.switchDuration, _nextBanner);
    }

    ///初始页面 指定
    _pageController = PageController(initialPage: _curPageIndex);
  }

  /// 进行翻页的动画
  _nextBanner(Timer timer) {
    _curPageIndex++;
    _curPageIndex = _curPageIndex == children.length ? 0 : _curPageIndex;

    //curve:和android一样 动画插值
    _pageController.animateToPage(_curPageIndex,
        duration: const Duration(milliseconds: 500), curve: Curves.linear);
  }

  @override
  void dispose() {
    /// 页面销毁时进行回收
    _pageController.dispose();
    _tabController.dispose();
    _timer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(
          height: 400.r,
          child: Listener(
              /// 点击时取消timer效果
              onPointerDown: (_) => _timer?.cancel(),
              onPointerUp: (_) {
                ///重新开启timer
                if (children.length > 1) {
                  _timer = Timer.periodic(widget.switchDuration, _nextBanner);
                }
              },
              child: NotificationListener(
                onNotification: (notification) {
                  if (notification is ScrollUpdateNotification) {
                    ScrollUpdateNotification n = notification;

                    /// 判断是否是一次完整的翻页
                    if (n.metrics.atEdge) {
                      if (_curPageIndex == children.length - 1) {
                        /// 如果是最后一页，那么就跳到第一页
                        _pageController.jumpToPage(1);
                      } else if (_curPageIndex == 0) {
                        /// 如果是第一页，再往前滑动，因为原来的list前后都加了一条数据，所以 -2
                        _pageController.jumpToPage(children.length - 2);
                      }
                    }
                  }
                  return true;
                },
                child: PageView.builder(
                  itemCount: children.length,
                  itemBuilder: (context, index) {
                    /// banner设置点击监听
                    return InkWell(
                      child: children[index],
                      onTap: () {
                        if (kDebugMode) {
                          print("点击Item");
                        }
                      },
                    );
                  },
                  onPageChanged: (index) {
                    _curPageIndex = index;
                    if (index == children.length - 1) {
                      /// 如果是最后一页，那么下面的指示器设置为0的位置
                      _tabController.animateTo(0);
                    } else if (index == 0) {
                      ///如果是第一页再往左滑，那么久设置为指示器最后的位置
                      _tabController.animateTo(_tabController.length - 1);
                    } else {
                      _tabController.animateTo(index - 1);
                    }
                  },
                  controller: _pageController,
                ),
              )),
        ),
        SizedBox(
          height: 10.r,
        ),
        Container(
          height: 18.r,
          decoration: BoxDecoration(borderRadius: BorderRadius.circular(5)),
          child: TabPageSelector(
            controller: _tabController,
            indicatorSize: 20.r,
            borderStyle: BorderStyle.none,
            color: AppColors.colorFFBFBFBF,
            selectedColor: Colors.white,
          ),
        ),
      ],
    );
  }
}
