import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/modules/meeting/meeting_controller.dart';
import 'package:flutter_metatel/app/modules/meeting/past_meeting_detail_view.dart';
import 'package:flutter_metatel/app/modules/meeting/widgets/meeting_date_item.dart';
import 'package:flutter_metatel/app/modules/meeting/widgets/meeting_item.dart';
import 'package:flutter_metatel/core/languages/l.dart';
import 'package:flutter_metatel/r.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import '../../../core/values/colors.dart';
import '../../data/models/meeting.dart';
import 'widgets/detail_info_item.dart';

class MeetingView extends GetView<MeetingController> {
  const MeetingView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.white,
      appBar: AppBar(
        title: Text(L.meeting.tr),
        centerTitle: true,
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    return Stack(
      children: [
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildTabbar(),
            Expanded(
              child: _buildTabbarView(),
            ),
          ],
        ),

        /// 创建会议按钮
        Positioned(
          right: 16.r,
          bottom: 60.r,
          child: _buildCreateMeetingBtn(
            onTap: controller.onCreateMeetingTap,
          ),
        ),
      ],
    );
  }

  Widget _buildCreateMeetingBtn({Function()? onTap}) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 9.r, horizontal: 10.r),
        decoration: BoxDecoration(
          color: AppColors.colorFF249ED9,
          borderRadius: BorderRadius.circular(18.r),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.2),
              spreadRadius: 2,
              blurRadius: 10,
              offset: Offset(0, 4),
            ),
          ],
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.add,
              size: 20.r,
              color: AppColors.white,
            ),
            SizedBox(width: 3.r),
            Text(
              L.create_meeting_2_cap.tr,
              style: TextStyle(
                color: AppColors.white,
                fontSize: 12.sp,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTabbar() {
    return Row(
      children: [
        TabBar(
          controller: controller.tabController,
          isScrollable: true,
          tabAlignment: TabAlignment.start,
          indicatorColor: AppColors.colorFF249ED9,
          indicatorPadding: EdgeInsets.symmetric(horizontal: 8.r),
          labelStyle: TextStyle(
            fontSize: 13.sp,
            fontWeight: FontWeight.w800,
            color: AppColors.colorFF249ED9,
          ),
          unselectedLabelStyle: TextStyle(
            fontSize: 13.sp,
            color: Colors.black,
            fontWeight: FontWeight.w400,
          ),
          padding: EdgeInsets.symmetric(horizontal: 16.r),
          labelPadding: EdgeInsets.symmetric(vertical: 10.r, horizontal: 16.r),
          tabs: [
            for (var item in controller.tabbarTitles)
              ConstrainedBox(
                constraints: BoxConstraints(minWidth: 40.r),
                child: Center(
                  child: Text(
                    item.tr,
                    style: TextStyle(fontSize: 14.sp),
                  ),
                ),
              ),
          ],
        ),
        // Spacer(),
        // IconButton(
        //   onPressed: () {},
        //   icon: Icon(Icons.search_rounded),
        //   iconSize: 20.r,
        //   color: AppColors.colorFF59595A,
        // ),
        // SizedBox(width: 13.r),
      ],
    );
  }

  Widget _buildTabbarView() {
    return Obx(
      () => TabBarView(
        controller: controller.tabController,
        physics: BouncingScrollPhysics(),
        children: [
          /// Upcoming Meetings
          _buildMeetingList(
            isLoading: controller.isUpcomingLoading.value,
            meetings: controller.upcomingMeeting,
            emptyDesc: L.upcoming_meeting_empty.tr,
            refreshController: controller.upcomingRefreshController,
            onRefresh: controller.onRefreshUpcoming,
          ),

          /// Past Meetings
          _buildMeetingList(
            isLoading: controller.isPastLoading.value,
            meetings: controller.pastMeetings,
            emptyDesc: L.past_meeting_empty.tr,
            isPast: true,
            refreshController: controller.pastRefreshController,
            onRefresh: controller.onRefreshPast,
          ),
        ],
      ),
    );
  }

  Widget _buildMeetingList({
    required bool isLoading,
    required Map<String, List<Meeting>> meetings,
    required String emptyDesc,
    required RefreshController refreshController,
    required Function()? onRefresh,
    bool isPast = false,
  }) {
    return isLoading
        ? _buildLoadingView()
        : !isLoading && meetings.isEmpty
            ? _buildEmptyView(emptyDesc)
            : SmartRefresher(
                controller: refreshController,
                onRefresh: onRefresh,
                header: WaterDropMaterialHeader(
                  backgroundColor: AppColors.colorFFF8F8F8,
                  color: AppColors.colorFF3474D0,
                ),
                child: ListView.builder(
                  itemCount: meetings.length,
                  physics: BouncingScrollPhysics(),
                  itemBuilder: (context, index) {
                    String date = meetings.keys.elementAt(index);
                    List<Meeting> meetingsPerDate =
                        meetings.values.elementAt(index);
                    int lastIndex = controller.getLastIndex(index, meetings);
                    return Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        if (index == 0) SizedBox(height: 15.r),
                        MeetingDateItem(
                          date: date,
                          children: [
                            for (var i = 0; i < meetingsPerDate.length; i++)
                              MeetingItem(
                                isPast: isPast,
                                actualIndex:
                                    controller.getActualIndex(i, lastIndex),
                                meeting: meetingsPerDate[i],
                                onTap: () async {
                                  if (isPast) {
                                    Get.to(
                                      () => PastMeetingDetailView(
                                        actualIndex: controller.getActualIndex(
                                            i, lastIndex),
                                        meeting: meetingsPerDate[i],
                                      ),
                                    );
                                  } else {
                                    await showModalBottomSheet(
                                      context: context,
                                      isScrollControlled: true,
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.only(
                                          topLeft: const Radius.circular(12).r,
                                          topRight: const Radius.circular(12).r,
                                        ),
                                      ),
                                      builder: (context) {
                                        return _buildDetailBtmSheet(
                                          actualIndex: controller
                                              .getActualIndex(i, lastIndex),
                                          meeting: meetingsPerDate[i],
                                        );
                                      },
                                    );
                                  }
                                },
                                onShareTap: () {
                                  controller.onShareTap(
                                    meetingsPerDate[i],
                                    isPast: isPast,
                                  );
                                },
                                onEditTap: () {
                                  controller
                                      .onEditMeetingTap(meetingsPerDate[i]);
                                },
                              ),
                          ],
                        ),
                        if (index == meetings.length - 1)
                          SizedBox(height: 80.r),
                      ],
                    );
                  },
                ),
              );
  }

  Widget _buildEmptyView(String text) {
    return Center(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Image.asset(
            R.iconMeetingEmpty,
            width: 40.r,
            height: 40.r,
          ),
          SizedBox(height: 8.r),
          Text(
            text,
            style: TextStyle(
              fontSize: 11.sp,
              color: Colors.black,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingView() {
    return Center(
      child: SpinKitDualRing(
        size: 40.r,
        lineWidth: 4,
        color: AppColors.colorFF249ED9,
      ),
    );
  }

  Widget _buildDetailBtmSheet(
      {required int actualIndex, required Meeting meeting}) {
    return Container(
      height: 0.8.sh,
      padding: EdgeInsets.symmetric(horizontal: 26.r, vertical: 30.r),
      child: SingleChildScrollView(
        physics: const BouncingScrollPhysics(),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            /// 头Action按钮：Copy,Edit, Share
            Row(
              children: [
                Container(
                  height: 5.r,
                  width: 55.r,
                  decoration: BoxDecoration(
                    color: controller.getPrimaryColor(actualIndex, false),
                    borderRadius: BorderRadius.only(
                      bottomLeft: Radius.circular(2.r),
                      bottomRight: Radius.circular(2.r),
                    ),
                  ),
                ),
                Spacer(),
                GestureDetector(
                  onTap: () {
                    controller.onCopyMeetingTap(meeting);
                  },
                  child: Image.asset(
                    R.iconMeetingCopy,
                    width: 16.r,
                    height: 16.r,
                  ),
                ),
                SizedBox(width: 18.r),
                if (controller.isHost(meeting.hostId!))
                  GestureDetector(
                    onTap: () {
                      Get.back();
                      controller.onEditMeetingTap(meeting);
                    },
                    child: Image.asset(
                      R.iconMeetingEdit,
                      width: 16.r,
                      height: 16.r,
                    ),
                  ),
                if (controller.isHost(meeting.hostId!)) SizedBox(width: 18.r),
                GestureDetector(
                  onTap: () {
                    controller.onShareTap(meeting);
                  },
                  child: Image.asset(
                    R.iconMeetingShare,
                    width: 16.r,
                    height: 16.r,
                  ),
                ),
              ],
            ),
            SizedBox(height: 15.r),
        
            /// 会议名
            Text(
              meeting.title ?? '',
              maxLines: 1,
              style: TextStyle(
                fontSize: 15.sp,
                fontWeight: FontWeight.w600,
                color: Colors.black,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            SizedBox(height: 8.r),
        
            /// 会议说明
            Container(
              constraints: BoxConstraints(maxHeight: 150.r),
              child: SingleChildScrollView(
                child: Text(
                  meeting.description ?? '',
                  style: TextStyle(
                    fontSize: 13.sp,
                    color: AppColors.colorFF7F7F7F,
                  ),
                ),
              ),
            ),
            SizedBox(height: 18.r),
        
            /// 主持人
            DetailInfoItem(title: L.host.tr, content: meeting.hostName),
            SizedBox(height: 16.r),
        
            /// 日期
            DetailInfoItem(
              title: L.date.tr,
              content: controller.getFormattedDate(meeting.startTime),
            ),
            SizedBox(height: 16.r),
        
            /// 开始时间，结束时间
            Row(
              children: [
                Expanded(
                  flex: 1,
                  child: DetailInfoItem(
                    title: L.start_time.tr,
                    content: controller.getFormattedTime(meeting.startTime),
                  ),
                ),
                Expanded(
                  flex: 1,
                  child: DetailInfoItem(
                    title: L.end_time.tr,
                    content: controller.getFormattedTime(meeting.endTime),
                  ),
                ),
              ],
            ),
            SizedBox(height: 16.r),
        
            /// 密码
            DetailInfoItem(title: L.passcode.tr, content: meeting.passcode),
            SizedBox(height: 16.r),
        
            /// 会议链接
            GestureDetector(
              behavior: HitTestBehavior.opaque,
              onTap: () {
                controller.onCopyMeetingUrlTap(
                    controller.isHost(meeting.hostId ?? '')
                        ? meeting.hostOpenMeeting ?? ''
                        : meeting.meetingLink ?? '');
              },
              child: DetailInfoItem(
                title: controller.isHost(meeting.hostId ?? '')
                    ? L.host_meeting_url.tr
                    : L.meeting.tr,
                content: controller.isHost(meeting.hostId ?? '')
                    ? meeting.hostOpenMeeting
                    : meeting.meetingLink,
                contentSuffix: Image.asset(
                  R.iconMeetingCopy,
                  width: 16.r,
                  height: 16.r,
                ),
              ),
            ),
            if (controller.isHost(meeting.hostId ?? '')) SizedBox(height: 12.r),
            if (controller.isHost(meeting.hostId ?? ''))
              Text(
                L.host_meeting_url_note.tr,
                style: TextStyle(
                  fontSize: 10.sp,
                  color: Colors.red,
                  fontStyle: FontStyle.italic,
                ),
              ),
        
            SizedBox(height: 16.r),
        
            /// 是否任何用户都能打开
            if (meeting.isAnyUserCanOpenMeeting ?? false)
              Text(
                "*** ${L.any_user_can_open_meeting_indicator.tr}",
                style: TextStyle(
                  fontSize: 11.sp,
                  fontStyle: FontStyle.italic,
                  fontWeight: FontWeight.w300,
                  color: AppColors.colorFF7F7F7F,
                ),
              ),
            SizedBox(height: 50.r),
            SizedBox(
              width: 1.sw,
              child: ElevatedButton(
                onPressed: () {
                  controller.onStartMeetingTap(meeting);
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primaryBgColor1,
                  foregroundColor: AppColors.white,
                  elevation: 0,
                  shape: StadiumBorder(),
                  fixedSize: Size(343.w, 44.r),
                  textStyle: TextStyle(
                    fontSize: 15.sp,
                  ),
                ),
                child: Text(
                  controller.isHost(meeting.hostId!)
                      ? L.start_meeting.tr
                      : L.join.tr,
                  style: TextStyle(
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ),
            ),
            SizedBox(height: 20.r),
          ],
        ),
      ),
    );
  }
}
