import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_metatel/app/data/models/meeting.dart';
import 'package:flutter_metatel/app/data/services/config_service.dart';
import 'package:flutter_metatel/core/languages/l.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:get/get.dart';

import '../../../../core/utils/app_log.dart';
import '../../../../core/utils/util.dart';
import '../../../data/providers/api/meeting_api.dart';

class CreateEditMeetingController extends GetxController {
  final formKey = GlobalKey<FormState>();
  RxBool isPasscode = false.obs;
  RxBool isObscure = true.obs;
  RxBool isAnyUserCanOpenMeeting = false.obs;
  TextEditingController titleEditingController = TextEditingController();
  TextEditingController descEditingController = TextEditingController();
  TextEditingController passcodeEditingController = TextEditingController();
  Rxn<DateTime> selectedDate = Rxn<DateTime>();
  Rxn<TimeOfDay> selectedStartTime = Rxn<TimeOfDay>();
  Rxn<TimeOfDay> selectedEndTime = Rxn<TimeOfDay>();
  RxString dateErrorMsg = ''.obs;
  RxString timeErrorMsg = ''.obs;
  RxBool isFormValid = false.obs;
  Rxn<Meeting> meeting = Rxn<Meeting>(); // 只有编辑帖，才会有
  DateTime now = DateTime.now();
  late DateTime validStartDate = DateTime(now.year, now.month, now.day);
  late DateTime validEndDate = validStartDate.add(Duration(days: 9));

  @override
  void onInit() {
    if (Get.arguments != null) {
      meeting.value =
          Get.arguments['meeting'] ?? Rxn<Meeting>().value; // 只有编辑帖，才会有
    }
    if (isEditView()) {
      /// 若为编辑页，导入原数据
      titleEditingController.text = meeting.value?.title ?? '';
      descEditingController.text = meeting.value?.description ?? '';
      if (meeting.value?.passcode != null &&
          meeting.value!.passcode!.isNotEmpty) {
        isPasscode.value = true;
        passcodeEditingController.text = meeting.value!.passcode!;
      }
      isAnyUserCanOpenMeeting.value =
          meeting.value?.isAnyUserCanOpenMeeting ?? false;
      if (meeting.value?.startTime != null) {
        DateTime startDateTime = DateTime.parse(meeting.value!.startTime!);
        selectedDate.value = DateTime(
            startDateTime.year, startDateTime.month, startDateTime.day);
        selectedStartTime.value =
            TimeOfDay(hour: startDateTime.hour, minute: startDateTime.minute);
      }
      if (meeting.value?.endTime != null) {
        DateTime endDateTime = DateTime.parse(meeting.value!.endTime!);
        selectedEndTime.value =
            TimeOfDay(hour: endDateTime.hour, minute: endDateTime.minute);
      }
    } else {
      /// 若为 创建新会议
      titleEditingController.text = L.meeting_created_by.tr.trParams(
        {"host": Get.find<AppConfigService>().getMySelfDisplayName()},
      );
      DateTime today = DateTime.now();
      selectedDate.value = DateTime(today.year, today.month, today.day);
      selectedStartTime.value =
          TimeOfDay(hour: TimeOfDay.now().hour + 1, minute: 0);
      selectedEndTime.value = selectedStartTime.value != null
          ? TimeOfDay(
              hour: selectedStartTime.value!.hour + 1,
              minute: selectedStartTime.value!.minute,
            )
          : TimeOfDay(
              hour: TimeOfDay.now().hour + 2,
              minute: 0,
            );
    }
    super.onInit();
  }

  /// 是否编辑页面
  /// 否 则为创建页面
  bool isEditView() {
    return meeting.value != null;
  }

  void onHandleDate(DateTime? dateTime) {
    if (dateTime == null) return;
    selectedDate.value = dateTime;
    dateErrorMsg.value = '';

    _validateTime();
  }

  void onHandleStartTime(TimeOfDay? timeOfDay) {
    if (timeOfDay == null) return;
    selectedStartTime.value = timeOfDay;

    _validateTime();
  }

  void onHandleEndTime(TimeOfDay? timeOfDay) {
    if (timeOfDay == null) return;
    selectedEndTime.value = timeOfDay;

    _validateTime();
  }

  void onSubmitTap() async {
    if (!_formValidation()) return;
    try {
      EasyLoading.show(maskType: EasyLoadingMaskType.black);
      bool? isSuccess = await MeetingApi.createMeeting(
        meeting: Meeting(
          title: titleEditingController.text,
          description: descEditingController.text,
          startTime: getDateTimeFromTimeOfDay(
              selectedDate.value!, selectedStartTime.value!),
          endTime: getDateTimeFromTimeOfDay(
              selectedDate.value!, selectedEndTime.value!),
          passcode: passcodeEditingController.text,
          isAnyUserCanOpenMeeting: isAnyUserCanOpenMeeting.value,
        ),
      );
      if (!(isSuccess ?? false)) {
        EasyLoading.showError(L.failed.tr);
        return;
      }
      EasyLoading.showSuccess(L.success.tr);
      Get.back(result: true);
    } catch (e) {
      EasyLoading.showError(L.failed.tr);
      AppLogger.e("Error Catch<onSubmitTap>: $e");
    }
  }

  void onUpdateTap() async {
    if (!_formValidation()) return;
    try {
      EasyLoading.show(maskType: EasyLoadingMaskType.black);
      bool? isSuccess = await MeetingApi.updateMeeting(
        meeting: Meeting(
          id: meeting.value?.id,
          title: titleEditingController.text,
          description: descEditingController.text,
          startTime: getDateTimeFromTimeOfDay(
              selectedDate.value!, selectedStartTime.value!),
          endTime: getDateTimeFromTimeOfDay(
              selectedDate.value!, selectedEndTime.value!),
          passcode: passcodeEditingController.text,
          isAnyUserCanOpenMeeting: isAnyUserCanOpenMeeting.value,
        ),
      );
      if (!(isSuccess ?? false)) {
        EasyLoading.showError(L.failed.tr);
        return;
      }
      EasyLoading.showSuccess(L.success.tr);
      Get.back(result: true);
    } catch (e) {
      EasyLoading.showError(L.failed.tr);
      AppLogger.e("Error Catch<onUpdateTap>: $e");
    }
  }

  void onCancelTap() async {
    try {
      bool? isConfirm = await showConfirmationDialog(
          L.cancel_meeting_confirmation.tr, L.confirm.tr);
      if (!(isConfirm ?? false)) return;
      EasyLoading.show(maskType: EasyLoadingMaskType.black);
      bool? isSuccess = await MeetingApi.cancelMeeting(
        meetingId: meeting.value?.id,
      );
      if (!(isSuccess ?? false)) {
        EasyLoading.showError(L.failed.tr);
        return;
      }
      EasyLoading.showSuccess(L.success.tr);
      Get.back(result: true);
    } catch (e) {
      EasyLoading.showError(L.failed.tr);
      AppLogger.e("Error Catch<onCancelTap>: $e");
    }
  }

  void onGeneratePasscodeTap() {
    String randomPasscode = getRandomString(8);
    passcodeEditingController.text = randomPasscode;
    isObscure.value = false;
  }

  String getRandomString(int length) {
    const _chars =
        'AaBbCcDdEeFfGgHhIiJjKkLlMmNnOoPpQqRrSsTtUuVvWwXxYyZz1234567890';
    Random _rnd = Random();
    return String.fromCharCodes(
      Iterable.generate(
        length,
        (_) => _chars.codeUnitAt(_rnd.nextInt(_chars.length)),
      ),
    );
  }

  Future<bool?> showConfirmationDialog(
      String dialogDesc, String positiveBtnText) async {
    return await SmartDialog.show(builder: (context) {
      return AlertDialog(
        content: Text(dialogDesc),
        actions: [
          TextButton(
            child: Text(L.cancel.tr),
            onPressed: () => SmartDialog.dismiss(), //关闭对话框,
          ),
          TextButton(
            child: Text(positiveBtnText),
            onPressed: () async {
              SmartDialog.dismiss(result: true);
            }, //关闭对话框,
          ),
        ],
        actionsAlignment: MainAxisAlignment.end,
      );
    });
  }

  bool _formValidation() {
    bool isValid = true;

    /// 验证 title, passcode(if checked)
    if (!formKey.currentState!.validate()) {
      isValid = false;
    }

    /// 验证日期
    if (!_validateDate()) {
      isValid = false;
    }

    /// 验证时间
    if (!_validateTime()) {
      isValid = false;
    }

    return isValid;
  }

  bool _validateDate() {
    if (selectedDate.value == null) {
      dateErrorMsg.value = L.meeting_date_required.tr;
      return false;
    }
    if (!isValidDateRange(selectedDate.value!)) {
      dateErrorMsg.value = L.meeting_date_invalid.tr;
      return false;
    }
    return true;
  }

  bool isValidDateRange(DateTime date) {
    /// 不在时间范围内
    if (date.isBefore(validStartDate) || date.isAfter(validEndDate)) {
      return false;
    }
    return true;
  }

  bool _validateTime() {
    bool isStartTimeValid = _validateStartTime();
    if (!isStartTimeValid) return false;
    bool isEndTimeValid = _validateEndTime();
    if (!isEndTimeValid) return false;
    timeErrorMsg.value = '';
    return true;
  }

  bool _validateStartTime() {
    if (selectedStartTime.value == null) {
      timeErrorMsg.value = L.meeting_start_time_required.tr;
      return false;
    }

    /// 当选择的日期是今天，且选择的开始时间 小过或等于 现在的时间
    if (!_validateDateStartTime()) {
      return false;
    }

    /// 当选择的开始时间 大过或等于 结束时间
    if (selectedStartTime.value != null && selectedEndTime.value != null) {
      if (toDouble(selectedStartTime.value!) >=
          toDouble(selectedEndTime.value!)) {
        timeErrorMsg.value = L.meeting_time_invalid.tr;
        return false;
      }
    }
    return true;
  }

  bool _validateEndTime() {
    if (selectedEndTime.value == null) {
      timeErrorMsg.value = L.meeting_end_time_required.tr;
      return false;
    }

    /// 当选择的结束时间 小过或等于 开始时间
    if (selectedStartTime.value != null && selectedEndTime.value != null) {
      if (toDouble(selectedEndTime.value!) <=
          toDouble(selectedStartTime.value!)) {
        timeErrorMsg.value = L.meeting_time_invalid.tr;
        return false;
      }
    }
    return true;
  }

  bool _validateDateStartTime() {
    /// 当选择的日期是今天，且选择的开始时间 小过现在的时间10分钟
    var now = DateTime.now();
    if (selectedDate.value != null && selectedStartTime.value != null) {
      if (selectedDate.value == DateTime(now.year, now.month, now.day) &&
          toDouble(selectedStartTime.value!) <=
              (toDouble(TimeOfDay.now()) - 10 / 60)) {
        timeErrorMsg.value = L.start_time_less_than_10_err_msg.tr;
        return false;
      }
    }
    return true;
  }

  String? titleValidator(String? value) {
    if (value == null || value.isEmpty) {
      return L.meeting_title_empty.tr;
    }
    return null;
  }

  String? passcodeValidator(String? value) {
    if (value == null || value.isEmpty) {
      return L.please_enter_passcode.tr;
    }
    if (value.length != 8) {
      return L.meeting_passcode_limit.trParams({"limit": "8"});
    }
    return null;
  }

  double toDouble(TimeOfDay myTime) => myTime.hour + myTime.minute / 60.0;
}
