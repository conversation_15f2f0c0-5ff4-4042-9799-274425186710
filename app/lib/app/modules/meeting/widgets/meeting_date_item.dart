import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/modules/meeting/widgets/meeting_item.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../core/values/colors.dart';

class MeetingDateItem extends StatelessWidget {
  const MeetingDateItem(
      {super.key, required this.children, required this.date});
  final String date;
  final List<MeetingItem> children;
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(
        horizontal: 18.r,
        vertical: 10.r,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            date,
            style: TextStyle(
              fontSize: 14.sp,
              color: AppColors.colorFF656565,
            ),
          ),
          SizedBox(height: 6.r),
          for (var child in children)
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 15.r, vertical: 10.r),
              child: child,
            ),
        ],
      ),
    );
  }
}
