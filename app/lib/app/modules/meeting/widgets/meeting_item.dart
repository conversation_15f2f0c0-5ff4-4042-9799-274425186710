import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/modules/meeting/meeting_controller.dart';
import 'package:flutter_metatel/core/languages/l.dart';
import 'package:flutter_metatel/core/values/colors.dart';
import 'package:flutter_metatel/r.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../data/models/meeting.dart';

class MeetingItem extends StatelessWidget {
  const MeetingItem({
    super.key,
    required this.actualIndex,
    required this.meeting,
    this.isPast = false,
    this.onTap,
    this.onShareTap,
    this.onEditTap,
  });

  final int actualIndex;
  final bool isPast;
  final Meeting meeting;
  final Function()? onTap;
  final Function()? onShareTap;
  final Function()? onEditTap;
  final double containerHeight = 120;

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<MeetingController>();
    return GestureDetector(
      onTap: onTap,
      child: Stack(
        children: [
          Container(
            height: containerHeight.r,
            padding: EdgeInsets.symmetric(horizontal: 30.r),
            decoration: BoxDecoration(
              color: controller.getSecondaryColor(actualIndex, isPast),
              borderRadius: BorderRadius.only(
                topRight: Radius.circular(5.r),
                bottomRight: Radius.circular(5.r),
              ),
            ),
            child: Center(
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  /// 详情内容
                  Expanded(
                    flex: 4,
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          meeting.title ?? '',
                          maxLines: 1,
                          style: TextStyle(
                            fontSize: 15.sp,
                            color: Colors.black,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        SizedBox(height: 4.r),
                        Text(
                          "${controller.getFormattedTime(meeting.startTime)} - ${controller.getFormattedTime(meeting.endTime)}",
                          style: TextStyle(
                            fontSize: 11.sp,
                            color: Colors.black,
                            fontWeight: FontWeight.w300,
                          ),
                        ),
                        SizedBox(height: 6.r),
                        Text(
                          meeting.description ?? '',
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                          style: TextStyle(
                            fontSize: 10.sp,
                            color: AppColors.colorFF7F7F7F,
                            fontWeight: FontWeight.w300,
                          ),
                        ),
                        SizedBox(height: 6.r),
                        RichText(
                          text: TextSpan(
                            text: '${L.host.tr}: ',
                            style: TextStyle(
                              fontSize: 10.sp,
                              color: Colors.black,
                              fontWeight: FontWeight.w300,
                            ),
                            children: [
                              TextSpan(
                                text: controller.isHost(meeting.hostId!)
                                    ? L.meeting_host_owner.tr
                                    : meeting.hostName,
                                style: TextStyle(
                                  color: AppColors.colorFF7F7F7F,
                                ),
                              ),
                            ],
                          ),
                        )
                      ],
                    ),
                  ),

                  /// 按钮
                  Expanded(
                    flex: 1,
                    child: isPast
                        ? const SizedBox.shrink()
                        : Column(
                            mainAxisSize: MainAxisSize.min,
                            crossAxisAlignment: CrossAxisAlignment.end,
                            children: [
                              isPast || !controller.isHost(meeting.hostId!)
                                  ? _buildActionBtn(
                                      image: R.iconMeetingDetail,
                                      color: controller.getPrimaryColor(
                                          actualIndex, isPast),
                                      onTap: onTap,
                                    )
                                  : _buildActionBtn(
                                      image: R.iconMeetingEdit,
                                      color: controller.getPrimaryColor(
                                          actualIndex, isPast),
                                      onTap: onEditTap,
                                    ),
                              SizedBox(height: 10.r),
                              _buildActionBtn(
                                image: R.iconMeetingShare,
                                color: controller.getPrimaryColor(
                                    actualIndex, isPast),
                                onTap: onShareTap,
                              ),
                            ],
                          ),
                  ),
                ],
              ),
            ),
          ),

          /// 颜色条
          Container(
            width: 5.r,
            height: containerHeight.r,
            color: controller.getPrimaryColor(actualIndex, isPast),
          ),
        ],
      ),
    );
  }

  Widget _buildActionBtn(
      {required String image, required Color color, Function()? onTap}) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 30.r,
        height: 30.r,
        decoration: BoxDecoration(
          color: AppColors.white,
          shape: BoxShape.circle,
        ),
        child: Center(
          child: Image.asset(
            image,
            color: color,
            width: 15.r,
            height: 15.r,
          ),
        ),
      ),
    );
  }
}
