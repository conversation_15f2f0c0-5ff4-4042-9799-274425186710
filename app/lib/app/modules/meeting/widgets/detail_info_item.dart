import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../core/values/colors.dart';

class DetailInfoItem extends StatelessWidget {
  const DetailInfoItem(
      {super.key,
      required this.title,
      this.content,
      this.contentSuffix,
      this.customContent});
  final String title;
  final String? content;
  final Widget? customContent;
  final Widget? contentSuffix;
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          title,
          style: TextStyle(fontSize: 15.sp, color: Colors.black),
        ),
        SizedBox(height: 4.r),
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (customContent != null) customContent!,
            if (content != null)
              Expanded(
                child: Text(
                  content!,
                  style: TextStyle(
                    fontSize: 13.sp,
                    color: AppColors.colorFF7F7F7F,
                  ),
                ),
              ),
            if (contentSuffix != null) ...[
              SizedBox(width: 16.r),
              contentSuffix!,
            ],
          ],
        ),
      ],
    );
  }
}
