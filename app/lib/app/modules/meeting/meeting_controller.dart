import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/data/providers/api/meeting_api.dart';
import 'package:flutter_metatel/core/languages/l.dart';
import 'package:flutter_metatel/core/utils/dio/dio_util.dart';
import 'package:flutter_metatel/core/values/config.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import '../../../core/task/time_task.dart';
import '../../../core/utils/app_log.dart';
import '../../../core/utils/util.dart';
import '../../../core/values/colors.dart';
import '../../../meeting/meeting_helper.dart';
import '../../../routes/pages.dart';
import '../../data/enums/enum.dart';
import '../../data/models/meeting.dart';
import '../../data/services/chatio_service.dart';
import '../../data/services/config_service.dart';
import '../../data/services/event_service.dart';

class MeetingController extends GetxController
    with GetTickerProviderStateMixin {
  RefreshController upcomingRefreshController =
      RefreshController(initialRefresh: false);
  RefreshController pastRefreshController =
      RefreshController(initialRefresh: false);
  late TabController tabController;
  RxInt _currentTabIndex = 0.obs;
  List<String> tabbarTitles = [L.upcomings.tr, L.past.tr];
  RxBool isUpcomingLoading = false.obs;
  RxBool isPastLoading = false.obs;
  RxMap<String, List<Meeting>> upcomingMeeting = <String, List<Meeting>>{}.obs;
  RxMap<String, List<Meeting>> pastMeetings = <String, List<Meeting>>{}.obs;
  // Map<String, List<Meeting>> upcomingMeeting = {
  //   "Today, 28 Nov": [
  //     Meeting(
  //       id: "e94be4f2ab5548f0ac97cdf9faa050b2",
  //       title: "Meeting Title 1",
  //       startTime: "2024-12-18 10:50:00",
  //       endTime: "2024-12-18 11:50:00",
  //       description:
  //           "Description Lorem ipsum dolor sit amet, consectetur adipiscing elit sajhdk aksdhkasjk kashdk ak akjsdhkj asdkj Lorem ipsum dolor sit amet, consectetur adipiscing elit. Duis facilisis at odio ac vestibulum. Integer malesuada mauris nec efficitur facilisis. Ut feugiat, turpis eu accumsan congue, neque sapien interdum erat, sit amet tincidunt urna dolor quis dui. Maecenas in arcu nulla. Nullam feugiat felis ut urna ultrices egestas non ut leo. Interdum et malesuada fames ac ante ipsum primis in faucibus. Donec nec urna velit. Aliquam aliquam tincidunt massa in consequat. Vivamus vel ex eget lectus lobortis blandit sit amet in lacus. Nulla facilisi. Nam interdum, arcu ut lobortis congue, mauris est maximus tortor, ut tempus dui odio ac augue. Sed at ullamcorper nisi. Quisque auctor maximus augue in pulvinar. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Quisque consectetur sem a felis iaculis convallis. Quisque ac nisl ut mi cursus lacinia ut nec neque. Donec augue lectus, aliquet a luctus nec, dictum non ante. Cras mattis mauris purus, ut malesuada tellus fringilla id. Proin sit amet gravida quam. Nam porta finibus nulla, at aliquet elit vestibulum quis. Cras nec volutpat nunc. Pellentesque vehicula ex sapien, eget sollicitudin nunc consequat consequat. In efficitur sodales velit, in vestibulum nibh semper eu. Aenean pretium, tellus eu semper semper, purus tortor faucibus augue, vitae tincidunt ex orci sed urna.",
  //       passcode: "123458",
  //       hostId: "GiYJhjrMd34cLxAGuoFfYQHYE2rzb9fXui6GCiuWY4Nk",
  //       hostName: "Test 66",
  //       meetingLink: "abc.test.com/join",
  //     ),
  //     Meeting(
  //       id: "b2b02322cfa53876bb8e2da40e08276979d52814-1733985909546",
  //       title: "Meeting Title 2",
  //       startTime: "2024-12-21 11:50:00",
  //       endTime: "2024-12-21 13:50:00",
  //       description: "Description Lorem ipsum dolor",
  //       passcode: "",
  //       hostId: "GiYJhjrMd34cLxAGuoFfYQHYE2rzb9fXui6GCiuWY4Nk",
  //       hostName: "Test 66",
  //       meetingLink: "abc.test.com/join",
  //     ),
  //   ],
  //   "Monday, 29 Nov": [
  //     Meeting(
  //       id: "1",
  //       title: "Meeting Title 3",
  //       startTime: "2024-12-18 10:50:00",
  //       endTime: "2024-12-18 11:50:00",
  //       description:
  //           "Description Lorem ipsum dolor sit amet, consectetur adipiscing elit sajhdk aksdhkasjk kashdk ak akjsdhkj asdkj ",
  //       hostId: "GiYJhjrMd34cLxAGuoFfYQHYE2rzb9fXui6GCiuWY4Nk",
  //       hostName: "Test 66",
  //       meetingLink: "abc.test.com/join",
  //     ),
  //     Meeting(
  //       id: "2",
  //       title: "Meeting Title 4",
  //       startTime: "2024-12-18 10:50:00",
  //       endTime: "2024-12-18 11:50:00",
  //       description: "Description Lorem ipsum dolor",
  //       passcode: "123458",
  //       hostId: "12345",
  //       hostName: "Terrence",
  //       meetingLink: "abc.test.com/join",
  //     ),
  //   ],
  //   "Tuesday, 30 Nov": [
  //     Meeting(
  //       id: "1",
  //       title: "Meeting Title 5",
  //       startTime: "2024-12-18 10:50:00",
  //       endTime: "2024-12-18 11:50:00",
  //       description:
  //           "Description Lorem ipsum dolor sit amet, consectetur adipiscing elit sajhdk aksdhkasjk kashdk ak akjsdhkj asdkj ",
  //       passcode: "123458",
  //       hostId: "12345",
  //       hostName: "Terrence",
  //       meetingLink: "abc.test.com/join",
  //     ),
  //     Meeting(
  //       id: "2",
  //       title: "Meeting Title 6",
  //       startTime: "2024-12-18 10:50:00",
  //       endTime: "2024-12-18 11:50:00",
  //       description: "Description Lorem ipsum dolor",
  //       passcode: "123458",
  //       hostId: "12345",
  //       hostName: "Terrence",
  //       meetingLink: "abc.test.com/join",
  //     ),
  //   ],
  // };

  // Map<String, List<Meeting>> pastMeetings = {
  //   "Today, 28 Nov": [
  //     Meeting(
  //       id: "1",
  //       title: "Meeting Title 1",
  //       startTime: "2024-12-18 10:50:00",
  //       endTime: "2024-12-18 11:50:00",
  //       description:
  //           "Description Lorem ipsum dolor sit amet, consectetur adipiscing elit sajhdk aksdhkasjk kashdk ak akjsdhkj asdkj Lorem ipsum dolor sit amet, consectetur adipiscing elit. Duis facilisis at odio ac vestibulum. Integer malesuada mauris nec efficitur facilisis. Ut feugiat, turpis eu accumsan congue, neque sapien interdum erat, sit amet tincidunt urna dolor quis dui. Maecenas in arcu nulla. Nullam feugiat felis ut urna ultrices egestas non ut leo. Interdum et malesuada fames ac ante ipsum primis in faucibus. Donec nec urna velit. Aliquam aliquam tincidunt massa in consequat. Vivamus vel ex eget lectus lobortis blandit sit amet in lacus. Nulla facilisi. Nam interdum, arcu ut lobortis congue, mauris est maximus tortor, ut tempus dui odio ac augue. Sed at ullamcorper nisi. Quisque auctor maximus augue in pulvinar. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Quisque consectetur sem a felis iaculis convallis. Quisque ac nisl ut mi cursus lacinia ut nec neque. Donec augue lectus, aliquet a luctus nec, dictum non ante. Cras mattis mauris purus, ut malesuada tellus fringilla id. Proin sit amet gravida quam. Nam porta finibus nulla, at aliquet elit vestibulum quis. Cras nec volutpat nunc. Pellentesque vehicula ex sapien, eget sollicitudin nunc consequat consequat. In efficitur sodales velit, in vestibulum nibh semper eu. Aenean pretium, tellus eu semper semper, purus tortor faucibus augue, vitae tincidunt ex orci sed urna.",
  //       passcode: "123458",
  //       hostId: "12345",
  //       hostName: "Terrence",
  //       meetingLink: "abc.test.com/join",
  //     ),
  //     Meeting(
  //       id: "2",
  //       title: "Meeting Title 2",
  //       startTime: "2024-12-18 10:50:00",
  //       endTime: "2024-12-18 11:50:00",
  //       description: "Description Lorem ipsum dolor",
  //       passcode: "123458",
  //       hostId: "GiYJhjrMd34cLxAGuoFfYQHYE2rzb9fXui6GCiuWY4Nk",
  //       hostName: "Test 66",
  //       meetingLink: "abc.test.com/join",
  //     ),
  //   ],
  //   "Monday, 29 Nov": [
  //     Meeting(
  //       id: "1",
  //       title: "Meeting Title 3",
  //       startTime: "2024-12-18 08:50:00",
  //       endTime: "2024-12-18 11:50:00",
  //       description:
  //           "Description Lorem ipsum dolor sit amet, consectetur adipiscing elit sajhdk aksdhkasjk kashdk ak akjsdhkj asdkj ",
  //       passcode: "123458",
  //       hostId: "12345",
  //       hostName: "Terrence",
  //       meetingLink: "abc.test.com/join",
  //     ),
  //     Meeting(
  //       id: "2",
  //       title: "Meeting Title 4",
  //       startTime: "2024-12-18 10:50:00",
  //       endTime: "2024-12-18 11:50:00",
  //       description: "Description Lorem ipsum dolor",
  //       passcode: "123458",
  //       hostId: "12345",
  //       hostName: "Terrence",
  //       meetingLink: "abc.test.com/join",
  //     ),
  //   ],
  //   "Tuesday, 30 Nov": [
  //     Meeting(
  //       id: "1",
  //       title: "Meeting Title 5",
  //       startTime: "2024-12-18 10:50:00",
  //       endTime: "2024-12-18 11:50:00",
  //       description:
  //           "Description Lorem ipsum dolor sit amet, consectetur adipiscing elit sajhdk aksdhkasjk kashdk ak akjsdhkj asdkj ",
  //       passcode: "123458",
  //       hostId: "12345",
  //       hostName: "Terrence",
  //       meetingLink: "abc.test.com/join",
  //     ),
  //     Meeting(
  //       id: "2",
  //       title: "Meeting Title 6",
  //       startTime: "2024-12-18 10:50:00",
  //       endTime: "2024-12-18 11:50:00",
  //       description: "Description Lorem ipsum dolor",
  //       passcode: "123458",
  //       hostId: "12345",
  //       hostName: "Terrence",
  //       meetingLink: "abc.test.com/join",
  //     ),
  //   ],
  // };

  @override
  void onInit() {
    /// 设置Dio的baseUrl为会议的baseUrl
    // DioUtil().setBaseUrl(Config.meetingApiBaseUrl());
    _initTabController();
    getMeetings();
    super.onInit();
  }

  @override
  void dispose() {
    tabController.dispose();
    super.dispose();
  }

  void onRefreshUpcoming() async {
    var result = await MeetingApi.getUpcomingMeetings();
    upcomingMeeting.value = _mapMeetingsByDate(result);
    upcomingRefreshController.refreshCompleted();
  }

  void onRefreshPast() async {
    var result = await MeetingApi.getPastMeetings();
    pastMeetings.value = _mapMeetingsByDate(result);
    pastRefreshController.refreshCompleted();
  }

  Future<void> getMeetings() async {
    isUpcomingLoading.value = true;
    isPastLoading.value = true;
    var result = await Future.wait([
      MeetingApi.getUpcomingMeetings(),
      MeetingApi.getPastMeetings(),
    ]);
    isUpcomingLoading.value = false;
    isPastLoading.value = false;
    upcomingMeeting.value = _mapMeetingsByDate(result[0]);
    pastMeetings.value = _mapMeetingsByDate(result[1]);
  }

  void _initTabController() {
    tabController = TabController(
      initialIndex: _currentTabIndex.value,
      length: tabbarTitles.length,
      vsync: this,
    );
    tabController.addListener(() {
      _currentTabIndex.value = tabController.index;
    });
  }

  void onCreateMeetingTap() async {
    bool? result = await goCreateEditMeetingView();
    if (!(result ?? false)) return;
    // 创建成功，更新页面
    List<Meeting>? meetings = await MeetingApi.getUpcomingMeetings();
    upcomingMeeting.value = _mapMeetingsByDate(meetings);
  }

  void onEditMeetingTap(Meeting meeting) async {
    bool? result = await goCreateEditMeetingView(meeting: meeting);
    if (!(result ?? false)) return;
    // 编辑/取消成功，更新页面
    List<Meeting>? meetings = await MeetingApi.getUpcomingMeetings();
    upcomingMeeting.value = _mapMeetingsByDate(meetings);
  }

  Future<bool?> goCreateEditMeetingView({Meeting? meeting}) async {
    final result = await Get.toNamed(
      Routes.CreateEditMeetingView,
      arguments: {
        "meeting": meeting, // 只有编辑，才会有
      },
    );
    return result != null ? result as bool : null;
  }

  void onShareTap(Meeting meeting, {bool isPast = false}) async {
    // String textToShare = getShareMsg(meeting, isPast: isPast);
    // Share.share(textToShare);

    var messageEvent = MessageEvent(
      uuid(),
      owner: '',
      type: MessageType.meeting,
      chatType: ChatType.singleChat,
      body: jsonEncode(meeting.toJson()),
      direction: Direction.outGoing,
      dateTime: TimeTask.instance.getNowDateTime(),
    );

    shareForwardMessage(Get.context!, [messageEvent]);
  }

  void onCopyMeetingTap(Meeting meeting, {bool isPast = false}) {
    String textToShare = getFormattedMeetingMsg(meeting, isPast: isPast);
    saveClipboard(textToShare);
    toast(L.chat_has_copy_to_shear_plate.tr);
  }

  void onCopyMeetingUrlTap(String url) {
    saveClipboard(url);
    toast(L.chat_has_copy_to_shear_plate.tr);
  }

  void onStartMeetingTap(Meeting meeting) {
    MeetingHelper().startMeeting(
      meeting.hostOpenMeeting ?? '',
      toExecute: () {
        Get.back();
      },
    );
  }

  bool isHost(String hostId) {
    /// 该会议主持人是否为自己
    return hostId == Get.find<AppConfigService>().getUserNameWithoutDomain();
  }

  int getLastIndex(int index, Map<String, List<Meeting>> meetings) {
    /// index: MeetingDateItem的Index
    /// lastIndex: 前一個MeetingDateItem內的最后一个MeetingItem Index
    if (index == 0) {
      return -1;
    }
    int totalPreviousMeetingCount = 0;
    for (var i = 0; i < index; i++) {
      int meetingCount = meetings.values.elementAt(i).length;
      totalPreviousMeetingCount += meetingCount;
    }
    return totalPreviousMeetingCount - 1;
  }

  int getActualIndex(int subIndex, int lastIndex) {
    /// subIndex: MeetingDateItem內的MeetingItem Index
    /// lastIndex: 前一個MeetingDateItem內的最后一个MeetingItem Index
    return lastIndex + subIndex + 1;
  }

  Color getPrimaryColor(int index, bool isPast) {
    if (isPast) {
      return index % 4 == 0
          ? AppColors.colorPastMeetingThemeBlue
          : index % 4 == 1
              ? AppColors.colorPastMeetingThemeRed
              : index % 4 == 2
                  ? AppColors.colorPastMeetingThemeOrange
                  : AppColors.colorPastMeetingThemeGreen;
    }
    return index % 4 == 0
        ? AppColors.colorMeetingThemeBlue
        : index % 4 == 1
            ? AppColors.colorMeetingThemeRed
            : index % 4 == 2
                ? AppColors.colorMeetingThemeOrange
                : AppColors.colorMeetingThemeGreen;
  }

  Color getSecondaryColor(int index, bool isPast) {
    if (isPast) {
      return index % 4 == 0
          ? AppColors.colorPastMeetingThemeBlueLight
          : index % 4 == 1
              ? AppColors.colorPastMeetingThemeRedLight
              : index % 4 == 2
                  ? AppColors.colorPastMeetingThemeOrangeLight
                  : AppColors.colorPastMeetingThemeGreenLight;
    }
    return index % 4 == 0
        ? AppColors.colorMeetingThemeBlueLight
        : index % 4 == 1
            ? AppColors.colorMeetingThemeRedLight
            : index % 4 == 2
                ? AppColors.colorMeetingThemeOrangeLight
                : AppColors.colorMeetingThemeGreenLight;
  }

  Map<String, List<Meeting>> _mapMeetingsByDate(List<Meeting>? meetings) {
    /// Date as key, Date Format: Today, 28 Nov

    if (meetings == null || meetings.isEmpty) {
      return {};
    }
    try {
      final organizedMeetings = meetings.fold(
        Map<String, List<Meeting>>(),
        (previousValue, element) => previousValue
          ..update(
            _getDateFromDateTime(element.startTime!),
            (value) => value..add(element),
            ifAbsent: () => [element],
          ),
      );
      return organizedMeetings;
    } catch (e) {
      AppLogger.e("Error Catch<_mapMeetingsByDate>: $e");
      return {};
    }
  }

  /// 会议列表的日期格式
  String _getDateFromDateTime(String dateTime) {
    // date.difference(DateTime.now()).inDays==0
    DateTime dt = DateTime.parse(dateTime);
    DateTime d = DateTime(dt.year, dt.month, dt.day);
    DateTime now = DateTime.now();
    DateTime today = DateTime(now.year, now.month, now.day);
    int dayDifference = d.difference(today).inDays; // 0: 今天，-1：昨天，1 明天

    /// Format 月和天
    String format = '';
    if (Get.locale?.languageCode != null && Get.locale?.languageCode == 'zh') {
      format = ', MMM d'; // 中文布局
    } else {
      format = ', d MMM';
    }
    String formattedDate =
        DateFormat(format, Get.locale?.languageCode).format(d);

    /// 若为中文 在天后面添加“日”
    if (Get.locale?.languageCode != null && Get.locale?.languageCode == 'zh') {
      formattedDate = formattedDate + "日";
    }

    /// Format前缀
    /// 若为昨天，今天，明天
    /// 其他为day in week
    switch (dayDifference) {
      case 0:
        formattedDate = L.today_1_cap.tr + formattedDate;
        break;
      case -1:
        formattedDate = L.yesterday_1_cap.tr + formattedDate;
        break;
      case 1:
        formattedDate = L.tomorrow_1_cap.tr + formattedDate;
        break;
      default:
        formattedDate =
            DateFormat("E", Get.locale?.languageCode).format(d) + formattedDate;
        break;
    }
    return formattedDate;
  }

  /// 会议详情页面内的日期显示
  String getFormattedDate(String? dateTime) {
    if (dateTime == null) {
      return '';
    }
    return formatDateTime(
      DateFormat("EEEE, dd MMMM yyyy", Get.locale?.languageCode),
      dateTime,
    );
  }

  /// 会议widget和会议详情内的时间显示
  String getFormattedTime(String? dateTime) {
    if (dateTime == null) {
      return '';
    }
    return formatDateTime(
      DateFormat("hh:mm a", Get.locale?.languageCode),
      dateTime,
    );
  }

  /// 旧会议分享，信息格式
  //     String textToShare =
  //     "test 2邀请您参加即将举行的线上会议!

  // 🖥 主题：test 2的房间
  // 📅 日期：2024-12-13 10:00:23
  // 🕒 时间: xx:xx-xx:xx
  // 🔗 会议链接：https://meeting.linksaychat.com:443/rooms/0366fe6a-b8f6-11ef-9213-00505653f01d/join
  // 🔑 與會者密碼：JHazVF

  // 议程安排：
  // XXXXXXXXXXXXXXXXXXXX";

  // "Test 33 invites you to join the upcoming online conference!

  // 🖥 Subject: Test 33的房间
  // 📅 Date: 2024-12-13 10:34:08
  // 🕒 Time: xx:xx-xx:xx
  // 🔗 URL: https://meeting.linksaychat.com:443/rooms/ba674e5e-b8fa-11ef-9e03-00505653f01d/join
  // 🔑 Moderator's Code：qaFUTC

  // Agenda:
  // XXXXXXXXXXXXXXXXXXXX"
}
