import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/modules/meeting/meeting_controller.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../core/languages/l.dart';
import '../../../core/values/colors.dart';
import '../../../r.dart';
import '../../data/models/meeting.dart';
import 'widgets/detail_info_item.dart';

class PastMeetingDetailView extends StatelessWidget {
  const PastMeetingDetailView({
    super.key,
    required this.actualIndex,
    required this.meeting,
  });
  final int actualIndex;
  final Meeting meeting;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.white,
      appBar: AppBar(
        title: Text(L.meeting.tr),
        centerTitle: true,
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    final controller = Get.find<MeetingController>();
    return SingleChildScrollView(
      physics: BouncingScrollPhysics(),
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 26.r, vertical: 30.r),
        height: 1.sh - kToolbarHeight,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            /// 头Action按钮：Copy, Share
            Row(
              children: [
                Container(
                  height: 5.r,
                  width: 55.r,
                  decoration: BoxDecoration(
                    color: controller.getPrimaryColor(actualIndex, true),
                    borderRadius: BorderRadius.only(
                      bottomLeft: Radius.circular(2.r),
                      bottomRight: Radius.circular(2.r),
                    ),
                  ),
                ),
                Spacer(),
                // GestureDetector(
                //   onTap: () {
                //     controller.onCopyMeetingTap(meeting, isPast: true);
                //   },
                //   child: Image.asset(
                //     R.iconMeetingCopy,
                //     width: 16.r,
                //     height: 16.r,
                //   ),
                // ),
                // SizedBox(width: 15.r),
                // GestureDetector(
                //   onTap: () {
                //     controller.onShareTap(meeting, isPast: true);
                //   },
                //   child: Image.asset(
                //     R.iconMeetingShare,
                //     width: 16.r,
                //     height: 16.r,
                //   ),
                // ),
              ],
            ),
            SizedBox(height: 15.r),

            /// 会议名
            Text(
              meeting.title ?? '',
              maxLines: 1,
              style: TextStyle(
                overflow: TextOverflow.ellipsis,
                fontSize: 15.sp,
                fontWeight: FontWeight.w600,
                color: Colors.black,
              ),
            ),
            SizedBox(height: 8.r),

            /// 会议详情
            Container(
              constraints: BoxConstraints(maxHeight: 200.r),
              child: SingleChildScrollView(
                physics: BouncingScrollPhysics(),
                child: Text(
                  meeting.description ?? '',
                  style: TextStyle(
                    fontSize: 13.sp,
                    color: AppColors.colorFF7F7F7F,
                  ),
                ),
              ),
            ),
            SizedBox(height: 18.r),

            /// 主持人
            DetailInfoItem(title: L.host.tr, content: meeting.hostName),
            SizedBox(height: 16.r),

            /// 日期
            DetailInfoItem(
              title: L.date.tr,
              content: controller.getFormattedDate(meeting.startTime),
            ),
            SizedBox(height: 16.r),

            /// 开始时间，结束时间
            Row(
              children: [
                Expanded(
                  flex: 1,
                  child: DetailInfoItem(
                    title: L.start_time.tr,
                    content: controller.getFormattedTime(meeting.startTime),
                  ),
                ),
                Expanded(
                  flex: 1,
                  child: DetailInfoItem(
                    title: L.end_time.tr,
                    content: controller.getFormattedTime(meeting.endTime),
                  ),
                ),
              ],
            ),
            SizedBox(height: 16.r),

            /// 密码
            DetailInfoItem(title: L.passcode.tr, content: meeting.passcode),
            SizedBox(height: 16.r),

            /// 会议链接
            DetailInfoItem(
              title: L.meeting.tr,
              content: meeting.meetingLink,
            ),
            // SizedBox(height: 40.r),
            // Container(
            //   height: 5.r,
            //   width: 55.r,
            //   decoration: BoxDecoration(
            //     color: controller.getPrimaryColor(actualIndex, true),
            //     borderRadius: BorderRadius.only(
            //       bottomLeft: Radius.circular(2.r),
            //       bottomRight: Radius.circular(2.r),
            //     ),
            //   ),
            // ),
            // SizedBox(height: 20.r),

            /// 录制的会议
            // DetailInfoItem(
            //   title: L.recorded_meeting.tr,
            //   customContent: RichText(
            //     text: TextSpan(
            //       text: "${L.view.tr} ",
            //       style: TextStyle(
            //         fontSize: 13.sp,
            //         color: AppColors.colorFF7F7F7F,
            //       ),
            //       children: [
            //         TextSpan(
            //           text: L.here.tr,
            //           style: TextStyle(
            //             color: AppColors.colorFF249ED9,
            //             decoration: TextDecoration.underline,
            //           ),
            //         ),
            //       ],
            //     ),
            //   ),
            // ),
          ],
        ),
      ),
    );
  }
}
