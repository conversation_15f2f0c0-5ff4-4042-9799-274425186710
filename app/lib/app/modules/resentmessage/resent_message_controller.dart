import 'package:flutter_metatel/app/data/enums/enum.dart';
import 'package:flutter_metatel/app/data/models/select_contact_model.dart';
import 'package:flutter_metatel/core/languages/l.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:get/get.dart';

import '../../../core/utils/util.dart';
import '../../data/providers/db/database.dart';

class RecentMessageController extends GetxController{
  var allSessionDataObs = <SessionData>[].obs;

  late AppDatabase _database;
  @override
  void onInit() async {
    AppLogger.d("onInit");
    _database = Get.find<AppDatabase>();
    var list = await _database.allSession().get();
    var groupSessions = await _database.allSessionByChatType(1).get();
    if (groupSessions.isNotEmpty) {
      var names = groupSessions.map((e) => e.username);
      var invalidGroup =
          await _database.groupInfoIsInvalid(List.from(names)).get();
      if (invalidGroup.isNotEmpty) {
        list.removeWhere((a) {
          var groupSession =
              invalidGroup.firstWhereOrNull((b) => a.username == b.groupId);
          return groupSession != null;
        });
      }
    }
    allSessionDataObs.value = list;
    super.onInit();
  }

  onTap(SessionData sessionData){
    var displayName =
    sessionData.chatType == 3 ? getOfficeDisplayName() : sessionData.displayname ;
    String text = '${L.forward_to.tr}"$displayName"';
    showBottomDialogCommonWithCancel(Get.context!, widgets: [
      getBottomSheetItemSimple(Get.context!, text, radius: 12, itemCallBack: () {
        var info = _toSelectData(sessionData);
        Get.back(result: info);
      })
    ]);
  }
  /// 转换数据
  SelectContactInfo _toSelectData(SessionData data) {
    List<ContactInfo> contacts = [];
    var displayName =
        data.chatType == 3 ? getOfficeDisplayName() : data.displayname;
    contacts.add(ContactInfo(
      userName: data.username,
      displayName: displayName,
      avatarPath: data.avatarPath,
      chatType: ChatType.values[data.chatType ?? ChatType.singleChat.index],
    ));
    return SelectContactInfo(type: ChatType.values[data.chatType ?? ChatType.singleChat.index], contacts: contacts);
  }
}