import 'package:flutter/material.dart';
import 'package:flutter_metatel/core/values/colors.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../../r.dart';

class AddMediaBtnWidget extends StatelessWidget {
  const AddMediaBtnWidget({super.key, this.onTap});
  final Function()? onTap;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 60.r,
        height: 60.r,
        decoration: BoxDecoration(
          border: Border.all(
            color: AppColors.colorFFA1A5B3,
          ),
          borderRadius: BorderRadius.circular(18.r)
        ),
        child: Image.asset(
          R.addPostBtn, 
          color: AppColors.colorFFA1A5B3,
        ),
      ),
    );
  }
}