import 'dart:typed_data';

import 'package:extended_image/extended_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class VideoThumbnailWidget extends StatelessWidget {
  const VideoThumbnailWidget({super.key, this.isRoundedCorner = true, this.thumbnail, this.thumbnailUrl, this.maxHeight});
  // final double aspectRatio;
  final Uint8List? thumbnail;
  final String? thumbnailUrl;
  final bool isRoundedCorner;
  final double? maxHeight;
  @override
  Widget build(BuildContext context) {
    return (thumbnail == null && (thumbnailUrl == null || thumbnailUrl!.isEmpty))
    ? SizedBox.shrink()
    : Stack(
      children: [
        ConstrainedBox(
          constraints: BoxConstraints(maxHeight: maxHeight ?? 180.h, minWidth: MediaQuery.of(context).size.width),
          child: thumbnail != null 
            ? ExtendedImage.memory(thumbnail!, fit: BoxFit.cover,)
            : ExtendedImage.network(thumbnailUrl!, fit: BoxFit.cover,),
        ),
        Positioned.fill(
          child: Center(
            child: Container(
              width: 40.r,
              height: 40.r,
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.5),
                borderRadius: BorderRadius.circular(20.r),
              ),
              child: Icon(
                Icons.play_arrow_rounded,
                color: Colors.white,
                size: 20.r,
              ),
            ),
          ),
        ),
      ],
    );
  }
}