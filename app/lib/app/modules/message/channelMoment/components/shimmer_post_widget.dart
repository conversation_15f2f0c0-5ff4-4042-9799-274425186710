import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_metatel/core/values/colors.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shimmer/shimmer.dart';

class ShimmerPostWidget extends StatelessWidget {
  const ShimmerPostWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Shimmer.fromColors(
      baseColor: AppColors.colorFFBFBFBF,
      highlightColor: AppColors.white,
      period: Duration(milliseconds: 3000),
      child: Container(
        width: 1.sw,
        padding: EdgeInsets.symmetric(horizontal: 26.r, vertical: 20.r),
        child: Column(
          children: [
            Row(
              children: [
                _buildDynamicCircle(36.r),
                <PERSON><PERSON><PERSON><PERSON>(width: 6.r),
                Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildDynamicLine(_generateRandomNumberBetweenRange(80,150).toDouble().r),
                    SizedBox(height: 4.r),
                    _buildDynamicLine(_generateRandomNumberBetweenRange(70,90).toDouble().r),
                  ],
                ),
              ],
            ),
            SizedBox(height: 120.r),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                _buildDynamicLine(_getBottomLineLength(4)),
                _buildDynamicLine(_getBottomLineLength(4)),
                _buildDynamicLine(_getBottomLineLength(4)),
                _buildDynamicLine(_getBottomLineLength(4)),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDynamicLine(double width) {
    return Container(
      width: width,
      height: 8.r,
      decoration: BoxDecoration(
        color: AppColors.colorFFBFBFBF,
        borderRadius: BorderRadius.circular(3.r),
      ),
    );
  }

  Widget _buildDynamicCircle(double size) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: AppColors.colorFFBFBFBF,
        borderRadius: BorderRadius.circular(size/2),
      ),
    );
  }

  int _generateRandomNumberBetweenRange(int min, int max) {
    final _random = new Random();
    return min + _random.nextInt(max - min);
  }

  double _getBottomLineLength(int num) {
    return (1.sw - 52.r - 30.r)/num;
  }
}