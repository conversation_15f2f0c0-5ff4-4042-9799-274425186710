import 'dart:typed_data';

import 'package:extended_image/extended_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/data/models/post.dart';
import 'package:flutter_metatel/app/modules/message/channelMoment/photo_preview_view.dart';
import 'package:flutter_metatel/core/languages/l.dart';
import 'package:flutter_metatel/core/values/colors.dart';
import 'package:flutter_metatel/r.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_web3/app/core/utils/util.dart';
import 'package:get/get.dart';
import 'package:video_thumbnail/video_thumbnail.dart';

import '../../../../data/enums/enum.dart';
import '../../../../widgets/mavatar_circle_avatar.dart';
import '../video_player_view.dart';
import 'video_thumbnail_widget.dart';



class PostWidget extends StatelessWidget {
  const PostWidget({super.key, required this.post, required this.isMenuVisible, this.onEditPostTap, this.onDeletePostTap, this.onLikeTap, this.onCommentTap, this.onShareTap, this.onMemberProfileTap, this.isDetailPage = false, this.onPostTap, required this.commentSection, });
  final bool isDetailPage;
  final Post post;
  final bool isMenuVisible;
  final Function()? onPostTap;
  final Function()? onMemberProfileTap;
  final Function()? onEditPostTap;
  final Function()? onDeletePostTap;
  final Function()? onLikeTap;
  final Function()? onCommentTap;
  final Function()? onShareTap;
  final Widget? commentSection;
  final double _avatarBigDiameter = 36;
  final double _postLeftPadding = 18;
  final double _postRightPadding = 10;
  final double _postVerticalPadding = 16;
  final double _spacingHeader = 10;
  final double _contentRightPadding = 30;
  final double _contentExtraLeftPadding = 3;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: onPostTap,
      child: Padding(
        padding: isDetailPage? EdgeInsets.zero : EdgeInsets.only(
          left: _postLeftPadding.r,
          right: _postRightPadding.r,
          top: _postVerticalPadding.r,
          bottom: _postVerticalPadding.r,
        ),
        child: Column(
          children: [
            // Post Header
            _buildHeader(onMemberProfileTap: onMemberProfileTap, onEditTap: onEditPostTap, onDeleteTap: onDeletePostTap, isMenuVisible: isMenuVisible),
            // Post Body
            Padding(
              padding: isDetailPage? EdgeInsets.zero : EdgeInsets.only(
                left: (_avatarBigDiameter + _spacingHeader).r,
                right: _contentRightPadding.r,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (post.content != null && post.content!.isNotEmpty)
                    _buildContentSection(post.content),
                  if (post.links?.isNotEmpty ?? false)
                    _buildLinksSection(post.links),
                  if (post.imageUrls?.isNotEmpty ?? false)
                    ..._buildImagesSection(post.imageUrls),
                  if(post.videoUrl?.isNotEmpty ?? false)
                    SizedBox(height: 12.r),
                  if(post.videoUrl?.isNotEmpty ?? false)
                    _buildVideoSection(
                      thumbnailUrl: post.videoThumbnail,
                      onVideoTap: () {
                        Get.to(() =>  VideoPlayerView(url: post.videoUrl,));
                      },
                    ),  
                  ..._buildUserActionSection(),
                ],
              ),
            ),
            if(commentSection != null)
            commentSection!,
          ],
        ),
      ),
    );
  }

  Widget _buildHeader({Function()? onMemberProfileTap, Function()? onEditTap, Function()? onDeleteTap, bool isMenuVisible = false}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      mainAxisSize: MainAxisSize.max,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        GestureDetector(
          onTap: onMemberProfileTap,
          child: MAvatarCircle(
            diameter: _avatarBigDiameter,
            text: post.user?.nickname,
            imagePath: post.user?.avatarUrl,
          ),
        ),
        SizedBox(width: _spacingHeader.r),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                post.user?.nickname ?? "",
                style: TextStyle(fontSize: 14.sp),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              SizedBox(height: 2.h),
              Text(
                post.createdAt ?? "",
                style: TextStyle(
                  fontSize: 10.sp,
                  color: Colors.black.withOpacity(0.3),
                ),
              ),
            ],
          ),
        ),
        SizedBox(width: 10.w),
        isMenuVisible ? buildPostMenu(onEditTap: onEditTap, onDeleteTap: onDeleteTap) : SizedBox.shrink(),
      ],
    );
  }

  Widget _buildContentSection(String? content) {
    // Content
    return Padding(
      padding: EdgeInsets.only(left: _contentExtraLeftPadding.r, top: 6.r),
      child: Text(
        post.content ?? "",
        maxLines: isDetailPage ? null: 5,
        overflow: isDetailPage ? null: TextOverflow.ellipsis,
        style: TextStyle(fontSize: 12.sp),
      ),
    );
  }

  Widget _buildLinksSection(List<String>? links) {
    // Links
    return Padding(
      padding: EdgeInsets.only(left: _contentExtraLeftPadding.r, top: 5.r),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          for (var link in links!)
            GestureDetector(
              onTap: (){
                jumpToBrowserMethod(link);
              },
              child: Text(
                link,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style: TextStyle(
                  color: AppColors.colorFF3474D0,
                  fontSize: 12.sp,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildVideoSection({String? thumbnailUrl, Function()? onVideoTap,}) {
    return thumbnailUrl == null
      ? SizedBox.shrink()
      : GestureDetector(
          onTap: onVideoTap,
          child: VideoThumbnailWidget(            
            thumbnailUrl: thumbnailUrl,
            isRoundedCorner: false,
          ),
        );
  }

  List<Widget> _buildImagesSection(List<String>? images) {
    // Images(max 9) or Video
    return [
      SizedBox(height: 12.r),
      GridView.builder(
        shrinkWrap: true,
        physics: NeverScrollableScrollPhysics(),
        padding: EdgeInsets.zero,
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: images!.length > 4 || images.length == 3
              ? 3
              : images.length > 1
                  ? 2
                  : 1,
          childAspectRatio: 1,
          mainAxisSpacing: 2.r,
          crossAxisSpacing: 2.r,
        ),
        itemCount: images.length,
        itemBuilder: (context, index) {
          return Hero(
            tag: "image_gallery_${post.id}_$index",
            child: GestureDetector(
              onTap: (){
                _onImageTap(images, index);             
              },
              child: ExtendedImage.network(
                images[index],
                fit: BoxFit.cover,
              ),
            ),
          );
        },
      ),
    ];
  }

  List<Widget> _buildUserActionSection() {
    // Like, Comment, Share
    return [
      SizedBox(height: 10.r),
      Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          _buildActionBtn(
            onLikeTap, 
            post.isLike ?? false ? R.iconLikeBtnSolid :R.iconLikeBtn, 
            "${post.likeCount}", 
            textColor: post.isLike ?? false ? AppColors.primaryBgColor1 : Colors.black
          ),
          _buildActionBtn(onCommentTap, R.iconCommentBtn, "${post.commentsCount}"),
          _buildActionBtn(onShareTap, R.iconShareBtn, "${post.shareCount}"),
        ],
      ),
    ];
  }

  Widget _buildActionBtn(Function()? onTap, String icon, String text, {Color? textColor}) {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: onTap,
      child: Padding(
        padding: EdgeInsets.all(10.r),
        child: Row(
          children: [
            Image.asset(
              icon,
              width: 18.r,
              height: 18.r,
            ),
            SizedBox(width: 9.r),
            Text(
              "$text",
              style: TextStyle(fontSize: 14.sp, color: textColor ?? Colors.black),
            ),
          ],
        ),
      ),
    );
  }

  Widget buildPostMenu({Function()? onEditTap, Function()? onDeleteTap}) {
    return PopupMenuButton(
      padding: EdgeInsets.all(0),
      icon: Image.asset(
        R.icoMore,
        width: 14.r,
        height: 14.r,
      ),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      // 在按钮下方显示
      offset: const Offset(0, 50),
      itemBuilder: (context) {
        return [
          PostUtil.buildPopupMenuItem(
            L.edit.tr,
            R.iconEditBtn,
            MomentPostOption.edit.index,
            onEditTap,
          ),
          PostUtil.buildPopupMenuItem(
            L.delete.tr,
            R.iconDeleteBtn,
            MomentPostOption.delete.index,
            onDeleteTap,
          ),
        ];
      },
      onSelected: (value) {
        _onPostMenuItemSelected(value as int);
      },
    );
  }

  /// 贴的菜单选中项
  void _onPostMenuItemSelected(int value) {
    MomentPostOption options = MomentPostOption.values[value];
    switch (options) {
      case MomentPostOption.edit:
        break;
      case MomentPostOption.delete:
        break;
      default:
    }
  }

  void _onImageTap(List<String> images, int selectedIndex) {
    List<GalleryItem> _galleryItems = [];
    if(images.isNotEmpty){
      for(var i = 0; i < images.length; i++){
        _galleryItems.add(GalleryItem(heroTag: "image_gallery_${post.id}_$i", imageUrl: post.imageUrls![i]));
      }
    }
    Get.to(
      () => PhotoPreviewView(galleryItems: _galleryItems, selectedIndex: selectedIndex),
    );
  }

}

class CommentWidget extends StatelessWidget {
  const CommentWidget({super.key, this.onProfileTap, this.onDeleteTap, required this.comment, required this.isMenuVisible, this.isDetailPage = false, });
  final bool isDetailPage;
  final Function()? onProfileTap;
  final Function()? onDeleteTap;
  final bool isMenuVisible;
  final Comment comment;
  final double _avatarBigDiameter = 36;
  final double _avatarSmallDiameter = 30;
  // final double _spacingHeader = 10;
  final int _maxLines = 10;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: isDetailPage? EdgeInsets.only(left: 10.r) : EdgeInsets.only(left: _avatarBigDiameter),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 评论头
          Row(
            children: [
              GestureDetector(
                onTap: onProfileTap,
                child: MAvatarCircle(
                  diameter: _avatarSmallDiameter,
                  text: comment.user?.nickname,
                  imagePath: comment.user?.avatarUrl,
                ),
              ),
              SizedBox(width: 10.r),
              Expanded(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      comment.user?.nickname ?? "",
                      style: TextStyle(fontSize: 14.sp),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    isMenuVisible ? buildCommentMenu(onDeleteTap: onDeleteTap) : SizedBox.shrink(),
                  ],
                ),
              ),
            ],
          ),
          // 评论内容
          Padding(
            padding: EdgeInsets.only(
                left: _avatarSmallDiameter.r + 10.r, right: 15.r),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  comment.comment ?? "",
                  style: TextStyle(fontSize: 12.sp),
                  maxLines: isDetailPage ? null: _maxLines,
                  overflow: isDetailPage ? null: TextOverflow.ellipsis,
                ),
                SizedBox(height: 6.r),
                Text(
                  comment.createdAt ?? "",
                  style: TextStyle(
                    fontSize: 10.sp,
                    color: Colors.black.withOpacity(0.3),
                  ),
                ),
              ],
            ),
          ),
          SizedBox(height: 10.r),
        ],
      ),
    );
  }

  Widget buildCommentMenu({Function()? onDeleteTap}) {
    return PopupMenuButton(
      padding: EdgeInsets.all(0),
      icon: Image.asset(
        R.icoMore,
        width: 14.r,
        height: 14.r,
      ),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      // 在按钮下方显示
      offset: const Offset(0, 50),
      itemBuilder: (context) {
        return [
          PostUtil.buildPopupMenuItem(
            L.delete.tr,
            R.iconDeleteBtn,
            MomentCommentOption.delete.index,
            onDeleteTap,
          ),
        ];
      },
      onSelected: (value) {
        _onCommentMenuItemSelected(value as int);
      },
    );
  }

  /// 留言的菜单选中项
  void _onCommentMenuItemSelected(int value) {
    MomentCommentOption options = MomentCommentOption.values[value];
    switch (options) {
      case MomentCommentOption.delete:
        break;
      default:
    }
  }

}


class PostUtil {

  /// 构建弹出菜单Item
  static PopupMenuItem buildPopupMenuItem(
      String title, String imageName, int position, Function()? onTap) {
    return PopupMenuItem(
      value: position,
      onTap: onTap,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Image.asset(imageName, width: 20.r, height: 20.r),
          SizedBox(width: 8.w),
          Padding(
            padding: EdgeInsets.only(right: 15.r),
            child: Text(
              title,
              style: TextStyle(fontSize: 13.sp, color: AppColors.colorFF333333),
            ),
          ),
        ],
      ),
    );
  }

  static Future<Uint8List?> getVideoThumbnail(String filePathOrUrl, {int? quality, int? maxWidth}) async {
    return await VideoThumbnail.thumbnailData(
      video: filePathOrUrl,
      imageFormat: ImageFormat.JPEG,
      maxWidth: maxWidth ?? 0,
      quality: quality ?? 35,
    );
  }

}