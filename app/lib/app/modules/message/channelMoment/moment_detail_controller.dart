import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_metatel/app/modules/message/channelMoment/channel_moment_controller.dart';
import 'package:flutter_metatel/core/languages/l.dart';
import 'package:get/get.dart';
import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';

import '../../../../core/utils/app_log.dart';
import '../../../data/models/post.dart';
import '../../../data/providers/api/channel.dart';
import '../../../data/services/channel_service.dart';

class MomentDetailController extends GetxController {
  late final String postId;
  RxString textFieldText = ''.obs;
  final TextEditingController textEditingController = TextEditingController();
  final PagingController<int, Comment> pagingController = PagingController(firstPageKey: 1);
  final ChannelMomentController _channelMomentController = Get.find<ChannelMomentController>();
  

  @override
  void onInit() {    
    postId = Get.arguments["post_id"];
    pagingController.addPageRequestListener((pageKey) {
      _fetchMoreComments(postId, pageNo: pageKey);
    });
    super.onInit();
  }

  @override
  void onClose() {
    pagingController.dispose();
    textEditingController.dispose();
    super.onClose();
  }

  bool isTextFieldEmpty() {
    return textFieldText.value.isEmpty;
  }

  bool isAdminOrOwner() {
    return _channelMomentController.isAdminOrOwner();
  }

  Post getPostByIdFromSource(String postId) {
    return _channelMomentController.getPostById(postId);
  }

  void onMemberProfileTap(User? user) {
    _channelMomentController.onMemberProfileTap(user);
  }

  void onEditPostTap(Post post) async {
    Post? result = await _channelMomentController.openCreatePostView(post: post);
    if(result == null) return;
    // 编辑帖 成功，更新页面
    _channelMomentController.refreshSpecificPost(result);
    pagingController.notifyPageRequestListeners(1);
    
  }

  void onDeletePostTap(Post post) {
    _channelMomentController.onDeletePostTap(post.id, needBack: true);
  }

  void onLikePostTap(Post post) {
    _channelMomentController.onLikePostTap(post.id);
  }

  void onSharePostTap(Post post) {
    _channelMomentController.onSharePostTap(post);
  }

  void onDeleteCommentTap(String postId, String? commentId) async {
    bool? isConfirm = await _channelMomentController.showConfirmationDialog(
        L.delete_comment_confirmation_desc.tr, L.confirm.tr);
    if (!(isConfirm ?? false)) return;
    if (commentId == null || commentId.isEmpty) return;
    EasyLoading.show(maskType: EasyLoadingMaskType.black);
    try {
      var result = await _channelMomentController.deleteComment(postId: postId, commentId: commentId);
      if (!(result ?? false)) {
        EasyLoading.showError(L.delete_fail.tr);
        return;
      }
      // 删除成功，刷新页面
      EasyLoading.showSuccess(L.delete_success.tr);
      pagingController.notifyPageRequestListeners(1);
    } catch (e) {
      EasyLoading.showError(L.delete_fail.tr);
      AppLogger.e("Error Catch<onDeleteCommentTap>: $e");
    }
  }

  void onSendComment(String postId, String comment) async {
    if(textFieldText.value.isEmpty) {
      return;
    }
    EasyLoading.show(maskType: EasyLoadingMaskType.black);
    try {
      var result = await _commentPost(postId, comment);
      if (!(result ?? false)) {
        EasyLoading.showError(L.failed.tr);
        return;
      }
      // 评论成功
      EasyLoading.showSuccess(L.success.tr);
      textEditingController.clear();
      textFieldText.value = "";
      pagingController.notifyPageRequestListeners(1);
    } catch (e) {
      EasyLoading.showError(L.failed.tr);
      AppLogger.e("Error Catch<onSendComment>: $e");
    }
  }

  Future<void> _fetchMoreComments(String postId, {int pageNo = 1}) async {
    try {
      var data = await ChannelMomentApi.getMoreComments(
        headers: _channelMomentController.apiHeaders(),
        postId: postId,
        pageNo: "$pageNo",
      );
      if(data == null) return;
      // 通过username去获取nickname & avatarUrl
      if (data.comments?.isNotEmpty ?? false) { 
        for(var comment in data.comments!){
          if (comment.user?.userName != null) {
            MemberInfo info = await _channelMomentController.getMemberData(comment.user!.userName!);
            comment.user?.nickname = info.nickname;
            comment.user?.avatarUrl = info.avatarPath;
          }
        }
      }
      // 处理&更新数据
      Post post = _channelMomentController.getPostById(postId);
      post.commentPaginationModel?.updateComment(commentPaginationModel: data);
      post.commentsCount = data.totalCount;
      _channelMomentController.moments.refresh();
      // 处理分页&显示
      final isLastPage = data.currentPage == data.totalPage;
      final isFirstPage = data.currentPage == 1;
      if(isFirstPage){
        pagingController.itemList?.clear();
      }
      if(isLastPage) {
        pagingController.appendLastPage(data.comments ?? []);
      } else {
        final nextPageKey = data.currentPage < data.totalPage 
          ? data.currentPage + 1
          : data.currentPage;        
        pagingController.appendPage(data.comments ?? [], nextPageKey);
      }
    } catch (e) {
      // pagingController.error = e;
      AppLogger.e("Error Catch <_fetchMoreComments>: $e");
      return null;
    }
  }

  Future<bool?> _commentPost(String postId, String comment) async {
    try {
      return await ChannelMomentApi.commentPost(
        headers: _channelMomentController.apiHeaders(),
        postId: postId,
        comment: comment,
      );
    } catch (e) {
      AppLogger.e("Error Catch <_commentPost>: $e");
      return null;
    }
  }
  
}
