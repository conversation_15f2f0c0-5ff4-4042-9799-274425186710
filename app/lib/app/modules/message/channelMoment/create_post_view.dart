import 'dart:io';
import 'dart:typed_data';

import 'package:extended_image/extended_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/modules/message/channelMoment/components/add_media_btn_widget.dart';
import 'package:flutter_metatel/app/modules/message/channelMoment/create_post_controller.dart';
import 'package:flutter_metatel/core/languages/l.dart';
import 'package:flutter_metatel/core/values/colors.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';

import 'components/video_thumbnail_widget.dart';
import 'photo_preview_view.dart';

class CreatePostView extends StatefulWidget {
  const CreatePostView({super.key});

  @override
  State<CreatePostView> createState() => _CreatePostViewState();
}

class _CreatePostViewState extends State<CreatePostView> {
  final CreatePostController _controller = Get.put(CreatePostController());

  @override
  void initState() {
    _controller.textEditingController.addListener(() {
      setState(() {}); // 字数显示
    });
    super.initState();
  }

  @override
  void dispose() {
    _controller.textEditingController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      child: GestureDetector(
        onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
        child: Scaffold(
          appBar: _buildAppBar(),
          body: SingleChildScrollView(
            child: Container(
              height: 1.sh,
              color: AppColors.white,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildTextFieldSection(),
                  _buildMediaSection(
                    context,
                    selectedImages: _controller.selectedImages, 
                    imageUrls: _controller.imageUrls,
                    selectedVideo: _controller.selectedVideo,
                    videoUrl: _controller.videoUrl,
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  AppBar _buildAppBar() {
    return AppBar(
      elevation: 1,
      leading: IconButton(
        onPressed: () {
          Get.back();
        },
        icon: Icon(Icons.close),
      ),
      title: Text(
        L.create_post.tr,
        style: TextStyle(
          fontWeight: FontWeight.w900,
        ),
      ),
      centerTitle: true,
      actions: [
        Obx(
          () => TextButton(
            onPressed: _controller.checkIsPostValid().value 
              ? _controller.onCreatePostTap    
              : null,
            child: Text(
              _controller.post.value == null 
                ? L.post.tr
                : L.update.tr,
              style: TextStyle(
                fontSize: 14.sp,
                fontWeight: FontWeight.w400,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildTextFieldSection() {
    final int _maxLength = 10000;
    return Stack(
      children: [
        TextFormField(
          autofocus: false,
          keyboardType: TextInputType.text,
          maxLines: 10,
          maxLength: _maxLength,
          controller: _controller.textEditingController,
          decoration: InputDecoration(
            contentPadding:
                EdgeInsets.symmetric(horizontal: 20.r, vertical: 10.r),
            hintText: L.create_post_hint.tr,
            hintStyle: TextStyle(
              color: AppColors.colorFFA1A5B3,
            ),
            counterText: "",
          ),
          onChanged: (value) {
            _controller.content = value;
          },
        ),
        Positioned(
          right: 20.r,
          bottom: 10.r,
          child: Text(
            "${_controller.textEditingController.text.length}/$_maxLength",
            style: TextStyle(
              color: AppColors.colorFF919499,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildMediaSection(BuildContext context, {required RxList<XFile> selectedImages, required RxList<String> imageUrls,required Rxn<XFile> selectedVideo, required RxString videoUrl }) { // selectedImage: 用户选择的图片；imageUrl: 编辑时的图片url
    return Obx(
      () => ((selectedImages.isEmpty && imageUrls.isEmpty) && (selectedVideo.value==null && videoUrl.isEmpty))
          ? Padding(
              padding: EdgeInsets.symmetric(horizontal: 30.r, vertical: 20.r),
              child: AddMediaBtnWidget(
                onTap: () {
                  _controller.onSelectMediaTap(context);
                },
              ),
            )
          : selectedImages.isNotEmpty || imageUrls.isNotEmpty
            ? _buildMediaImageSection(selectedImages: selectedImages, imageUrls: imageUrls)
            : _buildMediaVideoSection(selectedVideo: selectedVideo, videoUrl: videoUrl)
    );
  }

  Widget _buildMediaVideoSection({required Rxn<XFile> selectedVideo, required RxString videoUrl}) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 30.r, vertical: 20.r),
      child: _buildVideoWidget(
        thumbnail: _controller.selectedVideoThumbnail, 
        thumbnailUrl: _controller.videoThumbnailUrl,
        onRemoveVideoTap: () {
          if(selectedVideo.value != null){
            _controller.onRemoveVideoTap();
          }
          if(videoUrl.value.isNotEmpty){
            _controller.onRemoveVideoUrlTap();
          }
        },
        onVideoTap: () {
          _controller.onVideoTap(xfile: _controller.selectedVideo.value, url: _controller.videoUrl.value);
        }
      ),
    );
  }

  Widget _buildMediaImageSection({required RxList<XFile> selectedImages, required RxList<String> imageUrls}) {
    return Builder(
        builder: (context) {
          List<GalleryItem> _galleryItems = []; // 传入图片preview使用
          List<Widget> _gridWidgets = []; // 把数据format成widget
          // 对图片的url，处理进入GalleryItem
          if(imageUrls.isNotEmpty) {
            for(var i = 0; i < imageUrls.length; i++){
              _galleryItems.add(GalleryItem(heroTag: "selected_image_url_${i}", imageUrl: imageUrls[i]));
            }                  
          }
          // 对用户选取的图片，处理进入GalleryItem
          if(selectedImages.isNotEmpty) {
            for(var i = 0; i < selectedImages.length; i++){
              _galleryItems.add(GalleryItem(heroTag: "selected_image_${i}", imageFile: File(selectedImages[i].path)));
            }
          }
          // 把图片转为Widget
          if(_galleryItems.isNotEmpty){
            for(var i = 0; i < _galleryItems.length; i++){
              _gridWidgets.add(
                _buildImageWidget(
                  heroTag: _galleryItems[i].heroTag,
                  file: _galleryItems[i].imageFile ?? null,
                  imageUrl: _galleryItems[i].imageUrl ?? null,                        
                  onImageTap: () {                 
                    Get.to(
                      () => PhotoPreviewView(galleryItems: _galleryItems, selectedIndex: i),
                    );
                  },
                  onRemoveImageTap: () {
                    if(_galleryItems[i].imageFile != null) {
                      _controller.onRemoveSelectedImageTap(i);
                    }
                    if(_galleryItems[i].imageUrl != null) {
                      _controller.onRemoveImageUrlTap(i);
                    }
                  }
                ),
              );
            }
          }                
          if(_gridWidgets.length < 9) {  // 当图片小于9，添加“加图片”按钮
            _gridWidgets.add(
              Center(
                child: AddMediaBtnWidget(
                  onTap: () {
                    _controller.onSelectMediaTap(context);
                  },
                ),
              ),
            );
          }
          return Expanded(
            child: Container(
                width: double.infinity,
                padding: EdgeInsets.symmetric(horizontal: 30.r, vertical: 20.r),
                child: GridView.builder(
                  shrinkWrap: true,
                  physics: NeverScrollableScrollPhysics(),
                  gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 3,
                    childAspectRatio: 1,
                    mainAxisSpacing: 9.r,
                    crossAxisSpacing: 2.r,
                  ),
                  itemCount: _gridWidgets.length,
                  itemBuilder: (context, index) {
                    return _gridWidgets[index];                        
                  },
                ),
              ),
          );
        }
    );
  }

  Widget _buildImageWidget({String? imageUrl, File? file, Function()? onImageTap ,Function()? onRemoveImageTap, required String heroTag}) {
    return Stack(
      children: [
        GestureDetector(
          onTap: onImageTap,
          child: Hero(
            tag: heroTag,
            child: Container(
              width: double.infinity,
              height: double.infinity,
              padding: EdgeInsets.all(6.r),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(17.r),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(17.r),
                child: imageUrl != null 
                  ? ExtendedImage.network(
                      imageUrl,
                      fit: BoxFit.cover,
                    )
                  : Image.file(
                      file!,
                      fit: BoxFit.cover,  
                    ),
              ),
            ),
          ),
        ),
        Positioned(
          right: 0,
          top: 0,
          child: GestureDetector(
            onTap: onRemoveImageTap,
            child: Container(
              width: 22.r,
              height: 22.r,          
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.5),
                borderRadius: BorderRadius.circular(11.r),
              ),
              child: Icon(
                Icons.close, 
                color: AppColors.white,
                size: 18.r,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildVideoWidget({Uint8List? thumbnail, String? thumbnailUrl, Function()? onRemoveVideoTap, Function()? onVideoTap}) {
    return _controller.isGettingThumbnail.value
      ? SizedBox.shrink()
      : thumbnail == null && thumbnailUrl == null
        ? Container(
            width: 160.r,
            height: 100.r,
            decoration: BoxDecoration(
              color: Colors.grey,
              borderRadius: BorderRadius.circular(15.r)
            ),
            child: Center(
              child: Container(
                width: 30.r,
                height: 30.r,
                decoration: BoxDecoration(
                  color: Colors.black.withOpacity(0.5),
                  borderRadius: BorderRadius.circular(15.r),
                ),
                child: Icon(
                  Icons.error_outline_rounded,
                  color: Colors.white,
                  size: 15.r,
                ),
              ),
            ),
          )
        : Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              GestureDetector(
                onTap: onVideoTap,
                child: VideoThumbnailWidget(thumbnail: thumbnail, thumbnailUrl: thumbnailUrl),
              ),
              SizedBox(height: 10.r),
              GestureDetector(
                onTap: onRemoveVideoTap,
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: 20.r, vertical: 4.r),
                  decoration: BoxDecoration(
                    border: Border.all(color: AppColors.colorFFFF0000),
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                  child: Text(
                    L.remove.tr, 
                    style: TextStyle(
                      color: AppColors.colorFFFF0000, 
                      fontSize: 11.sp, 
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),              
            ],
          );

  }
}