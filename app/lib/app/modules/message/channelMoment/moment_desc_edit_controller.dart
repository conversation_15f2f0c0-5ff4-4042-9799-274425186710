import 'package:flutter/material.dart';
import 'package:get/get.dart';

class MomentDescEditController extends GetxController {
  String? initialDesc;
  late TextEditingController textEditingController;

  @override
  void onInit() {
    var argument = Get.arguments;
    if(argument !=null && argument.isNotEmpty){
      initialDesc = argument;
    }
    textEditingController = TextEditingController(text: initialDesc);
    super.onInit();
  }
}