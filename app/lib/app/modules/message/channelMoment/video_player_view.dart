import 'dart:io';

import 'package:flutter/material.dart';
import 'package:chewie/chewie.dart';
import 'package:flutter_metatel/core/languages/l.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:video_player/video_player.dart';

import '../../../../core/values/colors.dart';

class VideoPlayerView extends StatefulWidget {
  const VideoPlayerView({super.key, this.filePath, this.url,});
  final String? filePath;
  final String? url;
  @override
  State<VideoPlayerView> createState() => _VideoPlayerViewState();
}

class _VideoPlayerViewState extends State<VideoPlayerView> {
  late VideoPlayerController _videoPlayerController;
  ChewieController? _chewieController;
  bool hasError = false;
  
  @override
  void initState() {
    initPlayer();    
    super.initState();
  }

  @override
  void dispose() {
    _videoPlayerController.dispose();
    _chewieController?.dispose();
    super.dispose();
  }

  void initPlayer() async {
    if(widget.filePath != null) {
      _videoPlayerController = VideoPlayerController.file(File(widget.filePath!));
    }
    if(widget.url != null) {
      _videoPlayerController = VideoPlayerController.networkUrl(Uri.parse(widget.url!));
    }
    _videoPlayerController.addListener(() {
      if(_videoPlayerController.value.hasError){
        hasError = true;
        setState(() {});
      }
    });
    await _videoPlayerController.initialize();
    _chewieController = ChewieController(
      videoPlayerController: _videoPlayerController,
      autoPlay: true,
      showOptions: false,
      materialProgressColors: ChewieProgressColors(playedColor: AppColors.colorFF3474D0),
      // showControls: false,      
      // allowFullScreen: false,
    );
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(elevation: 1),
      body: Container(
        width: 1.sw,
        height: 1.sh - kToolbarHeight,
        color: Colors.black,
        child: hasError
          ? Center(
              child: Text(
                L.video_not_support.tr, 
                style: TextStyle(color: Colors.white, fontSize: 24.sp, fontWeight: FontWeight.w600),
                textAlign: TextAlign.center,
              ),
            )
          : _chewieController == null
            ? Center(
                child: SpinKitThreeBounce(
                  color: AppColors.colorFF3474D0,
                  size: 40.r,
                ),
              )
            : Chewie(controller: _chewieController!),
      ),
    );
  }
}