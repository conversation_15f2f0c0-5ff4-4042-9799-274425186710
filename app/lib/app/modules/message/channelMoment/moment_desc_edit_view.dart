import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/modules/message/channelMoment/moment_desc_edit_controller.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../core/languages/l.dart';
import '../../../../core/values/colors.dart';

class MomentDescEditView extends GetView<MomentDescEditController> {
  const MomentDescEditView({super.key});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
      child: Scaffold(
        appBar: _buildAppBar(),
        body: _buildBody(),
      ),
    );
  }

  AppBar _buildAppBar() {
    return AppBar(
      backgroundColor: AppColors.white,
      centerTitle: true,
      title: Text(
        L.update_description_dialog_title.tr,
        style: TextStyle(
          fontWeight: FontWeight.bold,
          // fontSize: 15.sp,
        ),
      ),
      // iconTheme: IconThemeData(color: AppColors.white),
    );
  }

  Widget _buildBody() {
    return Container(
      color: AppColors.white,
      padding: EdgeInsets.symmetric(horizontal: 20.r),
      constraints: BoxConstraints.expand(),
      child: SingleChildScrollView(
        physics: BouncingScrollPhysics(),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: 40.r),
            Text(
              L.channel_moment_desc.tr,
              style: TextStyle(
                fontSize: 15.sp,
                color: Colors.black,
              ),
            ),
            SizedBox(height: 10.r),
            SizedBox(
              width: 1.sw,
              child: TextField(
                controller: controller.textEditingController,
                maxLines: 7,
                maxLength: 10000,
                style: TextStyle(
                  fontSize: 12.sp,
                  color: Colors.black,
                ),
                decoration: InputDecoration(
                  hintText: L.update_description_dialog_hint.tr,
                  hintStyle: TextStyle(
                    fontSize: 12.sp,
                    color: AppColors.hintTextColor,
                  ),
                  contentPadding:
                      EdgeInsets.symmetric(horizontal: 10.r, vertical: 15.r),
                  // counterText: '',
                  // border: OutlineInputBorder(),
                ),
              ),
            ),
            SizedBox(height: 80.r),
            ElevatedButton(
              onPressed: () {
                Get.back(result: controller.textEditingController.text);
              },
              child: Text(
                L.update.tr,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
