import 'package:flutter/material.dart';
import 'package:flutter_metatel/core/languages/l.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';

import '../../../../core/values/colors.dart';
import '../../../data/models/post.dart';
import 'components/post_widget.dart';
import 'moment_detail_controller.dart';

class MomentDetailView extends StatefulWidget {
  const MomentDetailView({super.key});

  @override
  State<MomentDetailView> createState() => _MomentDetailViewState();
}

class _MomentDetailViewState extends State<MomentDetailView> {
  bool _requestFocus = false;
  final MomentDetailController _controller = Get.put(MomentDetailController());
  final FocusNode _focusNode = FocusNode();

  @override
  void initState() {
    _requestFocus = Get.arguments["request_focus"] ?? false;
    if(_requestFocus){
      _setFocus();
    }
    super.initState();
  }

  @override
  void dispose() {
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
      child: Scaffold(
        appBar: _buildAppBar(),
        body: Stack(
          children: [
            Container(
              color: AppColors.white,
              padding: EdgeInsets.symmetric(horizontal: 16.r),
              child: NestedScrollView(
                headerSliverBuilder:(context, innerBoxIsScrolled) {
                  return [
                    SliverToBoxAdapter(
                      child: SizedBox(
                        height: 20.r, 
                      ),
                    ),
                    SliverToBoxAdapter(
                      child: _buildPostBody(),
                    ),
                  ];
                }, 
                body: _buildPaginationCommentSection(),
              ),
            ),
            // 输入评论部分
            Positioned(
              bottom: 0,
              child: _buildWriteCommentSection(),
            ),
          ],
        ),
      ),
    );
  }

  AppBar _buildAppBar() {
    return AppBar(
      elevation: 1,
      title: Text(
        _controller.getPostByIdFromSource(_controller.postId).user?.nickname ?? '',
        style: TextStyle(
          fontWeight: FontWeight.w900,
        ),
      ),
      centerTitle: true,
    );
  }

  Widget _buildPostBody() {
    return Obx(() {
      String postId = _controller.postId;
      Post post = _controller.getPostByIdFromSource(postId);
      return PostWidget(
          isDetailPage: true,
          post: post,
          isMenuVisible: _controller.isAdminOrOwner(),
          onMemberProfileTap: () {
            _controller.onMemberProfileTap(post.user);
          },
          onEditPostTap: () {
            _controller.onEditPostTap(post);
          },
          onDeletePostTap: () {
            _controller.onDeletePostTap(post);            
          },
          onLikeTap: () {
            _controller.onLikePostTap(post);
          },
          onCommentTap: () {
            _setFocus();
          },
          onShareTap: () {
            _controller.onSharePostTap(post);
          },
          commentSection: null,
          // commentSection: _buildPaginationCommentSection(),
      );
    });
  }

  Widget _buildPaginationCommentSection() {
    return Column(
      children: [
        SizedBox(height: 24.r),
        Expanded(
          child: PagedListView<int, Comment>(
            pagingController: _controller.pagingController,
            physics:BouncingScrollPhysics(),
            builderDelegate: PagedChildBuilderDelegate<Comment>(
              itemBuilder: (context, item, index) => CommentWidget(
                isDetailPage: true,
                isMenuVisible: _controller.isAdminOrOwner(),
                comment: item,
                onProfileTap: () {
                  _controller.onMemberProfileTap(item.user);
                },
                onDeleteTap: () {
                  _controller.onDeleteCommentTap(_controller.postId, item.id);
                },
              ),
              firstPageProgressIndicatorBuilder: (context) {
                return SizedBox.shrink();
              },
              noItemsFoundIndicatorBuilder: (context) {
                return SizedBox.shrink();
              },
            ),
          ),
        ),
        SizedBox(height: 50.r), // 输入评论部分的高度
      ],
    );
  }

  Widget _buildWriteCommentSection() {
    return Obx(
      () => Container(
        width: 1.sw,
        height: 50.r,
        decoration: BoxDecoration(
          color: AppColors.backgroundGray,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.2),
              spreadRadius: 2,
              blurRadius: 10,
              offset: Offset(0, 4),
            ),
          ],
        ),
        child: Row(
          children: [
            // 输入框
            Expanded(
              child: Padding(
                padding: EdgeInsets.only(
                  top: 5.r,
                  bottom: 5.r,
                  left: 20.r,
                  right: _controller.isTextFieldEmpty() ? 20.r : 5.r,
                ),
                child: Container(
                  decoration: BoxDecoration(
                    color: AppColors.white,
                    // AppColors.colorB2666666,
                    borderRadius: BorderRadius.circular(20.r),
                  ),
                  child: Padding(
                    padding: EdgeInsets.symmetric(horizontal: 16.r),
                    child: TextField(
                      controller: _controller.textEditingController,
                      focusNode: _focusNode,
                      maxLines: 1,
                      maxLength: 500,
                      decoration: InputDecoration(
                        counterText: '',
                        border: InputBorder.none,
                        hintText: L.moment_comment_hint.tr,
                      ),
                      onChanged: (value) {
                        _controller.textFieldText.value = value;
                      },
                    ),
                  ),
                ),
              ),
            ),
            if (!_controller.isTextFieldEmpty())
              IconButton(
                onPressed: (){
                  _focusNode.unfocus();
                  _controller.onSendComment(_controller.postId, _controller.textFieldText.value);
                },
                icon: Icon(
                  Icons.send_rounded,
                  color: AppColors.appDefault,
                ),
              ),
            if (!_controller.isTextFieldEmpty()) SizedBox(width: 5.r),
          ],
        ),
      ),
    );
  }

  void _setFocus() {
    _focusNode.requestFocus();
  }
}
