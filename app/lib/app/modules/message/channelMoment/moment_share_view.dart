
import 'package:extended_image/extended_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/modules/message/channelMoment/moment_share_controller.dart';
import 'package:flutter_metatel/core/languages/l.dart';
import 'package:flutter_metatel/core/values/colors.dart';
import 'package:flutter_metatel/r.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:qr_flutter/qr_flutter.dart';

import '../../../widgets/mavatar_circle_avatar.dart';
import 'components/video_thumbnail_widget.dart';

class MomentShareView extends StatefulWidget {
  const MomentShareView({super.key});

  @override
  State<MomentShareView> createState() => _MomentShareViewState();
}

class _MomentShareViewState extends State<MomentShareView> {
  final MomentShareController _controller = Get.put(MomentShareController());
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      extendBodyBehindAppBar: true,
      body: _buildBody(),      
    );
  }

  AppBar _buildAppBar() {
    return AppBar(
      backgroundColor: AppColors.white,
      centerTitle: true,
      title: Text(
        L.share_now.tr, 
        style: TextStyle(
          fontWeight: FontWeight.bold,
          fontSize: 15.sp
        ),
      ),
      // iconTheme: IconThemeData(color: AppColors.white),
    );
  }

  Widget _buildBody() {
    return Container(
      height: 1.sh,
      width: 1.sw,        
      padding: EdgeInsets.symmetric(horizontal: 35.r),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppColors.primaryBgColor2,
            AppColors.primaryBgColor1,                            
          ],
          begin: Alignment(-1,-1),
          end: Alignment(1,1),      
        ),
      ),
      child: Column(
        children: [
          SizedBox(height: 0.18.sh,),
          _buildShareSection(),
          SizedBox(height: 20.r),
          _buildActionSection(),
        ],
      ),
    );
  }

  // 分享区
  Widget _buildShareSection() {
    return Expanded(
      child: Center(
        child: Container(
          constraints: BoxConstraints(maxHeight: 0.60.sh - 20.r, minWidth: 1.sw),
          padding: EdgeInsets.symmetric(horizontal: 10.r, vertical: 20.r),
          decoration: BoxDecoration(
            color: AppColors.white,
            borderRadius: BorderRadius.circular(20.r),            
          ),
          child: RepaintBoundary(
            key: _controller.key,
            child: Container(
              padding: EdgeInsets.all(10.r),
              color: AppColors.white,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  _buildPostSection(),
                  SizedBox(height: 15.r),
                  _buildShareInfoSection(),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  // 分享区内的帖展示
  Widget _buildPostSection() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildPostHeader(),
        _buildPostBody(),
      ],
    );
  }

  Widget _buildPostHeader() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      mainAxisSize: MainAxisSize.max,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        MAvatarCircle(
          diameter: 36,
          text: _controller.post.user?.nickname,
          imagePath: _controller.post.user?.avatarUrl,
        ),
        SizedBox(width: 10.r),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                _controller.post.user?.nickname ?? "",
                style: TextStyle(fontSize: 14.sp),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              SizedBox(height: 2.h),
              Text(
                _controller.post.createdAt ?? "",
                style: TextStyle(
                  fontSize: 10.sp,
                  color: Colors.black.withOpacity(0.3),
                ),
              ),
            ],
          ),
        ),
        SizedBox(width: 10.w),
      ],
    );
  }

  Widget _buildPostBody() {
    final post = _controller.post;
    return Container(
      constraints: BoxConstraints(minHeight: 0.2.sh),
      padding: EdgeInsets.only(
        left: 46.r,
        right: 30.r,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (post.content != null && post.content!.isNotEmpty)
            SizedBox(height: 4.r),
          if (post.content != null && post.content!.isNotEmpty)
            _buildPostContentSection(post.content),
          if (post.imageUrls?.isNotEmpty ?? false)
            SizedBox(height: 8.r),
          if (post.imageUrls?.isNotEmpty ?? false)
            _buildPostImagesSection(post.imageUrls),
          if(post.videoUrl?.isNotEmpty ?? false)
            SizedBox(height: 12.r),
          if(post.videoUrl?.isNotEmpty ?? false)
            _buildPostVideoSection(
              thumbnailUrl: post.videoThumbnail,
            ), 
        ],
      ),
    );
  }

  Widget _buildPostContentSection(String? content) {
    // Content
    return Padding(
      padding: EdgeInsets.only(left: 2.r),
      child: Text(
        content ?? "",
        maxLines: 5,
        overflow: TextOverflow.ellipsis,
        style: TextStyle(fontSize: 10.sp),
      ),
    );
  }

  Widget _buildPostVideoSection({String? thumbnailUrl, Function()? onVideoTap,}) {
    return thumbnailUrl == null
      ? SizedBox.shrink()
      : GestureDetector(
          onTap: onVideoTap,
          child: VideoThumbnailWidget(
            thumbnailUrl: thumbnailUrl,
            maxHeight: 150.h,
            isRoundedCorner: false,
          ),
        );
  }

  Widget _buildPostImagesSection(List<String>? images) {
    // Images(max 9) or Video
    return GridView.builder(
      padding: EdgeInsets.zero,
      shrinkWrap: true,
      physics: NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: images!.length > 4 || images.length == 3
            ? 3
            : images.length > 1
                ? 2
                : 1,
        childAspectRatio: 1,
        mainAxisSpacing: 2.r,
        crossAxisSpacing: 2.r,
      ),
      itemCount: images.length,
      itemBuilder: (context, index) {
        return ExtendedImage.network(
          images[index],
          fit: BoxFit.cover,
        );
      },
    );
  }

  // 分享区内的详情
  Widget _buildShareInfoSection() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      children: [
        _buildInvitationCodeSection(),
        _buildDivider(),
        _buildQr(),
        _buildStoreBadge(),
      ],
    );
  }

  Widget _buildInvitationCodeSection() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text("Invitation Code", style: TextStyle(fontSize: 12.sp),),
        Text("邀请码", style: TextStyle(fontSize: 12.sp),),
        SizedBox(height: 10.r),
        Text(
          "${_controller.inviteCode.value}", 
          style: TextStyle(
            fontSize: 18.sp, 
            color: AppColors.colorFFEB9D00, 
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  Widget _buildDivider(){
    return Container(
      height: 80.r,
      width: 3.r,
      decoration: BoxDecoration(
        border: Border(
          right: BorderSide(
              width: 2.r,
              color: AppColors.colorFFC9DBF4,
          ),
        ),
      ),
    );
  }

  Widget _buildQr() {
    return Container(
      width: 70.r,
      height: 70.r,
      decoration: BoxDecoration(
        border: Border.all(color: AppColors.colorFF180080, width: 2),
        borderRadius: BorderRadius.circular(10.r),
      ),
      child:  QrImageView(
        data: "https://linksaychat.com/download",
        gapless: false,
      ),
    );
  }

  Widget _buildStoreBadge() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 80.r,
          child: Image.asset(R.badgeAppStore),
        ),
        SizedBox(height: 8.r,),
        Container(
          width: 80.r,
          child: Image.asset(R.badgeGooglePlay),
        ),
      ],
    );
  }


  // 按钮区：分享 & 保存
  Widget _buildActionSection() {
    return Container(
      height: 0.22.sh,    
      padding: EdgeInsets.symmetric(horizontal: 60.r),
      child: Center(
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            _buildButton(
              icon: R.iconMomentShare, 
              text: L.share.tr,
              onTap: _controller.onShareBtnTap,
            ),
            _buildButton(
              icon: R.iconMomentSave, 
              text: L.save.tr,
              onTap: _controller.onSaveBtnTap,
            ),
          ],
        ),
      ),  
    );
  }

  // 按钮区内的按钮
  Widget _buildButton({required String icon, required String text, Function()? onTap}){
    return GestureDetector(
      onTap: onTap,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 40.r,
            height: 40.r,
            decoration: BoxDecoration(
              color: AppColors.white,
              borderRadius: BorderRadius.circular(20.r),
            ),
            child: Center(
              child: Image.asset(icon, width: 17.r, height: 17.r,),
              //  Icon(icon, size: 20.r, color: AppColors.primaryBgColor1,),
            ),
          ),
          SizedBox(height: 8.r),
          Text(
            text,
            style: TextStyle(
              fontSize: 12.sp,
              color: AppColors.white,
            ),
          ),
        ],
      ),
    );
  }

}


