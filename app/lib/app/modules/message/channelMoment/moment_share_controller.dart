import 'dart:io';
import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/data/models/post.dart';
import 'package:get/get.dart';
import 'package:share_plus/share_plus.dart';

import '../../../../core/languages/l.dart';
import '../../../../core/task/time_task.dart';
import '../../../../core/utils/save_image_to_photo.dart';
import '../../../../core/utils/util.dart';
import '../../../data/providers/api/invite.dart';
import '../../../data/services/config_service.dart';

class MomentShareController extends GetxController {
  late final Post post;
  RxString inviteCode = ''.obs;
  final _appConfigService = Get.find<AppConfigService>();
  GlobalKey key = GlobalKey();
  ByteData? imageBytes;

  @override
  void onInit() {
    post = Get.arguments["post"];
    getInviteCode();
    super.onInit();
  }

  void onShareBtnTap() {
    _shareImage();
  }

  void onSaveBtnTap() {
    _saveImage();
  }

  void _saveImage() async {
    imageBytes = await RepaintBoundaryUtils.savePhotoByGlobalKey(key);
    var millisecondsSinceEpoch = TimeTask.instance.getNowDateTime().millisecondsSinceEpoch;
    String name = "${L.app_name.tr}-${L.share.tr}-$millisecondsSinceEpoch";
    RepaintBoundaryUtils.savePhoto(
      imageBytes,
      name: name,
      toastSuccess: "${L.save.tr} ${L.success.tr}",
      toastError: "${L.save.tr} ${L.failed.tr}",
    );
  }

  void _shareImage() async {    
    imageBytes = await RepaintBoundaryUtils.savePhotoByGlobalKey(key);
    if(imageBytes == null) return;
    Uint8List image = imageBytes!.buffer.asUint8List(); // ByteData to List<int>
    var millisecondsSinceEpoch = TimeTask.instance.getNowDateTime().millisecondsSinceEpoch;
    var path = appTempAbsolutePath("${L.app_name.tr}-${L.share.tr}-$millisecondsSinceEpoch.png");
    if(path == null) return;
    final file = File(path);
    await file.writeAsBytes(image); // Write to temp file      
    Share.shareXFiles([XFile(path)]);

  }

  void getInviteCode() async {
    var readInviteCode = _appConfigService.readInviteCode();
    if (readInviteCode.isEmpty) {
      var userNameWithoutDomain = _appConfigService.getUserNameWithoutDomain();
      readInviteCode =
          (await Get.find<InviteApi>().getSharePoster(userNameWithoutDomain))
                  .data
                  ?.data
                  ?.inviteCode ??
              "";
    }
    inviteCode.value = readInviteCode;
  }
}