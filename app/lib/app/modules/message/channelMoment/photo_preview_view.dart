
import 'dart:io';

import 'package:extended_image/extended_image.dart';
import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:photo_view/photo_view.dart';
import 'package:photo_view/photo_view_gallery.dart';

class PhotoPreviewView extends StatefulWidget {
  const PhotoPreviewView({super.key, required this.galleryItems, required this.selectedIndex,});
  final List<GalleryItem> galleryItems;
  final int selectedIndex;
  
  @override
  State<PhotoPreviewView> createState() => _PhotoPreviewViewState();
}

class _PhotoPreviewViewState extends State<PhotoPreviewView> {
  late PageController _pageController;

  @override
  void initState() {
    _pageController = PageController(initialPage: widget.selectedIndex);
    super.initState();
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: (){
        Get.back();
      },
      child: SizedBox.expand(
        child: Container(
          color: Colors.black,
          child: PhotoViewGallery.builder(
            scrollPhysics: const BouncingScrollPhysics(),
            builder: (BuildContext context, int index) {
              return PhotoViewGalleryPageOptions(
                imageProvider: widget.galleryItems[index].imageUrl != null
                  ? ExtendedNetworkImageProvider(widget.galleryItems[index].imageUrl!, cache: true)
                  : FileImage(widget.galleryItems[index].imageFile!) as ImageProvider,
                initialScale: PhotoViewComputedScale.contained * 1,
                heroAttributes: PhotoViewHeroAttributes(tag: widget.galleryItems[index].heroTag),
                minScale: PhotoViewComputedScale.contained * 0.6,
                maxScale: PhotoViewComputedScale.contained * 2,
              );
            },
            itemCount: widget.galleryItems.length,
            // loadingBuilder: (context, event) => Center(
            //   child: Container(
            //     width: 20.0,
            //     height: 20.0,
            //     child: CircularProgressIndicator(
            //       value: event == null
            //           ? 0
            //           : event.cumulativeBytesLoaded / event.expectedTotalBytes,
            //     ),
            //   ),
            // ),
            // backgroundDecoration: widget.backgroundDecoration,
            pageController: _pageController,
            // onPageChanged: onPageChanged,
          )
        ),
      ),
    );
  }
}

class GalleryItem {
  final String heroTag;
  final String? imageUrl;
  final File? imageFile;

  GalleryItem({required this.heroTag, this.imageUrl, this.imageFile});
}