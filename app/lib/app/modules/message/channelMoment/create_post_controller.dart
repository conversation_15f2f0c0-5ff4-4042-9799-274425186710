import 'dart:io';
import 'dart:typed_data';

import 'package:easy_debounce/easy_debounce.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:flutter_metatel/app/data/models/pre_sign_model.dart';
import 'package:flutter_metatel/app/data/providers/api/channel.dart';
import 'package:flutter_metatel/app/modules/message/channelMoment/components/post_widget.dart';
import 'package:flutter_metatel/app/modules/message/channelMoment/video_player_view.dart';
import 'package:flutter_metatel/core/languages/l.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:flutter_metatel/core/utils/util.dart';
import 'package:flutter_metatel/core/values/colors.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:validators/validators.dart';

import '../../../../r.dart';
import '../../../data/models/post.dart';
import 'channel_moment_controller.dart';

enum MediaType { image, video }

class CreatePostController extends GetxController {
  /// 文件选择器
  final ImagePicker _imagePicker = ImagePicker();
  TextEditingController textEditingController = TextEditingController();
  ChannelMomentController _channelMomentController = Get.find<ChannelMomentController>();
  Rxn<Post> post = Rxn<Post>(); // 只有编辑帖，才会有
  String content = '';
  List<String> links = [];
  RxList<String> imageUrls = <String>[].obs; // 编辑时的图片url
  RxList<XFile> selectedImages = <XFile>[].obs; // 用户选择的图片列表
  RxString videoUrl = ''.obs; // 编辑时的视频url
  Rxn<XFile> selectedVideo = Rxn<XFile>(); // 用户选择的视频
  String? videoThumbnailUrl; // 编辑时和保存后
  Uint8List? selectedVideoThumbnail; // 用户选择视频时的缩略图
  double videoThumbnailRatio = 1; // 缩略图的比例
  RxBool isGettingThumbnail = false.obs;
  String searchText = ''; // 通过url搜索video时，搜索的text;

  @override
  void onInit() {
    if (Get.arguments != null) {
      post.value = Get.arguments['post'] ?? Rxn<Post>().value; // 只有编辑帖，才会有
    }
    if (post.value != null) {
      content = post.value?.content ?? '';
      textEditingController.text = content;
      imageUrls.assignAll(post.value?.imageUrls ?? []);
      if(post.value!.videoUrl != null && post.value!.videoUrl!.isNotEmpty) {
        videoUrl.value = post.value!.videoUrl!;
        videoThumbnailUrl = post.value!.videoThumbnail;
      }      
    }
    super.onInit();
  }

  void onCreatePostTap() async {
    if (!checkIsPostValid().value) return;
    EasyLoading.show(maskType: EasyLoadingMaskType.black, status: L.uploading.tr);
    try {
      List<String> _finalImageUrls = []; // 最后传给接口的图片Url列表 = 1. 用户选择的图片(转成链接后) + 2. 编辑时的图片url
      String _finalVideoUrl = ''; // 最后传给接口的视频Url
      // 对 Content进行处理
      if (content.isNotEmpty) {
        List<String> rawUrls = _extractUrlFromText(content);      
        List<String> validUrls = _validateUrl(rawUrls);      
        links.assignAll(validUrls);
      }      
      // 对照片进行处理
      _finalImageUrls.clear();
      if (imageUrls.isNotEmpty) {
        _finalImageUrls.addAll(imageUrls);
      }
      if (selectedImages.isNotEmpty) {
        // 存入Server, 获取Url
        _finalImageUrls.addAll(await _storeImagesToServer(selectedImages));      
      }
      // 对视频进行处理
      if(videoUrl.value.isNotEmpty){
        _finalVideoUrl = videoUrl.value;
        if(selectedVideoThumbnail != null){
          videoThumbnailUrl = await _storeVideoThumbnailToServer(selectedVideoThumbnail!);
        }        
      } else if(selectedVideo.value != null) {
        List<String> results = await Future.wait([
          _storeVideoToServer(selectedVideo),
          _storeVideoThumbnailToServer(selectedVideoThumbnail!)
        ]);
        _finalVideoUrl = results[0];
        videoThumbnailUrl = results[1];
      }
      // 调API接口
      bool? success = await _addPost(
        postId: post.value != null ? post.value?.id : null,
        content: content,
        links: links,
        imageUrls: _finalImageUrls,
        videoUrl: _finalVideoUrl,
        videoThumbnail: videoThumbnailUrl
      );
      if (!(success ?? false)) {
        EasyLoading.showError(L.failed.tr);
        return;
      }
      EasyLoading.showSuccess(L.success.tr);
      final Post result = Post(post.value?.id,content,links, _finalImageUrls, _finalVideoUrl, videoThumbnailUrl);  // 编辑成功，返回数据，以便更新
      Get.back(result: result);
    } catch (e) {
      EasyLoading.showError(L.failed.tr);
      AppLogger.e("Error Catch<onCreatePostTap>: $e");
    }    
  }

  void onSelectMediaTap(BuildContext context) {
    if(isGettingThumbnail.value) return;
    if((selectedImages.isEmpty && imageUrls.isEmpty) && (selectedVideo.value==null && videoUrl.isEmpty)){
      List<Widget> list = [
        getBottomSheetItemSimpleAndImg(
          context,
          L.select_image.tr,
          imagePath: R.icChatPictures,
          itemCallBack: () => {
            _openImageGallery(),
          },
        ),
        Container(
          height: 1.h,
          color: AppColors.backgroundGray,
        ),
        getBottomSheetItemSimpleAndImg(
          context,
          L.select_video.tr,
          imagePath: R.icChatVideo,
          itemCallBack: () => {
            _openVideoGallery(),
          },
        ),
        Container(
          height: 1.h,
          color: AppColors.backgroundGray,
        ),
        getBottomSheetItemSimpleAndImg(
          context,
          L.video_url.tr,
          imagePath: R.icChatVideoLink,
          itemCallBack: () => {
            _openAddVideoFromUrl(),        
          },
        ),
        Container(
          height: 10.h,
          color: AppColors.colorFFF3F3F3,
        )
      ];
      showBottomDialogCommonWithCancel(context, widgets: list);
    } else if(selectedImages.isNotEmpty || imageUrls.isNotEmpty) {
      _openImageGallery();
    } else {
      List<Widget> list = [
        getBottomSheetItemSimpleAndImg(
          context,
          L.select_video.tr,
          imagePath: R.icChatVideo,
          itemCallBack: () => {
            _openVideoGallery(),
          },
        ),
        Container(
          height: 1.h,
          color: AppColors.backgroundGray,
        ),
        getBottomSheetItemSimpleAndImg(
          context,
          L.video_url.tr,
          imagePath: R.icChatVideo,
          itemCallBack: () => {
            _openAddVideoFromUrl(),        
          },
        ),
        Container(
          height: 10.h,
          color: AppColors.colorFFF3F3F3,
        )
      ];
      showBottomDialogCommonWithCancel(context, widgets: list);
    }  
  }

  void onRemoveSelectedImageTap(int index) {
    selectedImages.removeAt(index);
  }

  void onRemoveImageUrlTap(int index) {
    imageUrls.removeAt(index);
  }

  void onRemoveVideoTap() {
    selectedVideo.value = null;
    selectedVideoThumbnail = null;
  }

  void onRemoveVideoUrlTap() {
    videoUrl.value = '';
    videoThumbnailUrl = null;
  }

  void onVideoTap({XFile? xfile, String? url}) {
    if(xfile != null){
      Get.to(() =>  VideoPlayerView(filePath: xfile.path));
    }
    if(url != null){
      Get.to(() =>  VideoPlayerView(url: url));
    }
  }

  RxBool checkIsPostValid() {
    // 是否能提交Post
    return (content.isNotEmpty || selectedImages.isNotEmpty || imageUrls.isNotEmpty || selectedVideo.value != null || videoUrl.value.isNotEmpty).obs;
  }

  List<XFile> _checkInvalidImage(List<XFile> oriImages) {
    var maxFileSizeInBytes = 25 * 1024 * 1024;  // 25mb
    List<XFile> invalidImages = [];  // images exceeded max file size
    for(var image in oriImages){
      final bytes = File(image.path).readAsBytesSync().lengthInBytes;
      if(bytes > maxFileSizeInBytes) {  // image excceeded
        invalidImages.add(image); 
      }
    }
    return invalidImages;
  }

  bool _checkInvalidVideo(XFile oriVideo) {
    var maxFileSizeInBytes = 250 * 1024 * 1024;  // 250mb
    final bytes = File(oriVideo.path).readAsBytesSync().lengthInBytes;
    if(bytes > maxFileSizeInBytes) {  
      return false; // 超过最大限制
    }
    return true;
  }

  Future<XFile> _compressImage(XFile file) async {
    if(File(file.path).readAsBytesSync().lengthInBytes <= (100 * 1024)){
      // 若图片大小 <= 100kb, 不进行压缩
      return file;
    }
    var tempPath = appTempAbsolutePath("${L.app_name.tr}-${L.share.tr}-${DateTime.now().microsecondsSinceEpoch}.jpg");
    if(tempPath == null) {
      throw '<_compressImage> tempPath is null';
    }
    var result = await FlutterImageCompress.compressAndGetFile(
      file.path, 
      tempPath,
      quality: 70,
    );
    if(result == null){
      throw '<_compressImage> image compress failed';
    }
    return result;
  }

  // Future<File> _compressVideo(XFile file) async {
  //   if(File(file.path).readAsBytesSync().lengthInBytes <= (15 * 1024 * 1024)){
  //     // 若视频大小 <= 15mb, 不进行压缩
  //     return File(file.path);
  //   }   
  //   var result = await VideoCompress.compressVideo(
  //     file.path,
  //     quality: VideoQuality.LowQuality,
  //     deleteOrigin: false,
  //   );
  //   if(result == null){
  //     throw '<_compressVideo> video compress failed';
  //   } 
  //   return result.file!;
  // }

  // 没使用直存OSS
  // Future<List<String>> _storeImagesToOss(RxList<XFile> images) async {
  //   List<String> uploadedImages = [];
  //   for(var image in images) {      
  //     uploadedImages.add(
  //       await OssProvider().upload('moment_post/${DateTime.now().microsecondsSinceEpoch}_${_generateUuid()}', image.path)
  //     );
  //   }
  //   return uploadedImages;
  // }

  Future<PreSignModel> _getPreSignUrl({required fileName}) async {
    try {
      PreSignModel? preSignModel = await ChannelMomentApi.getPreSignUrl(
        headers: _channelMomentController.apiHeaders(),
        fileName: fileName,
      );
      if (preSignModel == null) {
        throw "Get pre-sign url failed";
      }
      return preSignModel;
    } catch (e) {    
      AppLogger.e("Error Catch<_getPreSignUrl>: $e");
      throw e;
    }
  }

  Future<List<String>> _storeImagesToServer(RxList<XFile> images) async {
    try {
      List<String> uploadedImages = [];
      EasyLoading.showProgress(0, maskType: EasyLoadingMaskType.black, status: "${uploadedImages.length}/${images.length}");
      for (var image in images) {
        image = await _compressImage(image);
        // 创建文件名
        String fileName = "${L.app_name.tr}_moment_image_${DateTime.now().microsecondsSinceEpoch}_${image.path.split('/').last}";
        // 获取预签名链接
        PreSignModel preSignModel = await _getPreSignUrl(fileName: fileName);
        if(preSignModel.signUrl == null) throw "signUrl is null";
        if(preSignModel.targetUrl == null) throw "targetUrl is null";
         // 调接口，存入File
        bool? isSuccess = await ChannelMomentApi.storeMediaViaPreSignUrl(
          url: preSignModel.signUrl!, 
          file: File(image.path),
        );
        if (!(isSuccess ?? false)) {
          throw "Upload failed";
        }
        uploadedImages.add(preSignModel.targetUrl!);
        EasyLoading.showProgress((uploadedImages.length/images.length).toDouble(), maskType: EasyLoadingMaskType.black, status: "${uploadedImages.length}/${images.length}");
      }
      EasyLoading.show(maskType: EasyLoadingMaskType.black);
      return uploadedImages;      
    } catch (e) {
      toast(L.image_upload_failed.tr);
      AppLogger.e("Error Catch<_storeImagesToServer>: $e");
      throw e; 
    }    
  }

  Future<String> _storeVideoThumbnailToServer(Uint8List thumbnail) async {
    try {
      // 创建path
      var tempPath = appTempAbsolutePath("${L.app_name.tr}_moment_vthumbnail_${DateTime.now().microsecondsSinceEpoch}.jpg");
      if(tempPath == null) throw "tempPath is null";
      // 获取预签名链接
      PreSignModel preSignModel = await _getPreSignUrl(fileName: tempPath.split("/").last);
      if(preSignModel.signUrl == null) throw "signUrl is null";
      if(preSignModel.targetUrl == null) throw "targetUrl is null";
      // 写入File
      File file = File(tempPath);
      file.writeAsBytesSync(thumbnail);
      // 调接口，存入File
      bool? isSuccess = await ChannelMomentApi.storeMediaViaPreSignUrl(url: preSignModel.signUrl!, file: file);
      if (!(isSuccess ?? false)) {
        throw "Upload failed";
      }
      return preSignModel.targetUrl!;      
    } catch (e) {
      AppLogger.e("Error Catch<_storeVideoThumbnailToServer>: $e");
      throw e;
    }    
  }

  Future<String> _storeVideoToServer(Rxn<XFile> video) async {
    try {
      if(video.value == null) throw "Video is null";
      // 压缩视频
      // compressedVideo ??= await _compressVideo(video.value!);      
      // if(compressedVideo == null) throw "Compressed video is null";
      // 创建文件名
      String fileName = "${L.app_name.tr}_moment_video_${DateTime.now().microsecondsSinceEpoch}_${video.value!.path.split('/').last}";
      // 获取预签名链接
      PreSignModel preSignModel = await _getPreSignUrl(fileName: fileName);
      if(preSignModel.signUrl == null) throw "signUrl is null";
      if(preSignModel.targetUrl == null) throw "targetUrl is null";
      // 调接口，存入File
      bool? isSuccess = await ChannelMomentApi.storeMediaViaPreSignUrl(
        url: preSignModel.signUrl!, 
        file: File(video.value!.path),
        onSendProgress: (current, total) {
          double progress = _calculateUploadProgress(current: current, total: total);
          String progressInPercent = "${(progress * 100)}".split(".")[0]; 
          if(progress != 1.0) {
            EasyLoading.showProgress(progress, maskType: EasyLoadingMaskType.black, status: "$progressInPercent %");
          } else {
            EasyLoading.show(maskType: EasyLoadingMaskType.black);
          }        
        },
      );
      if (!(isSuccess ?? false)) {
        throw "Upload failed";
      }
      return preSignModel.targetUrl!;      
    } catch (e) {
      toast(L.video_upload_failed.tr);
      AppLogger.e("Error Catch<_storeVideoToServer>: $e");
      throw e;
    }    
  }

  Future<bool?> _addPost(
      {String? postId,
      String? content,
      List<String>? links,
      List<String>? imageUrls,
      String? videoUrl,
      String? videoThumbnail}) async {
    try {
      return await ChannelMomentApi.addPost(
        headers: _channelMomentController.apiHeaders(), 
        postId: postId, 
        content: content,
        links: links, 
        imageUrls: imageUrls, 
        videoUrl: videoUrl,
        videoThumbnail: videoThumbnail,
      );
    } catch (e) {
      AppLogger.e("Error Catch <_addPost>: $e");
      return null;
    }
  }

  // String _generateUuid() {
  //   var uuid = Uuid();
  //   return uuid.v4();
  // }

  void _openImageGallery() async {
    List<XFile> newImages = await _imagePicker.pickMultiImage();
    if(newImages.isEmpty) return;
    List<XFile> invalidImages = _checkInvalidImage(newImages);  // 超過最大文件限制的图片    
    if(invalidImages.isNotEmpty){
      toast("${invalidImages.length} ${L.exceed_file_size.tr} 25mb");
      for(var image in invalidImages){
        newImages.remove(image);
      }
    }
    if (selectedImages.length + imageUrls.length + newImages.length > 9) {  // Maximum 9张图片      
      toast(L.maximum_nine_images.tr);
      return;
    }
    _clearMedia(MediaType.video);
    selectedImages.addAll(newImages);
  }

  void _openVideoGallery() async {
    XFile? newVideo = await _imagePicker.pickVideo(source: ImageSource.gallery);
    if(newVideo == null) return;
    bool isValid = _checkInvalidVideo(newVideo);
    if(!isValid) {
      toast("${L.video_exceed_file_size.tr} 250mb");
      return;
    }    
    var (thumbnail, ratio) = await _getVideoThumbnail(file: newVideo); 
    if(thumbnail == null) return;   
    _clearMedia(MediaType.image);
    selectedVideo.value = newVideo;
    selectedVideoThumbnail = thumbnail;
    videoThumbnailRatio = ratio;
  }

  void _clearMedia(MediaType type) {
    if(type == MediaType.image) {
      imageUrls.clear();
      selectedImages.clear();
    }
    if(type == MediaType.video) {
      videoUrl.value = '';
      videoThumbnailUrl = null;
      selectedVideo.value = null;
      selectedVideoThumbnail = null;
      videoThumbnailRatio = 1;
    }    
  }

  Future<(Uint8List?,double)> _getVideoThumbnail({XFile? file, String? url}) async {
    try {
      isGettingThumbnail.value = true;
      EasyLoading.show(maskType: EasyLoadingMaskType.black, status: L.processing.tr);
      Uint8List? thumbnail = await PostUtil.getVideoThumbnail(file != null ? file.path : url!);
      double ratio = 1;
      if(thumbnail != null){ // 查缩略图的比例
        final decodedImage = await decodeImageFromList(thumbnail);
        final width = decodedImage.width;
        final height = decodedImage.height;
        ratio = width/height;
      }
      EasyLoading.dismiss();
      isGettingThumbnail.value = false;
      return (thumbnail, ratio);
    } catch (e) {
      EasyLoading.dismiss();
      isGettingThumbnail.value = false;
      toast(L.video_processing_failed.tr);
      AppLogger.e("Error Catch<_getVideoThumbnail>: $e");
      return (null,1.0);
    }
  }

  Future<void> _openAddVideoFromUrl() async {
    await Get.bottomSheet(
      Container(
        constraints: BoxConstraints(maxHeight: 800.r, minHeight: 90.r, minWidth: 1.sw),
        padding: EdgeInsets.symmetric(horizontal: 14.r, vertical: 16.r),
        decoration: BoxDecoration(
          color: AppColors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(15.r),
            topRight: Radius.circular(15.r),
          ),
        ),
        child: TextField(
          enabled: !isGettingThumbnail.value,
          autofocus: true,
          style: TextStyle(fontSize: 16.sp),
          onChanged: (text) => _onTextChange(text),          
          textAlign: TextAlign.start,
          textAlignVertical: TextAlignVertical.center,
          decoration: InputDecoration(
            hintText: L.searbar_hint_search.tr,
            contentPadding: EdgeInsets.zero,
            prefixIcon: const Icon(
              Icons.search,
              color: Color.fromARGB(255, 89, 90, 90),
            ),
            filled: true,
            fillColor: const Color(0xffF7F7F7),
            border: const OutlineInputBorder(
              borderSide: BorderSide.none,
              borderRadius: BorderRadius.all(Radius.circular(25)),
            ),
            suffixIcon: GestureDetector(
              onTap: () {
                _addVideoFromUrl(searchText);
              },               
              child: Padding(
                padding: EdgeInsets.only(right: 10.r),
                child: Icon(
                  Icons.check_rounded, 
                  color: AppColors.colorFF249ED9,
                ),
              ),
            ),          
          ),
        ),
      ),
    );
  }

  void  _onTextChange(String text) {
    searchText = text;
    EasyDebounce.debounce(  
      'search-video-url',                 
      Duration(milliseconds: 700),    
      () {
        _addVideoFromUrl(searchText);
      }              
    );
  }

  void _addVideoFromUrl(String text) async {
    if(text.isEmpty) return;
    var (thumbnail,ratio) =  await _getVideoThumbnail(url: text);
    if(thumbnail == null) return;
    _clearMedia(MediaType.image);
    selectedVideoThumbnail = thumbnail;
    videoThumbnailRatio = ratio;
    videoUrl.value = text;
    Get.back();
  }



  // Future<void> _showMediaSelectionBottomSheet({Function()? onSelectImageTap, Function()? onSelectVideoTap}) async {
  //   await Get.bottomSheet(
  //     Container(
  //       decoration: BoxDecoration(
  //         color: AppColors.white,
  //         borderRadius: BorderRadius.only(
  //           topLeft: Radius.circular(20.r),
  //           topRight: Radius.circular(20.r),
  //         ),
  //       ),
  //       child: Padding(
  //         padding: EdgeInsets.all(20.r),
  //         child: Column(
  //           mainAxisSize: MainAxisSize.min,
  //           children: <Widget>[
  //             TextButton(
  //               onPressed: onSelectImageTap, 
  //               child: Row(
  //                 mainAxisAlignment: MainAxisAlignment.center,
  //                 children: [
  //                   Icon(Icons.photo_library),
  //                   SizedBox(width: 10.r),
  //                   Text(L.select_image.tr),
  //                 ],
  //               )
  //             ),
  //             Divider(),
  //             TextButton(
  //               onPressed: onSelectVideoTap, 
  //               child: Row(
  //                 mainAxisAlignment: MainAxisAlignment.center,
  //                 children: [
  //                   Icon(Icons.video_collection),
  //                   SizedBox(width: 10.r),
  //                   Text(L.select_video.tr),
  //                 ],
  //               )
  //             ),
             
  //           ],
  //         ),
  //       ),
  //     ),
  //   );
  // }

  List<String> _extractUrlFromText(String text) {
    List<String> urls = [];
    RegExp exp = new RegExp(r'(?:(?:https?):\/\/)?[\w/\-?=%.]+\.[\w/\-?=%.#]+');
    
    Iterable<RegExpMatch> matches = exp.allMatches(text);
    if (matches.isEmpty) {
      return urls;
    }
    matches.forEach((match) {
      urls.add(text.substring(match.start, match.end));
    });
    return urls;
  }

  List<String> _validateUrl(List<String> rawUrls) {
    List<String> validUrls = [];
    rawUrls.forEach((rawUrl) {             
      if (isURL(rawUrl, protocols: ['http', 'https'])) {
        validUrls.add(rawUrl);
      }
    });
    return validUrls;
  }

  double _calculateUploadProgress({required int current, required int total}) {
    return current / total;
  }
}
