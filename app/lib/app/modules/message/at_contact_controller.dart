//FileName at_contact_controller
// <AUTHOR>
//@Date 2022/7/12 15:56
import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/data/enums/enum.dart';
import 'package:flutter_metatel/app/data/providers/api/channel.dart';
import 'package:flutter_metatel/core/utils/util.dart';
import 'package:get/get.dart';

import '../../../core/languages/l.dart';
import '../../../core/utils/app_log.dart';
import '../../../core/values/config.dart';
import '../../data/models/select_contact_model.dart';
import '../../data/providers/db/database.dart';
import '../../data/services/config_service.dart';
import '../../widgets/at_widget/at_text.dart';
import '../../widgets/select_contact_page.dart';

class AtContactController {
  AtContactController(this.onSelectedContacts);

  List<ContactInfo> atSelectContacts = <ContactInfo>[];
  Map<String,String>atUsers=<String,String>{};

  final ValueChanged<String>? onSelectedContacts;

  void cleanAtContact(){
    atSelectContacts.clear();
    atUsers.clear();
  }

  void atContactList(String groupId, BuildContext context) async {
    AppDatabase db = Get.find();
    List<String> value;
    var userName = await Get.find<AppConfigService>().getUserName();
    if (userName == null || userName.isEmpty || groupId.isEmpty) {
      return;
    }
    value = await db.oneGroupMemberExcludeUser(groupId, userName).get();
    var dbResults = await db.memberContactDatas(groupId, value).get();
    if (value.isEmpty) {
      return;
    }
    int? role=await db.groupMemberRole(groupId, userName).getSingleOrNull();
    List<ContactData> atContact = <ContactData>[];
    bool isAdmin= role != null?role>0 :false;
    for (var v in dbResults) {
      String? nName = v.displayname;
      // if (v.nickname?.isEmpty ?? true) {
      //   if (v.localname?.isEmpty ?? true) {
      //   } else {
      //     nName = v.localname;
      //   }
      // } else {
      //   nName = v.nickname;
      // }
      atContact.add(ContactData(id: 0, username: v.username,displayname: nName, localname: nName,avatarPath: v.avatarPath));
    }
    if(isAdmin){
      atContact.insert(0, ContactData(id: 0, username: Config.atAll,localname: L.all_member.tr, displayname: L.all_member.tr));
    }
    var data = await showDialog(
        context: context,
        builder: (_) {
          return SelectContactPage(
              type: SelectContactType.multiple,
              contactDatas: atContact,
              onSelectedContacts: (info) {});
        });
    if (data != null) {
      if (data.runtimeType == SelectContactInfo) {
        atSelectContacts.addAll(data.contacts);
        _selectContactForAt(data.contacts);
      }
    }
  }
  void _selectContactForAt(List<ContactInfo> info) {
    if (info.isNotEmpty) {
      StringBuffer str = StringBuffer();
      StringBuffer s;
      for (ContactInfo bean in info) {
        s = StringBuffer();
        s..write('@')..write(bean.displayName??displayNameProcess(bean.displayName??"", bean.userName))..write(AtText.flagEnd)..write(
            ' ');
        atUsers[s.toString()]=bean.userName;
        str.write(s);
      }
      onSelectedContacts?.call(str.toString());

    }
  }

  Future<bool> getMemberRequest(String groupId, String from, String channelOwner,
      List<String> channelAdminList) async {
    var info = await memberInfoRequest(
        groupId, Get.find<AppConfigService>().getUserName() ?? "", [from]);
    if (info?.isNotEmpty ?? false) {
      var element = (info?[0])!;
      int role = ChannelMemberRole.ordinary.index;
      if (element.username?.isNotEmpty == true &&
          channelOwner == element.username) {
        role = ChannelMemberRole.owner.index;
      } else if (channelAdminList.contains(element.username) == true) {
        role = ChannelMemberRole.administrator.index;
      }
      var groupMemberCompanion = GroupMemberCompanion.insert(
        groupId: groupId,
        username: element.username ?? '',
        displayname: ofNullable(element.nickname),
        memberUuid: groupId + (element.username ?? ''),
        role: ofNullable(role),
        createTime:
            ofNullable(DateTime.now().millisecondsSinceEpoch.toDouble()),
        updateTime:
            ofNullable(DateTime.now().millisecondsSinceEpoch.toDouble()),
      );
      var db = Get.find<AppDatabase>();
      await db.insertOrUpdateMemberData([groupMemberCompanion]);
      return true;
    }
    return false;
  }

  Future<bool> insertAt(String groupId, String from, String channelOwner,
      List<String> channelAdminList) async {
    AppDatabase db = Get.find();
    List<OneGroupMemberByUserResult> value;
    var userName = Get.find<AppConfigService>().getUserName();
    if (userName == null ||
        userName.isEmpty ||
        groupId.isEmpty ||
        from == userName) {
      return false;
    }
    value = await db.oneGroupMemberByUser(groupId, from).get();
    if (value.isEmpty) {
      var bool = await getMemberRequest(groupId, from, channelOwner, channelAdminList);
      if(bool){
        insertAt(groupId, from, channelOwner, channelAdminList);
      }
      AppLogger.d('Channel member is not exits local !!!');
      return false;
    }
    List<String> users = [];
    for (var e in value) {
      users.add(e.username);
    }
    var dbResults = await db.memberContactDatas(groupId, users).get();
    List<ContactInfo> atContact = <ContactInfo>[];
    for (var v in dbResults) {
      String? nName = v.displayname;
      String? groupDisplayName;
      try {
        OneGroupMemberByUserResult? result =
            value.firstWhere((element) => v.username == element.username);
        if (result.displayname?.isNotEmpty ?? false) {
          groupDisplayName = result.displayname;
        }
      } catch (e) {}
      // if (v.nickname?.isEmpty ?? true) {
      //   if (v.localname?.isEmpty ?? true) {
      //   } else {
      //     nName = v.localname;
      //   }
      // } else {
      //   nName = v.nickname;
      // }
      if (nName?.isEmpty ?? true) {
        nName = 'u${v.username.substring(0, 6)}';
      }
      AppLogger.d('displayName =$groupDisplayName nName=$nName');
      atContact.add(ContactInfo(
          userName: v.username,
          displayName: groupDisplayName ?? nName,
          localName: groupDisplayName ?? nName,
          avatarPath: v.avatarPath));
    }
    _selectContactForAt(atContact);
    return true;
  }

  List<String>? getAtUsers(String data){
    Set<String>? list=<String>{};
    if(data.isNotEmpty){
      List<String> arrays=data.split(AtText.flagEnd);
      if(arrays.isNotEmpty){
        for(String name in arrays){
          atUsers.forEach((key, value) {
            key=key.trim();
            if(name.contains(key)){
              list.add(value);
              //name.replaceAll(key, value);
            }
            AppLogger.d('getAtUsers name${name}');
            AppLogger.d('getAtUsers value${name}');
          });
        }
        data= const JsonEncoder().convert(arrays);
      }
    }
    AppLogger.d('getAtUsers ${list.toString()}');
    return list.toList();
  }
  void insertAtText(String text,TextEditingController _textEditingController) {
    TextEditingValue value = _textEditingController.value;
    int start = value.selection.baseOffset;
    int end = value.selection.extentOffset;
    String newText = '';
    if (value.selection.isValid) {
      var deleteAt=false;
      if (value.selection.isCollapsed) {
        if (end > 0) {
          newText = value.text.substring(0, end);
        }
        if(newText.endsWith('@')){
          newText=value.text.substring(0, end-1);
          deleteAt=true;
        }
        newText += text;
        if (value.text.length > end) {
          newText += value.text.substring(end, value.text.length);
        }
      } else {
        newText = value.text.replaceRange(start, end, text);
        end = start;
      }
      _textEditingController.value = value.copyWith(
          text: newText,
          selection: value.selection.copyWith(
              baseOffset: end + text.length-(deleteAt?1:0), extentOffset: end + text.length-(deleteAt?1:0)));
    } else {
      _textEditingController.value = TextEditingValue(
          text: text,
          selection:
          TextSelection.fromPosition(TextPosition(offset: text.length)));
    }
  }
}
