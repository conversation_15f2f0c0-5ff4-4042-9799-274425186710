// ignore_for_file: empty_catches

/*
 * <AUTHOR> <PERSON><PERSON>
 * @Date         : 2022-04-25 15:32:49
 * @Description  : 聊天消息控制器
 * @LastEditors  : <PERSON><PERSON>
 * @LastEditTime : 2022-08-01 14:13:35
 * @FilePath     : /flutter_metatel/lib/app/modules/message/message_controller.dart
 */
import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:async_task/async_task_extension.dart';
import 'package:easy_debounce/easy_debounce.dart';
import 'package:extended_image/extended_image.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_floating/floating/assist/floating_slide_type.dart';
import 'package:flutter_floating/floating/assist/slide_stop_type.dart';
import 'package:flutter_floating/floating/floating.dart';
import 'package:flutter_floating/floating_increment.dart';
import 'package:flutter_metatel/app/data/events/events.dart';
import 'package:flutter_metatel/app/data/models/channel_info_model_data.dart';
import 'package:flutter_metatel/app/data/models/cmd_model.dart';
import 'package:flutter_metatel/app/data/models/contact_card_model.dart';
import 'package:flutter_metatel/app/data/models/message_model.dart';
import 'package:flutter_metatel/app/data/services/config_service.dart';
import 'package:flutter_metatel/app/data/services/event_service.dart';
import 'package:flutter_metatel/app/modules/group/details/group_details_page.dart';
import 'package:flutter_metatel/app/modules/home/<USER>/session_controller.dart';
import 'package:flutter_metatel/app/modules/hongbao/hongbao_transfer_page.dart';
import 'package:flutter_metatel/app/modules/message/components/attachment_dialog.dart';
import 'package:flutter_metatel/app/modules/message/components/top_controller.dart';
import 'package:flutter_metatel/app/modules/message/file_help.dart';
import 'package:flutter_metatel/app/modules/message/link/link_model.dart';
import 'package:flutter_metatel/app/modules/message/link/link_parse_util.dart';
import 'package:flutter_metatel/app/modules/wallet/wallet_manage.dart';
import 'package:flutter_metatel/app/socket/socket_task.dart';
import 'package:flutter_metatel/core/hongbao/hongbao_view.dart';
import 'package:flutter_metatel/core/languages/l.dart';
import 'package:flutter_metatel/core/task/channel_option_task.dart';
import 'package:flutter_metatel/core/task/channel_task.dart';
import 'package:flutter_metatel/core/task/chat_task.dart';
import 'package:flutter_metatel/core/task/hongbao_task.dart';
import 'package:flutter_metatel/core/task/merge_message_task.dart';
import 'package:flutter_metatel/core/task/session_task.dart';
import 'package:flutter_metatel/core/task/time_task.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:flutter_metatel/core/utils/comm_util.dart';
import 'package:flutter_metatel/core/utils/events_bus.dart';
import 'package:flutter_metatel/core/utils/file_util.dart';
import 'package:flutter_metatel/core/utils/keboard_util.dart';
import 'package:flutter_metatel/core/utils/url_util.dart';
import 'package:flutter_metatel/core/utils/util.dart';
import 'package:flutter_metatel/core/values/config.dart';
import 'package:flutter_metatel/core/values/keys.dart';
import 'package:flutter_metatel/meeting/meeting_helper.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:flutter_web3/app/core/hw_wallet_manager.dart';
import 'package:flutter_web3/app/core/open_wallet_helper.dart';
import 'package:flutter_web3/app/core/utils/util.dart' as web3util;
import 'package:flutter_web3/app/data/enums/enum.dart';
import 'package:flutter_web3/app/db/database.dart' hide ofNullable;
import 'package:flutter_web3/app/modules/home/<USER>';
import 'package:flutter_web3/app/modules/wallet/ethereum/wallet_controller.dart';
import 'package:flutter_web3/web3dart/src/utils/quene_task.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:scrollable_positioned_list/scrollable_positioned_list.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:wechat_camera_picker/wechat_camera_picker.dart';

import '../../../core/task/announcement_task.dart';
import '../../../core/task/cmd_task.dart';
import '../../../core/task/message_task.dart';
import '../../../core/task/official_account_task.dart';
import '../../../core/utils/qr_util.dart';
import '../../../core/utils/translator_util.dart';
import '../../../core/utils/vibration_util.dart';
import '../../../core/values/code.dart';
import '../../../core/values/colors.dart';
import '../../../r.dart';
import '../../../routes/pages.dart';
import '../../../webrtc/core/webrtc_call_helper.dart';
import '../../data/models/channel_card_model.dart';
import '../../data/models/channel_operation_model.dart';
import '../../data/models/meeting.dart';
import '../../data/models/poll.dart';
import '../../data/models/replay_message_model.dart';
import '../../data/models/res/emoticon_res_model.dart';
import '../../data/models/role.dart';
import '../../data/models/select_contact_model.dart';
import '../../data/models/user_message_model.dart';
import '../../data/models/weak_image_bean.dart';
import '../../data/providers/api/channel.dart';
import '../../data/providers/db/database.dart';
import '../../data/providers/native/chatio/chatio_async.dart';
import '../../data/services/channel_service.dart';
import '../../data/services/chatio_service.dart';
import '../../data/services/down_loads_service.dart';
import '../../data/services/notification_service.dart';
import '../../data/services/push_service.dart';
import '../../widgets/chat_widget/photos_widget.dart';
import '../../widgets/voice_player/voice_player_new.dart';
import '../complaint/complaint_helper.dart';
import '../complaint/complaint_view.dart';
import '../group/other/announcement_detail_page.dart';
import '../group/other/modify_page.dart';
import '../home/<USER>/detail/contact_detail.dart';
import '../resentmessage/resent_message_page.dart';
import 'at_contact_controller.dart';
import 'components/reply_controller.dart';

enum RecordStatus {
  none,
  start,
  locked,
  play,
}

enum IMRecordStatus {
  none,
  record,
  cancelRecord,
}

Map<String, String> playAudioStateMap = {};

typedef VoidFutureFunction = Future<void> Function();

class MyTextEditingController extends TextEditingController {
  MyTextEditingController({String? text}) : super(text: text);

  /// 是否禁止输入
  bool _inputText = true;

  set inputText(value) => _inputText = value;

  @override
  set text(String newText) {
    if (!_inputText) {
      return;
    }
    super.text = newText;
  }

  @override
  set value(TextEditingValue newValue) {
    if (!_inputText) {
      return;
    }

    super.value = newValue;
  }
}

class MessageController extends FullLifeCycleController
    with GetSingleTickerProviderStateMixin, FullLifeCycleMixin {
  /// 滚动条
  late ItemScrollController _itemScrollController;

  var curBottomHeight = 0.0;

  get itemScrollController => _itemScrollController;

  ItemScrollController getItemScrollController() {
    _itemScrollController = ItemScrollController();
    return _itemScrollController;
  }

  final ItemPositionsListener _itemPositionsListener =
      ItemPositionsListener.create();

  ItemPositionsListener get itemPositionsListener => _itemPositionsListener;

  ///
  final RefreshController _refreshController =
      RefreshController(initialRefresh: false);

  get refreshController => _refreshController;

  /// 输入框
  final MyTextEditingController _textEditingControllerInput =
      MyTextEditingController();

  TextEditingController get textEditingControllerInput =>
      _textEditingControllerInput;

  /// 输入框内容
  final RxBool _text = false.obs;

  bool get isText => _text.value;
  Worker? worker, worker1, jumpWorker; // ,worker2;
  final _currentIndex = 0.obs;

  final List<String> _currentMessageId = [];
  RxInt _updateMsgId = 0.obs;

  set text(bool value) {
    _text.value = value;
  }

  /// 输入框焦点
  final FocusNode _focusNodeInput = FocusNode();

  FocusNode get focusNodeInput => _focusNodeInput;

  /// 消息列表
  final RxList<MessageEvent> _messageList = RxList<MessageEvent>();

  RxList<MessageEvent> get messageList => _messageList;

  /// 动画
  AnimationController? _ac;
  Animation<double>? lockerAnimation;
  var rxLockPos = (-50.0).obs;
  var rxRecordStatus = RecordStatus.none.obs;
  var rxLockOnTap = 0.obs;
  var rxRecordSend = false.obs;
  var rxShowJump = false.obs;
  var rxLoadMore = false.obs;
  var shrinkWrap = false.obs;
  RxList<MessageData> newMessageCount = RxList<MessageData>();

  AnimationController? get animationController => _ac;

  /// 当前会话对象
  Rx<UserMessage>? _currentUser;

  UserMessage? get currentUser => _currentUser?.value;
  RxInt? _memberCount;
  RxBool isFileHelper = false.obs;

  int? get memberCount => _memberCount?.value;

  /// 频道同步的消息ID
  String? _firstUUID;
  String? _lastUUID;
  String? _minUUID;
  TaskQueue _taskQueue = TaskQueue();

  // int _currentMsgCount=0;//非数据库查询到的消息个数（主要处理频道消息，更新频道消息）
  final List<String> _groupMember = List<String>.empty(growable: true);

  List<String> get groupMember => _groupMember;

  // String? _maxMessageID;

  /// 频道同步的消息定时器
  Timer? _timerChannel;

  //同步消息超时定时器
  Timer? _timerSyncMessage;

  /// 记录时间气泡
  final Map<DateTime, int> _dateChipMap = {};

  /// 用于输入字符超出限定字数提示的控制变量
  final _rxInputTextChange = ''.obs;
  final textMaxLength = 2000;
  AtContactController? _atContactController;
  ReplyController? _replyController;

  // EmojiController? _emojiController;
  // final GlobalKey<ExtendedTextFieldState> _textFieldKey =
  //     GlobalKey<ExtendedTextFieldState>();

  /// 请求个人信息消息控制器
  final Map<String, ChatOwnState> _ownDataMap = {};
  final Map<String, MemberInfo> _ownInfoMap = {};
  Timer? _muteTime;

  var fromNameTextColor = Colors.white.obs;
  final _imagesWeakMap = List<WeakImageBean>.empty(growable: true);

  List<WeakImageBean> get imagesWeakMap => _imagesWeakMap;
  final Map<String, double> _imgHeightMaps = <String, double>{};
  final Map<String, double> _imgWidthMaps = <String, double>{};

  Map<String, double> get imgHeightMaps => _imgHeightMaps;

  Map<String, double> get imgWidthMaps => _imgWidthMaps;

  var isMessageCanMultiSelect = false.obs; //消息列表是否多选
  var isComplaintChangedMsgs = false.obs; //举报选择多个消息
  RxList<MessageEvent> selectedMessageEventList =
      RxList<MessageEvent>(); //通过消息多选选中的消息

  int curPosition = 0;
  /// 投票的漂浮按钮
  Floating? floating;
  RxString voteNumber = ''.obs;

  MemberInfo? ownInfo(String? username) {
    if (username == null) return null;
    if (_ownInfoMap.containsKey(username)) return _ownInfoMap[username];
    return null;
  }

  /// 当前显示的项
  List<ItemPosition> _currentItemPositions = [];

  late AppDatabase _database;
  final List<StreamSubscription> _subscriptions = [];

  /// 是否跳转
  bool _jump = false;
  String? _lastSyncMessageUuid;

  ///壁纸ImageProvider
  var wallPaperPath = "".obs;

  var isSticker = false.obs;

  Timer? timerKeyboard;

  var stickerHeight = 0.0.obs;
  var containerFillBottomHeight = 0.0.obs;

  BuildContext? context;
  TabooType? tabooType = TabooType.none;
  RxBool isTabooType = false.obs;

  // 阅后即焚状态
  final rxSelfDestruct = false.obs;

  // 阅后即焚点击倒计时缓存
  final Map<String, Timer> _mapCountdown = {};

  late StreamSubscription<bool> keyboardSubscription;

  /// metatel 录音按钮状态
  final rxIsRecordIM = false.obs;
  final rxIMRecordStatus = IMRecordStatus.none.obs;
  final rxIMRecordCountdown = (-1).obs;

  /// 消息置顶控制
  final TopController _topController = TopController(false);
  final TopController _topControllerAdmin = TopController(true);

  TopController get topController => _topController;

  TopController get topControllerAdmin => _topControllerAdmin;
  bool isUnDoEditOnTapDown = false;
  KeyBoardUtil keyBoardUtil = KeyBoardUtil();

  /// 频道属性
  int? _channelAttribute;
  int _itemPositionIndex = 0;
  bool contactLimit = false;
  bool isNoticeAdmin = false;
  bool clearChatHistory = false;
  final _currentMessageEvt = [];
  RxInt _lastMessage = 0.obs;
  RxInt _jumpToCount = 0.obs;
  double keyBoardHeight = 0;
  bool isAdminOrOwner = false;

  final _currentLastMessageEvt = [];

  // MessageEvent? _currentActivePlayMsgEvent;

  ///频道管理员缓存
  List<String> channelAdminList = [];

  ///频道群主userName
  String channelOwner = "";
  var rxMySelfIsChannelAdminOrOwner = false.obs;

  /// 频道角色标签
  final Map<String, Role> _mapChannelTag = {};
  /// 频道成员标签
  final Map<String, List<String>> _mapChannelMemberTag = {};

  List<Poll> onGoingVotes = [];

  void setContext(BuildContext context) {
    this.context = context;
  }

  StreamSubscription? subscriptionTopAdmin;
  
  late SharedPreferences _sharedPref;
  Rx<PlaybackSpeed> playbackSpeed = PlaybackSpeed.normal.obs;

  @override
  void dispose() {
    AppLogger.d('dispose000000000000');
    _muteTime?.cancel();
    _clearImageCache();
    animationController?.dispose();
    super.dispose();
  }

  @override
  void onInit() async {
    super.onInit();
    _getVoicePlaybackSpeed();
    _itemPositionsListener.itemPositions
        .addListener(() => AppLogger.d('positions'));
    _database = Get.find<AppDatabase>();
    isNoticeAdmin = OfficialAccountTask.instance.getRole();

    /// 动画初始化
    _ac = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
    Get.find<NotificationService>().cancelNotification();

    lockerAnimation = Tween<double>(begin: rxLockPos.value, end: 100).animate(
      CurvedAnimation(
        parent: animationController!,
        curve: const Interval(0.2, 1, curve: Curves.easeIn),
      ),
    )..addListener(() {
        rxLockPos.value = lockerAnimation!.value;
      });

    StreamSubscription subscription;

    /// 接收控制类消息
    subscription = Get.find<EventBus>().on<CmdModel>().listen((event) {
      _cmdDataHandle(event);
    });
    _subscriptions.add(subscription);

    /// 接收《更新界面》数据处理
    subscription =
        Get.find<EventBus>().on<MessageEvent>().listen((event) async {
      if (event.owner != currentUser?.userName) {
        return;
      }
      if (event.replayMsg?.isNotEmpty ?? false) {
        var map = await getReplayInfo2(event.replayMsg, messages: messageList);
        event.replayFilePath = map['replayFilePath'];
        event.replayFileFragment = map['replayFileFragment'];
        event.replayMsgId = map['replayMsgId'];
      }

      _updateMessageItem(event);
    });
    _subscriptions.add(subscription);

    /// 接收消息
    subscription = Get.find<EventBus>().on<MessageRecvEvent>().listen((event) {
      if (event.messages.isEmpty) {
        return;
      }
      if (event.messages.first.owner != currentUser?.userName) {
        return;
      }
      AppLogger.d('MessageRecvEvent ${!event.isSync} ${!rxShowJump.value}');

      if (!rxShowJump.value && !event.isSync) {
        if (getChatType() == ChatType.channelChat) {
          _lastMessage++;
          _currentLastMessageEvt.add(event);
          if (rxLoadMore.value) {
            Future.delayed(const Duration(milliseconds: 500), () {
              rxLoadMore.value = false;
            });
          }
        } else {
          _messageDataHandle(event);
        }
      } else {
        _currentMessageEvt.add(event);
        for (var element in event.messages) {
          if (event.exist) {
            var uuid = element.uuid;
            var owner = element.owner;
            if (uuid != null && owner != null) {
              updateMaxUUID(uuid, owner);
            }
          } else {
            AppLogger.d('MessageRecvEvent 1');
            newMessageCount.add(element);
            newMessageCount
                .sort((a, b) => (b.time ?? 0).compareTo(a.time ?? 0));
          }
        }
        if (event.messages.length == 1 &&
            event.messages.first.uuid == _lastSyncMessageUuid) {
          clickNewMessage(delay: true);
        }
      }
    });
    _subscriptions.add(subscription);

    /// 消息转发事件
    subscription =
        Get.find<EventBus>().on<MessageForwardEvent>().listen((event) {
      if (event.data.owner != currentUser?.userName) {
        return;
      }
      _insertMessage([event.data]);
    });
    _subscriptions.add(subscription);

    /// 每次同步消息的最后一条消息
    subscription = Get.find<EventBus>().on<LastMessageEvent>().listen((event) {
      _lastSyncMessageUuid = event.id;
      AppLogger.d('_lastSyncMessageUuid get $_lastSyncMessageUuid');
    });
    _subscriptions.add(subscription);

    /// 重新获取数据库消息数据
    subscription =
        Get.find<EventBus>().on<ChannelMessageUndoEvent>().listen((event) {
      if (event.channelId != currentUser?.userName) {
        return;
      }
      for (var data in event.messages) {
        messageList.removeWhere((element) => element.msgId == data.msgId);
        newMessageCount.removeWhere((element) => element.msgId == data.msgId);
      }
    });
    _subscriptions.add(subscription);

    /// 消息删除事件
    subscription = Get.find<EventBus>().on<MessageDelEvent>().listen((event) {
      if (event.userName != currentUser?.userName) {
        return;
      }
      for (var data in event.messages) {
        messageList.removeWhere((element) => element.msgId == data.msgId);
      }
    });
    _subscriptions.add(subscription);

    /// 设置联系人
    if (Get.arguments != null || Get.arguments.runtimeType == UserMessage) {
      await _setCurrentUser(Get.arguments as UserMessage);
    }

    /// 清除聊天记录
    subscription =
        Get.find<EventBus>().on<ClearChatHistoryEvent>().listen((event) {
      AppLogger.d('event.userName =${event.userName}');
      AppLogger.d('event.userName =${_currentUser?.value.userName}');

      if (event.userName == _currentUser?.value.userName) {
        clearChatHistory = true;
        _reset();
      }
    });
    _subscriptions.add(subscription);

    /// 修改联系人
    subscription =
        Get.find<EventBus>().on<ContactDataUpdateEvent>().listen((event) {
      if (event.contactData.username == _currentUser?.value.userName) {
        AppLogger.d('ContactDataUpdateEvent server=${event.server}');
        Get.find<ChannelService>().getOwnInfo(event.contactData.username,
            server: event.server, callback: (value) {
          var defaultName = 'u${event.contactData.username.substring(0, 6)}';
          if (defaultName.startsWith(value.nickname ?? '')) {
            value.nickname = _currentUser?.value.displayName;
          }
          _updateContact(
            event.contactData.username,
            value.avatarPath,
            value.nickname,
            isFriend: (value.state == ContactState.friend.index),
            isTid: value.isTid,
          );
        });
      }
    });
    _subscriptions.add(subscription);

    /// 频道信息更新
    subscription = Get.find<EventBus>()
        .on<ChannelOrGroupInfoUpdateEvent>()
        .listen((event) {
      AppLogger.d('ChannelOrGroupInfoUpdateEvent');
      if (event.id == _currentUser?.value.userName) {
        _updateContact(
          event.id!,
          appSupporAbsolutePath(event.avatarPath),
          event.title,
          isGroupValid: event.invalid,
        );
        updateAnnouncement();
        _selectGroupMember(event.id!);
      }
    });
    _subscriptions.add(subscription);

    //更新群公告
    subscription =
        Get.find<EventBus>().on<UpdateAnnouncementEvent>().listen((event) {
      AppLogger.d('UpdateAnnouncementEvent');
      updateAnnouncement();
    });
    _subscriptions.add(subscription);

    /// 聊天背景更新
    subscription =
        Get.find<EventBus>().on<ChatBackgroundUpdateEvent>().listen((event) {
      setWallpaper();
    });
    _subscriptions.add(subscription);

    subscription =
        Get.find<EventBus>().on<ChannelOptionEvent>().listen((event) {
      AppLogger.d('memberInfoRequest=${event.channelId}');
      if (event.channelId == _currentUser?.value.userName) {
        _requestChannelInfo();
      }
    });
    _subscriptions.add(subscription);

    /// 防止循环跳转
    subscription = Get.find<EventBus>().on<UserMessage>().listen((event) {
      keyBoardUtil.cancel();
      _setCurrentUser(event);
    });
    _subscriptions.add(subscription);

    ///
    subscription = Get.find<EventBus>()
        .on<UpdateOneEmojiMsgByMsgIdEvent>()
        .listen((event) {
      var msgId = event.msgId;
      for (MessageEvent e in _messageList) {
        if (e.msgId == msgId) {
          e.hasShown = true;
        }
      }
      update([msgId]);
    });
    _subscriptions.add(subscription);

    subscription = Get.find<EventBus>()
        .on<UpdateOneVoiceMessageHasReadByMsgIdEvent>()
        .listen((event) {
      var msgId = event.msgId;
      for (MessageEvent e in _messageList) {
        if (e.msgId == msgId) {
          e.messageHasRead = true;
          break;
        }
      }
      update([msgId]);
    });
    _subscriptions.add(subscription);

    subscription = Get.find<EventBus>() //播放语音事件
        .on<AudioPlayEvent>()
        .listen((event) {
      var msgId = event.msgId;
      var isClick = event.isClick;
      AppLogger.d('AudioPlayEvent ${event.isClick} id=${event.msgId}');
      if (!isClick) {
        MessageEvent? e = _activePlayMessage(msgId);
        AppLogger.d('AudioPlayEvent ===${e?.fileState} id=${e?.msgId}');
        if (e?.fileState == FileState.successful) {
          e?.activePlay = true;
          update([e!.msgId]);
        }
      }
    });
    _subscriptions.add(subscription);

    itemPositionsListener.itemPositions.addListener(() {
      _currentItemPositions =
          itemPositionsListener.itemPositions.value.toList();
      // AppLogger.d('item ====${_currentItemPositions.length}');
      if (_currentItemPositions.isEmpty) {
        return;
      }
      // // 排序，index从小到大
      // listItem.sort((left, right) {
      //   return left.index.compareTo(right.index);
      // });
      var itemPosition = _currentItemPositions.first;
      var itemPositionLast = _currentItemPositions.last;
      // AppLogger.d('item ====itemPositionLast==${itemPositionLast.index}');
      var index = itemPosition.index;

      if (_itemPositionIndex == index) {
        return;
      }
      if (_itemPositionIndex < index && !isJump) {
        AppLogger.d('position===jump2');
        //方向向下拉
        if (index <= 15 && _currentIndex.value > index) {
          _currentIndex.value = 0;
        } else if (index > _currentIndex.value + 20) {
          _currentIndex.value = index;
        } else if (itemPositionLast.index >= messageList.length - 3) {
          _currentIndex.value++;
        }
        // AppLogger.d(
        //     'item ====index==$index ${messageList.length} _currentIndex=$_currentIndex');
      }
      _itemPositionIndex = index;
      //  &&
      // itemPosition.itemLeadingEdge >= 0
    });

    interval(
      _rxInputTextChange,
      (_) {
        if (_rxInputTextChange.value.length >= textMaxLength) {
          toast(L.input_max_length.trParams({'length': '$textMaxLength'}));
        }
      },
      time: const Duration(seconds: 1),
    );

    try {
      _textEditingControllerInput.addListener(back);
    } catch (e) {
      AppLogger.e('_textEditingControllerInput e =${e.toString()}');
    }

    subscription =
        Get.find<EventBus>().on<UpdateMsgByMsgIdEvent>().listen((event) {
      update([event.msgId]);
    });
    _subscriptions.add(subscription);

    subscription = Get.find<EventBus>().on<BlacklistEvent>().listen((event) {
      updateBlackMsg(event.isBlack);
    });
    _subscriptions.add(subscription);

    subscription =
        Get.find<EventBus>().on<ContactUpdateEvent>().listen((event) {
      topController.updateContactInfo(event.userName);
      _ownInfoMap[event.userName]?.nickname = event.name;
      _ownInfoMap[event.userName]?.avatarPath = event.avatar;
      // List<MessageEvent> updateMsgList=[];
      // // updateMsgList.addAll(_messageList.where((msg) {return msg.from==event.userName;}));
      // // for(MessageEvent e in updateMsgList){
      // //   _updateMessageItem(e,isFromDB:true);
      // // }
    });
    _subscriptions.add(subscription);

    /// 录音时,文本框禁止输入
    subscription = rxRecordStatus.listen((value) {
      if (value == RecordStatus.none) {
        _textEditingControllerInput.inputText = true;
      } else {
        _textEditingControllerInput.inputText = false;
      }
    });
    _subscriptions.add(subscription);

    ///更新emoji
    subscription =
        Get.find<EventBus>().on<UpdateEmoticonEvent>().listen((event) {
      AppLogger.d('UpdateEmoticonEvent ${event.list?.length}');
      if (event.userName == _currentUser?.value.userName) {
        updateEmoji(event.list);
      }
    });
    _subscriptions.add(subscription);

    /// 更新文件装态
    subscription = Get.find<EventBus>().on<UpdateFileState>().listen((event) {
      if (!event.mergeMsg) {
        updateDownFileState(event.event, event.updateUi);
      }
    });
    _subscriptions.add(subscription);

    ///解密失败可能需要重新下载
    subscription = Get.find<EventBus>().on<CipherErrorEvent>().listen((event) {
      cipherError(event.msgId, event.filePath);
    });
    _subscriptions.add(subscription);

    /// 头像更新
    subscription = Get.find<EventBus>().on<TaskData>().listen((event) {
      if (event.datas.isEmpty || event.own != _currentUser?.value.userName) {
        return;
      }

      List<String> messageIDs = [];
      for (var element in event.datas) {
        var username = element.username;
        if (_ownInfoMap.containsKey(username)) {
          _ownInfoMap[username]!.avatarPath = element.savePath;
        }

        for (var item in _currentItemPositions) {
          var message = messageList[item.index];
          if (message.from == username) {
            messageIDs.add(message.msgId);
          }
        }
      }

      update(messageIDs);
    });
    _subscriptions.add(subscription);

    /// 同步频道角色标签
    subscription = Get.find<EventBus>().on<SyncChannelTagEvent>().listen((event) {
      _storeMapChannelTag(event.roles);
    });

    /// 同步频道成员角色标签
    subscription = Get.find<EventBus>().on<SyncChannelMemberTagEvent>().listen((event) {
      syncChannelMemberTagFromDb();
    });
    _subscriptions.add(subscription);
  }

  void _getVoicePlaybackSpeed() async {
    _sharedPref = await SharedPreferences.getInstance();
    final savedSpeed = _sharedPref.getDouble(VOICE_PLAYBACK_SPEED);
    if (savedSpeed != null) {
      playbackSpeed.value = PlaybackSpeed.fromSpeed(savedSpeed);
    }
  }

  void changePlaybackSpeed(PlaybackSpeed playbackSpeed) {
    this.playbackSpeed.value = playbackSpeed;
    _saveSpeed(playbackSpeed);
  }

  Future<void> _saveSpeed(PlaybackSpeed playbackSpeed) async {
    EasyDebounce.debounce(
      'save-voice-playback-speed',                 
      Duration(milliseconds: 500),    
      () async {
        await _sharedPref.setDouble(VOICE_PLAYBACK_SPEED, playbackSpeed.speed);
      }              
    );
  }

  _initVoteFloating() {
    if(floating != null) return;
    floating = Floating(
      GestureDetector(
        onTap: onVotePress,
        child: Container(
          padding:EdgeInsets.only(left:13.r ,right: 13.r),
          height: 30.r,
          decoration: BoxDecoration(
            color: AppColors.colorFF41CC20,
            borderRadius: BorderRadius.only(
                topLeft: Radius.circular(20.r),
                bottomLeft: Radius.circular(20.r)),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Obx(
                () => Text(
                  voteNumber.value,
                  style: TextStyle(color: AppColors.white, fontSize: 14.sp),
                ),
              ),
              SizedBox(width: 6.r,),
              Image.asset(
                R.icVote,
                width: 16.r,
              )
            ],
          ),
        ),
      ),
      slideType: FloatingSlideType.onRightAndTop,
      isShowLog: false,
      slideTopHeight: 80,
      top: 0.5.sh,
      slideStopType: SlideStopType.slideStopRightType,
      slideBottomHeight: 60,
    );
    
  }

  void openVoteFloating() {
    var context = Get.context;
    if (context == null) return;
    floating?.open(context);
  }

  void closeVoteFloating() {
    floating?.close();
  }

  void showVoteFloating() {
    floating?.showFloating();
  }

  void hideVoteFloating() {
    floating?.hideFloating();
  }

  void onVotePress() {
    if(currentUser?.isGroupValid != true) return;
    Get.toNamed(Routes.PollView);
  }

  bool mySelfIsChannelAdminOrOwner() {
    var bool = mySelfIsChannelAdmin() || mySelfIsChannelOwner();
    rxMySelfIsChannelAdminOrOwner.value = bool;
    return bool;
  }

  mySelfIsChannelAdmin() {
    return channelAdminList
        .contains(Get.find<AppConfigService>().getUserName());
  }

  mySelfIsChannelOwner() {
    return channelOwner == Get.find<AppConfigService>().getUserName();
  }

  isChannelAdmin(String username) {
    return channelAdminList.contains(username);
  }

  isChannelOwner(String username) {
    return channelOwner == username;
  }

  String getUserName() {
    return currentUser?.userName ?? '';
  }

  back() {
    if (!isTabooType.value) {
      Get.find<AppConfigService>()
          .saveDraft(getUserName(), _textEditingControllerInput.text);
    }
  }

  void _createWorker() {
    worker?.dispose();
    worker = interval(
      _currentIndex,
      (_) {
        onLoad();
      },
      time: const Duration(milliseconds: 600),
      condition: () => true,
    );
    worker1?.dispose();
    worker1 = interval(
      _lastMessage,
      (_) {
        _addCurrentLastMessageEvtToList();
      },
      time: const Duration(milliseconds: 1000),
      condition: () => true,
    );

    jumpWorker?.dispose();
    jumpWorker = interval(
      _jumpToCount,
      (_) {
        _updateJumpState();
      },
      time: const Duration(milliseconds: 1000),
      condition: () => true,
    );
    // worker2?.dispose();
    // worker2 = interval(
    //   _updateMsgId,
    //   (_) {
    //     _freshUpdate();
    //   },
    //   time: const Duration(milliseconds: 1000),
    //   condition: () => true,
    // );
  }

  bool isJump = false;

  _updateJumpState() {
    AppLogger.d('_updateJumpState isJump=$isJump');
    if (isJump) {
      isJump = false;
    }
  }

  void cipherError(String msgId, String? imgPath) {
    try {
      var e = messageList.firstWhere((element) => msgId == element.msgId);
      var downThumbnail = false;
      if (e.filePath == imgPath) {
        e.filePath = null;
      } else if (e.thumbnailPath == imgPath) {
        e.thumbnailPath = null;
        downThumbnail = true;
      }
      _updateMessageItem(e, updateUi: true, isFromDB: true);
      Get.find<AppDatabase>().updateMessageData(
        MessageCompanion.insert(
          msgId: e.msgId,
          filePath: ofNullable(e.filePath),
          thumbnailPath: ofNullable(e.thumbnailPath),
        ),
      );
      if ((e.thumbnailUrl?.isNotEmpty ?? false) && downThumbnail) {
        Get.find<DownLoadService>().loadFile(e, downThumbnail: true);
      } else if ((e.fileUrl?.isNotEmpty ?? false) && !downThumbnail) {
        Get.find<DownLoadService>().loadFile(e, downThumbnail: false);
      }
    } catch (e) {}
  }

  void updateDownFileState(MessageEvent event, bool updateUi) {
    try {
      var e = messageList.firstWhere((element) => event.msgId == element.msgId);
      if (event.filePath?.isNotEmpty ?? false) {
        e.filePath = event.filePath;
      }
      if (event.thumbnailPath?.isNotEmpty ?? false) {
        e.thumbnailPath = event.thumbnailPath;
      }

      if (event.fileState != null) {
        e.fileState = event.fileState;
        // if (_currentActivePlayMsgEvent != null &&
        //     e.msgId == _currentActivePlayMsgEvent?.msgId &&
        //     e.fileState == FileState.successful) {
        //   e.activePlay = true;
        //   AppLogger.d('AudioPlayEvent 2==${e.fileState} id=${e.msgId}');
        // }
      }
      _updateMessageItem(e, updateUi: updateUi, isFromDB: true);
    } catch (e) {}
  }

  void _clearImageCache() {
    for (var element in _imagesWeakMap) {
      clearMemoryImageCache(element.path);
    }
    _imagesWeakMap.clear();
    _imgHeightMaps.clear();
    _imgWidthMaps.clear();
  }

  @override
  void onClose() {
    AppLogger.d('messagecontroller onclose');
    if (currentUser?.userName?.isNotEmpty == true) {
      Get.find<DownLoadService>().quit(currentUser?.userName ?? '');
    }

    keyBoardUtil.cancel();
    VoicePlayerNew().stop();
    Get.find<AppConfigService>().currSessionIDChange = '';
    for (var element in _subscriptions) {
      element.cancel();
    }
    subscriptionTopAdmin?.cancel();
    subscriptionTopAdmin = null;
    _clearImageCache();
    _subscriptions.clear();
    _timerChannel?.cancel();
    _timerSyncMessage?.cancel();
    allTimerCancel();
    _textEditingControllerInput.removeListener(back);
    animationController?.dispose();
    topController.dispose();
    topControllerAdmin.dispose();
    worker?.dispose();
    worker1?.dispose();
    jumpWorker?.dispose();
    // worker2?.dispose();
    closeVoteFloating();

    super.onClose();
  }

  void onSticker({bool backButton = false}) async {
    // if (!_focusNodeInput.hasFocus) {
    //   AppLogger.d("focusNodeInput.has not Focus");
    //
    // }
    AppLogger.d(
        "message page: onSticker stickerHeight.value==${stickerHeight.value}");
    if (!backButton) {
      if (!isSticker.value) {
        // containerFillBottomHeight.value=0;
        if (stickerHeight.value < StickerDefault.stickerDefaultHeight) {
          stickerHeight.value = StickerDefault.stickerDefaultHeight;
          // Get.find<AppConfigService>().saveKeyBoardHeight(StickerDefault.stickerDefaultHeight);
        }
      }
      isSticker.value = !(isSticker.value);
    } else {
      isSticker.value = false;
    }
    await SystemChannels.textInput.invokeMethod('TextInput.hide');
  }

  void onSendTextMessage() {
    String message = _textEditingControllerInput.text.trim();
    if (message.isEmpty) {
      toast(L.the_message_cannot_be_empty.tr);
      return;
    }
    message = _textEditingControllerInput.text;
    if (message.length > textMaxLength) {
      toast(L.the_message_length_cannot_exceed_1000_characters.tr);
      return;
    }
    _text.value = false;
    List<String>? atList = _atContactController?.getAtUsers(message);
    var username = currentUser?.userName;
    var chatType = currentUser?.chatType;
    if (username == null || chatType == null) {
      toast(L.the_current_conversation_is_exceptional.tr);
      return;
    }
    _sendMessage(MessageEvent(
      uuid(),
      owner: username,
      chatType: ChatType.values[chatType],
      type: MessageType.text,
      body: message,
      direction: 1,
      dateTime: TimeTask.instance.getNowDateTime(),
      at: atList,
      selfDestruct: rxSelfDestruct.value,
    ));
    _textEditingControllerInput.clear();
    _rxInputTextChange.value = '';
    _atContactController?.cleanAtContact();
  }

  void onTextEditUnfocus() {
    _focusNodeInput.unfocus();
    isSticker.value = false;
  }

  void onTextChanged(String text) {
    if (text.trim().isEmpty) {
      _text.value = false;
    } else {
      _text.value = true;
    }
    atJudge(text);
    AppLogger.d('onTextChanged text 0000is $text....');
    _rxInputTextChange.value = text;
  }

  void _onSetText(String text) {
    _textEditingControllerInput.text = text;
    if (text.trim().isEmpty) {
      _text.value = false;
    } else {
      _text.value = true;
    }
    _rxInputTextChange.value = text;
  }

  void atJudge(String text) {
    if (!isSingleChat() && isAt(text)) {
      String? groupId = currentUser?.userName;
      if (groupId == null || groupId.isEmpty) {
        return;
      }
      if (Get.context != null) {
        _atContactController?.atContactList(groupId, Get.context!);
      }
    }
  }

  bool isAt(String text) {
    AppLogger.d(
        'onTextChanged _rxInputTextChange is ${_rxInputTextChange.value} ${_rxInputTextChange.value.length}');
    if (text.trim().isEmpty ||
        text.length <= _rxInputTextChange.value.length ||
        (text.length - _rxInputTextChange.value.length > 1)) {
      return false;
    }
    final TextEditingValue value = _textEditingControllerInput.value;
    final int start = value.selection.baseOffset;
    String? c;
    if (start > 0) {
      c = text.substring(start - 1, start);
    }
    if (c == '@' || text.endsWith('@')) {
      return true;
    }
    return false;
  }

  void onLoad() {
    if (currentUser?.chatType == ChatType.channelChat.index) {
      AppLogger.d(
          'syncMessageRequest mapData  _firstUUID=$_firstUUID _minUUID=$_minUUID');
      if (_firstUUID == null) {
        return;
      }
      if (_minUUID != null && _firstUUID!.compareTo(_minUUID!) <= 0) {
        return;
      }
      if (repeatSync() || _currentIndex.value == 0) {
        return; //重复请求
      }
      rxLoadMore.value = true;
      _startSysTimer();
      ChannelTask.syncMessgae(currentUser?.userName, 25,
          maxUuid: _firstUUID, minUuid: _minUUID);
    } else if (currentUser?.chatType == ChatType.officialChat.index) {
      loadNoticeMsg();
    }
  }

  void _startSysTimer() {
    if (currentUser?.chatType == ChatType.channelChat.index) {
      _timerSyncMessage?.cancel();
      _timerSyncMessage = Timer.periodic(const Duration(seconds: 3), (timer) {
        if (rxLoadMore.value) {
          rxLoadMore.value = false;
        }
      });
    }
  }

  String? _currentUUId;
  int _lastSyncTime = 0;

  bool repeatSync() {
    int t = TimeTask.instance.getNowTime() ~/ 1000;
    if (t - _lastSyncTime < 5 && _currentUUId == _firstUUID) {
      AppLogger.d('syncMessageRequest repeatSync _currentUUId=$_currentUUId');
      return true;
    }
    _currentUUId = _firstUUID;
    _lastSyncTime = t;
    return false;
  }

  void loadNoticeMsg() {
    OfficialAccountTask.instance.getNoticeList();
  }

  /// 跳转详情
  void onGotoDetail({bool clickTitle = true}) {
    var chatType = currentUser?.chatType;
    var username = currentUser?.userName;
    if (username == null ||
        chatType == null ||
        isFileHelper.value ||
        isMeetingRobot()) {
      return;
    }

    if (currentUser?.chatType != ChatType.singleChat.index &&
        currentUser?.isGroupValid != true &&
        currentUser?.chatType != ChatType.officialChat.index) {
      return;
    }
    if (chatType == ChatType.officialChat.index) {
      Get.toNamed(Routes.OfficialDetailPage);
    } else if (chatType == ChatType.singleChat.index) {
      Get.to(const ContactDetailView(), arguments: currentUser);
    } else if (clickTitle &&
        (chatType == ChatType.channelChat.index ||
            chatType == ChatType.groupChat.index)) {
      Get.to(() => GroupDetailsPage(
        groupID: username,
        avatarPath: currentUser?.avatarPath,
        chatType: chatType,
      ));
    }
  }

  _saveLoc(MessageEvent message) async {
    String? filePath = message.filePath;
    SaveFileType saveFileType = SaveFileType.file;
    switch (message.type) {
      case MessageType.image:
        saveFileType = SaveFileType.image;
        break;
      case MessageType.video:
        saveFileType = SaveFileType.video;
        break;
      case MessageType.file:
        saveFileType = SaveFileType.file;
        break;
      default:
        break;
    }
    saveFileLoc(
      filePath: filePath ?? "",
      fileName: message.fileName,
      fileFragment: message.fileFragment,
      saveFileType: saveFileType,
    );
  }

  /// 菜单功能 保存
  void onSave(MessageEvent message) async {
    if (await Permission.storage.request().isGranted ||
        await Permission.photos.request().isGranted ||
        await Permission.videos.request().isGranted) {
      // Either the permission was already granted before or the user just granted it.
      var filePath = message.filePath;
      if (filePath == null) {
        Get.find<DownLoadService>().loadFile(message, onEndBack: (message) {
          _saveLoc(message);
        });
        return;
      } else {
        _saveLoc(message);
      }
    } else {
      toast(L.chat_storage_permission_refuse_then_cannot_operate_file.tr);
    }
  }

  /// 菜单功能
  void onCopyMessage(MessageEvent message) {
    switch (message.type) {
      case MessageType.meeting:    
        if(message.body == null) return;    
        Meeting? meeting = Meeting.fromJson(jsonDecode(message.body!));        
        String textToCopy = getFormattedMeetingMsg(meeting);
        Clipboard.setData(ClipboardData(text: textToCopy));
        break;
      default:
        Clipboard.setData(ClipboardData(text: message.body ?? ""));
        break;
    }
    
  }

  void onDeleteMessage(List<MessageEvent> messageList) {
    for (MessageEvent message in messageList) {
      AppDatabase database = Get.find<AppDatabase>();
      String? msgId = message.msgId;

      _messageList.remove(message);
      if (message.chatType == ChatType.channelChat) {
        database.updateMessageData(
          MessageCompanion.insert(
            msgId: message.msgId,
            state: ofNullable(MessageStatus.del),
            filePath: ofNullable(null),
            thumbnailPath: ofNullable(null),
            thumbnailUrl: ofNullable(null),
            fileUrl: ofNullable(null),
            thumbnailFragment: ofNullable(null),
            fileFragment: ofNullable(null),
          ),
        );
      } else {
        database.deleteMessage(msgId);
      }
      deleteFileOrThumbnailPath([
        appSupporAbsolutePathToPath(message.filePath)
      ], [
        appSupporAbsolutePathToPath(message.thumbnailPath)
      ], manDelete: false);
      SessionTask.deleteMsg(message.owner, msgId);
      if (getChatType() == ChatType.officialChat) {
        OfficialAccountTask.instance.deleteNotice(message.uuid ?? '');
      }
      Get.find<AppConfigService>()
          .cancelMsgTopTop(message.owner, message.msgId);
    }
    changeMessageCanMultiSelect(false);
  }

  Future<bool> onForwardMessage(List<MessageEvent> messageList) async {
    return await forwardMessage(messageList);
  }

  Future<void> onTranslateMessage(
      BuildContext context, MessageEvent messageInfo) async {
    if (messageInfo.body != null) {
      bool isTranslateed = (messageInfo.translateMsg?.isNotEmpty ??
          false) /*&& !TranslatorUtil.isTranslateLoading(messageInfo.translateMsg)*/;
      if (isTranslateed) {
        Get.find<AppDatabase>().updateMessageTranslate(null, messageInfo.msgId);
        messageInfo.translateMsg = null;
      } else {
        messageInfo.translateMsg = TranslatorUtil.translatorLoading;
        update([messageInfo.msgId]);
        var data = await TranslatorUtil.translatorData(
            context, messageInfo.body ?? '');
        if (data?.isNotEmpty ?? false) {
          Get.find<AppDatabase>()
              .updateMessageTranslate(data, messageInfo.msgId);
          messageInfo.translateMsg = data;
        } else {
          messageInfo.translateMsg = null;
        }
      }
      update([messageInfo.msgId]);
    }
  }

  void onUndoMessage(MessageEvent message) async {
    if (message.chatType == ChatType.channelChat &&
        (message.uuid?.isNotEmpty ?? false)) {
      if (message.direction == Direction.outGoing) {
        ///频道单条消息撤回
        bool result = await undoMessageRequest(message.uuid ?? '');
        if (!result) {
          return;
        }

        int time = TimeTask.instance.getNowDateTime().millisecondsSinceEpoch;
        var dateTime = DateTime.fromMillisecondsSinceEpoch(time);
        String timeText = DateFormat("yyyy-MM-dd HH:mm:ss").format(dateTime);
        String bodyText = '$timeText ${L.the_message_was_recall.tr}';

        var mapData = ChannelOperation(
          action: ChannelOption.sigRecall,
          time: time,
          chatType: ChatType.channelChat.index,
          type: MessageType.channelOpera.index,
          targetId: [message.msgId],
          msgId: uuid(),
          owner: currentUser?.userName ?? '',
          body: bodyText,
        ).toJson();
        var send = json.encode(mapData);
        var res = await sendMessageRequest(currentUser?.userName ?? '', send);
        if (res != null) {
          AppLogger.d('onUndoMessage 撤回.......');
          var msgTask = ChannelOptionTask();
          ChannelOperation option = ChannelOperation.fromJson(mapData);
          msgTask.sigRecall(option);
        } else {
          toast(L.undo_fail.tr);
        }
      } else {
        ChannelTask.repealMemberMsg(
            channelId: message.owner, memberUserName: message.from ?? "");
      }
    } else {
      undoMessage(message);
    }
  }

  onMute(MessageEvent message) {
    String memberNickName = ownInfo(message.from ?? "")?.nickname ?? "";
    if (memberNickName.isEmpty && (message.from ?? "").length > 6) {
      memberNickName = "u${(message.from ?? "").substring(0, 6)}";
    }
    ChannelTask.muteMember(
        channelId: message.owner,
        mute: true,
        memberUserName: message.from ?? "",
        memberNickName: memberNickName);
  }

  jumpQRData(MessageEvent message) async {
    AppLogger.d('jumpQRData ${message.ext1}');
    if (message.ext1?.isNotEmpty ?? false) {
      QrUtil.isLegitimate(
        message.ext1 ?? '',
      );
    }
  }

  recognizeQRCode(MessageEvent message) async {
    var imgByte = getImageCache(message.filePath ?? message.thumbnailPath);
    if (imgByte != null) {
      try {
        var data = await QrUtil.isQrData(imgByte);
        if (data?.isNotEmpty ?? false) {
          Get.find<AppDatabase>().updateMessageData(MessageCompanion.insert(
            msgId: message.msgId,
            ext1: ofNullable(data),
          ));
        }
        return data;
      } catch (e) {
        AppLogger.e('recognizeQRCode ${e.toString()}');
      }
    }
    return null;
  }

  Future<void> _onAttachment(MessageType type, String? srcFilePath,
      {String? body, Duration? duration}) async {
    String msgId = uuid();

    File? srcFile;
    int? srcFileSize;
    String? srcFileName;
    int? fileDuration = duration?.inMilliseconds;
    int? fileState;
    List<double>? noises;
    if (srcFilePath != null) {
      srcFile = File(srcFilePath);
      if (!srcFile.existsSync()) {
        return;
      }
      srcFileSize = srcFile.lengthSync();
      srcFileName = srcFilePath.split('/').last;
      if (type == MessageType.audio) {
        fileDuration ??= await getAudioFileDurationMs(srcFilePath);
        noises = getNoises();
      }
      fileState = FileState.successful;
    }

    String? username = _currentUser?.value.userName;
    int? chatType = _currentUser?.value.chatType;
    if (username == null || chatType == null) {
      return;
    }

    bool selfDestruct = false;
    if (type == MessageType.image) {
      selfDestruct = rxSelfDestruct.value;
    }

    _sendMessage(
      MessageEvent(
        msgId,
        owner: username,
        type: type,
        chatType: ChatType.values[chatType],
        body: body,
        fileName: srcFileName,
        fileSize: srcFileSize,
        direction: 1,
        dateTime: TimeTask.instance.getNowDateTime(),
        fileDuration: fileDuration,
        selfDestruct: selfDestruct,
        fileState: fileState,
        noises: noises,
      ),
      srcFile: srcFile,
    );
  }

  bool onEditImageSend(String filePath) {
    try {
      final file = File(filePath);
      final fileSize = file.lengthSync();

      AppLogger.d('File size: $fileSize bytes');

      // Proceed with the upload without checking file size
      _onAttachment(MessageType.image, filePath, body: null);
      return true;
    } catch (e) {
      AppLogger.e('Failed to send image: $e');
      return false;
    }
  }

    bool onVideoSend(String filePath) {
    try {
      final file = File(filePath);
      final fileSize = file.lengthSync();

      AppLogger.d('File size: $fileSize bytes');

      // Proceed with the upload without checking file size
      _onAttachment(MessageType.video, filePath, body: null);
      return true;
    } catch (e) {
      AppLogger.e('Failed to send image: $e');
      return false;
    }
  }

  

  void onAttachmentSelect(List<AttachmentEntity> attachments) {
    for (AttachmentEntity attachment in attachments) {
      var srcFilePath = attachment.filePath;
      final contactInfo = attachment.contactInfo;

      if (attachment.type == MessageType.contactCard && contactInfo != null) {
        for (var contact in contactInfo.contacts) {
          ContactBean contactBean = ContactBean(
            contact.userName,
            contact.displayName,
          );
          srcFilePath = appSupporAbsolutePath(contact.avatarPath);
          _onAttachment(attachment.type, srcFilePath,
              body: jsonEncode(contactBean.toJson()));
        }
      } else {
        _onAttachment(attachment.type, srcFilePath,
            body: attachment.type == MessageType.sticker
                ? L.collection_sticker_messages.tr
                : null);
      }
    }
  }

  void onSendVoiceMessage(String? filePath, Duration? duration) {
    if (filePath == null) {
      return;
    }
    // SocketTask.instance.offlineAudioToText(filePath);
    _onAttachment(MessageType.audio, filePath, duration: duration)
        .then((value) {
      // File(filePath).delete();
    });
  }

  /// 重发消息
  void onResendMessage(MessageEvent info) {
    resendMessage(info);
  }

  void onRsendMessage(MessageEvent info) {
    _messageList.remove(info);
    File? srcFile;
    if (info.filePath != null) {
      srcFile = File(info.filePath!);
      if (!srcFile.existsSync()) {
        srcFile = null;
      }
    }
    info.dateTime = TimeTask.instance.getNowDateTime();
    info.state = BubbleItemState.LOAD;
    info.chatType = getChatType();
    _sendMessage(info, srcFile: srcFile);
  }

  void jumpTo() {
    if (_jump && _messageList.length > 1) {
      Future.delayed(const Duration(milliseconds: 100), () {
        if (itemScrollController.isAttached) {
          itemScrollController.jumpTo(index: 0);
          rxShowJump.value = false;
        }
      });
    }
    _jump = false;
  }

  /// 获取分享的联系人信息
  Stream<MemberInfo?> shareContactInfo(ContactBean? bean) {
    late final StreamController<MemberInfo?> controller;
    controller = StreamController<MemberInfo?>(
      onListen: () async {
        MemberInfo? info = ownInfo(bean?.userName);
        if (info == null) {
          info = MemberInfo(bean?.userName ?? '',
              displayname: bean?.displayName, from: bean?.from);
          updateOwnInfo([info]);

          Get.find<ChannelService>().getOwnInfo(
            bean?.userName,
            server: true,
            callback: (data) {
              data.from = bean?.from;
              updateOwnInfo([data]);
            },
          );
        }
        controller.sink.add(info);
        await controller.close();
      },
    );
    return controller.stream;
  }

  ///频道头像缓存
  Map<String, String> channelCardAvatarCache = {};

  /// 获取频道头像
  Stream<String?> getChannelAvatarPath(ChannelCard? bean) {
    late final StreamController<String?> controller;
    controller = StreamController<String?>(
      onListen: () async {
        var channelId = bean?.channelId;
        String? avatarPath;
        if (channelId != null) {
          var avatarCache = channelCardAvatarCache[channelId];
          if (avatarCache?.isNotEmpty ?? false) {
            avatarPath = avatarCache;
          } else {
            var modelData = await getChannelInfoRequest(channelId, dsc: true);
            if (bean?.title?.isEmpty ?? true) {
              bean?.title = modelData.title;
            }
            var db = Get.find<AppDatabase>();
            var oldChannelData = await db
                .oneChannelInfo(bean?.channelId ?? '')
                .getSingleOrNull();
            String? newAvatarPath;
            String url = modelData.avatar ?? '';
            String path = oldChannelData?.avatarPath ?? '';
            if (url.isNotEmpty &&
                (url != oldChannelData?.avatarUrl ||
                    path.isEmpty ||
                    !File(appSupporAbsolutePath(path) ?? '').existsSync())) {
              var savePath = avatarSavePath(userName: channelId, fileName: url);
              newAvatarPath = await downloadFile(url,
                  savePath: savePath, isComPressImageJPG: true);
              newAvatarPath = appSupporAbsolutePathToPath(newAvatarPath);
              avatarPath = newAvatarPath ?? '';
            } else {
              avatarPath = path;
            }
            channelCardAvatarCache[channelId] = avatarPath;
          }
        }
        controller.sink.add(appSupporAbsolutePath(avatarPath));
        await controller.close();
      },
    );
    return controller.stream;
  }

  _getLinkImageWH(LinkModel linkModel) async {
    var maxWidth = Get.size.width * .8;
    var maxHeight = maxWidth * 2 / 3;
    if (linkModel.imageWidth != null && linkModel.imageHeight != null) {
      linkModel.imageWidth =
          linkModel.imageWidth! > maxWidth ? maxWidth : linkModel.imageWidth;
      linkModel.imageHeight = linkModel.imageHeight! > maxHeight
          ? maxHeight
          : linkModel.imageHeight;
    } else if (linkModel.imagePath != null) {
      var imageWH = await getImageWH(
        image: Image.file(
          File(linkModel.imagePath!),
        ),
      );
      linkModel.imageWidth =
          imageWH.width > maxWidth ? maxWidth : imageWH.width;
      linkModel.imageHeight =
          imageWH.height > maxHeight ? maxHeight : imageWH.height;
    }
  }

  Map<String, MessageEvent> linkMessageEventCache = {};

  Future<LinkModel?> downLoadLinkImage(MessageEvent event) async {
    bool isUpdateDb = false;
    if ((event.expand?.linkModel?.content?.isEmpty ?? true) ||
        (event.expand?.linkModel?.title?.isEmpty ?? true) == true ||
        ((event.expand?.linkModel?.imageUrl?.isNotEmpty ?? false) &&
            ((event.expand?.linkModel?.imagePath?.isEmpty ?? true) ||
                event.expand?.linkModel?.imageHeight == 0 ||
                event.expand?.linkModel?.imageWidth == 0))) {
      isUpdateDb = true;
    } else {
      isUpdateDb = false;
    }
    if (linkMessageEventCache.containsKey(event.msgId)) {
      var cache = linkMessageEventCache[event.msgId];
      var linkModel = cache?.expand?.linkModel;
      if (linkModel != null) {
        await _getLinkImageWH(linkModel);
      }
      return linkModel;
    }

    var linkModel = event.expand?.linkModel;
    if (linkModel == null) {
      await ChatTask.instance.parseLinkMsg(event);
      linkModel = event.expand?.linkModel;
    }
    if (linkModel != null) {
      var s = await LinkParseUtil().downLoadImage(linkModel);
      if (linkModel.imagePath != s) {
        linkModel.imagePath = s;
        isUpdateDb = true;
      }
      await _getLinkImageWH(linkModel);
      event.expand ??= MessageExpand();
      event.expand?.linkModel = linkModel;
      linkMessageEventCache[event.msgId] = event;
    }
    if (isUpdateDb) {
      if (event.expand != null) {
        String expand = json.encode(event.expand);
        Get.find<AppDatabase>().updateMessageData(
          MessageCompanion.insert(
            msgId: event.msgId,
            expand: ofNullable(expand),
          ),
        );
      }
    }
    return linkModel;
  }

  topInsertMessage(
    List<MessageData> events,
  ) async {
    List<MessageEvent> messageEvents = [];
    for (var element in events) {
      if (element.owner != _currentUser?.value.userName) {
        break;
      }
      var username = element.owner;
      var chatType = element.chatType;
      if (username == null || chatType == null) {
        continue;
      }
      var map = await getReplayInfo2(element.replayMsg, messages: messageList);
      var messageEvent = data2event(element,
          replayFilePath: map['replayFilePath'],
          replayFileFragment: map['replayFileFragment'],
          replayMsgId: map['replayMsgId']);

      if (messageEvent != null) {
        messageEvents.add(messageEvent);
      }
    }
    _insertMessage(messageEvents, isFromDB: true);
  }

  _insertMessage(List<MessageEvent> events, {isFromDB = false}) async {
    if (events.isEmpty) {
      return;
    }
    List? currentList = [];
    for (MessageEvent event in events) {
      var uuid = event.uuid;
      var msgID = event.msgId;
      var dateTime = event.dateTime;

      /// 记录  uuid 排序
      if (uuid != null && !isFromDB) {
        updateMaxUUID(uuid, event.owner);
      }

      /// 去重
      for (var i = 0; i < messageList.length; i++) {
        var mmsgID = messageList[i].msgId;

        /// 已经存在的消息
        if (mmsgID == msgID) {
          _updateMessageItem(event, isFromDB: isFromDB);
          currentList.add(event);
          break;
        }
      }

      var chipDate = DateTime(dateTime.year, dateTime.month, dateTime.day);
      _dateChipMap[chipDate] = (_dateChipMap[chipDate] ?? 0) + 1;

      if (messageList.isEmpty || _dateChipMap[chipDate] == 1) {
        messageList.add(MessageEvent("",
            owner: "", chatType: ChatType.singleChat, dateTime: chipDate));
      }
    }
    events.removeWhere((element) {
      return currentList.contains(element) ||
          (isChannelChat() && element.isUndo);
    });
    messageList.addAll(events);
    messageList.sort((a, b) => b.dateTime.compareTo(a.dateTime));
    _updateRemoteAvatar();
    // _updateDaoPollBubbleStatus();
  }

  /// 更新投票气泡的状态
  // void _updateDaoPollBubbleStatus() {
  //   if(_messageList.isEmpty || onGoingVotes.isEmpty) return;
  //   List<String> idsToUpdate = [];
  //   for (MessageEvent element in messageList) {
  //     if (element.type == MessageType.daoPoll) {
  //       var data = Poll.fromJson(jsonDecode(element.body ?? ""));
  //       /// onGoingVotes内有的需要更新，通过Poll的id判断是否有
  //       if (onGoingVotes.any((element) => element.id == data.id)) {
  //         data.status = DaoPollStatus.ongoing;
  //         element.body = jsonEncode(data.toJson());
  //         idsToUpdate.add(element.msgId);
  //       }
  //     }
  //   }
  //   if(idsToUpdate.isEmpty) return;
  //   update(idsToUpdate);
  // }

  void updateBlackAndAddFriendDescribeMsgEvent(DateTime time) {
    for (MessageEvent element in messageList) {
      DateTime t = element.dateTime;
      if (element.msgId == Config.blackTag) {
        var compareTo = t.compareTo(time);
        if (compareTo == 1) {
          t = time.add(const Duration(milliseconds: -10));
        }
      } else if (element.msgId == Config.addFriendDescribe) {
        var compareTo = t.compareTo(time);
        if (compareTo == 1) {
          t = time.add(const Duration(milliseconds: -20));
        }
      }
      element.dateTime = t;
    }
  }

  Future<void> _sendMessage(
    MessageEvent event, {
    File? srcFile,
  }) async {
    if (isChannelChat() && memberCount != null) {
      try {
        var count = memberCount ?? 0;
        if (count >= Config.maxNumberCount && !isAdminOrOwner) {
          if (!CommUtil.instance.canSend(event)) {
            toast(L.operation_restricted.tr);
            return;
          }
        }
      } catch (e) {
        AppLogger.e(
            '_sendMessage memberCount =$memberCount error ${e.toString()}');
      }
    } else if (isGroupChat()) {
      event.toNumbers = _groupMember;
    }
    if (isReplayMsg()) {
      event.replayMsg = _replyController?.getReplayMsgData();
      var map = await getReplayInfo2(event.replayMsg, messages: messageList);
      event.replayFilePath = map['replayFilePath'];
      event.replayFileFragment = map['replayFileFragment'];
      event.replayMsgId = map['replayMsgId'];
    }
    await _insertMessage([event]);
    if (event.direction == 1 && messageList.length > 1) {
      //看jumpTo源码得知，当ScrollablePositionedList：itemCount为0时，界面还没来得及刷新，这时跳转就会把index复制为-1
      // _scrollController.jumpTo(index: 0);
      _jump = true;
    }
    jumpTo();

    sendMessage(srcFile, event, pushType: PushType.message);
  }

  bool isReplayMsg() {
    return _replyController?.isReplayMsg() ?? false;
  }

  bool isGroupChat() {
    return currentUser?.chatType == ChatType.groupChat.index;
  }

  bool isChannelChat() {
    return currentUser?.chatType == ChatType.channelChat.index;
  }

  bool isDaoChannelChat() {
    return currentUser?.chatType == ChatType.channelChat.index && _channelAttribute == ChannelAttribute.dao.index;
  }

  ChatType getChatType() {
    ChatType type = ChatType.singleChat;
    switch (currentUser?.chatType) {
      case 1:
        type = ChatType.groupChat;
        break;
      case 2:
        type = ChatType.channelChat;
        break;
      case 3:
        type = ChatType.officialChat;
        break;
    }
    return type;
  }

  Future<MessageEvent?> _updateMessageItem(MessageEvent event,
      {bool isFromDB = false, bool updateUi = true}) async {
    for (var i = 0; i < messageList.length; i++) {
      if (messageList[i].msgId == event.msgId) {
        messageList[i] = event;
        if (updateUi) {
          _update([event.msgId]);
        }
        var uuid = event.uuid;
        if (uuid != null && !isFromDB) {
          updateMaxUUID(uuid, event.owner);
        }
        return event;
      }
    }
    return null;
  }

  _update(List<String> ids) {
    // _updateMsgId.value++;
    // _currentMessageId.addAll(ids);
    update(ids);
  }

  _freshUpdate() {
    List<String> ids = [];
    for (String id in _currentMessageId) {
      ids.add(id);
    }
    update(ids);
    AppLogger.d("_freshUpdate ids=${ids.length} ");

    _currentMessageId.removeWhere((element) => ids.contains(element));
  }

  _messageDataHandle(MessageRecvEvent event) async {
    List<MessageEvent> messageEvents = [];
    for (var element in event.messages) {
      var uuid = element.uuid;
      var owner = element.owner;
      if (uuid != null && owner != null) {
        updateMaxUUID(uuid, owner);
      }
      if (event.exist) {
        continue;
      }
      if (element.owner != _currentUser?.value.userName) {
        continue;
      }
      var username = element.owner;
      var chatType = element.chatType;
      if (username == null || chatType == null) {
        continue;
      }
      var map = await getReplayInfo2(element.replayMsg, messages: messageList);
      var messageEvent = data2event(element,
          replayFilePath: map['replayFilePath'],
          replayFileFragment: map['replayFileFragment'],
          replayMsgId: map['replayMsgId']);

      if (messageEvent != null) {
        if (messageEvent.direction == Direction.inComing) {
          messageEvent.hasShown = false;
        }
        messageEvents.add(messageEvent);
      }
    }
    _insertMessage(messageEvents);
  }

  /// 控制类消息
  void _cmdDataHandle(CmdModel data) {
    List<String> targetIds = data.targetId ?? [];
    for (var element in targetIds) {
      int index = 0;
      MessageEvent? message;

      for (index = 0; index < messageList.length; index++) {
        if (messageList[index].msgId == element) {
          message = messageList[index];
          break;
        }
      }
      if (message == null) {
        continue;
      }

      if (data.action == MessageAction.cmdBurn) {
        // 自毁
        messageList.removeAt(index);
      } else if (data.action == MessageAction.cmdRead) {
        // 已读
        message.state = BubbleItemState.SEEN;
        messageList[index] = message;
        update([message.msgId]);
      } else if (data.action == MessageAction.cmdRevoke) {
        // 撤回
        message.isUndo = true;
        if (data.undoMsgDatas?.containsKey(element) == true) {
          var msgData = data.undoMsgDatas![element] as MessageData;
          message.ext1 = msgData.ext1;
        }
        messageList[index] = message;
        _update([message.msgId]);

        var player = VoicePlayerNew();
        if (player.currPlayID == element) {
          player.stop();
        }
      } else if (data.action == MessageAction.cmdResend) {
        // 重发,不需要界面做操作
      }
    }
  }

  void _updateContact(String username, String? avatar, String? displayname,
      {bool? isFriend, bool? isGroupValid, bool? isTid}) {
    _currentUser?.value.userName = username;
    _currentUser?.value.avatarPath = avatar;
    _currentUser?.value.displayName =
        getChatDisplayNameOffTid(displayname ?? '',isChannelChat(), isTid: isTid, user: username);
    AppLogger.d(
        '_currentUser?.value.displayName =${_currentUser?.value.displayName}');
    if (isTid != null) {
      _currentUser?.value.isTid = isTid;
    }
    if (isFriend != null) _currentUser?.value.isFriend = isFriend;
    if (isGroupValid != null) _currentUser?.value.isGroupValid = isGroupValid;
    update(["message_page_title"]);
  }

  /// 更改和设置新的会话对象
  Future<void> _setCurrentUser(UserMessage userMessage) async {
    if (currentUser?.userName?.isNotEmpty == true) {
      Get.find<DownLoadService>().quit(currentUser?.userName ?? '');
    }

    AppLogger.d("session empty userMessage==${userMessage.toJson()}");
    if (userMessage.chatType == ChatType.officialChat.index) {
      userMessage.displayName = getOfficeDisplayName();
    }
    _memberCount = null;
    Get.find<AppConfigService>().currSessionIDChange =
        userMessage.userName ?? '';
    _atContactController = AtContactController(_selectContactForAt);
    _replyController = ReplyController(imagesWeakMap, this);
    // _emojiController = EmojiController(userMessage.userName??'');
    String? username = userMessage.userName;
    Get.find<PushService>().clearAllNotifications();
    var chatType = userMessage.chatType;
    if (username == null || chatType == null) {
      return;
    }

    var oldUserName = currentUser?.userName;
    if (username == oldUserName) {
      return;
    }

    if (_currentUser == null) {
      _currentUser = userMessage.obs;
    } else {
      _currentUser!.value = userMessage;
    }
    var user = Get.find<AppConfigService>().getUserName();
    isFileHelper.value = username == user;
    if (isFileHelper.value) {
      _currentUser?.value.displayName =
          getChatDisplayNameOffTid('', false,isTid: false, user: username);
    }
    AppLogger.d("session empty userMessage==${user}");
    AppLogger.d("session empty userMessage==${username}");

    rxSelfDestruct.value = false;
    allTimerCancel();

    _setDraft();

    VoicePlayerNew().stop();
    isTabooType.value = false;
    _mute();
    channelAdminList.clear();
    channelOwner = "";
    isAdminOrOwner = false;
    if (currentUser?.chatType == ChatType.channelChat.index) {
      _requestChannelInfo(); //获取频道信息
    }
    clearChatHistory = false;
    setWallpaper();
    await _reset();
  }

  channelMemberAdminProcess(List<String> admins) {
    if (channelAdminList.isEmpty) {
      channelAdminList.addAll(admins);
      AppLogger.d(
          'channelMemberAdminProcess needUpdateUserName=${channelAdminList.toString()}');
      update(channelAdminList);
    } else {
      List<String> needUpdateUserName = [];
      for (int i = 0; i < channelAdminList.length; i++) {
        String admin = channelAdminList[i];
        if (!admins.contains(admin)) {
          needUpdateUserName.add(admin);
          channelAdminList.remove(admin);
          i--;
        } else {
          admins.remove(admin);
        }
      }
      for (String admin in admins) {
        needUpdateUserName.add(admin);
        channelAdminList.add(admin);
      }
      if (needUpdateUserName.isNotEmpty) {
        AppLogger.d(
            'channelMemberAdminProcess needUpdateUserName=${needUpdateUserName.toString()}');
        update(needUpdateUserName);
      }
    }
  }

  void _storeMapChannelTag(List<Role>? tags) {
    if(tags == null || tags.isEmpty) return;
    _mapChannelTag.clear();
    for (var tag in tags) {
      if(tag.tagId == null) continue;
      _mapChannelTag[tag.tagId!] = tag;
    }
  }

  Map<String, Role> getMapChannelTag() {
    return _mapChannelTag;
  }

  void _requestChannelInfo() async {
    String? channelId = _currentUser?.value.userName;
    if (channelId == null || channelId.isEmpty) {
      return;
    }
    var modelData = await getChannelInfoRequest(channelId);
    AppLogger.d('memberInfoRequest modelData=${modelData.toJson()}');
    _muteTime?.cancel();
    var username = Get.find<AppConfigService>().getUserName();
    if (modelData.code == Code.code200) {
      /// 若为DAO频道，则查询用户代币持有量
      /// 未达门槛，自动退群
      if(isDaoChannelChat()) {
        if(modelData.chain != null && int.tryParse(modelData.chain!) != null && modelData.tokenAddress != null && modelData.minNumToken != null && modelData.walletAddress != null) {
          /// 传入之前绑定的钱包地址进行持币量检测
          _checkTokenMeetRequirement(int.parse(modelData.chain!), modelData.tokenAddress!, modelData.minNumToken!, modelData.walletAddress!);
        }        
      }
      if(modelData.tags != null && modelData.tags!.isNotEmpty) {
        /// 同步到缓存
        Get.find<EventBus>().fire(SyncChannelTagEvent(modelData.tags!));
      }      
      _topControllerAdmin.adminTopMsgSync(channelId, modelData);
      if (channelOwner.isEmpty) {
        channelOwner = modelData.owner ?? "";
        if (channelOwner.isNotEmpty) {
          update([channelOwner]);
        }
      }
      channelMemberAdminProcess(modelData.admins ?? []);
      mySelfIsChannelAdminOrOwner();
      _serMemberCount(modelData.memberCount);
      //群主或者管理员
      bool isOwner = username == modelData.owner;
      bool isAdmin = modelData.admins?.contains(username) ?? false;
      contactLimit =
          ChannelTask.contactLimit(modelData.options) && !isAdmin && !isOwner;
      AppLogger.d(
          'oneChannelInfo contactLimit=$contactLimit isOwner=$isOwner isAdmin=$isAdmin');
      AppLogger.d('oneChannelInfo contactLimit=${modelData.admins}');
      isAdminOrOwner = isOwner || isAdmin;
      if (isOwner) {
        tabooType = TabooType.none;
      } else if ((modelData.mute ?? false) && !isAdmin) {
        tabooType = TabooType.taboo_all;
        if (!isTabooType.value) {
          isTabooType.value = true;
          _mute();
        }
      } else {
        if (username == null || username.isEmpty) {
          AppLogger.d('memberInfoRequest username is null');
          return;
        }
        List<String> tagId = List<String>.empty(growable: true);
        tagId.add(username);
        var info = await memberInfoRequest(channelId, username, tagId);
        if (info != null && info.isNotEmpty) {
          var i = info.first;
          if (i.mute != null) {
            if (i.mute! < 0) {
              tabooType = TabooType.taboo_all;
            } else if (i.mute! > 0) {
              int t = i.mute! - TimeTask.instance.getNowTime() ~/ 1000;
              if (t > 0) {
                tabooType = TabooType.taboo;
                _muteTime = Timer(Duration(seconds: t), () {
                  _requestChannelInfo();
                });
              } else {
                tabooType = TabooType.none;
              }
            } else {
              tabooType = TabooType.none;
            }
          }
        }
      }
      var isTabooTypeT = tabooType != TabooType.none;
      if (isTabooType.value != isTabooTypeT) {
        isTabooType.value = isTabooTypeT;
        _mute();
      }
      if (isTabooType.value) {
        onTextEditUnfocus();
      }
      ChannelTask.channels([modelData]);
      //请求过于频繁导致卡顿，每次进来更新一次成员信息，详情再更新
      // var myName = Get.find<AppConfigService>().getUserName() ?? '';
      // Get.find<ChannelService>().getAllChannelMemberInfos(channelId, myName);
    } else {
      if (modelData.code == Code.code11401 ||
          modelData.code == Code.code11404 ||
          modelData.code == Code.code11430 ||
          modelData.code == Code.code11431) {
        if (modelData.code == Code.code11431) {
          toast(L.other_remove_by_group_owner.tr);
        } else if (modelData.code == Code.code11430) {
          toast(L.the_group_has_been_disbanded_by_the_group_leader.tr);
        }
        /// 清除聊天记录
        _clearChatHistory(currentUser!.userName!);
        /// 清除聊天入口 & draft if any
        SessionTask.delete(currentUser!.userName!, null,null);
        Get.find<AppConfigService>().removeDraft(currentUser!.userName!);
        Get.find<ChannelService>().channelInvalidDbDataUpdate(channelId);
        Get.back();
        return;
      }
    }
  }

  /// DAO频道未达门槛，自动退群判定
  Future<void> _checkTokenMeetRequirement(int chainId, String tokenAddress, double minNumToken, String? walletAddress) async {
    if(walletAddress == null) return;
    var balance = await getTokensBlance(chainId, tokenAddress, walletAddress);
    if(balance == null) return;
    if(balance < minNumToken) {
      if(currentUser?.userName == null) return;
      /// 清除聊天记录
      _clearChatHistory(currentUser!.userName!);
      /// 退群
      _leaveGroup(currentUser!.userName!);
    }
  }

  /// 清除聊天记录
  void _clearChatHistory(String channelId) {
    ChannelTask.updateChannelMinUuid(channelId);
    Get.find<EventBus>()
        .fire(ClearChatHistoryEvent(channelId));
    SessionTask.clear(channelId);
    Get.find<AppDatabase>().topMsgIdByUserNameAdmin(channelId).get().then((value){
      deleteMessageAndFile(channelId,clearTop: true,noDelMsgIds: value).then((value) {
        Get.find<EventBus>()
            .fire(ClearChatHistoryEvent(channelId));
      });
    });
  }

  /// 退出群
  Future<bool> _leaveGroup(String channelId) async {
    if (channelId.isEmpty) return false;
    await _createHintsMsg(channelId,[]);
    var success = await channelDismissOrLeaveRequest(channelId, false);
    if (success) {
      ChannelTask.dismissOrLeave(channelId);
      toast(L.tip_of_does_not_meet_min_requirement_of_dao.tr);
      Get.back();
      return true;
    }
    return false;
  }

  Future<void> _createHintsMsg(String channelId,[List<String>? members]) async {
    var myName = Get.find<AppConfigService>().getUserName() ?? "";
    List<String> names = [myName, ...?members];

    var resultData = await Get.find<ChannelService>()
        .getDBMemberData(channelId, names);
    if (resultData.isEmpty) return;

    String selfNickname = '';
    List<String> nicknames = [];

    for (var element in resultData.values) {
      String? nickname = element.nickname?.isNotEmpty == true
          ? element.nickname
          : element.displayname;
      nickname = nickname?.isNotEmpty == true ? nickname : element.username;

      if (element.username == myName) {
        selfNickname = nickname ?? '';
      } else {
        nicknames.add(nickname ?? '');
      }
    }

    String body = selfNickname + L.quit_group.tr;

    /// 发送消息
    String msgID = uuid();
    MessageEvent event = MessageEvent(
      msgID,
      owner: channelId,
      chatType: ChatType.channelChat,
      dateTime: TimeTask.instance.getNowDateTime(),
      body: body,
      type: MessageType.tip,
    );
    await sendMessage(null, event);
    Get.find<AppDatabase>().oneMessage(msgID).getSingle().then((value) {
      Get.find<EventBus>().fire(MessageRecvEvent(false, [value]));
    });
  }

  /// 更新头像好和用户名
  void _updateRemoteAvatar() {
    var chatType = currentUser?.chatType;
    var username = currentUser?.userName;
    if (chatType == null || username == null) {
      return;
    }
    if (chatType == ChatType.singleChat.index) {
      return;
    }

    List<String> names = [];
    for (var i = 0; i < messageList.length; i++) {
      var message = messageList[i];
      var from = message.from;
      if (from == null) {
        continue;
      }
      if (from != _currentUser?.value.userName &&
          from.isNotEmpty &&
          _ownDataMap[message.from] == null) {
        _ownDataMap[from] = ChatOwnState.ing;
        names.add(from);
      }
    }
    if (names.isEmpty) {
      return;
    }
    Get.find<ChannelService>().getMemberInfo(
      username,
      names,
      callback: (value) {
        updateOwnInfo(value);
      },
      avatarAsyncNotify: true,
    );
  }

  void updateOwnInfo(List<MemberInfo>? memberInfos) {
    if (memberInfos == null) {
      return;
    }
    List<String> messageIDs = [];
    for (var i = 0; i < memberInfos.length; i++) {
      var item = memberInfos[i];
      var username = item.name;
      var from = item.from;
      _ownInfoMap[username] = item;
      for (var item in _currentItemPositions) {
        var message = messageList[item.index];
        if (message.from == username || message.from == from) {
          messageIDs.add(message.msgId);
        }
      }
    }
    update(messageIDs);
  }

  Future<List<MessageEvent>> msgToMsgEvent(List<MessageData> messages,
      {ValueChanged<MessageEvent>? valueChanged}) async {
    List<MessageEvent> events = [];
    for (var data in messages) {
      // AppLogger.d('syncMessageRequest mapData data=${json.encode(data)}');
      var map = await getReplayInfo(data.replayMsg, messages: messages);
      var event = data2event(data,
          replayFilePath: map['replayFilePath'],
          replayFileFragment: map['replayFileFragment'],
          replayMsgId: map['replayMsgId'],
          undoEdit: false);
      if (event == null) {
        AppLogger.e('syncMessageRequest event is null');
        continue;
      }
      if (event.type == MessageType.audio &&
          (event.fileDuration == null || (event.fileDuration ?? 0) <= 0)) {
        updateAudioFileDuration(event);
      }

      events.add(event);
      valueChanged?.call(event);
    }
    AppLogger.e('syncMessageRequest  events =${events.length}');

    return events;
  }

  updateAudioFileDuration(MessageEvent event) async {
    var durationMs = 0;
    try {
      durationMs = await getAudioFileDurationMs(event.filePath).catchError((e) {
        return 0;
      });
    } catch (e) {
      AppLogger.e(e.toString());
    }
    AppLogger.d(
        'updateAudioFileDuration event.msgId=${event.msgId} durationMs=${durationMs.toString()}');
    Get.find<AppDatabase>()
        .updateAudioFileDuration(durationMs.toString(), event.msgId);
  }

  // double curBottomHeight=0.0;
  _reset() async {
    subscriptionTopAdmin?.cancel();
    isUnDoEditOnTapDown = false;
    contactLimit = false;
    rxShowJump.value = false;
    isJump = false;
    var chatType = currentUser?.chatType;
    var username = currentUser?.userName;
    if (chatType == null || username == null) {
      return;
    }

    /// 初始化变量
    _timerChannel?.cancel();
    _timerSyncMessage?.cancel();
    _currentLastMessageEvt.clear();
    _currentMessageEvt.clear();
    newMessageCount.clear();
    _firstUUID = null;
    _lastMessage.value = 0;
    _currentIndex.value = 0;
    _updateMsgId.value = 0;
    // _currentMsgCount = 0;
    _lastUUID = null;
    _lastSyncMessageUuid = null;
    // _currentActivePlayMsgEvent = null;
    _channelAttribute = null;
    playAudioStateMap.clear();
    _clearImageCache();
    _imagesWeakMap.add(WeakImageBean(
        R.file, (await rootBundle.load(R.file)).buffer.asUint8List()));
    _imagesWeakMap.add(WeakImageBean(R.icoPicLoadFailed,
        (await rootBundle.load(R.icoPicLoadFailed)).buffer.asUint8List()));
    _imagesWeakMap.add(WeakImageBean(R.icImageLoading,
        (await rootBundle.load(R.icImageLoading)).buffer.asUint8List()));
    _createWorker();
    _messageList.clear();
    _dateChipMap.clear();
    shrinkWrap.value = false;
    _replyController?.setReplayData(null);
    _topController.setMsgData(null);
    _topControllerAdmin.setMsgData(null);
    _topController.setAnnouncement(null);
    _currentItemPositions.clear();
    selectedMessageEventList.clear();
    changeMessageCanMultiSelect(false);

    /// 更改撤回编辑状态，要在获取消息之前调用
    _database.updateMessageByUndoEdit(username);

    /// 数据库中查询 所有message

    List<MessageData> messages =
        await _database.chatsByUserName(username).get();
    _messageList.clear();
    _database.updateMessageOneEmojiForHasShown(username);
    List<MessageEvent> events = [];
    if (messages.isNotEmpty) {
      AppLogger.d('syncMessageRequest mapData length=${messages.length}');
      AppLogger.d('syncMessageRequest mapData last=${messages.last.uuid}');
    }

    events.addAll(
      await msgToMsgEvent(
        messages,
      ),
    );
    if (chatType == ChatType.singleChat.index && !isMeetingRobot()) {
      int time = TimeTask.instance.getNowTime();
      if (messages.isNotEmpty) {
        for (int i = messages.length - 1; i >= 0; i--) {
          MessageData d = messages[i];
          if (d.time != null && d.time! >= 0) {
            time = d.time ?? time - 10;
            break;
          }
        }
      }
      if (!isFileHelper.value) {
        events.add(data2event(MessageData(
            id: 0,
            owner: username,
            msgId: uuid(),
            chatType: ChatType.singleChat.index,
            undo: true,
            ext1: L.add_friend_describe.tr,
            time: time - 10))!);
      }
      var blackc =
          await Get.find<AppDatabase>().oneContact(username).getSingleOrNull();
      AppLogger.d('black contact ${blackc?.toJson()}');
      if ((blackc == null || blackc.username.isEmpty || blackc.state != 0) &&
          username != Get.find<AppConfigService>().getUserName()) {
        events.add(data2event(MessageData(
            id: 0,
            owner: username,
            msgId: Config.blackTag,
            chatType: ChatType.singleChat.index,
            undo: true,
            undoEdit: true,
            messageHasRead: blackc?.isBlack,
            //这个字段在这情况下临时表示是否加入了黑名单
            ext1: L.the_other_party_is_not_your_communication.tr,
            time: time - 5))!);
      }
    }
    if (events.length < 20) {
      shrinkWrap.value = true;
    }
    await _insertMessage(events, isFromDB: true);
    //如果是搜索过来的就跳转到对应的搜索item
    var searchMsgId = currentUser?.msgId;
    if (searchMsgId != null && searchMsgId.isNotEmpty) {
      Future.delayed(const Duration(milliseconds: 50), () {
        var isSearch = messageList.length != _currentItemPositions.length;
        replayOnClick(searchMsgId, isSearch: isSearch);
      });
    }

    /// 点对点聊天
    if (chatType == ChatType.singleChat.index) {
      SessionTask.msgReading(username);
      if (!isFileHelper.value) {
        Get.find<ChannelService>().getOwnInfo(username, server: true,
            callback: (value) {
          var nickname = value.nickname;
          AppLogger.d("_updateContact value.isTid==${value.isTid}");

          var defaultName = 'u${username.substring(0, 6)}';
          if (defaultName.startsWith(nickname ?? '')) {
            nickname = _currentUser?.value.displayName;
          }

          _updateContact(
            username,
            value.avatarPath,
            nickname,
            isFriend: (value.state == ContactState.friend.index),
            isTid: value.isTid,
          );
        });
      }
    } else if (chatType == ChatType.channelChat.index) {
      _database.oneChannelInfo(username).getSingleOrNull().then((data) {
        if (data == null) {
          return;
        }
        _lastUUID = data.maxMsgUuid;
        _minUUID = data.minMsgUuid;
        _firstUUID = _firstUUID ?? _lastUUID;
        _channelAttribute = data.attribute;
        _serMemberCount(data.memberCount);
        //群主或者管理员
        bool isOwner = username == data.owner;
        if (isAdminOrOwner) {
          contactLimit = false;
        } else {
          contactLimit = ChannelTask.contactLimit(data.options) && !isOwner;
        }
        if (isOwner) {
          isAdminOrOwner = true;
        }

        AppLogger.d('oneChannelInfo data.memberCount=${data.memberCount}');
        ChannelTask.getMessgae(
          [
            {'channel_id': username, 'id': _lastUUID}
          ],
          20,
        );
        ChannelTask.syncMessgae(currentUser?.userName, 20,
            maxUuid: _firstUUID, minUuid: _minUUID, isSync: false);
        _updateContact(
          data.channelId,
          appSupporAbsolutePath(data.avatarPath),
          data.title,
          isGroupValid: (data.state == ChannelState.normal.index),
        );
        /// 若为DAO频道，则初始化投票floating
        if(isDaoChannelChat() && currentUser?.isGroupValid == true){
          _initVoteFloating();
          openVoteFloating();
          getDaoVotes();
        }
      });
      Get.find<ChannelService>().getAllChannelMemberInfos(
          username, Get.find<AppConfigService>().getUserName() ?? '');      
    } else if (chatType == ChatType.groupChat.index) {
      _database.oneGroupInfo(username).getSingle().then((data) {
        _selectGroupMember(data.groupId);
        _updateContact(
          data.groupId,
          appSupporAbsolutePath(data.avatarPath),
          data.title,
          isGroupValid: data.invalid,
        );
      });
    }
    updateAnnouncement();
    onResumed();
    _topController.getTopMsg(messages, username);
    if (isChannelChat()) {
      subscriptionTopAdmin =
          _topControllerAdmin.getTopMsgAdmin(messages, username, this);
    }
    var replyMsgId = Get.find<AppConfigService>().getReply(getUserName());
    var indexWhere =
        messageList.indexWhere((element) => element.msgId == replyMsgId);
    setReplayData(indexWhere);
  }

  /// 从db同步频道成员标签
  void syncChannelMemberTagFromDb() async {
    var channelId = currentUser?.userName;
    if (channelId == null || channelId.isEmpty) {
      return;
    }
    List<GroupMemberData> members = await _database.allGroupMemberWithTags(channelId).get();
    if(members.isEmpty) return;
    
    /// 保存到缓存
    _storeMapChannelMemberTag(members);
    
    /// 通过username获取对应的msgId去刷新
    List<String> msgIds = _findMsgIdsToRebuildByUsername(_mapChannelMemberTag.keys.toList());    
    if(msgIds.isNotEmpty) {
      update(msgIds);
    }    
  }

  /// 保存到缓存
  void _storeMapChannelMemberTag(List<GroupMemberData> members) {
    if(members.isEmpty) return;
    _mapChannelMemberTag.clear();
    for (var member in members) {
      if(member.tags == null || member.tags!.isEmpty || member.tags == "null") continue;
      List<dynamic> decodedTags = json.decode(member.tags!);
      List<String> tags = decodedTags.map((e) => e.toString()).toList();
      _mapChannelMemberTag[member.username] = tags;
    }
  }

  Map<String, List<String>> getMapChannelMemberTag() {
    return _mapChannelMemberTag;
  }

  List<String> _findMsgIdsToRebuildByUsername(List<String> usernames) {
    List<String> _msgIds = [];
    for (var message in messageList) {
      if (message.direction == Direction.outGoing) continue;
      if (usernames.contains(message.from)) {
        _msgIds.add(message.msgId);
      }
    }
    return _msgIds;
  }

  void getDaoVotes() async {
    /// 获取正在进行的投票
    if(floating == null) return;
    onGoingVotes = await getDaoVotesRequest(currentUser?.userName ?? '', condition: 1) ?? [];
    if(onGoingVotes.isEmpty) {      
      voteNumber.value = '';
    } else {
      // _updateDaoPollBubbleStatus();
      voteNumber.value = '${onGoingVotes.length}';
    }
  }

  Future<Map<String, String?>> getReplayInfo(String? replayMsg,
      {List<MessageData>? messages}) async {
    Map<String, String?> map = <String, String?>{};
    if (replayMsg?.isNotEmpty ?? false) {
      String? replayFilePath, replayFileFragment, replayMsgId;
      Map<String, dynamic> mapData = json.decode(replayMsg!);
      ReplayMessage? msg = ReplayMessage.fromJson(mapData);
      AppLogger.d('ReplayMessage =${msg.toJson()}');
      if (msg.msgId != null && msg.msgId!.isNotEmpty) {
        MessageData? replayMsg;
        if (messages?.isNotEmpty ?? false) {
          try {
            replayMsg =
                messages?.firstWhere((element) => msg.msgId == element.msgId);
          } catch (e) {
            AppLogger.e('ReplayMessage e=${e.toString()}');
          }
        } else {
          replayMsg = await Get.find<AppDatabase>()
              .oneMessage(msg.msgId!)
              .getSingleOrNull();
        }
        AppLogger.d('ReplayMessage =replayMsg=${replayMsg?.toJson()}');
        if (replayMsg != null) {
          if (replayMsg.type == 1 &&
              (replayMsg.filePath?.isNotEmpty ?? false)) {
            replayFilePath = replayMsg.filePath;
            replayFileFragment = replayMsg.fileFragment;
          } else {
            replayFilePath = replayMsg.thumbnailPath;
            replayFileFragment = replayMsg.thumbnailFragment;
          }
          replayMsgId = replayMsg.msgId;
        }
        map['replayFilePath'] = replayFilePath;
        map['replayFileFragment'] = replayFileFragment;
        map['replayMsgId'] = replayMsgId;
      }
    }
    return map;
  }

  Future<Map<String, String?>> getReplayInfo2(String? replayMsg,
      {List<MessageEvent>? messages}) async {
    Map<String, String?> map = <String, String?>{};
    if (replayMsg?.isNotEmpty ?? false) {
      String? replayFilePath, replayFileFragment, replayMsgId;
      Map<String, dynamic> mapData = json.decode(replayMsg!);
      ReplayMessage? msg = ReplayMessage.fromJson(mapData);
      AppLogger.d('ReplayMessage =${msg.toJson()}');
      if (msg.msgId != null &&
          msg.msgId!.isNotEmpty &&
          (msg.type == 1 || msg.type == 3)) {
        MessageData? replayMsg;
        if (messages?.isNotEmpty ?? false) {
          try {
            var e =
                messages?.firstWhere((element) => msg.msgId == element.msgId);
            if (e != null) {
              if (e.type.index == 1 && (e.filePath?.isNotEmpty ?? false)) {
                replayFilePath = e.filePath;
                replayFileFragment = e.fileFragment;
              } else {
                replayFilePath = e.thumbnailPath;
                replayFileFragment = e.thumbnailFragment;
              }
              replayMsgId = e.msgId;
            }
          } catch (e) {}
        } else {
          replayMsg = await Get.find<AppDatabase>()
              .oneMessage(msg.msgId!)
              .getSingleOrNull();
          if (replayMsg != null) {
            if (replayMsg.type == 1 &&
                (replayMsg.filePath?.isNotEmpty ?? false)) {
              replayFilePath = replayMsg.filePath;
              replayFileFragment = replayMsg.fileFragment;
            } else {
              replayFilePath = replayMsg.thumbnailPath;
              replayFileFragment = replayMsg.thumbnailFragment;
            }

            replayMsgId = replayMsg.msgId;
          }
        }
        map['replayFilePath'] = replayFilePath;
        map['replayFileFragment'] = replayFileFragment;
        map['replayMsgId'] = replayMsgId;
        AppLogger.d('ReplayMessage replayFilePath=$replayFilePath');
      }
    }
    return map;
  }

  _selectGroupMember(String groupId) async {
    _database.allGroupMember(groupId).get().then((value) {
      _groupMember.clear();
      for (var m in value) {
        _groupMember.add(m.username);
      }

      if (_currentUser?.value.chatType == ChatType.groupChat.index &&
          _currentUser?.value.isGroupValid == true) {
        if (_groupMember.length < 2) {
          isTabooType.value = true;
          tabooType = TabooType.groupOnlySelf;
          _mute();
        } else {
          if (isTabooType.value) {
            isTabooType.value = false;
            _mute();
          }
          tabooType = TabooType.none;
        }
      }
    });
  }

  updateMaxUUID(String uuid, String owner) {
    _taskQueue.submit(_updateMaxUUID, {'uuid': uuid, 'owner': owner});
  }

  _updateMaxUUID(Map map) {
    String uuid = map['uuid'];
    String owner = map['owner'];

    /// 记录  uuid 排序
    // _currentMsgCount++;
    _firstUUID ??= uuid;
    _lastUUID ??= uuid;
    if (uuid.compareTo(_firstUUID!) <= 0) {
      _firstUUID = uuid;
    }
    if (uuid.compareTo(_lastUUID!) >= 0) {
      _lastUUID = uuid;
      ChannelTask.updateChannelMaxUuid(owner, uuid);
    }
  }

  @override
  void onDetached() {
    AppLogger.d("MessagePage onDetached");
  }

  @override
  void onInactive() {
    AppLogger.d('MessagePage onInactive');
  }

  @override
  void onPaused() {
    AppLogger.d('MessagePage onPaused');
    _timerChannel?.cancel();
    VoicePlayerNew().stop();
  }

  @override
  void onResumed() {
    AppLogger.d('MessagePage onResumed');
    var username = currentUser?.userName;
    var chatType = currentUser?.chatType;
    if (username == null || chatType == null) {
      return;
    }
    if (chatType == ChatType.channelChat.index) {
      _timerChannel?.cancel();
      _timerChannel = Timer.periodic(const Duration(seconds: 5), (timer) {
        // > msgID 获取，最新消息
        ChannelTask.getMessgae(
          [
            {'channel_id': username, 'id': _lastUUID}
          ],
          20,
        );
      });
    } else if (chatType == ChatType.officialChat.index && !clearChatHistory) {
      OfficialAccountTask.instance.getNoticeList(page: 1, size: 10);
      OfficialAccountTask.instance.getNoticeRole();
    }
  }

  void call(BuildContext context, bool isVideo) {
    if (currentUser?.userName != null) {
        showBottomDialogCommonWithCancel(context, widgets: [
          getBottomSheetItemSimple(context, L.voice_phone.tr,
              textColor: AppColors.appDefault,
              itemCallBack: () => {
                    WebRtcCallHelper.instance.mackCall(
                        currentUser!.userName!, false, currentUser!.displayName)
                  }),
          Container(
            height: 1.h,
            color: AppColors.backgroundGray,
          ),
          getBottomSheetItemSimple(context, L.video_phone.tr,
              textColor: AppColors.appDefault,
              itemCallBack: () => {
                    WebRtcCallHelper.instance.mackCall(
                        currentUser!.userName!, true, currentUser!.displayName)
                  }),
          Container(
            height: 10.h,
            color: AppColors.colorFFF3F3F3,
          ),
        ]);
    }
  }

  void changeMore(BuildContext context) {
    bool singleChat = currentUser?.chatType == ChatType.singleChat.index;
    List<Widget> list = [];
    if (isMeetingRobot()) {
      if (!rxSelfDestruct.value && singleChat && Config.isOversea) {
        list.add(getBottomSheetItemSimpleAndImg(context, L.collect_money.tr,
            imagePath: R.iconCollectMoney,
            itemCallBack: () => {
                  WalletManage().changeConnectMoney(
                      context, _currentUser?.value.userName ?? "")
                }));
      }
    } else {
      list = [
        getBottomSheetItemSimpleAndImg(
          context,
          rxSelfDestruct.value ? L.chat_img.tr : L.chat_img_video.tr,
          imagePath: R.icChatPhotos,
          itemCallBack: () => {
            showDialog(
              barrierDismissible: false,
              context: context,
              useSafeArea: false,
              builder: (_) {
                return PhotosWidget(
                    type: rxSelfDestruct.value
                        ? RequestType.image
                        : RequestType.common,
                    haveCamera: false,
                    onAttachmentSelect: (List<AttachmentEntity> attachments) {
                      onAttachmentSelect(attachments); // 相册选择完成
                      Navigator.pop(context);
                    });
              },
            ),
          },
        ),
        Container(
          height: 1.h,
          color: AppColors.backgroundGray,
        ),
        getBottomSheetItemSimpleAndImg(context,
            rxSelfDestruct.value ? L.chat_camera_pic.tr : L.chat_camera.tr,
            imagePath: R.icChatPictures,
            itemCallBack: () => {
                  _onCamera(context, false),
                }),
        Container(
          height: 1.h,
          color: AppColors.backgroundGray,
        ),
        if (!rxSelfDestruct.value && singleChat)
          getBottomSheetItemSimpleAndImg(context, L.voice_phone.tr,
              imagePath: R.icChatVoice,
              itemCallBack: () => {
                    WebRtcCallHelper.instance.mackCall(
                        currentUser!.userName!, false, currentUser!.displayName)
                  }),
        if (!rxSelfDestruct.value && singleChat && Config.isOversea)
          Container(
            height: 1.h,
            color: AppColors.backgroundGray,
          ),
        if (!rxSelfDestruct.value && singleChat)
          getBottomSheetItemSimpleAndImg(context, L.video_phone.tr,
              imagePath: R.icChatVideo,
              itemCallBack: () => {
                    WebRtcCallHelper.instance.mackCall(
                        currentUser!.userName!, true, currentUser!.displayName)
                  }),
        if (!rxSelfDestruct.value && singleChat && Config.isOversea)
          Container(
            height: 1.h,
            color: AppColors.backgroundGray,
          ),
        if (!rxSelfDestruct.value && singleChat && Config.isOversea)
          getBottomSheetItemSimpleAndImg(context, L.send_token.tr,
              imagePath: R.iconTransfer,
              itemCallBack: () => {
                    WalletManage().sendToken(
                        context,
                        _currentUser?.value.userName ?? "",
                        _currentUser?.value.displayName ?? "")
                  }),
        if (!rxSelfDestruct.value && singleChat && Config.isOversea)
          Container(
            height: 1.h,
            color: AppColors.backgroundGray,
          ),
        if (!rxSelfDestruct.value && singleChat && Config.isOversea)
          getBottomSheetItemSimpleAndImg(context, L.collect_money.tr,
              imagePath: R.iconCollectMoney,
              itemCallBack: () => {
                    WalletManage().changeConnectMoney(
                        context, _currentUser?.value.userName ?? "")
                  }),
        if (!rxSelfDestruct.value && singleChat && Config.isOversea)
          Container(
            height: 1.h,
            color: AppColors.backgroundGray,
          ),
        if (!rxSelfDestruct.value)
          getBottomSheetItemSimpleAndImg(context, L.the_file.tr,
              imagePath: R.icChatFile,
              itemCallBack: () => {
                    _fileSelect(),
                  }),
        if (!rxSelfDestruct.value && !singleChat && Config.isOversea)
          Container(
            height: 1.h,
            color: AppColors.backgroundGray,
          ),
        if (!rxSelfDestruct.value && !singleChat && Config.isOversea)
          getBottomSheetItemSimpleAndImg(context, L.the_red_envelope.tr,
              imagePath: R.icChatHongBao,
              itemCallBack: () => {
                    _sendHongBao(context),
                  }),
        if (!rxSelfDestruct.value && isDaoChannelChat() && Config.isOversea)
          Container(
            height: 1.h,
            color: AppColors.backgroundGray,
          ),
        if (!rxSelfDestruct.value && isDaoChannelChat() && Config.isOversea)
          getBottomSheetItemSimpleAndImg(
            context, 
            L.poll.tr,
            imagePath: R.icChatPoll,
            itemCallBack: onVotePress,
          ),
        Container(
          height: 10.h,
          color: AppColors.colorFFF3F3F3,
        )
      ];
    }
    showBottomDialogCommonWithCancel(context, widgets: list);
  }

  void _onCamera(BuildContext context, bool isVideo) async {
    final AssetEntity? entity = await CameraPicker.pickFromCamera(
      context,
      pickerConfig: CameraPickerConfig(
        enableRecording: rxSelfDestruct.value ? false : true,
        theme: CameraPicker.themeData(Theme.of(context).colorScheme.primary),
        textDelegate: currentLanguageIsSimpleChinese()
            ? const CameraPickerTextDelegate()
            : const EnglishCameraPickerTextDelegate(),
        shouldDeletePreviewFile: true,
        lockCaptureOrientation: DeviceOrientation.portraitUp,
        enableScaledPreview: false,
      ),
    );
    List<AttachmentEntity> assets = [];
    if (entity != null) {
      MessageType type = MessageType.file;
      switch (entity.type) {
        case AssetType.image:
          type = MessageType.image;
          break;
        case AssetType.video:
          type = MessageType.video;
          break;
        default:
          return;
      }
      File? file = await entity.file;
      assets.add(AttachmentEntity(type, filePath: file?.path));
      onAttachmentSelect(assets);
    }
  }

  Future<void> _fileSelect() async {
    var result = await FilePicker.platform.pickFiles(allowMultiple: true);
    if (result == null) {
      return;
    }
    List<String?> filePaths = result.paths.map((path) => path).toList();
    List<AttachmentEntity> attachments = [];
    bool isToast = false;
    for (String? filePath in filePaths) {
      var length = File(filePath ?? '').lengthSync();
      if (length > Config.selectFileMaxBytes) {
        isToast = true;
        continue;
      }

      attachments.add(AttachmentEntity(MessageType.file, filePath: filePath));
    }
    onAttachmentSelect(attachments); // 文件选择完成
    if (isToast) {
      toast(L.file_select_max_length.trParams(
          {'len': byteFormat(Config.selectFileMaxBytes, fractionDigits: 0)}));
    }
  }

//old
  Future<void> _sendHongBao(BuildContext context,
      {String msgId = '1', String? ext1,String?extension}) async {
    HongBaoTask().showHongBao(context, getUserName(), getChatType(),
        msgId: msgId, ext1: ext1,extension:extension);
  }
//old
  Future<void> sendHongBao(BuildContext context,
      {String? msgId, String? ext1,String? extension}) async {
    _sendHongBao(context, msgId: msgId ?? '1', ext1: ext1, extension:extension);
  }

  setWallpaper() async {
    var userName = currentUser?.userName;
    if (userName != null) {
      if (currentUser?.chatType == ChatType.singleChat.index) {
        AppLogger.d("setWallpaper userName==${currentUser?.userName}");
        ContactData? data =
            await _database.oneContact(userName).getSingleOrNull();
        AppLogger.d("oneChannelInfo setWallPaper data==$data");
        if (data != null) {
          currentUser?.isTid = data.isTid;
          String? path = appSupporAbsolutePath(data.chatBackgroundPath);
          String? url = data.chatBackgroundUrl;
          if (path != null) {
            if (!File(path).existsSync()) {
              await downloadFile(url, savePath: path, isComPressImageJPG: true);
            }
            wallPaperPath.value = path;
            fromNameTextColor.value = Colors.white;
          } else {
            wallPaperPath.value = "";
            fromNameTextColor.value = Colors.white;
          }
        } else {
          wallPaperPath.value = "";
          fromNameTextColor.value = Colors.white;
        }
      } else if (currentUser?.chatType == ChatType.channelChat.index) {
        var data = await _database.oneChannelInfo(userName).getSingleOrNull();
        AppLogger.d("oneChannelInfo setWallPaper data==$data");
        if (data != null) {
          String? path = appSupporAbsolutePath(data.backgroundPath);
          String? url = data.backgroundUrl;
          if (path != null && url != null) {
            if (!File(path).existsSync()) {
              await downloadFile(url, savePath: path, isComPressImageJPG: true);
            }
            wallPaperPath.value = path;
            fromNameTextColor.value = Colors.white;
          } else {
            wallPaperPath.value = "";
            fromNameTextColor.value = Colors.white;
          }
        } else {
          wallPaperPath.value = "";
          fromNameTextColor.value = Colors.white;
        }
      }
    }
  }

  String getChatWallpaper() {
    return wallPaperPath.value;
  }

  bool isSingleChat() {
    return currentUser?.chatType == 0;
  }

  bool isOfficialChat() {
    return currentUser?.chatType == ChatType.officialChat.index;
  }

  bool isMeetingRobot() {
    return currentUser?.userName == Config.meetingRobot;
  }

  void _selectContactForAt(String str) {
    AppLogger.d('_selectContactForAt $str');
    if (str.isNotEmpty) {
      _atContactController?.insertAtText(str, _textEditingControllerInput);
      _rxInputTextChange.value = _textEditingControllerInput.text;
      text = true;
    }
  }

  void _serMemberCount(int? memberCount) {
    _memberCount = memberCount?.obs;
    update(["message_page_title"]);
  }

  sendDefaultSticker(String path) {
    String msgId = uuid();

    String? username = _currentUser?.value.userName;
    int? chatType = _currentUser?.value.chatType;
    if (username == null || chatType == null) {
      return;
    }
    _sendMessage(
      MessageEvent(
        msgId,
        owner: username,
        type: path.startsWith(R.stickerRabbitAssertPrefix)
            ? MessageType.stickerDefaultRabbit
            : path.startsWith(R.stickerEmojiRabbitAssertPrefix)
                ? MessageType.stickerDefaultEmoji
                : MessageType.stickerDefault,
        chatType: ChatType.values[chatType],
        filePath: path,
        body: path,
        direction: Direction.outGoing,
        dateTime: TimeTask.instance.getNowDateTime(),
      ),
    );
  }

  void openChannelMomentView() {
    var data = {
      "current_user": _currentUser?.value,
      "member_count": _memberCount?.value,
    };
    Get.toNamed(Routes.ChannelMomentView, arguments: data);
  }

  /// 阅后即焚状态变化
  void selfDestructChange() {
    if (currentUser?.chatType == ChatType.singleChat.index) {
      rxSelfDestruct.value = !rxSelfDestruct.value;
      if (rxSelfDestruct.value) {
        _replyController?.setReplayData(null);
      }
      isSticker.value = false;
      update(['message_page_title']);
    }
  }

  /// 气泡点击
  void itemOnTap(String msgId) {
    MessageEvent? info = findMessageEvent(msgId);
    if (info == null ||
        !info.selfDestruct ||
        info.direction == Direction.outGoing ||
        _mapCountdown.containsKey(info.msgId)) {
      return;
    }

    if (info.type != MessageType.text) {
      messageList.remove(info);
      CmdTask.sendBurn(
          _currentUser?.value.userName ?? '', [info.msgId], info.chatType);

      deleteFile(info.filePath);
      deleteFile(info.thumbnailPath);
      return;
    }

    var timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      MessageEvent? info = findMessageEvent(msgId);
      if (info == null) {
        timer.cancel();
        _mapCountdown.remove(msgId);
        return;
      }

      if (info.countdown > 1) {
        --info.countdown;
        update([info.msgId]);
      } else {
        timer.cancel();
        _mapCountdown.remove(msgId);
        messageList.remove(info);
      }
    });

    _mapCountdown[info.msgId] = timer;
    info.countdown = Config.selfDestructCountdown;
    update([info.msgId]);
    CmdTask.sendBurn(
        _currentUser?.value.userName ?? '', [info.msgId], info.chatType);
  }

  /// 定时器取消
  void allTimerCancel() {
    for (var element in _mapCountdown.values) {
      element.cancel();
    }
    _mapCountdown.clear();
  }

  MessageEvent? findMessageEvent(String msgId) {
    MessageEvent? info;
    for (var element in messageList) {
      if (element.msgId == msgId) {
        info = element;
        break;
      }
    }
    return info;
  }

  Future<Uint8List?> getImageUnit8ByPath(
    String imagePath,
    String fragment,
  ) async {
    Uint8List? data;
    try {
      data =
          imagesWeakMap.firstWhere((element) => element.path == imagePath).data;
    } catch (e) {
      AppLogger.e("getImageUnit8ByPath Exception==$e");
    }
    if (data == null) {
      if (imagePath.endsWith(Config.enc)) {
        data = await ChatioNative.utilFileDecryptToMemory(imagePath, fragment);
      } else {
        File file = File(imagePath);
        data = file.readAsBytesSync();
      }
      imagesWeakMap.add(WeakImageBean(imagePath, data));
    }
    return data;
  }

  Uint8List? getImageCache(String? path) {
    if (path == null) {
      return null;
    }
    Uint8List? data;
    for (var element in imagesWeakMap) {
      if (element.path == path) {
        data = element.data;
        break;
      }
    }
    return data;
  }

  onBottomMessageForward(BuildContext context) async {
    showBottomDialogCommonWithCancel(context, widgets: [
      getBottomSheetItemSimple(context, L.one_by_one_forward.tr,
          itemCallBack: () async {
        bool result = await messageForwardJudge(context, false);
        if (result) {
          changeMessageCanMultiSelect(false);
        }
      }),
      getBottomSheetItemSimple(context, L.combine_and_forward.tr,
          itemCallBack: () async {
        bool result = await messageForwardJudge(context, true);
        if (result) {
          changeMessageCanMultiSelect(false);
        }
      })
    ]);
  }

  Future<bool> messageForwardJudge(BuildContext context, bool isMerge) async {
    if (selectedMessageEventList.isEmpty) {
      return false;
    }
    List<MessageEvent> removeData = [];
    for (MessageEvent message in selectedMessageEventList) {
      if (message.type == MessageType.audio ||
          message.selfDestruct == true ||
          ((message.type == MessageType.image ||
                  message.type == MessageType.video ||
                  message.type == MessageType.file) &&
              !File(message.filePath ?? "").existsSync()) ||
          message.type == MessageType.call ||
          message.type == MessageType.msgMergeForward ||
          message.type == MessageType.contactCard ||
          message.state == BubbleItemState.FAILED ||
          message.state == BubbleItemState.SENSITIVE ||
          message.type == MessageType.stickerDefault ||
          message.type == MessageType.stickerDefaultRabbit ||
          message.type == MessageType.stickerDefaultEmoji ||
          message.type == MessageType.walletAddress ||
          message.type == MessageType.sticker ||
          message.type == MessageType.walletTransaction ||
          message.type == MessageType.walletBill ||
          message.type == MessageType.moneyExchange ||
          message.type == MessageType.hongbao) {
        removeData.add(message);
      }
    }
    selectedMessageEventList.sort((a, b) {
      return a.dateTime.millisecondsSinceEpoch
          .compareTo(b.dateTime.millisecondsSinceEpoch);
    });
    if (removeData.isEmpty) {
      if (isMerge) {
        return await margeMessageForward(context, selectedMessageEventList);
      } else {
        return await onForwardMessage(selectedMessageEventList);
      }
    } else {
      showBottomDialogCommonWithCancel(context, widgets: [
        Container(
          padding:
              const EdgeInsets.only(left: 15, right: 15, top: 10, bottom: 10).r,
          child: Center(
            child: Text(
              L.forword_tips.tr,
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 16.sp,
                color: AppColors.colorFF333333,
              ),
            ),
          ),
        ),
        getBottomSheetItemSimple(context, L.continue_.tr,
            itemCallBack: () async {
          for (MessageEvent message in removeData) {
            selectedMessageEventList.remove(message);
          }
          if (selectedMessageEventList.isEmpty) {
            changeMessageCanMultiSelect(false);
            toast(L.no_messages_can_be_forwarded.tr);
          } else {
            if (isMerge) {
              var bool =
                  await margeMessageForward(context, selectedMessageEventList);
              if (bool) {
                changeMessageCanMultiSelect(false);
              }
            } else {
              var bool = await onForwardMessage(selectedMessageEventList);
              if (bool) {
                changeMessageCanMultiSelect(false);
              }
            }
          }
        })
      ]);
      return false;
    }
  }

  Future<bool> margeMessageForward(
      BuildContext context, List<MessageEvent> messageEventList) async {
    List<ContactData> selectable =
        await Get.find<AppDatabase>().allFriendContact().get();
    var resultData = await showDialog(
      context: Get.context!,
      builder: (_) {
        return ResentMessagePage(
          contactDatas: selectable,
          showGroup: true,
        );
      },
    );
    if (resultData == null) {
      return false;
    }
    var db = Get.find<AppDatabase>();
    var contacts = resultData as SelectContactInfo;
    for (ContactInfo c in contacts.contacts) {
      List<String>? toNumbers;
      if (c.chatType == ChatType.groupChat) {
        var userName = Get.find<AppConfigService>().getUserName();
        toNumbers =
            await db.oneGroupMemberExcludeUser(c.userName, userName!).get();
      } else if (c.chatType == ChatType.channelChat) {
        AppLogger.d('forwardMessage message');
        var channelInfo = await db.oneChannelInfo(c.userName).get();
        AppLogger.d('forwardMessage message ${channelInfo.length}');

        if (channelInfo.isNotEmpty) {
          var countNumber = channelInfo.first.memberCount ?? 0;
          AppLogger.d('forwardMessage message countNumber=$countNumber');
          if (countNumber >= Config.maxNumberCount) {
            if (!CommUtil.instance
                .canSend(messageEventList.first, isForword: true)) {
              toast(L.operation_restricted.tr);
              return false;
            }
          }
        }
      }

      String title = "";
      if (_currentUser?.value.chatType == ChatType.groupChat.index) {
        title = L.group_chat.tr;
      } else if (_currentUser?.value.chatType == ChatType.channelChat.index) {
        title = L.channel_group.tr;
      } else if (_currentUser?.value.chatType == ChatType.singleChat.index) {
        title = _currentUser?.value.displayName ?? "";
      } else if (_currentUser?.value.chatType == ChatType.officialChat.index) {
        title = getOfficeDisplayName();
      }
      MergeSendMsgTask task = MergeSendMsgTask(
        title: title,
        messageList: messageEventList,
        userName: c.userName,
        chatType: contacts.type,
        toNumbers: toNumbers,
      );
      task.process();
    }
    return true;
  }

  onMessageSelected(MessageEvent msgEvent, {bool clean = false}) {
    if (clean) {
      selectedMessageEventList.clear();
    }
    if (selectedMessageEventList.contains(msgEvent)) {
      selectedMessageEventList.remove(msgEvent);
    } else {
      selectedMessageEventList.add(msgEvent);
    }
    update([msgEvent.msgId]);
  }

  onMessageMultiSelectCancel() {
    isMessageCanMultiSelect.value = false;
    isComplaintChangedMsgs.value = false;
    selectedMessageEventList.clear();
  }

  Widget getReplayWidget(BuildContext context) {
    return _replyController?.createReplayWidget(context) ?? Container();
  }

  void setReplayData(int index) {
    if (index < 0) {
      return;
    }
    MessageEvent messageData = messageList[index];
    _replyController?.setReplayData(messageData);
  }

  void msgTop(int index) {
    MessageEvent messageData = messageList[index];
    List<String> msgTopID =
        Get.find<AppConfigService>().getMsgTopID(messageData.owner) ?? [];
    if (msgTopID.contains(messageData.msgId)) {
      msgTopID.remove(messageData.msgId);
    }
    _topController.updateTopMsg(messageData.msgId);
    msgTopID.add(messageData.msgId);
    Get.find<AppConfigService>().saveMsgTop(messageData.owner, msgTopID);
    topController.setMsgData(messageData);
  }

  Future<bool> msgTopAdmin(int index) async {
    bool ret = false;
    MessageEvent messageData = messageList[index];
    var modelData = await getChannelInfoRequest(getUserName());
    var options = modelData.options;
    ChannelOptions? op;
    if (options != null) {
      var decode = json.decode(options);
      op = ChannelOptions.fromJson(decode);
    }
    var nowTime = TimeTask.instance.getNowTime();
    op ??= ChannelOptions();
    op.topMsgDataList ??= [];
    var topMsgAdminData =
        TopMsgAdminData(msgUuid: messageData.uuid, topTime: nowTime);
    op.topMsgDataList
        ?.removeWhere((element) => element.msgUuid == messageData.uuid);
    op.topMsgDataList?.add(topMsgAdminData);
    op.count = (op.count ?? 0) + 1;
    var encodeOptions = json.encode(op.toJson());
    var value = await updateChannelInfoRequest(
      ChannelInfoModelData(
          id: modelData.id,
          title: modelData.title,
          describe: modelData.describe,
          avatar: modelData.avatar,
          announcement: modelData.announcement,
          options: encodeOptions,
          wallpaper: modelData.wallpaper),
    );
    if (value) {
      ChannelTask.updateInfo(modelData.id ?? "", options: encodeOptions);
      var mapData = ChannelOperation(
        action: ChannelOption.topMsg,
        time: nowTime,
        chatType: ChatType.channelChat.index,
        type: MessageType.channelOpera.index,
        targetId: [messageData.msgId],
        msgId: uuid(),
        owner: currentUser?.userName ?? '',
        body: "",
      ).toJson();
      var send = json.encode(mapData);
      var res = await sendMessageRequest(currentUser?.userName ?? '', send);
      if (res != null) {
        AppLogger.d('msgTopAdmin.......');
        _topControllerAdmin.updateTopMsgAdmin(messageData.msgId, false);
        toast(L.top_success.tr);
        ret = true;
      } else {
        toast(L.failed_top.tr);
      }
    } else {
      toast(L.failed_top.tr);
    }
    return ret;
  }

  Future<bool> msgTopAdminCancel(MessageEvent messageData) async {
    bool ret = false;
    var modelData = await getChannelInfoRequest(getUserName());
    var options = modelData.options;
    ChannelOptions? op;
    if (options != null) {
      var decode = json.decode(options);
      op = ChannelOptions.fromJson(decode);
    }
    var nowTime = TimeTask.instance.getNowTime();
    op ??= ChannelOptions();
    op.count = (op.count ?? 0) + 1;
    op.topMsgDataList ??= [];
    op.topMsgDataList
        ?.removeWhere((element) => element.msgUuid == messageData.uuid);
    if (op.topMsgDataList?.isEmpty ?? true) {
      op.count = null;
    }
    var encodeOptions = json.encode(op.toJson());
    var value = await updateChannelInfoRequest(
      ChannelInfoModelData(
          id: modelData.id,
          title: modelData.title,
          describe: modelData.describe,
          avatar: modelData.avatar,
          announcement: modelData.announcement,
          options: encodeOptions,
          wallpaper: modelData.wallpaper),
    );
    if (value) {
      ChannelTask.updateInfo(modelData.id ?? "", options: encodeOptions);
      var mapData = ChannelOperation(
        action: ChannelOption.topMsgCancel,
        time: nowTime,
        chatType: ChatType.channelChat.index,
        type: MessageType.channelOpera.index,
        targetId: [messageData.msgId],
        msgId: uuid(),
        owner: currentUser?.userName ?? '',
        body: "",
      ).toJson();
      var send = json.encode(mapData);
      var res = await sendMessageRequest(currentUser?.userName ?? '', send);
      if (res != null) {
        AppLogger.d('msgTopAdminCancel.......');
        _topControllerAdmin.updateTopMsgAdmin(messageData.msgId, true);
        // toast(L.top_success.tr);
        ret = true;
      }
    }
    return ret;
  }

  void replayOnClick(String? msgId, {bool isSearch = false}) {
    if (msgId != null && _messageList.length > 1) {
      int index = _messageList.indexWhere((element) => element.msgId == msgId);
      if (index == -1) {
        return;
      }
      if (_messageList[index].isUndo) {
        return;
      }
      bool has = false;
      try {
        ItemPosition position = _currentItemPositions
            .firstWhere((element) => element.index == index);
        if (position.itemTrailingEdge < 1) {
          //避免一部分显示出来的那一种
          has = true;
        }
      } catch (e) {}
      if (!has || isSearch) {
        _jumpToCount++;
        isJump = true;
        _itemScrollController.jumpTo(index: index);
      }
      var m = _messageList[index];
      m.replayClicked = true;
      update([msgId]);
      Future.delayed(const Duration(milliseconds: 2000), () {
        m.replayClicked = false;
        update([msgId]);
      });
    }
  }

  bool isCanMultiMessage(MessageEvent msgEvent) {
    return isMessageCanMultiSelect.value &&
        ((!isComplaintChangedMsgs.value &&
                msgEvent.type != MessageType.date &&
                msgEvent.type != MessageType.tip &&
                msgEvent.type != MessageType.group_operation &&
                msgEvent.type != MessageType.other &&
                msgEvent.type != MessageType.channelOpera &&
                !msgEvent.isUndo) ||
            isComplaintChangedMsgs.value &&
                (msgEvent.type == MessageType.text ||
                    msgEvent.type == MessageType.image ||
                    msgEvent.type == MessageType.video ||
                    msgEvent.type == MessageType.audio ||
                    msgEvent.type == MessageType.file));
  }

  Future<void> audioRecordDialog() async {
    if (rxRecordStatus.value == RecordStatus.none) {
      return;
    }

    return showDialog(
      barrierDismissible: false,
      context: Get.context!,
      builder: (_) {
        return AlertDialog(
          content: Text(L.record_send.tr),
          actionsAlignment: MainAxisAlignment.end,
          actions: [
            TextButton(
              onPressed: () => Get.back(),
              child: Text(L.cancel.tr),
            ),
            TextButton(
              onPressed: () {
                rxRecordSend.value = true;
                Get.back();
              },
              child: Text(L.other_send.tr),
            ),
          ],
        );
      },
    );
  }

  bool isShowTextField() {
    var isRecordIM = rxIsRecordIM.value;
    return !isRecordIM;
  }

  void onRecordFinished(String? filePath, {Duration? duration}) {
    onSendVoiceMessage(filePath, duration);
  }

  void undoMsgEdit(MessageEvent msgEvent) {
    AppLogger.d("onPointerUp onUndoEditTapUp ");
    String newText = textEditingControllerInput.text + (msgEvent.body ?? '');

    textEditingControllerInput.value = TextEditingValue(
      text: newText,
      selection:
          TextSelection.fromPosition(TextPosition(offset: newText.length)),
    );

    if (textEditingControllerInput.text.isNotEmpty) {
      text = true;
    }
  }

  bottomHeightChange(double bottomHeight, BuildContext context) {
    if (keyBoardHeight == 0) {
      keyBoardHeight = Get.find<AppConfigService>().getKeyBoardHeight();
    }
    if (isSticker.value &&
        keyBoardUtil.curBottomHeight == 0 &&
        keyBoardHeight != 0) {
      ///当前是表情且已经存了键盘高度去展开键盘
      containerFillBottomHeight.value = keyBoardHeight;
      isSticker.value = false;
      keyBoardUtil.curBottomHeight = bottomHeight;
    } else {
      if (keyBoardUtil.curBottomHeight == 0) {
        ///默认状态
        containerFillBottomHeight.value = bottomHeight;
      }
      if (keyBoardUtil.curBottomHeight > bottomHeight) {
        ///键盘收起开始
        containerFillBottomHeight.value = bottomHeight;
      }
      if (!isSticker.value) {
        ///为了表情能直接正常显示再显示键盘的时候给表情高度赋值
        stickerHeight.value = bottomHeight;
      }
      keyBoardUtil.startListener(context, show: (bottom) {
        ///键盘完全显示
        isSticker.value = false;
        containerFillBottomHeight.value = bottom;
        stickerHeight.value = bottom;
      }, hide: (bottom) {
        ///键盘完全隐藏
        if (stickerHeight.value == 0) {
          stickerHeight.value =
              Get.find<AppConfigService>().getKeyBoardHeight();
        }
      });
    }
  }

  void insertAt(String? form) async {
    String? groupId = currentUser?.userName;
    if (groupId == null ||
        groupId.isEmpty ||
        form == null ||
        !showInputView() ||
        isTabooType.value) {
      return;
    }
    _atContactController
        ?.insertAt(groupId, form, channelOwner, channelAdminList)
        .then((value) {
      if (value) {
        VibrationUtil.instance.vibrationHeavyImpact();
      }
    });
  }

  bool showInputView() {
    bool visible = true;

    if ((currentUser?.chatType == ChatType.channelChat.index ||
            currentUser?.chatType == ChatType.groupChat.index) &&
        currentUser?.isGroupValid != true) {
      visible = false;
    }
    return visible;
  }

  void toBottom() async {
    _jump = true;
    await addCurrentMessageEvtToList();
    rxShowJump.value = false;
    jumpTo();
  }

  _addCurrentLastMessageEvtToList() async {
    if (_currentLastMessageEvt.isNotEmpty) {
      var list = [];
      MessageRecvEvent? exitList, notExitList;
      for (MessageRecvEvent e in _currentLastMessageEvt) {
        list.add(e);
        if (e.exist) {
          if (exitList == null) {
            exitList = e;
          } else {
            exitList.messages.addAll(e.messages);
          }
        } else {
          if (notExitList == null) {
            notExitList = e;
          } else {
            notExitList.messages.addAll(e.messages);
          }
        }
      }
      if (exitList != null && exitList.messages.isNotEmpty) {
        await _messageDataHandle(exitList);
      }
      if (notExitList != null && notExitList.messages.isNotEmpty) {
        await _messageDataHandle(notExitList);
      }
      _currentLastMessageEvt.removeWhere((value) {
        return list.contains(value);
      });
    }
  }

  addCurrentMessageEvtToList() async {
    if (_currentMessageEvt.isNotEmpty) {
      var list = [];
      MessageRecvEvent? exitList, notExitList;
      for (MessageRecvEvent e in _currentMessageEvt) {
        list.add(e);
        if (e.exist) {
          if (exitList == null) {
            exitList = e;
          } else {
            exitList.messages.addAll(e.messages);
          }
        } else {
          if (notExitList == null) {
            notExitList = e;
          } else {
            notExitList.messages.addAll(e.messages);
          }
        }
      }
      if (exitList != null && exitList.messages.isNotEmpty) {
        await _messageDataHandle(exitList);
      }
      if (notExitList != null && notExitList.messages.isNotEmpty) {
        await _messageDataHandle(notExitList);
      }
      _currentMessageEvt.removeWhere((value) {
        return list.contains(value);
      });
    }
    // clickLongNewMessage();
  }

  clickNewMessage({bool delay = false}) async {
    await addCurrentMessageEvtToList();
    if (delay) {
      // await Future.delayed(const Duration(milliseconds: 200));
      return;
    }
    AppLogger.d('clickNewMessage delay =$delay');
    if (newMessageCount.isNotEmpty) {
      var first = newMessageCount.last;
      replayOnClick(first.msgId);
      newMessageCount.removeWhere((element) => element.msgId == first.msgId);
    }
  }

  clickLongNewMessage() async {
    var list = newMessageCount.copy();
    await addCurrentMessageEvtToList();
    newMessageCount.removeWhere((element) => list.contains(element));
  }

  void setShowBottom(bool scroll) {
    var itemPosition = _currentItemPositions.first;
    if (messageList.length > 10 && itemPosition.index > 3) {
      if (!rxShowJump.value) {
        rxShowJump.value = true;
      }
    } else {
      if (rxShowJump.value) {
        rxShowJump.value = false;
        addCurrentMessageEvtToList();
      }
    }
  }

  void setComplaintChange(bool show) {
    isComplaintChangedMsgs.value = show;
    if (show) {
      isMessageCanMultiSelect.value = true;
    }
  }

  void changeComplaint(BuildContext context) async {
    if (!await ComplaintHelper.isComplaintTimes()) {
      toast(L.complaints_times.tr);
      return;
    }
    changeMessageCanMultiSelect(false);
    await SmartDialog.show(
      builder: (context) {
        return ComplaintView(this);
      },
      useAnimation: false,
      backDismiss: true,
      clickMaskDismiss: false,
      keepSingle: true,
    );
  }

  void changeMessageCanMultiSelect(bool change) {
    isMessageCanMultiSelect.value = change;
    if (!change) {
      isComplaintChangedMsgs.value = false;
    }
  }

  Widget buildGroupIcon() {
    bool visible = false;
    Widget? child;

    if (currentUser?.chatType == ChatType.groupChat.index ||
        currentUser?.chatType == ChatType.channelChat.index) {
      visible = true;

      if (currentUser?.chatType == ChatType.channelChat.index) {
        if (isDaoChannelChat()) {
          String? chainId = currentUser?.userName == null ? null : Get.find<SessionController>().daoChannelChainId(currentUser!.userName!);
          var chainName;
          if (chainId != null && chainId.isNotEmpty) {
            var _chainId = int.tryParse(chainId);
            if(_chainId != null && Get.isRegistered<WalletController>()) {
              NetworkInfoData? network;
              try {
                network = Get.find<WalletController>().allListNet.firstWhereOrNull((network) => network.chainid == _chainId);
                if(network != null){
                  chainName = network.name;
                }
              } catch (e) {
                AppLogger.e("_buildGroupIcon Get.find<WalletController>() error: $e");
              }            
            }
          }
          child = chainName != null 
          ? Text(
              "[$chainName]",
              style: TextStyle(
                color: AppColors.colorFFB3B3B3,
                fontWeight: FontWeight.bold,
                fontSize: 9.sp,
              ),
            )
          : SizedBox.shrink();
          // child = Image.asset(
          //   R.icoDao,
          //   width: 18,
          //   height: 14,
          // );
        }
      } else {
        child = Image.asset(
          R.icoPrivate,
          width: 18,
          height: 14,
        );
      }
    }
    return Visibility(
      visible: visible,
      child: child ?? Container(),
    );
  }

  void updateAnnouncement() {
    var userName = currentUser?.userName ?? '';
    if (getChatType() != ChatType.singleChat && userName.isNotEmpty) {
      var announcement = AnnouncementTask.getAnnouncement(userName);
      AppLogger.d('updateAnnouncement $announcement');
      if (announcement.isNotEmpty) {
        _topController.setAnnouncement(announcement);
      }
    }
  }

  void toBannerDetailView() {
    var userName = currentUser?.userName ?? '';
    if (getChatType() != ChatType.singleChat && userName.isNotEmpty) {
      var mode = AnnouncementTask.getLocalAnnouncementMode(userName);
      if (mode != null) {
        AnnouncementTask.updateRead(userName);
        Get.to(AnnouncementDetailPage(
          mode: mode,
          op: Options.announcement,
          isAdmin: false,
        ));
      }
    }
  }

  void updateEmoji(List<EmBean>? list) {
    StickerDefault.ownRabbit = false;
    StickerDefault.ownEmojiRabbit = false;
    if (list?.isNotEmpty ?? false) {
      for (var b in list!) {
        if (b.mintId == 1) {
          StickerDefault.ownRabbit = true;
        } else if (b.mintId == 2) {
          StickerDefault.ownEmojiRabbit = true;
        }
      }
    }
  }

  void joinBlackList(bool hasBlacked) {
    if (hasBlacked) {
      Get.toNamed(Routes.Blacklist);
    } else {
      showBottomDialogCommonWithCancel(Get.context!,
          widgets: _buildBottomSheetAddBlackWidgets());
    }
  }

  List<Widget> _buildBottomSheetAddBlackWidgets() {
    return [
      Container(
        padding:
            EdgeInsets.only(top: 16.r, left: 16.r, right: 16.r, bottom: 16.r),
        child: Center(
          child: Text(L.after_being_added_to_the_blacklist.tr,
              overflow: TextOverflow.ellipsis,
              softWrap: true,
              style:
                  TextStyle(fontSize: 16.sp, color: AppColors.colorFF333333)),
        ),
      ),
      Container(
        height: 1.h,
        color: AppColors.backgroundGray,
      ),
      getBottomSheetItemSimple(
        Get.context!,
        L.block_user.tr,
        textColor: AppColors.colorFFCD3A3A,
        radius: 12,
        itemCallBack: () {
          addBlack();
        },
      ),
    ];
  }

  void addBlack() {
    var userName = currentUser?.userName ?? '';
    Get.find<AppDatabase>()
        .updateBlackContact(true, userName)
        .then((value) => updateBlackMsg(true));
  }

  void updateBlackMsg(bool hasBlacked) {
    try {
      var msg =
          messageList.firstWhere((element) => element.msgId == Config.blackTag);
      msg.messageHasRead = hasBlacked; //
      update([Config.blackTag]);
    } catch (e) {}
  }

  void updateMsg(bool hasBlacked) {
    try {
      var msg =
          messageList.firstWhere((element) => element.msgId == Config.blackTag);
      msg.messageHasRead = hasBlacked; //
      update([Config.blackTag]);
    } catch (e) {}
  }

  MessageEvent? _activePlayMessage(String msgId) {
    try {
      var msg = messageList.firstWhere((element) => element.msgId == msgId);
      var index = messageList.indexOf(msg);
      if (index >= 0) {
        for (var i = index; i >= 0; i--) {
          var m = messageList[i];
          if (m.type == MessageType.audio &&
              m.messageHasRead != true &&
              m.direction == 0 &&
              m.fileState == FileState.successful) {
            return m;
          }
        }
      }
    } catch (e) {}
    return null;
  }

  _setDraft() {
    String draft =
        Get.find<AppConfigService>().getDraft(_currentUser?.value.userName) ??
            '';
    _onSetText(draft);
  }

  _mute() {
    if (isTabooType.value) {
      _onSetText("");
    } else {
      _setDraft();
    }
  }

  updateQrState(bool has, String? data, String msgId) {
    Get.find<AppDatabase>().updateMessageIdentifyState(has, data, msgId);
  }

  textMessageJumpUri(String body, {String? url}) {
    var map = UrlUtil.parseMeetingUrl(body ?? '');
    if (map.isNotEmpty && map.length == 3) {
      // MeetingHelper().getJoinMeeting(map['url'], map['id'], map['code']);
      MeetingHelper().joinMeeting(map['url'], map['id']);
    } else if (url?.isNotEmpty == true) {
      web3util.jumpToBrowserMethod(
        url ?? '',
      );
    }
  }

  void openMeeting(String? url) async {
    if(url == null || url.isEmpty) return;
    /// url已帶有meeting id
    var conf = Get.find<AppConfigService>();
    var nickname = conf.getMySelfDisplayName();
    var userId = conf.getUserNameWithoutDomain();
    nickname = Uri.encodeFull(nickname);
    userId = Uri.encodeFull(userId);
    String meetingUrl = '$url&userID=$userId&userName=$nickname';
    MeetingHelper().startMeeting(
      meetingUrl,    
    );
  }

  // void goToVote(Poll? poll) async {
  //   if(poll == null) return;
  //   if(poll.channelId != currentUser?.userName) return;
  //   Get.toNamed(Routes.PollView, arguments: {"poll_id": poll.id});
  // }

  @override
  void onHidden() {}
}
