import 'package:flutter_metatel/app/data/providers/db/database.dart';
import 'package:flutter_metatel/app/data/services/config_service.dart';
import 'package:flutter_metatel/core/values/config.dart';
import 'package:get/get.dart';

import '../../../core/languages/l.dart';
import '../../../r.dart';

class FileHelperProcess {
  void updateFileHelper() async {
    var database = Get.find<AppDatabase>();
    var userName = await Get.find<AppConfigService>().getUserName() ?? '';
    ContactData? own = await database.oneContact(userName).getSingleOrNull();
    if (own == null || own.username.isEmpty) {
      await database.deleteContactByType(ContactType.fileHelper);
      double time = DateTime.now().millisecondsSinceEpoch.toDouble();
      var companion = ContactCompanion.insert(
        username: userName ?? "",
        avatarPath: ofNullable(<PERSON><PERSON><PERSON><PERSON><PERSON>ile<PERSON>el<PERSON>),
        displayname: ofNullable(L.metatel_file_assistant.tr),
        localname: ofNullable(L.metatel_file_assistant.tr),
        fistname: ofNullable(L.metatel_file_assistant.tr),
        lastname: ofNullable(L.metatel_file_assistant.tr),
        read: ofNullable(true),
        state: ofNullable(0),
        edit: ofNullable(true),
        createTime: ofNullable(time),
        updateTime: ofNullable(time),
        type: ofNullable(ContactType.fileHelper),
      );
      await database.insertOrUpdateContactData(companion);
    }
  }
}
