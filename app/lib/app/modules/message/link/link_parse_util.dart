import 'dart:io';

import 'package:dio/dio.dart' as dio;
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/data/enums/path.dart';
import 'package:flutter_metatel/app/modules/message/link/link_model.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:flutter_metatel/core/utils/util.dart';
import 'package:get/get.dart';
import 'package:html/dom.dart' as dom;
import 'package:html/parser.dart';
class LinkParseUtil{
  factory LinkParseUtil() =>_privateConfigTask();

  // 静态私有成员，没有初始化
  static LinkParseUtil? _instance;

  // 私有构造函数
  LinkParseUtil._() {
    // 具体初始化代码
    imageDir="${appTempDir?.path??""}/LinkMsgData";
  }

  // 静态、同步、私有访问点
  static LinkParseUtil _privateConfigTask() {
    return _instance ??= LinkParseUtil._();
  }

  late String imageDir;

  Future<LinkModel?> parseHtml(String url,String uuid) async {
    var maxWidth = Get.size.width * .8;
    var maxHeight =  maxWidth*2/3;
    LinkModel? h;
    try{
      var options = BaseOptions();
      options.connectTimeout = const Duration(seconds: 15);
      options.sendTimeout = const Duration(seconds: 15);
      final dio.Dio d = createBaseDio(options:options);
      dio.Response res = await d.get(url);
      if(res.statusCode==200){
        h=LinkModel();
        dom.Document document = parse(res.data);
        h.title=_getTitle(document);
        h.imageUrl=await _getImageFromHtml(document,url);
        var path = await _downLoadImage(h.imageUrl,uuid);
        h.imagePath=appSupporAbsolutePathToPath(path);
        h.content=_getContent(document);
        h.videoUrl=_getVideoUrl(document);
        if (path != null) {
          var imageWH=await getImageWH(
            image: Image.file(
              File(path),
            ),
          );
          h.imageWidth=imageWH.width>maxWidth?maxWidth:imageWH.width;
          h.imageHeight=imageWH.height>maxHeight?maxHeight:imageWH.height;
        }
        h.uuid=uuid;
      }
    }catch(e){
      e.printError();
    }
    return h;
  }
  Future<String?> _downLoadImage(String? url,uuid) async {
    String? path;
    if(url?.isNotEmpty==true){
      path = await downloadFile(url,savePath: "$imageDir/$uuid/${url!.split("/").last}");
    }
    return path;
  }
  Future<String?> downLoadImage(LinkModel model) async {
    var imagePath = model.imagePath;
    var imageUrl = model.imageUrl;
    var imageFileExists = File(appSupporAbsolutePath(imagePath)??"").existsSync();
    if(imageFileExists){
      return imagePath;
    }else if(imageUrl?.isNotEmpty==true){
      String? path = await downloadFile(imageUrl,savePath: "$imageDir/${model.uuid??""}/${imageUrl!.split("/").last}");
      return path;
    }
    return null;
  }

  _getImageFromHtml(dom.Document document,String url) async {
    var index = url.lastIndexOf("/");
    if(index==url.length-1){
      url=url.substring(0,index);
    }
    String ret="";
    ret= document.head
            ?.querySelector("meta[name=\"twitter:image:src\"]")
            ?.attributes["content"] ??
        document.head
            ?.querySelector("meta[name=\"twitter:image\"]")
            ?.attributes["content"] ??
        document.head
            ?.querySelector("meta[property=\"og:image\"]")
            ?.attributes["content"] ??
        document.head?.querySelector("meta[itemprop=\"image\"]")?.attributes["content"]??"";
    // var uri = Uri.parse(url);
    // var host = uri.host;
    // var scheme = uri.scheme;
    // String rootUrl="$scheme://$host";
    // if(ret.isEmpty){
    //   var htmlRootIcon = await _getHtmlRootIcon(rootUrl);
    //   ret=htmlRootIcon;
    // }
    if(ret.isNotEmpty&&!ret.startsWith("http")){
      ret = "http:$ret";
    }
    AppLogger.d("_getImageFromHtml==$ret");
    return ret;
  }
  _getTitle(dom.Document document){
    String? text;
    text =  document.head
        ?.querySelector("meta[name=\"twitter:title\"]")
        ?.attributes["content"] ??
        document.head
        ?.querySelector("meta[property=\"og:title\"]")
        ?.attributes["content"] ??
        document.head
            ?.querySelector("meta[name=\"irTitle\"]")
            ?.attributes["content"] ??
        document.head?.querySelector("title")?.text;
    return text;
  }
  _getContent(dom.Document document){
    String? text;
    text = document.head
        ?.querySelector("meta[property=\"og:description\"]")
        ?.attributes["content"] ??
        document.head
            ?.querySelector("meta[name=\"twitter:description\"]")
            ?.attributes["content"] ??
        document.head
            ?.querySelector("meta[itemprop=\"description\"]")
            ?.attributes["content"] ??
        document.head
            ?.querySelector("meta[name=\"description\"]")
            ?.attributes["content"];
    return text;
  }
  _getSitName(dom.Document document,String url){
    var uri = Uri.parse(url);
    var host = uri.host;
    String? text;
    text = document.head
        ?.querySelector("meta[property=\"og:site_name\"]")
        ?.attributes["content"] ??host;
    return text;
  }
  _getVideoUrl(dom.Document document){
    String? videoUrl;
    videoUrl = document.head
        ?.querySelector("meta[name=\"twitter:player\"]")
        ?.attributes["content"]??
        document.head
            ?.querySelector("meta[property=\"og:video:url\"]")
            ?.attributes["content"]??
        document.head
            ?.querySelector("meta[property=\"og:video:secure_url\"]")
            ?.attributes["content"];
    return videoUrl;
  }
}