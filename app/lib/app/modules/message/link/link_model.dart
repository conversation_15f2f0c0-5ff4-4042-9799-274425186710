class LinkModel{
  String? imageUrl;
  String? title;
  String? content;
  String? imagePath;
  double? imageWidth;
  double? imageHeight;
  String? videoUrl;
  String? uuid;
  LinkModel({this.imageUrl,this.title,this.content,this.imagePath});
  Map<String, dynamic> toJson() {
    final mapData = <String, dynamic>{};
    mapData['imageUrl'] = imageUrl;
    mapData['title'] = title;
    mapData['content'] = content;
    mapData['imagePath'] = imagePath;
    mapData['uuid'] = uuid;
    mapData['videoUrl'] = videoUrl;
    mapData['imageWidth'] = imageWidth;
    mapData['imageHeight'] = imageHeight;
    return mapData;
  }
  LinkModel.fromJson(Map<String, dynamic> json){
    imageUrl=json['imageUrl'];
    title=json['title'];
    content=json['content'];
    imagePath=json['imagePath'];
    uuid=json['uuid'];
    videoUrl=json['videoUrl'];
    imageWidth=json['imageWidth'];
    imageHeight=json['imageHeight'];
  }
}