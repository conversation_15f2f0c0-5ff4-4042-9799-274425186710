/*
 * <AUTHOR> <PERSON><PERSON>
 * @Date         : 2022-04-25 15:32:14
 * @Description  : 聊天界面
 * @LastEditors  : <PERSON><PERSON>
 * @LastEditTime : 2022-08-01 14:14:46
 * @FilePath     : /flutter_metatel/lib/app/modules/message/message_page.dart
 */

import 'dart:io';
import 'dart:ui';

import 'package:extended_text/extended_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:flutter_metatel/core/values/colors.dart';
import 'package:flutter_metatel/core/values/config.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';

import '../../../core/languages/l.dart';
import '../../../core/task/quene_task.dart';
import '../../../core/utils/only_one_point.dart';
import '../../../r.dart';
import '../../data/enums/enum.dart';
import '../../widgets/at_widget/my_special_text_span_builder.dart';
import 'components/sub_component.dart';
import 'message_controller.dart';
class MessagePage extends StatefulWidget {
  
  const MessagePage({Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() =>MessagePageState();
}

class MessagePageState extends State<MessagePage> {
  late MessageController controller;

  @override
  void initState() {
    super.initState();
    controller=Get.put(MessageController(),);
  }

  @override
  void dispose(){
    super.dispose();
    AppLogger.d('dispose0000000000001111');
    Get.delete<MessageController>(force: true);
  }
  /// 不要乱动这个空函数
  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    AppLogger.d('MessagePageState didChangeDependencies');
  }
  @override
  void didUpdateWidget(covariant MessagePage oldWidget) {
    super.didUpdateWidget(oldWidget);
    AppLogger.d('MessagePageState didUpdateWidget');
  }
  BuildContext? appBarContext;
  @override
  Widget build(BuildContext context) {
    controller.setContext(context);
    var paddingTop = MediaQuery.of(context).padding.top;
    var bottomHeight = MediaQuery.of(context).viewInsets.bottom;
    controller.bottomHeightChange(bottomHeight, context);
    return OnlyOnePointerRecognizerWidget(
      child: Scaffold(
        resizeToAvoidBottomInset: false,
        appBar: PreferredSize(
          preferredSize: const Size.fromHeight(kToolbarHeight),
          child: Builder(
              builder: (c){
                appBarContext=c;
                AppLogger.d(
                    "message page:0");
                return AppBar(
                  leading: BackButton(
                    onPressed: () async {
                      // await SystemChannels.textInput.invokeMethod('TextInput.hide');
                      controller.onSticker(backButton: true);
                      if (controller.isMessageCanMultiSelect.value) {
                        controller.changeMessageCanMultiSelect(false);
                      } else {
                        Navigator.maybePop(context);
                      }
                    },
                  ),
                  titleSpacing: -5,
                  title: InkWell(
                    onTap: controller.onGotoDetail,
                    child: GetBuilder<MessageController>(
                      id: 'message_page_title',
                      builder: (controller) {
                        String textBody = controller.currentUser?.displayName ?? '';
                        String? memberCount;
                       if(controller.memberCount!=null &&controller.memberCount != 0){
                         memberCount = ' (${controller.memberCount})';
                       }
                        return Row(
                          children: [
                            createHeader(context, controller.currentUser),
                            const SizedBox(width: 5),
                            Flexible(
                              flex: 3,
                                child: ExtendedText(
                                  textBody,
                                  overflow: TextOverflow.ellipsis,
                                  specialTextSpanBuilder: MySpecialTextSpanBuilder(
                                    showAtBackground: false, size: Size(15.r, 15.r),
                                  ),
                                  style: TextStyle(fontSize: 16.sp),
                                  // style: Theme.of(context).textTheme.headline6,
                                )),
                            memberCount != null
                                ? Flexible(
                                child: Text(
                                  memberCount,
                                  style: TextStyle(fontSize: 16.sp),
                                  // style: Theme.of(context).textTheme.headline6,
                                ))
                                : const SizedBox(),
                            SizedBox(width: 5.r),
                            controller.buildGroupIcon(),
                            controller.currentUser?.isFriend == false && !controller.isOfficialChat() && !controller.isMeetingRobot()
                                ? Image.asset(
                              R.personAdd,
                              width: 15.w,
                              height: 14.h,
                            )
                                : const SizedBox(),
                          ],
                        );
                      },
                    ),

                  ),
                  actions: [
                    Obx(() => controller.isMessageCanMultiSelect.value
                        ? GestureDetector(
                      onTap: () {
                        controller.changeMessageCanMultiSelect(false);
                      },
                      child: Center(
                        child: Text(
                          L.cancel.tr,
                          style: TextStyle(
                              fontSize: 14,
                              color: Theme.of(context).colorScheme.primary),
                        ),
                      ),
                    )
                        : Row(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                          Obx(() {
                            return Visibility(
                              visible: controller.currentUser?.chatType ==
                                  ChatType.channelChat.index,
                              child: IconButton(
                                icon: Image.asset(R.iconChannelMoment),
                                padding: EdgeInsets.zero,
                                onPressed: controller.openChannelMomentView,
                              ),
                            );
                          }),
                        Obx(() {
                          String image = controller.rxSelfDestruct.isTrue
                              ? R.readDestroyRed
                              : R.readDestroy;
                          return Visibility(
                            visible: controller.currentUser?.chatType ==
                                ChatType.singleChat.index &&
                                controller.currentUser?.userName !=
                                    Config.fileHelperOwner && !controller.isMeetingRobot(),
                            child: IconButton(
                              icon: Image.asset(image),
                              onPressed: controller.selfDestructChange,
                            ),
                          );
                        }),
                        Obx(() {
                          return Visibility(
                              visible: controller.currentUser?.chatType ==
                                  ChatType.singleChat.index &&
                                  controller.currentUser?.userName !=
                                      Config.fileHelperOwner &&
                                  !controller.isOfficialChat() &&
                                    !controller.isMeetingRobot(),
                              child: IconButton(
                                icon: Image.asset(R.icoPhoneCall),
                                onPressed: () {
                                  controller.call(context, false);
                                },
                              ));
                        }),
                        Obx(() {
                          return Visibility(
                            visible: controller.currentUser?.chatType !=
                                ChatType.singleChat.index &&
                                controller.currentUser?.userName !=
                                    Config.fileHelperOwner ||
                                controller.isOfficialChat() &&
                                    !controller.isMeetingRobot(),
                            child: IconButton(
                              icon: Image.asset(
                                R.icoMore,
                                width: 17.w,
                                height: 4.h,
                              ),
                              onPressed: () {
                                controller.onGotoDetail(clickTitle: true);
                              },
                            ),
                          );
                        }),
                      ],
                    )),
                    const SizedBox(width: 10),
                  ],
                  elevation: 0,
                );
              }
          ),
        ),
        body: Obx(() {
          AppLogger.d(
              "message page:1");
          if (Platform.isAndroid) {
            return WillPopScope(
                child: bodyWidget(context, paddingTop),
                onWillPop: () async {
                  await controller.audioRecordDialog();

                  if (controller.isMessageCanMultiSelect.value == true) {
                    controller.changeMessageCanMultiSelect(false);
                    return false;
                  } else {
                    return true;
                  }
                });
          } else {
            return bodyWidget(context, paddingTop);
          }
        }),
      ),
    );
  }

  ImageProvider _createImageProvider() {
    var chatWallpaper = controller.getChatWallpaper();
    if (chatWallpaper.isEmpty || !File(chatWallpaper).existsSync()) {
      return const AssetImage(R.defaultBackground);
    } else {
      return FileImage(File(controller.getChatWallpaper()));
    }
  }

  Widget _buildRecordWidget() {
    return Obx(() {
      var status = controller.rxIMRecordStatus.value;
      var visible = true;
      String text = '';
      Color textColor = Colors.white;

      switch (status) {
        case IMRecordStatus.none:
          visible = false;
          break;
        case IMRecordStatus.record:
          text = L.other_finger_move_up_cancel_send.tr;
          break;
        case IMRecordStatus.cancelRecord:
          text = L.other_release_cancel_send.tr;
          break;
        default:
      }

      var countdown = controller.rxIMRecordCountdown.value;
      if (countdown >= 0) {
        text = '${L.other_can_said_time.tr}$countdown${L.second.tr}';
      }

      return Visibility(
        visible: visible,
        child: AbsorbPointer(
          child: Center(
            child: Container(
              width: 130.w,
              padding: EdgeInsets.all(18.w),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(25.r),
                color: AppColors.primaryBgColor1,
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Image.asset(R.icoRecordIM, width: 26.w, height: 35.h),
                  SizedBox(height: 7.5.h),
                  Text(
                    text,
                    textAlign: TextAlign.center,
                    style: TextStyle(color: textColor, fontSize: 10.sp),
                  ),
                ],
              ),
            ),
          ),
        ),
      );
    });
  }

  Widget bodyWidget(BuildContext context, double paddingTop) {
    AppLogger.d('bodyWidget....');
    ImageProvider bgImageProvider = _createImageProvider();
    // var decoration;
    // if (bgImageProvider == null) {
    //   decoration = const BoxDecoration(
    //     color: AppColors.colorFFF8F8F8,);
    // } else {
    //   // decoration = BoxDecoration(
    //   //     image: DecorationImage(
    //   //         image: bgImageProvider, fit: BoxFit.cover));
    // }

    return Container(
      height: context.mediaQuerySize.height,
      width: context.mediaQuerySize.width,
      decoration: const BoxDecoration(
        color: AppColors.backgroundGray,
      ),
      child: SafeArea(
        child: Stack(
          children: [
            Image(image: bgImageProvider,fit: BoxFit.cover,height: double.infinity,width: double.infinity),
            Column(
              children: [
                controller.topControllerAdmin.createTopWidget(appBarContext,controller),
                controller.topController.createTopWidget(appBarContext,controller),
                controller.topController.createTopBannerWidget(),
                Expanded(
                  child: Container(
                    // decoration: decoration,
                    child: Stack(
                      children: [
                        NotificationListener(
                          onNotification: (ScrollNotification notification) {
                            if (notification is ScrollEndNotification) {
                              controller.setShowBottom(false);
                            } else if (notification
                                is ScrollStartNotification) {
                              //controller.setShowBottom(true);
                            }
                            return false;
                          },
                          child: createMessageList(controller, paddingTop),
                        ),
                        Positioned(
                          bottom: 10,
                          right: 16,
                          child: GestureDetector(
                              onTap: () {
                                controller.toBottom();
                              },
                              child: Visibility(
                                visible: controller.rxShowJump.value,
                                child: Container(
                                  padding: EdgeInsets.all(10.r),
                                  child: Image.asset(
                                    R.icToBottom,
                                    width: 35.r,
                                    height: 35.r,
                                  ),
                                ),
                              )),
                        ),
                        Positioned(
                          right: 16,
                          top: 0,
                          left: 16,
                          bottom: 0,
                          child: Visibility(
                            visible: controller.rxLoadMore.value,
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                SizedBox(
                                  child: RefreshProgressIndicator(
                                    color: AppColors.appDefault,
                                  ),
                                ),

                              ],
                            ),
                          ),
                        ),
                        Positioned(
                          right: 16,
                          top: 20,
                          child: Visibility(
                            visible: controller.newMessageCount.isNotEmpty,
                            child: GestureDetector(
                              onTap: () {
                                controller.clickNewMessage();
                              },
                              onLongPress: () {
                                controller.clickLongNewMessage();
                              },
                              child: Container(
                                padding: const EdgeInsets.only(
                                        right: 10, left: 10, top: 5, bottom: 5)
                                    .r,
                                decoration: BoxDecoration(
                                  color: AppColors.backgroundGray,
                                  borderRadius: BorderRadius.all(
                                    Radius.circular(15.r),
                                  ),
                                ),
                                child: Obx(() => Text(
                                      L.new_message_count.trParams({
                                        'number':
                                            '${controller.newMessageCount.length}'
                                      }),
                                      style: TextStyle(
                                          fontSize: 13.sp,
                                          color: AppColors.colorFFCD3A3A),
                                    )),
                              ),
                            ),
                          ),
                        )
                      ],
                    ),
                  ),
                ),
                Visibility(
                  visible: !controller.isMessageCanMultiSelect.value,
                  replacement: !controller.isComplaintChangedMsgs.value
                      ? Row(
                          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                          children: [
                            if(!controller.isOfficialChat())
                              IconButton(
                                onPressed: () {
                                  controller.onBottomMessageForward(context);
                                },
                                icon: Image.asset(
                                  R.iconForward,
                                  fit: BoxFit.contain,
                                  color: Colors.white,
                                )),
                            IconButton(
                                onPressed: () {
                                  controller.onDeleteMessage(
                                      controller.selectedMessageEventList);
                                },
                                icon: Image.asset(
                                  R.iconDelete,
                                  fit: BoxFit.contain,
                                  color: Colors.white,
                                )),
                          ],
                        )
                      : IconButton(
                          onPressed: () {
                            controller.changeComplaint(context);
                          },
                          icon: const Icon(Icons.info_outline)),
                  child: createInputBox(context),
                ),
              ],
            ),
            _buildRecordWidget(),
          ],
        ),
      ),
    );
  }

}