//FileName EmojiController
// <AUTHOR>
//@Date 2023/2/3 16:49
import 'dart:convert';

import 'package:flutter_metatel/app/data/services/config_service.dart';
import 'package:get/get.dart';

import '../../../../core/utils/app_log.dart';
import '../../../../core/utils/events_bus.dart';
import '../../../data/events/events.dart';
import '../../../data/models/res/emoticon_res_model.dart';
import '../../dao/api/dao_api.dart';

class EmojiController {
  String TAG = 'EmojiController';

  EmojiController(this.userName) {
    requestEmojiMade();
  }

  String userName;

  void requestEmojiMade() async {
    var res = await Get.find<DaoApiProvider>().getEmotionList();
    AppLogger.d('$TAG res.body==${res.data?.toJson()}');
    var config = Get.find<AppConfigService>();
    if (res.statusCode == 200) {
      EmoticonResModel? body = res.data;
      if (body != null && body.code == 200) {
        if (body.data?.isNotEmpty ?? false) {
          await config.saveEmojiMade(json.encode(body.data));
        }
        sendEmotiocnEvent(body.data);
      }
    } else {
      decodeEmoji(config.readEmojiMade());
    }
  }

  void decodeEmoji(String? data) {
    if(data==null||data.isEmpty){
      return;
    }
    try {
      AppLogger.d('$TAG data=$data');
      List<dynamic>? emojiList = json.decode(data);
      AppLogger.d('$TAG emojiList=${emojiList.toString()}');
      List<EmBean> list = <EmBean>[];
      emojiList?.forEach((v) {
        list.add(EmBean.fromJson(v));
      });
      sendEmotiocnEvent(list);
    } catch (e) {
      AppLogger.e('$TAG emojiList=${e.toString()}');
    }
  }

  sendEmotiocnEvent(List<EmBean>? list) {
    Get.find<EventBus>().fire(UpdateEmoticonEvent(userName, list));
  }
}
