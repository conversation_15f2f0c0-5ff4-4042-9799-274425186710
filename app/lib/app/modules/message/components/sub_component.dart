/*
 * <AUTHOR> <PERSON><PERSON>
 * @Date         : 2022-05-11 14:55:34
 * @Description  : 
 * @LastEditors  : <PERSON><PERSON>
 * @LastEditTime : 2022-08-01 12:16:24
 * @FilePath     : /flutter_metatel/lib/app/modules/message/components/sub_component.dart
 */

import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:async_task/async_task_extension.dart';
import 'package:extended_image/extended_image.dart';
import 'package:extended_text_field/extended_text_field.dart';
import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/data/models/channel_card_model.dart';
import 'package:flutter_metatel/app/data/models/meeting.dart';
import 'package:flutter_metatel/app/data/models/user_message_model.dart';
import 'package:flutter_metatel/app/modules/home/<USER>/session_controller.dart';
import 'package:flutter_metatel/app/modules/mergemessage/merge_message_page.dart';
import 'package:flutter_metatel/app/modules/message/components/top_controller.dart';
import 'package:flutter_metatel/app/modules/message/link/link_model.dart';
import 'package:flutter_metatel/app/modules/message/message_controller.dart';
import 'package:flutter_metatel/app/modules/wallet/wallet_manage.dart';
import 'package:flutter_metatel/app/widgets/chat_bubbles/bubbles/bubble_hongbao.dart';
import 'package:flutter_metatel/app/widgets/chat_bubbles/bubbles/bubble_image_sticker.dart';
import 'package:flutter_metatel/app/widgets/chat_bubbles/bubbles/bubble_link.dart';
import 'package:flutter_metatel/app/widgets/chat_bubbles/bubbles/bubble_meeting.dart';
import 'package:flutter_metatel/app/widgets/chat_bubbles/bubbles/bubble_money_exchange.dart';
import 'package:flutter_metatel/app/widgets/chat_bubbles/bubbles/bubble_text_merge.dart';
import 'package:flutter_metatel/app/widgets/chat_bubbles/bubbles/bubble_wallet_address.dart';
import 'package:flutter_metatel/core/task/chat_task.dart';
import 'package:flutter_metatel/core/task/session_task.dart';
import 'package:flutter_metatel/core/task/time_task.dart';
import 'package:flutter_metatel/core/values/colors.dart';
import 'package:flutter_metatel/core/values/config.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:flutter_web3/app/core/open_wallet_helper.dart';
import 'package:flutter_web3/app/core/utils/util.dart' as web3_util;
import 'package:flutter_web3/app/core/values/config.dart';
import 'package:flutter_web3/app/data/enums/enum.dart';
import 'package:flutter_web3/app/data/models/mony_exchange_model.dart';
import 'package:flutter_web3/app/data/models/wallet_model.dart';
import 'package:flutter_web3/app/data/models/wallet_transaction.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:scrollable_positioned_list/scrollable_positioned_list.dart';

import '../../../../core/languages/l.dart';
import '../../../../core/utils/app_log.dart';
import '../../../../core/utils/util.dart';
import '../../../../core/utils/vibration_util.dart';
import '../../../../r.dart';
import '../../../../routes/pages.dart';
import '../../../../webrtc/core/webrtc_call_helper.dart';
import '../../../data/enums/path.dart';
import '../../../data/models/contact_card_model.dart';
import '../../../data/models/merge_message_body_model.dart';
import '../../../data/models/role.dart';
import '../../../data/providers/db/database.dart';
import '../../../data/services/channel_service.dart';
import '../../../data/services/config_service.dart';
import '../../../data/services/down_loads_service.dart';
import '../../../data/services/event_service.dart';
import '../../../widgets/at_widget/my_special_text_span_builder.dart';
import '../../../widgets/auto_quit_view.dart';
import '../../../widgets/chat_bubbles/bubbles/bubble_channel.dart';
import '../../../widgets/chat_bubbles/bubbles/bubble_fail.dart';
import '../../../widgets/chat_bubbles/bubbles/bubble_image_sticker_default.dart';
import '../../../widgets/chat_bubbles/chat_bubbles.dart';
import '../../../widgets/chat_widget/photos_video_preview_view.dart';
import '../../../widgets/chat_widget/record_button/record_button.dart';
import '../../../widgets/chat_widget/record_button/record_button_im.dart';
import '../../../widgets/divider_cus.dart';
import '../../../widgets/down_load_file_view.dart';
import '../../../widgets/down_load_view.dart';
import '../../home/<USER>/detail/contact_detail.dart';
import 'attachment_dialog.dart';
import 'menu_dialog.dart';
import 'mysticker/my_sticker_view.dart';
import 'package:decimal/decimal.dart';
const String messageMenuPopTag = "message-menu-pop";

void itemOnTop(BuildContext context, MessageController controller, int index) {
  MessageEvent info = controller.messageList[index];
  MessageType type = info.type;
  String? filePath = info.filePath;

  if(info.isUndo){
    return;
  }

  if (info.direction == Direction.outGoing &&
      info.state == BubbleItemState.FAILED) {
    controller.onRsendMessage(info);
    return;
  }

  if (info.state == BubbleItemState.SENSITIVE) {
    toast(L.not_sensitive_words.tr);
  }
  if(type==MessageType.image||type==MessageType.video){
    String? thumbPath=info.thumbnailPath;
    String? imagePath = info.filePath;
    bool downThumPath=((thumbPath?.isEmpty ?? true) || !File(thumbPath!).existsSync()) && (info.thumbnailUrl?.isNotEmpty ?? false);
    bool downImgFile = type == MessageType.image && (((imagePath?.isEmpty ?? true) || !File(imagePath!).existsSync()) &&
            (info.fileUrl?.isNotEmpty ?? false));
    if (downThumPath) {
      Get.find<DownLoadService>().loadFile(info, downThumbnail: true);
      if(downImgFile){
        return;
      }
    } else if (downImgFile&&(info.thumbnailUrl?.isEmpty ?? true)) {
      Get.find<DownLoadService>().loadFile(info);
      return;
    }
  }else if(type==MessageType.audio){
    String? filePath = info.filePath;
    if((filePath?.isEmpty ?? true) || !File(filePath!).existsSync()){
      Get.find<DownLoadService>().loadFile(info);
    }
  }

  /// 0 文本 1 图片、2音频、3视频、4文件
  switch (type) {
    case MessageType.sticker:
      List<PhotosData> pathList = [];
      int currentIndex = 0;
      for (int i = 0; i < controller.messageList.length; i++) {
        MessageEvent data = controller.messageList[i];
        if (data.type == MessageType.sticker) {
          var filePath = data.filePath;
          var needDown = false;
          String? path;
          if (filePath != null && filePath.isNotEmpty) {
            path = filePath;
          } else {
            var thumbnailPath = appSupporAbsolutePath(data.filePath);
            if (thumbnailPath?.isNotEmpty ?? false) {
              path = thumbnailPath;
            }
            needDown = true;
          }
          if (filePath != null) {
            pathList.insert(0, PhotosData(
                msgId: data.msgId,
                path: path,
                sourceFragment: data.fileFragment,
                needDown: needDown,
                fileName: data.fileName,
                fileState: data.fileState,
                fileUrl: data.fileUrl));
            if (index == i) {
              currentIndex = 0;
            } else {
              currentIndex++;
            }
          }
        }
      }
      autoQuitShowDialog(
        barrierDismissible: false,
        context: context,
        useSafeArea: false,
        id: info.msgId,
        body: PhotosPreviewView(
          messageList: pathList,
          initIndex: currentIndex,
          weakList: controller.imagesWeakMap,
          isOriginalSelected: false,
        ),
      );
      break;
    case MessageType.image:
      List<PhotosData> pathList = [];
      int currentIndex = 0;
      int? countdown;
      if (info.selfDestruct && info.direction == Direction.inComing) {
        countdown = Config.selfDestructCountdown;
        var needDown = false;
        var filePath = appSupporAbsolutePath(info.filePath);
        String? path;
        if (filePath != null && filePath.isNotEmpty) {
          path = filePath;
        } else {
          var thumbnailPath = appSupporAbsolutePath(info.thumbnailPath);
          if (thumbnailPath?.isNotEmpty ?? false) {
            path = thumbnailPath;
          }
          needDown = true;
        }
        if(path?.isNotEmpty??false){
          pathList.add(PhotosData(
              msgId: info.msgId,
              path: path,
              fileState: info.fileState,
              sourceFragment: info.fileFragment,
              needDown: needDown,
              fileName: info.fileName,
              fileUrl: info.fileUrl));
        }
      } //
      else {
        for (int i = 0; i < controller.messageList.length; i++) {
          MessageEvent data = controller.messageList[i];
          if (data.selfDestruct && data.direction == Direction.inComing) {
            continue;
          }

          if (data.type == MessageType.image || data.type == MessageType.sticker) {
            var needDown = false;
            var filePath = appSupporAbsolutePath(data.filePath);
            String? path;
            if (filePath != null && filePath.isNotEmpty) {
              path = filePath;
            } else {
              var thumbnailPath = appSupporAbsolutePath(data.thumbnailPath);
              if (thumbnailPath?.isNotEmpty ?? false) {
                path = thumbnailPath;
              }
              needDown = true;
            }
            if(path?.isNotEmpty??false){
              if (index == i) {
                currentIndex = 0;
              } else {
                currentIndex++;
              }
              pathList.insert(0,PhotosData(
                  msgId: data.msgId,
                  path: path,
                  needDown: needDown,
                  fileState: data.fileState,
                  fileName: data.fileName,
                  sourceFragment: data.fileFragment,
                  fileUrl: data.fileUrl));
            }
          }
        }
      }

      showDialog(
          barrierDismissible: false,
          context: context,
          useSafeArea: false,
          builder: (_) {
            return PhotosPreviewView(
              messageList: pathList,
              initIndex: currentIndex,
              countdown: countdown,
              weakList: controller.imagesWeakMap, isOriginalSelected: true,
            );
          });
      break;
    case MessageType.video:
        AppLogger.d('filePath: $filePath');
        try {
          DownLoadShowDialog(
            barrierDismissible: true,
            context: context,
            event: info,
            body:Container(),
          );
        } catch (e) {
          toast('');
        }
      break;
    case MessageType.file:
      if(showDownLoading(info)){
        downLoadFileShowDialog(
          barrierDismissible: true,
          parentContext: context,
          event: info,
        );
      } else{
        openFileDialog(
          context,
          info.fileName,
          info.filePath,
          fragment: info.fileFragment,
          id: info.msgId,
        );
      }

      break;
    case MessageType.contactCard:
      ContactBean? bean;
      try {
        bean = ContactBean.fromJson(jsonDecode(info.body ?? "{}"));
      } catch (e) {
        bean = null;
      }
      String? value=Get.find<AppConfigService>().getUserName();
      if (value == bean?.userName) {} else
      if (bean != null && (bean.userName?.isNotEmpty ?? false)) {
        ContactData data = ContactData(
            id: 0,
            username: bean.userName ?? '',
            localname: bean.displayName);
        Get.toNamed(Routes.CONTACT_DETAIL, arguments: data);
      }
      break;
    case MessageType.fail:
      controller.onResendMessage(info);
      break;
    case MessageType.call:
      if (controller.currentUser?.userName != null) {
        WebRtcCallHelper.instance.mackCall(controller.currentUser!.userName!,
            info.ext1 == 'video', controller.currentUser!.displayName);
      }
      break;
    case MessageType.channelCard:
      ChannelCard? channelCard;
      try {
        channelCard = ChannelCard.fromJson(jsonDecode(info.body ?? "{}"));
      } catch (e) {
        channelCard = null;
      }
      if (channelCard != null &&
          channelCard.channelId != null &&
          (channelCard.channelId?.isNotEmpty ?? false)) {
        int? sharTime = channelCard.expiresTime;
        int now = TimeTask.instance.getNowTime() ~/ 1000;
        AppLogger.d('sharTime =$sharTime');
        if (sharTime != null && now < sharTime + Config.shareTime) {
          Get.toNamed(Routes.ChannelJoin, arguments: {
            "channelId": channelCard.channelId ?? '',
            "invitee_user": info.from ?? '',
            "action": channelCard.action ?? ''
          });
        } else {
          toast(L.invitation_has_expired.tr);
        }
      }
      break;
    case MessageType.walletAddress:
      WalletModel? walletModel;
      try {
        walletModel = WalletModel.fromJson(jsonDecode(info.body ?? "{}"));
        if(walletModel.address?.isNotEmpty??false){
          WalletManage().toTransfer(
            walletModel.chainType ?? 0,
            walletModel.address!,
            ToWalletTransactionModel(
              billType: BillType.normal.index,
            ),
            context: context
          );
        }
      } catch (e) {
        walletModel = null;
      }
      break;
      case MessageType.walletBill:
        ToWalletTransactionModel? bean2;
        try {
          bean2=ToWalletTransactionModel.fromJson(jsonDecode(info.body ?? "{}"));
          AppLogger.d('walletBill ${info.body}');
          // bean2.amount = 1;
          WalletManage()
              .toTransfer(bean2.chainType??0, bean2.toAddress??'',bean2,transactionType: bean2.transactionType!=1?TransactionType.balance:TransactionType.token,context:context);
        } catch (e) {
          bean2 = null;
        }
      break;
    case MessageType.walletTransaction:
      WalletTransactionModel? walletModel;
      try {
        walletModel = WalletTransactionModel.fromJson(jsonDecode(info.body ?? "{}"));
      } catch (e) {
        walletModel = null;
      }
      if(walletModel!=null){
        String browserUrl=walletModel.browserUrl??'';
        if(browserUrl.isEmpty) return;
        var uri = Uri.parse(browserUrl);
        var host = uri.host;
        var scheme = uri.scheme;
        browserUrl="$scheme://$host";
        String browserTag=walletModel.chainType==ChainType.chainEthereum?"/tx/":'/#/transaction/';
        var url = "$browserUrl$browserTag${walletModel.transactionHash}";
        web3_util.jumpToBrowserMethod(url);
      }
      break;
    case MessageType.moneyExchange:
      MoneyExchangeModel? model;
      try {
        model = MoneyExchangeModel.fromJson(jsonDecode(info.body ?? "{}"));
      } catch (e) {
        model = null;
      }
      if(model!=null &&(model.url?.isNotEmpty ?? false)){
        web3_util.jumpToBrowserMethod(model.url!,);
      }
      break;
    case MessageType.msgMergeForward:
      MergeMessageBody body =
          MergeMessageBody.fromJson(jsonDecode(info.body ?? "{}"));
      Get.to(MergeMessagePage(info, body.title ?? ""));
      break;
    case MessageType.text:
      if(ChatTask.instance.isLinkMsg(info)){
        web3_util.jumpToBrowserMethod(info.body??'',);
      } else {
        controller.textMessageJumpUri(info.body ?? '');
      }
      break;
    case MessageType.hongbao:
      controller.sendHongBao(context,msgId: info.msgId,ext1: info.ext1,extension: info.extension);
      
      break;
    case MessageType.meeting:
      Meeting? meeting;
      try {
        meeting = Meeting.fromJson(jsonDecode(info.body ?? "{}"));
      } catch (e) {
        meeting = null;
      }      
      controller.openMeeting(meeting?.meetingLink);
      //会议点击
      break;
    // case MessageType.daoPoll:
    //   //投票点击
    //   Poll? poll;
    //   try {
    //     poll = Poll.fromJson(jsonDecode(info.body ?? "{}"));
    //   } catch (e) {
    //     poll = null;
    //   }
    //   controller.goToVote(poll);
    //   break;
    default:
      break;
  }

  controller.itemOnTap(info.msgId);
}

Widget buildItem(
  BuildContext context,
  MessageController controller,
  MessageEvent messageInfo, {
  Key? key,
  GestureTapCallback? onTap,
  GestureLongPressCallback? onLongPress,
  GestureTapCallback? onAvatarTap,
  GestureTapCallback? onAvatarLongTap,
  ValueChanged<String?>? onReplayTap,
  bool joinZeroWidthSpace = false,
  bool isDialog = false,
}) {
  // MessageInfo messageInfo = controller.messageList[index];
  bool isSender = messageInfo.direction == 1;
  MessageType type = messageInfo.type;
  bool thumbnailDownLoadError = messageInfo.thumbnailFileState == FileState.downError;
  bool fileDownLoadError = messageInfo.fileState == FileState.downError;

  // AppLogger.d("downLoadError =$thumbnailDownLoadError");
  if(type==MessageType.image||type==MessageType.video){
    String? thumbPath=messageInfo.thumbnailPath;
    String? imagePath = messageInfo.filePath;
    bool downThumPath=((thumbPath?.isEmpty ?? true) || !File(thumbPath!).existsSync()) && (messageInfo.thumbnailUrl?.isNotEmpty ?? false);
    bool downImgFile = type == MessageType.image && (((imagePath?.isEmpty ?? true) || !File(imagePath!).existsSync()) &&
        (messageInfo.fileUrl?.isNotEmpty ?? false));
    if (downThumPath&&!thumbnailDownLoadError) {
      Get.find<DownLoadService>().loadFile(messageInfo, downThumbnail: true);
    } else if (downImgFile&&(messageInfo.thumbnailUrl?.isEmpty ?? true)&&!fileDownLoadError) {
      Get.find<DownLoadService>().loadFile(messageInfo);
    }
  }else if((type==MessageType.audio||type==MessageType.sticker)&&!fileDownLoadError){
    String? filePath = messageInfo.filePath;
    if((filePath?.isEmpty ?? true) || !File(filePath!).existsSync()){
      Get.find<DownLoadService>().loadFile(messageInfo);
    }
  }

  if (messageInfo.isUndo) {
    String title = L.retracted_a_msg.trParams({'name': ''});
    if (messageInfo.ext1 != null && messageInfo.ext1!.isNotEmpty) {
      title = messageInfo.ext1!;
    }
    var isBlack=Config.blackTag==messageInfo.msgId;
    var hasBlacked=messageInfo.messageHasRead??false;
    return TextChip(
      title: title,
      tips: isBlack?hasBlacked?L.blocked.tr:L.block_user.tr:null,
      textColor: controller.fromNameTextColor.value,
      onUndoEditTapUp: messageInfo.undoEdit == true
          ? (detail) =>isBlack?controller.joinBlackList(hasBlacked):controller.undoMsgEdit(messageInfo)
          : null,
      onUndoEditTapDown: messageInfo.undoEdit == true
          ? (detail) {
              AppLogger.d("onPointerUp onUndoEditTapDown ");
              controller.isUnDoEditOnTapDown = true;
            }
          : null,
    );
  }

  String? fromname;
  String? avatarPath;
  bool isOfficeMsg = messageInfo.chatType == ChatType.officialChat;
  if (!isSender && messageInfo.chatType != ChatType.singleChat && !isOfficeMsg) {
    fromname = messageInfo.from;
    MemberInfo? info = controller.ownInfo(messageInfo.from);
    if (info != null && info.nickname != null) {
      fromname = info.nickname;
      avatarPath = info.avatarPath;
    }
  }
  if (isOfficeMsg) {
    bool isAdmin = controller.isNoticeAdmin;
    if (isAdmin) {
      isSender = true;
    } else {
      var user = Config.userNameWithoutDomain;
      if (messageInfo.from == user) {
        isSender = true;
      }
    }
  }
  var activePlay = messageInfo.activePlay ?? false;
  messageInfo.activePlay = false;
  ChannelMemberRole role=ChannelMemberRole.ordinary;
  ChannelAttribute? channelAttribute;
  List<Role> tags = [];
  if(controller.isChannelChat()&&!isSender){
    channelAttribute=Get.find<SessionController>().channelAttribute(controller.currentUser?.userName ?? '');
    if(channelAttribute==ChannelAttribute.dao){
      /// Leader标签
      if(messageInfo.from != null && controller.isChannelOwner(messageInfo.from!)) {
        tags.add(
          Role(tagId: DefaultDaoRoleId.LEADER, name: L.leader.tr, color: 0xFF750C0C)
        );
      }

      /// Manager标签
      if(messageInfo.from != null && controller.isChannelAdmin(messageInfo.from!)) {
        tags.add(
          Role(tagId: DefaultDaoRoleId.MANAGER, name: L.manager.tr, color: 0xFF393166)
        );
      }

      /// 普通标签标签
      /// 从缓存中获取角色标签
      var mapTags = controller.getMapChannelTag();
      /// 从缓存中获取成员角色标签
      var mapMemberTags = controller.getMapChannelMemberTag();
      /// 获取当前成员的标签
      var memberTags = mapMemberTags[messageInfo.from];            
      if(memberTags!=null && memberTags.isNotEmpty){
        for(var memberTag in memberTags) {
          if(!mapTags.containsKey(memberTag)) continue;
          tags.add(mapTags[memberTag]!);
        }
      }
    }
    if(controller.channelOwner==messageInfo.from){
      role=ChannelMemberRole.owner;
    }else if(controller.channelAdminList.contains(messageInfo.from)){
      role=ChannelMemberRole.administrator;
    }
  }

  BubbleMetadata metadata = BubbleMetadata(
    isSender: isSender,
    fromname: fromname,
    avatarPath: avatarPath,
    state: messageInfo.state,
    dateTime: messageInfo.dateTime,
    selfDestruct: messageInfo.selfDestruct,
    weakList: controller.imagesWeakMap,
    countdown: messageInfo.countdown,
    msgId: messageInfo.msgId,
    fileState: messageInfo.fileState,
    onLongPress: onLongPress,
    onTap: onTap,
    onAvatarTap: onAvatarTap,
    onAvatarLongTap: onAvatarLongTap,
    replayMsg: messageInfo.replayMsg,
    replayFileFragment: messageInfo.replayFileFragment,
    replayFilePath: messageInfo.replayFilePath,
    onReplayTap: onReplayTap,
    hasShown: messageInfo.hasShown,
    messageHasRead: messageInfo.messageHasRead,
    size: messageInfo.expand?.size,
    noises: messageInfo.noises,
    activePlay: activePlay,
    channelMemberRole: role,
    translateMsg: messageInfo.translateMsg,
    msgType: messageInfo.type.index,
    channelAttribute: channelAttribute,
    tags: tags, 
  );

  /// 0 文本 1 图片、2音频、3视频、4文件
  switch (type) {
    case MessageType.msgMergeForward:
      return BubbleTextMarge(
        key: key,
        metadata: metadata,
        text: messageInfo.body,
        fromNameTextColor: controller.fromNameTextColor.value,
      );
    case MessageType.text:
      messageInfo.body = messageInfo.body?.trim();
      if(ChatTask.instance.isLinkMsg(messageInfo)){
        return /*BubbleLink(
          metadata: metadata,
          fromNameTextColor: controller.fromNameTextColor.value,
          text: messageInfo.body,
          linkModel: LinkModel(),
        );*/FutureBuilder(
          future: controller.downLoadLinkImage(messageInfo),
          builder: (c, s) {
            LinkModel l = messageInfo.expand?.linkModel??LinkModel();
            if(s.connectionState==ConnectionState.done){
              if(s.data.runtimeType==LinkModel){
                l=s.data as LinkModel;
              }
            }
            return BubbleLink(
              metadata: metadata,
              fromNameTextColor: controller.fromNameTextColor.value,
              text: messageInfo.body,
              linkBack: (url){
                controller.textMessageJumpUri(messageInfo.body ?? '',url: url);
              },
              linkModel: l,
            );
          },
        );
      }
      return BubbleText(
        key: key,
        metadata: metadata,
        text: messageInfo.body,
        fromNameTextColor: controller.fromNameTextColor.value,
        joinZeroWidthSpace :joinZeroWidthSpace,
        isDialog:isDialog,
        linkBack: (url){
          controller.textMessageJumpUri(messageInfo.body ?? '',url: url);
        },
      );
    case MessageType.stickerDefaultRabbit:
    case MessageType.stickerDefaultEmoji:
    case MessageType.stickerDefault:
    String? imagePath=messageInfo.filePath;
    var isAssets =imagePath?.startsWith('assets/images');
    String? stickerName;
    if(isAssets??false){
      stickerName ??= currentLanguageIsSimpleChinese()
          ? StickerDefault.mapNameCn[imagePath]
          : StickerDefault.mapNameEn[imagePath];
      stickerName ??= currentLanguageIsSimpleChinese()
          ? StickerDefault.mapNameEmojiRabbitCn[imagePath]
          : StickerDefault.mapNameEmojiRabbitEn[imagePath];
      stickerName ??= currentLanguageIsSimpleChinese()
          ? StickerDefault.mapNameRabbitCn[imagePath]
          : StickerDefault.mapNameRabbitEn[imagePath];
    }

      return BubbleImageStickerDefault(
        key: key,
        metadata: metadata,
        imagePath: messageInfo.filePath,
        fromNameTextColor: controller.fromNameTextColor.value,
      );
    case MessageType.sticker:
      return BubbleImageSticker(
        key: key,
        metadata: metadata,
        imageUrl: messageInfo.fileUrl,
        imagePath: messageInfo.filePath,
        fromNameTextColor: controller.fromNameTextColor.value,
        imgHeightMaps: controller.imgHeightMaps,
        imgWidthMaps: controller.imgWidthMaps,
        fileFragment: messageInfo.fileFragment,
      );
    case MessageType.image:
      var filePath=messageInfo.thumbnailPath;
      var fileFragment=messageInfo.thumbnailFragment;
      if(messageInfo.filePath?.isNotEmpty??false){
        filePath=messageInfo.filePath;
        fileFragment=messageInfo.fileFragment;
      }

      return BubbleImage(
        key: key,
        metadata: metadata,
        imagePath:filePath,
        imageUrl: messageInfo.fileUrl,
        fromNameTextColor: controller.fromNameTextColor.value,
        imgHeightMaps: controller.imgHeightMaps,
        imgWidthMaps: controller.imgWidthMaps,
        fileFragment: fileFragment,
      );
    case MessageType.audio:
      var duration = messageInfo.fileDuration ?? 0;
      return BubbleAudio(
        key: key,
        metadata: metadata,
        filePath: messageInfo.filePath,
        duration: Duration(milliseconds: duration),
        fromNameTextColor: controller.fromNameTextColor.value,
        weakList: controller.imagesWeakMap,
        fileFragment: messageInfo.fileFragment,

      );
    case MessageType.video:
      return BubbleVideo(
        key: key,
        metadata: metadata,
        imagePath: messageInfo.thumbnailPath,
        imageUrl: messageInfo.fileUrl,
        fromNameTextColor: controller.fromNameTextColor.value,
        imgHeightMaps: controller.imgHeightMaps,
        imgWidthMaps: controller.imgWidthMaps,
        fileFragment: messageInfo.thumbnailFragment,
      );
    case MessageType.file:
      return BubbleFile(
        key: key,
        metadata: metadata,
        filePath: messageInfo.filePath,
        fileName: messageInfo.fileName,
        fileSize: messageInfo.fileSize,
        fromNameTextColor: controller.fromNameTextColor.value,
      );
    case MessageType.contactCard:
      {
        ContactBean? bean;
        try {
          bean = ContactBean.fromJson(jsonDecode(messageInfo.body ?? "{}"));
          bean.from=messageInfo.from;
        } catch (e) {
          bean = null;
        }

        MemberInfo? info = controller.ownInfo(bean?.userName);
        return StreamBuilder(
          stream: controller.shareContactInfo(bean),
          builder: (context, snapshot) {
            String? displayName = bean?.displayName;
            String? avatarPath;

            if (snapshot.data != null) {
              info = snapshot.data as MemberInfo;
            }

            if (info != null) {
              displayName = info?.nickname ?? info?.displayname;
              avatarPath = info?.avatarPath;
            }

            return BubbleContact(
              key: key,
              metadata: metadata,
              displayName: displayName,
              username: bean?.userName,
              avatarPath: avatarPath,
              fromNameTextColor: controller.fromNameTextColor.value,
            );
          },
        );
      }
    case MessageType.fail:
      {
        return BubbleFail(
          key: key,
          metadata: metadata,
          state: messageInfo.state,
          fromNameTextColor: controller.fromNameTextColor.value,
        );
      }
    case MessageType.channelOpera:
    case MessageType.tip:
      {
        return TextChip(
          key: key,
          title: messageInfo.body,
          textColor: controller.fromNameTextColor.value,
        );
      }
    case MessageType.date:
      {
        return DateChip(
          key: key,
          date: messageInfo.dateTime,
          textColor: controller.fromNameTextColor.value,
        );
      }
    case MessageType.call:
      {
        return BubbleCall(
          key: key,
          metadata: metadata,
          text: messageInfo.body,
          isVideoCall: messageInfo.ext1 == 'video',
          fromNameTextColor: controller.fromNameTextColor.value,
        );
      }
    case MessageType.channelCard:
      {
        AppLogger.d('channelCard===${messageInfo.body}');
        ChannelCard? bean;
        try {
          bean = ChannelCard.fromJson(jsonDecode(messageInfo.body ?? "{}"));
        } catch (e) {
          bean = null;
        }

        return StreamBuilder(
          stream: controller.getChannelAvatarPath(bean),
          builder: (context, snapshot) {
            String? displayName = bean?.title;
            String? avatarPath;
            if (snapshot.data != null) {
              avatarPath = snapshot.data;
            }
            return BubbleChannel(
              key: key,
              metadata: metadata,
              displayName: displayName,
              description: bean?.description,
              avatarPath: avatarPath,
              fromNameTextColor: controller.fromNameTextColor.value,
              channelId:bean?.channelId??"",
              tokenAddress:bean?.tokenAddress,
              channelAttribute: bean?.channelAttribute,
              chainId: bean?.chainId,
            );
          },
        );
      }
    case MessageType.walletAddress:
      {
        WalletModel? bean;
        try {
          bean = WalletModel.fromJson(jsonDecode(messageInfo.body ?? "{}"));
          String? chainId = bean.chainId.toString();
          var filePath = web3_util.iconByChainType(bean.chainType??-1,chainId:chainId);
          var displayName= web3_util.chainNameByChainType(bean.chainType??0,chainId:chainId);
          return BubbleWalletAddress(
            key: key,
            metadata: metadata,
            displayName: displayName,
            description: bean.address,
            avatarPath: filePath,
            fromNameTextColor: controller.fromNameTextColor.value, value:'', title: '', address: '',
          );
        } catch (e) {
          bean = null;
        }
      }
    case MessageType.walletTransaction:
      {
        WalletTransactionModel? bean;
        try {
          bean = WalletTransactionModel.fromJson(jsonDecode(messageInfo.body ?? "{}"));
        } catch (e) {
          bean = null;
        }
        var transactionType = (bean?.transactionType??TransactionType.balance.index);
        var filePath = transactionType==TransactionType.balance.index?web3_util.netWorkIconByChatId(bean?.chainId??0):web3_util.iconBySymbolOrNetUuid(bean?.symbol??"");
        var displayName=bean?.symbol;
        return BubbleWalletAddress(
          key: key,
          metadata: metadata,
          displayName: displayName,
          description: bean?.netName??"",
          avatarPath: filePath,
          fromNameTextColor: controller.fromNameTextColor.value, value:"${bean?.value??0.0}", title: messageInfo.direction==Direction.outGoing?L.send_a_transfer.tr:L.receive_a_transfer.tr, address: bean?.address??'',
        );
      }
      case MessageType.walletBill:
      {
        ToWalletTransactionModel? bean2;
        try {
          bean2 = ToWalletTransactionModel.fromJson(jsonDecode(messageInfo.body ?? "{}"));
          AppLogger.d('MessageType.walletBill messageInfo.body==${messageInfo.body}');
        } catch (e) {
          bean2 = null;
        }
        String? chainId = bean2?.chainList?.first.chainId;
        var filePath = web3_util.iconByChainType(bean2?.chainType??0,chainId:chainId); ///暂时写死
        double value = bean2?.amount??0;
        var amountStr = value.isZero?'':Decimal.parse(value.toString()).toString();
        return BubbleWalletAddress(
          key: key,
          metadata: metadata,
          displayName: bean2?.content,
          description: bean2?.toAddress,
          avatarPath: filePath,
          fromNameTextColor: controller.fromNameTextColor.value,
          value: amountStr,
          title: bean2?.title,
          address: '',
        );
      }
    case MessageType.moneyExchange:
      {
        MoneyExchangeModel? bean;
        try {
          bean =
              MoneyExchangeModel.fromJson(jsonDecode(messageInfo.body ?? "{}"));
          return BubbleMoneyExchange(
            key: key,
            metadata: metadata,
            model: bean,
            fromNameTextColor: controller.fromNameTextColor.value,
          );
        } catch (e) {
          bean = null;
        }
        break;
      }
    case MessageType.hongbao:
      {
        return BubbleHongBao(
          key: key,
          metadata: metadata,
          fromNameTextColor: controller.fromNameTextColor.value,
          text:messageInfo.body,
          exten: messageInfo.extension,
        );
       }
    case MessageType.meeting: //会议气泡
      {
        Meeting? bean;
        try {
          bean = Meeting.fromJson(jsonDecode(messageInfo.body ?? "{}"));
        } catch (e) {
          bean = null;
        }
        String date = "";
        String time = "";
        String? passcode;
        if(bean?.startTime != null){
          date = formatDateTime(
            DateFormat("yyyy-MM-dd", Get.locale?.languageCode),
            bean!.startTime!,
          );
        }
        if(bean?.startTime != null && bean?.endTime != null){
          String startTime = formatDateTime(
            DateFormat("hh:mm a", Get.locale?.languageCode),
            bean!.startTime!,
          );
          String endTime = formatDateTime(
            DateFormat("hh:mm a", Get.locale?.languageCode),
            bean.endTime!,
          );
          time =  startTime + " - " + endTime;
        }
        passcode = bean?.passcode;

        return BubbleMeeting(
          key: key,
          metadata: metadata,
          fromNameTextColor: controller.fromNameTextColor.value,
          title: bean?.title ?? 'Meeting',
          date: date,
          time: time,
          passcode: passcode,
        );
      }
    // case MessageType.daoPoll: //投票气泡
    //   {
    //     Poll? bean;
    //     try {
    //       bean = Poll.fromJson(jsonDecode(messageInfo.body ?? "{}"));
    //     } catch (e) {
    //       bean = null;
    //     }
    //     return BubbleDaoPoll(
    //       key: key,
    //       metadata: metadata,
    //       fromNameTextColor: controller.fromNameTextColor.value,
    //       title: bean?.title,
    //       desc: bean?.desc,
    //       type: bean?.type,
    //       isOngoing: bean?.status == DaoPollStatus.ongoing && bean?.channelId == controller.currentUser?.userName,
    //     );
    //   }
    default:
      break;
  }
  AppLogger.e("_buildItem: $type");
  return BubbleNone(
    key: key,
    metadata: metadata,
    fromNameTextColor: controller.fromNameTextColor.value,
  );
}

Future showMenuDialog(
  BuildContext context,
  MessageController controller,
  int index,
  bool isSender,
  ShowMenuParams params,
) {
  int copy = 0x01;
  int delete = 0x02;
  int reply = 0x04;
  int forward = 0x08;
  int undo = 0x10;
  int pin = 0x20;
  int select = 0x40;
  int save = 0x80;
  int top = 0x100;
  int complaint = 0x200;
  int mute = 0x400;
  int qr = 0x800;
  int translate = 0x1000;//翻译

  // int menuMode = reply | select | delete | top | forward;
  RxInt menuMode =0.obs;
   menuMode.value = select | delete | forward;

  MessageEvent messageInfo = controller.messageList[index];
  AppLogger.d('messageInfo=${messageInfo.state}');
  bool selfDestruct = controller.rxSelfDestruct.value;
  // int inMilliseconds =
  //     TimeTask.instance.getNowDateTime().difference(messageInfo.dateTime).inMilliseconds;
  AppLogger.d('messageInfo body =${isSender } ${messageInfo.fileUrl} ${messageInfo.state }');

  if (isSender && true/*
      (inMilliseconds < defaultUndoMsgTime)*/ &&
      (messageInfo.chatType != ChatType.channelChat &&
          messageInfo.chatType != ChatType.officialChat) &&
      (messageInfo.state == BubbleItemState.DELIVERED ||
          messageInfo.state == BubbleItemState.SEEN)) {

    menuMode.value = menuMode.value | undo;
  }
  if(messageInfo.chatType == ChatType.channelChat){
    bool mySelfSend = messageInfo.direction==Direction.outGoing;
    if(mySelfSend){
      menuMode.value = menuMode.value | undo;
    }else{
      final mySelfUsername = Get.find<AppConfigService>().getUserName();
      String? from = messageInfo.from;
      var msgController = Get.find<MessageController>();
      bool memberIsOwner = from == msgController.channelOwner;
      bool memberIsAdmin = msgController.channelAdminList.contains(from);
      bool mySelfIsOwner = mySelfUsername == msgController.channelOwner;
      bool mySelfIsAdmin = msgController.channelAdminList.contains(mySelfUsername);
      if (mySelfIsOwner||(mySelfIsAdmin&&!memberIsOwner && !memberIsAdmin)) {
        menuMode.value = menuMode.value | mute | undo;
      }
    }
  }
  if ((messageInfo.state == BubbleItemState.DELIVERED ||
          messageInfo.state == BubbleItemState.SEEN ||
          !isSender) &&
      (messageInfo.type.index <= 4 && messageInfo.type.index != 2)) {
    menuMode.value = menuMode.value | reply;
  }


  switch (messageInfo.type) {
    case MessageType.text:
    case MessageType.walletAddress:
    case MessageType.meeting:
      menuMode.value = menuMode.value | copy | top;
      break;
    // case MessageType.daoPoll:
    //   menuMode.value = select | delete | forward | top;
    //   break;
    case MessageType.image:
    case MessageType.msgMergeForward:
    case MessageType.video:
    case MessageType.file:
      if(messageInfo.type==MessageType.video||messageInfo.type==MessageType.file||messageInfo.type==MessageType.image){
        menuMode.value = menuMode.value | top;
      }
      var path = messageInfo.filePath??'';
      var qrPath = messageInfo.filePath??messageInfo.thumbnailPath??'';
      // bool thump = false;
      // if(messageInfo.type==MessageType.video||messageInfo.type==MessageType.image){
      //   var thumbPath = messageInfo.thumbnailPath;
      //   thump=((thumbPath?.isEmpty ?? true) || !File(thumbPath!).existsSync()) || ((messageInfo.thumbnailUrl?.isEmpty ?? true) && (messageInfo.thumbnailFragment?.isEmpty ?? true));
      // }
      if (messageInfo.type == MessageType.image) {
        if ((messageInfo.ext1?.isNotEmpty ?? false)) {
          menuMode.value = menuMode.value | qr;
        } else if (!(messageInfo.hasIdentify ?? false) && qrPath.isNotEmpty) {
          controller.recognizeQRCode(messageInfo).then((value){
            if(value!=null){
              messageInfo.ext1 = value;
              menuMode.value = menuMode.value | qr;
            }
          });

        }
      }

      if (messageInfo.fileUrl?.isEmpty ?? true) {
        menuMode.value = menuMode.value ^ forward;
      }
      break;
    case MessageType.audio:
    case MessageType.sticker:
    case MessageType.stickerDefault:
    case MessageType.stickerDefaultRabbit:
    case MessageType.stickerDefaultEmoji:
    // case MessageType.channelCard:
    case MessageType.walletAddress:
    case MessageType.walletBill:
    case MessageType.walletTransaction:
    case MessageType.moneyExchange:
    menuMode.value = menuMode.value ^ forward;
      break;
    case MessageType.hongbao:
      menuMode.value = menuMode.value ^ forward ^ undo ^ mute ^ complaint;
      break;
    default:
      break;
  }
  if (messageInfo.selfDestruct) {
    menuMode.value = menuMode.value ^ forward ^ copy ^ top;
    if (menuMode.value & reply == reply) {
      menuMode.value = menuMode.value ^ reply;
    }
  }
  if (selfDestruct) {
    //当前是阅后即焚模式
    if (menuMode.value & reply == reply) {
      menuMode.value = menuMode.value ^ reply;
    }
  } else {
    if ((messageInfo.type == MessageType.text ||
        messageInfo.type == MessageType.video ||
        messageInfo.type == MessageType.image ||
        messageInfo.type == MessageType.audio ||
        messageInfo.type == MessageType.file)&&messageInfo.direction==Direction.inComing) {
      menuMode.value = menuMode.value | complaint;
    }
  }

  if (/*Platform.isAndroid&&*/ (messageInfo.type == MessageType.video ||
      messageInfo.type == MessageType.image ||
      messageInfo.type == MessageType.file)) {
    menuMode.value = menuMode.value | save;
  }

  if (messageInfo.state == BubbleItemState.SENSITIVE) {
    menuMode.value = menuMode.value ^ forward ^ complaint;
  }

  if (messageInfo.state == BubbleItemState.FAILED) {
    menuMode.value = menuMode.value ^ complaint ^ forward;
  }
  if (messageInfo.chatType == ChatType.officialChat) {
    menuMode.value = menuMode.value ^ reply ^ complaint ;
  }
  if (messageInfo.type == MessageType.text) {
    menuMode.value = menuMode.value | translate ;
  }
  VibrationUtil.instance.vibrationHeavyImpact();
  return SmartDialog.show(
      tag: messageMenuPopTag,
      useAnimation: false,
      builder: (c) {
        return Obx(() => MenuDialog(
          params: params,
          isSender: isSender,
          messageInfo: messageInfo,
          itemWidget: buildItem(
                context,
                controller,
                messageInfo,
                joinZeroWidthSpace: true,
                isDialog: true,
                onTap: () => {SmartDialog.dismiss(tag: messageMenuPopTag)},
              ),
              onSave: menuMode.value & save == save
              ? () async {
            SmartDialog.dismiss(tag: messageMenuPopTag);
            controller.onSave(messageInfo);
          }
              : null,
          onCopy: menuMode.value & copy == copy
              ? () async {
            SmartDialog.dismiss(tag: messageMenuPopTag);
            controller.onCopyMessage(messageInfo);
          }
              : null,

          onDelete: menuMode.value & delete == delete
              ? () async {
            SmartDialog.dismiss(tag: messageMenuPopTag);
            showBottomDialogCommonWithCancel(
              context,
              widgets: [
                getBottomSheetItemSimple(
                  context,
                  L.chat_if_confirm_del_this_message.tr,
                  textColor: Colors.red,
                  itemCallBack: () {
                    controller.onDeleteMessage([messageInfo]);
                  },
                ),
              ],
            );
          }
              : null,
          onComplaint: menuMode.value & complaint == complaint
              ? () async {
            SmartDialog.dismiss(tag: messageMenuPopTag);
            controller.onMessageSelected(messageInfo, clean: true);
            controller.changeComplaint(context);
          }
              : null,
          onReply: menuMode.value & reply == reply
              ? () {
            SmartDialog.dismiss(tag: messageMenuPopTag);
            controller.setReplayData(index);
          }
              : null,
          onForward: menuMode.value & forward == forward
              ? () {
            SmartDialog.dismiss(tag: messageMenuPopTag);
            controller.onForwardMessage([messageInfo]);
          }
              : null,
          onTranslate: menuMode.value & translate == translate
              ? () {
            SmartDialog.dismiss(tag: messageMenuPopTag);
            controller.onTranslateMessage(context,messageInfo);
          }
              : null,
          onUndo: menuMode.value & undo == undo
              ? () {
            SmartDialog.dismiss(tag: messageMenuPopTag);

            controller.onUndoMessage(messageInfo);
          }
              : null,
          onPin: menuMode.value & pin == pin
              ? () {
            SmartDialog.dismiss(tag: messageMenuPopTag);
            // controller.onTopMessage(index);
          }
              : null,
          onSelect:
          (menuMode.value & select == select) && !controller.rxSelfDestruct.value
              ? () {
            SmartDialog.dismiss(tag: messageMenuPopTag);
            controller.selectedMessageEventList.clear();
            controller.changeMessageCanMultiSelect(true);
            controller.selectedMessageEventList.add(messageInfo);
          }
              : null,
          onTop: menuMode.value & top == top
              ? () async {
            if (controller.mySelfIsChannelAdminOrOwner()&&(messageInfo.state==BubbleItemState.DELIVERED||messageInfo.direction==Direction.inComing)) {
              var ret =await SmartDialog.show(
                tag: TopMsgDialog.topMsgDialogTag,
                builder: (c) {
                  return TopMsgDialog(
                    index: index,
                    callBack: (check) async {
                      if(check){
                        var ret = await controller.msgTopAdmin(index);
                        return ret;
                      }else{
                        controller.msgTop(index);
                        return true;
                      }
                    },
                  );
                },
              );
              if(ret==true){
                SmartDialog.dismiss(tag: messageMenuPopTag);
              }
            }else{
              controller.msgTop(index);
              SmartDialog.dismiss(tag: messageMenuPopTag);
            }
          }
              : null,
          onMute: menuMode.value & mute == mute
              ? () {
            SmartDialog.dismiss(tag: messageMenuPopTag);
            controller.onMute(messageInfo);
          }
              : null,
          onQrRecognition: menuMode.value & qr == qr
              ? () {
            SmartDialog.dismiss(tag: messageMenuPopTag);
            controller.jumpQRData(messageInfo);
          }
              : null,
        ));
      });
}

bool isShowMenuDialog = false;
RenderBox? box;
ShowMenuParams? params;

Widget createMessageList(
  MessageController controller,
  double paddingTop,
) {
  var outGoingUser = Get.find<AppConfigService>().getUserName();
  return Listener(
    onPointerUp: (down) {
      AppLogger.d("onPointerUp createMessageList");
      if (!isShowMenuDialog && !controller.isUnDoEditOnTapDown) {
        controller.onTextEditUnfocus();
      }
      if (controller.isUnDoEditOnTapDown) {
        controller.isUnDoEditOnTapDown = false;
      }
    },
    child: SizedBox(
      height: double.infinity,
      child: InkWell(
        child: Obx(
          () => ScrollablePositionedList.builder(
            itemScrollController: controller.getItemScrollController(),
            itemPositionsListener: controller.itemPositionsListener,
            itemCount: controller.messageList.length,
            addAutomaticKeepAlives:false,
            addRepaintBoundaries:false,
            reverse: true,
            // physics: const NeverScrollableScrollPhysics(),
            shrinkWrap: true,
            itemBuilder: (context, index) {
              if (index < 0) {
                index = 0;
              }
              GlobalKey key = GlobalKey();
              MessageEvent messageInfo = controller.messageList[index];
              bool isSender = messageInfo.direction == 1;
              var id = messageInfo.direction==Direction.outGoing?outGoingUser:messageInfo.from;
              if(id?.isEmpty ??true){
                AppLogger.d('createMessageList id = $id');
              }
              return GetBuilder<MessageController>(
                id: id,
                builder: (controller) {
                  return GetBuilder<MessageController>(
                    id: messageInfo.msgId,
                    builder: (controller) {
                      MessageEvent messageInfo = controller.messageList[index];
                      return GestureDetector(
                        onTap: () {
                          controller.onMessageSelected(messageInfo);
                        },
                        child: AbsorbPointer(
                          absorbing: controller.isCanMultiMessage(messageInfo),
                          child: Container(
                            color: messageInfo.replayClicked ?? false
                                ? Colors.grey.shade100.withOpacity(0.2)
                                : Colors.transparent,
                            child: Row(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                Visibility(
                                    visible:
                                    controller.isCanMultiMessage(messageInfo),
                                    child: Container(
                                      margin: EdgeInsets.only(
                                        left: 12.w,
                                      ),
                                      width: 32.w,
                                      height: 20.h,
                                      child: Center(
                                          child: Image.asset(
                                            controller.selectedMessageEventList
                                                .contains(messageInfo)
                                                ? R.contactorChecked
                                                : R.contactorDefault,
                                            width: 20.w,
                                            height: 20.h,
                                          )),
                                    )),
                                Expanded(
                                  child: buildItem(context, controller, messageInfo,
                                      key: key, onTap: () {
                                        FocusNode focusNodeInput =
                                            controller.focusNodeInput;
                                        if (focusNodeInput.hasFocus) {
                                          controller.focusNodeInput.unfocus();
                                          //return;
                                        }
                                        // 点击控件
                                        itemOnTop(context, controller, index);
                                      }, onLongPress: () async {
                                        box = key.currentContext?.findRenderObject()
                                        as RenderBox?;
                                        AppLogger.d(
                                            "Get.height==${Get.height} paddingTop==$paddingTop  ");
                                        double bottomHeight = 0;
                                        if (controller.isSticker.value) {
                                          bottomHeight = controller.stickerHeight.value;
                                        } else {
                                          bottomHeight = controller
                                              .containerFillBottomHeight.value;
                                        }
                                        double h = Get.height - bottomHeight;
                                        AppLogger.d(
                                            "Get.height==$h paddingTop==$paddingTop  ");
                                        params = ShowMenuParams(
                                            box, h, paddingTop, bottomHeight);
                                        isShowMenuDialog = true;
                                        AppLogger.d("showMenuDialog start!!!");
                                        await showMenuDialog(context, controller, index,
                                            isSender, params!);
                                        AppLogger.d("showMenuDialog end!!!");
                                        isShowMenuDialog = false;
                                      }, onAvatarTap: () {
                                        if(controller.contactLimit && controller.isChannelChat()){
                                          return;
                                        }
                                        MemberInfo? info =
                                        controller.ownInfo(messageInfo.from);
                                        Get.to(
                                          ContactDetailView(
                                            channelId: messageInfo.owner,
                                          ),
                                          arguments: ContactData(
                                            id: -1,
                                            username: messageInfo.from ?? '',
                                            displayname: info?.name,
                                            avatarPath: info?.avatarPath,
                                          ),
                                        );
                                      },
                                      onAvatarLongTap: controller.isSingleChat()
                                          ? null
                                          : () {
                                        controller.insertAt(messageInfo.from);
                                      },
                                      onReplayTap: controller.replayOnClick),
                                )
                              ],
                            ),
                          ),
                        ),
                      );
                    },
                  );
                },
              );
            },
          ),
          // ),
        ),
      ),
    ),
  );
}

// late KeyboardVisibilityController keyboardVisibilityController;
// late StreamSubscription<bool> keyboardSubscription;

Widget createInputBox(BuildContext context) {
  return GetBuilder<MessageController>(
    id: 'message_page_title',
    dispose: (state) {
      AppLogger.d('Keyboard visibility view keyboardSubscription.cancel()');
      // keyboardSubscription.cancel();
    },
    initState: (state) {
      // keyboardVisibilityController = KeyboardVisibilityController();
      // keyboardSubscription =
      //     keyboardVisibilityController.onChange.listen((bool visible) {
      //   AppLogger.d(
      //       'Keyboard visibility view direct query Is visible: $visible');
      //   state.controller
      //       ?.keyboard(keyboardVisibilityController.isVisible, state.context);
      // });
    },
    builder: (control) {
      bool visible = control.showInputView();
      var selfDestruct = control.rxSelfDestruct.value;
      bool isOffice = control.isOfficialChat();
      bool isMeetingRobot = control.isMeetingRobot();
      return Visibility(
        visible: visible,
        child: Container(
          padding: const EdgeInsets.fromLTRB(0, 0, 0, 0),
          margin: const EdgeInsets.fromLTRB(0, 0, 0, 0),
          decoration: const BoxDecoration(
            color: AppColors.backgroundGray,
          ),
          child: Stack(
            alignment: Alignment.centerRight,
            children: [
              Column(
                children: [
                  control.getReplayWidget(context),
                  const DividerCus(),
                  Stack(
                    alignment: AlignmentDirectional.center,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          Expanded(
                            child: Visibility(
                              visible: true,
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.end,
                                mainAxisSize: MainAxisSize.max,
                                children: [
                                  GestureDetector(
                                    behavior: HitTestBehavior.opaque,
                                    onTap: () {
                                      control.changeMore(context);
                                    },
                                    child: Padding(
                                      padding: EdgeInsets.only(
                                          left: 16.w, right: 13.w),
                                      child: isOffice
                                          ? Container()
                                          : Image.asset(
                                              selfDestruct
                                                  ? R.attachFileRed
                                                  : R.attachFile,
                                              width: 21.r,
                                              height: 21.r,
                                            ),
                                    ),
                                  ),
                                  Flexible(
                                    child: Obx(() {
                                      return control.isShowTextField()
                                          ? Container(
                                              margin: const EdgeInsets.only(
                                                      left: 0,
                                                      top: 12,
                                                      right: 0,
                                                      bottom: 12)
                                                  .r,
                                              decoration: BoxDecoration(
                                                color: Colors.white,
                                                borderRadius:
                                                    BorderRadius.circular(30).r,
                                              ),
                                              child: Center(
                                                child: Row(
                                                  children: [
                                                    const SizedBox(width: 10),
                                                    Flexible(
                                                      // width: context.mediaQuerySize.width * .5,
                                                      child: Container(
                                                        constraints:
                                                            BoxConstraints(
                                                                minHeight:
                                                                    30.h),
                                                        child: Center(
                                                          child:
                                                              ExtendedTextField(
                                                            strutStyle:
                                                                const StrutStyle(
                                                              forceStrutHeight:
                                                                  false,
                                                            ),
                                                            style: TextStyle(
                                                              fontSize: 16.sp,
                                                              height: 1.3,
                                                              color:
                                                                  Colors.black,
                                                            ),
                                                            controller: control
                                                                .textEditingControllerInput,
                                                            focusNode: control
                                                                .focusNodeInput,
                                                            onChanged: control
                                                                .onTextChanged,
                                                            // onEditingComplete: controller.onTextEditCompleted,
                                                            decoration:
                                                                const InputDecoration(
                                                              border:
                                                                  InputBorder
                                                                      .none,
                                                              counterText: '',
                                                              // hintText: 'Message',
                                                              isCollapsed: true,
                                                              contentPadding:
                                                                  EdgeInsets
                                                                      .fromLTRB(
                                                                          5,
                                                                          5,
                                                                          5,
                                                                          5),
                                                            ),
                                                            specialTextSpanBuilder:
                                                                MySpecialTextSpanBuilder(
                                                              showAtBackground:
                                                                  false,
                                                            ),
                                                            maxLines: 5,
                                                            minLines: 1,
                                                            maxLength: control
                                                                .textMaxLength,
                                                          ),
                                                        ),
                                                      ),
                                                    ),
                                                    // const SizedBox(width: 30),
                                                    GestureDetector(
                                                      behavior:
                                                                    HitTestBehavior
                                                                        .opaque,
                                                      onTap: () async {
                                                                  await showModalBottomSheet(
                                                                    context:
                                                                        context,
                                                                    isScrollControlled:
                                                                        true,
                                                                    useSafeArea:
                                                                        true,
                                                                    shape:
                                                                        RoundedRectangleBorder(
                                                                      borderRadius:
                                                                          BorderRadius
                                                                              .only(
                                                                        topLeft:
                                                                            Radius.circular(15.r),
                                                                        topRight:
                                                                            Radius.circular(15.r),
                                                                      ),
                                                                    ),
                                                                    builder:
                                                                        (context) {
                                                                      return expandedInputBox(
                                                                        context,
                                                                        control,
                                                                      );
                                                                    },
                                                                  );
                                                                },
                                                      child: Padding(
                                                        padding: EdgeInsets.only(top:8.r, bottom: 8.r, right: 8.r),
                                                        child: Icon(Icons.fullscreen, size: 24.r,),
                                                      ),
                                                    )
                                                  ],
                                                ),
                                              ),
                                            )
                                          : RecordButtonIM(
                                              dir: appTempDir!,
                                              messageController: control,
                                              onRecordFinished:
                                                  control.onRecordFinished,
                                            );
                                    }),
                                  ),
                                ],
                              ),
                            ),
                          ),
                          /* Obx(
                            () => (control.isText || selfDestruct)
                            ? const SizedBox(height: 35)
                            :*/
                          Obx(() {
                            var isSticker = control.isSticker.value;

                            bool visible = true;
                            if (control.rxIsRecordIM.value) {
                              visible = false;
                            }

                            return Visibility(
                              visible: visible,
                              child: GestureDetector(
                                onTap: control.onSticker,
                                behavior: HitTestBehavior.opaque,
                                child: Padding(
                                  padding: EdgeInsets.only(left: 10.r),
                                  child: Image.asset(
                                    isSticker
                                        ? R.icInputEmoSelected
                                        : (selfDestruct
                                            ? R.icInputEmoRed
                                            : R.icInputEmo),
                                    width: 21.r,
                                    height: 21.r,
                                  ),
                                ),
                              ),
                            );
                          }),

                          /*    ),*/
                          Obx(
                            () {
                              bool visible = control.isText || isOffice || isMeetingRobot;
                              double width = 60.w;
                              if (selfDestruct && !control.isText) {
                                visible = false;
                                width = 16.w;
                              }

                              return IntrinsicHeight(
                                child: SizedBox(
                                  width: width,
                                  child: Visibility(
                                    visible: visible,
                                    child: GestureDetector(
                                      behavior: HitTestBehavior.opaque,
                                      onTap: () {
                                        control.onSendTextMessage();
                                      },
                                      child: Center(
                                        child: Image.asset(
                                          R.icInputSend,
                                          width: 21.r,
                                          height: 21.h,
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              );
                            },
                          ),
                        ],
                      ),
                      Align(
                        alignment: Alignment.centerRight,
                        child: Container(
                          margin: EdgeInsets.only(right: 10.r),
                          child: Obx(() {
                            if (selfDestruct ||
                                (control.rxRecordStatus.value ==
                                        RecordStatus.none &&
                                    control.isText)) {
                              return const SizedBox();
                            }
                            var isRecord = control.rxIsRecordIM.value;
                            return isOffice || isMeetingRobot
                                ? Container()
                                : GestureDetector(
                                    behavior: HitTestBehavior.opaque,
                                    onTap: () {
                                      control.rxIsRecordIM.value =
                                          !control.rxIsRecordIM.value;
                                      if (control.rxIsRecordIM.value) {
                                        control.onTextEditUnfocus();
                                      }
                                    },
                                    child: Padding(
                                      padding: EdgeInsets.only(right: 16.r),
                                      child: Image.asset(
                                        isRecord
                                            ? R.icoExpandButton
                                            : R.icInputAudio,
                                        height: 21.r,
                                      ),
                                    ),
                                  );
                          }),
                        ),
                      ),
                    ],
                  ),
                  Obx(() {
                    return Visibility(
                      visible: control.isSticker.value,
                      // child: createSticker(context, control),
                      child: MyStickerView(
                        messageController: control,
                      ),
                    );
                  }),
                  Obx(() {
                    return Visibility(
                      visible: !control.isSticker.value,
                      child: createContainerFillBottom(context, control),
                    );
                  }),
                ],
              ),
              Obx(
                () {
                  String text = '';
                  switch (control.tabooType) {
                    case TabooType.taboo:
                      text = L.taboo.tr;
                      break;
                    case TabooType.taboo_all:
                      text = L.taboo_all.tr;
                      break;
                    case TabooType.groupOnlySelf:
                      text = L.group_only_self.tr;
                      break;
                    default:
                  }

                  return Visibility(
                    visible: control.isTabooType.value,
                    child: Positioned(
                      left: 0,
                      right: 0,
                      top: 0,
                      bottom: 0,
                      child: AbsorbPointer(
                        absorbing: true,
                        child: Center(
                          child: Text(
                            text,
                            style:
                                const TextStyle(color: AppColors.colorFF999999),
                          ),
                        ),
                      ),
                    ),
                  );
                },
              ),
            ],
          ),
        ),
      );
    },
  );
}

Widget expandedInputBox(BuildContext context, MessageController control) {
  return Padding(
    padding: EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
    child: Container(
      constraints: BoxConstraints(minHeight: 450.r),
      padding: EdgeInsets.only(top: 20.r),
      child: Column(
        children: [
          /// 关闭按钮
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 14.r),
            child: Align(
              alignment: Alignment.centerLeft,
              child: GestureDetector(
                onTap: () => Get.back(),
                child: Container(
                  width: 30.r,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: AppColors.colorFFC7C7C7,
                  ),
                  child: Center(
                    child: Icon(
                      Icons.keyboard_arrow_down_rounded,
                      size: 24.r,
                      color: AppColors.colorFF000000,
                    ),
                  ),
                ),
              ),
            ),
          ),
          SizedBox(height: 20.r),

          /// 输入框
          Expanded(
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 22.r),
              child: ExtendedTextField(
                // strutStyle:
                //     const StrutStyle(
                //   forceStrutHeight:
                //       false,
                // ),
                // scrollController: PrimaryScrollController.of(context),
                // autofocus: control.isSticker.value ? false : true,
                autofocus: true,
                style: TextStyle(
                  fontSize: 16.r,
                  height: 1.4,
                  color: AppColors.colorFF000000,
                ),

                controller: control.textEditingControllerInput,
                // focusNode: control.focusNodeInput,
                // onChanged: control.onTextChanged,
                decoration: InputDecoration(
                  hintText: L.enter_your_message_here.tr,
                  hintStyle: TextStyle(
                    color: AppColors.colorFF989898,
                    fontSize: 16.r,
                  ),
                  border: InputBorder.none,
                  counterText: '',
                  // hintText: 'Message',
                  isCollapsed: true,
                  contentPadding: EdgeInsets.fromLTRB(5, 5, 5, 5),
                ),
                specialTextSpanBuilder: MySpecialTextSpanBuilder(
                  showAtBackground: false,
                ),
                maxLines: 50,
                minLines: 1,
                maxLength: control.textMaxLength,
              ),
            ),
          ),
          SizedBox(
            height: 20.r,
          ),

          // SizedBox(height: MediaQuery.of(context).viewInsets.bottom),
        ],
      ),
    ),
  );
}

Widget createHeader(BuildContext context, UserMessage? user) {
  String? avatar = user?.avatarPath;
  String? name = user?.displayName;
  SessionTask.updateAvatar(user?.userName??'',avatarPath: user?.avatarPath);

  return buildChatAvatarWithAttr(user?.chatType ?? 0, user?.userName ?? "",
    diameter: 30,
    text: name,
    imagePath: avatar,
    textStyle: const TextStyle(fontSize: 16, color: Colors.white),
    iconSize: 15.r,
    channelAttribute: user?.channelAttribute,
    tokenAddress: user?.tokenAddress,
    chainId: user?.chainId,
  );
}

Widget createContainerFillBottom(
    BuildContext context, MessageController messageController) {
  return Obx(() {
    return Container(
      color: Colors.transparent,
      height: /* messageController.stickerHeight.value == 0
          ? StickerDefault.stickerDefaultHeight
          : */
          messageController.containerFillBottomHeight.value,
    );
  });
}
