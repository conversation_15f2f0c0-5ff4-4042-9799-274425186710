// import 'package:flutter/material.dart';
// import 'package:flutter_metatel/app/data/enums/enum.dart';
// import 'package:flutter_metatel/app/data/providers/api/channel.dart';
// import 'package:flutter_metatel/app/data/services/config_service.dart';
// import 'package:flutter_metatel/app/data/services/event_service.dart';
// import 'package:flutter_metatel/core/utils/app_log.dart';
// import 'package:flutter_metatel/core/values/code.dart';
// import 'package:get/get.dart';
//
// class MenuDialogController extends GetxController{
//   var canRepeal=false.obs;
//   var canMute=false.obs;
//   late MessageEvent messageEvent;
//   RxList<Widget> list = RxList();
//   @override
//   void onReady() {
//     if(messageEvent.chatType==ChatType.channelChat){
//       _requestChannelInfo();
//     }else{
//       canRepeal.value=true;
//       canMute.value=false;
//     }
//     super.onReady();
//   }
//
//   void _requestChannelInfo() async {
//     String username = messageEvent.owner;
//     String from = messageEvent.from??"";
//     var modelData = await getChannelInfoRequest(username);
//     AppLogger.d(
//         'MenuDialogController _requestChannelInfo modelData=${modelData.toJson()}');
//     var mySelfUsername = Get.find<AppConfigService>().getUserName();
//     if (modelData.code == Code.code200) {
//       bool memberIsOwner = from == modelData.owner;
//       bool memberIsAdmin = modelData.admins?.contains(from) ?? false;
//       bool mySelfIsOwner = mySelfUsername == modelData.owner;
//       bool mySelfIsAdmin = modelData.admins?.contains(mySelfUsername) ?? false;
//       if (mySelfIsOwner) {
//         canRepeal.value = true;
//         canMute.value = true;
//       } else if (mySelfIsAdmin) {
//         if (!memberIsOwner && !memberIsAdmin) {
//           canRepeal.value = true;
//           canMute.value = true;
//         }
//       }
//     }
//   }
// }
//
