import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';

import 'package:extended_image/extended_image.dart';
import 'package:extended_text/extended_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_metatel/app/data/providers/db/database.dart';
import 'package:flutter_metatel/app/data/providers/native/chatio/chatio_async.dart';
import 'package:flutter_metatel/app/modules/message/message_controller.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:flutter_metatel/core/utils/util.dart';
import 'package:flutter_metatel/core/values/colors.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../core/languages/l.dart';
import '../../../../core/values/config.dart';
import '../../../../r.dart';
import '../../../data/enums/enum.dart';
import '../../../data/models/replay_message_model.dart';
import '../../../data/models/weak_image_bean.dart';
import '../../../data/services/config_service.dart';
import '../../../data/services/event_service.dart';
import '../../../widgets/at_widget/my_special_text_span_builder.dart';

class ReplyController {
  final String replayId = 'replayId';
  final RxBool _isReplay = false.obs;
  final RxString displayName = ''.obs;
  MessageEvent? _replayMsgData;
  Uint8List? _imageUnitList;
  String? _imagePath;
  String? _thumpPath;
  List<WeakImageBean>? weakImage;
  MessageController? messageController;
  Uint8List?_fileUni8;
  Uint8List?_errorUni8;

  ReplyController(this.weakImage, this.messageController){
    _load();
  }
  _load()async{
    _fileUni8= (await rootBundle.load(R.file)).buffer.asUint8List();
    _errorUni8=(await rootBundle.load(R.icoPicLoadFailed)).buffer.asUint8List();
  }

  void setReplayData(MessageEvent? data) {
    if (data == null) {
      _replayMsgData = null;
      _imageUnitList = null;
      _isReplay.value = false;
    } else {
      _replayMsgData = data;
      if(isVideo()){
        _thumpPath=_replayMsgData?.thumbnailPath;
        _imageUnitList = getImageDatas(_replayMsgData?.thumbnailPath);
      }else if(isImage()){
        _thumpPath=_replayMsgData?.thumbnailPath??_replayMsgData?.filePath;
        _imagePath=_replayMsgData?.filePath;
        _imageUnitList = getImageDatas(_replayMsgData?.thumbnailPath)??getImageDatas(_replayMsgData?.filePath);
      }
      _isReplay.value = true;
      Get.find<AppConfigService>().saveReply(messageController?.getUserName()??"", data.msgId);
    }
    updateDisplayName();
    messageController?.update([replayId]);
  }

  void updateDisplayName() async {
    var userName = _replayMsgData?.from;
    if(userName==null||userName.isEmpty){
      if(_replayMsgData?.chatType==ChatType.singleChat){
        userName=_replayMsgData?.direction==Direction.outGoing?await Get.find<AppConfigService>().getUserName():_replayMsgData?.owner;
      }
    }
    if (userName != null && userName.isNotEmpty) {
      AppConfigService service = Get.find<AppConfigService>();
      if (await service.getUserName() == userName) {
        displayName.value = service.getMySelfDisplayName();
      } else {
        Get.find<AppDatabase>()
            .oneContact(userName)
            .getSingleOrNull()
            .then((value) {
          if (value != null) {
            displayName.value = displayNameProcess(value.displayname ??"", value.username);
            AppLogger.d(
                'ReplyController displayName.value111=${displayName.value}');
          }
        });
      }
    }
    AppLogger.d('ReplyController userName=$userName');

    AppLogger.d('ReplyController displayName.value=${displayName.value}');
  }

  Widget createReplayWidget(BuildContext context) {
    return Obx(
      () => Visibility(
        visible: _isReplay.value,
        child: Padding(
          padding:
              EdgeInsets.only(left: 16.w, top: 10.w, right: 16.w, bottom: 10.w),
          child: GetBuilder<MessageController>(
            id: replayId,
            builder: (controller) {
              return Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Container(
                    width: 2.w,
                    height: 29.w,
                    color: AppColors.appDefault,
                  ),
                  Visibility(
                    visible: isNotText(),
                    child: Row(
                      children: [
                        SizedBox(
                          width: 5.w,
                        ),
                        Container(
                          clipBehavior: Clip.hardEdge,
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.all(
                            const Radius.circular(3).r,
                          )),
                          child: Stack(
                            alignment: AlignmentDirectional.center,
                            children: [
                              createImageView(),
                              Visibility(
                                  visible: isVideo(),
                                  child: Container(
                                    width: 15,
                                    height: 15,
                                    decoration: BoxDecoration(
                                      color: Colors.black.withOpacity(0.5),
                                      borderRadius: BorderRadius.circular(20),
                                    ),
                                    child: const Icon(
                                      Icons.play_arrow,
                                      color: Colors.white,
                                      size: 10,
                                    ),
                                  )),
                            ],
                          ),
                        )
                      ],
                    ),
                  ),
                  SizedBox(
                    width: 5.w,
                  ),
                  Expanded(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Obx(() => Text(
                              getDisplayName(),
                              overflow: TextOverflow.ellipsis,
                              maxLines: 1,
                              style: TextStyle(
                                  fontSize: 12.sp, color: AppColors.appDefault),
                            )),
                        ExtendedText(
                          getTextData(),
                          specialTextSpanBuilder: MySpecialTextSpanBuilder(
                              showAtBackground: false,
                              showLinkColor: true,
                              linkColor: AppColors.appDefault,
                              atTextColor: AppColors.appDefault),
                          overflow: TextOverflow.ellipsis,
                          maxLines: 1,
                          style: TextStyle(
                              fontSize: 12.sp, color: AppColors.colorFF666666),
                        ),
                      ],
                    ),
                  ),
                  GestureDetector(
                    onTap: () {
                      closeReplay();
                    },
                    child: Image.asset(
                      R.icReplayClose,
                      width: 18.5.w,
                      height: 18.5.w,
                    ),
                  ),
                ],
              );
            },
          ),
        ),
      ),
    );
  }

  Uint8List? getImageDatas(String? imagePath) {
    Uint8List? data;
    if (imagePath != null) {
      try {
        data =
            weakImage?.firstWhere((element) => element.path == imagePath).data;
      } catch (e) {}
      return data;
    }
    return null;
  }

  void closeReplay() {
    _imageUnitList = null;
    _replayMsgData = null;
    _isReplay.value = false;
    Get.find<AppConfigService>().removeReply(messageController?.getUserName()??"");
  }

  String getDisplayName() {
    return displayName.value;
  }

  String getTextData() {
    String data = _replayMsgData?.body ?? '';
    switch (_replayMsgData?.type) {
      case MessageType.image:
        data = L.other_pic_message.tr;
        break;
      case MessageType.video:
        data = L.other_video_message.tr;
        break;
      case MessageType.file:
        data = _replayMsgData?.fileName??L.other_file_message.tr;
        break;
      default:
    }
    return data;
  }

  bool isNotText() {
    return _replayMsgData?.type.index != 0;
  }

  bool isVideo() {
    return _replayMsgData?.type.index == 3;
  }
  bool isImage() {
    return _replayMsgData?.type == MessageType.image;
  }
  MessageType getType() {
    return _replayMsgData?.type ?? MessageType.text;
  }

  Widget createImageView() {
    String? imagePath = _replayMsgData?.thumbnailPath??_replayMsgData?.filePath;
    if ((_imageUnitList?.isNotEmpty ?? false) && imagePath != null) {
      return createExtendedImageView(_imageUnitList, imagePath);
    }else if(_thumpPath?.isNotEmpty??false){
      if(isCipherFile(_thumpPath)){
        return createEncExtendedImageView(_thumpPath!,_replayMsgData?.thumbnailFragment??"");
      }
      return createExtendedImageView(null, _thumpPath!);
    }else if(_imagePath?.isNotEmpty??false){
      if(isCipherFile(_imagePath)){
        return createEncExtendedImageView(_imagePath!,_replayMsgData?.fileFragment??"");
      }
      return createExtendedImageView(null, _imagePath!);
    }else
    if (getType() == MessageType.file) {
      return imageAsset(_fileUni8!, R.file);
    }
    return imageAsset(_errorUni8!, R.icoPicLoadFailed);
  }
  Future<String> decImg() async {
    String path="";

    return path;
  }
  Widget createExtendedImageView(Uint8List? data, String imagePath) {
    return data==null?ExtendedImage.file(
      File(imagePath),
      fit: BoxFit.cover,
      imageCacheName: imagePath,
      width: 29.w,
      height: 29.w,
      borderRadius: BorderRadius.all(const Radius.circular(30).r),
      maxBytes: Config.maxImageBytes,
      loadStateChanged: (state) {
        if (state.extendedImageLoadState == LoadState.failed&&_errorUni8!=null) {
          return imageAsset(_errorUni8!,R.icoPicLoadFailed);
        }
        return null;
      },
    ):ExtendedImage.memory(
      data,
      fit: BoxFit.cover,
      imageCacheName: imagePath,
      width: 29.w,
      height: 29.w,
      borderRadius: BorderRadius.all(const Radius.circular(30).r),
      maxBytes: Config.maxImageBytes,
      loadStateChanged: (state) {
        if (state.extendedImageLoadState == LoadState.failed&&_errorUni8!=null) {
          return imageAsset(_errorUni8!,R.icoPicLoadFailed);
        }
        return null;
      },
    );
  }
  Widget createEncExtendedImageView(String imagePath,String fileFragment) {
    return FutureBuilder(
      future: ChatioNative.utilFileDecryptToMemory(imagePath, fileFragment),
        builder: (c,s){
        Uint8List? imageDatas;
      if(s.connectionState==ConnectionState.done){
        imageDatas=s.requireData;
      }
      return imageDatas==null?ExtendedImage.asset(
        R.icImageLoading,
        fit: BoxFit.cover,
        imageCacheName: imagePath,
        width: 29.w,
        height: 29.w,
        borderRadius: BorderRadius.all(const Radius.circular(30).r),
        maxBytes: Config.maxImageBytes,
        loadStateChanged: (state) {
          if (state.extendedImageLoadState == LoadState.failed&&_errorUni8!=null) {
            return imageAsset(_errorUni8!,R.icoPicLoadFailed);
          }
          return null;
        },
      ):ExtendedImage.memory(
        imageDatas,
        fit: BoxFit.cover,
        imageCacheName: imagePath,
        width: 29.w,
        height: 29.w,
        borderRadius: BorderRadius.all(const Radius.circular(30).r),
        maxBytes: Config.maxImageBytes,
        loadStateChanged: (state) {
          if (state.extendedImageLoadState == LoadState.failed&&_errorUni8!=null) {
            return imageAsset(_errorUni8!,R.icoPicLoadFailed);
          }
          return null;
        },
      );
    });
  }
  Widget imageAsset(Uint8List name, String key) {
    AppLogger.d('message type key=${key}');
    AppLogger.d('message type name=${name.length}');

    return ExtendedImage.memory(
      name,
      borderRadius: BorderRadius.all(const Radius.circular(3.0).r).r,
      fit: BoxFit.cover,
      width: 29.w,
      height: 29.w,
      imageCacheName: key,
      maxBytes: Config.maxImageBytes,
    );
  }

  String? getReplayMsgData() {
    if (isReplayMsg()) {
      ReplayMessage msg = ReplayMessage(
          body: _replayMsgData?.body,
          type: _replayMsgData?.type.index,
          chatType: _replayMsgData?.chatType.index,
          fileName: _replayMsgData?.fileName,
          userName: _replayMsgData?.from,
          displayName: displayName.value,
          msgId: _replayMsgData?.msgId,
          owner: _replayMsgData?.owner);
      var s = json.encode(msg);
      AppLogger.d('ReplyController msg=${s}');
      setReplayData(null);
      Get.find<AppConfigService>().removeReply(messageController?.getUserName()??"");
      return s;
    }
    return null;
  }

  bool isReplayMsg() {
    return _isReplay.value && (_replayMsgData != null);
  }
}
