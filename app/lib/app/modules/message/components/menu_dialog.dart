/*
 * <AUTHOR> <PERSON><PERSON>
 * @Date         : 2022-04-27 17:33:33
 * @Description  : 聊天窗口菜单
 * @LastEditors  : <PERSON><PERSON>
 * @LastEditTime : 2022-07-15 18:01:01
 * @FilePath     : /flutter_metatel/lib/app/modules/message/components/menu_dialog.dart
 */

import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_metatel/app/data/services/config_service.dart';
import 'package:flutter_metatel/app/data/services/event_service.dart';
import 'package:flutter_metatel/app/modules/message/components/sub_component.dart';
import 'package:flutter_metatel/app/modules/message/message_controller.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:flutter_metatel/core/values/colors.dart';
import 'package:flutter_metatel/r.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:get/get.dart';
import '../../../../core/languages/l.dart';
import '../../../../core/utils/translator_util.dart';
import '../../../../core/values/config.dart';
import '../../../widgets/chat_widget/menu_item.dart' as menu;

class ShowMenuParams {
  final RenderBox? box;
  final double height;
  final double paddingTop;
  final double bottomMargin;

  ShowMenuParams(this.box, this.height, this.paddingTop, this.bottomMargin);

  double topDy(double menuHeight) {
    AppLogger.d("Get.height  box==$box");
    final size = box?.size ?? Size.zero;
    AppLogger.d("Get.height  box?.size==$size");
    final topLeftPosition = box?.localToGlobal(Offset.zero);
    AppLogger.d("Get.height topLeftPosition==$topLeftPosition");
    double dy = 0.0;
    if (topLeftPosition != null) {
      final by = topLeftPosition.dy + size.height + menuHeight;
      final overflow = by - height;
      if (overflow > 0) {
        dy -= overflow;
      }
      dy += topLeftPosition.dy - paddingTop;
    }
    dy -= 10.w;
    if (dy < 0) {
      dy = 0;
    }
    AppLogger.d("Get.height dy==$dy");
    return dy;
  }
}


class MenuDialog extends StatefulWidget {
  final Widget itemWidget;
  final ShowMenuParams params;
  final bool isSender;
  final GestureTapCallback? onReply;
  final GestureTapCallback? onCopy;
  final GestureTapCallback? onDelete;
  final GestureTapCallback? onSelect;
  final GestureTapCallback? onUndo;
  final GestureTapCallback? onForward;
  final GestureTapCallback? onComplaint;
  final GestureTapCallback? onPin;
  final GestureTapCallback? onSave;
  final GestureTapCallback? onTop;
  final GestureTapCallback? onMute;
  final GestureTapCallback? onQrRecognition;
  final GestureTapCallback? onTranslate;

  final MessageEvent messageInfo;
  const MenuDialog({
    Key? key,
    required this.itemWidget,
    required this.params,
    required this.messageInfo,
    this.isSender = false,
    this.onCopy,
    this.onDelete,
    this.onSelect,
    this.onUndo,
    this.onReply,
    this.onForward,
    this.onPin,
    this.onComplaint,
    this.onSave,
    this.onTop,
    this.onMute,
    this.onQrRecognition,
    this.onTranslate,

  }) : super(key: key);

  @override
  State<MenuDialog> createState() => _MenuDialogState();
}

class _MenuDialogState extends State<MenuDialog> {
  // final MenuDialogController controller=Get.put(MenuDialogController());
  var canRepeal=false;
  var canMute=false;
  late MessageEvent messageEvent;
  final ScrollController scrollController =ScrollController();
  final mySelfUsername = Get.find<AppConfigService>().getUserName();
  @override
  void initState() {
    messageEvent=widget.messageInfo;
    bool mySelfSend = messageEvent.direction==Direction.outGoing;
    if(mySelfSend){
      canRepeal=true;
      canMute=false;
    }else{
      String? from = messageEvent.from;
      var msgController = Get.find<MessageController>();
      bool memberIsOwner = from == msgController.channelOwner;
      bool memberIsAdmin = msgController.channelAdminList.contains(from);
      bool mySelfIsOwner = mySelfUsername == msgController.channelOwner;
      bool mySelfIsAdmin = msgController.channelAdminList.contains(mySelfUsername);
      if (mySelfIsOwner) {
        canRepeal = true;
        canMute = true;
      } else if (mySelfIsAdmin) {
        if (!memberIsOwner && !memberIsAdmin) {
          canRepeal = true;
          canMute = true;
        }
      }
    }

    SchedulerBinding.instance.addPostFrameCallback((_) {
      scrollController.jumpTo(scrollController.position.maxScrollExtent);
    });
    super.initState();
  }
  // void _requestChannelInfo() async {
  //   String username = messageEvent.owner;
  //   String from = messageEvent.from??"";
  //   var modelData = await getChannelInfoRequest(username);
  //   AppLogger.d(
  //       'MenuDialogController _requestChannelInfo modelData=${modelData.toJson()}');
  //   if (modelData.code == Code.code200) {
  //     bool memberIsOwner = from == modelData.owner;
  //     bool memberIsAdmin = modelData.admins?.contains(from) ?? false;
  //     bool mySelfIsOwner = mySelfUsername == modelData.owner;
  //     bool mySelfIsAdmin = modelData.admins?.contains(mySelfUsername) ?? false;
  //     if (mySelfIsOwner) {
  //       canRepeal = true;
  //       canMute = true;
  //     } else if (mySelfIsAdmin) {
  //       if (!memberIsOwner && !memberIsAdmin) {
  //         canRepeal = true;
  //         canMute = true;
  //       }
  //     }
  //     setState(() {});
  //   }
  // }
  @override
  void dispose() {
    // Get.delete<MenuDialogController>();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    double menuHeight = 5.w;
    List<Widget> list = [];
    list.add(widget.itemWidget);
    bool isTranslate = (messageEvent.translateMsg?.isNotEmpty ?? false) && !TranslatorUtil.isTranslateLoading(messageEvent.translateMsg);

    if (widget.onReply != null) {
      list.add(menu.MenuItem(
        onTap: widget.onReply,
        iconAsset: R.icoChatMenuDialogReply,
        isSender: widget.isSender,
        topRadius: 20.0,
        title: L.replay.tr,
      ));
      menuHeight += 41.w;
    }
    if (widget.onTop != null) {
      list.add(menu.MenuItem(
        onTap: widget.onTop,
        iconAsset: R.icoChatMenuDialogTop,
        isSender: widget.isSender,
        topRadius: widget.onReply == null ? 20 : 0,
        title: L.chat_item_pin.tr,
      ));
      menuHeight += 41.w;
    }
    if (widget.onCopy != null) {
      list.add(menu.MenuItem(
        onTap: widget.onCopy,
        iconAsset: R.icoChatMenuDialogCopy,
        isSender: widget.isSender,
        title: L.other_copy.tr,
      ));
      menuHeight += 41.w;
    }
    if (widget.onForward != null) {
      list.add(menu.MenuItem(
        onTap: widget.onForward,
        iconAsset: R.icoChatMenuDialogForward,
        isSender: widget.isSender,
        title: L.message_dialog_forward.tr,
      ));
      menuHeight += 41.w;
    }
    if (widget.onTranslate != null) {
      list.add(menu.MenuItem(
        onTap: widget.onTranslate,
        iconAsset: isTranslate ? R.icUnTranslate : R.icTranslate,
        isSender: widget.isSender,
        title: isTranslate ? L.message_dialog_un_translate.tr :L.message_dialog_translate.tr,
      ));
      menuHeight += 41.w;
    }
    if (widget.onPin != null) {
      list.add(menu.MenuItem(
        onTap: widget.onPin,
        iconAsset: R.icoChatMenuDialogTop,
        isSender: widget.isSender,
        title: L.chat_item_pin.tr,
      ));
      menuHeight += 41.w;
    }
    if (widget.onUndo != null) {
      list.add(menu.MenuItem(
        onTap: (){
          if(canRepeal){
            widget.onUndo?.call();
          }
        },
        isSender: widget.isSender,
        iconAsset: canRepeal?R.icoChatMenuDialogRepeal:R.icoChatMenuDialogRepealNo,
        color: canRepeal?Colors.black:AppColors.colorFFF7F7F7,
        title: L.other_repeal.tr,
      ));
      menuHeight += 41.w;
    }
    if (widget.onMute != null) {
      list.add(menu.MenuItem(
        onTap: () {
          if (canMute) {
            widget.onMute?.call();
          }
        },
        isSender: widget.isSender,
        iconAsset: canMute?R.icoChatMenuDialogMute:R.icoChatMenuDialogMuteNo,
        title: L.mute_talk.tr,
        color: canMute?Colors.black:AppColors.colorFFF7F7F7,
      ));
      menuHeight += 41.w;
    }
    if (widget.onSave != null) {
      list.add(menu.MenuItem(
        onTap: widget.onSave,
        iconAsset: R.icoChatMenuDialogSave,
        title: L.save.tr,
        isSender: widget.isSender,
      ));
      menuHeight += 41.w;
    }
    if (widget.onQrRecognition != null) {
      list.add(menu.MenuItem(
        onTap: widget.onQrRecognition,
        iconAsset: R.icoChatMenuDialogQr,
        title: L.recognize_QR_code.tr,
        isSender: widget.isSender,
      ));
      menuHeight += 41.w;
    }
    if (widget.onComplaint != null) {
      list.add(menu.MenuItem(
        onTap: widget.onComplaint,
        icon: Icons.info_outline,
        title: L.complaint.tr,
        isSender: widget.isSender,
      ));
      menuHeight += 41.w;
    }
    if (widget.onDelete != null) {
      list.add(menu.MenuItem(
        onTap: widget.onDelete,
        iconAsset: R.icoChatMenuDialogDel,
        isSender: widget.isSender,
        color: AppColors.colorFFCD3A3A,
        bottomRadius: widget.onSelect==null?20.0:0,
        title: L.chat_contact_del.tr,
      ));
      menuHeight += 41.w;
    }
    if (widget.onSelect != null) {
      list.add(SizedBox(
        height: 3.w,
      ));
      list.add(menu.MenuItem(
        onTap: widget.onSelect,
        iconAsset: R.icoChatMenuDialogMulti,
        isSender: widget.isSender,
        bottomRadius: 20.0,
        title: L.select.tr,
      ));
      menuHeight += 44.w;
    }
    Widget backdropFilter = BackdropFilter(
      filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
      child: Material(
        type: MaterialType.transparency,
        child: InkWell(
            onTap: () => {SmartDialog.dismiss(tag: messageMenuPopTag)},
            child: Column(
              mainAxisSize: MainAxisSize.max,
              children: [
                SizedBox(
                  height: widget.params.topDy(menuHeight),
                ),
                Expanded(
                  child: ListView(
                    controller: scrollController,
                    children: list,
                  ),
                ),
                SizedBox(
                  height: widget.params.bottomMargin,
                ),
              ],
            )),
      ),
    );
    return backdropFilter;
  }
}
