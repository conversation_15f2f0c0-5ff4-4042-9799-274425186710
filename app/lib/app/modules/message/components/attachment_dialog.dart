/*
 * <AUTHOR> <PERSON><PERSON>
 * @Date         : 2022-04-29 11:24:40
 * @Description  : 附件选择对话框
 * @LastEditors  : <PERSON><PERSON>
 * @LastEditTime : 2022-07-20 11:51:53
 * @FilePath     : /flutter_metatel/lib/app/modules/message/components/attachment_dialog.dart
 */

import 'dart:async';
import 'dart:io';
import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:file_picker/file_picker.dart';
import 'package:get/get.dart';
import 'package:photo_manager/photo_manager.dart';

import '../../../../core/languages/l.dart';
import '../../../../core/utils/util.dart';
import '../../../../core/values/config.dart';
import '../../../data/enums/enum.dart';
import '../../../data/models/select_contact_model.dart';
import '../../../data/providers/db/database.dart';
import '../../../data/services/config_service.dart';
import '../../../widgets/chat_widget/photos_widget.dart';
import '../../../widgets/select_contact_page.dart';
import '../../resentmessage/resent_message_page.dart';

export '../../../data/enums/enum.dart';

class AttachmentDialog extends StatefulWidget {
  const AttachmentDialog({
    Key? key,
    this.onAttachmentSelect,
    this.selfDestruct = false,
  }) : super(key: key);

  final OnAttachmentSelect? onAttachmentSelect;
  final bool selfDestruct;

  @override
  _AttachDialogState createState() => _AttachDialogState();
}

typedef OnAttachmentSelect = void Function(List<AttachmentEntity> attachments);

class AttachmentEntity {
  final String? filePath;
  final SelectContactInfo? contactInfo;
  final MessageType type;
  final Uint8List? originBytes;
  AttachmentEntity(this.type, {this.filePath, this.contactInfo,this.originBytes});
}

class _AttachDialogState extends State<AttachmentDialog> {
  Future<void> _fileSelect() async {
    var result = await FilePicker.platform.pickFiles(allowMultiple: true);
    if (result == null) {
      return;
    }
    List<String?> filePaths = result.paths.map((path) => path).toList();
    List<AttachmentEntity> attachments = [];
    bool isToast = false;
    for (String? filePath in filePaths) {
      var length = File(filePath ?? '').lengthSync();
      if (length > Config.selectFileMaxBytes) {
        isToast = true;
        continue;
      }

      attachments.add(AttachmentEntity(MessageType.file, filePath: filePath));
    }

    widget.onAttachmentSelect?.call(attachments); // 文件选择完成
    Navigator.pop(context);

    if (isToast) {
      toast(L.file_select_max_length.trParams(
          {'len': byteFormat(Config.selectFileMaxBytes, fractionDigits: 0)}));
    }
  }

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: IndexedStack(
          index: 0,
          children: [
            PhotosWidget(
                type: widget.selfDestruct
                    ? RequestType.image
                    : RequestType.common,
                onAttachmentSelect: (List<AttachmentEntity> attachments) {
                  widget.onAttachmentSelect?.call(attachments); // 相册选择完成
                  Navigator.pop(context);
                }),
          ],
        ),
      ),
      bottomNavigationBar: widget.selfDestruct
          ? null
          : BottomNavigationBar(
              items: [
                BottomNavigationBarItem(
                  icon: const Icon(Icons.photo),
                  label: L.image.tr,
                ),
                BottomNavigationBarItem(
                  icon: const Icon(Icons.file_upload),
                  label: L.file.tr,
                ),
                BottomNavigationBarItem(
                  icon: const Icon(Icons.contact_mail),
                  label: L.contact.tr,
                ),
              ],
              currentIndex: 0,
              onTap: (index) async {
                if (index == 1) {
                  _fileSelect();
                } else if (index == 2) {
                  AppDatabase db = Get.find();
                  String userName = Get.find<AppConfigService>().currSessionID;
                  db
                      .allContactWithoutUserName(userName)
                      .get()
                      .then((value) async {
                    var data = await showDialog(
                        context: context,
                        builder: (_) {
                          return SelectContactPage(
                            type: SelectContactType.forwarding,
                            contactDatas: value,
                          );
                        });
                    // var data = await Get.to();
                    if (data != null) {
                      if (data.runtimeType == SelectContactInfo) {
                        var contactInfo = data as SelectContactInfo;
                        widget.onAttachmentSelect?.call([
                          AttachmentEntity(MessageType.contactCard,
                              contactInfo: contactInfo)
                        ]);
                      }
                      Navigator.pop(context);
                    }
                  });
                }
                // setState(() {
                //   // _currentIndex = index;
                // });
              },
            ),
    );
  }

  @override
  void dispose() {
    super.dispose();
  }
}
