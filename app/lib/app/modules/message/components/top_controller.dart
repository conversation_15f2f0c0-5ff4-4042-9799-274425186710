import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:extended_image/extended_image.dart';
import 'package:extended_text/extended_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/data/enums/enum.dart';
import 'package:flutter_metatel/app/data/models/channel_info_model_data.dart';
import 'package:flutter_metatel/app/data/models/meeting.dart';
import 'package:flutter_metatel/app/data/providers/native/chatio/chatio_async.dart';
import 'package:flutter_metatel/app/data/services/chatio_service.dart';
import 'package:flutter_metatel/app/data/services/config_service.dart';
import 'package:flutter_metatel/app/modules/message/message_controller.dart';
import 'package:flutter_metatel/app/widgets/text_middle_overlow.dart';
import 'package:flutter_metatel/core/task/channel_option_task.dart';
import 'package:flutter_metatel/core/task/time_task.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:flutter_metatel/core/utils/events_bus.dart';
import 'package:flutter_metatel/core/utils/util.dart';
import 'package:flutter_metatel/r.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:flutter_web3/app/data/models/wallet_model.dart';
import 'package:get/get.dart';

import '../../../../core/languages/l.dart';
import '../../../../core/values/colors.dart';
import '../../../../core/values/config.dart';
import '../../../data/events/events.dart';
import '../../../data/providers/db/database.dart';
import '../../../data/services/event_service.dart';
import '../../../widgets/at_widget/my_special_text_span_builder.dart';
import 'package:flutter/services.dart';

extension MessageEventListContains on List<MessageEvent> {
  bool msgIdContains(String msgId, List<MessageEvent> source) {
    for (MessageEvent e in source) {
      if (e.msgId == msgId) {
        return true;
      }
    }
    return false;
  }
}

class TopController {
  TopController(this.isAdmin) {
    _fileUni8 = R.file;
    _errorUni8 = R.icoPicLoadFailed;
    _subscription =
        Get.find<EventBus>().on<CancelMsgTopEvent>().listen((event) {
      if (_msgData?.owner.isNotEmpty == true &&
          _msgData?.msgId.isNotEmpty == true &&
          _msgData?.owner == event.username&&!isAdmin) {
        _updateTopWidget(msgId: event.msgid, change: false);
      }
    });
  }

  final _rxIndex = 0.obs;
  final _rxShow = false.obs;
  final _rxDisplayName = ''.obs;
  StreamSubscription? _subscription;
  MessageEvent? _msgData;
  final RxList<MessageEvent> _msgDataList = RxList();
  final Map<String,Uint8List?> _imageUnitListMap={};
  final Map<String,String?> _imagePathMap={};
  final Map<String,String?> _thumpPathMap={};
  late String _fileUni8;
  late String _errorUni8;
  final topListDialogTag="top_msg_list";
  late bool isAdmin;
  void dispose() {
    _subscription?.cancel();
  }

  void setMsgData(MessageEvent? data) {
    _rxIndex.value++;
    if (data == null) {
      _msgData = null;
      _rxShow.value = false;
      _msgDataList.clear();
    } else {
      if(_msgDataList.msgIdContains(data.msgId, _msgDataList)){
        _msgDataList.removeWhere((e) {
          return data.msgId == e.msgId;
        });
      }
      _msgDataList.add(data);
      _msgData = data;
      if(_isVideo(data)){
        _thumpPathMap[data.msgId]=data.thumbnailPath;
        _imageUnitListMap[data.msgId] = _getImageDatas(data.thumbnailPath);
      }else if(_isImage(data)){
        _thumpPathMap[data.msgId]=data.thumbnailPath;
        _imagePathMap[data.msgId]=data.filePath;
        _imageUnitListMap[data.msgId] = _getImageDatas(data.thumbnailPath)??_getImageDatas(data.filePath);
      }
      _rxShow.value = true;
      _updateDisplayName(_msgData);
    }
  }
  Future<String?> _updateDisplayName(MessageEvent? messageEvent, {bool updateMain=true}) async {
    String? displayName;
    var userName = messageEvent?.from ?? "";
    var service = Get.find<AppConfigService>();
    if (service.getUserName() == userName ||
        (userName.isEmpty && messageEvent?.direction == Direction.outGoing)) {
      displayName = service.getMySelfDisplayName();
    } else if (userName.isNotEmpty) {
      var value =
      await Get.find<AppDatabase>().oneContact(userName).getSingleOrNull();
      if (value != null) {
        displayName = value.localname ?? value.displayname ?? value.username;
      }else{
        displayName=userName.length>7?userName.substring(0,7):userName;
      }
    }
    if(displayName!=null&&updateMain){
      _rxDisplayName.value=displayName;
    }
    return displayName;
  }
  updateContactInfo(String userName) async {
    List<MessageEvent> msgs=[];
    msgs.addAll(_msgDataList.where((p0){return p0.from==userName;}));
    if(userName==_msgData?.from){
      _updateDisplayName(_msgData);
    }
    for(MessageEvent msg in msgs){
      Get.find<MessageController>().update(["top_${msg.msgId}"]);
    }
  }

  _updateTopWidget({bool change = true, String? msgId}) {
    if (_msgData != null) {
      msgId ??= _msgData?.msgId;
      _msgDataList.removeWhere((e) {
        return e.msgId == msgId;
      });
      if (change) {
        _msgDataList.insert(0, _msgData!);
      }
      setMsgData(_msgDataList.isEmpty ? null : _msgDataList.last);
    }
  }

  Widget createTopWidget(BuildContext? c,MessageController controller) {
    return Obx(() {
      return Visibility(
        visible: _rxShow.value,
        child: Container(
          color: AppColors.colorFFF7F7F7,
          padding:
          const EdgeInsets.only(top: 0, bottom: 15, left: 14, right: 14).r,
          child: Stack(
            children: [
              ///底部占位
              Visibility(
                visible: _msgDataList.length > 1,
                child: Container(
                  height: 44.r,
                  width: double.infinity,
                  decoration: BoxDecoration(
                    borderRadius: const BorderRadius.all(Radius.circular(14)).r,
                    color: AppColors.white,
                  ),
                  margin: const EdgeInsets.only(top: 10, left: 14, right: 14).r,
                  padding: const EdgeInsets.only(
                      left: 16, top: 10, right: 16, bottom: 10)
                      .r,
                  child: const SizedBox.shrink(),
                ),
              ),
              ///真正显示 单条
              Builder(
                builder: (c) => Container(
                  decoration: BoxDecoration(
                      borderRadius:
                      const BorderRadius.all(Radius.circular(14)).r,
                      boxShadow: [
                        BoxShadow(
                          offset: Offset(0, 2.r),
                          //x,y轴
                          color: AppColors.colorFFF2F2F2,
                          //投影颜色
                          blurRadius: 2.r,
                          //投影距离
                          spreadRadius: 0.r,
                          blurStyle: BlurStyle.normal,
                        )
                      ]),
                  child: _buildTopItem(_msgData, false,controller,
                      canLongPress: false,
                      isMore: _msgDataList.length > 1,
                      attachTargetContext: c),
                ),
              ),
            ],
          ),
        ),
      );
    });
  }

  _createTopList(MessageController controller) {

    return Column(
      children: [
        Container(
          constraints: BoxConstraints(maxHeight: 500.h),
          padding: const EdgeInsets.only(left: 16, right: 16).r,
          width: double.infinity,
          decoration: const BoxDecoration(
            color: AppColors.colorFFF7F7F7,
          ),
          child: Obx(
                () {
              List<MessageEvent> msgDataET=[];
              var reversed = _msgDataList.reversed;
              msgDataET.addAll(reversed);
              return ListView.builder(
                padding: EdgeInsets.zero,
                shrinkWrap: true,
                itemBuilder: (context, index) {
                  if (msgDataET.length==1) {
                    SmartDialog.dismiss(
                      tag: topListDialogTag,
                    );
                  }
                  return Container(
                    margin: EdgeInsets.only(bottom: index ==msgDataET.length-1?0:10).r,
                    child: _buildTopItem(msgDataET[index],true,controller),
                  );
                },
                itemCount: msgDataET.length,
              );
            },
          ),
        ),
        Container(
          alignment: Alignment.center,
          decoration: const BoxDecoration(
            color: AppColors.colorFFF7F7F7,
          ),
          child: GestureDetector(
            onTap: () =>  SmartDialog.dismiss(
              tag: topListDialogTag,
            ),
            child: Container(
              color: AppColors.transparent,
              padding: const EdgeInsets.all(10).r,
              child: Image.asset(
                R.icoListPackUp,
                width: 19.w,
                height: 19.w,
              ),
            ),
          ),
        ),
      ],
    );
  }
  ///真正显示 多条的item
  Widget _buildTopItem(MessageEvent? m,bool isListItem,MessageController controller,{bool isMore=false,bool canLongPress=true,BuildContext? attachTargetContext}) {
    return  Builder(
      builder: (c) {
        return Material(
          color: AppColors.white,
          borderRadius: const BorderRadius.all(Radius.circular(8)).r,
          child: InkWell(
            borderRadius: const BorderRadius.all(Radius.circular(8)).r,
            onTap: () {
              Get.find<MessageController>().replayOnClick(m?.msgId);
              if(isMore){
                _updateTopWidget();
              }else{
                SmartDialog.dismiss(
                  tag: topListDialogTag,
                );
              }
            },
            child: GestureDetector(
              behavior: HitTestBehavior.translucent,
              onLongPressStart: !canLongPress?null:(detail) {
                if(m!=null){
                  SmartDialog.showAttach(
                      tag: "top_${m.msgId}",
                      targetBuilder: (_, __) {
                        var globalPosition = detail.globalPosition;
                        double dx = globalPosition.dx;
                        double dy =
                        globalPosition.dy > 500.h ? 500.h : globalPosition.dy;

                        return Offset(dx, dy);
                      },
                      builder: (c) {
                        return _showMenuDialog(controller, m);
                      },
                      targetContext: null);
                }
              },
              child: Container(
                height: 44.r,
                width: double.infinity,
                decoration: BoxDecoration(
                  borderRadius: const BorderRadius.all(Radius.circular(8)).r,
                ),
                padding: const EdgeInsets.symmetric(horizontal: 11,).r,
                child: Row(
                  children: [
                    Image.asset(
                      isAdmin?R.topMsgAdmin:R.topMsg,
                      width: 20.r,
                      height: 20.r,
                    ),
                    SizedBox(width: 11.r,),
                    Expanded(
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          GetBuilder(
                              id:m?.msgId,
                              builder: (MessageController c){
                                return FutureBuilder(
                                  future: _updateDisplayName(m,updateMain: false),
                                  builder: (c, snapshot) {
                                    if (snapshot.connectionState ==
                                        ConnectionState.done) {
                                      if (snapshot.data != null) {
                                        m?.sendDisplayName = snapshot.data as String;
                                      }
                                    }
                                    return MiddleText(
                                      "${m?.sendDisplayName ?? ''}:",
                                      WXTextOverflow.ellipsisMiddle,
                                      style: TextStyle(
                                        fontSize: 13.sp,
                                        color: AppColors.colorFF333333,
                                      ),
                                    );
                                  },
                                );
                              }),
                          Visibility(
                            visible: _isNotText(m),
                            child: Row(
                              children: [
                                SizedBox(
                                  width: 5.w,
                                ),
                                Container(
                                  clipBehavior: Clip.hardEdge,
                                  decoration: BoxDecoration(
                                      borderRadius: BorderRadius.all(
                                        const Radius.circular(3).r,
                                      )),
                                  child: Stack(
                                    alignment: AlignmentDirectional.center,
                                    children: [
                                      _createImageView(m),
                                      Visibility(
                                          visible: _isVideo(m),
                                          child: Container(
                                            width: 15.r,
                                            height: 15.r,
                                            decoration: BoxDecoration(
                                              color: Colors.black.withOpacity(0.5),
                                              borderRadius: BorderRadius.circular(20).r,
                                            ),
                                            child: Icon(
                                              Icons.play_arrow,
                                              color: Colors.white,
                                              size: 10.r,
                                            ),
                                          )),
                                    ],
                                  ),
                                )
                              ],
                            ),
                          ),
                          SizedBox(
                            width: 5.r,
                          ),
                          Expanded(
                            child: ExtendedText(
                              _getTextData(m),
                              specialTextSpanBuilder: MySpecialTextSpanBuilder(
                                  showAtBackground: false,
                                  showLinkColor: true,
                                  linkColor: AppColors.appDefault,
                                  atTextColor: AppColors.appDefault),
                              overflow: TextOverflow.ellipsis,
                              maxLines: 1,
                              style: TextStyle(
                                fontSize: 13.sp,
                                color: AppColors.colorFF333333,
                              ),
                            ),
                          ),

                        ],
                      ),
                    ),
                    GestureDetector(
                      onTap: () async {
                        if (!isMore) {
                          if (m != null) {
                            _cancelAdminTopMsg(m,isListItem);
                          }
                        } else {
                          SmartDialog.showAttach(
                            tag: topListDialogTag,
                            targetBuilder: (offset,size){
                              return Offset(offset.dx, offset.dy-44.r);
                            },
                            targetContext: c,
                            builder: (ctx) {
                              return _createTopList(controller);
                            },
                            maskColor: AppColors.transparent,
                          );
                        }
                      },
                      child: Container(
                        color: AppColors.transparent,
                        padding: const EdgeInsets.symmetric(
                            horizontal: 10, vertical: 10)
                            .r,
                        child: !isMore
                            ? isAdmin
                            ? Obx(() {
                          return controller
                              .rxMySelfIsChannelAdminOrOwner
                              .value
                              ? Image.asset(R.topMsgClose,
                              width: 19.w, height: 19.w)
                              : const SizedBox.shrink();
                        })
                            : Image.asset(R.topMsgClose,
                            width: 19.w, height: 19.w)
                            : RotatedBox(
                          quarterTurns: 2,
                          child: Image.asset(
                            R.icoListPackUp,
                            width: 19.r,
                            height: 19.r,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );

  }
  Widget _showMenuDialog(MessageController messageController,MessageEvent m){
    bool canForward=true;
    bool canCancelPin=true;
    var path = m.filePath??'';
    if ((m.type == MessageType.image ||
        m.type == MessageType.file ||
        m.type == MessageType.video) &&
        (path.isEmpty || !File(path).existsSync())) {
      canForward=false;
    }
    canCancelPin=messageController.rxMySelfIsChannelAdminOrOwner.value||!messageController.isChannelChat();
    return Container(
      padding: const EdgeInsets.only(left: 14, right: 14).r,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius:
        const BorderRadius.all(Radius.circular(10)).r,
      ),
      child: Column(
        children: [
          TextButton(
            onPressed: () {
              messageController.onCopyMessage(m);
              SmartDialog.dismiss(tag: "top_${m.msgId}");
            },
            child: Text(L.other_copy.tr),
          ),
          if(canCancelPin)
            TextButton(
              onPressed: () async {
                var bool = await _cancelAdminTopMsg(m,false);
                if(bool){
                  SmartDialog.dismiss(tag: "top_${m.msgId}");
                }
              },
              child: Text(L.other_cancel_stick.tr),
            ),
          if(canForward)
            TextButton(
              onPressed: () {
                SmartDialog.dismiss(tag: "top_${m.msgId}");
                SmartDialog.dismiss(tag: topListDialogTag);
                messageController.onForwardMessage([m]);
              },
              child: Text(L.message_dialog_forward.tr),
            ),
          if(!isAdmin)
            TextButton(
              onPressed: () async {
                var bool = await _cancelAdminTopMsg(m,false);
                if(bool){
                  messageController.onDeleteMessage([m]);
                  SmartDialog.dismiss(tag: "top_${m.msgId}");
                }
              },
              child: Text(L.chat_contact_del.tr),
            ),
        ],
      ),
    );
  }
  void setAnnouncement(String? announcement) {
    _announcemnet.value = announcement ?? '';
    _rxShowBanner.value = announcement?.isNotEmpty ?? false;
    _rxIndex.value++;
  }

  final _announcemnet = ''.obs;
  final _rxShowBanner = false.obs;

  Widget createTopBannerWidget() {
    return Obx(() {
      return Visibility(
        visible: _rxShowBanner.value,
        child:Container(
          color: AppColors.colorFFF7F7F7,
          padding:
          const EdgeInsets.only(top: 0, bottom: 10, left: 14, right: 14).r,
          child: Stack(
            children: [
              Container(
                height: 44.r,
                decoration: BoxDecoration(
                    borderRadius: const BorderRadius.all(Radius.circular(8)).r,
                    boxShadow: [
                      BoxShadow(
                        offset: Offset(0, 2.r),
                        //x,y轴
                        color: AppColors.colorFFF2F2F2,
                        //投影颜色
                        blurRadius: 2.r,
                        //投影距离
                        spreadRadius: 0.r,
                        blurStyle: BlurStyle.normal,
                      )
                    ]),
                child: Material(
                  color: AppColors.white,
                  borderRadius: const BorderRadius.all(Radius.circular(8)).r,
                  child: InkWell(
                    borderRadius: const BorderRadius.all(Radius.circular(8)).r,
                    onTap: () {
                      setAnnouncement(null);
                      Get.find<MessageController>().toBannerDetailView();
                    },
                    child: Container(
                      decoration: BoxDecoration(
                        color: AppColors.transparent,
                        borderRadius:
                        const BorderRadius.all(Radius.circular(8)).r,
                      ),
                      padding: const EdgeInsets.only(
                          left: 16, top: 10, right: 16, bottom: 10).r,
                      child: Obx(() {
                        _rxIndex.value; // 不能删

                        return Row(
                          children: [
                            Image.asset(
                              R.iconAnnouncementTop,
                              width: 18.5.w,
                              height: 18.5.w,
                            ),
                            SizedBox(
                              width: 8.r,
                            ),
                            Expanded(
                              child: Obx(
                                    () => ExtendedText(
                                  _announcemnet.value,
                                  specialTextSpanBuilder: MySpecialTextSpanBuilder(
                                    showAtBackground: false,
                                  ),
                                  overflow: TextOverflow.ellipsis,
                                  maxLines: 1,
                                  style: TextStyle(
                                    fontSize: 13.sp,
                                    color: AppColors.colorFF2c2c2c,
                                  ),
                                ),
                              ),
                            ),
                            SizedBox(
                              width: 20.r,
                            ),

                            RotatedBox(
                              quarterTurns: 1,
                              child: Image.asset(
                                R.icoListPackUp,
                                width: 15.r,
                                height: 15.r,
                                fit: BoxFit.cover,
                              ),
                            ),
                          ],
                        );
                      }),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    });
  }
  updateTopMsg(String msgId) async {
    var message = await Get.find<AppDatabase>().oneMessage(msgId).getSingleOrNull();
    if(message==null){
      return;
    }
    Get.find<AppDatabase>().insertOrUpdateTopMsg([
      MessageTopCompanion.insert(
        msgId: message.msgId,
        owner: ofNullable(message.owner),
        from: ofNullable(message.from),
        body: ofNullable(message.body),
        filePath: ofNullable(message.filePath),
        fileUrl: ofNullable(message.fileUrl),
        fileFragment: ofNullable(message.fileFragment),
        thumbnailPath: ofNullable(message.thumbnailPath),
        thumbnailUrl: ofNullable(message.thumbnailUrl),
        thumbnailFragment: ofNullable(message.thumbnailFragment),
        selfDestruct: ofNullable(message.selfDestruct),
        undo: ofNullable(message.undo),
        undoEdit: ofNullable(message.undoEdit),
        time: ofNullable(message.time),
        type: ofNullable(message.type),
        chatType: ofNullable(message.chatType),
        callState: ofNullable(message.callState),
        state: ofNullable(message.state),
        direction: ofNullable(message.direction),
        fileState: ofNullable(message.fileState),
        thumbnailFileState: ofNullable(message.thumbnailFileState),
        uuid: ofNullable(message.uuid),
        fileName: ofNullable(message.fileName),
        fileSize: ofNullable(message.fileSize),
        ext1: ofNullable(message.ext1),
        ackNumber: ofNullable(message.ackNumber),
        read: ofNullable(message.read),
        replayMsg: ofNullable(message.replayMsg),
        margeMsg: ofNullable(message.margeMsg),
        contactMsg: ofNullable(message.contactMsg),
        shareMsg: ofNullable(message.shareMsg),
        createTime: ofNullable(message.createTime),
        at: ofNullable(message.at),
        updateTime:
        ofNullable(TimeTask.instance.getNowTime().toDouble()),
        resourceUuid: ofNullable(message.resourceUuid),
        hasShown: ofNullable(message.hasShown),
        messageHasRead: ofNullable(message.messageHasRead),
        fileDuration: ofNullable(message.fileDuration),
        userNameFileHelper: ofNullable(message.userNameFileHelper),
        noises: ofNullable(message.noises),
        expand: ofNullable(message.expand),
      )
    ]);
  }
  updateTopMsgAdmin(String msgId,bool cancel) async {
    var database = Get.find<AppDatabase>();
    if(cancel){
      database.deleteChannelTopMessageAdminByMsgId(msgId);
    }else{
      var message = await database.oneMessage(msgId).getSingleOrNull();
      if(message==null){
        return;
      }
      Get.find<AppDatabase>().insertOrUpdateTopMsgAdmin([
        MessageTopAdminCompanion.insert(
          msgId: message.msgId,
          owner: ofNullable(message.owner),
          from: ofNullable(message.from),
          body: ofNullable(message.body),
          filePath: ofNullable(message.filePath),
          fileUrl: ofNullable(message.fileUrl),
          fileFragment: ofNullable(message.fileFragment),
          thumbnailPath: ofNullable(message.thumbnailPath),
          thumbnailUrl: ofNullable(message.thumbnailUrl),
          thumbnailFragment: ofNullable(message.thumbnailFragment),
          selfDestruct: ofNullable(message.selfDestruct),
          undo: ofNullable(message.undo),
          undoEdit: ofNullable(message.undoEdit),
          time: ofNullable(message.time),
          type: ofNullable(message.type),
          chatType: ofNullable(message.chatType),
          callState: ofNullable(message.callState),
          state: ofNullable(message.state),
          direction: ofNullable(message.direction),
          fileState: ofNullable(message.fileState),
          thumbnailFileState: ofNullable(message.thumbnailFileState),
          uuid: ofNullable(message.uuid),
          fileName: ofNullable(message.fileName),
          fileSize: ofNullable(message.fileSize),
          ext1: ofNullable(message.ext1),
          ackNumber: ofNullable(message.ackNumber),
          read: ofNullable(message.read),
          replayMsg: ofNullable(message.replayMsg),
          margeMsg: ofNullable(message.margeMsg),
          contactMsg: ofNullable(message.contactMsg),
          shareMsg: ofNullable(message.shareMsg),
          createTime: ofNullable(message.createTime),
          at: ofNullable(message.at),
          updateTime:
          ofNullable(TimeTask.instance.getNowTime().toDouble()),
          resourceUuid: ofNullable(message.resourceUuid),
          hasShown: ofNullable(message.hasShown),
          messageHasRead: ofNullable(message.messageHasRead),
          fileDuration: ofNullable(message.fileDuration),
          userNameFileHelper: ofNullable(message.userNameFileHelper),
          noises: ofNullable(message.noises),
          expand: ofNullable(message.expand),
        )
      ]);
    }
  }
  MessageData _topMsgToMsg(MessageTopData topMsg){
    return MessageData(
      id: topMsg.id,
      msgId: topMsg.msgId,
      owner:topMsg.owner,
      from:topMsg.from,
      body:topMsg.body,
      filePath:topMsg.filePath,
      fileUrl:topMsg.fileUrl,
      fileFragment:topMsg.fileFragment,
      thumbnailPath:topMsg.thumbnailPath,
      thumbnailUrl:topMsg.thumbnailUrl,
      thumbnailFragment:topMsg.thumbnailFragment,
      selfDestruct:topMsg.selfDestruct,
      undo:topMsg.undo,
      undoEdit:topMsg.undoEdit,
      time:topMsg.time,
      type:topMsg.type,
      chatType:topMsg.chatType,
      callState:topMsg.callState,
      state:topMsg.state,
      direction:topMsg.direction,
      fileState:topMsg.fileState,
      thumbnailFileState:topMsg.thumbnailFileState,
      uuid:topMsg.uuid,
      fileName:topMsg.fileName,
      fileSize:topMsg.fileSize,
      ext1:topMsg.ext1,
      ackNumber:topMsg.ackNumber,
      read:topMsg.read,
      replayMsg:topMsg.replayMsg,
      margeMsg:topMsg.margeMsg,
      contactMsg:topMsg.contactMsg,
      shareMsg:topMsg.shareMsg,
      createTime:topMsg.createTime,
      at:topMsg.at,
      updateTime:topMsg.updateTime,
      resourceUuid:topMsg.resourceUuid,
      hasShown:topMsg.hasShown,
      messageHasRead:topMsg.messageHasRead,
      fileDuration:topMsg.fileDuration,
      userNameFileHelper:topMsg.userNameFileHelper,
      noises:topMsg.noises,
      expand:topMsg.expand,
    );
  }
  MessageData _topMsgAdminToMsg(MessageTopAdminData topMsg){
    return MessageData(
      id: topMsg.id,
      msgId: topMsg.msgId,
      owner:topMsg.owner,
      from:topMsg.from,
      body:topMsg.body,
      filePath:topMsg.filePath,
      fileUrl:topMsg.fileUrl,
      fileFragment:topMsg.fileFragment,
      thumbnailPath:topMsg.thumbnailPath,
      thumbnailUrl:topMsg.thumbnailUrl,
      thumbnailFragment:topMsg.thumbnailFragment,
      selfDestruct:topMsg.selfDestruct,
      undo:topMsg.undo,
      undoEdit:topMsg.undoEdit,
      time:topMsg.time,
      type:topMsg.type,
      chatType:topMsg.chatType,
      callState:topMsg.callState,
      state:topMsg.state,
      direction:topMsg.direction,
      fileState:topMsg.fileState,
      thumbnailFileState:topMsg.thumbnailFileState,
      uuid:topMsg.uuid,
      fileName:topMsg.fileName,
      fileSize:topMsg.fileSize,
      ext1:topMsg.ext1,
      ackNumber:topMsg.ackNumber,
      read:topMsg.read,
      replayMsg:topMsg.replayMsg,
      margeMsg:topMsg.margeMsg,
      contactMsg:topMsg.contactMsg,
      shareMsg:topMsg.shareMsg,
      createTime:topMsg.createTime,
      at:topMsg.at,
      updateTime:topMsg.updateTime,
      resourceUuid:topMsg.resourceUuid,
      hasShown:topMsg.hasShown,
      messageHasRead:topMsg.messageHasRead,
      fileDuration:topMsg.fileDuration,
      userNameFileHelper:topMsg.userNameFileHelper,
      noises:topMsg.noises,
      expand:topMsg.expand,
    );
  }

  ///置顶消息
  getTopMsg(List<MessageData> events,String userName) async {
    var database = Get.find<AppDatabase>();
    var msgTopDataList = await database.topMsgByUserName(userName).get();
    if (msgTopDataList.isNotEmpty) {
      int minTime=0;
      List<MessageData> e=[];
      for (var d in msgTopDataList) {
        var eTop = events.where((element) => d.msgId==element.msgId);
        if((d.time??0)<minTime||minTime==0){
          minTime=d.time??0;
        }
        var msg = _topMsgToMsg(d);
        MessageEvent? m=data2event(msg);
        if(m!=null){
          setMsgData(m);
          if(eTop.isEmpty){
            e.add(msg);
          }
        }
      }
      Get.find<MessageController>().updateBlackAndAddFriendDescribeMsgEvent(DateTime.fromMillisecondsSinceEpoch(minTime));
      Get.find<MessageController>().topInsertMessage(e);
    } else if(!isAdmin){ ///处理旧置顶数据保存到message_top表里
      var conf = Get.find<AppConfigService>();
      var msgTopIDs = conf.getMsgTopID(userName);
      if(msgTopIDs!=null){
        List<MessageTopCompanion> topMsgCom=[];
        for(String msgId in msgTopIDs){
          var message = events.firstWhereOrNull((element)=>element.msgId==msgId);
          if(message==null){
            return;
          }
          var messageTopCompanion = MessageTopCompanion.insert(
            msgId: message.msgId,
            owner: ofNullable(message.owner),
            from: ofNullable(message.from),
            body: ofNullable(message.body),
            filePath: ofNullable(message.filePath),
            fileUrl: ofNullable(message.fileUrl),
            fileFragment: ofNullable(message.fileFragment),
            thumbnailPath: ofNullable(message.thumbnailPath),
            thumbnailUrl: ofNullable(message.thumbnailUrl),
            thumbnailFragment: ofNullable(message.thumbnailFragment),
            selfDestruct: ofNullable(message.selfDestruct),
            undo: ofNullable(message.undo),
            undoEdit: ofNullable(message.undoEdit),
            time: ofNullable(message.time),
            type: ofNullable(message.type),
            chatType: ofNullable(message.chatType),
            callState: ofNullable(message.callState),
            state: ofNullable(message.state),
            direction: ofNullable(message.direction),
            fileState: ofNullable(message.fileState),
            thumbnailFileState: ofNullable(message.thumbnailFileState),
            uuid: ofNullable(message.uuid),
            fileName: ofNullable(message.fileName),
            fileSize: ofNullable(message.fileSize),
            ext1: ofNullable(message.ext1),
            ackNumber: ofNullable(message.ackNumber),
            read: ofNullable(message.read),
            replayMsg: ofNullable(message.replayMsg),
            margeMsg: ofNullable(message.margeMsg),
            contactMsg: ofNullable(message.contactMsg),
            shareMsg: ofNullable(message.shareMsg),
            createTime: ofNullable(message.createTime),
            at: ofNullable(message.at),
            updateTime:
            ofNullable(TimeTask.instance.getNowTime().toDouble()),
            resourceUuid: ofNullable(message.resourceUuid),
            hasShown: ofNullable(message.hasShown),
            messageHasRead: ofNullable(message.messageHasRead),
            fileDuration: ofNullable(message.fileDuration),
            userNameFileHelper: ofNullable(message.userNameFileHelper),
            noises: ofNullable(message.noises),
            expand: ofNullable(message.expand),
          );
          topMsgCom.add(messageTopCompanion);
          var event = data2event(message);
          if(event!=null){
            setMsgData(event);
          }
        }
        Get.find<AppDatabase>().insertOrUpdateTopMsg(topMsgCom);
      }
    }
  }
  ///置顶消息admin
  getTopMsgAdmin(List<MessageData> events,String userName,MessageController controller) {
    AppLogger.d('topMsgAdmin start');
    var database = Get.find<AppDatabase>();
    return database.topMsgByUserNameAdmin(userName).watch().listen((msgTopDataList) {
      AppLogger.d('topMsgAdmin .watch().listen');
      if (msgTopDataList.isNotEmpty) {
        int minTime=0;
        List<MessageData> e=[];
        List<MessageEvent> needDelTopList=[..._msgDataList];
        String userNameT='';
        for (var d in msgTopDataList) {
          userNameT=d.owner??"";
          needDelTopList.removeWhere((element) => element.msgId==d.msgId);
          var eTop = events.where((element) => d.msgId==element.msgId);
          if((d.time??0)<minTime||minTime==0){
            minTime=d.time??0;
          }
          var msg = _topMsgAdminToMsg(d);
          MessageEvent? m=data2event(msg);
          if(m!=null){
            setMsgData(m);
            if(eTop.isEmpty){
              e.add(msg);
            }
          }
        }
        for(MessageEvent e in needDelTopList){
          _msgDataList.removeWhere((element) => element.msgId==e.msgId);
        }
        if(userName==userNameT){
          AppLogger.d('topMsgAdmin topInsertMessage userNameT==$userNameT');
          controller.updateBlackAndAddFriendDescribeMsgEvent(DateTime.fromMillisecondsSinceEpoch(minTime));
          controller.topInsertMessage(e);
        }
      }else{
        setMsgData(null);
      }
    });
  }
  Uint8List? _getImageDatas(String? imagePath) {
    Uint8List? data;
    if (imagePath != null) {
      try {
        data =
            Get.find<MessageController>().imagesWeakMap.firstWhere((element) => element.path == imagePath).data;
      } catch (e) {
        e.printError();
      }
      return data;
    }
    return null;
  }

  String _getTextData(MessageEvent? msgEvent) {
    String data = msgEvent?.body ?? '';
    switch (msgEvent?.type) {
      case MessageType.image:
      case MessageType.video:
        data = "";
        break;
      case MessageType.file:
        data =  msgEvent?.fileName??L.other_file_message.tr;
        break;
      case MessageType.walletAddress:
       var bean = WalletModel.fromJson(jsonDecode(data ?? "{}"));
       data = bean.address?? data;
        break;
      case MessageType.meeting:
        var bean = Meeting.fromJson(jsonDecode(data ?? "{}"));
        data = bean.title ??  L.meeting.tr;
        break;
      // case MessageType.daoPoll:
      //   var bean = Poll.fromJson(jsonDecode(data ?? "{}"));
      //   data = bean.title ??  L.the_poll_info.tr;
      //   break;   
      default:
    }
    return data;
  }

  bool _isNotText(MessageEvent? m) {
    return m?.type != MessageType.text && m?.type != MessageType.walletAddress && m?.type != MessageType.meeting;
  }

  bool _isVideo(MessageEvent? msgData) {
    return msgData?.type == MessageType.video;
  }
  bool _isImage(MessageEvent? msgData) {
    return msgData?.type==MessageType.image;
  }
  MessageType _getType(MessageEvent? msgData ) {
    return msgData?.type ?? MessageType.text;
  }

  Widget _createImageView(MessageEvent? messageEvent) {
    var imageUnitList=_imageUnitListMap[messageEvent?.msgId];
    var thumpPath=_thumpPathMap[messageEvent?.msgId];
    var imagePath0=_imagePathMap[messageEvent?.msgId];
    String? imagePath = messageEvent?.thumbnailPath??messageEvent?.filePath;
    if ((imageUnitList?.isNotEmpty ?? false) && imagePath != null) {
      return _createExtendedImageView(imageUnitList, imagePath);
    }else if(thumpPath?.isNotEmpty??false){
      if(isCipherFile(thumpPath)){
        return _createEncExtendedImageView(messageEvent?.msgId??'',thumpPath!,messageEvent?.thumbnailFragment??"");
      }
      return _createExtendedImageView(null, thumpPath!);
    }else if(imagePath0?.isNotEmpty??false){
      if(isCipherFile(imagePath0)){
        return _createEncExtendedImageView(messageEvent?.msgId??'',imagePath0!,messageEvent?.fileFragment??"");
      }
      return _createExtendedImageView(null, imagePath0!);
    }else if (_getType(messageEvent) == MessageType.file) {
      return _imageAsset(_fileUni8, R.file);
    }
    return _imageAsset(_errorUni8, R.icoPicLoadFailed);
  }
  Widget _createExtendedImageView(Uint8List? data, String imagePath) {
    return data==null?ExtendedImage.file(
      File(imagePath),
      fit: BoxFit.cover,
      imageCacheName: imagePath,
      width: 30.w,
      height: 30.w,
      borderRadius: const BorderRadius.all(Radius.circular(30)).r,
      maxBytes: Config.maxImageBytes,
      loadStateChanged: (state) {
        if (state.extendedImageLoadState == LoadState.failed) {
          return _imageAsset(_errorUni8,R.icoPicLoadFailed);
        }
        return null;
      },
    ):ExtendedImage.memory(
      data,
      fit: BoxFit.cover,
      imageCacheName: imagePath,
      width: 30.w,
      height: 30.w,
      borderRadius: const BorderRadius.all(Radius.circular(30)).r,
      maxBytes: Config.maxImageBytes,
      loadStateChanged: (state) {
        if (state.extendedImageLoadState == LoadState.failed) {
          return _imageAsset(_errorUni8,R.icoPicLoadFailed);
        }
        return null;
      },
    );
  }
  Widget _createEncExtendedImageView(String msgId,String imagePath,String fileFragment) {
    return FutureBuilder(
        future: ChatioNative.utilFileDecryptToMemory(imagePath, fileFragment),
        builder: (c,s){
          Uint8List? imageDatas;
          if(s.connectionState==ConnectionState.done){
            imageDatas=s.requireData;
            _imageUnitListMap[msgId]=imageDatas;
          }
          return imageDatas==null?ExtendedImage.asset(
            R.icImageLoading,
            fit: BoxFit.cover,
            imageCacheName: imagePath,
            width: 30.r,
            height: 30.r,
            borderRadius: const BorderRadius.all(Radius.circular(30)).r,
            maxBytes: Config.maxImageBytes,
            loadStateChanged: (state) {
              if (state.extendedImageLoadState == LoadState.failed) {
                return _imageAsset(_errorUni8,R.icoPicLoadFailed);
              }
              return null;
            },
          ):ExtendedImage.memory(
            imageDatas,
            fit: BoxFit.cover,
            imageCacheName: imagePath,
            width: 30.r,
            height: 30.r,
            borderRadius: const BorderRadius.all(Radius.circular(30)).r,
            maxBytes: Config.maxImageBytes,
            loadStateChanged: (state) {
              if (state.extendedImageLoadState == LoadState.failed) {
                return _imageAsset(_errorUni8,R.icoPicLoadFailed);
              }
              return null;
            },
          );
        });
  }
  Widget _imageAsset(String name, String key) {
    return ExtendedImage.asset(
      name,
      borderRadius: const BorderRadius.all(Radius.circular(3.0)).r,
      fit: BoxFit.cover,
      width: 30.r,
      height: 30.r,
      imageCacheName: key,
      maxBytes: Config.maxImageBytes,
    );
  }

  adminTopMsgSync(String channelId,ChannelInfoModelData info) async {
    var options = info.options;
    if(options!=null){
      try{
        var channelOptions = ChannelOptions.fromJson(json.decode(options));
        // int? optionsCount = Get.find<AppConfigService>().readChannelInfoOptionsCount(channelId);
        // if(optionsCount!=null&&optionsCount==channelOptions.count){
        //   return;
        // }
        ChannelOptionTask().topMsgAdmin(channelId, channelOptions);
      }catch(e){
        e.printError();
      }
    }
  }
  Future<bool> _cancelAdminTopMsg(MessageEvent m,bool isListItem) async {
    if (isAdmin&&Get.find<MessageController>().rxMySelfIsChannelAdminOrOwner.value) {
      showLoadingDialog();
      var bool = await Get.find<MessageController>()
          .msgTopAdminCancel(m);
      dismissLoadingDialog();
      toast(bool?L.top_cancel_success.tr:L.top_cancel_failed.tr);
      return bool;
    } else {
      Get.find<AppConfigService>().cancelMsgTopTop(
          m.owner, m.msgId,
          clear: !isListItem);
      return true;
    }
  }
}


typedef FutureCallBackBool = Future<bool> Function(bool b);
class TopMsgDialog extends StatelessWidget{
  static const topMsgDialogTag="TopMsgDialog";
  final height=220.r;

  final int index;
  final FutureCallBackBool callBack;
  TopMsgDialog({super.key,required this.index, required this.callBack});

  @override
  Widget build(BuildContext context) {
    var loading = false.obs;
    var check = false;
    return Stack(
      alignment: Alignment.center,
      children: [
        Container(
          height: height,
          margin: const EdgeInsets.only(left: 20, right: 20).r,
          decoration: BoxDecoration(
              color: AppColors.white,
              borderRadius: const BorderRadius.all(Radius.circular(16)).r),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                margin: const EdgeInsets.only(
                  top: 18,
                ).r,
                alignment: Alignment.center,
                child: Text(
                  L.top_msg.tr,
                  style: TextStyle(
                    color: AppColors.colorFF000000,
                    fontSize: 18.sp,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              Container(
                margin: const EdgeInsets.only(top: 18,left: 20,right: 20).r,
                child: Text(
                  L.whether_to_display_this_message_at_the_top_of_the_session.tr,
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: AppColors.colorFF333333,
                  ),
                ),
              ),
              Container(
                margin: const EdgeInsets.only(top: 10,left: 25,right: 25).r,
                child:  Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    StatefulBuilder(
                      builder: (_, setState) {
                        return Checkbox(
                          value: check,
                          onChanged: (value) {
                            setState(() {
                              check = value ?? false;
                            });
                          },
                        );
                      },
                    ),
                    Text(
                      L.visible_to_all_group_members.tr,
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: const Color(0xff525252),
                      ),
                    ),

                  ],
                ),
              ),
              const Spacer(),
              Padding(
                padding:
                const EdgeInsets.symmetric(horizontal: 20, vertical: 10)
                    .r,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Flexible(
                      child: TextButton(
                        style: ButtonStyle(
                          fixedSize: MaterialStateProperty.all(
                              Size.fromHeight(40.r)),
                          shape: MaterialStateProperty.all(
                            RoundedRectangleBorder(
                              borderRadius:
                              const BorderRadius.all(Radius.circular(5)).r,
                            ),
                          ),
                          backgroundColor: MaterialStateProperty.all(
                              AppColors.colorFFF2F2F2),
                        ),
                        onPressed: () {
                          SmartDialog.dismiss(tag: TopMsgDialog.topMsgDialogTag,result: false);
                        },
                        child: Container(
                          height: 40.r,
                          alignment: Alignment.center,
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Text(
                                L.cancel.tr,
                                style: TextStyle(
                                  color: AppColors.colorFF333333,
                                  fontSize: 14.sp,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                    SizedBox(
                      width: 10.w,
                    ),
                    Flexible(
                      child: TextButton(
                        style: ButtonStyle(
                          fixedSize: MaterialStateProperty.all(
                              Size.fromHeight(40.r)),
                          shape: MaterialStateProperty.all(
                            RoundedRectangleBorder(
                              borderRadius:
                              const BorderRadius.all(Radius.circular(5)).r,
                            ),
                          ),
                          backgroundColor:
                          MaterialStateProperty.all(AppColors.appDefault),
                        ),
                        onPressed: () async {
                          loading.value=true;
                          var ret=await callBack.call(check);
                          loading.value=false;
                          if(ret){
                            SmartDialog.dismiss(tag: TopMsgDialog.topMsgDialogTag,result: ret);
                          }
                        },
                        child: Container(
                          height: 40.r,
                          alignment: Alignment.center,
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Text(
                                L.chat_item_pin.tr,
                                style: TextStyle(
                                  color: AppColors.white,
                                  fontSize: 14.sp,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        Obx(() => Visibility(
          visible: loading.value,
          child: Container(
            margin: const EdgeInsets.only(left: 16, right: 16).r,
            decoration: BoxDecoration(
                color: const Color.fromRGBO(0, 0, 0, 0.46),
                borderRadius:
                const BorderRadius.all(Radius.circular(16)).r),
            height: height,
            child: loadingView(),
          ),
        )),
      ],
    );
  }
}


