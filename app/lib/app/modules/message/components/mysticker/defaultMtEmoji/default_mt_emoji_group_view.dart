import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/modules/message/message_controller.dart';
import 'package:flutter_metatel/app/widgets/emoji/emoji_remote.dart';
import 'package:flutter_metatel/core/values/colors.dart';
import 'default_mt_emoji_view.dart';

class MyDefaultMtEmojiGroupView extends StatefulWidget {
  const MyDefaultMtEmojiGroupView(this.messageController, this.emojiDatas,
      {this.fronSourceTips = '', Key? key})
      : super(key: key);

  final MessageController messageController;
  final List<EmojiWidgetData> emojiDatas;
  final String fronSourceTips;

  @override
  State<StatefulWidget> createState() {
    return _MyDefaultMtEmojiGroupViewState();
  }
}

class _MyDefaultMtEmojiGroupViewState extends State<MyDefaultMtEmojiGroupView>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final List<Tab> _tabs = [];
  final List<Widget> _children = [];

  @override
  void initState() {
    super.initState();
    for (var element in widget.emojiDatas) {
      _tabs.add(Tab(text: element.group));
      _children.add(MyDefaultMtEmojiView(
        widget.messageController,
        element.datas,
        fronSourceTips: widget.fronSourceTips,
      ));
    }
    _tabController = TabController(length: _tabs.length, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    _children.clear();
    _tabs.clear();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Theme.of(context).primaryColor,
      child: Column(
        children: [
          TabBar(
            indicatorColor: Theme.of(context).colorScheme.primary,
            indicatorSize: TabBarIndicatorSize.label,
            indicatorPadding: EdgeInsets.zero,
            labelColor: Theme.of(context).colorScheme.primary,
            unselectedLabelColor: AppColors.colorFFA1A5B3,
            controller: _tabController,
            tabs: _tabs,
            isScrollable: true,
            padding: EdgeInsets.zero,
          ),
          Expanded(
            child: Container(
              color: Theme.of(context).primaryColor,
              // padding: const EdgeInsets.only(left: 15, right: 15).r,
              child: TabBarView(
                controller: _tabController,
                children: _children,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
