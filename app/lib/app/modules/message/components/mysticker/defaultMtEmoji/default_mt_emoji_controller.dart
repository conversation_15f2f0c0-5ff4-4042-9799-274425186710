/*
 * <AUTHOR> <PERSON><PERSON>
 * @Date         : 2022-07-04 17:48:18
 * @Description  : 
 * @LastEditors  : <PERSON><PERSON>
 * @LastEditTime : 2022-07-20 11:50:07
 * @FilePath     : /flutter_metatel/lib/app/modules/mycollection/avatar/default_sticker_controller.dart
 */

import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/data/services/emoji_manage_service.dart';
import 'package:flutter_metatel/app/modules/message/message_controller.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

class MyDefaultEmojiController extends GetxController {
  late RefreshController refreshController;
  List<String> emojiDatas = [];

  MyDefaultEmojiController(List<String> datas) {
    emojiDatas = datas;
  }

  @override
  void onInit() {
    refreshController = RefreshController(
      initialRefresh: false,
    );
    super.onInit();
  }

  onRefresh() async {
    // monitor network fetch
    // data.value=mexGetDdcListResData;
    // if failed,use refreshFailed()
    refreshController.refreshCompleted();
  }

  onLoading() async {
    // monitor network fetch
    // if failed,use loadFailed(),if no data return,use LoadNodata()
    // items.add((items.length + 1).toString());
    refreshController.loadComplete();
  }

  onDel(MessageController msgController) {
    _deleteText(msgController.textEditingControllerInput);
  }

  onItemTap(MessageController msgController, String tag) {
    msgController.text = true;
    _insertText(tag, msgController.textEditingControllerInput);
  }

  _insertText(String text, TextEditingController _textEditingController) {
    final TextEditingValue value = _textEditingController.value;
    final int start = value.selection.baseOffset;
    int end = value.selection.extentOffset;
    if (value.selection.isValid) {
      String newText = '';
      if (value.selection.isCollapsed) {
        if (end > 0) {
          newText += value.text.substring(0, end);
        }
        newText += text;
        if (value.text.length > end) {
          newText += value.text.substring(end, value.text.length);
        }
      } else {
        newText = value.text.replaceRange(start, end, text);
        end = start;
      }

      _textEditingController.value = value.copyWith(
          text: newText,
          selection: value.selection.copyWith(
              baseOffset: end + text.length, extentOffset: end + text.length));
    } else {
      _textEditingController.value = TextEditingValue(
          text: text,
          selection:
              TextSelection.fromPosition(TextPosition(offset: text.length)));
    }
  }

  // SchedulerBinding.instance.addPostFrameCallback((Duration timeStamp) {
  //   _key.currentState?.bringIntoView(_textEditingController.selection.base);
  // });
  ///系统键盘输入的信息使用自定义的按钮删除
  _backSpaceText(
      TextEditingController textEditingController, int start, int end) {
    String originalText = textEditingController
        .text; // textEditingController 我textField的Controller
    String splitPreText = originalText.substring(0, end);
    String splitAddText = originalText.substring(end, originalText.length);
    dynamic text;
    if (originalText.isNotEmpty) {
      text = splitPreText.characters
          .skipLast(1); // 这里是做一次字符串的转化，我们不能直接as String去转，不然会报错
      int offset = "$text".length;
      text = "$text$splitAddText";
      AppLogger.d("_deleteText _backSpaceText== offset==$offset text==$text");
      textEditingController.value = textEditingController.value.copyWith(
        text: text,
        selection: textEditingController.value.selection
            .copyWith(baseOffset: offset, extentOffset: offset),
      );
    }
  }

  _deleteText(TextEditingController textEditingController) {
    EmojiManageService service = Get.find();

    final TextEditingValue value = textEditingController.value;
    final int start = value.selection.baseOffset;
    final int end = value.selection.extentOffset;
    int offset = 0;
    String text = value.text;
    if (value.selection.start > 0 && value.selection.end > 0) {
      if (value.selection.isCollapsed) {
        String tempText = value.text.substring(0, start);
        int i = tempText.lastIndexOf('[');
        if (i > -1) {
          String cs = tempText.substring(i, start);
          if (service.find(cs) != null) {
            text = text.replaceRange(i, start, "");
            offset = i;
            AppLogger.d(
                "_deleteText emojiPanel text==$text start==$start end==$end text.length==${text.length} offset==$offset");
            textEditingController.value = value.copyWith(
              text: text,
              selection: value.selection
                  .copyWith(baseOffset: offset, extentOffset: offset),
            );
            return;
          }
        }
      }
      _backSpaceText(textEditingController, start, end);
    }
  }
}
