import 'dart:io';

import 'package:flutter/material.dart';

import 'package:flutter_metatel/app/data/services/emoji_manage_service.dart';
import 'package:flutter_metatel/app/modules/message/message_controller.dart';
import 'package:flutter_metatel/app/widgets/emoji/emoji_remote.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import '../../../../../../core/values/config.dart';
import '../../../../../../r.dart';
import 'default_mt_emoji_controller.dart';

class MyDefaultMtEmojiView extends StatefulWidget {
  const MyDefaultMtEmojiView(this.messageController, this.emojiDatas,
      {this.fronSourceTips = '', Key? key})
      : super(key: key);

  final MessageController messageController;
  final List<String> emojiDatas;
  final String fronSourceTips;

  @override
  State<StatefulWidget> createState() {
    return _MyDefaultMtEmojiViewState();
  }
}

class _MyDefaultMtEmojiViewState extends State<MyDefaultMtEmojiView> {
  late MyDefaultEmojiController _controller;
  final EmojiManageService _service = Get.find();

  @override
  void initState() {
    super.initState();
    var time = DateTime.now();
    var tag = "${time.minute}:${time.second}:${time.millisecond}";
    _controller =
        Get.put(MyDefaultEmojiController(widget.emojiDatas), tag: tag);
  }

  @override
  void dispose() {
    Get.delete<MyDefaultEmojiController>();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Theme.of(context).primaryColor,
      padding: const EdgeInsets.only(left: 0, right: 0, top: 20).r,
      child: SmartRefresher(
        controller: _controller.refreshController,
        enablePullDown: true,
        enablePullUp: false,
        header: MaterialClassicHeader(
          color: Theme.of(context).colorScheme.primary,
        ),
        onRefresh: _controller.onRefresh,
        child: Stack(
          children: [
            CustomScrollView(slivers: [
              SliverGrid(
                gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 7,
                  crossAxisSpacing: 10.w,
                  mainAxisSpacing: 10.h,
                ),
                delegate: SliverChildBuilderDelegate(
                  (BuildContext context, int index) {
                    var tag = widget.emojiDatas[index];
                    var data = _service.find(tag);

                    var width = 35.w;
                    var height = 35.w;

                    late Widget image;
                    if (data != null) {
                      if (data.source == EmojiSource.openEmoji) {
                        width = 45.w;
                        height = 45.w;
                      }

                      if (data.type == EmojiFileType.assets) {
                        image = Image.asset(
                          data.file,
                          width: width,
                          height: height,
                        );
                      } else {
                        image = Image.file(
                          File(data.file),
                          width: width,
                          height: height,
                        );
                      }
                    } else {
                      image = Icon(
                        Icons.error,
                        size: width,
                      );
                    }

                    return GestureDetector(
                      onTap: () {
                        _controller.onItemTap(widget.messageController, tag);
                      },
                      child: Container(
                        alignment: Alignment.center,
                        child: image,
                      ),
                    );
                  },
                  childCount: widget.emojiDatas.length,
                ),
              ),
              SliverToBoxAdapter(
                child: Container(
                  padding: EdgeInsets.only(bottom: 10.r),
                  alignment: Alignment.center,
                  child: Text(widget.fronSourceTips),
                ),
              )
            ]),
            Positioned(
              right: 10.w,
              bottom: 10.w,
              child: IconButton(
                iconSize: 30.r,
                onPressed: () {
                  _controller.onDel(widget.messageController);
                },
                icon: Image.asset(
                  R.icoKeyboardDelete,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
