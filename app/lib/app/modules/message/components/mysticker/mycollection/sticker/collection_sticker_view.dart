import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/modules/message/components/mysticker/mycollection/sticker/collection_sticker_controller.dart';
import 'package:flutter_metatel/app/modules/message/message_controller.dart';
import 'package:flutter_metatel/core/utils/util.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import '../../../../../../../core/utils/app_log.dart';
import '../../../../../../../r.dart';
import '../../../../../../data/models/res/mex_get_ddc_list_res_model.dart';
import '../../../../../../widgets/loading_view.dart';
import '../../../attachment_dialog.dart';

class CollectionStickerView extends GetView<CollectionStickerController> {
  const CollectionStickerView({Key? key, required this.messageController})
      : super(key: key);
  final MessageController messageController;

  @override
  Widget build(BuildContext context) {
    Get.put(CollectionStickerController());
    return Obx(() {
      return Container(
        decoration: const BoxDecoration(color: Colors.white),
        padding: const EdgeInsets.only(left: 0, right: 0, top: 20).r,
        child: SmartRefresher(
          controller: controller.refreshController,
          enablePullDown: true,
          enablePullUp: false,
          header: MaterialClassicHeader(
            color: Theme.of(context).colorScheme.primary,
          ),
          onRefresh: controller.onRefresh,
          child: Container(
            decoration: const BoxDecoration(color: Colors.white),
            child: (controller.stickerList.isEmpty)?buildNoData():GridView.builder(
              itemCount: controller.stickerList.length,
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 5,
                crossAxisSpacing: 16.w,
                mainAxisSpacing: 16.h,
              ),
              itemBuilder: (BuildContext context, int index) {
                List<MexGetDdcListResDataList>? value =
                    controller.stickerList;
                for (MexGetDdcListResDataList element in value) {
                  AppLogger.d("element==${element.name}");
                  AppLogger.d("fileUrl==${element.fileUrl}");
                }
                return GestureDetector(
                  onTap: () {
                    if ((value[index].fileUrl == null)) {
                      return;
                    }
                    controller.downloadSticker(value[index].fileUrl).then(
                        (value) => messageController.onAttachmentSelect([
                              AttachmentEntity(MessageType.sticker,
                                  filePath: value)
                            ]));
                  },
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: (value[index].fileUrl == null)
                        ? Image.asset(R.icoPicLoadFailed)
                        : CachedNetworkImage(
                            imageUrl: value[index].fileUrl ?? "",
                            placeholder: (context, url) {
                              return const Center(
                                child: LoadingView(),
                              );
                            },
                            errorWidget: (context, url, error) =>
                                Image.asset(R.icoPicLoadFailed),
                          ),
                  ),
                );
              },
            ),
          ),
        ),
      );
    });
  }
}
