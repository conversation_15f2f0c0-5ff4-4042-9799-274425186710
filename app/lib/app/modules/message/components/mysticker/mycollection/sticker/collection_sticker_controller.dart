import 'dart:io';

import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import '../../../../../../../core/utils/app_log.dart';
import '../../../../../../../core/utils/util.dart';
import '../../../../../../data/models/res/mex_get_ddc_list_res_model.dart';
import '../../../../../../data/providers/api/api.dart';
import '../../../../../../data/services/chatio_service.dart';
import '../../../../../../data/services/config_service.dart';
import '../../../../../../data/services/secure_store_service.dart';
import '../../../../../dao/api/dao_api.dart';



class CollectionStickerController extends GetxController{
  final RxList<MexGetDdcListResDataList> stickerList = RxList<MexGetDdcListResDataList>();
  final RxBool stickerListEmpty = false.obs;
  final RefreshController refreshController=RefreshController(
    initialRefresh: true,
  );

  final AppConfigService configService = Get.find<AppConfigService>();
  final String ddcListTag = "表情";

  void onRefresh() async {
    // monitor network fetch
    String? addressMex;
    addressMex = await  Get.find<SecureStoreService>().secureReadUserAddressMex();
    if (addressMex == null || addressMex.isEmpty) {
      addressMex = await  Get.find<SecureStoreService>().secureReadUserAddressMex();
    }
    if (addressMex == null || addressMex.isEmpty) {
      AppLogger.d("无藏品---addressMex==$addressMex");
      refreshController.refreshCompleted();
      return;
    }
    var response =
    await Get.find<DaoApiProvider>().getMexDdcList(addressMex, ddcListTag);
    List<MexGetDdcListResDataList>? mexGetDdcListResData = response.data?.data;
    if (mexGetDdcListResData?.isEmpty??true) {
      refreshController.refreshCompleted();
      AppLogger.d("无藏品");
      // toast("获取藏品数据失败，请稍后重试！");
      return;
    }
    stickerList.clear();
    stickerList.addAll(mexGetDdcListResData!);
    stickerListEmpty.value=mexGetDdcListResData.isEmpty;
    // data.value=mexGetDdcListResData;
    // if failed,use refreshFailed()
    refreshController.refreshCompleted();
  }

  ///下载贴纸
  Future<String?> downloadSticker(String? url) async {
    if (url == null) {
      return null;
    }
    AppLogger.d("downloadSticker url==$url");
    String? path = appSupporAbsolutePath(url.split('/').last);
    if (!File(path ?? "").existsSync()) {
      path = await downloadFile(url, savePath: path);
    }
    AppLogger.d("path head==$path");
    if (path != null && File(path).existsSync()) {
        return path;
    }
    return null;
  }
}