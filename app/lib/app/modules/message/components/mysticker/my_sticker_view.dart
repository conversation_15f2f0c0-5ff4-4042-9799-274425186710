import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/modules/message/message_controller.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../../core/values/colors.dart';
import 'my_sticker_controller.dart';

class MyStickerView extends StatefulWidget {
  const MyStickerView(
      {super.key,
      required this.messageController});

  final MessageController messageController;

  @override
  State<StatefulWidget> createState() => _MyStickerViewState();
}

class _MyStickerViewState extends State<MyStickerView> {
  final MyStickerController controller = Get.put(MyStickerController());

  @override
  void initState() {
    controller.init(widget.messageController);
    super.initState();
  }

  @override
  void dispose() {
    Get.delete<MyStickerController>();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Obx(
      () {
        return Container(
          color: Theme.of(context).primaryColor,
          height: /*messageController.stickerHeight.value == 0
          ? 400
          : */
              widget.messageController.stickerHeight.value,
          child: Column(
            children: [
              Container(
                alignment: Alignment.topLeft,
                color: Theme.of(context).primaryColor,
                child: TabBar(
                  indicatorColor: Theme.of(context).colorScheme.primary,
                  indicatorSize: TabBarIndicatorSize.label,
                  indicatorPadding: EdgeInsets.zero,
                  labelColor: Theme.of(context).colorScheme.primary,
                  unselectedLabelColor: AppColors.colorFFA1A5B3,
                  controller: controller.tabController,
                  tabs: controller.myTabs,
                  isScrollable: true,
                ),
              ),
              Expanded(
                child: Container(
                  // height: (messageController.stickerHeight.value - 50) < 200.h
                  //     ? StickerDefault.stickerDefaultHeight
                  //     : messageController.stickerHeight.value - 50,
                  color: Theme.of(context).primaryColor,
                  padding: const EdgeInsets.only(left: 15, right: 15).r,
                  child: TabBarView(
                    controller: controller.tabController,
                    children: controller.tabWidgetList,
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
