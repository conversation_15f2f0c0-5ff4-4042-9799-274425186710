import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/data/services/emoji_manage_service.dart';
import 'package:flutter_metatel/app/widgets/emoji/emoji.dart';
import 'package:get/get.dart';

import '../../../../../core/languages/l.dart';
import '../../../../../core/values/config.dart';
import '../../message_controller.dart';
import 'default/default_sticker_view.dart';
import 'defaultMtEmoji/default_mt_emoji_view.dart';
import 'defaultOne/default_emoji_sticker_view.dart';
import 'defaultOne/default_one_sticker_view.dart';
import 'mycollection/sticker/collection_sticker_view.dart';

typedef CollectionCallBack = void Function();

class MyStickerController extends GetxController
    with GetTickerProviderStateMixin {
  CollectionCallBack? callBack;
  TabController? tabController;
  List<Tab> myTabs = [];
  List<Widget> tabWidgetList = [];

  @override
  void onClose() {
    if (tabController != null) {
      tabController!.dispose();
    }
  }

  init(MessageController messageController) {
    myTabs.clear();
    tabWidgetList.clear();

    // 存储服务上的表情包
    EmojiManageService service = Get.find();
    var datas = service.tabs(messageController);
    for (var element in datas) {
      myTabs.add(Tab(text: element.title));
      tabWidgetList.add(element.tab);
    }

    // 公司设计的表情包
    List<String> emojiDatas = [];
      emojiDatas = EmojiUtil.mtMEmojiTagList;

    tabWidgetList.add(MyDefaultMtEmojiView(messageController, emojiDatas));
    myTabs.add(
      Tab(
        text: L.emoji.tr,
      ),
    );

    // OpenMoji表情包
      tabWidgetList.add(
          MyDefaultMtEmojiView(messageController, EmojiUtil.mOpenmojiTagList));
      myTabs.add(const Tab(text: "OMI"));

    if (!messageController.rxSelfDestruct.value) {
        ///藏品
        myTabs.add(
          Tab(
            text: L.more.tr,
          ),
        );
        tabWidgetList.add(
          CollectionStickerView(
            messageController: messageController,
          ),
        );
    }
    tabController = TabController(length: myTabs.length, vsync: this);
  }
}
