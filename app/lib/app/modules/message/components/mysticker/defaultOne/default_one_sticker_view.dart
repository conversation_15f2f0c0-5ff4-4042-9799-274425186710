
import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/modules/message/message_controller.dart';
import 'package:flutter_metatel/core/values/config.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'default_one_sticker_controller.dart';

class MyDefaultOneStickerView extends StatefulWidget{
  const MyDefaultOneStickerView(this.messageController, {Key? key})
      : super(key: key);

  final MessageController messageController;

  @override
  State<StatefulWidget> createState() =>_MyDefaultOneStickerViewState();

}
class _MyDefaultOneStickerViewState extends State<MyDefaultOneStickerView> {

  final _controller = Get.put(MyDefaultOneStickerController());

  @override
  void dispose() {
    Get.delete<MyDefaultOneStickerController>();
    super.dispose();
  }
  @override
  Widget build(BuildContext context) {

    /////获取asset里面的文件
    // DefaultAssetBundle.of(context)
    //     .loadString('AssetManifest.json')
    //     .then((value) {
    //   final images = json
    //       .decode(value)
    //       .keys
    //       .where((String key) => key.startsWith('assets/images/sticker'));
    //   for (String path in images) {
    //     AppLogger.d("images path==$path");
    //     StickerDefault.pathList.add(path);
    //   }
    // });
    return Container(
        color: Theme.of(context).primaryColor,
        padding: const EdgeInsets.only(left: 0, right: 0, top: 20,bottom: 10).r,
        child: SmartRefresher(
          controller: _controller.refreshController,
          enablePullDown: true,
          enablePullUp: false,
          header: MaterialClassicHeader(
            color: Theme.of(context).colorScheme.primary,
          ),
          onRefresh: _controller.onRefresh,
          child: GridView.builder(
            itemCount: StickerDefault.pathListRabbit.length,
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 5,
              crossAxisSpacing: 16.w,
              mainAxisSpacing: 16.h,
            ),
            itemBuilder: (BuildContext context, int index) {
              return GestureDetector(
                onTap: () {
                  widget.messageController.sendDefaultSticker(StickerDefault.pathListRabbit[index]);
                },
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: Image.asset(StickerDefault.pathListRabbit[index]),
                ),
              );
            },
          ),
        ),
      );
  }
}
