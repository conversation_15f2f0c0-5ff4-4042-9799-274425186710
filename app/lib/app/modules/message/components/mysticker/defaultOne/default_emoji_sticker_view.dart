
import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/modules/message/message_controller.dart';
import 'package:flutter_metatel/core/values/config.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'default_emoji_sticker_controller.dart';
import 'default_one_sticker_controller.dart';

class MyDefaultEmojiStickerView extends StatefulWidget{
  const MyDefaultEmojiStickerView(this.messageController, {Key? key})
      : super(key: key);

  final MessageController messageController;

  @override
  State<StatefulWidget> createState() =>_MyDefaultEmojiStickerViewState();

}
class _MyDefaultEmojiStickerViewState extends State<MyDefaultEmojiStickerView> {

  final _controller = Get.put(MyDefaultEmojiStickerController());

  @override
  void dispose() {
    Get.delete<MyDefaultEmojiStickerController>();
    super.dispose();
  }
  @override
  Widget build(BuildContext context) {

    return Container(
        color: Theme.of(context).primaryColor,
        padding: const EdgeInsets.only(left: 0, right: 0, top: 20,bottom: 10).r,
        child: SmartRefresher(
          controller: _controller.refreshController,
          enablePullDown: true,
          enablePullUp: false,
          header: MaterialClassicHeader(
            color: Theme.of(context).colorScheme.primary,
          ),
          onRefresh: _controller.onRefresh,
          child: GridView.builder(
            itemCount: StickerDefault.pathListEmojiRabbit.length,
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 5,
              mainAxisSpacing: 10.h,
            ),
            itemBuilder: (BuildContext context, int index) {
              return GestureDetector(
                onTap: () {
                  widget.messageController.sendDefaultSticker(StickerDefault.pathListEmojiRabbit[index]);
                },
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: Image.asset(StickerDefault.pathListEmojiRabbit[index]),
                ),
              );
            },
          ),
        ),
      );
  }
}
