/*
 * <AUTHOR> <PERSON><PERSON>
 * @Date         : 2022-07-04 17:48:18
 * @Description  : 
 * @LastEditors  : <PERSON><PERSON>
 * @LastEditTime : 2022-07-20 11:50:07
 * @FilePath     : /flutter_metatel/lib/app/modules/mycollection/avatar/default_sticker_controller.dart
 */

import 'package:flutter_metatel/app/data/services/config_service.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';




class MyDefaultEmojiStickerController extends GetxController {
  late RefreshController refreshController;
  @override
  void onInit() {
    refreshController = RefreshController(
      initialRefresh: false,
    );
    super.onInit();
  }
  final AppConfigService configService = Get.find<AppConfigService>();

  void onRefresh() async {
    // monitor network fetch
    // data.value=mexGetDdcListResData;
    // if failed,use refreshFailed()
    refreshController.refreshCompleted();
  }

  void onLoading() async {
    // monitor network fetch
    // if failed,use loadFailed(),if no data return,use LoadNodata()
    // items.add((items.length + 1).toString());
    refreshController.loadComplete();
  }
}
