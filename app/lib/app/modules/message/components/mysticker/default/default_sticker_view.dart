
import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/modules/message/message_controller.dart';
import 'package:flutter_metatel/core/values/config.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import 'default_sticker_controller.dart';
class MyDefaultStickerView extends StatefulWidget{
  const MyDefaultStickerView(this.messageController, {Key? key})
      : super(key: key);

  final MessageController messageController;

  @override
  State<StatefulWidget> createState() =>_MyDefaultStickerViewState();
}
class _MyDefaultStickerViewState extends State<MyDefaultStickerView> {


  final _controller = Get.put(MyDefaultStickerController());
  @override
  void dispose() {
    Get.delete<MyDefaultStickerController>();
    super.dispose();
  }
  @override
  Widget build(BuildContext context) {
    /////获取asset里面的文件
    // DefaultAssetBundle.of(context)
    //     .loadString('AssetManifest.json')
    //     .then((value) {
    //   final images = json
    //       .decode(value)
    //       .keys
    //       .where((String key) => key.startsWith('assets/images/sticker'));
    //   for (String path in images) {
    //     AppLogger.d("images path==$path");
    //     StickerDefault.pathList.add(path);
    //   }
    // });
    return Container(
        color: Theme.of(context).primaryColor,
        padding: const EdgeInsets.only(left: 0, right: 0, top: 20).r,
        child: SmartRefresher(
          controller: _controller.refreshController,
          enablePullDown: true,
          enablePullUp: false,
          header: MaterialClassicHeader(
            color: Theme.of(context).colorScheme.primary,
          ),
          onRefresh: _controller.onRefresh,
          child: GridView.builder(
            itemCount: StickerDefault.pathList.length,
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 5,
              crossAxisSpacing: 16.w,
              mainAxisSpacing: 16.h,
            ),
            itemBuilder: (BuildContext context, int index) {
              return GestureDetector(
                onTap: () {
                  widget.messageController.sendDefaultSticker(StickerDefault.pathList[index]);
                },
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: Image.asset(StickerDefault.pathList[index]),
                ),
              );
            },
          ),
        ),
      );
  }
}
