// import 'package:flutter/material.dart';
// import 'package:flutter_metatel/app/modules/realauthenticate/user_name_id_card_controller.dart';
// import 'package:flutter_metatel/app/widgets/mavatar.dart';
// import 'package:get/get.dart';
//
// import '../../../core/languages/l.dart';
// import '../../widgets/mavatar_circle_avatar.dart';
//
// class GatherFacePhotoPage extends GetView<UserNameAndIdCardController> {
//   GatherFacePhotoPage(this.name, this.idCard, {Key? key}) : super(key: key);
//   @override
//   final UserNameAndIdCardController controller =
//   Get.put(UserNameAndIdCardController(), permanent: true);
//
//   final String name;
//   final String idCard;
//
//   final TextEditingController _textEditControllerName = TextEditingController();
//   final TextEditingController _textEditControllerIDCARD = TextEditingController();
//
//   @override
//   Widget build(BuildContext context) {
//     controller.setNameAndIdCardHint(name,idCard);
//     return Scaffold(
//         appBar: AppBar(
//           title: Text(
//             L.identity_authenticate.tr,
//             style: const TextStyle(
//               color: Colors.black,
//             ),
//           ),
//           centerTitle: true,
//           leading: IconButton(
//             iconSize: 46,
//             onPressed: () {
//               Get.back();
//             },
//             icon: const Icon(Icons.chevron_left, color: Colors.blue),
//           ),
//         ),
//         body: Container(
//           color: Colors.white,
//           child: Padding(
//               padding: const EdgeInsets.only(
//                   left: 20, top: 20, right: 20, bottom: 150),
//               child: Column(
//                 crossAxisAlignment: CrossAxisAlignment.center,
//                 children: [
//                   Padding(
//                     padding: const EdgeInsets.only(left: 20, right: 20),
//                     child: Text(L.take_a_photo_of_your_own_face_please_make_sure_you_are_facing_the_phone_and_there_is_sufficient_light.tr),),
//                   const SizedBox(height: 10,),
//                   Padding(
//                     padding: const EdgeInsets.only(left: 50, right: 50),
//                     child: Text(controller.nameAndIdCardHint),),
//                   const SizedBox(height: 40,),
//                   const MAvatarCircle(diameter: 100,text: "",),
//                   const Spacer(),
//                   MaterialButton(
//                     minWidth: double.infinity,
//                     height: 50,
//                     elevation: 10,
//                     shape: RoundedRectangleBorder(
//                       borderRadius: BorderRadius.circular(20.0),
//                     ),
//                     color: Colors.blue,
//                     onPressed: () {},
//                     child: Text(
//                       L.gather_my_face.tr,
//                       style: const TextStyle(
//                         color: Colors.white,
//                       ),
//                     ),
//                   ),
//                 ],
//               )),
//         ),
//     resizeToAvoidBottomInset:false ,);
//   }
// }
