// import 'package:flutter_metatel/core/utils/app_log.dart';
// import 'package:get/get.dart';
// import 'package:image_picker/image_picker.dart';
//
// import '../../../core/utils/util.dart';
//
// class UserNameAndIdCardController extends GetxController{
//   late String nameAndIdCardHint;
//   late String name;
//   late String idCard;
//   final ImagePicker _imagePicker = ImagePicker();
//   @override
//   void onInit() {
//     super.onInit();
//   }
//   void setNameAndIdCardHint(String name,String idCard){
//     this.name=name;
//     this.idCard=idCard;
//     String replaceName="";
//     String replaceIdCard="***************";
//     for(int i=0;i<name.length-1;i++){
//       replaceName+="*";
//     }
//     var replaceNameResult = name.replaceRange(0, name.length-1, replaceName);
//     var replaceIdCardResult = idCard.replaceRange(1, idCard.length-2, replaceIdCard);
//     AppLogger.d("replaceNameResult==$replaceNameResult");
//     AppLogger.d("replaceIdCardResult==$replaceIdCardResult");
//     nameAndIdCardHint=replaceNameResult+","+replaceIdCardResult;
//   }
//
//   Future gatherFaceTakePhoto() async{
//     var image= await _imagePicker.pickImage(source: ImageSource.camera,maxWidth: 1080,maxHeight: 1920);
//     if(image!=null){
//       var path = image.path;
//        var base64Image = await imageToBase64(path);
//        var encodeComponent = Uri.encodeComponent(base64Image);
//
//     }
//   }
//
//
// }