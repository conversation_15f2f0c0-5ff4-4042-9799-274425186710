import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_metatel/app/data/providers/api/operation_center_api.dart';
import 'package:flutter_metatel/app/data/services/config_service.dart';
import 'package:flutter_metatel/core/languages/l.dart';
import 'package:flutter_metatel/core/values/code.dart';
import 'package:flutter_metatel/core/values/colors.dart';
import 'package:flutter_metatel/core/values/config.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../core/utils/util.dart';
import '../../data/models/real_name_verify_model.dart';
import '../../data/providers/api/api.dart';

class RealNameAuthenticatePage extends StatefulWidget {
  const RealNameAuthenticatePage({
    super.key,
    required this.data,
    this.waitSeconds,
  });

  final RealNameVerifyModel data;
  final int? waitSeconds;

  @override
  State<RealNameAuthenticatePage> createState() =>
      _RealNameAuthenticatePageState();
}

class _RealNameAuthenticatePageState extends State<RealNameAuthenticatePage> {
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _numberController = TextEditingController();
  final TextEditingController _phoneController = TextEditingController();

  @override
  void dispose() {
    _nameController.dispose();
    _numberController.dispose();
    _phoneController.dispose();
    super.dispose();
  }

  String _getTips(String? code) {
    String tips = '';
    if (VerifyCode.code0100 == code) {
      tips = L.idcard_error.tr;
    } else if (VerifyCode.code0103 == code) {
      tips = L.id_name_mismatch.tr;
    } else if (VerifyCode.code0105 == code) {
      tips = L.certification_expired.tr;
    } else {
      tips = 'code:$code';
    }
    return tips;
  }

  void _hideKeyboard() {
    FocusScopeNode currentFocus = FocusScope.of(context);
    if (!currentFocus.hasPrimaryFocus && currentFocus.focusedChild != null) {
      FocusManager.instance.primaryFocus?.unfocus();
    }
  }

  void _onAuthenticate() async {
    String name = _nameController.text;
    String number = _numberController.text;
    String phone = _phoneController.text;
    if (name.isEmpty) {
      toast(L.qr_code_name_not_empty.tr);
      return;
    }

    if (number.isEmpty) {
      toast(L.idcard_not_empty.tr);
      return;
    }
    // if (phone.isEmpty) {
    //   toast(L.phone_not_empty.tr);
    //   return;
    // }
    _hideKeyboard();

    showLoadingDialog(isBack: false, text: L.verifying_real_name.tr);
    var account = await Get.find<AppConfigService>().getUserNameWithoutDomain();
    var response = await Get.find<OperationCenterApiProvider>().realNameAuthentication(
      widget.data.verifyurl ?? '',
      widget.data.keyboxid ?? '',
      widget.data.signature ?? '',
      account ?? '',
      name,
      number,
      widget.data.timestamp ?? 0,
      phone,
    );

    if (response.statusCode != 200) {
      dismissLoadingDialog();
      toast('code:${response.statusCode}');
      return;
    }

    if (response.data?.code != VerifyCode.code0000) {
      dismissLoadingDialog();
      toast(_getTips(response.data?.code));
      return;
    }

    if (widget.waitSeconds != null) {
      Future.delayed(Duration(seconds: widget.waitSeconds!), () {
        dismissLoadingDialog();
        Get.back(result: StrKey.realNameSuccess);
      });
    } else {
      dismissLoadingDialog();
    }
  }

  Widget _buildTextField({
    TextEditingController? controller,
    int? maxLength,
    String? hintText,
    List<TextInputFormatter>? inputFormatters,
  }) {
    return TextField(
      controller: controller,
      maxLength: maxLength,
      style: TextStyle(color: AppColors.colorFF333333, fontSize: 16.sp),
      decoration: InputDecoration(
        hintText: hintText,
        hintStyle: TextStyle(color: AppColors.colorFF999999, fontSize: 16.sp),
        counterText: '',
        focusedBorder: const UnderlineInputBorder(
            borderSide: BorderSide(
          color: AppColors.colorFFF8F8F8,
          width: 0.5,
        )),
        enabledBorder: const UnderlineInputBorder(
            borderSide: BorderSide(
          color: AppColors.colorFFF8F8F8,
          width: 0.5,
        )),
      ),
      inputFormatters: inputFormatters,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: Text(
          L.real_authenticate.tr,
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
      ),
      body: GestureDetector(
        behavior: HitTestBehavior.translucent,
        onTap: _hideKeyboard,
        child: Column(
          children: [
            Divider(height: 0.5, thickness: 0.5, color: AppColors.colorDivider),
            const SizedBox(height: 25),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 16.w),
              child: _buildTextField(
                controller: _nameController,
                hintText: L.please_input_name.tr,
              ),
            ),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 16.w),
              child: _buildTextField(
                controller: _numberController,
                maxLength: 18,
                hintText: L.please_input_id_card.tr,
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp("[a-zA-Z]|[0-9]")),
                ],
              ),
            ),
            // Padding(
            //   padding: EdgeInsets.symmetric(horizontal: 16.w),
            //   child: _buildTextField(
            //     controller: _phoneController,
            //     maxLength: 11,
            //     hintText: L.please_phone_hint.tr,
            //     inputFormatters: [
            //       FilteringTextInputFormatter.allow(RegExp("[0-9]")),
            //     ],
            //   ),
            // ),
            SizedBox(height: 70.h),
            Container(
              padding: EdgeInsets.symmetric(horizontal: 16.w),
              alignment: Alignment.center,
              child: ElevatedButton(
                onPressed: _onAuthenticate,
                style: ElevatedButton.styleFrom(
                  minimumSize: const Size(double.infinity, 50),
                  shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(30)),
                ),
                child: Text(L.go_authenticate.tr),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
