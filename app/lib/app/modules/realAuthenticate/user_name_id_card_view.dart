// import 'dart:convert';
//
// import 'package:flutter/material.dart';
// import 'package:flutter/services.dart';
// import 'package:flutter_metatel/app/data/providers/native/chatio/chatio_async.dart';
// import 'package:flutter_metatel/app/modules/realauthenticate/user_name_id_card_controller.dart';
// import 'package:flutter_metatel/core/utils/app_log.dart';
// import 'package:flutter_metatel/core/values/config.dart';
// import 'package:get/get.dart';
//
// import '../../../core/languages/l.dart';
//
// class UserNameIdCardPage extends GetView<UserNameAndIdCardController> {
//   UserNameIdCardPage({Key? key}) : super(key: key);
//   @override
//   final UserNameAndIdCardController controller =
//   Get.put(UserNameAndIdCardController(), permanent: true);
//
//   final TextEditingController _textEditControllerName = TextEditingController();
//   final TextEditingController _textEditControllerIDCARD = TextEditingController();
//
//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//         appBar: AppBar(
//           title: Text(
//             L.real_authenticate.tr,
//             style: const TextStyle(
//               color: Colors.black,
//             ),
//           ),
//           centerTitle: true,
//           leading: IconButton(
//             iconSize: 46,
//             onPressed: () {
//               Get.back();
//             },
//             icon: const Icon(Icons.chevron_left, color: Colors.blue),
//           ),
//         ),
//         body: Container(
//           color: Colors.white,
//           child: Padding(
//               padding: const EdgeInsets.only(
//                   left: 20, top: 20, right: 20,bottom: 50),
//               child: Column(
//                 children: [
//                   Container(
//                     height: 300,
//                     decoration: const BoxDecoration(
//                       color: Colors.blue,
//                       borderRadius: BorderRadius.all(Radius.circular(10.0)),
//                     ),
//                     child: Padding(
//                       padding: const EdgeInsets.only(
//                           left: 10, right: 10, top: 60, bottom: 80),
//                       child: Column(
//                         crossAxisAlignment: CrossAxisAlignment.start,
//                         children: [
//                           Container(
//                             padding: const EdgeInsets.only(bottom: 10),
//                             constraints: const BoxConstraints(maxHeight: 50),
//                             child: TextField(
//                               autofocus: true,
//                               textAlignVertical: TextAlignVertical.bottom,
//                               controller: _textEditControllerName,
//                               decoration: InputDecoration(
//                                 hintText: L.please_input_name.tr,
//                                 contentPadding: const EdgeInsets.only(
//                                     left: 10, top: 0, bottom: 0),
//                                 border: const OutlineInputBorder(
//                                   borderSide: BorderSide.none,
//                                 ),
//                               ),
//                               maxLines: 1,
//                               maxLength: 20,
//                               maxLengthEnforcement:
//                                   MaxLengthEnforcement.enforced,
//                             ),
//                           ),
//                           Container(
//                             padding: const EdgeInsets.only(
//                               left: 10,
//                             ),
//                             child: const Divider(
//                                 height: 1, color: Color(0xffE6E6E6)),
//                           ),
//                           const Spacer(),
//                           Container(
//                             padding: const EdgeInsets.only(bottom: 10),
//                             constraints: const BoxConstraints(maxHeight: 50),
//                             child: TextField(
//                               controller: _textEditControllerIDCARD,
//                               textAlignVertical: TextAlignVertical.bottom,
//                               decoration: InputDecoration(
//                                 hintText: L.please_input_id_card.tr,
//                                 contentPadding: const EdgeInsets.only(left: 10),
//                                 border: const OutlineInputBorder(
//                                   borderSide: BorderSide.none,
//                                 ),
//                               ),
//                               maxLines: 1,
//                               maxLength: 18,
//                               maxLengthEnforcement:
//                                   MaxLengthEnforcement.enforced,
//                             ),
//                           ),
//                           Container(
//                             padding: const EdgeInsets.only(
//                               left: 10,
//                             ),
//                             child: const Divider(
//                                 height: 1, color: Color(0xffE6E6E6)),
//                           ),
//                         ],
//                       ),
//                     ),
//                   ),
//                   const Spacer(),
//                   MaterialButton(
//                     minWidth: double.infinity,
//                     height: 50,
//                     elevation: 10,
//                     shape: RoundedRectangleBorder(
//                       borderRadius: BorderRadius.circular(20.0),
//                     ),
//                     color: Colors.blue,
//                     onPressed: () async{
//                       var privatekey = await ChatioNative.utilCreatePrivate();
//                       if(privatekey!=null){
//                         var encode = base64.encode(privatekey);
//                         var s = await ChatioNative.authV3GetToken(Config.authUrl, "", encode, Config.node());
//                         AppLogger.d("authV3GetToken==$s");
//                       }
//                       // var isSuccess = verifyCardId(_textEditControllerIDCARD.text);
//                       // if(isSuccess){
//                       //   Get.to(GatherFacePhotoPage(_textEditControllerName.text,_textEditControllerIDCARD.text));
//                       // }
//                     },
//                     child: Text(
//                       L.go_authenticate.tr,
//                       style: const TextStyle(
//                         color: Colors.white,
//                       ),
//                     ),
//                   ),
//                 ],
//               )),
//         ),
//     resizeToAvoidBottomInset: true,);
//   }
// }
