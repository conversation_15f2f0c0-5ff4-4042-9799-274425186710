// import 'dart:io';
//
// import 'package:flutter/material.dart';
// import 'package:image_picker/image_picker.dart';
//
// class FaceCollectionPage extends StatefulWidget {
//   const FaceCollectionPage({
//     super.key,
//     required this.name,
//     required this.number,
//   });
//
//   final String name;
//   final String number;
//
//   @override
//   State<FaceCollectionPage> createState() => _FaceCollectionPageState();
// }
//
// class _FaceCollectionPageState extends State<FaceCollectionPage> {
//   final ImagePicker _imagePicker = ImagePicker();
//   String _imagePath = '';
//
//   @override
//   void initState() {
//     super.initState();
//   }
//
//   @override
//   void dispose() {
//     super.dispose();
//   }
//
//   void _onPickImage() async {
//     XFile? pickedFile = await _imagePicker.pickImage(
//       source: ImageSource.camera,
//     );
//
//     _imagePath = '';
//     if (pickedFile != null) {
//       _imagePath = pickedFile.path;
//     }
//     setState(() {});
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: AppBar(
//         title: Text('身份认证'),
//         centerTitle: true,
//       ),
//       body: Column(
//         children: [
//           const SizedBox(height: 30),
//           const Center(child: Text('拍摄你本人人脸，请确保正对手机，光线充足')),
//           const SizedBox(height: 15),
//           Center(child: Text('${widget.name}、${widget.number}')),
//           Container(
//             alignment: Alignment.center,
//             color: Colors.red,
//             height: 200,
//             child: Image.asset(
//               'assets/images/default_avatar.png',
//               width: 180,
//               height: 180,
//             ),
//           ),
//           if (_imagePath.isNotEmpty)
//             Expanded(child: Image.file(File(_imagePath))),
//           Expanded(
//             child: Container(
//               padding: const EdgeInsets.symmetric(horizontal: 5),
//               alignment: Alignment.center,
//               child: ElevatedButton(
//                 onPressed: _onPickImage,
//                 style: ElevatedButton.styleFrom(
//                   minimumSize: const Size(double.infinity, 50),
//                   shape: RoundedRectangleBorder(
//                       borderRadius: BorderRadius.circular(30)),
//                 ),
//                 child: Text('采集本人人脸'),
//               ),
//             ),
//           ),
//         ],
//       ),
//     );
//   }
// }
