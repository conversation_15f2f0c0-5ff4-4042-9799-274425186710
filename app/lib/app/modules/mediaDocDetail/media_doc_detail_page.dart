import 'package:extended_text/extended_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/data/enums/enum.dart';
import 'package:flutter_metatel/app/modules/mediaDocDetail/components/file_view.dart';
import 'package:flutter_metatel/app/modules/mediaDocDetail/components/photo_album_view.dart';
import 'package:flutter_metatel/core/languages/l.dart';
import 'package:flutter_metatel/core/utils/util.dart';
import 'package:flutter_metatel/core/values/colors.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:get/get.dart';

import '../../../r.dart';
import '../../widgets/at_widget/my_special_text_span_builder.dart';
import 'media_doc_detail_controller.dart';

class MediaDocDetailPage extends StatefulWidget {
  const MediaDocDetailPage({
    Key? key,
    required this.userName,
    required this.chatType,
    this.dispName,
  }) : super(key: key);

  final String userName;
  final String? dispName;
  final int chatType;

  @override
  State<MediaDocDetailPage> createState() => _MediaDocDetailPageState();
}

class _MediaDocDetailPageState extends State<MediaDocDetailPage> {
  final _controller = Get.put(MediaDocDetailController());

  final List<String> _categories = [
    L.the_picture.tr,
    L.the_videos.tr,
    L.the_file.tr,
  ];

  @override
  void initState() {
    super.initState();
    _controller.chatType = widget.chatType;
    _controller.userName = widget.userName;
  }

  /// 删除
  void _onDelete() {
    showBottomDialogCommonWithCancel(context, widgets: [
      getBottomSheetItemSimple(context, L.backup_confirm.tr,
          radius: 12, textColor: Colors.red, itemCallBack: () {
        _controller.onFileDelete();
      })
    ]);
  }

  /// 底部按钮
  Widget _buildBottomButton(bool disabled) {
    Color color =
        disabled ? const Color.fromARGB(255, 204, 204, 204) : Colors.blue;

    return Container(
      alignment: Alignment.bottomLeft,
      padding: const EdgeInsets.all(12),
      color: Colors.white,
      child: Row(
        children: [
          const Spacer(),
          // 删除按钮
          IconButton(
            onPressed: disabled ? null : _onDelete,
            iconSize: 30,
            icon: Image.asset(
              R.icFileDetail,
              color: color,
            ),
          ),
          const Spacer(),
          // 转发按钮
          IconButton(
            onPressed:
                disabled ? null : () => _controller.onFileForwarding(context),
            iconSize: 30,
            icon: Image.asset(
              R.icFileShare,
              color: color,
            ),
          ),
          const Spacer(),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: _categories.length,
      child: Scaffold(
        backgroundColor: AppColors.white,
        appBar: AppBar(
          centerTitle: true,
          // 标题
          title: ExtendedText(
            widget.dispName ?? '',
            style: const TextStyle(
              fontSize: 16,
            ),
            specialTextSpanBuilder: MySpecialTextSpanBuilder(
              showAtBackground: false, size: Size(15.r, 15.r),
            ),
          ),
          // 右侧按钮
          actions: [
            Obx(() {
              var isSelect = _controller.isSelect;
              return TextButton(
                onPressed: () => _controller.selectChanged(!isSelect),
                child: Text(isSelect ? L.chat_contact_cancel.tr : L.select.tr),
              );
            }),
          ],
          // 底部tab
          bottom: TabBar(
            indicatorColor: AppColors.colorFF249ED9,
            labelColor: AppColors.colorFF249ED9,
            unselectedLabelColor: Colors.black,
            onTap: _controller.onTap,
            tabs: [
              Tab(text: _categories[0]),
              Tab(text: _categories[1]),
              Tab(text: _categories[2]),
            ],
          ),
        ),
        body: Column(
          children: [
            const Expanded(child: TabBarView(
              children: [
                PhotoAlbumView(type: MessageType.image),
                PhotoAlbumView(type: MessageType.video),
                FileView(),
              ],
            )),
            Obx(() {
              var isSelect = _controller.isSelect;
              var disabled = _controller.selectMsgDatas.isEmpty;
              return Visibility(
                visible: isSelect,
                child: _buildBottomButton(disabled),
              );
            }),
          ],
        ),
      ),
    );
  }
}
