import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/data/providers/db/database.dart';
import 'package:flutter_metatel/app/widgets/down_load_file_view.dart';
import 'package:flutter_metatel/core/utils/util.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

import '../../../../core/languages/l.dart';
import '../../../../core/values/colors.dart';
import '../../../data/services/chatio_service.dart';
import '../../../widgets/divider_cus.dart';
import '../media_doc_detail_controller.dart';

class FileView extends StatefulWidget {
  const FileView({Key? key}) : super(key: key);

  @override
  State<FileView> createState() => _FileViewState();
}

class _FileViewState extends State<FileView> {
  final MediaDocDetailController _controller = Get.find();

  // void _showDialog(Widget widget) {
  //   showDialog(
  //     barrierDismissible: false,
  //     context: context,
  //     useSafeArea: false,
  //     builder: (_) {
  //       return widget;
  //     },
  //   );
  // }

  /// item点击
  void _onItemTapUp(MessageData msgData, int index) {
    downLoadFileShowDialog(
      parentContext: context,
      barrierDismissible: true,
      event: data2event(
        msgData,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      var listData = _controller.allFileDatas.values.toList();
      var isSelect = _controller.isSelect;

      return ListView.builder(
          itemCount: listData.length,
          itemBuilder: (context, index) {
            var msgData = listData[index];

            return GetBuilder<MediaDocDetailController>(
              id: msgData.msgId,
              builder: (controller) {
                var isChecked = _controller.isChecked(msgData.msgId);

                return FileItem(
                  index: index,
                  msgData: msgData,
                  isSelect: isSelect,
                  isChecked: isChecked,
                  onCheckedChanged: _controller.onCheckedChanged,
                  onTapUp: _onItemTapUp,
                );
              },
            );
          });
    });
  }
}

class FileItem extends StatelessWidget {
  const FileItem({
    Key? key,
    required this.index,
    required this.msgData,
    this.isChecked = false,
    this.isSelect = false,
    this.onCheckedChanged,
    this.onTapUp,
  }) : super(key: key);

  final int index;
  final MessageData msgData;
  final bool isChecked;
  final bool isSelect;
  final ValueChangedCallback<MessageData, bool>? onCheckedChanged;
  final ValueChangedCallback<MessageData, int>? onTapUp;

  /// 点击
  void _onTapUp() {
    if (isSelect) {
      // var path = msgData.filePath ?? '';
      // if (path.isNotEmpty && !File(path).existsSync()) {
      //   onCheckedChanged?.call(msgData, !isChecked);
      // }
      onCheckedChanged?.call(msgData, !isChecked);
    } else {
      onTapUp?.call(msgData, index);
    }
  }

  bool showDown() {
    var path = msgData.filePath ?? '';
    if (path.isNotEmpty && !File(path).existsSync()) {
      return false;
    }
    return true;
  }

  @override
  Widget build(BuildContext context) {
    String fileSize = byteFormat(msgData.fileSize ?? 0);
    var dateTime = DateTime.fromMillisecondsSinceEpoch(msgData.time ?? 0);
    var timeStr = DateFormat("yyyy-MM-dd").format(dateTime);

    String fileImage = '';
    FileCategory category = fileCategory(msgData.fileName ?? '');
    switch (category) {
      case FileCategory.image:
        fileImage = 'assets/images/file_image.png';
        break;
      case FileCategory.audio:
        fileImage = 'assets/images/file_audio.png';
        break;
      case FileCategory.video:
        fileImage = 'assets/images/file_video.png';
        break;
      case FileCategory.document:
        fileImage = 'assets/images/file_doc.png';
        break;
      default:
        fileImage = 'assets/images/file_unknown.png';
        break;
    }

    double padding = 10;

    return SizedBox(
      height: 90,
      child: InkWell(
        onTap: _onTapUp,
        child: Column(
          children: [
            // 主体部分
            Expanded(
              child: Padding(
                padding: EdgeInsets.all(padding),
                child: Row(
                  children: [
                    // 图标
                    Stack(
                      children: [
                        Image.asset(
                          fileImage,
                          width: 60.r,
                          height: 60.r,
                        ),
                        Positioned(
                          child: Visibility(
                            visible: showDown(),
                            child: Container(
                              color: AppColors.colorB2666666,
                              width: 60.r,
                              height: 60.r,
                              child: Center(
                                child: Text(
                                  L.undownloaded.tr,
                                  style: TextStyle(
                                      fontSize: 12.sp,
                                      color: AppColors.colorFF333333),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                    // 间隔
                    const SizedBox(width: 10),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Spacer(),
                          // 文件名
                          Text(
                            msgData.fileName ?? '',
                            overflow: TextOverflow.ellipsis,
                          ),
                          // 文件大小
                          Text(fileSize),
                          // 文件时间
                          Text(timeStr),
                          const Spacer(),
                        ],
                      ),
                    ),
                    // 选中按钮
                    Visibility(
                      visible: isSelect,
                      child: Icon(
                        isChecked
                            ? Icons.check_circle_outline
                            : Icons.radio_button_unchecked_outlined,
                        color: Colors.blue,
                        size: 25,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            // 分割线
            DividerCus(
              indent: padding,
              endIndent: padding,
            ),
          ],
        ),
      ),
    );
  }
}
