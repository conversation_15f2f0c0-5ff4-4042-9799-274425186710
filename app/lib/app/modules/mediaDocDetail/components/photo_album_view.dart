import 'dart:io';
import 'dart:typed_data';

import 'package:extended_image/extended_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/data/enums/enum.dart';
import 'package:flutter_metatel/app/data/providers/db/database.dart';
import 'package:flutter_metatel/app/data/services/chatio_service.dart';
import 'package:flutter_metatel/core/utils/util.dart';
import 'package:flutter_metatel/core/values/colors.dart';
import 'package:flutter_metatel/core/values/config.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../core/languages/l.dart';
import '../../../../r.dart';
import '../../../widgets/chat_widget/photos_video_preview_view.dart';
import '../../../widgets/down_load_view.dart';
import '../media_doc_detail_controller.dart';

class PhotoAlbumView extends StatefulWidget {
  const PhotoAlbumView({Key? key, required this.type}) : super(key: key);

  final MessageType type;

  @override
  State<PhotoAlbumView> createState() => _PhotoAlbumViewState();
}

class _PhotoAlbumViewState extends State<PhotoAlbumView> {
  final MediaDocDetailController _controller = Get.find();

  /// item点击
  void _onItemTapUp(MessageData msgData, int index) {
    if (widget.type == MessageType.image) {
      List<PhotosData> paths = [];
      for (var msgData in _controller.allImageDatas.values) {
        var needDown = false;
        var filePath = appSupporAbsolutePath(msgData.filePath);
        String? path;
        if (filePath != null && filePath.isNotEmpty) {
          path = filePath;
        } else {
          var thumbnailPath = appSupporAbsolutePath(msgData.thumbnailPath);
          if (thumbnailPath?.isNotEmpty ?? false) {
            path = thumbnailPath;
          }
          needDown = true;
        }
        if (path?.isNotEmpty ?? false) {
          paths.add(PhotosData(
              msgId: msgData.msgId,
              path: path,
              sourceFragment: msgData.fileFragment,
              sourcePath: msgData.filePath,
              needDown: needDown,
              fileName: msgData.fileName,
              fileUrl: msgData.fileUrl));
        }
      }

      showDialog(
        barrierDismissible: false,
        context: context,
        useSafeArea: false,
        builder: (_) {
          return PhotosPreviewView(
            messageList: paths,
            initIndex: index,
            weakList: _controller.getImagesCaches(), isOriginalSelected: false,
          );
        },
      );
    } else if (widget.type == MessageType.video) {
      try {
        DownLoadShowDialog(
          barrierDismissible: true,
          context: context,
          event: data2event(msgData),
          body: Container(),
        );
      } catch (e) {
        toast('');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      List<MessageData> listData = [];
      if (widget.type == MessageType.image) {
        listData = _controller.allImageDatas.values.toList();
      } else if (widget.type == MessageType.video) {
        listData = _controller.allVideoDatas.values.toList();
      }

      var isSelect = _controller.isSelect;

      return GridView.builder(
        itemCount: listData.length,
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 4,
          crossAxisSpacing: 5,
          mainAxisSpacing: 5,
        ),
        itemBuilder: (context, index) {
          var msgData = listData[index];

          return GetBuilder<MediaDocDetailController>(
            id: msgData.msgId,
            builder: (controller) {
              var isChecked = _controller.isChecked(msgData.msgId);

              return PhotoAlbumItem(
                index: index,
                msgData: msgData,
                isSelect: isSelect,
                isChecked: isChecked,
                onCheckedChanged: _controller.onCheckedChanged,
                onTapUp: _onItemTapUp,
              );
            },
          );
        },
      );
    });
  }
}

class PhotoAlbumItem extends StatefulWidget {
  const PhotoAlbumItem({
    Key? key,
    required this.index,
    required this.msgData,
    this.isChecked = false,
    this.isSelect = false,
    this.onCheckedChanged,
    this.onTapUp,
  }) : super(key: key);

  final int index;
  final MessageData msgData;
  final bool isChecked;
  final bool isSelect;
  final ValueChangedCallback<MessageData, bool>? onCheckedChanged;
  final ValueChangedCallback<MessageData, int>? onTapUp;

  @override
  State<PhotoAlbumItem> createState() => _PhotoAlbumItemState();
}

class _PhotoAlbumItemState extends State<PhotoAlbumItem> {
  String _imageCacheName = '';
  final MediaDocDetailController _controller = Get.find();

  /// 点击
  void _onTapUp() {
    if (widget.isSelect) {
      // var path = widget.msgData.filePath ?? '';
      // if (path.isNotEmpty && !File(path).existsSync()) {
      //   widget.onCheckedChanged?.call(widget.msgData, !widget.isChecked);
      // }
      widget.onCheckedChanged?.call(widget.msgData, !widget.isChecked);
    } else {
      widget.onTapUp?.call(widget.msgData, widget.index);
    }
  }

  bool showDown() {
    var path = widget.msgData.filePath ?? '';
    if (path.isNotEmpty && !File(path).existsSync()) {
      return false;
    }
    return true;
  }

  Widget imageAsset(String name, BoxConstraints constraints) {
    _controller.addImageCacheName(name);
    return ExtendedImage.asset(
      name,
      fit: BoxFit.cover,
      imageCacheName: name,
      width: constraints.maxWidth,
      height: constraints.maxHeight,
      clearMemoryCacheWhenDispose: true,
      maxBytes: Config.maxImageBytes,
    );
  }

  /// 图片
  Widget _buildImage(
    String? filePath,
    String? fileFragment,
    BoxConstraints constraints,
  ) {
    _controller.addImageCacheName(_imageCacheName);

    Widget image;
    var data = _controller.getImageCache(filePath);
    if (data != null) {
      image = ExtendedImage.memory(
        data,
        fit: BoxFit.cover,
        imageCacheName: _imageCacheName,
        width: constraints.maxWidth,
        height: constraints.maxHeight,
        maxBytes: Config.maxImageBytes,
        loadStateChanged: (state) {
          if (state.extendedImageLoadState == LoadState.failed) {
            return imageAsset(R.icoPicLoadFailed, constraints);
          }
          return null;
        },
      );
    } else {
      image = FutureBuilder(
        future:
            _controller.getImageUnit8ByPath(filePath ?? '', fileFragment ?? ''),
        builder: (_, snapshot) {
          if (snapshot.connectionState == ConnectionState.done) {
            if (snapshot.data != null) {
              var data = snapshot.data as Uint8List;
              return ExtendedImage.memory(
                data,
                fit: BoxFit.cover,
                imageCacheName: _imageCacheName,
                width: constraints.maxWidth,
                height: constraints.maxHeight,
                maxBytes: Config.maxImageBytes,
                loadStateChanged: (state) {
                  if (state.extendedImageLoadState == LoadState.failed) {
                    return imageAsset(R.icoPicLoadFailed, constraints);
                  }
                  return null;
                },
              );
            } else {
              return imageAsset(R.icoPicLoadFailed, constraints);
            }
          } else {
            return imageAsset(R.icImageLoading, constraints);
          }
        },
      );
    }

    return image;
  }

  /// 视频图片
  Widget _buildVideoImage(
    String? filePath,
    String? fileFragment,
    BoxConstraints constraints,
  ) {
    return Stack(
      children: [
        _buildImage(filePath, fileFragment, constraints),
        // 播放按钮图片
        const Align(
          child: Icon(
            Icons.play_arrow,
            color: Colors.white,
            size: 20,
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    var type = MessageType.values[widget.msgData.type ?? 0];

    String? filePath;
    String? fileFragment;
    switch (type) {
      case MessageType.image:
        filePath = widget.msgData.filePath ?? widget.msgData.thumbnailPath;
        fileFragment = widget.msgData.fileFragment;
        break;
      case MessageType.video:
        filePath = widget.msgData.thumbnailPath ?? widget.msgData.thumbnailPath;
        fileFragment = widget.msgData.thumbnailFragment;
        break;
      default:
        break;
    }
    filePath = appSupporAbsolutePath(filePath);
    _imageCacheName = '$filePath-docImage';

    return LayoutBuilder(
      builder: (_, constraints) {
        return GestureDetector(
          onTap: _onTapUp,
          child: Stack(
            children: [
              type == MessageType.image
                  ? _buildImage(filePath, fileFragment, constraints)
                  : _buildVideoImage(filePath, fileFragment, constraints),
              Positioned(
                child: Visibility(
                  visible: showDown(),
                  child: Container(
                    color: AppColors.colorB2666666,
                    child: Center(
                      child: Text(
                        L.undownloaded.tr,
                        style: TextStyle(
                            fontSize: 12.sp, color: AppColors.colorFF333333),
                      ),
                    ),
                  ),
                ),
              ),
              // 选中按钮
              Positioned(
                right: 5,
                top: 5,
                child: Visibility(
                  visible: widget.isSelect,
                  child: Icon(
                    widget.isChecked
                        ? Icons.check_circle_outline
                        : Icons.radio_button_unchecked_outlined,
                    color: Colors.blue,
                    size: 20,
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
