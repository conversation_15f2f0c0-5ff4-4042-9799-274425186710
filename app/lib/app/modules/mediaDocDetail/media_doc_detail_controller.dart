import 'dart:async';
import 'dart:io';
import 'dart:typed_data';

import 'package:extended_image/extended_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/data/enums/enum.dart';
import 'package:flutter_metatel/app/data/providers/db/database.dart';
import 'package:flutter_metatel/app/data/services/chatio_service.dart';
import 'package:flutter_metatel/app/data/services/config_service.dart';
import 'package:flutter_metatel/app/modules/message/message_controller.dart';
import 'package:flutter_metatel/core/utils/util.dart';
import 'package:get/get.dart';

import '../../../core/languages/l.dart';
import '../../../core/utils/events_bus.dart';
import '../../../core/values/config.dart';
import '../../data/events/events.dart';
import '../../data/models/weak_image_bean.dart';
import '../../data/providers/native/chatio/chatio_async.dart';
import '../../data/services/event_service.dart';

enum EMsgType {
  text,
  picture,
  audio,
  video,
  file,
}

enum TabBarType {
  picture,
  video,
  file,
}

typedef ValueChangedCallback<T, V> = void Function(T msgData, V value);

class MediaDocDetailController extends GetxController {
  final allImageDatas = <String, MessageData>{}.obs;
  final allVideoDatas = <String, MessageData>{}.obs;
  final allFileDatas = <String, MessageData>{}.obs;

  /// 选中的数据
  final selectMsgDatas = <String, MessageData>{}.obs;

  /// 是否选择
  final _isSelect = false.obs;

  String _userName = '';
  late AppDatabase _db;

  StreamSubscription? _imageSubscription;
  StreamSubscription? _videoSubscription;
  StreamSubscription? _fileSubscription;
  int _chatType = 0;
  set chatType(int chatType) =>_chatType = chatType;
  set userName(String userName) => _userName = userName;
  String get userID => _userName;

  bool get isSelect => _isSelect.value;

  /// 图片缓存
  final List<WeakImageBean> _imagesCache = [];
  final List<String> _imageCacheNames = [];

  @override
  void onInit() {
    super.onInit();
    _db = Get.find();
  }

  @override
  void onReady() {
    super.onReady();
    // _allFileMessage(MessageType.image);

    // 获取所有图片
    _imageSubscription = _db
        .allFileMessageByType(_userName, MessageType.image.index)
        .watch()
        .listen((msgDatas) {
      allImageDatas.clear();
      _listToMap(allImageDatas, msgDatas);
    });

    // 获取所有视频
    _videoSubscription = _db
        .allFileMessageByType(_userName, MessageType.video.index)
        .watch()
        .listen((msgDatas) {
      allVideoDatas.clear();
      _listToMap(allVideoDatas, msgDatas);
    });

    // 获取所有文件
    _fileSubscription = _db
        .allFileMessageByType(_userName, MessageType.file.index)
        .watch()
        .listen((msgDatas) {
      allFileDatas.clear();
      _listToMap(allFileDatas, msgDatas);
    });
  }

  @override
  void onClose() {
    for (var element in _imageCacheNames) {
      clearMemoryImageCache(element);
    }

    _imageSubscription?.cancel();
    _videoSubscription?.cancel();
    _fileSubscription?.cancel();
    _imagesCache.clear();
    _imageCacheNames.clear();
  }

  /// 标签栏点击
  void onTap(int index) {
    // if (_currIndex == index) return;

    // _currIndex = index;
    // if (index == TabBarType.picture.index) {
    //   _allFileMessage(MessageType.image);
    // } else if (index == TabBarType.video.index) {
    //   _allFileMessage(MessageType.video);
    // } else if (index == TabBarType.file.index) {
    //   _allFileMessage(MessageType.file);
    // }
  }

  /// 选择变化
  void selectChanged(bool value) {
    _isSelect.value = value;

    if (!value) {
      selectMsgDatas.clear();
    }
  }

  /// 是否选中
  bool isChecked(String msgID) {
    return selectMsgDatas.containsKey(msgID);
  }

  /// 文件选中变化
  void onCheckedChanged(MessageData msgData, bool value) {
    update([msgData.msgId]);
    if (value) {
      selectMsgDatas[msgData.msgId] = msgData;
    } else {
      selectMsgDatas.remove(msgData.msgId);
    }
  }

  /// 删除文件
  void onFileDelete() {
    var msgs =<MessageData>{};
    selectMsgDatas.forEach((key, value) {
      msgs.add(value);
      if(_chatType==ChatType.channelChat.index){
        _db.updateOneMessageDelete(MessageStatus.del,key);
      } else {
        _db.deleteMessage(key);
      }

    });
    Get.find<EventBus>()
        .fire(MessageDelEvent(userID, msgs.toList()));
    selectMsgDatas.clear();
    _isSelect.value = false;
  }


  /// 文件转发
  void onFileForwarding(BuildContext context) async {
    var msgDatas = selectMsgDatas.values.toList();
    selectChanged(false);

    List<MessageEvent> events = [];
    for (var element in msgDatas) {
      var path = element.filePath ?? '';
      if (path.isNotEmpty && !File(path).existsSync()) {
        var data = data2event(element);
        if (data != null) {
          events.add(data);
        }
      }
    }
    if (msgDatas.length != events.length) {
      toast(L.undownloaded_source_files_cannot_be_shared.tr);
    }
    if (events.isNotEmpty) {
      forwardMessage(events);
    }
  }

  List<WeakImageBean> getImagesCaches() {
    if (_checkChatObject()) {
      return Get.find<MessageController>().imagesWeakMap;
    }
    return _imagesCache;
  }

  void addImageCacheName(String name) {
    if (!_imageCacheNames.contains(name)) {
      _imageCacheNames.add(name);
    }
  }

  Future<Uint8List?> getImageUnit8ByPath(
    String imagePath,
    String fragment,
  ) async {
    if (_checkChatObject()) {
      return Get.find<MessageController>()
          .getImageUnit8ByPath(imagePath, fragment);
    }

    Uint8List? data = getImageCache(imagePath);
    if (data == null) {
      if (imagePath.endsWith(Config.enc)) {
        data = await ChatioNative.utilFileDecryptToMemory(imagePath, fragment);
      } else {
        File file = File(imagePath);
        data = file.readAsBytesSync();
      }

      if (data.isNotEmpty) {
        _imagesCache.add(WeakImageBean(imagePath, data));
      }
    }

    return data;
  }

  Uint8List? getImageCache(String? path) {
    if (_checkChatObject()) {
      return Get.find<MessageController>().getImageCache(path);
    }

    Uint8List? data;
    for (var element in _imagesCache) {
      if (element.path == path) {
        data = element.data;
        break;
      }
    }
    return data;
  }


  bool _checkChatObject() {
    var currSessionID = Get.find<AppConfigService>().currSessionID;
    if (currSessionID.isNotEmpty && currSessionID == _userName) {
      return true;
    }
    return false;
  }

  void _listToMap(Map<String, MessageData> dst, List<MessageData> src) {
    for (var element in src) {
      dst[element.msgId] = element;
    }
  }
}
