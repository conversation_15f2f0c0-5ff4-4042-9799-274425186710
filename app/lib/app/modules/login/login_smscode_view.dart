// import 'package:flutter/material.dart';
// import 'package:flutter_metatel/core/utils/app_log.dart';
// import 'package:flutter_metatel/core/utils/util.dart';
// import 'package:get/get.dart';
//
// import '../../../core/languages/l.dart';
// import '../../../core/values/colors.dart';
// import '../../widgets/verification/verification_box.dart';
// import '../../widgets/verification/verification_box_item.dart';
// import 'login_controller.dart';
//
// class LoginSmsCodeView extends GetView<LoginController> {
//   const LoginSmsCodeView({Key? key}) : super(key: key);
//
//
//   @override
//   Widget build(BuildContext context) {
//     var phone = Get.arguments['phone'];
//     AppLogger.d('LoginSmsCodeView build$phone');
//     controller.startTime(); // 点
//     return Scaffold(
//         body: ConstrainedBox(
//       constraints:const BoxConstraints.expand(),
//       child: Stack(
//         alignment: Alignment.topLeft, //指定未定位或部分定位widget的对齐方式
//         children: <Widget>[
//           Positioned(
//             child: TextButton(
//               onPressed: () {
//                 Get.back();
//               },
//               child: Image.asset(
//                 'assets/images/appbar_back.png',
//                 width: 9.5,
//               ),
//             ),
//             left: 16,
//             top: 41,
//           ),
//           Padding(
//             padding:const EdgeInsets.only(left: 40, top: 110, right: 40),
//             child: Column(
//               //mainAxisAlignment: MainAxisAlignment.start,
//               crossAxisAlignment: CrossAxisAlignment.start,
//               children: [
//                 Text(
//                   L.please_input_sms_code.tr,
//                   style:const TextStyle(fontSize: 26),
//                 ),
//                 const SizedBox(height: 28),
//                 Text(
//                   L.sms_code_info.trParams({'1s': '$phone'}),
//                   style:const TextStyle(fontSize: 12, color: AppColors.txtColor),
//                 ),
//                 const SizedBox(height: 100),
//                 SizedBox(
//                   height: 45,
//                   child: VerificationBox(
//                     count: 6,
//                     type: VerificationBoxItemType.underline,
//                     onSubmitted: (value) {
//                       showLoadingDialog();
//                       controller.login(value, phone);
//                     },
//                     textStyle:const TextStyle(color: Colors.lightBlue),
//                   ),
//                 ),
//                 const SizedBox(height: 58),
//                 Obx(() => Center(
//                         child: TextButton(
//                       child: Text(
//                         controller.count.value == 0
//                             ? L.reset_resend_after.tr
//                             : L.resend_after
//                                 .trParams({'1s': '${controller.count.value}'}),
//                         //style: const TextStyle(color: ),
//                       ),
//                       onPressed: controller.againSend.value
//                           ? () => controller.reSendCode(phone)
//                           : null,
//                     )))
//               ],
//             ),
//           ),
//         ],
//       ),
//     ));
//   }
// }
