// import 'dart:async';
// import 'dart:convert';
//
// import 'package:flutter_metatel/core/utils/util.dart';
// import 'package:get/get.dart';
//
// import '../../../core/languages/l.dart';
// import '../../../core/utils/app_log.dart';
// import '../../../core/values/config.dart';
// import '../../../routes/pages.dart';
// import '../../data/providers/native/chatio/auth/auth_ffi.dart';
// import '../../data/providers/native/chatio/chatio_async.dart';
// import '../../data/services/chatio_service.dart';
// import '../../data/services/config_service.dart';
//
// class LoginController extends GetxController {
//   //TODO: Implement LoginController
//   late AuthFFi _authFFi;
//   RxInt count = 60.obs;
//   dynamic checked = false.obs;
//   RxBool againSend = false.obs;
//   Timer? _timer;
//
//   @override
//   void onInit() {
//     super.onInit();
//     _authFFi = AuthFFi();
//     AppLogger.d('onInit');
//   }
//
//   @override
//   void dispose() {
//     Get.delete<LoginController>();
//     _timer?.cancel();
//     super.dispose();
//   }
//
//   @override
//   void onClose() {
//     super.onClose();
//     _authFFi.destroy();
//   }
//
//   bool judgeArgument(String phone) {
//     if (!checked.value) {
//       toast(L.please_user_service_agreement.tr);
//       return false;
//     }
//     if (phone.isEmpty) {
//       toast(L.please_input_user_phone_number.tr);
//       return false;
//     }
//     if (!isChinaPhoneLegal(phone)) {
//       toast(L.user_phone_number_type.tr);
//       return false;
//     }
//     return true;
//   }
//
//   void sendCode(String phone) async {
//     if (!judgeArgument(phone)) {
//       return;
//     }
//     var privatekey = await ChatioNative.utilCreatePrivate();
//     if (privatekey == null) {
//       AppLogger.e('privatekey is null');
//       return;
//     }
//     AppLogger.d('privatekey=$privatekey');
//     var isSuccess = await _authFFi.createAuthHandle(Config.nodeUrl,
//         base64.encode(privatekey), Config.node(), phone, Config.deviceInfo);
//     AppLogger.d('isSuccess=$isSuccess');
//     if (isSuccess) {
//       Get.toNamed(Routes.LOGIN_SMS, arguments: {
//         "phone": phone,
//       });
//     } else {
//       toast(L.send_sms_error.tr);
//     }
//   }
//
//   void reSendCode(String phone) async {
//     if (!judgeArgument(phone)) {
//       return;
//     }
//     var privatekey = await ChatioNative.utilCreatePrivate();
//     if (privatekey == null) {
//       return;
//     }
//     againSend.value = false;
//     AppLogger.d('privatekey=$privatekey');
//     var isSuccess = await _authFFi.createAuthHandle(Config.nodeUrl,
//         base64.encode(privatekey), Config.node(), phone, Config.deviceInfo);
//     AppLogger.d('isSuccess=$isSuccess');
//     if (isSuccess) {
//       AppLogger.d('LOGIN_SMS $phone');
//       startTime();
//     } else {
//       toast(L.send_sms_error.tr);
//       againSend.value = true;
//     }
//   }
//
//   void login(String code, String phone) async {
//     var token = await _authFFi.getToken(code);
//     AppLogger.d('login=$token');
//     if (token.isNotEmpty) {
//       AppConfigService configService = Get.find();
//       configService.saveToken(token);
//       var privateKey = await _authFFi.getPrivateKey();
//       if (privateKey.isNotEmpty) {
//         configService.savePrivateKey(privateKey);
//         configService.changeFistAccess(true);
//         configService.savePhone(phone);
//         if (configService.getMySelfDisplayName().isEmpty) {
//           configService.saveMySelfDisplayName(phone);
//         }
//         bool login = await Get.find<ChatioService>().connection();
//         if (!login) {
//           toast(L.login_fail.tr);
//         } else {
//           Get.find<ChatioService>().initPush(token);
//           Get.offAllNamed(Routes.HOME);
//         }
//       }
//     } else {
//       toast(L.code_error.tr);
//     }
//     Get.back();
//   }
//
//   void startTime() {
//     count.value = 60;
//     _timer?.cancel();
//     _timer = Timer.periodic(const Duration(milliseconds: 1000), (timer) {
//       _timer = timer;
//       //AppLogger.d('isSuccess=$count.value');
//       if (count.value >= 1) {
//         count.value -= 1;
//       } else {
//         // 倒计时结束
//         if (!againSend.value) {
//           againSend.value = true;
//         }
//         timer.cancel();
//       }
//     });
//   }
// }
