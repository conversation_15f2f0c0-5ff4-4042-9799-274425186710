// import 'package:flutter/material.dart';
// import 'package:flutter_metatel/core/utils/app_log.dart';
// import 'package:get/get.dart';
//
// import '../../../core/languages/l.dart';
// import '../../data/services/config_service.dart';
// import 'login_controller.dart';
//
// class LoginView extends GetView<LoginController> {
//   const LoginView({Key? key}) : super(key: key);
//
//   @override
//   void didChangeDependencies() {
//     // ignore: must_call_super
//   }
//   @override
//   Widget build(BuildContext context) {
//     FocusNode focusNode = FocusNode(); //密码框焦点
//     AppConfigService configService = Get.find();
//     var phone = configService.getPhone();
//     final myController = TextEditingController(text: phone);
//
//     AppLogger.d('LoginView build');
//     return Scaffold(
//         body: ConstrainedBox(
//       constraints: const BoxConstraints.expand(),
//       child: Stack(
//         alignment: Alignment.topLeft, //指定未定位或部分定位widget的对齐方式
//         children: <Widget>[
//           Positioned(
//             child: Image.asset(
//               'assets/images/appbar_back.png',
//               width: 9.5,
//             ),
//             left: 16,
//             top: 41,
//           ),
//           Positioned(
//             right: 0,
//             child: Image.asset(
//               'assets/images/ic_register.png',
//               width: 152,
//             ),
//           ),
//           Padding(
//             padding: const EdgeInsets.only(left: 40, top: 110, right: 40),
//             child: Column(
//               //mainAxisAlignment: MainAxisAlignment.start,
//               crossAxisAlignment: CrossAxisAlignment.start,
//               children: [
//                 Text(
//                   L.welcome.tr,
//                 ),
//                 const SizedBox(height: 15),
//                 Text(
//                   L.create_you_account.tr,
//                 ),
//                 const SizedBox(height: 100),
//                 TextField(
//                   autofocus: true,
//                   controller: myController,
//                   enabled: phone.isEmpty,
//                   keyboardType: TextInputType.phone,
//                   onEditingComplete: () {
//                     FocusScope.of(context).requestFocus(focusNode); //焦点付给密码输入框
//                   },
//                   decoration: InputDecoration(
//                     hintText: L.please_input_user_phone_number.tr,
//                   ),
//                 ),
//                 const SizedBox(height: 15),
//                 Text(
//                   L.register_info.tr,
//                 ),
//                 const SizedBox(height: 52),
//                 Row(
//                   children: [
//                     Obx(
//                       () {
//                         var checked = controller.checked.value;
//                         return Checkbox(
//                             value: checked,
//                             activeColor: Colors.blue,
//                             onChanged: (value) {
//                               controller.checked.value = value!;
//                             });
//                       },
//                     ),
//                     Text(
//                       L.read_and_agree.tr +
//                           L.user_service_agreement.tr +
//                           L.rivacy_clause.tr,
//                       //'${L.read_and_agree.tr}${L.usepr_service_agreement.tr}',
//                       style: const TextStyle(fontSize: 12, color: Colors.grey),
//                       maxLines: 1,
//                       overflow: TextOverflow.ellipsis,
//                     ),
//                   ],
//                 ),
//                 const Spacer(
//                   flex: 2,
//                 ),
//                 MaterialButton(
//                   minWidth: double.infinity,
//                   height: 40,
//                   elevation: 10,
//                   shape: RoundedRectangleBorder(
//                     borderRadius: BorderRadius.circular(20.0),
//                   ),
//                   color: Colors.blue,
//                   onPressed: () {
//                     FocusScope.of(context).requestFocus(focusNode); //指定为空白焦点
//                     controller.sendCode(myController.value.text);
//                   },
//                   child: Text(
//                     L.send_sms_verification_code.tr,
//                     style: const TextStyle(
//                       color: Colors.white,
//                     ),
//                   ),
//                 ),
//                 const Spacer(
//                   flex: 3,
//                 ),
//               ],
//             ),
//           ),
//         ],
//       ),
//     ));
//   }
// }
