import 'package:flutter_metatel/app/data/events/events.dart';
import 'package:flutter_metatel/app/data/providers/db/database.dart';
import 'package:flutter_metatel/core/languages/l.dart';
import 'package:flutter_metatel/core/utils/events_bus.dart';
import 'package:flutter_web3/app/core/utils/util.dart';
import 'package:get/get.dart';

import '../../../core/task/message_task.dart';
import '../../../core/task/session_task.dart';
import '../../../core/values/config.dart';
import '../../data/services/config_service.dart';

class OfficialDetailController extends GetxController{
  /// 消息免打扰变化
  void msgMuteChange(bool value) async{
    Get.find<AppConfigService>()
        .saveUserMessageSilenceState(Config.officialAccount, value);
    await Get.find<AppDatabase>().updateSessionSilence(value,Config.officialAccount);
    Get.find<EventBus>().fire(UpdateMuteChangeEvent(Config.officialAccount));

  }

  void clearChatHistory(bool check) async {
    var messageData = await Get.find<AppDatabase>()
        .oneRecentMessage(Config.officialAccount)
        .getSingleOrNull();
    if (messageData != null) {
      double time = messageData.updateTime ?? 0.0;
      time = time == 0 ? messageData.createTime ?? 0.0 : time;
      Get.find<AppConfigService>().saveOfficialMsgTimeRecent(time);
      SessionTask.clear(Config.officialAccount);
      deleteMessageAndFile(Config.officialAccount,clearTop: check).then((value) {
        Get.find<EventBus>().fire(ClearChatHistoryEvent(Config.officialAccount));
        toast(L.chat_history_cleared_successfully.tr);
      });
    }
  }
}