import 'package:extended_text/extended_text.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/data/enums/enum.dart';
import 'package:flutter_metatel/app/modules/official/official_detail_controller.dart';
import 'package:flutter_metatel/app/widgets/mavatar_circle_avatar.dart';
import 'package:flutter_metatel/core/languages/l.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../core/utils/util.dart';
import '../../../core/values/colors.dart';
import '../../../core/values/config.dart';
import '../../../r.dart';
import '../../data/services/config_service.dart';
import '../../widgets/at_widget/my_special_text_span_builder.dart';
import '../../widgets/divider_cus.dart';
import '../mediaDocDetail/media_doc_detail_page.dart';

class OfficialDetailPage extends StatefulWidget {
  const OfficialDetailPage({
    super.key,
  });

  @override
  createState() => _OfficialDetailPageState();
}

class _OfficialDetailPageState extends State<OfficialDetailPage> {
  final OfficialDetailController _controller =
      Get.put(OfficialDetailController());
  late Color _backgroundColor;
  bool _msgMute = false;

  @override
  void initState() {
    super.initState();
    _backgroundColor = AppColors.backgroundGray;
    _msgMute = Get.find<AppConfigService>()
        .getUserMessageSilenceState(Config.officialAccount);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: _backgroundColor,
      appBar: AppBar(
        backgroundColor: _backgroundColor,
        title: ExtendedText(
          getOfficeDisplayName(),
          specialTextSpanBuilder: MySpecialTextSpanBuilder(
            showAtBackground: false,
            size: Size(15.r, 15.r),
          ),
        ),
        centerTitle: true,
      ),
      body: Container(
        color: _backgroundColor,
        child: ListView(
          children: [
            const DividerCus(),
            _buildAvatarButton(),
            const DividerCus(),
            Container(
              color: Colors.white,
              padding: const EdgeInsets.only(
                      top: 10, bottom: 10, left: 16, right: 16)
                  .r,
              margin: const EdgeInsets.only(bottom: 10).r,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    L.introduce.tr,
                    style: TextStyle(
                      color: Colors.black,
                      fontSize: 16.sp,
                    ),
                  ),
                  const SizedBox(height: 10),
                  Flexible(
                    child: Text(
                      L.official_introduce.tr,
                      maxLines: 4,
                      overflow: TextOverflow.ellipsis,
                      style: TextStyle(
                        color: const Color(0xff999999),
                        fontSize: 12.sp,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            _buildSet(),
          ],
        ),
      ),
    );
  }

  /// 构建设置部分
  Widget _buildSet() {
    return Container(
      color: Colors.white,
      padding: const EdgeInsets.only(top: 10).r,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 消息免打扰
          _buildSetRow(
            L.mute.tr,
            onTap: () {},
            right: StatefulBuilder(
              builder: (context, setState) {
                return CupertinoSwitch(
                  value: _msgMute,
                  onChanged: (value) {
                    setState(() {
                      _msgMute = value;
                      _controller.msgMuteChange(value);
                    });
                  },
                );
              },
            ),
          ),
          const DividerCus(),
          // 媒体和文档
          _buildSetRow(
            L.media_links_and_docs.tr,
            right: const Icon(Icons.chevron_right, size: 20),
            onTap: () {
              Get.to(MediaDocDetailPage(
                userName: Config.officialAccount,
                dispName: getOfficeDisplayName(),
                chatType: ChatType.channelChat.index,
              ));
            },
          ),
          Container(
            height: 10.h,
            color: AppColors.backgroundGray,
          ),
          // 清除聊天记录
          _buildSetRow(
            L.clear_dialog.tr,
            right: const Icon(Icons.chevron_right, size: 20),
            onTap: () {
              _onShowModalBottomSheet(L.clear_dialog.tr, checkBox:true,onSure: (check) {
                _controller.clearChatHistory(check);
              });
            },
          ),
        ],
      ),
    );
  }

  /// 构建头像、操作按钮部分
  Widget _buildAvatarButton() {
    return SizedBox(
      child: Padding(
        padding:
            const EdgeInsets.only(left: 16, right: 16, top: 20, bottom: 30).r,
        child: Column(
          children: [
            // 头像
            MAvatarCircle(
              diameter: 100,
              chatType: ChatType.officialChat,
            ),
            // 间隔
            SizedBox(height: 11.h),
            // 群标题部分
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                // 标题
                Flexible(
                  child: ExtendedText(
                    getOfficeDisplayName(),
                    overflow: TextOverflow.ellipsis,
                    style: TextStyle(
                      fontSize: 20.sp,
                      color: Colors.black,
                      fontWeight: FontWeight.bold,
                    ),
                    specialTextSpanBuilder: MySpecialTextSpanBuilder(
                      showAtBackground: false,
                      size: Size(15.r, 15.r)
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }


  Widget _buildSetRow(String text, {Widget? right, GestureTapCallback? onTap}) {
    Rx<Color> backColor = AppColors.transparent.obs;
    return Obx(() => Material(
        color: backColor.value,
        child: InkWell(
          radius: 1,
          onTap: () {
            ItemClickFunction(startFunction: () {
              backColor.value = AppColors.colorFFF2F2F2;
            }, endFunction: () {
              if (onTap != null) {
                onTap.call();
              }
              backColor.value = AppColors.white;
            });
          },
          child: Container(
            padding: const EdgeInsets.only(
              left: 16,
              right: 16,
            ).r,
            height: 45.h,
            child: Row(
              children: [
                Text(
                  text,
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: Colors.black,
                  ),
                ),
                const Spacer(),
                right ?? const Spacer(),
              ],
            ),
          ),
        )));
  }

  void _onShowModalBottomSheet(String sureText, {BoolCallBack? onSure,bool checkBox=false,String? checkBoxTitle}) {
    checkBoxTitle??=L.also_clear_pinned_messages.tr;
    bool check=false;
    showBottomDialogCommonWithCancel(
      context,
      widgets: [
        getBottomSheetItemSimple(
          context,
          sureText,
          radius: 12,
          textColor: Colors.red,
          itemCallBack: () {
            onSure?.call(check);
          },
        ),
        Visibility(
          visible:checkBox,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              StatefulBuilder(
                builder: (_, setState) {
                  return Checkbox(
                    value: check,
                    onChanged: (value) {
                      setState(() {
                        check = value ?? false;
                      });
                    },
                  );
                },
              ),
              Text(
                checkBoxTitle,
                style: TextStyle(
                  fontSize: 12.sp,
                  color: const Color(0xff525252),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
