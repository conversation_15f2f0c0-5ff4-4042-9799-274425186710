import 'package:flutter/material.dart';
import 'package:flutter_metatel/core/utils/util.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../core/languages/l.dart';
import '../../../core/values/colors.dart';
import '../../../core/values/config.dart';
import '../../../r.dart';
import '../../../routes/pages.dart';
import '../../widgets/mavatar_circle_avatar.dart';
import 'secret_universe_controller.dart';

class SecretUniversePage extends StatefulWidget {
  const SecretUniversePage({Key? key}) : super(key: key);

  @override
  State<SecretUniversePage> createState() => _SecretUniversePageState();
}

class _SecretUniversePageState extends State<SecretUniversePage> {
  final SecretUniverseController _controller =
      Get.put(SecretUniverseController(), permanent: true);

  @override
  void dispose() {
    Get.delete<SecretUniverseController>();
    super.dispose();
  }

  void onNotOpen() {
    toast(L.not_yet_develop.tr);
  }

  Widget _buildAppItem(
    String assetImage,
    String text, {
    GestureTapCallback? onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Image.asset(
            assetImage,
            width: 49.w,
            height: 49.h,
          ),
          SizedBox(height: 6.5.h),
          Text(
            text,
            style: TextStyle(
              color: AppColors.colorFF333333,
              fontSize: 12.sp,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildApp() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        _buildAppItem(
          R.icoTrend,
          L.trend.tr,
          onTap: () => Get.toNamed(Routes.BrowserView),
        ),
        Opacity(
          opacity: 0.0,
          child: _buildAppItem(
            'assets/images/market.png',
            L.market.tr,
            // onTap: onNotOpen,
          ),
        ),
        Opacity(
          opacity: 0.0,
          child: _buildAppItem(
            'assets/images/crowd_creation.png',
            L.crowd_creation.tr,
            // onTap: onNotOpen,
          ),
        ),
        Opacity(
          opacity: 0.0,
          child:
              _buildAppItem('assets/images/universe_scan.png', L.main_scan.tr),
        ),
      ],
    );
  }

  Widget _buildCommunityItem(
    String imagePath,
    String text,
    String describe, {
    GestureTapCallback? onTap,
  }) {
    return Container(
      padding: EdgeInsets.only(top: 10.h),
      height: 89.h,
      child: GestureDetector(
        onTap: onTap,
        child: Container(
          padding: EdgeInsets.only(left: 15.w, right: 15.w),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(15),
            color: Colors.white,
          ),
          child: Row(
            children: [
              // 头像
              MAvatarCircle(
                diameter: 49,
                text: text,
                imagePath: imagePath,
              ),
              SizedBox(width: 6.w),
              // 名称/描述部分
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      text,
                      overflow: TextOverflow.ellipsis,
                      style: TextStyle(
                        color: AppColors.colorFF333333,
                        fontSize: 14.sp,
                      ),
                    ),
                    SizedBox(height: 9.h),
                    Text(
                      describe,
                      overflow: TextOverflow.ellipsis,
                      style: TextStyle(
                        color: AppColors.colorFF666666,
                        fontSize: 12.sp,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCommunity() {
    return Column(
      children: [
        _buildCommunityItem(
          'assets/images/war_mech.png',
          'CyberPanda-DAO',
          '赛博熊猫官方DAO，密世界首个Web3社区',
          onTap: onNotOpen,
        ),
        _buildCommunityItem(
          'assets/images/web3.png',
          'Web3志愿者',
          '中国Web3自治委员会官方社区，人员招募中...',
          onTap: onNotOpen,
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.contactColor,
      appBar: AppBar(
        backgroundColor: AppColors.contactColor,
        centerTitle: true,
        // 标题
        title: Text(
          L.main_browser.tr,
          style: TextStyle(
            fontSize: 16.sp,
            color: Colors.black,
            fontWeight: FontWeight.bold,
          ),
        ),
        // 底部搜索框
        bottom: PreferredSize(
          child: Padding(
            padding: EdgeInsets.only(left: 16.w, right: 16.w, bottom: 10.h),
            child: SizedBox(
              height: 36.h,
              child: TextField(
                onTap: () {
                  // add ly
                  int searchType = SearchResultType.chat |
                      SearchResultType.function |
                      SearchResultType.group |
                      SearchResultType.contactor;
                  Get.toNamed(Routes.SEARCH, arguments: {'type': searchType});
                },
                readOnly: true,
                textAlign: TextAlign.start,
                textAlignVertical: TextAlignVertical.center,
                decoration: InputDecoration(
                  hintText: L.searbar_hint_search.tr,
                  hintStyle: const TextStyle(color: AppColors.colorFF808080),
                  // 设置后，提升文本居中
                  contentPadding: EdgeInsets.zero,
                  prefixIcon: const Icon(
                    Icons.search,
                    color: Color.fromARGB(255, 89, 90, 90),
                  ),
                  filled: true,
                  fillColor: Colors.white,
                  border: const OutlineInputBorder(
                    borderSide: BorderSide.none,
                    borderRadius: BorderRadius.all(Radius.circular(25)),
                  ),
                ),
              ),
            ),
          ),
          preferredSize: Size(double.infinity, 50.h),
        ),
      ),
      body: Padding(
        padding: EdgeInsets.only(left: 16.w, right: 16.w, top: 9.h),
        child: Column(
          children: [
            // 应用部分
            SizedBox(
              height: 115.h,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    L.application.tr,
                    style: TextStyle(
                      color: AppColors.colorFF323233,
                      fontSize: 14.sp,
                    ),
                  ),
                  // 间隔
                  SizedBox(height: 10.h),
                  Expanded(
                    child: Container(
                      padding: EdgeInsets.only(left: 16.w, right: 16.w),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(15),
                        color: Colors.white,
                      ),
                      child: _buildApp(),
                    ),
                  ),
                ],
              ),
            ),
            // 间隔
            SizedBox(height: 20.h),
            // 社区部分
            Expanded(
              child: Visibility(
                visible: false,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'DAO社区',
                      style: TextStyle(
                        color: AppColors.colorFF323233,
                        fontSize: 14.sp,
                      ),
                    ),
                    Expanded(
                      child: _buildCommunity(),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
