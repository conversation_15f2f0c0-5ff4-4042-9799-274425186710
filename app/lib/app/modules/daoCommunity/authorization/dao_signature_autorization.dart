import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/data/enums/enum.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_web3/app/core/language/s.dart';
import 'package:flutter_web3/app/core/values/colors.dart';
import 'package:flutter_web3/app/data/models/token_model.dart';
import 'package:flutter_web3/app/db/database.dart';
import 'package:flutter_web3/app/modules/wallet/ethereum/wallet_controller.dart';
import 'package:flutter_web3/app/widgets/ic_widget.dart';
import 'package:get/get.dart';

import '../../../widgets/mavatar_circle_avatar.dart';
import '../../../widgets/mdao_circle_avatar.dart';


class DaoSignatureAutorization extends StatelessWidget {
  const DaoSignatureAutorization({super.key, this.chainId, this.daoName, this.imagePath, this.tokenAddress, this.token});
  final int? chainId;
  final String? daoName;
  final String? imagePath; /// 自定义头像
  final String? tokenAddress;
  final TokenDataModel? token;
  
  _onSignature() async {
    Get.back(result: 'ok');
    // var contr = Get.find<WalletController>();
    // String address = contr.wallet.value.address ?? '';

    // String url = _dapp?.url ?? widget.url?.origin ?? '';
    // String? name = _dapp?.title;
    // String? iconUrl = _dapp?.iconUrl;

    // List? data;
    // try {
    //   data = await Get.find<WalletDatabase>()
    //       .oneDappConnetByUrl(url, netUuid, address)
    //       .get();
    // } catch (e) {
    //   walletLog(e.toString());
    // }
    // if (data?.isEmpty ?? true) {
    //   Get.find<WalletDatabase>().insertOrUpdateDappConnectData(
    //     DappConnectCompanion.insert(
    //       uuid: uuid(),
    //       url: url,
    //       iconUrl: ofNotNull(iconUrl),
    //       name: ofNotNull(name),
    //       netUuid: netUuid,
    //       walletAddress: address,
    //       createTime:
    //           ofNotNull(DateTime.now().millisecondsSinceEpoch.toDouble()),
    //       updateTime:
    //           ofNotNull(DateTime.now().millisecondsSinceEpoch.toDouble()),
    //     ),
    //   );
    // }
  }

  Widget _buildItem(String text, Widget child, {double? height}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          text,
          style: TextStyle(
            fontSize: 16.sp,
          ),
        ),
        SizedBox(height: 10.h),
        Container(
          height: height,
          width: double.infinity,
          alignment: Alignment.centerLeft,
          padding: EdgeInsets.symmetric(
              horizontal: 10.w, vertical: height == null ? 15.r : 0),
          decoration: BoxDecoration(
            color: WalletColors.colorFFF8F8F8,
            borderRadius: BorderRadius.all(Radius.circular(10.r)),
          ),
          child: child,
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    double height = Get.height * 0.776;
    var contr = Get.find<WalletController>();
    String? chainIcon;
    String? chainName;
    if (chainId != null) {
      NetworkInfoData? network;
      try {
        network = contr.allListNet.firstWhereOrNull((network) => network.chainid == chainId);
        if(network != null){
          chainIcon = network.chainLogo;
          chainName = network.name;
        }
      } catch (e) {
        
      }            
    }
    return Container(
      height: height,
      padding: EdgeInsets.symmetric(horizontal: 24.5.r),
      child: ListView(
        physics: const BouncingScrollPhysics(),
        children: [
          SizedBox(height: 25.r),
          // 标题部分
          Text(
            S.current.signature_auth_title,
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 18.r),
          // 授权对象部分
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              ClipOval(
                child: SizedBox(
                  width: 33.r,
                  height: 33.r,
                  child: imagePath != null && imagePath!.isNotEmpty
                    ? CachedNetworkImage(
                        imageUrl: imagePath!,
                        width: 33.r,  
                        height: 33.r, 
                        fit: BoxFit.cover,
                      )
                    : token != null 
                      ? IcWidget(
                          diameter: 33,
                          isNet: false,
                          isMain: token?.isMain ?? false,
                          chainid: token?.chainId,
                          filePath: token?.image,
                          symbol: token?.symbol,
                          uuid: token?.uuid ?? '',
                        )
                      : tokenAddress!=null 
                        ? MDaoCircleAvatar(diameter: 33.r, tokenAddress: tokenAddress!)
                        : MAvatarCircle(
                            diameter: 33.r,
                            imagePath: imagePath,
                            chatType: ChatType.channelChat,
                            textStyle: TextStyle(
                              color: Colors.white,
                              fontSize: 22.sp,
                              fontWeight: FontWeight.normal,
                            ),
                          ),
                ),
              ),
              SizedBox(width: 9.r),
              Text(
                daoName ?? "",
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style: TextStyle(
                  fontSize: 15.sp,
                ),
              ),
            ],
          ),
          SizedBox(height: 24.r),
          // 网络
          _buildItem(
            height: 47.5.r,
            S.current.connect_network,
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                // IcWidget(
                //   isNet: true,
                //   diameter: 33,
                //   // chainid: networkInfoData.chainid,
                //   filePath: chainPath,
                // ),
                Container(
                  width: 33.r,
                  height: 33.r,
                  decoration: BoxDecoration(
                    color: WalletColors.colorBlack,
                    borderRadius: BorderRadius.circular(33.r/2),
                    border: Border.all(
                      color: WalletColors.colorWhite,
                      width: 1.r,
                    ),
                  ),
                  alignment: Alignment.center,
                  child: IcWidget(diameter:33.r,isNet: false,filePath: chainIcon,),
                ),
                SizedBox(width: 10.h),
                Expanded(
                    child: Text(
                  chainName ?? "",
                  style: TextStyle(
                    fontSize: 14.sp,
                  ),
                )),
                // if (canSwitch)
                //   GestureDetector(
                //     onTap: () {
                //       if (widget.canSwitchNetwork) {
                //         showEthNetWorkListView(context);
                //       }
                //     },
                //     child: Icon(
                //       Icons.keyboard_arrow_down,
                //       size: 25.r,
                //       color: WalletColors.colorBlack,
                //     ),
                //   ),
              ],
            ),
          ),
          SizedBox(height: 24.h),
          // 地址
          _buildItem(
            S.current.authorized_address,
            height: 47.5.r,
            Row(
              children: [
                Obx(
                  () => Expanded(
                    child: Text(
                      contr.wallet.value.address ?? '',
                      style: TextStyle(
                        fontSize: 13.sp,
                      ),
                    ),
                  )
                ),
                // Icon(
                //   Icons.keyboard_arrow_down,
                //   size: 25.r,
                //   color: WalletColors.colorBlack,
                // ),
              ],
            ),
          ),
          SizedBox(height: 20.h),
          // 授权提示部分
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                S.current.authorized_hint_1,
                style: TextStyle(
                  fontSize: 12.sp,
                  color: WalletColors.colorFFBBBBBB,
                ),
              ),
              Text(
                S.current.authorized_hint_2,
                style: TextStyle(
                  fontSize: 12.sp,
                  color: WalletColors.colorFFBBBBBB,
                ),
              ),
              Text(
                S.current.authorized_hint_3,
                style: TextStyle(
                  fontSize: 12.sp,
                  color: WalletColors.colorFFBBBBBB,
                ),
              ),
            ],
          ),
          SizedBox(height: 39.r),
          // 按钮部分
          ElevatedButton(
            onPressed: () {
              _onSignature();
            },
            child: Text(
              S.current.signature,
              style: TextStyle(
                fontSize: 16.sp,
                color: WalletColors.colorWhite,
              ),
            ),
          ),
          SizedBox(height: 18.r),
          Center(
            child: GestureDetector(
              onTap: () => Get.back(),
              child: Text(
                S.current.cancel,
                style: TextStyle(
                  fontSize: 16.sp,
                  color: WalletColors.colorFF249ED9,
                ),
              ),
            ),
          ),
          SizedBox(height: 15.h),
        ],
      ),
    );
  }
}