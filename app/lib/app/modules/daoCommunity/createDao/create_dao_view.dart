import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_web3/app/widgets/ic_widget.dart';
import 'package:get/get.dart';

import '../../../../core/languages/l.dart';
import '../../../../core/utils/textInputFormatter/decimal_text_input_formatter.dart';
import '../../../../core/values/colors.dart';
import 'create_dao_controller.dart';

class CreateDaoView extends GetView<CreateDaoController> {
  const CreateDaoView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(L.create_dao.tr),
        centerTitle: true,
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    return GestureDetector(
      onTap: () {
        FocusManager.instance.primaryFocus?.unfocus();
      },
      child: Container(
        width: 1.sw,
        height: 1.sh,
        color: Colors.white,
        child: SingleChildScrollView(
          physics: const BouncingScrollPhysics(),
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 30.r),
            child: Form(
              key: controller.formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(height: 30.h),
                  Center(
                    child: IcWidget(
                      diameter: 100,
                      isNet: false,
                      isMain: controller.token.isMain,
                      chainid: controller.token.chainId,
                      filePath: controller.token.image,
                      symbol: controller.token.symbol,
                      uuid: controller.token.uuid ?? '',
                    ),
                  ),
                  SizedBox(height: 40.r),

                  /// 代币名称
                  _buildLabel(L.name_of_token.tr),
                  TextFormField(
                    enabled: false,
                    initialValue: controller.token.symbol,
                    decoration: _buildInputDecoration(L.name_of_token.tr),
                  ),
                  SizedBox(height: 23.r),

                  /// 合约地址
                  _buildLabel(L.contract_address_1_cap.tr),
                  TextFormField(
                    enabled: false,
                    ignorePointers: false,
                    initialValue: controller.token.address,
                    decoration:
                        _buildInputDecoration(L.contract_address_1_cap.tr),
                  ),
                  SizedBox(height: 23.r),

                  /// 代币数量最低门槛
                  _buildLabel(L.minimum_number_of_token.tr),
                  TextFormField(
                    controller: controller.minNumberOfTokenEditingController,
                    maxLength: 10,
                    textInputAction: TextInputAction.next,
                    keyboardType: TextInputType.numberWithOptions(decimal: true),
                    inputFormatters: [
                      DecimalTextInputFormatter(),
                    ],
                    decoration: _buildInputDecoration(
                      "0",
                      counterText: '',
                    ),
                    validator: controller.minNumberOfTokenValidator,
                  ),
                  SizedBox(height: 23.r),

                  /// 社群简介
                  _buildLabel(L.introduction_of_dao.tr),
                  TextFormField(
                    controller: controller.introDaoEditingController,
                    textInputAction: TextInputAction.done,
                    maxLines: 4,
                    maxLength: 200,
                    decoration:
                        _buildInputDecoration(L.introduction_of_dao_hint.tr),
                    // validator: controller.titleValidator,
                  ),
                  SizedBox(height: 13.r),
                  Text(
                    L.create_dao_intro.tr,
                    style: TextStyle(
                      color: AppColors.colorFF7F7F7F,
                      fontSize: 12.sp,
                    ),
                  ),
                  SizedBox(height: 34.r),
                  SizedBox(
                    width: 1.sw,
                    child: ElevatedButton(
                      onPressed: controller.onConfirmTap,
                      child: Text(L.confirm.tr),
                    ),
                  ),
                  SizedBox(height: 50.r),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildLabel(String text) {
    return Text(
      text,
      style: TextStyle(
        fontSize: 14.sp,
        color: Colors.black,
      ),
    );
  }

  InputDecoration _buildInputDecoration(String hintText,
      {Widget? suffix, String? counterText}) {
    return InputDecoration(
      hintText: hintText,
      hintStyle: TextStyle(
        fontSize: 12.sp,
        fontWeight: FontWeight.w300,
        color: AppColors.colorFFBBBBBB,
      ),
      counterText: counterText,
      contentPadding: EdgeInsets.symmetric(horizontal: 5.r, vertical: 10.r),
      enabledBorder: UnderlineInputBorder(
        borderSide: BorderSide(color: AppColors.colorFFCBCBCB),
      ),
      suffix: suffix,
    );
  }
}
