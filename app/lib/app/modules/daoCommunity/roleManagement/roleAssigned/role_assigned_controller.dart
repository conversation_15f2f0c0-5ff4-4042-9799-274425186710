import 'package:get/get.dart';

import '../../../../../core/languages/l.dart';
import '../../../../../core/utils/app_log.dart';
import '../../../../../core/utils/util.dart';
import '../../../../data/providers/api/channel.dart';
import '../../../../data/providers/db/database.dart';
import '../../../../data/services/channel_service.dart';
import '../../../group/details/dao_details_controller.dart';
import '../../../group/details/group_details_base_controller.dart';
import '../createEditRole/create_edit_role_controller.dart';

class RoleAssignedController extends GetxController {
  final _controller = Get.find<GroupDetailsBaseController>() as DaoDetailsController;
  final _createEditRoleController = Get.find<CreateEditRoleController>();

  RxBool isLoading = false.obs;

  /// 选中的成员Id列表
  RxList<String> selectedIds = <String>[].obs;

  /// 群成员列表
  List<ContactData> members = [];
  List<ContactData> currentMembers = [];
  List<ContactData> otherMembers = [];

  /// 搜索结果
  RxList<ContactData> searchResults = <ContactData>[].obs;   
  RxBool isSearching = false.obs;

  @override
  void onInit() {
    selectedIds.addAll(Get.arguments['selected'] ?? []);
        
    /// 获取群内用户成员列表
    _getMembers();
    if(_createEditRoleController.isManagerTag()){
      _getDaoManagers();
    } else {
      /// 获取当前角色已分配的成员列表
      _getDaoRoleMembers();
    }    
    super.onInit();
  }

  void _getMembers() {
    for(var element in _controller.rxMemberDatas.values) {
      /// 当标签为manager时，选择列表内不加入群主
      if(_createEditRoleController.isManagerTag() && _controller.rxGroupInfoData.value.owner == element.username) {
        continue;
      }
      MemberInfo info = _controller.getMemberData(element.username);
      members.add(ContactData(
        id: 0,
        username: element.username,
        displayname: info.nickname,
        localname: info.nickname,
        avatarPath: appSupporAbsolutePath(info.avatarPath),
      ));
    }
  }

  void _getDaoRoleMembers() async {
    try {
      /// 从接口获取当前该角色已分配的成员列表
      isLoading.value = true;      
      var channelId = _controller.rxGroupInfoData.value.channelId;
      var result = await getDaoRoleMembersRequest(channelId, _createEditRoleController.role!.tagId!);      
      isLoading.value = false;
      List<String> currentIds = [];
      if (result != null && result.isNotEmpty) {
        currentIds = result.map((e) => e.username ?? '').toList();
      };
      currentMembers.addAll(members.where((member) => currentIds.contains(member.username)));
      otherMembers.addAll(members.where((member) => !currentIds.contains(member.username)));
      /// 设置已选中成员
      if(selectedIds.isNotEmpty) return;
      selectedIds.addAll(currentIds);
    } catch (e) {
      AppLogger.d("getDaoRoleMembers error=$e");
      isLoading.value = false;
    }
  }

  void _getDaoManagers() {
    try {
      /// 获取当前该角色的管理者
      var adminDatas = _controller.rxAdminDatas.values.toList();
      // if(adminDatas.isEmpty) return;
      List<String> adminIds = adminDatas.map((e) => e.username).toList();
      currentMembers.addAll(members.where((member) => adminIds.contains(member.username)));
      otherMembers.addAll(members.where((member) => !adminIds.contains(member.username)));
      /// 设置已选中成员
      if(selectedIds.isNotEmpty) return;
      selectedIds.addAll(adminIds);
    } catch (e) {
      AppLogger.d("getDaoRoleMembers error=$e");
    }
  }

  void onSaveTap() {
    /// 最多只能选择5个管理员
    if(_createEditRoleController.isManagerTag() && selectedIds.length > 5) {
      toast(L.maximum_managers_hint.tr);
      return;
    }
    /// 筛出需要被删除的成员
    List<String> deleteIds = [];
    for (var member in currentMembers) {
      if (!selectedIds.contains(member.username)) {
        deleteIds.add(member.username);
      }
    }
    /// Manager标签，需要新添加的manager
    List<String> addIds = [];
    if(_createEditRoleController.isManagerTag()){
      for(var selectedId in selectedIds) {
        if(currentMembers.where((element) => element.username == selectedId).isEmpty) {
          addIds.add(selectedId);
        }
      }
    }    
    Get.back(
      result: {
        "selectedIds": selectedIds,
        "deleteIds": deleteIds,
        "addIds": addIds,
      }
    );
  }

  void onItemTap(String username) {
    if (selectedIds.contains(username)) {
      selectedIds.removeWhere((id) => id == username);
    } else {
      selectedIds.add(username);
    }
  }

  /// 文本变化监听
  void onSearchBarChanged(String? text) {
    if(members.isEmpty) return;
    if (text?.isNotEmpty ?? false) {
      isSearching.value = true;
      searchResults.value = _searchMemberDatas(text) ?? [];
    } else {
      isSearching.value = false;
      searchResults.value = [];
    }
  }

  List<ContactData>? _searchMemberDatas(String? data) {
    if (data == null || data.isEmpty || data.trim().isEmpty) return null;    
    List<ContactData> results = [];
    members.forEach((element) {
      if (_strContains(element.displayname ?? '', data)) {
        results.add(element);
      }
    });
    return results;
  }

  bool _strContains(String src, String key) {
    var srcLow = src.toLowerCase();
    var keyLow = key.toLowerCase();
    return srcLow.contains(keyLow);
  }
}