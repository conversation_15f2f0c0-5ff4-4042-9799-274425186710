import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/modules/daoCommunity/roleManagement/roleAssigned/role_assigned_controller.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../../core/languages/l.dart';
import '../../../../../core/values/colors.dart';
import '../../../../data/providers/db/database.dart';
import '../../widgets/action_selectable_tile.dart';

class RoleAssignedView extends GetView<RoleAssignedController> {
  const RoleAssignedView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          L.role_assigned.tr,
        ),
        centerTitle: true,
        actions: [
          TextButton(
            onPressed: controller.onSaveTap,
            child: Text(
              L.save.tr,
              style: TextStyle(
                color: AppColors.primaryBgColor1,
                fontSize: 13.sp,
              ),
            ),
          ),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    return GestureDetector(
      onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
      child: Container(
        width: 1.sw,
        height: 1.sh - kToolbarHeight,
        color: AppColors.white,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: 18.r),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 25.r),
              child: _buildSearchBar(),
            ),
            SizedBox(height: 20.r),
            Obx(
              ()=> Expanded(
                child: controller.isLoading.value 
                  ? SizedBox.shrink()
                  : _buildList(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 搜索框
  Widget _buildSearchBar() {
    return SizedBox(
      height: 37.r,
      child: TextField(
        autofocus: false,
        textAlign: TextAlign.start,
        style: TextStyle(fontSize: 12.sp),
        textAlignVertical: TextAlignVertical.center,
        onChanged: (text) => controller.onSearchBarChanged(text),
        maxLength: 200,
        decoration: InputDecoration(
          hintText: L.search_contact.tr,
          hintStyle: TextStyle(
            color: AppColors.colorFF7F7F7F,
            fontSize: 12.sp,
            fontWeight: FontWeight.w400,
          ),
          // 设置后，提升文本居中
          contentPadding: EdgeInsets.zero,
          prefixIcon: Icon(
            Icons.search,
            color: AppColors.colorFF59595A,
            size: 17.r,
          ),
          filled: true,
          fillColor: const Color(0xffF7F7F7),
          border: const OutlineInputBorder(
            borderSide: BorderSide.none,
            borderRadius: BorderRadius.all(Radius.circular(19)),
          ),
          counterText: "",
        ),
      ),
    );
  }

  Widget _buildList() {
    return controller.isSearching.value
        ? Obx(() => _buildSearchList())
        : _buildCategorizedList();
  }

  /// 搜索列表
  Widget _buildSearchList() {
    var members = controller.searchResults;

    return ListView.separated(
      itemCount: members.length,
      physics: const BouncingScrollPhysics(),
      itemBuilder: (context, index) {
        return Obx(
          ()=> ActionSelectableTile(
            image: members.elementAt(index).avatarPath,
            name: members.elementAt(index).displayname ?? "",
            isSelected: controller.selectedIds
                .contains(members.elementAt(index).username),
            onTap: () {
              controller.onItemTap(members.elementAt(index).username);
            },
          ),
        );
      },
      separatorBuilder: (context, index) {
        return Divider(
          height: 2,
          color: AppColors.colorFFE7E7E7,
          indent: 50.r,
        );
      },
    );
  }

  /// 编辑角色，编辑成员页面
  Widget _buildCategorizedList() {
    return SingleChildScrollView(
      physics: const BouncingScrollPhysics(),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSection(
            title: L.currently_assigned_to.tr,
            members: controller.currentMembers,
          ),
          SizedBox(height: 20.r),
          _buildSection(
            title: L.other_members.tr,
            members: controller.otherMembers,
          ),
          SizedBox(height: 20.r),
        ],
      ),
    );
  }

  Widget _buildSection(
      {required title, required List<ContactData> members}) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          height: 30.r,
          // width: 0.5.sw,
          // constraints: BoxConstraints(minWidth: 0.5.sw, maxWidth: 0.6.sw),
          padding: EdgeInsets.symmetric(horizontal: 30.r),
          decoration: BoxDecoration(
            color: AppColors.colorFFF7F7F7,
            borderRadius: BorderRadius.only(
              topRight: Radius.circular(20.r),
              bottomRight: Radius.circular(20.r),
            ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                title,
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 13.sp,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
        for (var member in members)
          ActionSelectableTile(
            image: member.avatarPath,
            name: member.displayname ?? "",
            isSelected: controller.selectedIds.contains(member.username),
            onTap: () {
              controller.onItemTap(member.username);
            },
          ),
      ],
    );
  }
}