import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/modules/daoCommunity/roleManagement/role_management_controller.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../core/languages/l.dart';
import '../../../../core/values/colors.dart';

class RoleManagementView extends GetView<RoleManagementController> {
  const RoleManagementView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(L.role_management.tr),
        centerTitle: true,
        actions: [
          TextButton(
            onPressed: controller.onAddNewTap,
            child: Text(
              L.add_new.tr,
              style: TextStyle(
                color: AppColors.primaryBgColor1,
                fontSize: 13.sp,
              ),
            ),
          ),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    return Container(
      width: 1.sw,
      height: 1.sh - kToolbarHeight,
      color: AppColors.colorFFF8F8F8,
      child: Column(
        children: [
          SizedBox(height: 10.r),
          Obx(
            () => Expanded(
              child: controller.roles.isEmpty
                  ? const SizedBox.shrink()
                  : ListView.separated(
                      itemBuilder: (context, index) {
                        return _buildRoleItem(
                          role: controller.roles[index].name,
                          colorCode: controller.roles[index].color,
                          onTap: () {
                            controller.onRoleItemTap(controller.roles[index]);
                          },
                        );
                      },
                      separatorBuilder: (context, index) {
                        return SizedBox(height: 10.r);
                      },
                      itemCount: controller.roles.length,
                    ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRoleItem(
      {required String? role, required int? colorCode, Function()? onTap}) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 1.sw,
        height: 100.r,
        color: AppColors.white,
        padding: EdgeInsets.only(left: 60.r, right: 40.r),
        child: Center(
          child: Row(
            children: [
              _buildColorIndicator(colorCode: colorCode ?? 0xFFC91A1A),
              SizedBox(width: 20.r),
              SizedBox(
                width: 0.5.sw,
                child: Text(
                  role ?? '',
                  maxLines: 2,
                  style: TextStyle(
                    fontSize: 14.sp,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ),
              SizedBox(width: 20.r),
              Icon(
                Icons.arrow_forward_ios_rounded,
                size: 10.r,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildColorIndicator({required int colorCode}) {
    return Container(
      width: 20.r,
      height: 20.r,
      decoration: BoxDecoration(
        color: Color(colorCode),
        shape: BoxShape.circle,
      ),
    );
  }
}
