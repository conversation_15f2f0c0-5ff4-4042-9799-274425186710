import 'package:get/get.dart';

import '../../../../../core/utils/util.dart';
import '../../../../data/providers/db/database.dart';
import '../../../../data/services/channel_service.dart';
import '../../../group/details/group_details_base_controller.dart';

class AssignRoleToController extends GetxController {
  final _controller = Get.find<GroupDetailsBaseController>();

  /// 选中的成员Id列表
  RxList<String> selectedIds = <String>[].obs;

  /// 群成员列表
  List<ContactData> members = [];

  /// 搜索结果
  List<ContactData> searchResults = [];    

  RxBool isSearching = false.obs;

  @override
  void onInit() {
    selectedIds.addAll(Get.arguments['selected'] ?? []);
    /// 获取群内用户成员列表
    _getMembers();
    super.onInit();
  }

  void _getMembers() {
    for(var element in _controller.rxMemberDatas.values) {
      MemberInfo info = _controller.getMemberData(element.username);
      members.add(ContactData(
        id: 0,
        username: element.username,
        displayname: info.nickname,
        localname: info.nickname,
        avatarPath: appSupporAbsolutePath(info.avatarPath),
      ));
    }
  }

  void onSaveTap() {
    Get.back(result: selectedIds);
  }

  void onItemTap(String username) {
    if (selectedIds.contains(username)) {
      selectedIds.removeWhere((id) => id == username);
    } else {
      selectedIds.add(username);
    }
  }

  /// 文本变化监听
  void onSearchBarChanged(String? text) {
    if (text?.isNotEmpty ?? false) {
      isSearching.value = true;
      searchResults = _searchMemberDatas(text) ?? [];
    } else {
      isSearching.value = false;
      searchResults = [];
    }
  }

  List<ContactData>? _searchMemberDatas(String? data) {
    if (data == null || data.isEmpty || data.trim().isEmpty) return null;
    List<ContactData> results = [];
    members.forEach((element) {
      if (_strContains(element.displayname ?? '', data)) {
        results.add(element);
      }
    });
    return results;
  }

  bool _strContains(String src, String key) {
    var srcLow = src.toLowerCase();
    var keyLow = key.toLowerCase();
    return srcLow.contains(keyLow);
  }
}
