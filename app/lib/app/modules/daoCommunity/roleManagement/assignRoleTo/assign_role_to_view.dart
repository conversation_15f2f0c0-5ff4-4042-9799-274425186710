import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../../core/languages/l.dart';
import '../../../../../core/values/colors.dart';
import '../../widgets/action_selectable_tile.dart';
import 'assign_role_to_controller.dart';

class AssignRoleToView extends GetView<AssignRoleToController> {
  const AssignRoleToView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text( L.assign_role_to.tr),
        centerTitle: true,
        actions: [
          TextButton(
            onPressed: controller.onSaveTap,
            child: Text(
              L.save.tr,
              style: TextStyle(
                color: AppColors.primaryBgColor1,
                fontSize: 13.sp,
              ),
            ),
          ),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    return GestureDetector(
      onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
      child: Container(
        width: 1.sw,
        height: 1.sh - kToolbarHeight,
        color: AppColors.white,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: 18.r),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 25.r),
              child: _buildSearchBar(),
            ),
            SizedBox(height: 20.r),
            Expanded(
              child: _buildList(),
            ),
          ],
        ),
      ),
    );
  }

  /// 搜索框
  Widget _buildSearchBar() {
    return SizedBox(
      height: 37.r,
      child: TextField(
        autofocus: false,
        textAlign: TextAlign.start,
        style: TextStyle(fontSize: 12.sp),
        textAlignVertical: TextAlignVertical.center,
        onChanged: (text) => controller.onSearchBarChanged(text),
        maxLength: 200,
        decoration: InputDecoration(
          hintText: L.search_contact.tr,
          hintStyle: TextStyle(
            color: AppColors.colorFF7F7F7F,
            fontSize: 12.sp,
            fontWeight: FontWeight.w400,
          ),
          // 设置后，提升文本居中
          contentPadding: EdgeInsets.zero,
          prefixIcon: Icon(
            Icons.search,
            color: AppColors.colorFF59595A,
            size: 17.r,
          ),
          filled: true,
          fillColor: const Color(0xffF7F7F7),
          border: const OutlineInputBorder(
            borderSide: BorderSide.none,
            borderRadius: BorderRadius.all(Radius.circular(19)),
          ),
          counterText: "",
        ),
      ),
    );
  }

  Widget _buildList() {
    return Obx(() => _buildRawList());
  }

  /// 创建新角色，选择成员页面
  Widget _buildRawList() {
    var listItems = controller.isSearching.value ? controller.searchResults : controller.members;
    return ListView.separated(
      itemCount: listItems.length,
      physics: const BouncingScrollPhysics(),
      itemBuilder: (context, index) {
        return Obx(
          ()=> ActionSelectableTile(
            image: listItems.elementAt(index).avatarPath,
            name: listItems.elementAt(index).displayname ?? "",
            isSelected: controller.selectedIds
                .contains(listItems.elementAt(index).username),
            onTap: () {
              controller.onItemTap(listItems.elementAt(index).username);
            },
          ),
        );
      },
      separatorBuilder: (context, index) {
        return Divider(
          height: 2,
          color: AppColors.colorFFE7E7E7,
          indent: 50.r,
        );
      },
    );
  }
}
