import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../../core/languages/l.dart';
import '../../../../../core/values/colors.dart';
import '../../widgets/action_tile.dart';
import '../../widgets/role_color_selector.dart';
import 'create_edit_role_controller.dart';

class CreateEditRoleView extends GetView<CreateEditRoleController> {
  const CreateEditRoleView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          controller.role == null
              ? L.add_new_role.tr
              : controller.role!.name ?? '',
        ),
        centerTitle: true,
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    return GestureDetector(
      onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
      child: Container(
        width: 1.sw,
        height: 1.sh - kToolbarHeight,
        color: AppColors.white,
        child: Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                physics: BouncingScrollPhysics(),
                child: Padding(
                  padding: EdgeInsets.symmetric(horizontal: 30.r),
                  child: Form(
                    key: controller.formKey,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SizedBox(height: 30.r),
                        _buildLabel(L.role.tr),
                        TextFormField(
                          enabled: !controller.isEditingLeader() && !controller.isEditingManager(),
                          controller: controller.roleTextEditingController,
                          maxLength: 40,
                          textInputAction: TextInputAction.done,
                          decoration: _buildInputDecoration(L.role.tr),
                          validator: controller.roleValidator,
                        ),
                        SizedBox(height: 12.r),
                        Obx(
                          () => RoleColorSelector(
                            enabled: !controller.isEditingLeader() && !controller.isEditingManager(),
                            colors: controller.colors,
                            selectedColor: controller.colorCode.value,
                            onColorSelected: (color) {
                              controller.colorCode.value = color;
                            },
                          ),
                        ),
                        SizedBox(height: 20.r),
                        if (controller.role == null)
                          ActionTile(
                            title: L.assign_to.tr,
                            onTap: controller.onAssignToTap,
                          ),
                        if (controller.role != null && !controller.isEditingLeader())
                          ActionTile(
                            title: L.who_is_in_charge.tr,
                            onTap: controller.onWhoIsInChargeTap,
                          ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
            SizedBox(height: 6.r),
            if(!controller.isEditingLeader())
            ElevatedButton(
              onPressed: controller.isManagerTag() 
                ? controller.onManagerSubmitTap 
                : controller.onSubmitTap,
              child: Text(L.submit_1_cap.tr),
            ),
            if (controller.role != null && !controller.isEditingLeader() && !controller.isEditingManager()) SizedBox(height: 6.r),
            if (controller.role != null && !controller.isEditingLeader() && !controller.isEditingManager())
              TextButton(
                onPressed: controller.onDeleteTap,
                style: TextButton.styleFrom(
                  foregroundColor: AppColors.colorNegativeBtn,
                ),
                child: Text(L.delete_role.tr),
              ),
            SizedBox(height: 16.r),
          ],
        ),
      ),
    );
  }

  Widget _buildLabel(String text) {
    return Text(
      text,
      style: TextStyle(
        fontSize: 14.sp,
        color: Colors.black,
      ),
    );
  }

  InputDecoration _buildInputDecoration(String hintText,
      {Widget? suffix, String? counterText}) {
    return InputDecoration(
      hintText: hintText,
      hintStyle: TextStyle(
        fontSize: 12.sp,
        fontWeight: FontWeight.w300,
        color: AppColors.colorFFBBBBBB,
      ),
      counterText: counterText,
      contentPadding: EdgeInsets.symmetric(horizontal: 5.r, vertical: 10.r),
      enabledBorder: UnderlineInputBorder(
        borderSide: BorderSide(color: AppColors.colorFFCBCBCB),
      ),
      suffix: suffix,
    );
  }
}
