import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_metatel/app/modules/daoCommunity/widgets/delete_role_confirmation.dart';
import 'package:flutter_metatel/app/modules/group/details/dao_details_controller.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:flutter_metatel/core/utils/util.dart';
import 'package:flutter_metatel/routes/pages.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../../core/languages/l.dart';
import '../../../../../core/task/channel_task.dart';
import '../../../../../core/utils/events_bus.dart';
import '../../../../../core/values/code.dart';
import '../../../../../core/values/config.dart';
import '../../../../data/events/events.dart';
import '../../../../data/models/role.dart';
import '../../../../data/providers/api/channel.dart';
import '../../../../data/providers/db/database.dart';
import '../../../../data/services/channel_service.dart';
import '../../../../data/services/config_service.dart';
import '../../../group/details/group_details_base_controller.dart';

class CreateEditRoleController extends GetxController {
  final formKey = GlobalKey<FormState>();
  Role? role;
  final List<Color> colors = [
    Color(0xFFC91A1A),
    Color(0xFFC9831A),
    Color(0xFF60C91A),
    Color(0xFF1AC3C9),
    Color(0xFFC9BD1A),
    Color(0xFF1AC999),
    Color(0xFF1A6CC9),
    Color(0xFF7D1AC9),
    Color(0xFFC31AC9),
    Color(0xFF2390AC),
  ];
  late RxInt colorCode; // Default color
  List<String> _selectedMembers = [];
  /// 编辑时需要被删除的原有成员
  List<String> _deleteIds = [];
  /// manager标签，需要添加的manager
  List<String> _addIds = [];
  TextEditingController roleTextEditingController = TextEditingController();
  var _controller = Get.find<GroupDetailsBaseController>() as DaoDetailsController;

  @override
  void onInit() {
    colorCode = colors.first.value.obs;
    if (Get.arguments != null) {
      role = Get.arguments["role"] as Role;
      colorCode.value = role!.color!;
      roleTextEditingController.text = role!.name ?? "";
    }
    super.onInit();
  }

  bool isEditingLeader() {
    return role != null && role!.tagId == DefaultDaoRoleId.LEADER;
  }

  bool isEditingManager() {
    return role != null && role!.tagId == DefaultDaoRoleId.MANAGER;
  }

  String? roleValidator(String? value) {
    if (value == null || value.isEmpty) {
      return "${L.role.tr} ${L.required.tr}";
    }
    return null;
  }

  /// 创建时
  void onAssignToTap() async {
    var result = await Get.toNamed(
      Routes.AssignRoleToView,
      arguments: {'selected': _selectedMembers},
    );
    if (result == null) return;
    _selectedMembers = result as List<String>;
  }

  /// 编辑时
  void onWhoIsInChargeTap() async {
    var result = await Get.toNamed(
      Routes.RoleAssignedView,
      arguments: {'selected': _selectedMembers},
    );
    if (result == null) return;
    result = result as Map<String,dynamic>;
    _selectedMembers = result['selectedIds'];
    _deleteIds = result['deleteIds'];
    _addIds = result['addIds'];
  }

  bool isManagerTag() {
    return role != null && role!.tagId == DefaultDaoRoleId.MANAGER;
  }

  void onSubmitTap() async {
    if (!formKey.currentState!.validate()) return;
    try {
      EasyLoading.show(maskType: EasyLoadingMaskType.black);
      var channelId = _controller.rxGroupInfoData.value.channelId;
      /// 创建或编辑接口
      var result = await createOrUpdateDaoRoleRequest(
        channelId,
        roleName: roleTextEditingController.text.trim(),
        roleColor: "${colorCode.value}",
        assignedUsers: _selectedMembers,
        roleId: role?.tagId,
      );
      if(!result){
        EasyLoading.dismiss();
        toast(L.failed.tr);
        return;
      }
      /// 编辑时，删除Dao角色成员接口
      if(role != null && _deleteIds.isNotEmpty) {
        var success = await deleteDaoRoleMembersRequest(
          channelId,
          role!.tagId!,
          _deleteIds,
        );
        if(!success){
          EasyLoading.dismiss();
          toast(L.failed.tr);
          return;
        }
      }

      /// 重新获取数据并同步
      var requestResult = await getChannelInfoRequest(channelId);
      EasyLoading.dismiss();
      if (requestResult.code == Code.code200) {
        if(requestResult.tags != null && requestResult.tags!.isNotEmpty) {
          /// 同步到缓存
          Get.find<EventBus>().fire(SyncChannelTagEvent(requestResult.tags!));
        } 
        /// 同步频道信息和成员信息
        ChannelTask.channels([requestResult]);
        Get.find<ChannelService>().getAllChannelMemberInfos(
            channelId, Get.find<AppConfigService>().getUserName() ?? '');
      };         
      Get.back(result: true);
    } catch (e) {
      EasyLoading.dismiss();
      AppLogger.e("CreteateEditRoleController onSubmitTap error=$e");
    }
  }

  /// 编辑Manager标签
  void onManagerSubmitTap() async {
    if (!formKey.currentState!.validate()) return;
    if(_addIds.isEmpty && _deleteIds.isEmpty) return;
    /// 最多只能选择5个管理员
    if(_selectedMembers.length > 5) {
      toast(L.maximum_managers_hint.tr);
      return;
    }
    try {
      EasyLoading.show(maskType: EasyLoadingMaskType.black);
      var channelId = _controller.rxGroupInfoData.value.channelId;
      
      List<String> addMemberDisplayNames = [];
      List<String> deleteMemberDisplayNames = [];

      /// 编辑Manager时，删除旧的Manager
      if(_deleteIds.isNotEmpty) {
        for (var id in _deleteIds) {
          deleteMemberDisplayNames.add(_controller.getMemberData(id).displayname ?? '');
        }        
        bool success = await Get.find<ChannelService>().addOrRemoveAdmin(channelId, _deleteIds, deleteMemberDisplayNames, false); 
        if(!success){
          EasyLoading.dismiss();
          toast(L.failed.tr);
          return;
        }
      }

      /// 编辑Manager时，添加新的Manager
      if(_addIds.isNotEmpty) {
        for (var id in _addIds) {
          addMemberDisplayNames.add(_controller.getMemberData(id).displayname ?? '');
        }        
        bool success = await Get.find<ChannelService>().addOrRemoveAdmin(channelId, _addIds, addMemberDisplayNames, true); 
        if(!success){
          EasyLoading.dismiss();
          toast(L.failed.tr);
          return;
        }
      }

      /// 重新获取数据并同步
      var requestResult = await getChannelInfoRequest(channelId);
      EasyLoading.dismiss();
      if (requestResult.code == Code.code200) {
        if(requestResult.tags != null && requestResult.tags!.isNotEmpty) {
          /// 同步到缓存
          Get.find<EventBus>().fire(SyncChannelTagEvent(requestResult.tags!));
        }        
        /// 同步频道信息和成员信息
        ChannelTask.channels([requestResult]);
        Get.find<ChannelService>().getAllChannelMemberInfos(
            channelId, Get.find<AppConfigService>().getUserName() ?? '');
      };          
      Get.back(result: true);
    } catch (e) {
      EasyLoading.dismiss();
      AppLogger.e("CreteateEditRoleController onManagerSubmitTap error=$e");
    }
  }

  void onDeleteTap() async {
    if(role?.tagId == null) return;
    var result = await showModalBottomSheet(
      context: Get.context!,
      isScrollControlled: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: const Radius.circular(12).r,
          bottom: const Radius.circular(0).r,
        ),
      ),
      builder: (context) {
        return DeleteRoleConfirmation(
          onConfirmTap: () async {
            Get.back(result: true);
          },
        );
      },
    );
    if (result == null) return;
    try {
      /// 调用接口
      EasyLoading.show(maskType: EasyLoadingMaskType.black);
      var channelId = _controller.rxGroupInfoData.value.channelId;
      bool success = await deleteDaoRoleRequest(channelId, role!.tagId!);
      if(!success) {
        EasyLoading.dismiss();
        return;
      };

      /// 更新db: channel_info 的tags
      var db = Get.find<AppDatabase>();
      var channelInfo = await db.oneChannelInfo(channelId).get();
      var dbTags = channelInfo.first.tags ?? '[]';
      List<dynamic> jsonList = json.decode(dbTags);
      List<Role> tags = jsonList.map((e) => Role.fromJson(e)).toList();
      
      tags.removeWhere((element) => element.tagId == role!.tagId);
      ChannelTask.updateInfo(channelId, tags: tags);

      /// 更新db: group_member 的tags
      var members = await db.allGroupMemberWithTags(channelId).get();
      Map<String, List<String>> userTagsMap = {};
      for (var member in members) {
        var tags = member.tags ?? '[]';
        List<dynamic> jsonList = json.decode(tags);
        List<String> memberTags = jsonList.map((e) => e.toString()).toList();
        if(memberTags.contains(role!.tagId)) {
          memberTags.remove(role!.tagId);
          userTagsMap[member.username] = memberTags;
        }
      }
      ChannelTask.batchUpdateMemberTags(channelId, userTagsMap);
      Get.find<EventBus>().fire(SyncChannelTagEvent(tags));
      Get.find<EventBus>().fire(SyncChannelMemberTagEvent());
      Get.back();
    } catch (e) {
      AppLogger.e("onDeleteTap error=$e");
    } finally {
      EasyLoading.dismiss();
    }
  }
}
