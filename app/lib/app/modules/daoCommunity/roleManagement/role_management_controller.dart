import 'dart:async';
import 'dart:convert';

import 'package:flutter_metatel/app/data/providers/db/database.dart';
import 'package:flutter_metatel/core/languages/l.dart';
import 'package:flutter_metatel/core/values/config.dart';
import 'package:get/get.dart';

import '../../../../routes/pages.dart';
import '../../../data/models/role.dart';
import '../../group/details/dao_details_controller.dart';
import '../../group/details/group_details_base_controller.dart';

class RoleManagementController extends GetxController {
  RxList<Role> roles = <Role>[].obs;
  List<Role> defaultRoles = [];
  var db = Get.find<AppDatabase>();
  late String channelId;
  StreamSubscription? subscription;

  @override
  void onInit() {
    var _controller = Get.find<GroupDetailsBaseController>() as DaoDetailsController;
    channelId = _controller.rxGroupInfoData.value.channelId;            
    subscription = db.oneChannelInfo(channelId).watch().listen((event) {
      roles.clear();
      if(_controller.isMyOwner()){
        /// 只有群主才显示leader & manager
        setDefaultRoles();
      }      
      var channelInfoData = event.first;
      var dbTags =channelInfoData.tags;
      if(dbTags == null) return;
      List<dynamic> jsonList = json.decode(dbTags);
      if(jsonList.isEmpty) return;
      List<Role> tags = jsonList.map((e) => Role.fromJson(e)).toList();
      if(tags.isEmpty) return;      
      roles.addAll(tags);      
    });
    super.onInit();
  }

  @override
  void onClose() {
    subscription?.cancel();
    super.onClose();
  }

  /// 创建新角色
  void onAddNewTap() async {
    await Get.toNamed(Routes.CreateEditDaoRoleView);
  }

  /// 编辑角色
  void onRoleItemTap(Role role) async {
    await Get.toNamed(Routes.CreateEditDaoRoleView, arguments: {'role': role});
  }

  /// 添加默认角色
  Future<void> setDefaultRoles() async {
    roles.addAll([
      Role(
        tagId: DefaultDaoRoleId.LEADER,
        name: L.leader.tr,
        color: 0xFF750C0C,
      ),
      Role(
        tagId: DefaultDaoRoleId.MANAGER,
        name: L.manager.tr,
        color: 0xFF393166,
      ),
    ]);
  }
}
