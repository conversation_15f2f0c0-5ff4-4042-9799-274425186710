import 'package:flutter_web3/app/core/open_wallet_helper.dart';
import 'package:flutter_web3/app/data/models/token_model.dart';
import 'package:flutter_web3/app/db/database.dart';
import 'package:flutter_web3/app/modules/wallet/ethereum/wallet_controller.dart';
import 'package:get/get.dart';

class TokenSelectionController extends GetxController {
  /// 创建DAO社群，代币列表
  RxList<TokenDataModel> daoTokens = <TokenDataModel>[].obs;
  RxList<NetworkInfoData> networkLists = <NetworkInfoData>[].obs;
  Rxn<TokenDataModel> currentToken = Rxn<TokenDataModel>();
  RxBool isLoading = false.obs;
  var walletController = Get.find<WalletController>();

  @override
  void onInit() {
    super.onInit();
    getNetworkList();
    getTokenList();
  }

  void getTokenList({int chainId = 756}) async {
    isLoading.value = true;
    // var tokens = await DaoApi.getTokens();
    var tokens = await getTokens(chainId: chainId);
    isLoading.value = false;
    daoTokens.clear();
    if (tokens != null && tokens.isNotEmpty) {
      daoTokens.addAll(tokens);
    }
  }

  void getNetworkList() {
    networkLists.clear();
    networkLists.addAll(walletController.allListNet);
    /// 支持K链 & BSC链
    networkLists.value = networkLists.where((network) => network.chainid == 756 || network.chainid == 56).toList();
  }

  /// 创建DAO社群，选择代币
  void onTokenSelected(TokenDataModel data) {
    if(currentToken.value == data) {
      currentToken.value = null;
    } else {
      currentToken.value = data;
    }
  }

  void onConfirmTap() {
    if (currentToken.value == null) return;
    Get.back(result: currentToken.value);    
  }

  void onNetworkSelected(NetworkInfoData data) async {
    Get.back();
    getTokenList(chainId: data.chainid);
  }
}
