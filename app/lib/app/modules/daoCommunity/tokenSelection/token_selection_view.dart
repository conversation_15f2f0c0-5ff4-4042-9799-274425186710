import 'package:flutter/material.dart';
import 'package:flutter_metatel/core/values/colors.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:flutter_web3/app/core/language/s.dart';
import 'package:flutter_web3/app/core/utils/util.dart';

import 'package:flutter_web3/app/core/values/config.dart';
import 'package:flutter_web3/app/data/models/token_model.dart';
import 'package:flutter_web3/app/data/services/wallet_server.dart';
import 'package:flutter_web3/app/db/database.dart';
import 'package:flutter_web3/app/modules/wallet/ethereum/wallet_controller.dart';
import 'package:flutter_web3/app/modules/wallet/wallet_main.dart';
import 'package:flutter_web3/app/widgets/ic_widget.dart';
import 'package:get/get.dart';

import '../../../../core/languages/l.dart';
import '../../../../r.dart';
import 'token_selection_controller.dart';

class TokenSelectionView extends GetView<TokenSelectionController> {
  const TokenSelectionView({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.only(bottom: 16).r,
      width: double.infinity,
      constraints: BoxConstraints(maxHeight: 463.h),
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.only(
              left: 16,
              right: 16,
            ).r,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Container(
                  padding: const EdgeInsets.only(top: 21, bottom: 21).r,
                  child: Text(
                    S.current.tokens,
                    style: TextStyle(
                      fontSize: 18.sp,
                    ),
                  ),
                ),
                _buildNetworkSelection(context),
              ],
            ),
          ),
          Expanded(
            child: Obx(
              () {
                var tokenDatas = controller.daoTokens;
                return controller.isLoading.value
                    ? SpinKitDualRing(
                        size: 40.r,
                        lineWidth: 4,
                        color: AppColors.primaryBgColor1,
                      )
                    : tokenDatas.isEmpty 
                      ? Center(child: Text(L.no_data.tr, style: TextStyle(fontSize: 11.sp, color: AppColors.colorFF919499),),)
                      : ListView.separated(
                          itemBuilder: (context, index) {
                            return _buildTokenItem(tokenDatas[index]);
                          },
                          itemCount: tokenDatas.length,
                          separatorBuilder: (BuildContext context, int index) =>
                              Divider(height: 0.5.h, color: Color(0xFFE6E6E6)),
                        );
              },
            ),
          ),
          SizedBox(height: 16.h),
          Obx(
            () => ElevatedButton(
              onPressed: controller.isLoading.value || controller.daoTokens.isEmpty ? null : controller.onConfirmTap,
              child: Text(L.confirm.tr),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTokenItem(TokenDataModel data) {
    Widget walletItem = Column(
      children: [
        Material(
          color: Colors.transparent,
          child: InkWell(
            borderRadius: null,
            onTap: () {
              controller.onTokenSelected(data);
              // Get.back();
            },
            child: Container(
              height: 60.r,
              padding: const EdgeInsets.only(
                left: 16,
                right: 16,
              ).r,
              child: Row(
                children: [
                  IcWidget(
                    diameter: 42,
                    isNet: false,
                    isMain: data.isMain,
                    chainid: data.chainId,
                    filePath: data.image,
                    symbol: data.symbol,
                    uuid: data.uuid ?? '',
                  ),
                  SizedBox(
                    width: 10.r,
                  ),
                  Text(
                    data.symbol ?? '',
                    style: TextStyle(
                      fontSize: 13.sp,
                    ),
                  ),
                  Spacer(),
                  Padding(
                    padding:
                        EdgeInsets.symmetric(horizontal: 8.h, vertical: 8.h),
                    child: Obx(
                      () => Image.asset(
                        data.address == controller.currentToken.value?.address ? R.icoSelected : R.icoUnselected,
                        width: 16.r,
                        height: 16.r,
                        package: WalletConfig.packageName,
                      ),
                    ),
                  ),
                  SizedBox(
                    width: 16.r,
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );

    return walletItem;
  }

  Widget _buildNetworkSelection(BuildContext context) {
    return GestureDetector(
      onTap: () {
        showModalBottomSheetUtil(
          context: context,
          widget: _buildNetWorkList(),
        );
      },
      child: Container(
        height: 24.r,
        alignment: Alignment.centerRight,
        color: Colors.transparent,
        child: Container(
          padding: const EdgeInsets.only(left: 10, right: 10).r,
          height: 24.r,
          alignment: Alignment.centerLeft,
          decoration: BoxDecoration(
            borderRadius: const BorderRadius.all(Radius.circular(26)).r,
            border: Border.all(
              color: AppColors.colorFF000000,
              width: 0.5.r,
            ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Obx(() {
                var connectd = Get.find<WalletService>()
                    .getEthServerTask()
                    .rxConnected
                    .value;
                var color = connectd == 2
                    ? Colors.green
                    : connectd == 1
                        ? Colors.red
                        : Colors.grey;
                return CircleAvatar(
                  radius: 4.0.r,
                  backgroundColor: color,
                );
              }),
              SizedBox(width: 8.5.w),
              GetBuilder(
                id: Get.find<WalletController>().builderIdNetName,
                builder: (WalletController controller) => Obx(
                  () => Container(
                    constraints:
                        BoxConstraints(maxWidth: Get.width / 7 * 2),
                    child: Text(
                      controller.netModel.value.name ?? "",
                      style: TextStyle(
                        color: AppColors.colorFF000000,
                        fontSize: 13.sp,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ),
              ),
              SizedBox(width: 8.r),
              Icon(
                Icons.keyboard_arrow_down,
                size: 15.r,
                color: AppColors.colorFF000000,
              ),
            ],
          ),
        ),
      ),
    );
  }


  Widget _buildNetWorkList() {    
    return Container(
      padding: const EdgeInsets.only(bottom: 16, top: 15).r,
      width: double.infinity,
      constraints: BoxConstraints(maxHeight: 463.r, minHeight: 300.r),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.only(bottom: 21).r,
            margin: EdgeInsets.only(top: 5.r),
            width: double.infinity,
            child: Center(
              child: Text(
                S.current.net,
                style: TextStyle(
                  fontSize: 18.sp,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          Expanded(
            child: Container(
              padding: const EdgeInsets.only(top: 10, bottom: 5).r,
              constraints: BoxConstraints(minHeight: 463.r),
              decoration: BoxDecoration(
                border: Border(
                  left: BorderSide(
                    width: 1.r, 
                    color: AppColors.colorFFF8F8F8,
                  ),
                ),
              ),
              child: Container(
                padding: const EdgeInsets.only(bottom: 30).r,
                child: ListView.builder(
                  shrinkWrap: true,
                  itemBuilder: (context, index) {
                    NetworkInfoData data = controller.networkLists[index];
                    bool isSelected = data == Get.find<WalletController>().netModel.value;
                    return _buildNetworkItem(
                      data, 
                      isSelected: isSelected,
                      title: data.name ?? "",
                      callBack: (data) {
                        controller.onNetworkSelected(data);
                      },
                    );
                  },
                  itemCount: controller.networkLists.length,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNetworkItem(NetworkInfoData data, 
    {required bool isSelected, required String title, required WalletOrNetworkItemCallBack callBack}) {
    var rpc = data.rpcUrl ?? '';
    rpc = rpc.replaceFirst('http://', '');
    rpc = rpc.replaceFirst('https://', '');
    return Column(
      children: [
        Material(
          color: Colors.transparent,
          child: InkWell(
            borderRadius: null,
            onTap: () {
              callBack(data);
            },
            child: Container(
              height: 60.r,
              padding: const EdgeInsets.only(
                left: 16,
                right: 16,
              ).r,
              margin:
                  const EdgeInsets.only(left: 27, right: 27, top: 5, bottom: 5).r,            
              child: Row(
                children: [
                  IcWidget(diameter:38,isNet: true,chainid:data.chainid,filePath: data.chainPath,),
                  SizedBox(width: 10.w),
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: 14.sp,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  Spacer(),
                  Padding(
                    padding: EdgeInsets.symmetric(
                        horizontal: 8.h, vertical: 8.h), // 增加点击区域
                    child: Image.asset(
                      isSelected ? R.icoSelected : R.icoUnselected,
                      width: 14.r,
                      height: 14.r,
                      package: WalletConfig.packageName,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }
}
