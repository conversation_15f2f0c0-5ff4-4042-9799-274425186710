import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/data/models/poll.dart';
import 'package:flutter_metatel/core/values/config.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

import '../../../../core/languages/l.dart';
import '../../../../core/values/colors.dart';
import '../../../data/services/config_service.dart';

class PollTile extends StatelessWidget {
  const PollTile({super.key, required this.poll, this.onTap});
  final Poll poll;
  final Function()? onTap;
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 30, vertical: 20).r,
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              flex: 5,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    _getVoteTypeText(poll.category),
                    style: TextStyle(
                      fontSize: 11.sp,
                      color: AppColors.colorFF989898,
                    ),
                  ),
                  Text(
                    poll.title ?? '',
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: Colors.black,
                    ),
                  ),
                  poll.desc != null && poll.desc!.isNotEmpty
                    ? Text(
                        poll.desc ?? '',
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                        style: TextStyle(
                          fontSize: 12.sp,
                          color: Colors.black,
                          fontWeight: FontWeight.w300,
                        ),
                      )
                    : SizedBox(height: 5.r),
                  SizedBox(height: 5.r),  
                  Text(
                    "${L.duration.tr}: ${_formatTimestampToDate(poll.startTime)} - ${_formatTimestampToDate(poll.endTime)}",
                    style: TextStyle(
                      fontSize: 10.sp,
                      color: AppColors.colorFF989898,
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                ],
              ),
            ),
            Expanded(
              flex: 1,
              child: SizedBox.shrink(),
            ),
            Expanded(
              flex: 2,
              child: Column(
                children: [
                  SizedBox(height: 15.r),
                  _generateActionButton(poll, onTap: onTap),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _getVoteTypeText(int? type) {
    switch (type) {
      case DaoPollCategory.general:
        return L.general_vote.tr;
      case DaoPollCategory.proposal:
        return L.proposal_vote.tr;
      case DaoPollCategory.dao:
        return L.dao_vote.tr;
      case DaoPollCategory.leader:
        return L.leader_vote.tr;
      default:
        return "";
    }
  }

  /// 当投票进行中：Vote, Voted, Apply, Applied | 状态有: active, inactive
  /// 当投票结束：Expired | 状态有: disable
  Widget _generateActionButton(Poll poll, {Function()? onTap}) {
    if(poll.registerAt == null || poll.startTime == null || poll.endTime == null) {
      return SizedBox.shrink();
    }
    /// 投票结束Expired
    if(poll.status != DaoPollStatus.ongoing) {
      return _buildActionButton(type: ActionButtonType.disable, text: L.vote_action_btn_expired.tr, onTap: onTap);
    }
    var username = Get.find<AppConfigService>().getUserName();
  
    /// Leader报名期间（从options的label判定是否已经报名）
    /// 期间为registerAt和startTime之间
    var currentTime = DateTime.now().millisecondsSinceEpoch;
    bool isApplyPeriod = currentTime >= poll.registerAt!*1000 && currentTime < poll.startTime!*1000;
    if(poll.category == DaoPollCategory.leader && isApplyPeriod){
      bool isApplied = poll.options != null && poll.options!.any((element) => element.label == username);
      return _buildActionButton(
        type: isApplied ? ActionButtonType.inactive : ActionButtonType.active,
        text: isApplied ? L.vote_action_btn_participated.tr :L.vote_action_btn_participate.tr,
        onTap: onTap,
      );
    }

    /// 投票期间
    bool isVoted = poll.votedMembers != null && poll.votedMembers!.any((element) => element.username == username);
    if(isVoted) {
      return _buildActionButton(type: ActionButtonType.inactive, text: L.vote_action_btn_voted.tr, onTap: onTap);
    } else {
      return _buildActionButton(type: ActionButtonType.active, text: L.vote_action_btn_vote_now.tr, onTap: onTap);
    }    
  }

  Widget _buildActionButton(
      {int type = ActionButtonType.active, required String text, Function()? onTap}) {
    return SizedBox(
      height: 30.r,
      child: ElevatedButton(
        onPressed: onTap,
        style: ElevatedButton.styleFrom(
          backgroundColor: type == ActionButtonType.active
              ? AppColors.primaryBgColor1
              : type == ActionButtonType.inactive
                  ? AppColors.colorFFCCE9F6
                  : AppColors.colorFFD5D5D5,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(30.r),
          ),
        ),
        child: Text(
          text,
          style: TextStyle(
            fontSize: 11.sp,
            color: type == 2 ? AppColors.primaryBgColor1 : AppColors.white,
          ),
        ),
      ),
    );
  }

  String _formatTimestampToDate(int? timestamp) {
    if (timestamp == null) return "";
    try {
      var date = DateTime.fromMillisecondsSinceEpoch(timestamp*1000);
      return DateFormat('dd MMM yyyy | HH:mm').format(date);
    } catch (e) {
      return "";
    }
  }
}

class ActionButtonType {
  static const int active = 1;
  static const int inactive = 2;
  static const int disable = 3;
}
