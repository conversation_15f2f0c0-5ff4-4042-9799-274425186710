import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class ActionTile extends StatelessWidget {
  const ActionTile({super.key, required this.title, this.onTap});
  final String title;
  final Function()? onTap;
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      behavior: HitTestBehavior.opaque,
      child: SizedB<PERSON>(
        height: 60.r,
        child: Row(
          children: [
            Text(
              title,
              style: TextStyle(fontSize: 14.sp),
            ),
            const Spacer(),
            Icon(Icons.arrow_forward_ios_rounded, size: 10.r),
          ],
        ),
      ),
    );
  }
}
