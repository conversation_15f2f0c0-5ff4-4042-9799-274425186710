import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../core/values/colors.dart';
import '../../../widgets/mavatar_circle_avatar.dart';

class ActionSelectableTile extends StatelessWidget {
  const ActionSelectableTile(
      {super.key,
      this.image,
      required this.name,
      required this.isSelected,
      this.onTap});
  final String? image;
  final String name;
  final bool isSelected;
  final Function()? onTap;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: onTap,
      child: Container(
        height: 70.r,
        padding: EdgeInsets.symmetric(horizontal: 25.r),
        child: Row(
          children: [
            image == null 
              ? Container(
                  width: 42.r,
                  height: 42.r,
                  decoration: const BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: LinearGradient(
                      colors: [
                        AppColors.colorFF3F7FDC,
                        AppColors.colorFF8C31AC,
                      ],
                      begin: Alignment(-0.5, -0.5),
                      end: Alignment(0.5, 0.5),
                    ),
                  ),
                )
              : MAvatarCircle(
                  diameter: 42,
                  text: name,
                  textStyle: TextStyle(
                    fontSize: 20.sp,
                    color: Colors.white,
                    fontWeight: FontWeight.normal,
                  ),
                  imagePath: image,
                ),
            SizedBox(width: 13.r),
            SizedBox(
              width: 0.6.sw,
              child: Text(
                name,
                maxLines: 1,
                style: TextStyle(fontSize: 14.sp, overflow: TextOverflow.ellipsis),
              ),
            ),
            Spacer(),
            _buildSelectable(isSelected: isSelected),
            SizedBox(width: 8.r),
          ],
        ),
      ),
    );
  }

  Widget _buildSelectable({required bool isSelected}) {
    return Container(
      width: 15.r,
      height: 15.r,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: isSelected ? AppColors.primaryBgColor1 : AppColors.white,
        border: isSelected
            ? null
            : Border.all(
                color: AppColors.colorFF707070,
                width: 1,
              ),
      ),
      child: Center(
        child: isSelected
            ? Icon(
                Icons.check,
                size: 10.r,
                color: AppColors.white,
              )
            : null,
      ),
    );
  }
}
