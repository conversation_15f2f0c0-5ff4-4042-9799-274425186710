import 'package:flutter/material.dart';
import 'package:flutter_metatel/core/values/colors.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class RoleTag extends StatelessWidget {
  const RoleTag({super.key, required this.role, required this.color});
  final String? role;
  final int? color;
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 1).r,
      decoration: BoxDecoration(
        color: color == null ? AppColors.colorFFEBEBEB : Color(color!),
        borderRadius: BorderRadius.circular(7.r),
      ),
      child: Text(
        role ?? "",
        style: TextStyle(
          color: Colors.white,
          fontSize: 9.sp,
        ),
      ),
    );
  }
}
