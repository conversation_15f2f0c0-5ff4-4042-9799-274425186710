import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class PollActionConfirmation extends StatelessWidget {
  const PollActionConfirmation({super.key, required this.title, required this.detail, this.onConfirmTap,});  
  final String title;
  final String detail;
  final Function()? onConfirmTap;
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 30.r),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(height: 20.r),

          /// 标题
          Text(
            title,
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 35.r),

          Text(
            detail,
            style: TextStyle(
              fontSize: 13.sp,
            ),
          ),

          Sized<PERSON><PERSON>(height: 50.r),
          ElevatedButton(
            onPressed: onConfirmTap,
            child: Text("confirm".tr),
          ),
          TextButton(
            onPressed: () {
              Get.back();
            },
            child: Text("cancel".tr),
          ),
          SizedBox(height: 40.r),
        ],
      ),
    );
  }
}
