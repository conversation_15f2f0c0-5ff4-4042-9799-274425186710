import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_web3/app/data/models/token_model.dart';
import 'package:flutter_web3/app/widgets/ic_widget.dart';
import 'package:get/get.dart';

import '../../../../core/languages/l.dart';

class DaoExistBtmSheet extends StatelessWidget {
  const DaoExistBtmSheet({super.key, required this.daoToken});
  final TokenDataModel daoToken;
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.only(bottom: 16).r,
      width: double.infinity,
      // constraints: BoxConstraints(maxHeight: 310.h),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(height: 30.h),
          Text(
            L.dao_exist_btm_sheet_title.tr,
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 10.h),
          Text(
            L.dao_exist_btm_sheet_content.tr,
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 15.sp,
              fontWeight: FontWeight.w300,
            ),
          ),
          SizedBox(height: 28.h),
          IcWidget(
            diameter: 86,
            isNet: false,
            isMain: daoToken.isMain,
            chainid: daoToken.chainId,
            filePath: daoToken.image,
            symbol: daoToken.symbol,
            uuid: daoToken.uuid ?? '',
          ),
          SizedBox(height: 25.h),
          ElevatedButton(
            onPressed: () {
              Get.back();
            },
            child: Text(L.confirm.tr),
          ),
          SizedBox(height: 20.r),
        ],
      ),
    );
  }
}
