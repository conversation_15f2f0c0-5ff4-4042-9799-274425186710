import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../core/languages/l.dart';
import '../../../../core/values/colors.dart';

class PollSelector extends StatefulWidget {
  const PollSelector({super.key, required this.voteOptions});
  final Map<String, Function()?> voteOptions;
  @override
  State<PollSelector> createState() => _PollSelectorState();
}

class _PollSelectorState extends State<PollSelector> {
  bool _isExpanded = false;

  @override
  Widget build(BuildContext context) {
    return ExpansionTile(
      textColor: Colors.black,
      iconColor: Colors.black,
      collapsedIconColor: Colors.black,
      backgroundColor: AppColors.colorFFE2F2FA,
      collapsedBackgroundColor: AppColors.colorFFE2F2FA,
      shape: Border.all(color: Colors.transparent),
      tilePadding: EdgeInsets.symmetric(horizontal: 30.r),
      title: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            L.create_poll.tr,
            style: TextStyle(
              fontSize: 13.sp,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
      trailing: Icon(
        _isExpanded ? Icons.close : Icons.add,
        size: 17.r,
      ),
      onExpansionChanged: (bool expanded) {
        setState(() {
          _isExpanded = expanded;
        });
      },
      children: <Widget>[
        for (var i = 0; i < widget.voteOptions.keys.length; i++)
          GestureDetector(
            behavior: HitTestBehavior.opaque,
            onTap: widget.voteOptions.values.elementAt(i),
            child: Container(
              width: 1.sw,
              padding: EdgeInsets.symmetric(
                horizontal: 30.r,
                vertical: 16.r,
              ),
              color: AppColors.colorFFE2F2FA,
              child: Text(
                widget.voteOptions.keys.elementAt(i),
                style: TextStyle(fontSize: 14.sp),
              ),
            ),
          )
      ],
    );
  }
}
