import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../core/languages/l.dart';

class RoleColorSelector extends StatefulWidget {
  const RoleColorSelector(
      {super.key,
      required this.colors,
      required this.selectedColor,
      this.onColorSelected,
      this.enabled = true});
  final List<Color> colors;
  final int selectedColor;
  final Function(int)? onColorSelected;
  final bool enabled;

  @override
  State<RoleColorSelector> createState() => _RoleColorSelectorState();
}

class _RoleColorSelectorState extends State<RoleColorSelector> {
  bool _isExpanded = false;
  final _expansionController = ExpansionTileController();
  @override
  Widget build(BuildContext context) {
    var _colors = widget.colors;
    return ExpansionTile(
      enabled: widget.enabled,
      controller: _expansionController,
      textColor: Colors.black,
      iconColor: Colors.black,
      collapsedIconColor: Colors.black,
      title: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildColorCircle(color: Color(widget.selectedColor)),
          SizedBox(width: 15.r),
          Text(
            L.default_color.tr,
            style: TextStyle(fontSize: 14.sp),
          ),
        ],
      ),
      tilePadding: EdgeInsets.zero,
      shape: Border.all(color: Colors.transparent),
      trailing: Icon(
        _isExpanded
            ? Icons.keyboard_arrow_up_rounded
            : Icons.keyboard_arrow_down_rounded,
        size: 17.r,
      ),
      onExpansionChanged: (bool expanded) {
        setState(() {
          _isExpanded = expanded;
        });
      },
      children: <Widget>[
        SizedBox(
          height: 140.r,
          child: GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: _colors.length,
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 5,
              childAspectRatio: 1,
            ),
            itemBuilder: (context, index) {
              return GestureDetector(
                behavior: HitTestBehavior.opaque,
                onTap: () {
                  _expansionController.collapse();
                  widget.onColorSelected?.call(_colors[index].value);
                },
                child: Center(
                  child: _buildColorCircle(
                    color: _colors[index],
                    size: 33.r,
                  ),
                ),
              );
            },
          ),
        )
      ],
    );
  }

  Widget _buildColorCircle({required Color color, double? size}) {
    return Container(
      width: size ?? 18.r,
      height: size ?? 18.r,
      decoration: BoxDecoration(
        color: color,
        shape: BoxShape.circle,
      ),
    );
  }
}
