import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../core/languages/l.dart';
import '../../../../core/values/colors.dart';

class ExpandableText extends StatefulWidget {
  final String text;
  final int maxLines;
  final TextStyle? style;
  final double width;

  const ExpandableText({
    Key? key,
    required this.text,
    required this.width,
    required this.maxLines,
    this.style,
  }) : super(key: key);

  @override
  _ExpandableTextState createState() => _ExpandableTextState();
}

class _ExpandableTextState extends State<ExpandableText> {
  bool _isExpanded = false;
  late String _text;
  late int _maxLines;
  late TextStyle? _style;
  late double _width;
  late TextSpan _textSpan;
  late TextPainter _textPainter;
  late bool _isTextOverflowed;

  @override
  void initState() {
    super.initState();
    _text = widget.text;
    _maxLines = widget.maxLines;
    _style = widget.style;
    _width = widget.width;

    // Properly initialize TextPainter with all necessary parameters
    _textSpan = TextSpan(
      text: _text,
      style: _style,
    );
    _textPainter = TextPainter(
      text: _textSpan,
      maxLines: _maxLines,
      textDirection: TextDirection.ltr,
      textScaler: TextScaler.noScaling,
    );

    _textPainter.layout(maxWidth: _width);
    _isTextOverflowed = _textPainter.didExceedMaxLines;
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        RichText(
          text: _textSpan,
          maxLines: _isExpanded ? null : _maxLines, // 如果展开则不限制行数
          textDirection: TextDirection.ltr,
          textScaler: TextScaler.noScaling,
          overflow: _isExpanded
              ? TextOverflow.clip
              : TextOverflow.ellipsis, // 如果未展开则显示省略号
        ),
        if (_isTextOverflowed)
          SizedBox(
            width: 1.sw,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                SizedBox(height: 20.r),
                GestureDetector(
                  onTap: () {
                    setState(() {
                      _isExpanded = !_isExpanded;
                    });
                  },
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        _isExpanded ? L.view_less.tr : L.view_more.tr,
                        style: TextStyle(
                          color: AppColors.colorFF707070,
                          fontSize: 10.sp,
                        ),
                      ),
                      Icon(
                        _isExpanded
                            ? Icons.keyboard_arrow_up_rounded
                            : Icons.keyboard_arrow_down_rounded,
                        size: 12.r,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }
}
