import 'package:flutter/material.dart';
import 'package:flutter_metatel/core/languages/l.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class DeleteRoleConfirmation extends StatelessWidget {
  const DeleteRoleConfirmation({super.key, this.onConfirmTap,});  
  final Function()? onConfirmTap;
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 30.r),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(height: 20.r),

          /// 标题
          Text(
            L.delete_role.tr,
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 35.r),

          Text(
            L.delete_role_detail.tr,
            style: TextStyle(
              fontSize: 13.sp,
            ),
          ),

          <PERSON><PERSON><PERSON><PERSON>(height: 50.r),
          Elevated<PERSON>utton(
            onPressed: onConfirmTap,
            child: Text("confirm".tr),
          ),
          TextButton(
            onPressed: () {
              Get.back();
            },
            child: Text("cancel".tr),
          ),
          Sized<PERSON><PERSON>(height: 40.r),
        ],
      ),
    );
  }
}
