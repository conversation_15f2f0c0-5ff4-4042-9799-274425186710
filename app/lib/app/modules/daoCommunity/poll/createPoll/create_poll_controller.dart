
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_metatel/app/data/providers/api/channel.dart';
import 'package:flutter_metatel/core/utils/util.dart';
import 'package:flutter_metatel/core/values/config.dart';
import 'package:get/get.dart';

import '../../../../../core/languages/l.dart';
import '../../../message/message_controller.dart';

class CreatePollController extends GetxController {
  /// 1: General vote, 2: Proposal vote, 3: Dao vote
  late int voteType;
  final formKey = GlobalKey<FormState>();
  final titleTextEditingController = TextEditingController();
  final descTextEditingController = TextEditingController();
  RxInt selectedDaoTitleIndex = 0.obs;
  RxInt selectedVotingMethodIndex = 0.obs;
  RxInt selectedDurationIndex = 0.obs;
  List<TextEditingController> voteOptionControllers = <TextEditingController>[];
  final daoTitleOptions = [
    L.dao_vote_title_minimum_joining_requirement.tr,
  ];
  final votingMethodOptions = [
    L.voting_method_option_1.tr,
    L.voting_method_option_2.tr,
  ];
  final votingMethodOptionsValue = [DaoPollType.onePersonOneVote, DaoPollType.shareholdingVoting];
  final durationOptions = [
    L.duration_option_hour.trParams({'num': "1"}),
    L.duration_option_day.trParams({'num': "1"}),
    L.duration_option_days.trParams({'num': "3"}),
    L.duration_option_days.trParams({'num': "7"}),
  ];
  final durationOptionsValue = [3600, 86400, 259200, 604800];

  @override
  void onInit() {
    voteType = Get.arguments['vote_type'];
    super.onInit();
  }

  void onVotingMethodSelect(int index) {
    selectedVotingMethodIndex.value = index;
  }

  void onDurationSelect(int index) {
    selectedDurationIndex.value = index;
  }

  void onDaoTitleSelect(int index) {
    selectedDaoTitleIndex.value = index;
  }

  void onCreateTap() async {
    if (!formKey.currentState!.validate()) return;
    /// Trim空白,并ignore为空的选项
    List<String> options = [];
    for (var controller in voteOptionControllers) {
      String option = controller.text.trim();
      if (option.isNotEmpty) {
        options.add(option.trim());
      }
    }
    if(options.length < 2) {
      toast(L.at_least_2_options_are_required.tr);
      return;
    }

    /// 若该投票category为Dao,判定是否选项都为数字
    if(voteType == DaoPollCategory.dao) {
      bool allValid = true;
      for (var option in options) {
        double? value = double.tryParse(option);
        if(value == null) {
          allValid = false;
          toast(L.dao_vote_option_invalid.tr);
          break;
        }
        if(value == 0) {
          allValid = false;
          toast(L.dao_vote_option_non_zero.tr);
          break;
        }
      }
      if(!allValid) return;
    }

    var channelId = Get.find<MessageController>().currentUser?.userName;
    if(channelId == null) return;
    String title = voteType == DaoPollCategory.dao ? daoTitleOptions[0] : titleTextEditingController.text.trim();
    String desc = descTextEditingController.text.trim();
    int type = votingMethodOptionsValue[selectedVotingMethodIndex.value];
    EasyLoading.show(maskType: EasyLoadingMaskType.black);
    String? id = await createDaoVoteRequest(
      channelId, 
      title: title,
      describe: desc, 
      category: voteType, 
      type: type,
      options: options, 
      duration: durationOptionsValue[selectedDurationIndex.value],
    );
    EasyLoading.dismiss();
    if(id == null) return;

    /// 发送消息到群里
    // Poll poll = Poll(
    //   id: id,
    //   channelId: channelId,
    //   title: title,
    //   desc: desc,
    //   type: type,
    // );
    // sendMessageToDao(poll);

    /// 返回并刷新
    Get.back(result: true); 
  }

  // void sendMessageToDao(Poll poll) {
  //   var toUsername = Get.find<MessageController>().currentUser?.userName;
  //   if(toUsername == null) return;
  //   var tomsg = MessageEvent(
  //     uuid(),
  //     owner: toUsername,
  //     chatType: ChatType.channelChat,
  //     type: MessageType.daoPoll,
  //     body: jsonEncode(poll.toJson()),
  //     direction: Direction.outGoing,
  //     dateTime: TimeTask.instance.getNowDateTime(),
  //   );
  //   Get.find<EventBus>().fire(MessageForwardEvent(tomsg));
  //   sendMessage(null, tomsg, pushType: PushType.message);
  // }

  String? titleValidator(String? value) {
    if (value == null || value.isEmpty) {
      return L.title_empty.tr;
    }
    return null;
  }
}
