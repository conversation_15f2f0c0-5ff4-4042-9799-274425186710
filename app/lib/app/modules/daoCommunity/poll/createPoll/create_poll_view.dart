import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/modules/daoCommunity/poll/createPoll/create_poll_controller.dart';
import 'package:flutter_metatel/app/widgets/form/label_input_vote.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../../core/languages/l.dart';
import '../../../../../core/utils/util.dart';
import '../../../../../core/values/colors.dart';
import '../../../../widgets/form/label_dropdown.dart';
import '../../../../widgets/form/label_text_field.dart';

class CreatePollView extends GetView<CreatePollController> {
  const CreatePollView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          controller.voteType == 1
              ? L.create_general_vote.tr
              : controller.voteType == 2
                  ? L.create_proposal_vote.tr
                  : L.create_dao_vote.tr,
        ),
        centerTitle: true,
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    return GestureDetector(
      onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
      child: Container(
        width: 1.sw,
        height: 1.sh - kToolbarHeight,
        child: SingleChildScrollView(
          child: Padding(
            padding: EdgeInsets.only(left: 25.r, right: 25.r, top: 30.r),
            child: Form(
              key: controller.formKey,
              child: Column(
                children: [
                  /// 标题
                  if (controller.voteType == 1 || controller.voteType == 2)
                    LabelTextField(
                      controller: controller.titleTextEditingController,
                      label: controller.voteType == 1
                          ? L.general_vote_title.tr
                          : controller.voteType == 2
                              ? L.proposal_vote_title.tr
                              : L.dao_vote_title.tr,
                      hintText: L.hint_title.tr,
                      validator: controller.titleValidator,
                    ),
                  if (controller.voteType == 3)
                    Obx(
                      () => LabelDropdown(
                        label: L.dao_vote_title.tr,
                        dropdownLabel: controller.daoTitleOptions[
                            controller.selectedDaoTitleIndex.value],
                        onTap: () {
                          showBottomDialogCommonWithCancel(
                            Get.context!,
                            widgets: [
                              for (var i = 0;
                                  i < controller.daoTitleOptions.length;
                                  i++)
                                Column(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Padding(
                                      padding: EdgeInsets.symmetric(
                                          horizontal: 16.r),
                                      child: getBottomSheetItemSimpleAndImg(
                                        Get.context!,
                                        controller.daoTitleOptions[i],
                                        itemCallBack: () {
                                          controller.onDaoTitleSelect(i);
                                        },
                                      ),
                                    ),
                                    i == controller.daoTitleOptions.length - 1
                                        ? Container(
                                            height: 10.h,
                                            color: AppColors.colorFFF3F3F3,
                                          )
                                        : Container(
                                            height: 1.h,
                                            color: AppColors.backgroundGray,
                                          ),
                                  ],
                                ),
                            ],
                          );
                        },
                      ),
                    ),
                  SizedBox(height: 18.r),

                  /// 描述
                  LabelTextField(
                    controller: controller.descTextEditingController,
                    label: L.description_optional.tr,
                    hintText: L.hint_what_is_this_vote_about.tr,
                    maxLines: 5,
                    maxLength: 200,
                  ),
                  SizedBox(height: 18.r),

                  LabelInputVote(
                    label: L.option_up_to.trParams(
                        {"limit": controller.voteType == 2 ? "2" : "3-5"}),
                    max: controller.voteType == 2 ? 2 : 5,
                    textFieldMaxLength: controller.voteType == 2 ? 10 : 100,
                    onChanged: (options) {
                      controller.voteOptionControllers = options;
                    },
                  ),
                  SizedBox(height: 28.r),

                  /// 投票方式
                  Obx(
                    () => LabelDropdown(
                      label: L.voting_method.tr,
                      dropdownLabel: controller.votingMethodOptions[
                          controller.selectedVotingMethodIndex.value],
                      onTap: () {
                        showBottomDialogCommonWithCancel(
                          Get.context!,
                          widgets: [
                            for (var i = 0;
                                i < controller.votingMethodOptions.length;
                                i++)
                              Column(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Padding(
                                    padding:
                                        EdgeInsets.symmetric(horizontal: 16.r),
                                    child: getBottomSheetItemSimpleAndImg(
                                      Get.context!,
                                      controller.votingMethodOptions[i],
                                      // textColor: i ==
                                      //         controller
                                      //             .selectedVotingMethodIndex
                                      //             .value
                                      //     ? AppColors.primaryBgColor1
                                      //     : null,
                                      itemCallBack: () {
                                        controller.onVotingMethodSelect(i);
                                      },
                                    ),
                                  ),
                                  i == controller.votingMethodOptions.length - 1
                                      ? Container(
                                          height: 10.h,
                                          color: AppColors.colorFFF3F3F3,
                                        )
                                      : Container(
                                          height: 1.h,
                                          color: AppColors.backgroundGray,
                                        ),
                                ],
                              ),
                          ],
                        );
                      },
                    ),
                  ),
                  SizedBox(height: 28.r),

                  /// 投票时长
                  Obx(
                    () => LabelDropdown(
                      label: L.duration.tr,
                      dropdownLabel: controller.durationOptions[
                          controller.selectedDurationIndex.value],
                      onTap: () {
                        showBottomDialogCommonWithCancel(
                          Get.context!,
                          widgets: [
                            for (var i = 0;
                                i < controller.durationOptions.length;
                                i++)
                              Column(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Padding(
                                    padding:
                                        EdgeInsets.symmetric(horizontal: 16.r),
                                    child: getBottomSheetItemSimpleAndImg(
                                      Get.context!,
                                      controller.durationOptions[i],
                                      // textColor: i ==
                                      //         controller
                                      //             .selectedDurationIndex.value
                                      //     ? AppColors.primaryBgColor1
                                      //     : null,
                                      itemCallBack: () {
                                        controller.onDurationSelect(i);
                                      },
                                    ),
                                  ),
                                  i == controller.durationOptions.length - 1
                                      ? Container(
                                          height: 10.h,
                                          color: AppColors.colorFFF3F3F3,
                                        )
                                      : Container(
                                          height: 1.h,
                                          color: AppColors.backgroundGray,
                                        ),
                                ],
                              ),
                          ],
                        );
                      },
                    ),
                  ),

                  SizedBox(height: 50.r),

                  ElevatedButton(
                    onPressed: () {
                      controller.onCreateTap();
                    },
                    child: Text(L.create_1_cap.tr),
                  ),

                  SizedBox(height: 20.r),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
