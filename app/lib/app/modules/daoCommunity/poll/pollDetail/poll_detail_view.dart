import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/modules/daoCommunity/poll/pollDetail/poll_detail_controller.dart';
import 'package:flutter_metatel/app/widgets/mavatar_circle_avatar.dart';
import 'package:flutter_metatel/app/widgets/text_middle_overlow.dart';
import 'package:flutter_metatel/core/values/colors.dart';
import 'package:flutter_metatel/core/values/config.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../../core/languages/l.dart';
import '../../../../../r.dart';
import '../../../../data/models/poll.dart';
import '../../widgets/expandable_text.dart';
import '../../widgets/role_tag.dart';
import 'left_slanted_clipper.dart';
import 'right_slanted_clipper.dart';

class PollDetailView extends GetView<PollDetailController> {
  const PollDetailView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(L.vote_detail_title.tr),
        centerTitle: true,
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    return Container(
      width: 1.sw,
      height: 1.sh - kToolbarHeight,
      color: AppColors.white,
      child: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            /// 投票倒计时
            if (controller.showCountdownSection) _buildCountdownSection(),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 20.r),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if(controller.poll.category != DaoPollCategory.leader)
                  SizedBox(height: 16.r),

                  /// 用户信息
                  if(controller.poll.category != DaoPollCategory.leader)
                  _buildUserInfo(),
                  
                  SizedBox(height: 40.r),

                  /// 投票标题
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 16.r),
                    child: Text(
                      controller.poll.title ?? "",
                      style: TextStyle(
                        fontSize: 14.sp,
                        fontWeight: FontWeight.w500,
                        color: Colors.black,
                      ),
                    ),
                  ),
                  SizedBox(height: 10.r),

                  /// 简介
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 18.r),
                    child: ExpandableText(
                      text: controller.poll.desc ?? "",
                      maxLines: 14,
                      width: 1.sw - 36.r - 40.r,
                      style: TextStyle(
                        fontSize: 13.sp,
                        color: AppColors.colorFF7F7F7F,
                      ),
                    ),
                  ),
                  if (controller.showDivider) SizedBox(height: 16.r),
                  if (controller.showDivider)
                    Divider(
                      height: 0.5,
                      color: AppColors.colorFFE7E7E7,
                    ),
                  SizedBox(height: 26.r),

                  /// Leader投票参与者部分
                  if (controller.showCandidateSection) _buildCandidateSection(),

                  /// 投票区
                  if (controller.showVoteSection) _buildVoteSection(),

                  /// 投票结果
                  if (controller.showResultSection) _buildResultSection(),
                  SizedBox(height: 30.r),

                  /// 按钮区
                  SizedBox(
                    width: 1.sw,
                    child: _buildBtnSection(),
                  ),

                  SizedBox(height: 30.r),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildUserInfo() {
    return Container(
      width: 1.sw,
      color: AppColors.white,
      child: Row(
        children: [
          /// 用户头像
          GetBuilder<PollDetailController>(
            id: "user-info-avatar",
            builder: (context) {
              return MAvatarCircle(
                diameter: 40,
                imagePath: controller.getAvatarById(controller.poll.username),
                text: controller.getNameById(controller.poll.username, idToRebuild: "user-info-avatar"),
              );
            }
          ),
          SizedBox(width: 10.r),
          Expanded(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                /// 用户名
                GetBuilder<PollDetailController>(
                  id: "user-info-name",
                  builder: (context) {
                    return Text(
                      controller.getNameById(controller.poll.username, idToRebuild: "user-info-name"),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      style: TextStyle(fontSize: 12.sp),
                    );
                  }
                ),
                SizedBox(height: 2.r),

                /// 用户角色
                Wrap(
                  spacing: 6.r,
                  runSpacing: 4.r,
                  children: [
                    for (var role
                        in controller.getRolesById(controller.poll.username))
                      RoleTag(role: role.name, color: role.color),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCountdownSection() {
    return Container(
      width: 1.sw,
      padding: EdgeInsets.only(
        left: 22.r,
        right: 22.r,
        top: 34.r,
        bottom: 45.r,
      ),
      decoration: BoxDecoration(
        // borderRadius: BorderRadius.circular(10.r),
        gradient: const LinearGradient(
          colors: [
            AppColors.colorFF102B74,
            AppColors.colorFF4BB9F0,
          ],
          begin: Alignment(-1.4, 0),
          end: Alignment(1.1, 0),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.5),
            spreadRadius: 1,
            blurRadius: 5,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            "${L.time_left.tr}",
            textAlign: TextAlign.center,
            style: TextStyle(
              color: AppColors.white,
              fontSize: 14.sp,
              fontWeight: FontWeight.w600,
            ),
          ),
          // SizedBox(height: 2.r),
          // Text(
          //   "${L.let_us_know_what_do_you_think.tr} !",
          //   textAlign: TextAlign.center,
          //   style: TextStyle(
          //     color: AppColors.colorFFEFEFEF,
          //     fontSize: 12.sp,
          //     fontWeight: FontWeight.w300,
          //   ),
          // ),
          SizedBox(height: 30.r),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Obx(
                () => _buildLabelTime(
                  label: L.days_1_cap.tr,
                  time: controller.timerDays.value.toString().padLeft(2, '0'),
                ),
              ),
              SizedBox(width: 7.r),
              _buildLabelTimeSeparator(),
              SizedBox(width: 7.r),
              Obx(
                () => _buildLabelTime(
                  label: L.hour_1_cap.tr,
                  time: controller.timerHours.value.toString().padLeft(2, '0'),
                ),
              ),
              SizedBox(width: 7.r),
              _buildLabelTimeSeparator(),
              SizedBox(width: 7.r),
              Obx(
                () => _buildLabelTime(
                  label: L.min_1_cap.tr,
                  time:
                      controller.timerMinutes.value.toString().padLeft(2, '0'),
                ),
              ),
              SizedBox(width: 7.r),
              _buildLabelTimeSeparator(),
              SizedBox(width: 7.r),
              Obx(
                () => _buildLabelTime(
                  label: L.sec_1_cap.tr,
                  time:
                      controller.timerSeconds.value.toString().padLeft(2, '0'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildLabelTime({required String label, required String time}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Text(
          label,
          textAlign: TextAlign.center,
          style: TextStyle(
            fontSize: 10.sp,
            color: AppColors.colorFFEFEFEF,
          ),
        ),
        SizedBox(height: 4.r),
        Container(
          height: 45.r,
          width: 47.r,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(11.r),
            gradient: LinearGradient(
              colors: [
                AppColors.colorFF5D73BC.withValues(alpha: 0.5),
                AppColors.colorFF56B9D8.withValues(alpha: 0.5),
              ],
              begin: Alignment(0, -1.2),
              end: Alignment(0, 1),
            ),
          ),
          child: Center(
            child: Text(
              time,
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 18.sp,
                fontWeight: FontWeight.w500,
                color: AppColors.colorFFD6F4FF,
                height: 1.4,
                shadows: <Shadow>[
                  Shadow(
                    offset: Offset(1, 1),
                    blurRadius: 1,
                    color: Color.fromARGB(255, 133, 133, 133),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildLabelTimeSeparator() {
    return SizedBox(
      height: 60.r,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Expanded(
            flex: 1,
            child: SizedBox.shrink(),
          ),
          Expanded(
            flex: 3,
            child: Center(
              child: Text(
                ":",
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 12.sp,
                  fontWeight: FontWeight.bold,
                  color: AppColors.colorFF32C6FE,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 投票部分
  Widget _buildVoteSection() {
    if (controller.showGeneralDaoVoteSection) {
      return _buildGeneralAndDaoVoteSection();
    }
    if (controller.showProposalVoteSection) {
      return _buildProposalVoteSection();
    }
    if (controller.showLeaderVoteSection) {
      return _buildLeaderVoteSection();
    }
    return SizedBox.shrink();
  }

  /// 投票(General和Dao)
  Widget _buildGeneralAndDaoVoteSection() {
    return Container(
      width: 1.sw,
      // padding: EdgeInsets.symmetric(vertical: 20.r),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(17.r),
        border: Border.all(
          color: AppColors.colorFFE7E7E7,
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: 20.r),

          /// 投票方法
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 30.r),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Image.asset(
                  R.iconVoteMethod,
                  width: 17.r,
                  height: 17.r,
                ),
                SizedBox(width: 10.r),
                Text(
                  (controller.poll.type ?? -1) == DaoPollType.onePersonOneVote
                      ? L.voting_method_option_1.tr
                      : (controller.poll.type ?? -1) == DaoPollType.shareholdingVoting
                          ? L.voting_method_option_2.tr
                          : "",
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: Colors.black,
                  ),
                ),
              ],
            ),
          ),
          SizedBox(height: 16.r),
          Divider(
            height: 0.5,
            color: AppColors.colorFFE7E7E7,
          ),
          SizedBox(height: 26.r),

          /// 投票选项
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 30.r),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: controller.poll.options == null
                  ? []
                  : [
                      for (var i = 0; i < controller.poll.options!.length; i++)
                        Obx(
                          () {
                            var option = controller.poll.options![i];
                            return _buildOption(
                              isDisable:
                                  controller.isVoted ||
                                      controller.isTimerEnd.value,
                              option: option.label ?? "",
                              isSelected: controller.selectedOptionId.value ==
                                  (option.id ?? "-1"),
                              onTap: () {
                                controller.selectedOptionId.value =
                                    option.id ?? "-1";
                              },
                            );
                          },
                        ),
                    ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOption(
      {required String option,
      required bool isSelected,
      required bool isDisable,
      Function()? onTap}) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        GestureDetector(
          onTap: isDisable ? null : onTap,
          child: Row(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildTick(isSelected: isSelected, isDisable: isDisable),
              SizedBox(width: 10.r),
              SizedBox(
                width: 0.65.sw,
                child: Text(
                  option,
                  maxLines: 10,
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: Colors.black,                  
                  ),
                ),
              ),
            ],
          ),
        ),
        SizedBox(height: 28.r),
      ],
    );
  }

  Widget _buildTick({required isSelected, required isDisable}) {
    return isDisable && !isSelected
        ? SizedBox(
            width: 18.r,
            height: 18.r,
          )
        : Container(
            width: 18.r,
            height: 18.r,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: isDisable
                  ? AppColors.colorFFB2B2B2
                  : isSelected
                      ? AppColors.primaryBgColor1
                      : AppColors.white,
              border: isSelected
                  ? null
                  : Border.all(
                      color: AppColors.colorFF707070,
                      width: 0.5,
                    ),
            ),
            child: Center(
              child: isSelected
                  ? Icon(
                      Icons.check,
                      size: 15.r,
                      color: AppColors.white,
                    )
                  : null,
            ),
          );
  }

  /// 投票(Proposal)
  Widget _buildProposalVoteSection() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        /// 投票方法
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 30.r),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Image.asset(
                R.iconVoteMethod,
                width: 17.r,
                height: 17.r,
              ),
              SizedBox(width: 10.r),
              Text(
                (controller.poll.type ?? -1) == DaoPollType.onePersonOneVote
                    ? L.voting_method_option_1.tr
                    : (controller.poll.type ?? -1) == DaoPollType.shareholdingVoting
                        ? L.voting_method_option_2.tr
                        : "",
                style: TextStyle(
                  fontSize: 12.sp,
                  color: Colors.black,
                ),
              ),
            ],
          ),
        ),
        SizedBox(height: 20.r),
        /// 投票选项
        Center(
          child: Stack(
            // mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Obx(
                () => _buildRightSlantedButton(
                  isDisable: controller.selectedOptionId.value != null ||
                      controller.isTimerEnd.value,
                  label: controller.poll.options == null
                      ? ""
                      : controller.poll.options![0].label ?? "",
                  onTap: () {
                    controller.onProposalVoteSubmitTap(
                        controller.poll.options![0].id);
                  },
                ),
              ),
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  SizedBox(width: 125.r),
                  Obx(
                    () => _buildLeftSlantedButton(
                      isDisable: controller.selectedOptionId.value != null ||
                          controller.isTimerEnd.value,
                      label: controller.poll.options == null
                          ? ""
                          : controller.poll.options![1].label ?? "",
                      onTap: () {
                        controller.onProposalVoteSubmitTap(
                            controller.poll.options![1].id);
                      },
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
        Obx(
          () => controller.selectedOptionId.value != null
              ? SizedBox(height: 12.r)
              : SizedBox.shrink(),
        ),

        /// 用户投票选择
        Obx(
          () => controller.selectedOptionId.value == null
              ? SizedBox.shrink()
              : Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Image.asset(
                      R.iconVoteMethod,
                      width: 17.r,
                      height: 17.r,
                      color: AppColors.colorFF7F7F7F,
                    ),
                    SizedBox(width: 11.r),
                    Text(
                      L.you_voted_for.trParams({
                        "result": controller.getResultLabelById(
                            controller.selectedOptionId.value)
                      }),
                      style: TextStyle(
                        fontSize: 11.sp,
                        color: AppColors.colorFF7F7F7F,
                      ),
                    ),
                  ],
                ),
        ),
      ],
    );
  }

  Widget _buildRightSlantedButton(
      {required String label, bool isDisable = false, Function()? onTap}) {
    return GestureDetector(
      onTap: isDisable ? null : onTap,
      child: ClipPath(
        clipper: RightSlantedClipper(),
        child: Container(
          width: 130.r,
          height: 47.r,
          padding: EdgeInsets.symmetric(horizontal: 14.r, vertical: 13.r),
          decoration: BoxDecoration(
            color:
                isDisable ? AppColors.colorFFBDCAEE : AppColors.colorFF102B74,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(25.r),
              bottomLeft: Radius.circular(25.r),
            ),
          ),
          child: MiddleText(
            label,
            WXTextOverflow.ellipsisMiddle,
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 14.sp,
              color: Colors.white,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildLeftSlantedButton(
      {required String label, bool isDisable = false, Function()? onTap}) {
    return GestureDetector(
      onTap: isDisable ? null : onTap,
      child: ClipPath(
        clipper: LeftSlantedClipper(),
        child: Container(
          width: 130.r,
          height: 47.r,
          padding: EdgeInsets.symmetric(horizontal: 14.r, vertical: 13.r),
          decoration: BoxDecoration(
            color:
                isDisable ? AppColors.colorFFBEE9FF : AppColors.colorFF4BB9F0,
            borderRadius: BorderRadius.only(
              topRight: Radius.circular(25.r),
              bottomRight: Radius.circular(25.r),
            ),
          ),
          child: MiddleText(
            label,
            WXTextOverflow.ellipsisMiddle,
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 14.sp,
              color: Colors.white,
            ),
          ),
        ),
      ),
    );
  }

  /// 投票(Leader)
  Widget _buildLeaderVoteSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 18.r),
          child: Text(
            "${L.candidates.tr}:",
            style: TextStyle(
              fontSize: 14.sp,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        SizedBox(height: 12.r),
        Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            for (var i = 0; i < (controller.poll.options?.length ?? 0); i++)
              Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Obx(
                    () => _buildCandidateTile(
                      option: controller.poll.options![i],
                      isDisable: controller.isVoted ||
                          controller.isTimerEnd.value,
                      isSelected: controller.poll.options![i].id ==
                          controller.selectedOptionId.value,
                      onTap: () {
                        controller.selectedOptionId.value =
                            controller.poll.options![i].id ?? "-1";
                      },
                    ),
                  ),
                  if (i != controller.poll.options!.length - 1)
                    SizedBox(height: 8.r),
                ],
              ),
          ],
        ),
      ],
    );
  }

  /// 投票结果
  Widget _buildResultSection() {
    if (controller.showGeneralDaoResultSection) {
      return _buildGeneralAndDaoResultSection();
    }
    if (controller.showProposalResultSection) {
      return _buildProposalResultSection();
    }
    if (controller.showLeaderResultSection) {
      return _buildLeaderResultSection();
    }
    return SizedBox.shrink();
  }

  /// 投票结果(General和Dao)
  Widget _buildGeneralAndDaoResultSection() {
    return Container(
      width: 1.sw,
      padding: EdgeInsets.symmetric(horizontal: 16.r, vertical: 23.r),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(17.r),
        color: AppColors.colorFFFBFBFB,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            L.vote_results.tr,
            style: TextStyle(
              fontSize: 14.sp,
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(height: 11.r),

          /// 投票是否Valid
          _buildLabelValueRow(
            label: "${L.this_voting.tr}:",
            value: controller.poll.status == DaoPollStatus.valid
                ? "${L.valid_cap.tr}"
                : controller.poll.status == DaoPollStatus.invalid
                    ? "${L.invalid_cap.tr}"
                    : "",
            valueColor: controller.poll.status == DaoPollStatus.valid
                ? AppColors.colorFF60C91A
                : AppColors.colorFC52B2B,
          ),

          SizedBox(height: 11.r),

          /// 投票票数
          _buildLabelValueRow(
            label: "${L.valid_votes.tr}:",
            value: "${controller.totalVoteCount}",
          ),
          SizedBox(height: 11.r),

          /// Validity原因
          Text(
            controller.getResultReason(),
            style: TextStyle(
              fontSize: 12.sp,
              color: AppColors.colorFC52B2B,
              fontStyle: FontStyle.italic,
            ),
          ),

          SizedBox(height: 40.r),

          /// 投票结果展示
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            padding: EdgeInsets.zero,
            itemCount: controller.poll.options == null
                ? 0
                : controller.poll.options!.length,
            separatorBuilder: (context, index) => SizedBox(height: 20.r),
            itemBuilder: (context, index) {
              return _buildResultTile(
                option: controller.poll.options![index], 
                index: index,
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildResultTile({required PollOption option, required int index}) {
    return Row(
      children: [
        Expanded(
          flex: 9,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Padding(
                padding: const EdgeInsets.only(left: 8, right: 7).r,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    SizedBox(
                      width: 0.5.sw,
                      child: Text(
                        "${option.label}",
                        maxLines: 1,
                        style: TextStyle(
                          fontSize: 14.sp,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ),
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // _buildHorizontalAvatarList(),
                        SizedBox(width: 4.r),
                        Text(
                          "${option.total ?? ""}",
                          style: TextStyle(
                            fontSize: 11.sp,
                            color: AppColors.colorFF656565,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              SizedBox(height: 2.r),
              LinearProgressIndicator(
                minHeight: 6.r,
                borderRadius: BorderRadius.circular(10.r),
                value: option.total == null || option.total == 0 || controller.totalVoteCount == 0
                    ? 0
                    : option.total! / controller.totalVoteCount,
                backgroundColor: AppColors.white,
                color: _getColorByIndex(index),
              ),
            ],
          ),
        ),

        /// 最终结果
        Expanded(
          flex: 1,
          child: controller.poll.status == DaoPollStatus.valid &&
                  controller.getResult() != null && controller.getResult()!.id == option.id
              ? Image.asset(R.checkedCircle, width: 14.r, height: 14.r)
              : SizedBox.shrink(),
        ),
      ],
    );
  }

  Color _getColorByIndex(int index) {
    switch (index) {
      case 0:
        return AppColors.colorFC52B2B;
      case 1:
        return AppColors.colorFF249ED9;
      case 2:
        return AppColors.colorFFC9831A;
      case 3:
        return AppColors.colorFF60C91A;
      case 4:
        return AppColors.colorFFC9BD1A;
      default:
        return AppColors.colorFF656565;
    }
  }

  // Widget _buildHorizontalAvatarList() {
  //   int max = 5;
  //   return SizedBox(
  //     height: 12.r,
  //     child: Center(
  //       child: Stack(
  //         children: [
  //           for (int i = 1; i <= max; i++)
  //             Padding(
  //               padding: EdgeInsets.only(left: (i * 7).r),
  //               child: Container(
  //                 width: 11.r,
  //                 height: 11.r,
  //                 decoration: BoxDecoration(
  //                   shape: BoxShape.circle,
  //                   color: AppColors.colorFFEDF1FB,
  //                   border: Border.all(color: AppColors.colorFFEDF1FB),
  //                 ),
  //                 child: MAvatarCircle(diameter: 11.r),
  //               ),
  //             ),
  //         ],
  //       ),
  //     ),
  //   );
  // }

  /// 投票结果(Proposal)
  Widget _buildProposalResultSection() {
    var result = controller.getResult();    
    if(controller.poll.options == null) return SizedBox.shrink();
    return Container(
      width: 1.sw,
      padding: EdgeInsets.symmetric(horizontal: 16.r, vertical: 23.r),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(17.r),
        color: AppColors.colorFFFBFBFB,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            L.vote_results.tr,
            style: TextStyle(
              fontSize: 14.sp,
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(height: 11.r),

          /// 投票结果文字展示
          if(result != null && controller.poll.status == DaoPollStatus.valid)
            _buildLabelValueRow(
              label: "${L.vote_result.tr}:",
              value: result.label ?? "",
              valueColor:
                  controller.poll.options!.indexOf(result) == 0 
                    ? AppColors.colorFF102B74 
                    : AppColors.colorFF249ED9,
            ),
          if(result != null && controller.poll.status == DaoPollStatus.valid)
            SizedBox(height: 11.r),

          /// 投票是否Valid
          _buildLabelValueRow(
            label: "${L.this_voting.tr}:",
            value: controller.poll.status == DaoPollStatus.valid
                ? "${L.valid_cap.tr}"
                : controller.poll.status == DaoPollStatus.invalid
                    ? "${L.invalid_cap.tr}"
                    : "",
            valueColor: controller.poll.status == DaoPollStatus.valid
                ? AppColors.colorFF60C91A
                : AppColors.colorFC52B2B,
          ),
          SizedBox(height: 11.r),

          /// 投票票数
          _buildLabelValueRow(
            label: "${L.valid_votes.tr}:",
            value: "${controller.totalVoteCount}",
          ),
          SizedBox(height: 11.r),

          /// Validity原因
          Text(
            controller.getResultReason(),
            style: TextStyle(
              fontSize: 12.sp,
              color: AppColors.colorFC52B2B,
              fontStyle: FontStyle.italic,
            ),
          ),

          SizedBox(height: 40.r),

          /// 投票结果图表展示
          Center(
            child: _buildProposalResultGraph(width: 0.7.sw),
          ),
        ],
      ),
    );
  }

  Widget _buildLabelValueRow(
      {required String label, required String value, Color? valueColor}) {
    return Row(
      children: [
        Expanded(
          flex: 3,
          child: Text(
            label,
            style: TextStyle(
              fontSize: 12.sp,
            ),
          ),
        ),
        Expanded(
          flex: 7,
          child: Text(
            value,
            style: TextStyle(
              fontSize: 12.sp,
              fontWeight: FontWeight.bold,
              color: valueColor,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildProposalResultGraph({required double width}) {
    if (controller.poll.options == null ||
        controller.poll.options![0].total == null ||
        controller.poll.options![1].total == null) return SizedBox.shrink();

    var totalVoteCount = controller.totalVoteCount;
    var ratio1 = totalVoteCount == 0 ? 0 : controller.poll.options![0].total! / totalVoteCount;
    var ratio2 = totalVoteCount == 0 ? 0 : controller.poll.options![1].total! / totalVoteCount;
    return SizedBox(
      width: width,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
      
          /// 标签行
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                controller.poll.options![0].label ?? "",
                style: TextStyle(
                  fontSize: 10.sp,
                  fontWeight: FontWeight.w600,
                  color: AppColors.colorFF102B74,
                ),
              ),
              Text(
                controller.poll.options![1].label ?? "",
                style: TextStyle(
                  fontSize: 10.sp,
                  fontWeight: FontWeight.w600,
                  color: AppColors.colorFF249ED9,
                ),
              ),
            ],
          ),
          SizedBox(height: 3.r),

          /// 图表行
          ratio1 == 0 && ratio2 == 0
          ? Container(
              width: width,
              height: 6.r,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10.r),
                color: AppColors.colorFFEBEBEB,
              ),
            )
          : Row(
              children: [
                Container(
                  width: width * ratio1,
                  height: 6.r,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10.r),
                    color: AppColors.colorFF102B74,
                  ),
                ),
                Spacer(),
                Container(
                  width: width * ratio2,
                  height: 6.r,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10.r),
                    color: AppColors.colorFF249ED9,
                  ),
                ),
              ],
            ),
          SizedBox(height: 2.r),
      
          /// 数量行
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                "${controller.poll.options![0].total ?? 0}",
                style: TextStyle(fontSize: 10.sp),
              ),
              Text(
                "${controller.poll.options![1].total ?? 0}",
                style: TextStyle(fontSize: 10.sp),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 投票结果(Leader)
  Widget _buildLeaderResultSection() {
    var result = controller.getResult();
    if(controller.poll.options == null) return SizedBox.shrink();
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        /// 结果显示
        Container(
          width: 1.sw,
          padding: EdgeInsets.symmetric(horizontal: 16.r, vertical: 23.r),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(17.r),
            color: AppColors.colorFFFBFBFB,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                L.vote_results.tr,
                style: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w500,
                ),
              ),
              SizedBox(height: 11.r),

              /// 投票是否Valid
              _buildLabelValueRow(
                label: "${L.this_voting.tr}:",
                value: controller.poll.status == DaoPollStatus.valid
                    ? "${L.valid_cap.tr}"
                    : controller.poll.status == DaoPollStatus.invalid
                        ? "${L.invalid_cap.tr}"
                        : "",
                valueColor: controller.poll.status == DaoPollStatus.valid
                    ? AppColors.colorFF60C91A
                    : AppColors.colorFC52B2B,
              ),
              SizedBox(height: 11.r),

              /// 投票票数
              _buildLabelValueRow(
                label: "${L.valid_votes.tr}:",
                value: "${controller.totalVoteCount}",
              ),
              SizedBox(height: 11.r),

              /// 投票结果文字展示
              if(result != null && controller.poll.status == DaoPollStatus.valid)
                GetBuilder<PollDetailController>(
                  id: "leader-result",
                  builder: (context) {
                    return _buildLabelValueRow(
                      label: "${L.next_leader.tr}:",
                      value: controller.getNameById(result.label, idToRebuild: "leader-result"),
                    );
                  }
                ),
              if(result != null && controller.poll.status == DaoPollStatus.valid)
                SizedBox(height: 11.r),

              /// Validity原因
              Text(
                controller.getResultReason(),
                style: TextStyle(
                  fontSize: 12.sp,
                  color: AppColors.colorFC52B2B,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ],
          ),
        ),
        SizedBox(height: 28.r),

        /// Leaderboard 排行榜
        if(result != null && controller.poll.status == DaoPollStatus.valid)
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                "${L.leaderboard.tr}:",
                style: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w500,
                ),
              ),
              SizedBox(height: 12.r),
              Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  for (var i = 0; i < (controller.poll.options?.length ?? 0); i++)
                    Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        _buildLeaderboardTile(
                          index: i,
                          voteCount: controller.poll.options![i].total,
                          option: controller.poll.options![i],
                        ),
                        if (i != 0)
                          Divider(
                            thickness: 0.5,
                            color: AppColors.colorFFE7E7E7,
                          ),
                      ],
                    ),
                ],
              ),
            ],
          ),
      ],
    );
  }

  /// 排行榜组件
  Widget _buildLeaderboardTile({int? index, double? voteCount, PollOption? option}) {
    return index == null || option == null
        ? SizedBox.shrink()
        : Container(
            padding: EdgeInsets.symmetric(horizontal: 28.r, vertical: 16.r),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10.r),
              color: index == 0 ? AppColors.colorFFF6F6F6 : AppColors.white,
            ),
            child: Row(
              children: [
                Text(
                  "${index + 1}",
                  style: TextStyle(
                    fontSize: index == 0 ? 13.sp : 11.sp,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                SizedBox(width: 10.r),

                /// 用户头像
                GetBuilder<PollDetailController>(
                  id: "leaderboard-avatar-$index",
                  builder: (context) {
                    return MAvatarCircle(
                      diameter: index == 0 ? 40 : 30,
                      imagePath: controller.getAvatarById(option.label),
                      text: controller.getNameById(option.label, idToRebuild: "leaderboard-avatar-$index"),
                    );
                  }
                ),
                SizedBox(width: 10.r),
                Expanded(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      /// 用户名
                      GetBuilder<PollDetailController>(
                        id: "leaderboard-name-$index",
                        builder: (context) {
                          return Text(
                            controller.getNameById(option.label, idToRebuild: "leaderboard-name-$index"),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            style: TextStyle(
                              fontSize: index == 0 ? 14.sp : 12.sp,
                              fontWeight:
                                  index == 0 ? FontWeight.w500 : FontWeight.w400,
                            ),
                          );
                        }
                      ),
                      SizedBox(height: 2.r),

                      /// 用户角色
                      Wrap(
                        spacing: 6.r,
                        runSpacing: 4.r,
                        children: [
                          for (var role in controller
                              .getRolesById(option.label))
                            RoleTag(role: role.name, color: role.color),
                        ],
                      ),
                    ],
                  ),
                ),
                if (voteCount != null)
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Image.asset(
                        R.leaderboardVoteCount,
                        width: 9.r,
                        height: 18.r,
                      ),
                      SizedBox(width: 4.r),
                      Text(
                        "$voteCount",
                        style: TextStyle(
                          fontSize: index == 0 ? 15.sp : 11.sp,
                        ),
                      ),
                    ],
                  ),
              ],
            ),
          );
  }

  /// 参与者部分(Leader)
  Widget _buildCandidateSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 18.r),
          child: Text(
            "${L.candidates.tr}:",
            style: TextStyle(
              fontSize: 14.sp,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        SizedBox(height: 12.r),
        Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            for (var i = 0;
                i < (controller.poll.options?.length ?? 0);
                i++)
              Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  _buildCandidateTile(option: controller.poll.options![i]),
                  if (i != controller.poll.options!.length - 1)
                    SizedBox(height: 8.r),
                ],
              ),
          ],
        ),
      ],
    );
  }

  /// 参与者组件(Leader)
  Widget _buildCandidateTile(
      {required PollOption? option, Function()? onTap, bool? isSelected, bool? isDisable, int? voteCount}) {
    return option == null
        ? SizedBox.shrink()
        : GestureDetector(
            behavior: HitTestBehavior.opaque,
            onTap: (isDisable ?? false) ? null : onTap,
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 28.r, vertical: 16.r),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(17.r),
                color: AppColors.white,
                boxShadow: voteCount != null
                    ? null
                    : [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.2),
                          // spreadRadius: 2,
                          blurRadius: 6,
                          offset: Offset(0, 3),
                        ),
                      ],
              ),
              child: Row(
                children: [
                  if (isSelected != null)
                    _buildTick(isSelected: isSelected, isDisable: isDisable),
                  if (isSelected != null) SizedBox(width: 10.r),

                  /// 用户头像
                  GetBuilder<PollDetailController>(
                    id: "candidate-avatar-${option.id}",
                    builder: (context) {
                      return MAvatarCircle(
                        diameter: 40,
                        imagePath: controller.getAvatarById(option.label),
                        text: controller.getNameById(option.label, idToRebuild: "candidate-avatar-${option.id}"),
                      );
                    }
                  ),
                  SizedBox(width: 10.r),
                  Expanded(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        /// 用户名
                        GetBuilder<PollDetailController>(
                          id: "candidate-name-${option.id}",
                          builder: (context) {
                            return Text(
                              controller.getNameById(option.label, idToRebuild: "candidate-name-${option.id}"),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                              style: TextStyle(fontSize: 12.sp),
                            );
                          }
                        ),
                        SizedBox(height: 2.r),

                        /// 用户角色
                        Wrap(
                          spacing: 6.r,
                          runSpacing: 4.r,
                          children: [
                            for (var role
                                in controller.getRolesById(option.label))
                              RoleTag(role: role.name, color: role.color),
                          ],
                        ),
                      ],
                    ),
                  ),
                  if (voteCount != null)
                    Text(
                      "$voteCount",
                      style: TextStyle(
                        fontSize: 14.sp,
                      ),
                    ),
                ],
              ),
            ),
          );
  }

  /// 按钮部分
  Widget _buildBtnSection() {
    if (controller.showVotedBtn) {
      return _buildVotedBtn();
    }
    if (controller.showVoteNowBtn) {
      return _buildVoteNowBtn();
    }
    if (controller.showAppliedBtn) {
      return _buildAppliedBtn();
    }
    if (controller.showApplyBtn) {
      return _buildApplyBtn();
    }
    return SizedBox.shrink();
  }

  Widget _buildVotedBtn() {
    return ElevatedButton(
      onPressed: null,
      style: ElevatedButton.styleFrom(
        disabledBackgroundColor: AppColors.colorFFCCE9F6,
      ),
      child: Text(
        L.vote_action_btn_voted.tr,
        style: TextStyle(color: AppColors.primaryBgColor1),
      ),
    );
  }

  Widget _buildVoteNowBtn() {
    return Obx(
      () => ElevatedButton(
        onPressed: controller.isTimerEnd.value
            ? null
            : controller.onSubmitVoteTap,
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primaryBgColor1,
          textStyle: TextStyle(color: AppColors.white),
        ),
        child: Text(L.submit_vote.tr),
      ),
    );
  }

  Widget _buildAppliedBtn() {
    return ElevatedButton(
      onPressed: null,
      style: ElevatedButton.styleFrom(
        disabledBackgroundColor: AppColors.colorFFCCE9F6,
      ),
      child: Text(
        L.vote_action_btn_participated.tr,
        style: TextStyle(color: AppColors.primaryBgColor1),
      ),
    );
  }

  Widget _buildApplyBtn() {
    return Obx(
      () => ElevatedButton(
        onPressed: controller.isTimerEnd.value
            ? null
            : controller.onApplyToBeLeaderTap,
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primaryBgColor1,
          textStyle: TextStyle(color: AppColors.white),
        ),
        child: Text(L.apply_to_be_the_next_leader.tr),
      ),
    );
  }
}
