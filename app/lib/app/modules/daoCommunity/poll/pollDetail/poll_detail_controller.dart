import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_metatel/app/modules/daoCommunity/widgets/poll_action_confirmation.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../../core/languages/l.dart';
import '../../../../../core/values/config.dart';
import '../../../../data/models/poll.dart';
import '../../../../data/models/role.dart';
import '../../../../data/providers/api/channel.dart';
import '../../../../data/services/channel_service.dart';
import '../../../../data/services/config_service.dart';
import '../../../message/message_controller.dart';

class PollDetailController extends GetxController {
  final _msgController = Get.find<MessageController>();
  late Poll poll;
  double totalVoteCount = 0;
  RxnString selectedOptionId = RxnString();
  RxBool isTimerEnd = false.obs;
  RxInt timeRemainingInSec = 0.obs;
  RxInt timerSeconds = 0.obs;
  RxInt timerMinutes = 0.obs;
  RxInt timerHours = 0.obs;
  RxInt timerDays = 0.obs;
  late Timer timer;

  /// 按钮相关
  late bool showVoteNowBtn;
  late bool showVotedBtn;
  late bool showApplyBtn;
  late bool showAppliedBtn;

  /// Result相关
  late bool showResultSection;
  late bool showGeneralDaoResultSection;
  late bool showProposalResultSection;
  late bool showLeaderResultSection;

  /// 投票相关
  late bool showVoteSection;
  late bool showGeneralDaoVoteSection;
  late bool showProposalVoteSection;
  late bool showLeaderVoteSection;

  /// 发起人
  late bool showInitiator;

  /// 倒计时区
  late bool showCountdownSection;

  /// 参与者区(Leader)
  late bool showCandidateSection;

  /// 分割线
  late bool showDivider;

  /// UI判断
  late bool isApplyPeriod;
  late bool isApplied;
  late bool isVoted;

  /// 需要获取用户信息的列表
  List<String> _ids = [];
  /// Get Builder的Id
  List<String> _idsToRebuild = [];


  @override
  void onInit() {
    poll = Get.arguments['poll'];
    var username = Get.find<AppConfigService>().getUserName();

    if(poll.status != DaoPollStatus.ongoing) {
      /// 投票总量遍历option的total并想加
      totalVoteCount = poll.options!.fold(0, (previousValue, element) => previousValue + (element.total ?? 0));      
    }

    /// 判断是否为报名期间(Leader)
    var currentTime = DateTime.now().millisecondsSinceEpoch;
    isApplyPeriod = currentTime >= poll.registerAt!*1000 && currentTime < poll.startTime!*1000;
    
    /// 判断是否已报名(Leader)
    isApplied = isApplyPeriod && poll.category == DaoPollCategory.leader && poll.options != null && poll.options!.any((element) => element.label == username);

    /// 判断是否已投票
    isVoted = poll.votedMembers != null && poll.votedMembers!.any((element) => element.username == username);

    /// 设置用户所选择的选项，当voted时
    if (isVoted) {      
      selectedOptionId.value = poll.votedMembers!.firstWhere((element) => element.username == username).optionId;
    }

    /// 设置倒计时剩余秒数
    if (poll.status == DaoPollStatus.ongoing) {
      var targetTime = poll.endTime;
      if(isApplyPeriod) {
        targetTime = poll.startTime;
      }

      timeRemainingInSec.value = _calcTimeRemainingInSec(targetTime);
    }
    _setupView();
    _startTimer();
    super.onInit();
  }

  @override
  void onReady() {
    _getMemberInfoById(_ids);
    super.onReady();
  }

  @override
  void onClose() {
    timer.cancel();
    super.onClose();
  }

  void _setupView() {
    bool _isGeneralDao =
        poll.category == DaoPollCategory.general || poll.category == DaoPollCategory.dao;
    bool _isProposal = poll.category == DaoPollCategory.proposal;
    bool _isLeader = poll.category == DaoPollCategory.leader;

    /// 按钮相关
    showVoteNowBtn =
        (_isGeneralDao || _isLeader) && poll.status == DaoPollStatus.ongoing && !isApplyPeriod && !isVoted;
    showVotedBtn =
        (_isGeneralDao || _isLeader) && poll.status == DaoPollStatus.ongoing && !isApplyPeriod && isVoted;
    showApplyBtn = _isLeader && poll.status == DaoPollStatus.ongoing && isApplyPeriod && !isApplied;
    showAppliedBtn = _isLeader && poll.status == DaoPollStatus.ongoing && isApplyPeriod && isApplied;

    /// Result相关
    showResultSection = poll.status != DaoPollStatus.ongoing;
    showGeneralDaoResultSection = showResultSection && _isGeneralDao;
    showProposalResultSection = showResultSection && _isProposal;
    showLeaderResultSection = showResultSection && _isLeader;

    /// 投票相关
    showVoteSection = poll.status == DaoPollStatus.ongoing && !isApplyPeriod;
    showGeneralDaoVoteSection = showVoteSection && _isGeneralDao;
    showProposalVoteSection = showVoteSection && _isProposal;
    showLeaderVoteSection = showVoteSection && _isLeader;

    /// 倒计时区
    showCountdownSection = poll.status == DaoPollStatus.ongoing;

    /// 参与者区(Leader)
    showCandidateSection = _isLeader && isApplyPeriod;

    /// 分割线
    showDivider = poll.status == DaoPollStatus.ongoing;
  }

  int _calcTimeRemainingInSec(int? endTime) {
    if (endTime == null) return 8553600; // 99 days
    try {
      DateTime targetDate = DateTime.fromMillisecondsSinceEpoch(endTime*1000);
      DateTime now = DateTime.now();
      Duration difference = targetDate.difference(now);
      return difference.inSeconds < 0 ? 0 : difference.inSeconds;
    } catch (e) {
      return 8553600; // 99 days
    }
  }

  String getNameById(String? id, {required String idToRebuild}) {
    if (id == null) return "";
    /// 如果id为自己，使用displayName, 避免出现nickname从contact拿时为"My Computer"问题
    if (id == Get.find<AppConfigService>().getUserName()) {
      return Get.find<AppConfigService>().getMySelfDisplayName();
    }
    String name = "";
    if(_msgController.ownInfo(id)?.nickname != null) {
      name = _msgController.ownInfo(id)!.nickname!;
    } else if(_msgController.ownInfo(id)?.displayname != null) {
      name = _msgController.ownInfo(id)!.displayname!;
    } else {
      /// 若displayname为空，返回7位id
      name = id.substring(0, 6);
      _ids.add(id);
      _idsToRebuild.add(idToRebuild);
    }
    return name;    
  }

  String getAvatarById(String? id) {
    if (id == null) return "";
    String avatar = _msgController.ownInfo(id)?.avatarPath ?? "";
    return avatar;
  }

  List<Role> getRolesById(String? id) {
    if (id == null) return [];
    List<Role> tags = [];
    /// Leader标签
    if(_msgController.isChannelOwner(id)) {
      tags.add(
        Role(tagId: DefaultDaoRoleId.LEADER, name: L.leader.tr, color: 0xFFC91A1A)
      );
    }
    /// Manager标签
    if(_msgController.isChannelAdmin(id)) {
      tags.add(
        Role(tagId: DefaultDaoRoleId.MANAGER, name: L.manager.tr, color: 0xFFC9831A)
      );
    }
    /// 从缓存中获取成员角色标签
    var mapTags = _msgController.getMapChannelTag();
    var mapMemberTags = _msgController.getMapChannelMemberTag();
    if(mapTags.isEmpty || mapMemberTags.isEmpty) return tags;
    var memberTags = mapMemberTags[id];
    if(memberTags==null || memberTags.isEmpty) return tags;
  
    /// 普通角色标签
    for(var memberTag in memberTags) {
      if(!mapTags.containsKey(memberTag)) continue;
      tags.add(mapTags[memberTag]!);
    }
    
    return tags;   
  }

  void onSubmitVoteTap() async {
    if (selectedOptionId.value == null) return;
    var result = await showModalBottomSheet(
      context: Get.context!,
      isScrollControlled: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: const Radius.circular(12).r,
          bottom: const Radius.circular(0).r,
        ),
      ),
      builder: (context) {
        return PollActionConfirmation(
          title: L.dao_vote_confirmation_title.tr,
          detail: L.dao_vote_confirmation_detail.tr,
          onConfirmTap: () async {
            Get.back(result: true);
          },
        );
      },
    );
    if (result == null) return;
    EasyLoading.show(maskType: EasyLoadingMaskType.black);
    bool isSuccess = await daoVoteSubmitRequest(poll.id ?? "", selectedOptionId.value ?? "");
    EasyLoading.dismiss();
    if(!isSuccess) return;
    Get.back(result: selectedOptionId.value);
  }

  void onProposalVoteSubmitTap(String? optionId) async {
    if (optionId == null) return;
    var result = await showModalBottomSheet(
      context: Get.context!,
      isScrollControlled: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: const Radius.circular(12).r,
          bottom: const Radius.circular(0).r,
        ),
      ),
      builder: (context) {
        return PollActionConfirmation(
          title: L.dao_vote_confirmation_title.tr,
          detail: L.dao_vote_confirmation_detail.tr,
          onConfirmTap: () async {
            Get.back(result: true);
          },
        );
      },
    );
    if (result == null) return;
    EasyLoading.show(maskType: EasyLoadingMaskType.black);    
    bool isSuccess = await daoVoteSubmitRequest(poll.id ?? "", optionId);
    EasyLoading.dismiss();
    if(!isSuccess) return;
    selectedOptionId.value = optionId;
    Get.back(result: selectedOptionId.value);
  }

  void onApplyToBeLeaderTap() async {
    var result = await showModalBottomSheet(
      context: Get.context!,
      isScrollControlled: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: const Radius.circular(12).r,
          bottom: const Radius.circular(0).r,
        ),
      ),
      builder: (context) {
        return PollActionConfirmation(
          title: L.apply_dao_leader_confirmation_title.tr,
          detail: L.apply_dao_leader_confirmation_detail.tr,
          onConfirmTap: () async {
            Get.back(result: true);
          },
        );
      },
    );
    if (result == null) return;
    EasyLoading.show(maskType: EasyLoadingMaskType.black);
    bool isSuccess = await daoLeaderRegisterRequest(poll.id ?? "");
    EasyLoading.dismiss();
    if(!isSuccess) return;
    Get.back(result: true);
  }

  void _startTimer() {
    _setTimeUI(timeRemainingInSec.value);
    timer = Timer.periodic(
      const Duration(seconds: 1),
      (Timer timer) {
        if (timeRemainingInSec.value == 0) {
          isTimerEnd.value = true;
          timer.cancel();
        } else {
          timeRemainingInSec.value--;
          _setTimeUI(timeRemainingInSec.value);
        }
      },
    );
  }

  void _setTimeUI(int sec) {
    int remaining = sec;
    timerDays.value = (remaining / 60 / 60 / 24).truncate();
    if (timerDays.value > 0) {
      remaining -= (timerDays.value * 24 * 60 * 60);
    }
    timerHours.value = (remaining / 60 / 60).truncate();
    if (timerHours.value > 0) {
      remaining -= (timerHours.value * 60 * 60);
    }
    timerMinutes.value = (remaining / 60).truncate();
    if (timerMinutes.value > 0) {
      remaining -= (timerMinutes.value * 60);
    }
    timerSeconds.value = remaining;
  }

  String getResultLabelById(String? id) {
    if (id == null || selectedOptionId.value == null || poll.options == null)
      return "";
    return poll.options!.firstWhere((element) => element.id == id).label ?? "";
  }

  PollOption? getResult() {
    if (poll.options == null || poll.options!.isEmpty) return null;
    
    /// options内total最高的为最终结果
    /// 若全部total为一样，则返回null
    final firstTotal = poll.options!.first.total;
    if(poll.options!.length > 1 && poll.options!.every((element) => element.total == firstTotal)) return null;
    var finalResult = poll.options!.reduce((value, element) => value.total! > element.total! ? value : element);
    
    return finalResult;
  }

  String getResultReason() {
    var requirement = (poll.total ?? 0)/2;
    return poll.status == DaoPollStatus.valid
      ? "*${L.vote_validity_reason.trParams({"requirement": "$requirement"})}"
      : poll.status == DaoPollStatus.invalid
          ? totalVoteCount >= requirement
              ? "*${L.vote_validity_reason_3.tr}"
              : "*${L.vote_validity_reason_2.trParams({"requirement": "$requirement"})}"
          : "";
  }

  /// 更新头像和用户名
  void _getMemberInfoById(List<String> ids) {    
    Get.find<ChannelService>().getMemberInfo(
      _msgController.currentUser?.userName ?? "",
      ids,
      callback: (value) {
        _updateOwnInfo(value);
      },
      avatarAsyncNotify: true,
    );
  }

  void _updateOwnInfo(List<MemberInfo>? memberInfos) {
    if (memberInfos == null) {
      return;
    }
    _msgController.updateOwnInfo(memberInfos);
    update(_idsToRebuild);
    _ids.clear();
    _idsToRebuild.clear();
  }
}
