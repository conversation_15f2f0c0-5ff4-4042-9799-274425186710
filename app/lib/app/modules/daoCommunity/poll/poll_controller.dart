import 'package:flutter/material.dart';
import 'package:flutter_metatel/core/utils/util.dart';
import 'package:flutter_metatel/core/values/config.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import '../../../../core/languages/l.dart';
import '../../../../routes/pages.dart';
import '../../../data/models/poll.dart';
import '../../../data/providers/api/channel.dart';
import '../../message/message_controller.dart';

class PollController extends GetxController with GetTickerProviderStateMixin {
  final _msgController = Get.find<MessageController>();
  late TabController tabController;
  RefreshController allRefreshController =
      RefreshController(initialRefresh: false);
  RefreshController onGoingRefreshController =
      RefreshController(initialRefresh: false);
  RefreshController expiredRefreshController =
      RefreshController(initialRefresh: false);
  RxInt _currentTabIndex = 0.obs;
  List<String> tabbarTitles = [
    L.vote_list_tab_all.tr,
    L.vote_list_tab_ongoing.tr,
    L.vote_list_tab_expired.tr,
  ];
  RxBool isLoading = false.obs;
  RxList<Poll> polls = <Poll>[].obs;
  RxList<Poll> ongoingPolls = <Poll>[].obs;
  RxList<Poll> expiredPolls = <Poll>[].obs;

  /// 从投票气泡点击进入
  // String? _pollIdToOpen;
  // bool _isPollOpen = false;

  @override
  void onInit() {
    _initTabController();
    // _pollIdToOpen = Get.arguments?['poll_id'];
    _getPoll(load: true);
    super.onInit();
  }

  @override
  void dispose() {
    tabController.dispose();
    super.dispose();
  }

  void _initTabController() {
    tabController = TabController(
      initialIndex: _currentTabIndex.value,
      length: tabbarTitles.length,
      vsync: this,
    );
    tabController.addListener(() {
      _currentTabIndex.value = tabController.index;
    });
  }

  Future<void> _getPoll({bool load = false}) async {
    try {
      if (load) {
        isLoading.value = true;
      }

      List<Poll>? _polls = await getDaoVotesRequest(_msgController.currentUser?.userName ?? '');
      if(_polls == null) {
        polls.clear();
        expiredPolls.clear();
        ongoingPolls.clear();
        if (load) {
          isLoading.value = false;
        }
        toast(L.load_failed_tip.tr);
        return;
      }
      var currentTime = DateTime.now().millisecondsSinceEpoch;
      
      /// 通过‘单个投票详情接口’获取vote_members
      for (var i = 0; i < _polls.length; i++) {
        /// 1. 只获取正在进行(ongoing)的vote_members
        /// 2. Leader报名期间，不需要获取vote_members
        bool isApplyPeriod = currentTime >= _polls[i].registerAt!*1000 && currentTime < _polls[i].startTime!*1000;
        if(_polls[i].status != DaoPollStatus.ongoing || (_polls[i].category == DaoPollCategory.leader && isApplyPeriod)) continue;
        var poll = await getDaoVoteRequest(_polls[i].id ?? '');
        if(poll == null) continue;
        _polls[i] = poll;
      }
      polls.value = _polls;
      
      _setLeaderPollData();
      
      expiredPolls.value = polls
          .where((item) => item.status != DaoPollStatus.ongoing)
          .toList();
      ongoingPolls.value = polls
          .where((item) => item.status == DaoPollStatus.ongoing)
          .toList();
      /// 更新悬浮图标数量
      if(Get.isRegistered<MessageController>()){
        Get.find<MessageController>().getDaoVotes();
      }    
      if (load) {
        isLoading.value = false;
      }
      /// 当从投票气泡点击进入时，跳转到投票详情页面
      // if(_isPollOpen == false){
      //   _isPollOpen = true;
      //   if(_pollIdToOpen != null){
      //     var poll = polls.firstWhereOrNull((element) => element.id == _pollIdToOpen);
      //     if(poll != null){
      //       goToVoteDetail(poll);
      //     }
      //   }
      // }
    } catch (e) {
      polls.clear();
      expiredPolls.clear();
      ongoingPolls.clear();
      if (load) {
        isLoading.value = false;
      }
      toast(L.load_failed_tip.tr);
    }
    
  }

  /// 设置Leader类型的poll的标题和描述
  void _setLeaderPollData() {
    for (var poll in polls) {
      if (poll.category == DaoPollCategory.leader) {
        poll.title = L.leader_poll_title.tr;
        poll.desc = L.leader_poll_desc.tr +
            "\n\n" +
            L.leader_poll_desc_duty.tr +
            "\n\n" +
            L.leader_poll_desc_right.tr +
            "\n\n" +
            L.leader_poll_desc_regulation.tr;
      }
    }
  }

  void onRefresh() async {
    await _getPoll(load: true);
    allRefreshController.refreshCompleted();
    onGoingRefreshController.refreshCompleted();
    expiredRefreshController.refreshCompleted();
  }

  bool isLeaderOrAdmin() {
    return _msgController.mySelfIsChannelAdminOrOwner();
  }

  void goToVoteDetail(Poll poll) async {
    await Get.toNamed(
      Routes.PollDetailView,
      arguments: {
        'poll': poll,
      },
    );
    // if (result == null) return;
    if(poll.status != DaoPollStatus.ongoing) return;
    _getPoll(load: true);  
  }

  void gotoCreatePoll(int voteType) async {
    var result = await Get.toNamed(
      Routes.CreatePollView,
      arguments: {
        'vote_type': voteType,
      },
    );
    if (result == null) return;
    if (!(result as bool)) return;
    _getPoll(load: true);  
  }
}
