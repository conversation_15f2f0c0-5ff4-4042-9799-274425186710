import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/modules/daoCommunity/poll/poll_controller.dart';
import 'package:flutter_metatel/core/values/config.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import '../../../../core/languages/l.dart';
import '../../../../core/values/colors.dart';
import '../../../../r.dart';
import '../../../data/models/poll.dart';
import '../widgets/poll_selector.dart';
import '../widgets/poll_tile.dart';

class PollView extends GetView<PollController> {
  const PollView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(L.poll.tr),
        centerTitle: true,
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    return Column(
      children: [
        if (controller.isLeaderOrAdmin())
          PollSelector(
            voteOptions: {
              L.general_vote.tr: () {
                controller.gotoCreatePoll(DaoPollCategory.general);
              },
              L.proposal_vote.tr: () {
                controller.gotoCreatePoll(DaoPollCategory.proposal);
              },
              L.dao_vote.tr: () {
                controller.gotoCreatePoll(DaoPollCategory.dao);
              },
            },
          ),
        SizedBox(height: 30.h),
        Container(
          width: 1.sw,
          padding: const EdgeInsets.symmetric(horizontal: 30).r,
          child: Text(
            L.what_is_going_on.tr,
            style: TextStyle(
              fontSize: 13.sp,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        SizedBox(height: 22.h),
        Expanded(
          child: Column(
            children: [
              _buildTabbar(),
              SizedBox(height: 4.r),
              Expanded(child: _buildTabbarView()),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildTabbar() {
    return Row(
      children: [
        TabBar(
          controller: controller.tabController,
          isScrollable: true,
          tabAlignment: TabAlignment.start,
          indicatorColor: AppColors.primaryBgColor1,
          indicatorPadding: EdgeInsets.symmetric(horizontal: 8.r),
          padding: EdgeInsets.symmetric(horizontal: 12.r),
          labelPadding: EdgeInsets.symmetric(vertical: 8.r, horizontal: 12.r),
          labelStyle: TextStyle(
            fontSize: 13.sp,
            fontWeight: FontWeight.w800,
            color: AppColors.primaryBgColor1,
          ),
          unselectedLabelStyle: TextStyle(
            fontSize: 13.sp,
            color: Colors.black,
            fontWeight: FontWeight.w400,
          ),
          tabs: [
            for (var item in controller.tabbarTitles)
              Center(
                child: Text(
                  item.tr,
                  style: TextStyle(fontSize: 14.sp),
                ),
              ),
          ],
        ),
      ],
    );
  }

  Widget _buildTabbarView() {
    return TabBarView(
      controller: controller.tabController,
      physics: BouncingScrollPhysics(),
      children: [
        /// All
        Obx(
          () => _buildTabbarViewItem(
            isLoading: controller.isLoading.value,
            polls: controller.polls,
            refreshController: controller.allRefreshController,
          ),
        ),

        /// Ongoing
        Obx(
          () => _buildTabbarViewItem(
            isLoading: controller.isLoading.value,
            polls: controller.ongoingPolls,
            refreshController: controller.onGoingRefreshController,
          ),
        ),

        /// Expired
        Obx(
          () => _buildTabbarViewItem(
            isLoading: controller.isLoading.value,
            polls: controller.expiredPolls,
            refreshController: controller.expiredRefreshController,
          ),
        ),
      ],
    );
  }

  Widget _buildTabbarViewItem({
    required bool isLoading,
    required RxList<Poll> polls,
    // required String emptyDesc,
    required RefreshController refreshController,
    // required Function()? onRefresh,
  }) {
    return isLoading
        ? _buildLoadingView()
        : SmartRefresher(
            controller: refreshController,
            onRefresh: () {
              controller.onRefresh();
            },
            header: WaterDropMaterialHeader(
              backgroundColor: AppColors.colorFFF8F8F8,
              color: AppColors.colorFF3474D0,
            ),
            child: !isLoading && polls.isEmpty
              ? _buildEmptyView(L.poll_is_empty.tr)
              : ListView.builder(
                  itemCount: polls.length,
                  physics: BouncingScrollPhysics(),
                  itemBuilder: (context, index) {
                    return Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        PollTile(
                          poll: polls[index],
                          onTap: () {
                            controller.goToVoteDetail(polls[index]);
                          },
                        ),
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 10).r,
                          child: Divider(thickness: 0.5, color: AppColors.colorFFE7E7E7,),
                        ),
                      ],
                    );
                  },
                ),
          );
  }

  Widget _buildEmptyView(String text) {
    return Center(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Image.asset(
            R.iconMeetingEmpty,
            width: 30.r,
            height: 30.r,
          ),
          SizedBox(height: 8.r),
          Text(
            text,
            style: TextStyle(
              fontSize: 11.sp,
              color: Colors.black,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingView() {
    return Center(
      child: SpinKitDualRing(
        size: 40.r,
        lineWidth: 4,
        color: AppColors.colorFF249ED9,
      ),
    );
  }
}
