import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_metatel/app/modules/nfc/wallet_input_pin_controller.dart';
import 'package:flutter_metatel/app/modules/password/pin/password_controller.dart';
import 'package:flutter_metatel/app/widgets/loading_view.dart';
import 'package:flutter_metatel/app/widgets/verification/verification_box.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_secure_keyboard/flutter_secure_keyboard.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:get/get.dart';

import '../../../../core/languages/l.dart';
import '../../../../core/values/colors.dart';

class WalletInputPinView extends StatefulWidget {
  const WalletInputPinView(
      {super.key,
        this.isDialog = false,
        this.canBack = false});

  final bool isDialog;
  final bool canBack;

  @override
  createState() => _WalletInputPinViewState();
}

class _WalletInputPinViewState extends State<WalletInputPinView> {
  final WalletInputPinController _controller = Get.put(WalletInputPinController());

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
        child: Stack(
          children: [
            GestureDetector(
              onTap: () {
                if (widget.canBack) {
                  if (widget.isDialog) {
                    SmartDialog.dismiss();
                  } else {
                    Get.back();
                  }
                }
              },
              child: WithSecureKeyboard(
                keyboardHeight: kKeyboardDefaultHeight.r,
                controller: _controller.secureKeyboardController,
                child: _buildContentView(),
              ),
            ),
            Obx(
                  () => Visibility(
                visible: _controller.isLoading.value,
                child: Container(
                  color: Colors.grey.withOpacity(0.5),
                  alignment: Alignment.center,
                  width: double.infinity,
                  height: double.infinity,
                  child: const LoadingView(),
                ),
              ),
            ),
          ],
        ),
        onWillPop: () async {
          return false;
        });
  }

  @override
  void initState() {
    SchedulerBinding.instance
        .addPostFrameCallback((_) => _controller.showSecureKeyBoardView());
    super.initState();
  }

  @override
  void dispose() {
    Get.delete<PassWordController>();
    super.dispose();
  }

  Widget _buildContentView() {
    return Container(
        color: AppColors.white,
        child: Padding(
          padding: const EdgeInsets.only(left: 16, right: 16),
          child: ListView(
            children: [
              Container(
                margin: const EdgeInsets.only(top: 20),
                alignment: Alignment.center,
                width: double.infinity,
                child: Text(
                  L.please_enter_pin.tr,
                  style:
                  TextStyle(fontSize: 16.sp, fontWeight: FontWeight.bold),
                ),
              ),
              SizedBox(
                height: 60.h,
              ),
              VerificationBox(
                count: 6,
                borderColor: Colors.grey,
                borderWidth: 1.r,
                key: verificationBoxKey03,
                focusBorderColor: Colors.lightBlue,
                textStyle: TextStyle(color: Colors.grey, fontSize: 20.sp),
                textEditingController: _controller.textEditingController,
                onSubmitted: (value, clean) async {
                  AppLogger.d("onSubmitted!!!");
                  Get.back(result: value);
                },
                onTap: (e) {
                  _controller.showSecureKeyBoardView();
                },
              ),
            ],
          ),
        ));
  }
}
