import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/widgets/verification/verification_box.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:flutter_metatel/core/utils/util.dart';
import 'package:flutter_secure_keyboard/flutter_secure_keyboard.dart';
import 'package:get/get.dart';

class WalletInputPinController extends GetxController {
  var isLoading = false.obs;
  final _secureKeyboardController = SecureKeyboardController();
  final TextEditingController _textEditingController = TextEditingController();
  final _focusNode = FocusNode();
  final _pinCodeTextFieldFocusNode = FocusNode();

  get focusNode => _focusNode;
  get pinCodeTextFieldFocusNode => _pinCodeTextFieldFocusNode;
  get secureKeyboardController => _secureKeyboardController;
  get textEditingController => _textEditingController;

  showSecureKeyBoardView() {
    AppLogger.d("showSecureKeyBoard");
    verificationBoxKey03.currentState?.onValueChange("");
    showSecureKeyBoard(_secureKeyboardController, (value) {
      verificationBoxKey03.currentState
          ?.onValueChange(String.fromCharCodes(value));
    });
  }
}
