import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_metatel/app/modules/password/pin/password_controller.dart';
import 'package:flutter_metatel/app/widgets/loading_view.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_secure_keyboard/flutter_secure_keyboard.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:get/get.dart';

import '../../../../core/languages/l.dart';
import '../../../../core/values/colors.dart';
import '../../../widgets/verification/verification_box.dart';

class PasswordView extends StatefulWidget {
  const PasswordView(
      {super.key,
        this.canDestruction = true,
        this.isDialog = false,
        this.canBack = false});

  final bool canDestruction;
  final bool isDialog;
  final bool canBack;

  @override
  createState() => _PasswordViewState();
}

class _PasswordViewState extends State<PasswordView> {
  final PassWordController _controller = Get.put(PassWordController());

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
        child: Stack(
          children: [
            GestureDetector(
              onTap: () {
                if (widget.canBack) {
                  if (widget.isDialog) {
                    SmartDialog.dismiss();
                  } else {
                    Get.back();
                  }
                }
              },
              child: WithSecureKeyboard(
                keyboardHeight: kKeyboardDefaultHeight.r,
                controller: _controller.secureKeyboardController,
                child: _buildContentView(),
              ),
            ),
            Obx(
                  () => Visibility(
                visible: _controller.isLoading.value,
                child: Container(
                  color: Colors.grey.withOpacity(0.5),
                  alignment: Alignment.center,
                  width: double.infinity,
                  height: double.infinity,
                  child: const LoadingView(),
                ),
              ),
            ),
          ],
        ),
        onWillPop: () async {
          return false;
        });
  }

  @override
  void initState() {
    SchedulerBinding.instance
        .addPostFrameCallback((_) => _controller.showSecureKeyBoardView());
    super.initState();
  }

  @override
  void dispose() {
    Get.delete<PassWordController>();
    super.dispose();
  }

  Widget _buildContentView() {
    return Container(
        color: AppColors.white,
        child: Padding(
          padding: const EdgeInsets.only(left: 16, right: 16),
          child: ListView(
            children: [
              Container(
                margin: const EdgeInsets.only(top: 20),
                alignment: Alignment.center,
                width: double.infinity,
                child: Text(
                  L.please_enter_pin.tr,
                  style:
                  TextStyle(fontSize: 16.sp, fontWeight: FontWeight.bold),
                ),
              ),
              SizedBox(
                height: 60.h,
              ),
              VerificationBox(
                count: 6,
                borderColor: Colors.grey,
                borderWidth: 1.r,
                key: verificationBoxKey03,
                focusBorderColor: Colors.lightBlue,
                textStyle: TextStyle(color: Colors.grey, fontSize: 20.sp),
                textEditingController: _controller.textEditingController,
                onSubmitted: (value, clean) async {
                  AppLogger.d("onSubmitted!!!");
                  _controller.onSubmit(value, widget.canDestruction, widget.isDialog);
                },
                onTap: (e) {
                  _controller.showSecureKeyBoardView();
                },
              ),
              // VerificationBox(
              //   obscureText: true,
              //   itemWidget: 40.h,
              //   itemHeight: 40.h,
              //   borderColor: Colors.grey,
              //   borderRadius: 0,
              //   borderWidth: 1.r,
              //   focusBorderColor: Colors.lightBlue,
              //   count: 6,
              //   cursorEndIndent: 10,
              //   key: verificationBoxKey,
              //   textEditingController: _controller.textEditingController,
              //   autoFocus: false,
              //   focusNode: _controller.focusNode,
              //   readOnly: true,
              //   type: VerificationBoxItemType.box,
              //   onSubmitted: (value) async {
              //     AppLogger.d("onSubmitted!!!");
              //     _controller.onSubmit(
              //         value, widget.canDestruction, widget.isDialog);
              //   },
              //   textStyle: TextStyle(color: Colors.grey, fontSize: 20.sp),
              // ),
            ],
          ),
        ));
  }
}
