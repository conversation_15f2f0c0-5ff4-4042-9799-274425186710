import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/data/services/database_service.dart';
import 'package:flutter_metatel/app/widgets/verification/verification_box.dart';
import 'package:flutter_metatel/core/task/private_task.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:flutter_metatel/core/utils/util.dart';
import 'package:flutter_secure_keyboard/flutter_secure_keyboard.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:get/get.dart';

import '../../../../core/languages/l.dart';
import '../../../../core/utils/pwd_util.dart';
import '../../../../routes/pages.dart';

class PassWordController extends GetxController {
  var isLoading = false.obs;
  final _secureKeyboardController = SecureKeyboardController();
  final TextEditingController _textEditingController = TextEditingController();
  final _focusNode = FocusNode();
  final _pinCodeTextFieldFocusNode = FocusNode();

  get focusNode => _focusNode;
  get pinCodeTextFieldFocusNode => _pinCodeTextFieldFocusNode;
  get secureKeyboardController => _secureKeyboardController;
  get textEditingController => _textEditingController;
  onSubmit(String key, bool canDestruction, bool isDialog) async {
    isLoading.value = true;
    var pwdType = await PwdUtil.getPwdType(key);
    AppLogger.d("pwdType==${pwdType.index}");
    switch (pwdType) {
      case PwdType.destruction:
        isLoading.value = false;
        if (canDestruction) {
          // AppLogger.d("clearAppData()");
          clearAppData();
        } else {
          toast(L.password_check_input_password_error.tr);
          showSecureKeyBoardView();
        }
        break;
      case PwdType.annotative:
        var mn = await PwdUtil.getAnnotativeWords(key);
        AppLogger.d("de mn==$mn");
        if (canDestruction && mn != null) {
          if (isDialog) {
            SmartDialog.dismiss(result: mn);
          } else {
            await Get.find<DatabaseService>().dbConnect();
            isLoading.value = false;
            Get.offNamed(Routes.HOME);
          }
        } else {
          if (isDialog) {
            isLoading.value = false;
            SmartDialog.dismiss(result: mn);
          } else {
            await Get.find<DatabaseService>().dbConnect();
            isLoading.value = false;
            Get.offNamed(Routes.HOME);
          }
        }
        break;
      case PwdType.none:
        isLoading.value = false;
        toast(L.password_check_input_password_error.tr);
        showSecureKeyBoardView();
        break;
      default:
        break;
    }
  }

  showSecureKeyBoardView() {
    AppLogger.d("showSecureKeyBoard");
    verificationBoxKey03.currentState?.onValueChange("");
    showSecureKeyBoard(_secureKeyboardController, (value) {
      verificationBoxKey03.currentState
          ?.onValueChange(String.fromCharCodes(value));
    });
  }
}
