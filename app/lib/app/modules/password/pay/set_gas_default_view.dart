import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:get/get.dart';

import '../../../../../core/languages/l.dart';
import '../../../../../core/utils/util.dart';
import '../../../../../core/values/colors.dart';
import '../../../../../core/values/config.dart';

class SetGasDefaultView extends StatelessWidget {
  const SetGasDefaultView({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      color: AppColors.transparent,
      alignment: Alignment.center,
      child: Container(
        constraints: BoxConstraints(
          minHeight: 325.r,
        ),
        padding: const EdgeInsets.only(left: 49, right: 49).r,
        decoration: BoxDecoration(
          borderRadius: const BorderRadius.all(Radius.circular(12)).r,
          color: AppColors.white,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            SizedBox(
              height: 30.r,
            ),
            Center(
              child: Text(
                L.set_as_default.tr,
                style: TextStyle(
                  fontSize: 18.sp,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            SizedBox(
              height: 25.r,
            ),
            Text(
              L.set_as_default_dailog_info.tr,
              style: TextStyle(color: AppColors.colorFF000000, fontSize: 15.sp),
            ),
            SizedBox(
              height: 16.r,
            ),
            Container(
              margin: EdgeInsets.only(top: 25.r),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  ElevatedButton(
                    onPressed: () {
                      SmartDialog.dismiss(result: true);
                    },
                    style: ElevatedButton.styleFrom(
                      fixedSize: Size(183.r, 44.r),
                    ),
                    child: Text(
                      L.confirm.tr,
                      style: TextStyle(fontSize: 16.sp, color: AppColors.white),
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(
              height: 17.r,
            ),
            Visibility(
              child: Container(
                margin: EdgeInsets.only(bottom: 31.r),
                child: GestureDetector(
                  onTap: () {
                    SmartDialog.dismiss();
                  },
                  child: Center(
                    child: Text(
                      L.cancel.tr,
                      style: TextStyle(
                        fontSize: 17.sp,
                        color: AppColors.colorFF249ED9,
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
