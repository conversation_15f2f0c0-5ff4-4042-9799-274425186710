import 'package:flutter/cupertino.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:flutter_metatel/core/utils/util.dart';
import 'package:flutter_web3/app/data/models/wallet_model.dart';
import 'package:flutter_web3/app/data/services/config_service.dart';
import 'package:flutter_web3/web3dart/src/core/amount.dart';
import 'package:get/get.dart';
import 'package:get/get_state_manager/src/simple/get_controllers.dart';

class GasSettingController extends GetxController {
  RxInt type = 0.obs;
  RxBool customizableValue = false.obs;
  RxDouble customValue = 0.0.obs;
  WalletModel? walletModel;
  RxInt customNumber = 1.obs;
  final TextEditingController controller = TextEditingController();

  clickType(int type) {
    this.type.value = type;
  }

  setWalletModel(WalletModel? walletModel) async {
    this.walletModel = walletModel;
    var chainId = walletModel?.chainId;
    AppLogger.d('setWalletModel chainId=$chainId');
    var isOpen =
        await Get.find<WalletConfigService>().getGasDefault('${chainId ?? ''}');
    if (isOpen == 'true') {
      type.value = 2;
      var defaultValue =
          await Get.find<WalletConfigService>().getGasValue('${chainId ?? ''}');
      customizableValue.value = true;
      if (defaultValue != null) {
        try {
          customNumber.value = int.parse(defaultValue ?? '');
          controller.text = customNumber.value.toString();
        } catch (e) {
          e.toString();
        }
      }
    }
    updateCustomValue();
  }

  updateCustomValue() {
    GasModel? gasModel = walletModel?.gasModel;
    num? gasLimit = gasModel?.gasLimit;
    num? gasPrice = gasModel?.gasPrice;

    if (gasLimit != null && gasPrice != null) {
      customValue.value = EtherAmount.inWei(
              BigInt.from(gasLimit * customNumber.value * gasPrice))
          .getValueInUnit(EtherUnit.ether);
    }
  }

  onchange(String? value) {
    if (value != null) {
      try {
        customNumber.value = int.parse(value ?? '');
        updateCustomValue();
      } catch (e) {
        AppLogger.e(e.toString());
      }
    }
  }

  setSubmit() {
    String value = '1';
    if (type.value == 0) {
      value = '1';
    } else if (type.value == 1) {
      value = '3';
    } else if (type.value == 2) {
      if (customNumber.value <= 0) {
        toast('error');
        return;
      }
      value = '${customNumber.value}';
    }
    if (type.value == 0 || type.value == 1) {
      Get.find<WalletConfigService>()
          .saveGasDefault('${walletModel?.chainId ?? ''}', 'false');
    }
    Get.find<WalletConfigService>()
        .saveGasValue('${walletModel?.chainId ?? ''}', value);
    Get.back(result: value);
  }
}
