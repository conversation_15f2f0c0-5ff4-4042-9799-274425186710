import 'package:flutter_metatel/core/languages/l.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_secure_keyboard/flutter_secure_keyboard.dart';
import 'package:flutter_web3/app/data/models/wallet_model.dart';
import 'package:get/get.dart';

class PayCardController extends GetxController {
  final _secureKeyboardController = SecureKeyboardController();

  get secureKeyboardController => _secureKeyboardController;

  var payHeight = 610.0.r.obs;

  bool showTransfer(String key) {
    return key == EthTransactionType.otherContract;
  }

  String getToDesc(String key, WalletModel? model) {
    String? desc = '';
    switch (key) {
      case EthTransactionType.transfer:
      case EthTransactionType.transferContract:
      case EthTransactionType.otherContract:
      case EthTransactionType.approveContract:
        desc = model?.to;
        break;
    }
    return desc ?? '';
  }

  payStateChanged(bool isPin) {
    if (isPin) {
      payHeight.value = 610.r;
    } else {
      payHeight.value = 600.r;
    }
  }

  String getToTitle(String key) {
    String to = '';
    switch (key) {
      case EthTransactionType.transfer:
        to = 'to:';
        break;
      case EthTransactionType.transferContract:
      case EthTransactionType.approveContract:
        to = 'to:';
        break;
      case EthTransactionType.otherContract:
        to = L.execute_the_contract.tr;
        break;
    }
    return to;
  }

  String getTransferAddressDesc(String key, WalletModel? model) {
    String? desc = '';
    switch (key) {
      case TrxTransactionType.triggerSmartContract:
        desc = model?.contractAdrress;
        break;
    }
    return desc ?? '';
  }

  String getResoureTitle(String key) {
    String to = '';
    switch (key) {
      case EthTransactionType.transfer:
      case EthTransactionType.transferContract:
      case EthTransactionType.approveContract:
        to = L.gas_fee_for_this_transaction.tr;
        break;
      case EthTransactionType.otherContract:
        to = L.min_gas.tr;
        break;
    }
    return to;
  }

  String getResoureDesc(String key, WalletModel? model) {
    String? desc = '';
    switch (key) {
      case EthTransactionType.transferContract:
      case EthTransactionType.transfer:
      case EthTransactionType.approveContract:
        desc = '${model?.gas} ${model?.gasSymbol?.toString() ?? ''}';
        break;
      case EthTransactionType.otherContract:
        desc = '${model?.minGas ?? model?.gas ?? ''} ${model?.gasSymbol ?? ''}';
        break;
    }
    return desc;
  }

  String getTradeDesc(String key, WalletModel? model) {
    String? desc = '';
    switch (key) {
      case EthTransactionType.transfer:
      case EthTransactionType.transferContract:
      case EthTransactionType.approveContract:
        desc = '${model?.value ?? ''} ${model?.symbol ?? ''}';
        break;
      case EthTransactionType.otherContract:
        desc = '${model?.maxGas ?? model?.gas ?? ''} ${model?.gasSymbol ?? ''}';
        break;
    }
    return desc;
  }

  // gas_fee_for_this_transaction ,payment_amount
  String getTradeTitle(String key) {
    String to = '';
    switch (key) {
      case EthTransactionType.transfer:
        to = L.payment_amount.tr;
        break;
      case EthTransactionType.transferContract:
        to = L.payment_amount.tr;
        break;
      case EthTransactionType.otherContract:
        to = L.max_gas.tr;
        break;
      case EthTransactionType.approveContract:
        to = L.approve_number.tr;
        break;
    }
    return to;
  }

}
