import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/modules/password/pay/card/password_verification_module_view.dart';
import 'package:flutter_metatel/app/modules/password/pay/card/pay_card_controller.dart';
import 'package:flutter_metatel/app/widgets/text_middle_overlow.dart';
import 'package:flutter_metatel/core/languages/l.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:flutter_metatel/core/values/colors.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_secure_keyboard/flutter_secure_keyboard.dart';
import 'package:flutter_web3/app/data/models/wallet_model.dart';
import 'package:flutter_web3/r.dart' as web3R;
import 'package:get/get.dart';

class PayCardPwdPage extends StatefulWidget {
  const PayCardPwdPage({super.key, required this.walletModel});

  final WalletModel? walletModel;

  @override
  State<StatefulWidget> createState() => _PayCardPwdPageState();
}

class _PayCardPwdPageState extends State<PayCardPwdPage>
    with WidgetsBindingObserver {
  final PayCardController _controller = Get.put(PayCardController());

  @override
  void dispose() {
    Get.delete<PayCardController>();
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    var model = widget.walletModel;
    var transactionType = model?.transactionType;
    AppLogger.d('transactionType==$transactionType');
    String to = _controller.getToTitle(transactionType ?? '');
    var toDesc = _controller.getToDesc(transactionType ?? '', model);

    var tradeTitle = _controller.getTradeTitle(transactionType ?? '');
    var tradeDesc = _controller.getTradeDesc(transactionType ?? '', model);

    var resoureTitle = _controller.getResoureTitle(transactionType ?? '');
    var resoureDesc = _controller.getResoureDesc(transactionType ?? '', model);
    return Obx(
      () => Container(
        height:
            widget.walletModel == null ? 420.r : _controller.payHeight.value,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.only(
                  topLeft: const Radius.circular(12).r,
                  topRight: const Radius.circular(12).r)
              .r,
          color: AppColors.white,
        ),
        child: WithSecureKeyboard(
          keyboardHeight: kKeyboardDefaultHeight.r,
          controller: _controller.secureKeyboardController,
          child: Padding(
            padding: const EdgeInsets.only(
              left: 16,
              right: 16,
            ).r,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(height:10.r),
                ///钱包信息
                Visibility(
                  visible: widget.walletModel != null,
                  child: Container(
                    height: 55.r,
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                        border: Border.all(
                            color: AppColors.colorFFF8F8F8, width: 0.5.r),
                        borderRadius:
                            const BorderRadius.all(Radius.circular(4)).r),
                    child: Padding(
                      padding: const EdgeInsets.only(
                              top: 5, bottom: 5, left: 10, right: 10)
                          .r,
                      child: Row(
                        children: [
                          Image.asset(
                            web3R.R.icCardImport,
                            width: 39.r,
                            height: 39.r,
                            package: "flutter_web3",
                          ),
                          const SizedBox(
                            width: 10,
                          ),
                          Expanded(
                              child: Column(
                            children: [
                              const Spacer(),
                              Row(
                                children: [
                                  Text(
                                    widget.walletModel?.name ?? "",
                                    style: TextStyle(
                                        fontSize: 14.sp,
                                        fontWeight: FontWeight.bold,
                                        color: AppColors.colorFF333333),
                                  ),
                                  SizedBox(
                                    width: 15.r,
                                  ),
                                  Expanded(
                                    child: MiddleText(
                                      "(${widget.walletModel?.address ?? " "})",
                                      WXTextOverflow.ellipsisMiddle,
                                      style: TextStyle(
                                        fontSize: 12.sp,
                                        color: AppColors.colorFF666666,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                              const Spacer(),
                              Row(
                                children: [
                                  Text(
                                    "${L.balance.tr}:",
                                    style: TextStyle(
                                        fontSize: 14.sp,
                                        fontWeight: FontWeight.bold,
                                        color: AppColors.colorFF333333),
                                  ),
                                  SizedBox(
                                    width: 15.r,
                                  ),
                                  Text(
                                    "(${widget.walletModel?.balance ?? ""} ${widget.walletModel?.symbol ?? ''})",
                                    style: TextStyle(
                                      fontSize: 12.sp,
                                      color: AppColors.colorFF666666,
                                    ),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  )
                                ],
                              ),
                              const Spacer(),
                            ],
                          ))
                        ],
                      ),
                    ),
                  ),
                ),
                SizedBox(
                  height: 5.r,
                ),

                //转账、其他合约信息
                Visibility(
                  visible: widget.walletModel != null,
                  child: Container(
                    height: 55.r,
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                        border: Border.all(
                            color: AppColors.colorFFF8F8F8, width: 0.5.r),
                        borderRadius:
                            const BorderRadius.all(Radius.circular(4)).r),
                    child: Padding(
                      padding: const EdgeInsets.only(
                              top: 5, bottom: 5, left: 10, right: 10)
                          .r,
                      child: Column(
                        children: [
                          const Spacer(),
                          Row(
                            children: [
                              Text(
                                to,
                                style: TextStyle(
                                  fontSize: 14.sp,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              SizedBox(
                                width: 10.r,
                              ),
                              Expanded(
                                child: MiddleText(
                                  toDesc,
                                  WXTextOverflow.ellipsisMiddle,
                                  textAlign: TextAlign.right,
                                  style: TextStyle(
                                    fontSize: 12.sp,
                                    color: AppColors.colorFF666666,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          const Spacer(),
                        ],
                      ),
                    ),
                  ),
                ),
                SizedBox(
                  height: 5.r,
                ),

                ///交易gas
                Visibility(
                  visible: widget.walletModel != null,
                  child: Container(
                    height: 65.r,
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                        border: Border.all(
                            color: AppColors.colorFFF8F8F8, width: 0.5.r),
                        borderRadius:
                            const BorderRadius.all(Radius.circular(4)).r),
                    child: Padding(
                      padding: const EdgeInsets.only(
                              top: 5, bottom: 5, left: 10, right: 10)
                          .r,
                      child: Column(
                        children: [
                          const Spacer(),
                          Row(
                            children: [
                              Text(
                                tradeTitle,
                                style: TextStyle(
                                    fontSize: 14.sp,
                                    fontWeight: FontWeight.bold,
                                    color: AppColors.colorFF333333),
                              ),
                              Expanded(
                                child: Text(
                                  tradeDesc,
                                  style: TextStyle(
                                    fontSize: 12.sp,
                                    color: AppColors.colorFF333333,
                                  ),
                                  textAlign: TextAlign.right,
                                ),
                              ),
                            ],
                          ),
                          const Spacer(),
                          Row(
                            children: [
                              Text(
                                resoureTitle,
                                style: TextStyle(
                                  fontSize: 14.sp,
                                  color: AppColors.colorFF333333,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              Expanded(
                                  child: Text(
                                resoureDesc,
                                style: TextStyle(
                                  fontSize: 13.sp,
                                  color: AppColors.colorFF333333,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                                textAlign: TextAlign.right,
                              )),
                            ],
                          ),
                          const Spacer(),
                        ],
                      ),
                    ),
                  ),
                ),
                PasswordVerificationModuleView(_controller.secureKeyboardController,
                  model?.paymentType ?? 0,payStateChanged: ( b) {
                      _controller.payStateChanged(b);
                    },model?.address??'',model?.index_??0),
                // Visibility(
                //   visible: _controller.isPin.value,
                //   child: SizedBox(
                //     height: 45,
                //     child: VerificationBox(
                //       obscureText: true,
                //       borderColor: Colors.grey,
                //       borderWidth: 1.r,
                //       key: verificationBoxKey,
                //       focusBorderColor: Colors.lightBlue,
                //       textEditingController: _controller.textEditingController,
                //       count: 6,
                //       type: VerificationBoxItemType.box,
                //       onSubmitted: (value, clean) async {
                //         AppLogger.d("onSubmitted!!!");
                //         _controller.onSubmit(value);
                //       },
                //       onTap: (e) {
                //         _controller.showSecureKeyBoardView();
                //       },
                //       textStyle: TextStyle(color: Colors.grey, fontSize: 20.sp),
                //     ),
                //   ),
                // ),
                // Visibility(
                //   visible: !_controller.isPin.value,
                //   child: Container(
                //     height: 273.r,
                //     alignment: Alignment.center,
                //     decoration: BoxDecoration(
                //         border: Border.all(
                //             color: AppColors.colorFFE6E6E6, width: 0.5.r),
                //         borderRadius:
                //             const BorderRadius.all(Radius.circular(4)).r),
                //     child: Padding(
                //       padding: const EdgeInsets.only(
                //               top: 5, bottom: 5, left: 10, right: 10)
                //           .r,
                //       child: Column(
                //         children: [
                //           const Spacer(),
                //           Text(
                //             L.please_confirm_the_fingerprint_signature_on_the_hardware_card_wallet
                //                 .tr,
                //             style: TextStyle(
                //                 fontSize: 12.sp,
                //                 color: AppColors.colorFFFF0000),
                //           ),
                //           const Spacer(),
                //         Image.asset(
                //           web3R.R.gifCardScan,
                //           height: 189.r,
                //           width: 173.r,
                //           package: "flutter_web3",
                //
                //         ),
                //           const Spacer(),
                //         ],
                //       ),
                //     ),
                //   ),
                // ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
