import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/modules/password/pay/card/pay_card_verification_model_controller.dart';
import 'package:flutter_metatel/app/widgets/verification/verification_box.dart';
import 'package:flutter_metatel/app/widgets/verification/verification_box_item.dart';
import 'package:flutter_metatel/core/languages/l.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:flutter_metatel/core/values/colors.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_secure_keyboard/flutter_secure_keyboard.dart';
import 'package:flutter_web3/app/core/values/config.dart';
import 'package:get/get.dart';
import 'package:flutter_web3/r.dart' as web3R;

class PasswordVerificationModuleView extends StatefulWidget {
  const PasswordVerificationModuleView(
      this.secureKeyboardController, this.paymentType, this.address, this.index,
      {super.key, this.payStateChanged});

  final SecureKeyboardController secureKeyboardController;
  final int paymentType;
  final ValueChanged? payStateChanged;
  final String address;
  final int index;

  @override
  State<StatefulWidget> createState() {
    return PasswordVerificationModuleViewSate();
  }
}

class PasswordVerificationModuleViewSate
    extends State<PasswordVerificationModuleView> {
  final PayCardVerificationModelController _controller =
      Get.put(PayCardVerificationModelController());

  @override
  void initState() {
    _controller.setSecureKeyboardController(
        widget.secureKeyboardController,
        widget.paymentType,
        widget.address,
        widget.index,
        widget.payStateChanged);
    super.initState();
  }

  getIsPin() {
    return _controller.isPin;
  }

  changePayState() {
    _controller.changePayState();
  }

  @override
  void dispose() {
    Get.delete<PayCardVerificationModelController>();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      int type = _controller.cardTradeType.value;
      if (type == CardTradeType.def) {
        return _createPayView();
      }
      return _createErrorView();
    });
  }

  Widget _createErrorView() {
    int type = _controller.cardTradeType.value;
    var img = web3R.R.icWalletCardScan;
    var tips = '', tipsError = '', title = L.tips.tr;
    double hw = 81.r;
    int remainingNum = _controller.remainingNum ?? 0;
    int used = 6 - remainingNum;
    if (type == CardTradeType.addressTradeError) {
      img = web3R.R.icWalletAddressDoesNotMatch;
      tips = L
          .the_wallet_address_does_not_match_please_select_the_correct_wallet
          .tr;
    } else if (type == CardTradeType.pinTradeError) {
      img = web3R.R.icWalletPinVerificationFailed;
      tips = L.note_pin_error.tr;
      tipsError = L.your_card_wallet_has_failed_left.tr;
    } else if (type == CardTradeType.pinTradeLocked) {
      img = web3R.R.icWalletPinIsLocked;
      tips = L.your_card_wallet_pin_verification_has_been_locked.tr;
    } else if (type == CardTradeType.pinTrade) {
      img = web3R.R.icWalletCardScanGif;
      title = L.ready_to_scan.tr;
      hw = 119.r;
    } else if (type == CardTradeType.pinTimeout) {
      img = web3R.R.icWalletCardScan;
      title = L.sensing_timeout.tr;
      tips = L.please_click_to_re_your_card_wallet.tr;
      hw = 119.r;
    }

    bool isPinTimeOut = type == CardTradeType.pinTimeout;

    return SizedBox(
      height: 375.r,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            margin: EdgeInsets.only(top: 20.r, bottom: 30.r),
            child: Center(
              child: Text(
                title,
                style: TextStyle(
                  fontSize: 18.sp,
                  color: AppColors.colorFF000000,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          Visibility(
            visible: type == CardTradeType.pinTrade,
            child: Container(
              margin: EdgeInsets.only(top: 0.r, bottom: 30.r),
              child: Center(
                child: Text(
                  L.please_bring_your_card_wallet_closer_for_scanning.tr,
                  style: TextStyle(
                      fontSize: 12.sp, color: AppColors.colorFF000000),
                ),
              ),
            ),
          ),
          Image.asset(
            img,
            width: hw,
            height: hw,
            package: "flutter_web3",
          ),
          Visibility(
            visible: tipsError.isNotEmpty,
            child: Container(
              margin: EdgeInsets.only(top: 20.r),
              child: RichText(
                text: TextSpan(children: [
                  TextSpan(
                      text: tipsError,
                      style: TextStyle(
                          fontSize: 14.sp, color: AppColors.colorFF000000)),
                  TextSpan(
                      text: '$used',
                      style: TextStyle(
                          fontSize: 14.sp, color: AppColors.colorFFFF0000)),
                  TextSpan(
                      text: L.your_card_wallet_has_failed_midl.tr,
                      style: TextStyle(
                          fontSize: 14.sp, color: AppColors.colorFF000000)),
                  TextSpan(
                      text: '$remainingNum',
                      style: TextStyle(
                          fontSize: 14.sp, color: AppColors.colorFF3474d1)),
                  TextSpan(
                      text: L.your_card_wallet_has_failed_right.tr,
                      style: TextStyle(
                          fontSize: 14.sp, color: AppColors.colorFF000000)),
                ]),
              ),
            ),
          ),
          Visibility(
            visible: tips.isNotEmpty,
            child: Container(
                margin: EdgeInsets.only(bottom: 20.r, top: 30.r),
                child: Text(
                  tips,
                  style: TextStyle(
                      fontSize: 12.sp, color: AppColors.colorFFFF0000),
                )),
          ),
          const Spacer(),
          Row(
            children: [
              SizedBox(
                width: 10.r,
              ),
              Expanded(
                child: ElevatedButton(
                  onPressed: () {
                    if(isPinTimeOut){
                      _controller.onSubmit();
                    } else {
                      Get.back();
                    }
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: isPinTimeOut ? AppColors.colorFF3474d1 : AppColors.colorFFF2F2F2,
                    minimumSize: Size(double.infinity, 44.r),
                    shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(5.r)),
                  ),
                  child: Text(
                    isPinTimeOut ? L.retry.tr : L.cancel.tr,
                    style: TextStyle(
                        color: isPinTimeOut ? AppColors.white : AppColors.colorFF000000, fontSize: 16.sp),
                  ),
                ),
              ),
              Visibility(
                visible: tipsError.isNotEmpty,
                child: SizedBox(
                  width: 20.r,
                ),
              ),
              Visibility(
                visible: tipsError.isNotEmpty,
                child: Expanded(
                  child: GestureDetector(
                    onTap: () {},
                    child: ElevatedButton(
                      onPressed: () {
                        _controller.pinContinue();
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.colorFF3474d1,
                        minimumSize: Size(double.infinity, 44.r),
                        shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(5.r)),
                      ),
                      child: Text(
                        L.continue_.tr,
                        style:
                            TextStyle(color: AppColors.white, fontSize: 16.sp),
                      ),
                    ),
                  ),
                ),
              ),
              SizedBox(
                width: 10.r,
              ),
            ],
          ),
          const Spacer(),
        ],
      ),
    );
  }

  Widget _createPayView() {
    var isFingerprint = widget.paymentType == 1;

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: double.infinity,
          height: 50.r,
          alignment: Alignment.center,
          child: Stack(
            fit: StackFit.expand,
            alignment: Alignment.center,
            children: [
              Positioned(
                right: 0,
                left : 0,
                top: 15.r,
                child: Text(
                  _controller.title.value,
                  style: TextStyle(
                    color: AppColors.colorFF333333,
                    fontSize: 18.sp,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
              Obx(
                () => Visibility(
                  visible: isFingerprint,
                   child: Positioned(
                    top: 5.r,
                    right: 16.r,
                    child: GestureDetector(
                      onTap: () {
                        _controller.changePayState();
                      },
                      child: Text(
                        _controller.isPin.value
                            ? L.fingerprint_payment.tr
                            : L.pin_code_payment.tr,
                        style: TextStyle(
                          color: AppColors.appDefault,
                          fontSize: 14.sp,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
        Visibility(
            visible: _controller.isPin.value,
            child: Text(
              L.scan_successfully_and_confirm_with_fingerprint_or_pin_code.tr,
              style: TextStyle(fontSize: 12.sp, color: AppColors.colorFF000000),
            )),
        SizedBox(
          height: 15.r,
        ),
        Visibility(
          visible: _controller.isPin.value,
          child: SizedBox(
            height: 45,
            child: VerificationBox(
              obscureText: true,
              borderColor: Colors.grey,
              borderWidth: 1.r,
              key: verificationBoxKey,
              focusBorderColor: Colors.lightBlue,
              textEditingController: _controller.textEditingController,
              count: 6,
              type: VerificationBoxItemType.box,
              onSubmitted: (value, clean) async {
                AppLogger.d("onSubmitted!!!");
                _controller.onSubmit(key: value);
              },
              onTap: (e) {
                _controller.showSecureKeyBoardView();
              },
              textStyle: TextStyle(color: Colors.grey, fontSize: 20.sp),
            ),
          ),
        ),
        Visibility(
          visible: !_controller.isPin.value,
          child: Container(
            height: 273.r,
            alignment: Alignment.center,
            child: Padding(
              padding:
                  const EdgeInsets.only(top: 5, bottom: 5, left: 10, right: 10)
                      .r,
              child: Column(
                children: [
                  const Spacer(),
                  Text(
                    L.please_confirm_the_fingerprint_signature_on_the_hardware_card_wallet
                        .tr,
                    style: TextStyle(
                        fontSize: 12.sp, color: AppColors.colorFFFF0000),
                  ),
                  const Spacer(
                    flex: 2,
                  ),
                  Image.asset(
                    web3R.R.gifCardScan,
                    height: 189.r,
                    width: 173.r,
                    package: "flutter_web3",
                  ),
                  const Spacer(),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }
}
