import 'package:async_task/async_task_extension.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_metatel/app/widgets/verification/verification_box.dart';
import 'package:flutter_metatel/core/languages/l.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:flutter_metatel/core/utils/util.dart';
import 'package:flutter_secure_keyboard/flutter_secure_keyboard.dart';
import 'package:flutter_web3/app/core/utils/events_bus.dart';
import 'package:flutter_web3/app/core/values/config.dart';
import 'package:flutter_web3/app/nfc/consts/consts.dart';
import 'package:flutter_web3/app/nfc/events/nfc.dart';
import 'package:flutter_web3/app/nfc/logic/nfc_channel.dart';
import 'package:flutter_web3/app/nfc/logic/nfc_manage.dart';
import 'package:flutter_web3/web3dart/src/credentials/hdwallet.dart';
import 'package:get/get.dart';
import 'package:local_auth/local_auth.dart';

class PayCardVerificationModelController extends GetxController {
  late final SecureKeyboardController _secureKeyboardController;

  get secureKeyboardController => _secureKeyboardController;

  final TextEditingController _textEditingController = TextEditingController();

  TextEditingController get textEditingController => _textEditingController;

  final _focusNode = FocusNode();
  ValueChanged? payStateChanged;

  get focusNode => _focusNode;
  RxBool isPin = true.obs;
  String? address;
  late int index_;
  int? paymentType;
  int? remainingNum;

  final GlobalKey<SecureKeyboardState> _key = GlobalKey<SecureKeyboardState>();
  final LocalAuthentication auth = LocalAuthentication();

  String? _pinData;
  Timer? _fingerprintTimer;
  Timer? _pinPrintTimer;

  RxInt cardTradeType = CardTradeType.def.obs;

  StreamSubscription<Object>? subscription;

  setSecureKeyboardController(
      SecureKeyboardController secureKeyboardController,
      int? paymentType,
      String? address,
      int? index,
      ValueChanged? payStateChanged) {
    _secureKeyboardController = secureKeyboardController;
    this.paymentType = paymentType;
    this.payStateChanged = payStateChanged;
    this.address = address;
    index_ = index ?? 0;
  }

  var title = "".obs;

  @override
  void onInit() {
    super.onInit();
    title.value = getTitle();

    subscription =
        Get.find<WalletEventBus>().on<NfcConnectEvent>().listen((event) {
      var status = Get.find<NfcManageService>().initStatus;
      if (status >= 0) {
        var keyData = textEditingController.text;
        if (_pinData != null && keyData == _pinData && _pinData?.length == 6) {
          // onSubmit(_pinData ?? '');
        }
      }
    });
  }

  _changeTitle(bool isPin) {
    if (isPin) {
      title.value = L.please_enter_payment_password.tr;
    } else {
      title.value = L.please_use_fingerprint_to_pay.tr;
      _secureKeyboardController.hide();
    }
  }

  String getTitle() {
    return paymentType == 1
        ? L.please_use_fingerprint_to_pay.tr
        : L.please_enter_payment_password.tr;
  }

  @override
  void onClose() {
    super.onClose();
    subscription?.cancel();
    _stopFingerprintTimer();
  }

  showSecureKeyBoardView() {
    showSecureKeyBoard(key: _key, _secureKeyboardController, (value) {
      AppLogger.d("showSecureKeyBoard value=$value");

      verificationBoxKey.currentState
          ?.onValueChange(String.fromCharCodes(value));
    }, onCloseKeyPressed: () {
      Get.back(result: false);
    });
  }

  @override
  onReady() {
    super.onReady();
    configBiometricsType();
  }

  changePayState() {
    isPin.value = !isPin.value;
    payStateChanged?.call(isPin.value);
    _changeTitle(isPin.value);
    if (!isPin.value) {
      _secureKeyboardController.hide();
      startFingerprintTimer();
    } else {
      showSecureKeyBoardView();
    }
  }

  configBiometricsType() {
    showSecureKeyBoardView();
  }



  void onSubmit({String? key}) async {
    if(key!=null){
      _pinData = key;
    }
    cardTradeType.value = CardTradeType.pinTrade;
    _secureKeyboardController.hide();
    startPinPrintTimer();
  }

  //判断钱包地址跟卡片钱包地址是否一致
  Future<bool> nfcIsCardWalletConsistent() async {
    var publicKey = await NfcChannel().publicKey(index_);
    var hdw = HDWallet.fromHD(HDClass(publicKey, index_, paymentType));
    var addr = hdw.address.hex;
    var isSame = address == addr;
    return isSame;
  }

  startFingerprintTimer() {
    _fingerprintTimer?.cancel();
    _fingerprintTimer =
        Timer.periodic(const Duration(milliseconds: 2000), (timer) {
      if (isPin.value) {
        timer.cancel();
      }
      _fingerprint();
    });
  }

  int fingerprintIndex = 0;

  //指纹验证
  _fingerprint() async {
    var nfcManage = Get.find<NfcManageService>();
    var isConnected =
        await nfcManage.nfcIsConnected(showToast: fingerprintIndex % 2 == 0);
    if (isConnected) {
      if (await nfcIsCardWalletConsistent()) {
        var fingerprintIsOk = await nfcManage.fingerprintVerif(
            showToast: fingerprintIndex % 2 == 0);
        if (fingerprintIsOk) {
          Get.back(result: true);
        }
      } else {
        cardTradeType.value = CardTradeType.addressTradeError;
      }
    }
    fingerprintIndex++;
  }

  _stopFingerprintTimer() {
    _fingerprintTimer?.cancel();
    _fingerprintTimer = null;
  }

  //pin码验证
  _pinPrint() async {
    if (_pinData?.isEmpty ?? true) {
      return;
    }
    var nfcManage = Get.find<NfcManageService>();
    var isConnected = await nfcManage.nfcIsConnected(showToast: false);
    AppLogger.d('pinVerif isConnected=$isConnected');
    if (isConnected) {
      if (await nfcIsCardWalletConsistent()) {
        var code = await nfcManage.pinVerif(_pinData!, showToast: false);
        AppLogger.d('pinVerif code=$code');
        if (code == cardCodeSuccess) {
          Get.back(result: true);
        } else {
          if (code == cardCodeLocked) {
            cardTradeType.value = CardTradeType.pinTradeLocked;
          } else {
            if (code >= cardCodePinErrNum0 && code <= cardCodePinErrNum6) {
              remainingNum = code - cardCodePinErrNum0 - 1;
            }
            verificationBoxKey.currentState?.onValueChange("");
            cardTradeType.value = CardTradeType.pinTradeError;
          }
          _pinData = '';
        }
      } else {
        cardTradeType.value = CardTradeType.addressTradeError;
      }
      _stopPinPrintTimer();
    }
  }

  pinContinue() {
    cardTradeType.value = CardTradeType.def;
    showSecureKeyBoardView();
  }

  double _currentPinTime = 0;

  startPinPrintTimer() {
    _currentPinTime = DateTime.now().millisecondsSinceEpoch / 1000;
    _pinPrintTimer?.cancel();
    _pinPrintTimer =
        Timer.periodic(const Duration(milliseconds: 1500), (timer) {
      if (!isPin.value) {
        timer.cancel();
      } else {
        double time = DateTime.now().millisecondsSinceEpoch / 1000;
        AppLogger.d('startPinPrintTimer time=$time');
        AppLogger.d('startPinPrintTimer _currentPinTime=$_currentPinTime');

        if (time - _currentPinTime > 20) {
          cardTradeType.value = CardTradeType.pinTimeout;
          timer.cancel();
        } else {
          _pinPrint();
        }
      }
    });
  }

  _stopPinPrintTimer() {
    _pinPrintTimer?.cancel();
    _pinPrintTimer = null;
  }
}
