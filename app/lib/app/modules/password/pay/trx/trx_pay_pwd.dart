import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_metatel/app/modules/password/pay/trx/trx_pay_controller.dart';
import 'package:flutter_metatel/app/widgets/text_middle_overlow.dart';
import 'package:flutter_metatel/app/widgets/verification/verification_box.dart';
import 'package:flutter_metatel/app/widgets/verification/verification_box_item.dart';
import 'package:flutter_metatel/core/languages/l.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:flutter_metatel/core/values/colors.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_secure_keyboard/flutter_secure_keyboard.dart';
import 'package:flutter_web3/app/data/models/wallet_model.dart';
import 'package:flutter_web3/r.dart' as web3R;
import 'package:get/get.dart';

class TrxPayPwdPage extends StatefulWidget {
  const TrxPayPwdPage(
      {super.key, required this.walletModel, required this.title});

  final WalletModel? walletModel;
  final String title;

  @override
  State<StatefulWidget> createState() => _TrxPayPwdPageState();
}

class _TrxPayPwdPageState extends State<TrxPayPwdPage>
    with WidgetsBindingObserver {
  final TrxPayController _controller = Get.put(TrxPayController());

  @override
  void dispose() {
    Get.delete<TrxPayController>();
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    _controller.appState = state;
  }

  @override
  void initState() {
    _controller.titleWidget=widget.title;
    _controller.title.value=widget.title;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    var model = widget.walletModel;
    var transactionType = model?.transactionType;
    String to = _controller.getToTitle(transactionType ?? '');
    var toDesc = _controller.getToDesc(transactionType ?? '', model);
    var tradeTitle = _controller.getTradeTitle(transactionType ?? '');
    var tradeDesc = _controller.getTradeDesc(transactionType ?? '', model);
    var resoureTitle = _controller.getResoureTitle(transactionType ?? '');
    var resoureDesc = _controller.getResoureDesc(transactionType ?? '', model);
    bool showTransfer = _controller.showTransfer(transactionType??'');
    var transferAddress = _controller.getTransferAddressDesc(transactionType ?? '', model);
    return Obx(
      () => Container(
        height:
            widget.walletModel == null ? 420.h : _controller.payHeight.value,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.only(
                  topLeft: const Radius.circular(12).r,
                  topRight: const Radius.circular(12).r)
              .r,
          color: AppColors.white,
        ),
        child: WithSecureKeyboard(
          keyboardHeight: kKeyboardDefaultHeight.r,
          controller: _controller.secureKeyboardController,
          child: Padding(
            padding: const EdgeInsets.only(
              left: 16,
              right: 16,
            ).r,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  width: double.infinity,
                  height: 60,
                  alignment: Alignment.center,
                  child: Stack(
                    fit: StackFit.expand,
                    alignment: Alignment.center,
                    children: [
                      Positioned(
                        right: 0,
                        left: 0,
                        child: Text(
                          _controller.title.value,
                          style: TextStyle(
                            color: AppColors.colorFF333333,
                            fontSize: 18.sp,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                      Obx(
                        () => Visibility(
                          visible: _controller.biometricsType.value.isNotEmpty,
                          child: Positioned(
                            right: 0.w,
                            child: GestureDetector(
                              onTap: () {
                                _controller.onBiometrics();
                              },
                              child: Text(
                                _controller.biometricsType.value,
                                style: TextStyle(
                                  color: AppColors.appDefault,
                                  fontSize: 14.sp,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                ///钱包信息
                Visibility(
                  visible: widget.walletModel != null,
                  child: Container(
                    height: 55.r,
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                        border: Border.all(
                            color: AppColors.colorFFF8F8F8, width: 0.5.r),
                        borderRadius:
                            const BorderRadius.all(Radius.circular(4)).r),
                    child: Padding(
                      padding: const EdgeInsets.only(
                              top: 5, bottom: 5, left: 10, right: 10)
                          .r,
                      child: Row(
                        children: [
                          Image.asset(
                            web3R.R.icoWallet,
                            width: 39.r,
                            height: 39.r,
                            package: "flutter_web3",
                          ),
                          const SizedBox(
                            width: 10,
                          ),
                          Expanded(
                              child: Column(
                            children: [
                              const Spacer(),
                              Row(
                                children: [
                                  Text(
                                    widget.walletModel?.name ?? "",
                                    style: TextStyle(
                                        fontSize: 14.sp,
                                        fontWeight: FontWeight.bold,
                                        color: AppColors.colorFF333333),
                                  ),
                                  SizedBox(
                                    width: 15.r,
                                  ),
                                  Expanded(
                                    child: MiddleText(
                                      "(${widget.walletModel?.address ?? " "})",
                                      WXTextOverflow.ellipsisMiddle,
                                      style: TextStyle(
                                        fontSize: 12.sp,
                                        color: AppColors.colorFF666666,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                              const Spacer(),
                              Row(
                                children: [
                                  Text(
                                    "${L.balance.tr}:",
                                    style: TextStyle(
                                        fontSize: 14.sp,
                                        fontWeight: FontWeight.bold,
                                        color: AppColors.colorFF333333),
                                  ),
                                  SizedBox(
                                    width: 15.r,
                                  ),
                                  Text(
                                    "(${widget.walletModel?.balance ?? ""} ${widget.walletModel?.symbol ?? ''})",
                                    style: TextStyle(
                                      fontSize: 12.sp,
                                      color: AppColors.colorFF666666,
                                    ),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  )
                                ],
                              ),
                              const Spacer(),
                            ],
                          ))
                        ],
                      ),
                    ),
                  ),
                ),
                SizedBox(
                  height: 10.r,
                ),

                //转账、其他合约信息
                Visibility(
                  visible: widget.walletModel != null,
                  child: Container(
                    height: 55.r,
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                        border: Border.all(
                            color: AppColors.colorFFF8F8F8, width: 0.5.r),
                        borderRadius:
                            const BorderRadius.all(Radius.circular(4)).r),
                    child: Padding(
                      padding: const EdgeInsets.only(
                              top: 5, bottom: 5, left: 10, right: 10)
                          .r,
                      child: Column(
                        children: [
                          const Spacer(),
                          Row(
                            children: [
                              Text(
                                to,
                                style: TextStyle(
                                  fontSize: 14.sp,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              SizedBox(
                                width: 10.r,
                              ),
                              Expanded(
                                child: MiddleText(
                                  toDesc,
                                  WXTextOverflow.ellipsisMiddle,
                                  textAlign: TextAlign.right,
                                  style: TextStyle(
                                    fontSize: 12.sp,
                                    color: AppColors.colorFF666666,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          const Spacer(),
                          Visibility(
                              visible: showTransfer,
                              child: Row(
                                children: [
                                  Text(
                                    L.call_contract.tr,
                                    style: TextStyle(
                                      fontSize: 14.sp,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  SizedBox(
                                    width: 10.r,
                                  ),
                                  Expanded(
                                    child: MiddleText(
                                      transferAddress,
                                      WXTextOverflow.ellipsisMiddle,
                                      textAlign: TextAlign.right,
                                      style: TextStyle(
                                        fontSize: 12.sp,
                                        color: AppColors.colorFF666666,
                                      ),
                                    ),
                                  ),
                                ],
                              )),
                          const Spacer(),
                        ],
                      ),
                    ),
                  ),
                ),
                SizedBox(
                  height: 10.r,
                ),

                ///交易gas
                Visibility(
                  visible: widget.walletModel != null,
                  child: Container(
                    height: 65.r,
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                        border: Border.all(
                            color: AppColors.colorFFF8F8F8, width: 0.5.r),
                        borderRadius:
                            const BorderRadius.all(Radius.circular(4)).r),
                    child: Padding(
                      padding: const EdgeInsets.only(
                              top: 5, bottom: 5, left: 10, right: 10)
                          .r,
                      child: Column(
                        children: [
                          const Spacer(),
                          Row(
                            children: [
                              Text(
                                tradeTitle,
                                style: TextStyle(
                                    fontSize: 14.sp,
                                    fontWeight: FontWeight.bold,
                                    color: AppColors.colorFF333333),
                              ),
                              Expanded(
                                child: Text(
                                  tradeDesc,
                                  style: TextStyle(
                                    fontSize: 12.sp,
                                    color: AppColors.colorFF333333,
                                  ),
                                  textAlign: TextAlign.right,
                                ),
                              ),
                            ],
                          ),
                          const Spacer(),
                          Row(
                            children: [
                              Text(
                                resoureTitle,
                                style: TextStyle(
                                  fontSize: 14.sp,
                                  color: AppColors.colorFF333333,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              Expanded(
                                  child: Text(
                                resoureDesc,
                                style: TextStyle(
                                  fontSize: 13.sp,
                                  color: AppColors.colorFF333333,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                                textAlign: TextAlign.right,
                              )),
                            ],
                          ),
                          const Spacer(),
                        ],
                      ),
                    ),
                  ),
                ),
                Obx(
                  () => Visibility(
                    visible: _controller.title.value.isEmpty,
                    child: Container(
                      margin: EdgeInsets.only(top:14.r),
                      child: ElevatedButton(
                        onPressed: () {
                          _controller.onPay();
                        },
                        child: Text(
                          L.payment.tr,
                          style: TextStyle(
                            fontSize: 16.sp,
                          ),
                        ),
                      ),),
                  ),
                ),
                SizedBox(
                  height: 10.r,
                ),
                Visibility(
                  visible: _controller.title.isNotEmpty,
                  child: SizedBox(
                    height: 45,
                    child: VerificationBox(
                      obscureText: true,
                      borderColor: Colors.grey,
                      borderRadius: 0,
                      borderWidth: 1.r,
                      key: verificationBoxKey,
                      focusBorderColor: Colors.lightBlue,
                      count: 6,
                      type: VerificationBoxItemType.box,
                      onSubmitted: (value,clean) async {
                        AppLogger.d("onSubmitted!!!");
                        _controller.onSubmit(value);
                      },
                      onTap: (e) {
                        _controller.showSecureKeyBoardView();
                      },
                      textStyle: TextStyle(color: Colors.grey, fontSize: 20.sp),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
