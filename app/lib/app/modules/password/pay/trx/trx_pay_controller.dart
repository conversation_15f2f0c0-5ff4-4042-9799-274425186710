import 'package:flutter/cupertino.dart';
import 'package:flutter_metatel/app/data/enums/enum.dart';
import 'package:flutter_metatel/app/data/services/biometrics_service.dart';
import 'package:flutter_metatel/app/data/services/config_service.dart';
import 'package:flutter_metatel/app/data/services/secure_store_service.dart';
import 'package:flutter_metatel/app/widgets/verification/verification_box.dart';
import 'package:flutter_metatel/core/languages/l.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:flutter_metatel/core/utils/util.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_secure_keyboard/flutter_secure_keyboard.dart';
import 'package:flutter_web3/app/data/models/wallet_model.dart';
import 'package:get/get.dart';
import 'package:local_auth/local_auth.dart';

class TrxPayController extends GetxController {
  final _secureKeyboardController = SecureKeyboardController();

  get secureKeyboardController => _secureKeyboardController;

  final TextEditingController _textEditingController = TextEditingController();

  TextEditingController get textEditingController => _textEditingController;

  final _focusNode = FocusNode();

  get focusNode => _focusNode;
  final GlobalKey<SecureKeyboardState> _key = GlobalKey<SecureKeyboardState>();

  var biometricsType = "".obs;

  final LocalAuthentication auth = LocalAuthentication();
  AppLifecycleState appState = AppLifecycleState.resumed;
  var payHeight=540.0.h.obs;
  var title="".obs;
  late String titleWidget;
  PayIdentifyType payIdentifyType=Get.find<AppConfigService>().readPayIdentifyType();
  showSecureKeyBoardView() {
    AppLogger.d("showSecureKeyBoard");
    showSecureKeyBoard(key:_key,_secureKeyboardController, (value) {
      verificationBoxKey.currentState
          ?.onValueChange(String.fromCharCodes(value));
    }, onCloseKeyPressed: () {
      Get.back(result: false);
    });
  }

  void onSubmit(String key) async {
    String? walletPwd =
        await Get.find<SecureStoreService>().secureReadWalletPwd();
    bool result = false;
    if (walletPwd == null) {
      toast(L.no_wallet_password_need_set.tr);
      showSecureKeyBoardView();
      verificationBoxKey.currentState?.onValueChange("");
    } else if (walletPwd == key) {
      result = true;
      Get.find<AppConfigService>().savePayIdentifyType(payIdentifyType);
      Get.back(result: result);
    } else {
      verificationBoxKey.currentState?.onValueChange("");
      toast(L.wallet.tr + L.password_check_input_password_error.tr);
      _key.currentState?.cleanCodeData();
    }
  }
  configBiometricsType() async {
    var biometricsOpen = Get.find<AppConfigService>().biometricsOpen();
    var biometricsType =Get.find<BiometricsService>().biometricsType??"";
    PayIdentifyType payIdentifySupportType = Get.find<BiometricsService>().payIdentifySupportType;
    if(biometricsOpen??false){
      switch(payIdentifyType){
        case PayIdentifyType.password:
          this.biometricsType.value=biometricsType;
          title.value=titleWidget;
          payHeight.value=540.h;
          _secureKeyboardController.hide();
          showSecureKeyBoardView();
          break;
        case PayIdentifyType.fingerprint:
          if(payIdentifySupportType==PayIdentifyType.fingerprint){
            this.biometricsType.value=L.use_password.tr;
            payHeight.value=350.h;
            title.value="";
            _secureKeyboardController.hide();
          }
          break;
        case PayIdentifyType.faceId:
          if(payIdentifySupportType==PayIdentifyType.faceId){
            this.biometricsType.value=L.use_password.tr;
            payHeight.value=350.h;
            title.value="";
            _secureKeyboardController.hide();
          }
          break;
      }
    }else{
      showSecureKeyBoardView();
    }
  }
  @override
  void onInit() {
    super.onInit();
    AppLogger.d("onInit");
    configBiometricsType();

  }

  @override
  onReady(){
    super.onReady();
    configBiometricsType();
  }
  onBiometrics() async {
    if(biometricsType.value==L.fingerprint.tr){
      payIdentifyType=PayIdentifyType.fingerprint;
    }else if(biometricsType.value==L.face_id.tr){
      payIdentifyType=PayIdentifyType.faceId;
    }else if(biometricsType.value==L.use_password.tr){
      payIdentifyType=PayIdentifyType.password;
    }
    configBiometricsType();
  }
  onPay() async {
    bool result=await Get.find<BiometricsService>().onBiometrics();
    if(result){
      Get.find<AppConfigService>().savePayIdentifyType(payIdentifyType);
      Get.back(result: result);
    }else{
      payIdentifyType=PayIdentifyType.password;
      configBiometricsType();
    }
  }
  bool showTransfer(String key){
    return key==TrxTransactionType.triggerSmartContract;
  }

  String getToTitle(String key) {
    String to = '';
    switch (key) {
      case TrxTransactionType.triggerSmartContract:
      to = 'to:';
      break;
      case TrxTransactionType.transferContract:
        to = 'to:';
        break;
      case TrxTransactionType.freezeBalanceV2Contract:
        to = L.pledge_2.tr;
        break;
      case TrxTransactionType.unfreezeBalanceV2Contract:
        to = L.unlock_2.tr;
        break;
      case TrxTransactionType.transferAssetContract:
        to = L.TRC10_transfer.tr;
        break;
      case TrxTransactionType.voteWitnessContract:
        to = L.vote.tr;
        break;
    }
    return to;
  }

  String getTradeTitle(String key) {
    String to = '';
    switch (key) {
      case TrxTransactionType.triggerSmartContract:
        to = L.the_amount.tr;
        break;
      case TrxTransactionType.transferContract:
        to = L.handling_fee.tr;
        break;
      case TrxTransactionType.freezeBalanceV2Contract:
        to = L.get_voting_rights.tr;
        break;
      case TrxTransactionType.unfreezeBalanceV2Contract:
        to = L.reduced_voting_rights.tr;
        break;
      case TrxTransactionType.transferAssetContract:
        to = L.handling_fee.tr;
        break;
    }
    return to;
  }

  String getResoureTitle(String key) {
    String to = '';
    switch (key) {
      case TrxTransactionType.transferAssetContract:
      case TrxTransactionType.transferContract:
        to = L.payment_amount.tr;
        break;
      default:
        to = L.required_resources.tr;
        break;
    }
    return to;
  }

  String getTradeDesc(String key, WalletModel? model) {
    String? desc = '';
    switch (key) {
      case TrxTransactionType.triggerSmartContract:
        desc = '${model?.value ?? ''} ${model?.gasSymbol ?? ''}';
        break;
      case TrxTransactionType.transferContract:
      case TrxTransactionType.transferAssetContract:
        desc = '${model?.gas ?? ''} ${model?.gasSymbol ?? ''}';
        break;
      case TrxTransactionType.freezeBalanceV2Contract:
      case TrxTransactionType.unfreezeBalanceV2Contract:
        desc = '${model?.value?.toString() ?? ''}${L.ticket.tr}';
        break;
    }
    return desc;
  }
  String getTransferAddressDesc(String key, WalletModel? model) {
    String? desc = '';
    switch (key) {
      case TrxTransactionType.triggerSmartContract:
        desc = model?.contractAdrress;
      break;
    }
    return desc ?? '';
  }
  String getToDesc(String key, WalletModel? model) {
    String? desc = '';
    switch (key) {
      case TrxTransactionType.triggerSmartContract:
      // desc = model?.functionSelector;
      // break;
      case TrxTransactionType.transferContract:
      case TrxTransactionType.transferAssetContract:
        desc = model?.to;
        break;
      case TrxTransactionType.freezeBalanceV2Contract:
      case TrxTransactionType.unfreezeBalanceV2Contract:
        desc = '${L.main_total.tr}${model?.value ?? ''}${model?.gasSymbol}';
        break;
    }
    return desc ?? '';
  }

  String getResoureDesc(String key, WalletModel? model) {
    String? desc = '';
    switch (key) {
      case TrxTransactionType.transferContract:
      case TrxTransactionType.transferAssetContract:
        // desc = model?.gas.toString();
        desc = '${model?.value} ${model?.gasSymbol?.toString() ?? ''}';
        break;
      case TrxTransactionType.triggerSmartContract:
        // var r = L.energy.tr;
        desc =
            '${L.bandwidth.tr} ${model?.bandwidth ?? ''}+${L.energy.tr} ${model?.gas?.toString() ?? ''}';
        break;
      case TrxTransactionType.freezeBalanceV2Contract:
      case TrxTransactionType.unfreezeBalanceV2Contract:
        var r = L.bandwidth.tr;
        if (model?.resource == ResourceType.energy) {
          r = L.energy.tr;
        }
        desc = '${model?.gas?.toString() ?? ''}$r';
        break;
    }
    return desc;
  }
}
