
import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/modules/password/pay/gas_setting_controller.dart';
import 'package:flutter_metatel/app/modules/password/pay/pay_controller.dart';
import 'package:flutter_metatel/app/widgets/text_middle_overlow.dart';
import 'package:flutter_metatel/core/utils/util.dart';
import 'package:flutter_metatel/r.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_secure_keyboard/flutter_secure_keyboard.dart';
import 'package:flutter_web3/app/data/models/wallet_model.dart';
import 'package:flutter_web3/r.dart' as web3R;
import 'package:get/get.dart';

import '../../../../core/languages/l.dart';
import '../../../../core/utils/app_log.dart';
import '../../../../core/values/colors.dart';
import '../../../widgets/verification/verification_box.dart';
import '../../../widgets/verification/verification_box_item.dart';
import 'gas_setting.dart';

class PayPwdPage extends StatefulWidget {
  const PayPwdPage({super.key,required this.walletModel, required this.title});

  final WalletModel? walletModel;
  final String title;

  @override
  State<StatefulWidget> createState() => _PayPwdPageState();
}

class _PayPwdPageState extends State<PayPwdPage> with WidgetsBindingObserver{
  final PayController _controller = Get.put(PayController());

  @override
  void dispose() {
    Get.delete<PayController>();
    super.dispose();
  }

  @override
  void initState() {
    _controller.setData(widget.title,widget.walletModel);
    super.initState();
  }
  @override
  Widget build(BuildContext context) {
    var model = widget.walletModel;
    var transactionType = model?.transactionType;
    AppLogger.d('transactionType==$transactionType');
    String to = _controller.getToTitle(transactionType ?? '');
    var toDesc = _controller.getToDesc(transactionType ?? '', model);

    var tradeTitle = _controller.getTradeTitle(transactionType ?? '');
    var tradeDesc = _controller.getTradeDesc(transactionType ?? '', model);

    var resoureTitle = _controller.getResoureTitle(transactionType ?? '');
    var radius = const Radius.circular(10).r;
    var defaultHeightWidget = SizedBox(height: 14.r,);
    var leftStyle =  TextStyle(
        fontSize: 13.sp,
        fontWeight: FontWeight.w500,
       );
    var rightStyle =  TextStyle(
        fontSize: 13.sp,);
    double mainLeftOrRight = 16.r;

    return Obx(
      () => Container(
        height: _controller.payHeight.value,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.only(
                  topLeft: const Radius.circular(20).r,
                  topRight: const Radius.circular(20).r)
              .r,
          color: AppColors.white,
        ),
        child: WithSecureKeyboard(
          keyboardHeight: kKeyboardDefaultHeight.r,
          controller: _controller.secureKeyboardController,
          child: Padding(
            padding: EdgeInsets.only(
              left: 32.r,
              right: 32.r,
            ).r,
            child: SingleChildScrollView(
              physics: const BouncingScrollPhysics(),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    width: double.infinity,
                    height: 55.r,
                    alignment: Alignment.center,
                    child: Stack(
                      fit: StackFit.expand,
                      alignment: Alignment.center,
                      children: [
                        Positioned(
                          right: 0,
                          left: 0,
                          child: Text(
                            _controller.title.value,
                            style: TextStyle(
                              fontSize: 18.sp,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                        Obx(
                          () => Visibility(
                            visible: _controller.biometricsType.value.isNotEmpty,
                            child: Positioned(
                              right: 0.w,
                              child: GestureDetector(
                                onTap: () {
                                  _controller.onBiometrics();
                                },
                                child: Text(
                                  _controller.biometricsType.value,
                                  style: TextStyle(
                                    color: AppColors.appDefault,
                                    fontSize: 14.sp,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
              
                  ///钱包信息
                  Visibility(
                    visible: widget.walletModel != null,
                    child: Container(
                      height: 55.r,
                      alignment: Alignment.center,
                      decoration: BoxDecoration(
                          border: Border.all(
                              color: AppColors.colorFFF8F8F8, width: 0.5.r),
                          borderRadius:
                            BorderRadius.all(radius)),
                      child: Padding(
                        padding: EdgeInsets.only(
                                top: 5, bottom: 5, left: mainLeftOrRight, right: mainLeftOrRight)
                            .r,
                        child: Row(
                          children: [
                            Image.asset(
                              web3R.R.icoWallet,
                              width: 39.r,
                              height: 39.r,
                              package: "flutter_web3",
                            ),
                            const SizedBox(
                              width: 10,
                            ),
                            Expanded(
                                child: Column(
                              children: [
                                const Spacer(),
                                Row(
                                  children: [
                                    Text(
                                      widget.walletModel?.name ?? "",
                                      style: leftStyle,
                                    ),
                                    SizedBox(
                                      width: 15.r,
                                    ),
                                    Expanded(
                                      child: MiddleText(
                                        "(${widget.walletModel?.address ?? " "})",
                                        WXTextOverflow.ellipsisMiddle,
                                        style: rightStyle,
                                      ),
                                    ),
                                  ],
                                ),
                                const Spacer(),
                                Row(
                                  children: [
                                    Text(
                                      "${L.balance.tr}:",
                                      style: leftStyle,
                                    ),
                                    SizedBox(
                                      width: 15.r,
                                    ),
                                    Text(
                                      "(${widget.walletModel?.balance ?? ""} ${widget.walletModel?.symbol ?? ''})",
                                      style: rightStyle,
                                      maxLines: 1,
                                      overflow: TextOverflow.ellipsis,
                                    )
                                  ],
                                ),
                                const Spacer(),
                              ],
                            ))
                          ],
                        ),
                      ),
                    ),
                  ),
                  defaultHeightWidget,
                  //转账、其他合约信息
                  Visibility(
                    visible: widget.walletModel != null,
                    child: Container(
                      height: 42.r,
                      alignment: Alignment.center,
                      decoration: BoxDecoration(
                          border: Border.all(
                              color: AppColors.colorFFF8F8F8, width: 0.5.r),
                          borderRadius:
                               BorderRadius.all(radius)),
                      child: Padding(
                        padding: EdgeInsets.only(
                                top: 5, bottom: 5, left: mainLeftOrRight, right: mainLeftOrRight)
                            .r,
                        child: Column(
                          children: [
                            const Spacer(),
                            Row(
                              children: [
                                Text(
                                  to,
                                  style: leftStyle,
                                ),
                                SizedBox(
                                  width: 10.r,
                                ),
                                Expanded(
                                  child: MiddleText(
                                    toDesc,
                                    WXTextOverflow.ellipsisMiddle,
                                    textAlign: TextAlign.right,
                                    style: rightStyle,
                                  ),
                                ),
                              ],
                            ),
                            const Spacer(),
                          ],
                        ),
                      ),
                    ),
                  ),
                  defaultHeightWidget,
              
                  ///交易gas
                  Visibility(
                    visible: widget.walletModel != null,
                    child: Container(
                      alignment: Alignment.center,
                      height: 90.r,
                      decoration: BoxDecoration(
                          border: Border.all(
                              color: AppColors.colorFFF8F8F8,
                              width: 0.5.r),
                          borderRadius: BorderRadius.all(radius)),
                      child: Padding(
                        padding: EdgeInsets.only(
                            top: 5,
                            bottom: 5,
                            left: mainLeftOrRight,
                            right: mainLeftOrRight)
                            .r,
                        child: Column(
                          children: [
                            const SizedBox(height: 3,),
                            Row(
                              children: [
                                Text(
                                  tradeTitle,
                                  style: leftStyle,
                                ),
                                Expanded(
                                  child: Text(
                                    tradeDesc,
                                    style: rightStyle,
                                    textAlign: TextAlign.right,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 8,),
                            GestureDetector(
                              onTap: () async{
                                if (model?.gasModel != null &&
                                    model?.gasModel?.gasLimit != null) {
                                  var res = await Get.to(GasSettingPage(
                                    walletModel: model,
                                  ));
                                  if (res != null) {
                                    _controller.setGasPrice(res);
                                  }
                                }
                              },
                              child: Row(
                              children: [
                                Expanded(
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.end,
                                      children: [
                                        Row(
                                          children: [
                                            Text(
                                              resoureTitle,
                                              style: leftStyle,
                                            ),
                                          Expanded(
                                              child: Obx(() => Text(
                                                    _controller.tradeDesc.value,
                                                    style: rightStyle,
                                                    maxLines: 1,
                                                    overflow:
                                                        TextOverflow.ellipsis,
                                                    textAlign: TextAlign.right,
                                                  ))),
                                        ],
                                        ),
                                        Obx(() => Text(
                                          _controller.gasInfo.value,
                                          style: rightStyle,
                                          maxLines: 1,
                                          overflow: TextOverflow.ellipsis,
                                          textAlign: TextAlign.right,
                                        )),
                                      ],
                                    )),
                                const SizedBox(width: 8,),
                                Image.asset(
                                  R.nextArrowGrey,
                                  width: 6.w,
                                  height: 10.h,
                                )
                              ],
                            ),),
                          ],
                        ),
                      ),
                    ),
                  ),
              
                  Obx(
                    () => Visibility(
                      visible: _controller.title.value.isEmpty,
                      child: Container(
                        margin: EdgeInsets.only(top:14.r),
                        child: ElevatedButton(
                        onPressed: () {
                          _controller.onPay();
                        },
                        child: Text(
                          L.payment.tr,
                          style: TextStyle(
                            fontSize: 16.sp,
                          ),
                        ),
                      ),),
                    ),
                  ),
                  defaultHeightWidget,
                  Visibility(
                    visible: _controller.title.isNotEmpty,
                    child: SizedBox(
                      height: 45,
                      child: VerificationBox(
                        obscureText: true,
                        borderColor: Colors.grey,
                        borderWidth: 1.r,
                        key: verificationBoxKey,
                        focusBorderColor: Colors.lightBlue,
                        textEditingController: _controller.textEditingController,
                        count: 6,
                        type: VerificationBoxItemType.box,
                        onSubmitted: (value,clean) async {
                          AppLogger.d("onSubmitted!!!");
                          _controller.onSubmit(value);
                        },
                        onTap: (e) {
                          _controller.showSecureKeyBoardView();
                        },
                        textStyle: TextStyle(color: Colors.grey, fontSize: 20.sp),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
