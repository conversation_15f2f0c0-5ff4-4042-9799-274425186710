import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_metatel/app/modules/password/pay/gas_setting_controller.dart';
import 'package:flutter_metatel/app/modules/password/pay/set_gas_default_view.dart';
import 'package:flutter_metatel/app/widgets/divider_cus.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:flutter_web3/app/data/models/wallet_model.dart';
import 'package:flutter_web3/app/data/services/config_service.dart';
import 'package:flutter_web3/web3dart/src/utils/typed_data.dart';
import 'package:flutter_web3/web3dart/web3dart.dart';
import 'package:get/get.dart';

import '../../../../core/languages/l.dart';
import '../../../../core/values/colors.dart';

class GasSettingPage extends StatefulWidget {
  const GasSettingPage({super.key, required this.walletModel});

  final WalletModel? walletModel;

  @override
  State<StatefulWidget> createState() => _GasPageState();
}

class _GasPageState extends State<GasSettingPage> with WidgetsBindingObserver {
  final GasSettingController _controller = Get.put(GasSettingController());

  @override
  void dispose() {
    Get.delete<GasSettingController>();
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    _controller.setWalletModel(widget.walletModel);
  }

  @override
  Widget build(BuildContext context) {
    var model = widget.walletModel;
    GasModel? gasModel = model?.gasModel;
    num? gasLimit = gasModel?.gasLimit;
    num? gasPrice = gasModel?.gasPrice;

    var recommendationValue;
    var speedyValue;
    if (gasLimit != null && gasPrice != null) {
      recommendationValue = EtherAmount.inWei(BigInt.from(gasLimit * gasPrice))
          .getValueInUnit(EtherUnit.ether);
      speedyValue = recommendationValue * 3;
    }

    var radius = const Radius.circular(18).r;

    return Scaffold(
      backgroundColor: AppColors.color99000000,
      body: Column(
        children: [
          Flexible(
              child: Listener(
            behavior: HitTestBehavior.opaque,
            onPointerUp: (event) {
              Get.back();
            },
          )),
          Container(
            constraints: BoxConstraints(maxHeight: 0.9.sh),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.only(
                      topLeft: const Radius.circular(20).r,
                      topRight: const Radius.circular(20).r)
                  .r,
              color: AppColors.white,
            ),
            child: Padding(
              padding: EdgeInsets.only(
                left: 25.r,
                right: 25.r,
              ).r,
              child: SingleChildScrollView(
                physics: const BouncingScrollPhysics(),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      width: double.infinity,
                      padding: EdgeInsets.only(
                        top: 15.r,
                        bottom: 15.r,
                      ).r,
                      // height: 55.r,
                      alignment: Alignment.center,
                      child: Text(
                        L.select_gas_fee.tr,
                        style: TextStyle(
                          fontSize: 18.sp,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    Obx(() {
                      var type = _controller.type.value;
                      var titleStyle = TextStyle(
                          fontSize: 11.sp, color: AppColors.colorFF249ED9);
                      var bottomStyle = TextStyle(fontSize: 11.sp);
                      var middleStyle =
                          TextStyle(fontSize: 13.sp, fontWeight: FontWeight.w500);
                    
                      var titleSelectedStyle =
                          TextStyle(fontSize: 11.sp, color: AppColors.white);
                      var bottomSelectedStyle =
                          TextStyle(fontSize: 11.sp, color: AppColors.white);
                      var middleSelectedStyle = TextStyle(
                          fontSize: 13.sp,
                          color: AppColors.white,
                          fontWeight: FontWeight.w500);
                      AppLogger.d('_controller.type = $type');
                      return Row(
                        children: [
                          Expanded(
                            child: GestureDetector(
                              onTap: () {
                                _controller.clickType(0);
                              },
                              child: Container(
                                decoration: BoxDecoration(
                                    color:
                                        type == 0 ? AppColors.colorFF249ED9 : null,
                                    border: type != 0
                                        ? Border.all(
                                            color: AppColors.colorFFC7C7C7,
                                            width: 0.5.r)
                                        : null,
                                    borderRadius: BorderRadius.all(radius)),
                                margin: EdgeInsets.only(left: 3.r, right: 3.r),
                                padding: EdgeInsets.only(
                                    top: 0.r, bottom: 0.r, left: 14.r, right: 14.r),
                                // width: 0.303.sw,
                                height: 75.r,
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    FittedBox(
                                      child: Text(
                                        L.recommendation.tr,
                                        style: type == 0
                                            ? titleSelectedStyle
                                            : titleStyle,
                                      ),
                                    ),
                                    Text(
                                      '${decimalData(recommendationValue,7) ?? ''}${model?.gasSymbol ?? ''}',
                                      style: type == 0
                                          ? middleSelectedStyle
                                          : middleStyle,
                                    ),
                                    Text(
                                      L.recommendation_value.tr,
                                      style: type == 0
                                          ? bottomSelectedStyle
                                          : bottomStyle,
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                          Expanded(
                            child: GestureDetector(
                                onTap: () {
                                  _controller.clickType(1);
                                },
                                child: Container(
                                  decoration: BoxDecoration(
                                      color: type == 1
                                          ? AppColors.colorFF249ED9
                                          : null,
                                      border: type != 1
                                          ? Border.all(
                                              color: AppColors.colorFFC7C7C7,
                                              width: 0.5.r)
                                          : null,
                                      borderRadius: BorderRadius.all(radius)),
                                  margin: EdgeInsets.only(left: 3.r, right: 3.r),
                                  padding: EdgeInsets.only(
                                      top: 0.r, bottom: 0.r, left: 14.r, right: 14.r),
                                  // width: 0.303.sw,
                                  height: 75.r,
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      FittedBox(
                                        child: Text(
                                          L.speedy.tr,
                                          maxLines: 1,
                                          style: type == 1
                                              ? titleSelectedStyle
                                              : titleStyle,
                                        ),
                                      ),
                                      Text(
                                        '${decimalData(speedyValue,7) ?? ''}${model?.gasSymbol ?? ''}',
                                        maxLines: 1,
                                        style: type == 1
                                            ? middleSelectedStyle
                                            : middleStyle,
                                      ),
                                      Text(
                                        L.speedy_time.tr,
                                        maxLines: 1,
                                        style: type == 1
                                            ? bottomSelectedStyle
                                            : bottomStyle,
                                      ),
                                    ],
                                  ),
                                )),
                          ),
                          GestureDetector(
                              onTap: () {
                                _controller.clickType(2);
                              },
                              child: Container(
                                  decoration: BoxDecoration(
                                      color: type == 2
                                          ? AppColors.colorFF249ED9
                                          : null,
                                      border: type != 2
                                          ? Border.all(
                                              color: AppColors.colorFFC7C7C7,
                                              width: 0.5.r)
                                          : null,
                                      borderRadius: BorderRadius.all(radius)),
                                  margin: EdgeInsets.only(left: 3.r, right: 3.r),
                                  padding: EdgeInsets.only(
                                      top: 0.r,
                                      bottom: 0.r,
                                      left: 14.r,
                                      right: 14.r),
                                  // width: 0.187.sw,
                                  height: 75.r,
                                  child: Center(
                                    child: Text(
                                      L.custom.tr,
                                      style: type == 2
                                          ? titleSelectedStyle
                                          : titleStyle,
                                    ),
                                  ))),
                        ],
                      );
                    }),
                    SizedBox(
                      height: 20.r,
                    ),
                          
                    //自定义gas设置
                    Obx(() => Visibility(
                        visible: _controller.type.value == 2,
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const DividerCus(),
                            SizedBox(
                              height: 20.r,
                            ),
                            Text(
                              L.customizable.tr,
                              style: TextStyle(
                                  fontSize: 16.sp, fontWeight: FontWeight.bold),
                            ),
                            Row(
                              children: [
                                Text(
                                  L.set_as_default.tr,
                                  style: TextStyle(fontSize: 14.sp),
                                ),
                                const Spacer(),
                                Transform.scale(
                                  scale: 0.75,
                                  // 这里的 scale 值可以调整控件大小，1.0 是原始大小
                                  child: CupertinoSwitch(
                                    value: _controller.customizableValue.value,
                                    onChanged: (bool value) async {
                                      if (value) {
                                        var res = await showDialog();
                                        if (res == true) {
                                          _controller.customizableValue.value =
                                              value;
                                        }
                                      } else {
                                        _controller.customizableValue.value =
                                            value;
                                      }
                                      await Get.find<WalletConfigService>()
                                          .saveGasDefault(
                                              '${model?.chainId ?? ''}',
                                              _controller.customizableValue.value
                                                  .toString());
                                    },
                                  ),
                                )
                              ],
                            ),
                            SizedBox(
                              height: 15.r,
                            ),
                            Text(
                              L.gas_price.tr,
                              style: TextStyle(fontSize: 16.sp),
                            ),
                            SizedBox(
                              height: 44.r,
                              child: TextField(
                                onChanged: _controller.onchange,
                                keyboardType: const TextInputType.numberWithOptions(decimal: true),  // 支持数字和小数点
                                controller: _controller.controller,
                                inputFormatters: [
                                  FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}')),
                                ],
                                decoration: InputDecoration(
                                  hintText: '1',
                                  contentPadding:
                                      EdgeInsets.only(left: 3.r, top: 0.5),
                                  hintStyle: TextStyle(
                                    fontSize: 13.sp,
                                    color: AppColors.colorFFBBBBBB,
                                  ),
                                  focusedBorder: const UnderlineInputBorder(
                                    borderSide: BorderSide(
                                        color: AppColors.colorFF338dcc),
                                  ),
                                  enabledBorder: const UnderlineInputBorder(
                                      borderSide: BorderSide(
                                          color: AppColors.colorFFCBCBCB)),
                                ),
                              ),
                            ),
                            Container(
                              margin: EdgeInsets.only(
                                top: 15.r,
                                bottom: 6.r,
                              ),
                              decoration: BoxDecoration(
                                  border: Border.all(
                                      color: AppColors.colorFFF8F8F8, width: 1.r),
                                  borderRadius: BorderRadius.all(radius)),
                              padding: EdgeInsets.only(
                                  top: 6.r, bottom: 6.r, left: 15.r, right: 15.r),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    children: [
                                      Text(
                                        L.gas_limit.tr,
                                        style: TextStyle(fontSize: 14.sp),
                                      ),
                                      const Spacer(),
                                      Text(
                                        '${gasLimit}',
                                        style: TextStyle(fontSize: 14.sp),
                                      )
                                    ],
                                  ),
                                  SizedBox(
                                    height: 18.r,
                                  ),
                                  Row(
                                    children: [
                                      Text(
                                        L.fee.tr,
                                        style: TextStyle(fontSize: 14.sp),
                                      ),
                                      const Spacer(),
                                      Obx(() => Text(
                                            decimalData(_controller.customValue.value,7),
                                            style: TextStyle(fontSize: 14.sp),
                                          ))
                                    ],
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ))),
                    SizedBox(
                      height: 29.r,
                    ),
                    SizedBox(
                      width: 1.sw,
                      child: ElevatedButton(
                        onPressed: () {
                          _controller.setSubmit();
                        },
                        child: Text(
                          L.confirm.tr,
                          style: TextStyle(fontSize: 16.sp, color: AppColors.white),
                        ),
                      ),
                    ),
                    SizedBox(
                      height: 26.r,
                    ),
                    Container(
                      margin: EdgeInsets.only(bottom: 31.r),
                      child: GestureDetector(
                        onTap: () {
                          Get.back();
                        },
                        child: Center(
                          child: Text(
                            L.cancel.tr,
                            style: TextStyle(
                              fontSize: 17.sp,
                              color: AppColors.colorFF249ED9,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<bool?> showDialog() async {
    var ret = await SmartDialog.show(
        useAnimation: false,
        builder: (ctx) {
          return Scaffold(
            backgroundColor: AppColors.transparent,
            body: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 30).r,
              child: const SetGasDefaultView(),
            ),
          );
        });

    return ret;
  }
}
