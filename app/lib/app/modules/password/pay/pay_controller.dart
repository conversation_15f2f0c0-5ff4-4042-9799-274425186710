
import 'package:flutter/cupertino.dart';
import 'package:flutter_metatel/app/data/enums/enum.dart';
import 'package:flutter_metatel/app/data/services/secure_store_service.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_secure_keyboard/flutter_secure_keyboard.dart';
import 'package:flutter_web3/app/data/models/wallet_model.dart';
import 'package:flutter_web3/web3dart/src/core/amount.dart';
import 'package:flutter_web3/web3dart/src/utils/typed_data.dart';
import 'package:get/get.dart';
import 'package:local_auth/local_auth.dart';

import '../../../../core/languages/l.dart';
import '../../../../core/utils/app_log.dart';
import '../../../../core/utils/util.dart';
import '../../../data/services/biometrics_service.dart';
import '../../../data/services/config_service.dart';
import '../../../widgets/verification/verification_box.dart';

class PayController extends GetxController {
  final _secureKeyboardController = SecureKeyboardController();
  get secureKeyboardController => _secureKeyboardController;

  final TextEditingController _textEditingController = TextEditingController();
  TextEditingController get textEditingController => _textEditingController;

  final _focusNode = FocusNode();
  get focusNode => _focusNode;

  var biometricsType = "".obs;
  final GlobalKey<SecureKeyboardState> _key = GlobalKey<SecureKeyboardState>();
  final LocalAuthentication auth=LocalAuthentication();

  var payHeight=630.0.r.obs;
  var title="".obs;
  late String titleWidget;
  RxString gasInfo = ''.obs;
  RxString tradeDesc = ''.obs;
  WalletModel? walletModel;

  setData(String titleWidget,WalletModel? walletMode) {
    this.titleWidget = titleWidget;
    this.walletModel = walletMode;
    this.title.value = titleWidget;
    changeGasInfo();
    getResoureDesc();

  }

  changeGasInfo() {
    var key = walletModel?.transactionType;
    switch (key) {
      case EthTransactionType.transfer:
      case EthTransactionType.transferContract:
      case EthTransactionType.approveContract:
        // var gasprice = 1;
        var gasprice =  EtherAmount.fromUnitAndValue(EtherUnit.wei,BigInt.from((walletModel?.gasModel?.gasPrice ??0) * (walletModel?.gasModel?.multiple ?? 1.0))).getValueInUnit(EtherUnit.gwei);
        AppLogger.d('gasprice $gasprice');
        AppLogger.d('gasprice ${walletModel?.gasModel?.gasPrice}');
        var gas = decimalData(gasprice,7);
        gasInfo.value = L.gas_fee_detail_info.trParams({'gaslimit':'${walletModel?.gasModel?.gasLimit?.toInt()}','gasprice':'$gas'});
        break;
      case EthTransactionType.otherContract:
        // gasInfo.value = L.min_gas.tr;
        break;
    }
  }

  setGasPrice(String value) {
    try {
      int price = int.parse(value);
      if (price > 0) {
        walletModel?.gasModel?.multiple = price.toDouble();
        changeGasInfo();
        getResoureDesc();

      }
    } catch (e) {
      e.toString();
    }
  }

  PayIdentifyType payIdentifyType=Get.find<AppConfigService>().readPayIdentifyType();
  showSecureKeyBoardView() {
    showSecureKeyBoard(key:_key,_secureKeyboardController, (value) {
      AppLogger.d("showSecureKeyBoard value=$value");

      verificationBoxKey.currentState
          ?.onValueChange(String.fromCharCodes(value));
    }, onCloseKeyPressed: () {
      Get.back(result: false);
    });
  }

  configBiometricsType() {
    var biometricsOpen = Get.find<AppConfigService>().biometricsOpen();
    var biometricsType =Get.find<BiometricsService>().biometricsType??"";
    PayIdentifyType payIdentifySupportType = Get.find<BiometricsService>().payIdentifySupportType;
    if(biometricsOpen??false){
      switch(payIdentifyType){
        case PayIdentifyType.password:
          this.biometricsType.value=biometricsType;
          title.value=titleWidget;
          payHeight.value=630.r;
          showSecureKeyBoardView();
          break;
        case PayIdentifyType.fingerprint:
          if(payIdentifySupportType==PayIdentifyType.fingerprint){
            this.biometricsType.value=L.use_password.tr;
            payHeight.value=400.r;
            title.value="";
            _secureKeyboardController.hide();
          }
          break;
        case PayIdentifyType.faceId:
          if(payIdentifySupportType==PayIdentifyType.faceId){
            this.biometricsType.value=L.use_password.tr;
            payHeight.value=400.r;
            title.value="";
            _secureKeyboardController.hide();
          }
          break;
      }
    }else{
      showSecureKeyBoardView();
    }
  }
  bool showTransfer(String key){
    return key==EthTransactionType.otherContract;
  }
  String getToDesc(String key, WalletModel? model) {
    String? desc = '';
    switch (key) {
      case EthTransactionType.transfer:
      // desc = model?.functionSelector;
      // break;
      case EthTransactionType.transferContract:
      case EthTransactionType.otherContract:
      case EthTransactionType.approveContract:
      desc = model?.to;
      break;

    }
    return desc ?? '';
  }
  String getToTitle(String key) {
    String to = '';
    switch (key) {
      case EthTransactionType.transfer:
        to = 'to:';
        break;
      case EthTransactionType.transferContract:
      case EthTransactionType.approveContract:
        to = 'to:';
        break;
      case EthTransactionType.otherContract:
        to = L.execute_the_contract.tr;
        break;

    }
    return to;
  }

  String getTransferAddressDesc(String key, WalletModel? model) {
    String? desc = '';
    switch (key) {
      case TrxTransactionType.triggerSmartContract:
        desc = model?.contractAdrress;
        break;
    }
    return desc ?? '';
  }
  String getResoureTitle(String key) {
    String to = '';
    switch (key) {
      case EthTransactionType.transfer:
      case EthTransactionType.transferContract:
      case EthTransactionType.approveContract:
        to = L.gas_fee_for_this_transaction.tr;
        break;
      case EthTransactionType.otherContract:
        to = L.min_gas.tr;
        break;
    }
    return to;
  }

  String getResoureDesc() {
    String? desc = '';
    var key = walletModel?.transactionType;
    WalletModel? model = walletModel;
    switch (key) {
      case EthTransactionType.transferContract:
      case EthTransactionType.transfer:
      case EthTransactionType.approveContract:
      GasModel? gasModel = model?.gasModel;
      num? gasLimit = gasModel?.gasLimit;
      num? gasPrice = gasModel?.gasPrice;
      num? multiple = gasModel?.multiple;
      AppLogger.d('getResoureDesc multiple=$multiple');
      AppLogger.d('getResoureDesc gasPrice=$gasPrice');

      AppLogger.d('getResoureDesc gasLimit=$gasLimit');

      var gs;
        if (gasLimit != null && gasPrice != null && multiple != null) {
          gs = EtherAmount.inWei(BigInt.from(gasLimit * gasPrice * multiple))
              .getValueInUnit(EtherUnit.ether);
        } else {
          gs = model?.gas;
        }
        desc = '$gs ${model?.gasSymbol?.toString() ?? ''}';
        break;
      case EthTransactionType.otherContract:
        var gs = model?.minGas??model?.gas;
        desc = '${gs??''} ${model?.gasSymbol ?? ''}' ;
        break;

    }
    tradeDesc.value = desc;

    return desc;
  }
  String getTradeDesc(String key, WalletModel? model) {
    String? desc = '';
    switch (key) {
      case EthTransactionType.transfer:
      case EthTransactionType.transferContract:
      case EthTransactionType.approveContract:
        desc = '${model?.value ?? ''} ${model?.symbol ?? ''}';
        break;
      case EthTransactionType.otherContract:
        desc = '${model?.maxGas??model?.gas??''} ${model?.gasSymbol ?? ''}' ;
        break;
    }
    return desc;
  }
  // gas_fee_for_this_transaction ,payment_amount
  String getTradeTitle(String key) {
    String to = '';
    switch (key) {
      case EthTransactionType.transfer:
        to = L.payment_amount.tr;
        break;
      case EthTransactionType.transferContract:
        to = L.payment_amount.tr;
        break;
      case EthTransactionType.otherContract:
        to = L.max_gas.tr;
        break;
      case EthTransactionType.approveContract:
        to = L.approve_number.tr;
        break;

    }
    return to;
  }

  void onSubmit(String key) async {
    String? walletPwd =
        await Get.find<SecureStoreService>().secureReadWalletPwd();
    bool result = false;
    if (walletPwd == null) {
      toast(L.no_wallet_password_need_set.tr);
      showSecureKeyBoardView();
      verificationBoxKey.currentState?.onValueChange("");
    } else if (walletPwd == key) {
      result = true;
      Get.find<AppConfigService>().savePayIdentifyType(payIdentifyType);
      Get.back(result: result);
    } else {
      verificationBoxKey.currentState?.onValueChange("");
      toast(L.wallet.tr + L.password_check_input_password_error.tr);
      _key.currentState?.cleanCodeData();
    }
  }

  @override
  onReady(){
    super.onReady();
    configBiometricsType();
  }

  onBiometrics() async {
    if(biometricsType.value==L.fingerprint.tr){
      payIdentifyType=PayIdentifyType.fingerprint;
    }else if(biometricsType.value==L.face_id.tr){
      payIdentifyType=PayIdentifyType.faceId;
    }else if(biometricsType.value==L.use_password.tr){
      payIdentifyType=PayIdentifyType.password;
    }
    configBiometricsType();
  }
  onPay() async {
    bool result=await Get.find<BiometricsService>().onBiometrics();
    if(result){
      Get.find<AppConfigService>().savePayIdentifyType(payIdentifyType);
      Get.back(result: result);
    }else{
      payIdentifyType=PayIdentifyType.password;
      configBiometricsType();
    }
  }
}

