//FileName mining_chat_item
// <AUTHOR>
//@Date 2023/3/28 17:01
import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/data/models/res/mining/mining_invite.dart';
import 'package:flutter_metatel/app/widgets/text_middle_overlow.dart';
import 'package:flutter_metatel/core/values/colors.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../r.dart';
import '../../widgets/mavatar_circle_avatar.dart';

class MiningInviteNewItem extends StatelessWidget {
  const MiningInviteNewItem({this.data, super.key});

  final MiningInviteValue? data;

  @override
  Widget build(BuildContext context) {
    String title = data?.account ?? '';
    String number = '${data?.reward ?? ''}';
    return SizedBox(
      height: 67.r,
      child: Row(
        children: [
          MAvatarCircle(
            diameter: 44,
            textStyle: TextStyle(
              fontSize: 20.sp,
              color: Colors.white,
              fontWeight: FontWeight.normal,
            ),
            imagePath: R.iconMiningInviteHead,
            isNet:true,
          ),
          SizedBox(
            width: 11.r,
          ),
          Expanded(
            child: Column(
              mainAxisSize :MainAxisSize.min,
              crossAxisAlignment:CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: MiddleText(
                        title,
                        WXTextOverflow.ellipsisMiddle,
                        style: TextStyle(
                            fontSize: 16.sp,
                            color: Colors.black,
                            ),
                      ),
                    ),
                    Text(
                        '+$number',
                      style: TextStyle(
                          fontSize: 16.sp,
                          color: AppColors.colorFF3474d1,
                          ),
                    ),
                  ],
                ),
                // SizedBox(height: 8.r,),
                // Text(
                //   time,
                //   style: TextStyle(
                //       fontSize: 12.sp, color: AppColors.colorFF999999),
                // ),
              ],
            ),
          )
        ],
      ),
    );
  }
}
