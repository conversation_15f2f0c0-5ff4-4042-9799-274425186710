import 'dart:convert';

import 'package:async_task/async_task_extension.dart';
import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/data/models/res/base_res_model.dart';
import 'package:flutter_metatel/app/data/models/res/mining/mining_invite_level.dart';
import 'package:flutter_metatel/app/data/models/res/mining/mining_sign_in_query.dart';
import 'package:flutter_metatel/app/data/providers/api/invite.dart';
import 'package:flutter_metatel/app/data/services/config_service.dart';
import 'package:flutter_metatel/app/modules/mining/mining_pwd_util.dart';
import 'package:flutter_metatel/app/modules/mining/mining_sign_in_success_dialog.dart';
import 'package:flutter_metatel/core/languages/l.dart';
import 'package:flutter_metatel/core/task/time_task.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:flutter_metatel/core/utils/util.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

import '../../../core/utils/events_bus.dart';
import '../../../core/values/config.dart';
import '../../data/events/events.dart';
import '../../data/models/res/mining/mining_chat.dart';
import '../../data/models/res/mining/mining_invite.dart';
import '../../data/models/res/mining/mining_task.dart';
import '../../data/models/res/mining/mining_total.dart';
import '../../data/providers/api/did.dart';
import '../../widgets/custom_picker.dart';
import '../home/<USER>/mine_controller.dart';
import '../wallet/wallet_manage.dart';

class MiningController extends GetxController with GetTickerProviderStateMixin {
  late TabController tabController;

  RxInt loadType = LoadType.defaultState.obs;
  RxInt loadTypeInvite = LoadType.defaultState.obs;
  RxInt loadTypeTask = LoadType.defaultState.obs;
  RxList<MiningChatDetail> chatList = RxList<MiningChatDetail>();
  RxList<MiningInviteValue> inviteNewList = RxList<MiningInviteValue>();
  RxList<MiningTaskData> taskList = RxList<MiningTaskData>();

  var miningChat = MiningChat().obs;
  var miningInvite = MiningInvite().obs;
  var miningTask = MiningTask().obs;
  var miningTotal= MiningTotal().obs;

  // var miningChat = MiningChat().obs;

  var periods = 0.obs;

  var thePeriodTotalValue = 0.0.obs;

  RxDouble todayTotal = 0.0.obs;
  RxString chatDay ="".obs;
  List<MiningChatValue> mChatValueList=[];
  int mChatValueListItemIndex=0;

  int invitePageNumber=1;
  int inviteTotalPage=1;
  int invitePageSize=20;
  late ScrollController inviteScrollController;

  ///签到
  var hasSign=false.obs;
  var canSign=true.obs;

  ///是否已经提现
  var hasWithdraw=false.obs;
  int signDays=0;
  var miningInviteLevelData=MiningInviteLevelData().obs;
  RxBool showDId = false.obs;
  RxInt totalComputing = 0.obs;
  RxBool isHostingDId = false.obs;
  StreamSubscription? subscription;

  @override
  void onInit() {
    super.onInit();
    Get.find<MineController>().searchBeanList.then((data) {
      showDId.value = data.value.isNotEmpty;
      isHostingDId.value = false;
      if (data.value.isNotEmpty) {
         var total = 0;

        for(var b in data.value){
          if(b.status==1){
            isHostingDId.value = true;
          }
          total+=b.powerUp??0;
        }
        totalComputing.value =  total==0?10000:total;


      }
    });
    subscription = Get.find<EventBus>().on<RepurchaseSuccessEvent>().listen((event) {
      if(miningTotal.value.data
          ?.vesting!=null){
        miningTotal.value.data
            ?.vesting = (miningTotal.value.data
            ?.vesting??0)+(event.totalCount??0);
      }
      refreshData();
     });
    inviteScrollController = ScrollController();
    tabController = TabController(length: 3, vsync: this);
    inviteScrollController.addListener(() {
      if (inviteScrollController.position.pixels ==
          inviteScrollController.position.maxScrollExtent) {
        if (invitePageNumber <= inviteTotalPage) {
          getInviteMiningData(isRefresh: false);
        }
      }
    });
    // tabController.addListener(() {
    //   refreshData();
    // });
    getAllData();
  }

  Future<void> refreshData() async {
    getMiningTotalData();
    switch(tabController.index){
      case 0:
        getChatMiningData();
        break;
      case 1:
        getInviteMiningData();
        getCurPowerSharing();
        break;
      case 2:
        signInQuery();
        whetherWithdraw();
        getTaskMiningData();
        break;
    }
  }
  getAllData() async{
    showLoadingDialog();
    await getMiningTotalData(isFirst: true);
    dismissLoadingDialog();
    signInQuery();
    whetherWithdraw();
    getChatMiningData();
    getInviteMiningData();
    getCurPowerSharing();
    getTaskMiningData();
  }

  reloading() {
    refreshData();
  }

  bool isLoading() {
    return loadType.value == LoadType.loading;
  }

  bool isLoadError() {
    return loadType.value == LoadType.error;
  }
  bool isLoadingInvite() {
    return loadTypeInvite.value == LoadType.loading;
  }

  bool isLoadErrorInvite() {
    return loadTypeInvite.value == LoadType.error;
  }
  bool isLoadingTask() {
    return loadTypeTask.value == LoadType.loading;
  }

  bool isLoadErrorTask() {
    return loadTypeTask.value == LoadType.error;
  }


  getChatMiningData() async {
    loadType.value=LoadType.loading;
    var chatRes = await Get.find<InviteApi>().getMining();
    if (chatRes.data != null) {

      miningChat.value = chatRes.data as MiningChat;
      periods.value = miningChat.value.data?.stage ?? 0;
      thePeriodTotalValue.value = miningChat.value.data?.integral ?? 0;
      int task11 = (miningChat.value.data?.task11??0).toInt();
      loadType.value=LoadType.success;
      if (task11 != 0 && thePeriodTotalValue.value >= task11) {
        Get.find<EventBus>().fire(MiningTaskEvent(MiningTaskType.MINING_TASK_CHAT_500));
      }
        AppLogger.d('miningChat.value.data......${miningChat.value.data?.value?.isEmpty}');
      if(miningChat.value.data?.value?.isEmpty??true){
        if(pastState()>0){
          chatList.clear();
          chatList.insert(0, MiningChatDetail());
        }
        return;
      }
      mChatValueList = (miningChat.value.data?.value)!;
      mChatValueList = mChatValueList.reversed.toList();
      MiningChatValue? d = mChatValueList[0];
      setSelectDayData(d);

    }else{
      loadType.value=LoadType.error;
    }
  }
  setSelectDayData(MiningChatValue? d){
    var r = d?.detail;
    chatDay.value=d?.day??"";
    if (r != null) {
      todayTotal.value=0;
      for (MiningChatDetail c in r) {
        todayTotal.value += c.value ?? 0.0;
      }
      chatList.clear();
      chatList.addAll(r);
      chatList.insert(0, MiningChatDetail());
    }
  }
  getInviteMiningData({bool isRefresh=true}) async {
    if(isRefresh){
      loadTypeInvite.value=LoadType.loading;
      invitePageNumber=1;
    }
    var m =
        await Get.find<InviteApi>().getMiningInviteLevel2(page: invitePageNumber,size:invitePageSize);
    if (m.data != null) {
      miningInvite.value = m.data as MiningInvite;
      List<MiningInviteValue>? v = miningInvite.value.data?.value;
      inviteTotalPage=miningInvite.value.data?.totalPage??inviteTotalPage;
      if(v?.isNotEmpty??false){
        invitePageNumber++;
        if (isRefresh) {
          inviteNewList.clear();
          inviteNewList.add(MiningInviteValue());
        }
        inviteNewList.addAll(v!);

      }
      loadTypeInvite.value=LoadType.success;
    }else{
      loadTypeInvite.value=LoadType.error;
    }
    if(inviteNewList.isEmpty){
      inviteNewList.add(MiningInviteValue());
    }
  }
  getCurPowerSharing() async {
    var m =
    await Get.find<InviteApi>().getMiningApiMinerInviteLevel(-1);
    if (m.data != null) {
      var more=m.data;
      if(more!=null&&more.data!=null){
        miningInviteLevelData.value =more.data!;
      }
    }
  }
  double totalCount(){
    return (miningTotal
        .value.data?.sumall ??
        0.0) -
        (miningTotal.value.data
            ?.vesting ??
            0.0);
  }
  getTaskMiningData() async {
    loadTypeTask.value=LoadType.loading;
    var m = await Get.find<InviteApi>().getMiningTaskInfo();
    if (m.data != null) {
      miningTask.value = m.data as MiningTask;
      List<MiningTaskData>? v = miningTask.value.data;
      if (v != null) {
        taskList.clear();
        taskList.add(MiningTaskData());
        taskList.add(MiningTaskData());
        taskList.addAll(v);
      }
      loadTypeTask.value=LoadType.success;
    }else{
      loadTypeTask.value=LoadType.error;
    }
  }
  getMiningTotalData({bool isFirst = false}) async {
    var res = await Get.find<InviteApi>().getMiningTotal();
    if (res.data != null) {
      miningTotal.value = res.data as MiningTotal;
      int task11 = (miningTotal.value.data?.task11??0).toInt();

      if(task11!=0&&(miningTotal.value.data?.chatvl??0)>=task11){
        Get.find<EventBus>().fire(MiningTaskEvent(MiningTaskType.MINING_TASK_CHAT_500));
      }
    } else {
      if(isFirst){
        getMiningTotalData();
      } else {
        toast(L.the_server_returned_failure_please_try_again.tr);
      }
    }
  }
  ///签到
  signIn() async {
    if(!canSign.value){
      AppLogger.d("signIn can not");
      return;
    }
    canSign.value=false;
    var res = await Get.find<InviteApi>().miningSignIn();
    if(res.data?.code==200){
      await signInQuery();
      getMiningTotalData();
      getChatMiningData();
      SmartDialog.show(builder: (c){
        return MiningSignInSuccessDialog(signDays: signDays,);
      });
      canSign.value=true;
    }else{
      canSign.value=true;
      toast(L.sign_in_failed.tr);
    }
  }
  ///签到查询
  signInQuery() async {
    var res = await Get.find<InviteApi>().miningSignInQuery();
    List<SignDate>? value = res.data?.data?.value;
    signDays=res.data?.data?.days??0;
    if(value?.isNotEmpty??false){
      String formatNowTimeYMD = DateFormat("yyyy-MM-dd").format(TimeTask.instance.getNowDateTime());
      for(SignDate signDate in value!){
          String dataTimes = signDate.datatimes??"";
          if(dataTimes.isNotEmpty){
            var split = dataTimes.split(" ");
            var ymd = split[0];
            if(formatNowTimeYMD==ymd){
              hasSign.value=true;
              canSign.value=false;
            }
          }
      }
    }
  }
  ///签到查询
  whetherWithdraw() async {
    var res = await Get.find<InviteApi>().miningHasWithdraw();
    var code = res.data?.code??-1;
    switch(code){
      case 200:
        hasWithdraw.value=true;
        break;
      case 601:
        hasWithdraw.value=false;
        break;
    }
  }
  ///提现
  Future<bool> withdraw(String amountStr,{String? address}) async {
    double amount=0;
    try{
      amount=double.parse(amountStr);
    }catch(e){

    }
    var d = (miningTotal.value.data?.sumall??0)-(miningTotal.value.data?.vesting??0);
    if(amount>d){
      toast(L.insufficient_balance.tr);
      return false;
    }
    var res = await Get.find<InviteApi>().miningApplyWithdraw(amount,address: address);
    if(res.data?.code==200){
      getMiningTotalData();
      getChatMiningData();
      whetherWithdraw();
      return true;
    }else if(res.data?.code!=610){
      // toast("提现失败");
    }
    return false;
  }
  void onSelectDay() {
    if(mChatValueList.isEmpty){
      return;
    }
    List<String> data=[];
    for(MiningChatValue m in mChatValueList){
      if(m.day!=null){
        data.add(m.day!);
      }
    }
    if(data.isEmpty){
      return;
    }
    CustomPicker.show(
      Get.context!,
      list: data,
      curIndex: mChatValueListItemIndex,
      onSelectedItemChanged: (v) {

      },
      confirmClick: (v) {
        mChatValueListItemIndex=v;
       setSelectDayData(mChatValueList[v]);
      },
    );
  }
  int pastState(){
    return miningChat.value.data?.previous ?? 0;
  }
  int recentStage(){
    return (miningChat.value.data?.stage ?? 1)-1;
  }

  int pastComputingPowerState(){
    AppLogger.d("pastComputingPowerState${miningInviteLevelData.value.stage ?? 0}");
    return miningInviteLevelData.value.stage ?? 0;
  }
  int pastComputingRecentStage(){
    return (miningInviteLevelData.value.stage ?? 1)-1;
  }
  @override
  void dispose() {
    super.dispose();
    subscription?.cancel();
  }
}
