import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:get/get.dart';

import '../../../../../core/values/colors.dart';
import '../../../../../r.dart';
import '../../../core/languages/l.dart';

class MiningPopDialog extends StatefulWidget {
  const MiningPopDialog({super.key, this.canCancel = true});

  final bool canCancel;

  @override
  State<MiningPopDialog> createState() => _MiningPopDialogState();
}

class _MiningPopDialogState extends State<MiningPopDialog> {
  @override
  Widget build(BuildContext context) {
    return Container(
      color: AppColors.transparent,
      // ignore: prefer_const_constructors
      padding: EdgeInsets.symmetric(horizontal: 16).r,
      alignment: Alignment.center,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            decoration: BoxDecoration(
                color: AppColors.white,
                borderRadius: BorderRadius.circular(16).r),
            padding: const EdgeInsets.symmetric(vertical: 25, horizontal: 25).r,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  width: double.infinity,
                  alignment: Alignment.center,
                  margin: const EdgeInsets.only(bottom: 21).r,
                  child: Text(
                    L.the_data_shows.tr,
                    style: TextStyle(
                      fontWeight: FontWeight.w600,
                      fontSize: 18.sp,
                    ),
                  ),
                ),
                _buildItem(title: L.mining_data_help_title_1.tr, isTitle: true),
                _buildItem(title: L.mining_data_help_1.tr, isTitle: false),
                _buildItem(title: L.mining_data_help_title_2.tr, isTitle: true),
                _buildItem(title: L.mining_data_help_2.tr, isTitle: false),
                _buildItem(title: L.mining_data_help_title_3.tr, isTitle: true),
                _buildItem(title: L.mining_data_help_3.tr, isTitle: false),
                _buildItem(title: L.mining_data_help_title_4.tr, isTitle: true),
                _buildItem(title: L.mining_data_help_4.tr, isTitle: false),
                _buildItem(title: L.mining_data_help_title_5.tr, isTitle: true),
                _buildItem(title: L.mining_data_help_5.tr, isTitle: false),
              ],
            ),
          ),
          SizedBox(
            height: 27.h,
          ),
          Visibility(
            visible: true,
            child: IconButton(
              onPressed: () {
                SmartDialog.dismiss();
              },
              icon: Image.asset(R.icoCloseTrans),
            ),
          ),
        ],
      ),
    );
  }

  _buildItem({required String title, required bool isTitle}) {
    return Container(
      margin: EdgeInsets.only(bottom: isTitle ? 5 : 20).r,
      child: Text(
        title,
        style: TextStyle(
            fontWeight: isTitle ? FontWeight.w500 : null,
            color: isTitle ? AppColors.colorFF333333 : AppColors.colorFF808080),
      ),
    );
  }
}
