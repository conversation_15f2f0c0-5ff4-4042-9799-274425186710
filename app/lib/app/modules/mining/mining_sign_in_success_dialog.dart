import 'package:flutter/material.dart';
import 'package:flutter_metatel/core/languages/l.dart';
import 'package:flutter_metatel/core/utils/util.dart';
import 'package:flutter_metatel/core/values/colors.dart';
import 'package:flutter_metatel/r.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:get/get.dart';

class MiningSignInSuccessDialog extends StatelessWidget {
  const MiningSignInSuccessDialog(
      {super.key, this.contributionValue = 20, this.signDays});

  final int contributionValue;
  final int? signDays;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: Center(
        child: Stack(
          alignment: Alignment.center,
          children: [
            Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  height: 300.h,
                  margin: const EdgeInsets.only(left: 40, right: 40).r,
                  decoration: BoxDecoration(
                      color: AppColors.white,
                      borderRadius:
                          const BorderRadius.all(Radius.circular(16)).r),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Container(
                        margin: const EdgeInsets.only(
                          top: 25,
                        ).r,
                        alignment: Alignment.center,
                        child: Image.asset(
                          R.iconMiningSignIn,
                          width: 106.w,
                          height: 74.h,
                        ),
                      ),
                      Container(
                        margin:
                            EdgeInsets.only(top: 25.h, left: 20, right: 20).r,
                        alignment: Alignment.center,
                        child: RichText(
                          textAlign: TextAlign.center,
                          text: TextSpan(
                              text:
                                  L.sign_in_successfully_contribution_value.tr,
                              style: TextStyle(
                                fontSize: 18.sp,
                                color: AppColors.colorFF333333,
                                fontWeight: FontWeight.w500,
                              ),
                              children: [
                                TextSpan(
                                  text: "+$contributionValue",
                                  style: TextStyle(
                                    fontSize: 18.sp,
                                    color: AppColors.colorFFFF7D0C,
                                    fontWeight: FontWeight.w500,
                                  ),
                                )
                              ]),
                        ) /*Row(
                          children: [
                            Text(
                              L.sign_in_successfully_contribution_value,
                              style: TextStyle(
                                fontSize: 18.sp,
                                color: AppColors.colorFF333333,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            Text(
                              "+${contributionValue??0}",
                              style: TextStyle(
                                fontSize: 18.sp,
                                color: AppColors.colorFFFF7D0C,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        )*/
                        ,
                      ),
                      Container(
                          margin:
                              EdgeInsets.only(top: 24.h, left: 20, right: 20).r,
                          alignment: Alignment.center,
                          child: RichText(
                          textAlign: TextAlign.center,
                          text: TextSpan(
                            text: L.sign_in_for.tr,
                            style: TextStyle(
                              fontSize: 14.sp,
                              color: AppColors.colorFF808080,
                              fontWeight: FontWeight.w500,
                            ),
                            children: _createSignPointsHint(),
                          ),
                        ),
                      ),
                      const Spacer(),
                      Padding(
                        padding: const EdgeInsets.only(
                                left: 48,right: 48, bottom: 35)
                            .r,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Flexible(
                              child: TextButton(
                                style: ButtonStyle(
                                  fixedSize: MaterialStateProperty.all(
                                      Size.fromHeight(40.h)),
                                  shape: MaterialStateProperty.all(
                                    RoundedRectangleBorder(
                                      borderRadius: const BorderRadius.all(
                                              Radius.circular(20))
                                          .r,
                                    ),
                                  ),
                                  // overlayColor:
                                  // MaterialStateProperty.all(Colors.transparent),
                                  // foregroundColor:
                                  // MaterialStateProperty.resolveWith((states) {
                                  //   return states.contains(MaterialState.pressed)
                                  //       ? Colors.black54
                                  //       : Colors.black38;
                                  // }),
                                  backgroundColor: MaterialStateProperty.all(
                                      AppColors.colorFF3474d1),
                                ),
                                onPressed: () {
                                  SmartDialog.dismiss();
                                },
                                child: Container(
                                  height: 40.h,
                                  alignment: Alignment.center,
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Text(
                                        L.i_see.tr,
                                        style: TextStyle(
                                          color: AppColors.white,
                                          fontSize: 14.sp,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  List<InlineSpan> _createSignPointsHint() {
    return (signDays??0) == 7
        ? [
            TextSpan(
              text: "$signDays",
              style: TextStyle(
                fontSize: 14.sp,
                color: AppColors.colorFF3474d1,
              ),
            ),
            TextSpan(
              text: L.consecutive_days.tr,
              style: TextStyle(
                fontSize: 14.sp,
                color: AppColors.colorFF808080,
              ),
            ),
            TextSpan(
              text: L.extra_reward_100_contribution_points.tr,
              style: TextStyle(
                fontSize: 14.sp,
                color: AppColors.colorFF808080,
              ),
            ),
            TextSpan(
              text: "100",
              style: TextStyle(
                fontSize: 14.sp,
                color: AppColors.colorFF3474d1,
              ),
            ),
            TextSpan(
              text:
                  "${currentLanguageIsSimpleChinese() ? "" : " "}${L.points.tr}",
              style: TextStyle(
                fontSize: 14.sp,
                color: AppColors.colorFF808080,
              ),
            )
          ]
        : [
            TextSpan(
              text: "$signDays",
              style: TextStyle(
                fontSize: 14.sp,
                color: AppColors.colorFF3474d1,
              ),
            ),
            TextSpan(
              text: L.consecutive_days.tr,
              style: TextStyle(
                fontSize: 14.sp,
                color: AppColors.colorFF808080,
              ),
            )
          ];
  }
}
