//FileName Repurchase3TController
// <AUTHOR>
//@Date 2024/3/11 12:04
import 'dart:convert';
import 'dart:math' as math;
import 'package:async_task/async_task_extension.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_metatel/app/data/events/events.dart';
import 'package:flutter_metatel/app/data/providers/api/did.dart';
import 'package:flutter_metatel/app/data/services/config_service.dart';
import 'package:flutter_metatel/app/modules/home/<USER>/mine_controller.dart';
import 'package:flutter_metatel/app/modules/mining/mining_pwd_util.dart';
import 'package:flutter_metatel/core/languages/l.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:flutter_metatel/core/utils/events_bus.dart';
import 'package:flutter_metatel/core/utils/util.dart';
import 'package:get/get.dart';

import '../../../../core/values/config.dart';
import '../../../data/models/res/mining/repurchase_model.dart';

class Repurchase3TController extends GetxController
    with GetTickerProviderStateMixin {
  RxList swapInfoList = <GetSwapInfoDataRes>[].obs;
  RxString walletAddress = ''.obs;
  RxString userName = ''.obs;
  RxDouble totalValue = 0.0.obs;
  RxDouble redeemable = 0.0.obs; //可兑换
  double? _redeemed; //已兑换
  RxInt swapValue = 0.obs;
  RxDouble redeemableUsdt = 0.0.obs; //可兑换usdt
  final TextEditingController edCtl = TextEditingController();
  RxBool showDId = false.obs;
  RxDouble handlingFee = 0.0.obs;//手续费

  bool? exceedAmount;
  int minCount = 1000;
  int maxCount = 100000;

  @override
  void onInit() {
    super.onInit();
    if (Config.exchangeArray.length == 2) {
      minCount = Config.exchangeArray.first;
      maxCount = Config.exchangeArray.last;
    }
    walletAddress.value = Get.find<AppConfigService>().readMiningMiningInfo();
    userName.value = Get.find<AppConfigService>().getUserNameWithoutDomain();
    Get.find<MineController>().searchBeanList.then((data) {
      showDId.value = data.value.isNotEmpty;
    });
  }

  setTotalValue(double? total) async {
    totalValue.value = total ?? 0.0;
    showLoadingDialog();
    await getSwapAmount();
    await getSwapInfo();
    dismissLoadingDialog();
  }

  changeText(String data) {
    try {
      double am = double.parse(data);
      if (am > redeemable.value || redeemable.value == 0.0) {
        toast(L.exceeds_the_maximum_redeemable_amount.tr);
        return;
      }
      updateRedeemable(am);
    } catch (e) {
      edCtl.text = "";
      updateRedeemable(0);
    }
  }

  getSwapInfo() async {
    var map = {'account': userName.value, 'address': walletAddress.value};
    var res = await Get.find<DidApi>().getSwapDataApi(map);
    if (res.body?.code == 200) {
      var list = res.body?.data?.result ?? [];
      swapInfoList.clear();
      if (list.isNotEmpty) {
        int count = 0;
        for (var b in list) {
          swapInfoList.add(b);
          if (b.status == 1) {
            count++;
          }
        }
        exceedAmount = count < 1;
      } else {
        exceedAmount = true;
      }
    }
    AppLogger.d('swapAddSwapApi list=${swapInfoList.length}');
  }

  getSwapAmount() async {
    var map = {'account': userName.value, 'address': walletAddress.value};
    var res = await Get.find<DidApi>().getMaxSwapAmountApi(map);
    if (res.body?.code == 200) {
      GetSwapAmountData? data = res.body?.data;
      if (data != null) {
        swapValue.value = data.swapValue ?? 0;
        _redeemed = data.result;
        var maxswapValue = data.maxSwapValue; //当前可兑换最大金额
        if (maxswapValue != null) {
          num minValue =
              [maxswapValue, totalValue.value].reduce((v, e) => math.min(v, e));
          AppLogger.d('swapAddSwapApi minValue = $minValue');
          redeemable.value = minValue.toDouble();
        }
        changeText(edCtl.text);
      }
    }
  }

  swapAddSwap(double amount) async {
    if (amount > redeemable.value || redeemable.value == 0.0) {
      toast(L.exceeds_the_maximum_redeemable_amount.tr);
      return;
    }
    if (amount < minCount || amount > maxCount) {
      toast(L.repurchase_min_number
          .trParams({'num1': '$minCount', 'num2': '$maxCount'}));
      return;
    }
    if (!showDId.value) {
      toast(L.repurchase_tid_submit_info.tr);
      return;
    }
    if (exceedAmount == null) {
      toast('await...');
      return;
    }
    if (!exceedAmount!) {
      toast(L.order_repurchase_tips.tr);
      return;
    }
    showLoadingDialog();
    var map = {
      'account': userName.value,
      'address': walletAddress.value,
      'amount': amount
    };
    String sigdata = await MiningPwdUtil.encryptForData(json.encode(map)) ?? '';
    Map<String, dynamic> query = {'account': userName.value, 'info': sigdata};
    var res = await Get.find<DidApi>().swapAddSwapApi(query);
    if (res.body?.data?.result == 1) {
      totalValue.value = totalValue.value - amount;
      exceedAmount = false;
      getSwapAmount();
      await getSwapInfo();
      toast(L.submit_success.tr);
      Get.find<EventBus>().fire(RepurchaseSuccessEvent(amount));
    } else {
      toast(res.body?.msg ?? 'error');
    }
    dismissLoadingDialog();
  }

  updateRedeemable(double amount) {
    if (totalValue.value != 0.0 && _redeemed != null&&amount>=minCount) {
      double d = amount / swapValue.value;
      if (d > 20) {
        handlingFee.value = double.parse((d * 0.05).toStringAsFixed(3));
      } else {
        handlingFee.value = 1;
      }
      redeemableUsdt.value =
          double.parse(((amount / swapValue.value)-handlingFee.value).toStringAsFixed(6));
    } else {
      handlingFee.value = 0.0;
      redeemableUsdt.value = 0.0;
    }
  }
}
