import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_metatel/app/data/models/res/mining/repurchase_model.dart';
import 'package:flutter_metatel/app/modules/mining/repurchase/repurchase_3T_controller.dart';
import 'package:flutter_metatel/app/widgets/divider_cus.dart';
import 'package:flutter_metatel/core/languages/l.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:flutter_metatel/core/values/colors.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_web3/wallet.dart';
import 'package:flutter_web3/web3dart/src/utils/typed_data.dart';
import 'package:get/get.dart';
import 'package:decimal/decimal.dart';

import 'dart:ui';

import '../../../../core/utils/util.dart' as u ;

typedef TextFun = Function(String);

class Repurchasel3TWidget extends StatefulWidget {
  Repurchasel3TWidget(this.totalCount, {super.key});

  double? totalCount;

  @override
  NestedScrollViewState createState() => NestedScrollViewState();
}

class NestedScrollViewState extends State<Repurchasel3TWidget> {
  ///滚动监听设置
  late ScrollController _scrollController;
  Repurchase3TController controller = Get.put(Repurchase3TController());

  ///头部背景布局 true滚动一定的高度 false 滚动高度为0
  bool headerWhite = false;
  BackButton backIcon = const BackButton(
    color: Colors.black,
  );
  bool scroll = true;
  bool isChiness = false;
  final FocusNode focusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    isChiness = u.currentLanguageIsSimpleChinese();
    controller.setTotalValue(widget.totalCount);
    _scrollController = ScrollController();
    _scrollController.addListener(() {
      ///监听滚动位置设置导航栏颜色
      setState(() {
        headerWhite = _scrollController.offset > 400 ? true : false;
        // backIcon = BackButton(
        //   color: _scrollController.offset > 400 ?Colors.black: AppColors.white,
        // );
      });
    });
  }

  @override
  void dispose() {
    super.dispose();
    Get.delete<Repurchase3TController>();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: NestedScrollView(
          controller: _scrollController,
          physics: scroll
              ? const AlwaysScrollableScrollPhysics()
              : const NeverScrollableScrollPhysics(),
          headerSliverBuilder: _headerSliverBuilder,
          body: buildSliverBody(context)),
    );
  }

  ///页面滚动头部处理
  List<Widget> _headerSliverBuilder(
      BuildContext context, bool innerBoxIsScrolled) {
    return <Widget>[buildSliverAppBar(context)];
  }

  ///导航部分渲染
  Widget buildSliverAppBar(BuildContext context) {
    var name = L.apply_for_redemption.tr;
    return SliverAppBar(
      pinned: true,
      stretch: true,
      expandedHeight: isChiness?530.r:540.r,
      elevation: 0,
      backgroundColor: Colors.white,
      // backgroundColor: headerWhite ? Colors.white : const Color(0xFFF4F5F7),
      snap: false,
      leading: IconButton(
          icon: backIcon,
          onPressed: () {
            Get.back();
          }),
      flexibleSpace: FlexibleSpaceBar(
        title: headerWhite
            ? Text(
                name,
                style: TextStyle(
                  color: AppColors.colorFF333333,
                  fontWeight: FontWeight.w700,
                  fontSize: 16.sp,
                ),
              )
            : const Text(''),
        centerTitle: true,
        background: buildAppBarBackground(context),
      ),
      systemOverlayStyle: SystemUiOverlayStyle.dark,
    );
  }

  ///渲染背景部分布局
  Widget buildAppBarBackground(BuildContext context) {
    return Column(
      children: <Widget>[
        SizedBox(
          height: (kToolbarHeight-10).r,
        ),
        Center(
          child: Text(
            L.apply_for_redemption.tr,
            style: TextStyle(
              color: AppColors.colorFF333333,
              fontWeight: FontWeight.w700,
              fontSize: 16.sp,
            ),
          ),
        ),
        SizedBox(
          height: 10.r,
        ),
        const DividerCus(
          thickness: 1,
        ),
        Container(
          height: isChiness?472.r:487.r,
          margin: const EdgeInsets.only(left: 16, right: 16).r,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(
                height: 10,
              ),
              Text(
                L.wallet_address.tr,
                style: TextStyle(
                  fontSize: 14.sp,
                  color: AppColors.colorFF333333,
                ),
              ),
              _buildEditText(
                  hintText: controller.walletAddress.value,
                  marginLeft: 0,
                  marginRight: 0,
                  fontSize: 12.sp,
                  maxLine: 2,
                  readOnly: true),
              Container(
                margin: const EdgeInsets.only(
                  top: 10,
                ).r,
                child: Text(
                  L.apply_for_redemption.tr,
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: AppColors.colorFF333333,
                  ),
                ),
              ),
              _buildEditText(
                  edCtl: controller.edCtl,
                  marginLeft: 0,
                  marginRight: 0,
                  focusNode: focusNode,
                  textFun: (s) {
                    controller.changeText(s);
                    AppLogger.d('changeText $s ');
                  },
                  hintText: L.please_enter_the_repurchase_amount.tr),
              //可兑换金额
              Row(
                children: [
                  Text(
                    L.can_be_repurchased.tr,
                    style: TextStyle(
                      color: AppColors.colorFF808080,
                      fontSize: 12.sp,
                    ),
                  ),
                  ConstrainedBox(
                    constraints: BoxConstraints(
                      maxWidth: 80.w,
                    ),
                    child: Obx(() => Text(
                          "： ${formatDouble(controller.redeemable.value, 2)} 3T,",
                          style: TextStyle(
                            color: AppColors.colorFF808080,
                            fontSize: 12.sp,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        )),
                  ),
                  const Spacer(),
                  TextButton(
                    style: ButtonStyle(
                      padding: MaterialStateProperty.all(EdgeInsets.zero),
                    ),
                    onPressed: () {
                      ///小数点后9位
                      // String text="${widget.totalValue}";
                      String text =
                          Decimal.parse(controller.redeemable.value.toString())
                              .toString();
                      var split = text.split(".");
                      String integer = split[0];
                      String decimal = split.length == 2 ? split[1] : "";
                      decimal = decimal.length > 9
                          ? decimal.substring(0, 9)
                          : decimal;
                      text = "$integer${decimal.isNotEmpty ? ".$decimal" : ""}";
                      if (text != "0") {
                        controller.edCtl.value = TextEditingValue(
                            text: text,
                            selection: TextSelection.fromPosition(TextPosition(
                                affinity: TextAffinity.downstream,
                                offset: text.length)));
                        // setState(() {});
                        controller.changeText(text);
                      }
                    },
                    child: Text(
                      L.uni_all.tr,
                      style: TextStyle(
                        color: AppColors.colorFF3474d1,
                        fontSize: 12.sp,
                      ),
                    ),
                  ),
                ],
              ),
              //兑换单价
              Obx(() => Text(
                    L.repurchase_unit_price
                        .trParams({'p': controller.swapValue.value==0?'--':controller.swapValue.value.toString()}),
                    style: TextStyle(
                      color: AppColors.colorFF333333,
                      fontSize: 12.sp,
                    ),
                  )),
              //可换USDT数量
              Container(
                margin: const EdgeInsets.only(top: 16, bottom: 10).r,
                padding: const EdgeInsets.all(10),
                width: double.infinity,
                decoration: BoxDecoration(
                    color: AppColors.appDefault,
                    gradient: const LinearGradient(
                        end: Alignment.bottomCenter,
                        begin: Alignment.topCenter,
                        colors: [Color(0xFFd9e8ff), Color(0xFFf2f7ff)]),
                    borderRadius:
                        const BorderRadius.all(Radius.circular(16)).r),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Obx(() => Visibility(
                            visible: controller.handlingFee.value > 0,
                            child: Text(L.handling_fee_02.trParams({'num':controller.handlingFee.value.toString()}),
                                style: TextStyle(
                                    color: AppColors.colorFF333333,
                                    fontSize: 12.sp)))),
                        Text(
                          L.expected_to_be_available.tr,
                          style: TextStyle(
                              fontSize: 12.sp, color: AppColors.colorFF333333),
                        )
                      ],
                    ),
                    SizedBox(
                      height: 5.r,
                    ),
                    Center(
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Obx(() => Text(
                              controller.redeemableUsdt.value.toString(),
                              style: TextStyle(
                                  color: AppColors.appDefault,
                                  fontSize: 24.sp))),
                          Text(
                            ' USDT',
                            style: TextStyle(
                                color: AppColors.colorFF333333,
                                fontSize: 12.sp),
                          )
                        ],
                      ),
                    ),
                    SizedBox(
                      height: 10.r,
                    ),
                  ],
                ),
              ),
              Text(
                L.expected_note.tr,
                style:
                    TextStyle(color: AppColors.colorFFCD3A3A, fontSize: 12.sp),
              ),
              const Spacer(),
              TextButton(
                style: ButtonStyle(
                  fixedSize: MaterialStateProperty.all(Size.fromHeight(40.h)),
                  shape: MaterialStateProperty.all(
                    RoundedRectangleBorder(
                      borderRadius: BorderRadius.all(Radius.circular(5.r).r).r,
                    ),
                  ),
                  backgroundColor:
                      MaterialStateProperty.all(AppColors.appDefault),
                ),
                onPressed: () {
                  focusNode.unfocus();
                  var amount = controller.edCtl.text.trim();
                  if (amount.isEmpty) {
                    toast(L.amount_is_required.tr);
                    return;
                  }
                  var contains = amount.contains(RegExp(r'[1-9]'));
                  if (!contains) {
                    controller.edCtl.text = "";
                    return;
                  }
                  try {
                    double am = double.parse(amount);
                    controller.swapAddSwap(am);
                  } catch (e) {
                    controller.edCtl.text = "";
                  }
                },
                child: Container(
                  height: 40.r,
                  alignment: Alignment.center,
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        L.apply_for_redemption.tr,
                        style: TextStyle(
                          color: AppColors.white,
                          fontSize: 14.sp,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              const Spacer(),
            ],
          ),
        ),
        SizedBox(
          height: 3.7.r,
        ),
        const DividerCus(
          thickness: 10,
        ),
      ],
    );
  }

  _buildEditText({
    String? hintText,
    double? marginLeft,
    double? marginRight,
    double? fontSize,
    int maxLine = 1,
    bool readOnly = false,
    TextEditingController? edCtl,
    FocusNode? focusNode,
    TextFun? textFun,
  }) {
    return Container(
      margin: EdgeInsets.only(
              left: marginLeft ?? 20, right: marginRight ?? 20, top: 10)
          .r,
      decoration: BoxDecoration(
        color: AppColors.colorFFF2F2F2,
        borderRadius: const BorderRadius.all(Radius.circular(10)).r,
      ),
      child: TextField(
        readOnly: readOnly,
        controller: edCtl,
        focusNode: focusNode,
        textAlignVertical: TextAlignVertical.center,
        inputFormatters: [
          FilteringTextInputFormatter.allow(RegExp(r'[0-9]')),
          // TextNumberLimitFormatter(10, 9),
        ],
        maxLines: 2,
        onChanged: (t) {
          textFun?.call(t);
        },
        keyboardType: TextInputType.number,
        style: TextStyle(
          fontSize: fontSize ?? 14.sp,
          color: readOnly ? AppColors.colorFF808080 : null,
        ),
        decoration: InputDecoration(
          contentPadding: const EdgeInsets.only(left: 14, right: 14).r,
          fillColor: AppColors.colorFFF2F2F2,
          hintText: hintText,
          hintMaxLines: 2,
          hintStyle: TextStyle(fontSize: 14.sp, color: AppColors.colorFF808080),
          border: OutlineInputBorder(
            borderRadius: const BorderRadius.all(Radius.circular(10)).r,
            borderSide: BorderSide.none,
          ),
        ),
      ),
    );
  }

  Widget buildSliverBody(BuildContext context) {
    return CustomScrollView(
      slivers: [
        SliverAppBar(
          automaticallyImplyLeading: false,
          title: Text(
            L.repurchase_record.tr,
            style: TextStyle(color: AppColors.colorFF333333, fontSize: 14.sp),
          ),
          floating: false,
          centerTitle: false,
          expandedHeight: 0.r,
          primary: true,
        ),
        SliverAppBar(
          automaticallyImplyLeading: false,
          title: createTop(),
          floating: false,
          centerTitle: false,
          expandedHeight: 0.r,
          primary: false,
        ),
        Obx(() {
          var list = controller.swapInfoList;
          return SliverList(
            delegate: SliverChildBuilderDelegate(
              (BuildContext context, int index) {
                var data = list[index];
                var islast = index == list.length - 1;
                return _buildItemChild(
                  index,
                  data,
                  isLast: islast,
                );
              },
              childCount: list.length,
            ),
          );
        })
      ],
    );
  }

  Widget _buildItemChild(int index, GetSwapInfoDataRes data,
      {bool isLast = false}) {
    var bgColor =
        index % 2 == 1 ? AppColors.colorFFE6EBF2 : AppColors.colorFFF7F7F7;
    var borderRad = isLast
        ? const BorderRadius.only(
            bottomLeft: Radius.circular(15),
            bottomRight: Radius.circular(15),
          ).r
        : const BorderRadius.all(Radius.circular(0)).r;
    var statues = data.status == 0
        ? L.repurchase_0.tr
        : data.status == 1
            ? L.repurchase_1.tr
            : L.repurchase_2.tr;
    var statuesColor = data.status == 1
        ? AppColors.colorFF3474d1
        : data.status == 2
            ? AppColors.colorFF333333
            : AppColors.colorFFCD3A3A;
    var t = data.createtime?.split(' ');
    var time = data.createtime;
    if (t?.isNotEmpty ?? false) {
      time = t?.first;
    }
    AppLogger.d('_buildNFTItemChild createtime ${index % 2}');

    return Container(
      height: 39.r,
      decoration: BoxDecoration(
        color: bgColor,
        border: Border.all(color: AppColors.colorFFEDEDED, width: 0.5),
        borderRadius: borderRad,
      ),
      margin: EdgeInsets.symmetric(horizontal: 16.r),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        mainAxisSize: MainAxisSize.max,
        children: [
          Expanded(
            child: Center(
              child: Text(
                '${data.amount ?? ''}',
                style: TextStyle(
                  color: AppColors.colorFF333333,
                  fontSize: 14.sp,
                ),
              ),
            ),
          ),
          Container(
            width: 0.5,
            height: double.infinity,
            color: AppColors.colorFFEDEDED,
          ),
          Expanded(
            child: Center(
              child: Text(
                '${data.prize ?? ''}',
                style: TextStyle(
                  color: AppColors.colorFF333333,
                  fontSize: 14.sp,
                ),
              ),
            ),
          ),
          Container(
            width: 0.5,
            height: double.infinity,
            color: AppColors.colorFFEDEDED,
          ),
          Expanded(
            child: Center(
              child: Text(
                statues,
                style: TextStyle(
                  color: statuesColor,
                  fontSize: 14.sp,
                ),
              ),
            ),
          ),
          Container(
            width: 0.5,
            height: double.infinity,
            color: AppColors.colorFFEDEDED,
          ),
          Expanded(
            child: Center(
              child: Text(
                time ?? '',
                style: TextStyle(
                  color: AppColors.colorFF333333,
                  fontSize: 14.sp,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget createTop() {
    return Container(
        height: 39.r,
        decoration: BoxDecoration(
          color: AppColors.colorFF333333,
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(15),
            topRight: Radius.circular(15),
          ).r,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          mainAxisSize: MainAxisSize.max,
          children: [
            Expanded(
              child: Center(
                child: Text(
                  L.repurchase_quantity.tr,
                  style: TextStyle(
                    color: AppColors.white,
                    fontSize: 14.sp,
                  ),
                ),
              ),
            ),
            Expanded(
              child: Center(
                child: Text(
                  L.amount_received.tr,
                  style: TextStyle(
                    color: AppColors.white,
                    fontSize: 14.sp,
                  ),
                ),
              ),
            ),
            Expanded(
              child: Center(
                child: Text(
                  L.state.tr,
                  style: TextStyle(
                    color: AppColors.white,
                    fontSize: 14.sp,
                  ),
                ),
              ),
            ),
            Expanded(
              child: Center(
                child: Text(
                  L.repurchase_time.tr,
                  style: TextStyle(
                    color: AppColors.white,
                    fontSize: 14.sp,
                  ),
                ),
              ),
            ),
          ],
        ));
  }
}
