import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_metatel/app/data/providers/api/invite.dart';
import 'package:flutter_metatel/app/modules/mining/contribution/contribution_detail_page.dart';
import 'package:flutter_metatel/app/modules/mining/divideinto/divide_into_detail_page.dart';
import 'package:flutter_metatel/app/modules/mining/mining_withdraw_dialog.dart';
import 'package:flutter_metatel/app/modules/mining/mining_withdraw_success_hint_dialog.dart';
import 'package:flutter_metatel/app/modules/mining/repurchase/repurchase_3tl_view.dart';
import 'package:flutter_metatel/app/modules/wallet/wallet_manage.dart';
import 'package:flutter_metatel/app/widgets/mavatar_circle_avatar.dart';
import 'package:flutter_metatel/core/values/colors.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:flutter_web3/app/core/utils/util.dart';
import 'package:flutter_web3/web3dart/src/utils/typed_data.dart';
import 'package:get/get.dart';

import '../../../core/languages/l.dart';
import '../../../core/utils/util.dart';
import '../../../core/values/config.dart';
import '../../../r.dart';
import '../../widgets/app_bar_cus.dart';
import '../../widgets/divider_cus.dart';
import 'mining_chat_item.dart';
import 'mining_controller.dart';
import 'mining_invant_new.dart';
import 'mining_pop_dialog.dart';
import 'mining_task_view.dart';

class MiningView extends GetView<MiningController> {
  const MiningView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: GetBuilder<MiningController>(builder: (controller) {
        return Obx(() {
          var sHeight = Config.isBrowser && controller.showDId.value?355.r:300.r;
           return Stack(
          children: [
            SizedBox(
              height: sHeight,
              child: Stack(
                children: [
                  Image.asset(
                    R.bgMining,
                    width: double.infinity,
                    height: sHeight,
                    fit: BoxFit.cover,
                  ),
                  Positioned(
                    left: 0,
                    bottom: 0,
                    right: 0,
                    child: Obx(()=>Container(
                      alignment: Alignment.center,
                      padding: const EdgeInsets.only(
                          top: 15, left: 16, right: 16, bottom: 10)
                          .r,
                      margin:
                      const EdgeInsets.only(top: 10, left: 16, right: 16).r,
                      decoration: BoxDecoration(
                          color: AppColors.appDefault,
                          borderRadius: const BorderRadius.only(
                              topLeft: Radius.circular(16),
                              topRight: Radius.circular(16))
                              .r),
                      child: Column(
                        children: [
                          Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                _createDetailItem(
                                    (controller.miningTotal.value.data
                                        ?.chatvl ??
                                        0.0) +
                                        (controller.miningTotal.value.data
                                            ?.signin ??
                                            0),
                                    L.chat_points.tr),
                                _createDetailItem(
                                    controller
                                        .miningTotal.value.data?.invitevl ??
                                        0,
                                    L.invite_new.tr),
                                _createDetailItem(
                                    controller.miningTotal.value.data?.taskvl ??
                                        0,
                                    L.task.tr),
                              ]),
                          SizedBox(
                            height: 10.r,
                          ),
                          Visibility(
                              visible: Config.isBrowser && controller.showDId.value && Config.redeemable,
                              child: TextButton(
                                style: ButtonStyle(
                                  backgroundColor:
                                  MaterialStateProperty.all(
                                      Colors.white),
                                  padding: MaterialStateProperty.all(
                                      const EdgeInsets.only(
                                          left: 25,
                                          right: 25,
                                          top: 0,
                                          bottom: 0)
                                          .r),
                                  shape: MaterialStateProperty.all(
                                    RoundedRectangleBorder(
                                        borderRadius:
                                        const BorderRadius.all(
                                            Radius.circular(20))
                                            .r),
                                  ),
                                  alignment: Alignment.center,
                                ),
                                onPressed: () async {
                                  WalletManage().getDisposit();
                                  Get.to(Repurchasel3TWidget(
                                      controller.totalCount()));
                                },
                                child: Text(
                                  L.repurchase.tr,
                                  style: TextStyle(
                                    color: AppColors.appDefault,
                                  ),
                                ),
                              ))
                        ],
                      ),
                    )),
                  )
                ],
              ),
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(
                  height: sHeight,
                  child: Column(
                    children: [
                      AppBarCommon().build(
                        context,
                        systemUiOverlayStyle: SystemUiOverlayStyle.light
                            .copyWith(statusBarColor: Colors.transparent),
                        leading: const BackButton(
                          color: AppColors.white,
                        ),
                        titleColor: AppColors.white,
                        title: L.chat_mining.tr,
                        backgroundColor: AppColors.transparent,
                      ),
                      Container(
                        width: double.infinity,
                        margin:
                        const EdgeInsets.only(left: 16, right: 16, top: 0)
                            .r,
                        child: Stack(
                          children: [
                            //提现
                            Positioned(
                              top: 10.r,
                              right: 0,
                              child: TextButton(
                                style: ButtonStyle(
                                  /* backgroundColor:
                                  MaterialStateProperty.all(Colors.white),*/
                                  padding: MaterialStateProperty.all(
                                      const EdgeInsets.only(
                                          left: 20, right: 20)
                                          .r),
                                  shape: MaterialStateProperty.all(
                                    RoundedRectangleBorder(
                                        borderRadius: const BorderRadius.all(
                                            Radius.circular(20))
                                            .r),
                                  ),
                                  side: MaterialStateProperty.all(
                                      const BorderSide(
                                          color: Colors.white, width: 0.67)),
                                  alignment: Alignment.center,
                                ),
                                onPressed: () async {
                                  WalletManage().getDisposit();
                                  bool? ret =
                                  await SmartDialog.show(builder: (c) {
                                    return MiningWithdrawDialog(
                                      totalValue: controller.totalCount(),
                                      canEditAddress: InviteApi
                                          .miningWithdrawAddressModify,
                                    );
                                  });
                                  if (ret ?? false) {
                                    SmartDialog.show(builder: (c) {
                                      return const MiningWithdrawSuccessHintDialog();
                                    });
                                  }
                                },
                                child: Text(
                                  L.withdraw.tr,
                                  style: const TextStyle(
                                    color: AppColors.white,
                                  ),
                                ),
                              ),
                            ),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  children: [
                                    Text(
                                      L.total_income.tr,
                                      style: TextStyle(
                                          color: Colors.white,
                                          fontSize: 14.sp),
                                    ),
                                    Container(
                                      height: 15.r,
                                      width: 15.r,
                                      margin:
                                      const EdgeInsets.only(left: 5).r,
                                      child: IconButton(
                                        onPressed: () {
                                          SmartDialog.show(builder: (c) {
                                            return const MiningPopDialog();
                                          });
                                        },
                                        padding: EdgeInsets.zero,
                                        icon: Image.asset(
                                          R.icoHelp,
                                          width: 15.r,
                                          height: 15.r,
                                        ),
                                        iconSize: 15.r,
                                      ),
                                    ),
                                  ],
                                ),
                                SizedBox(
                                  height: 5.r,
                                ),
                                Row(
                                  children: [
                                    Builder(builder: (context) {
                                      return GestureDetector(
                                        onLongPress: () {
                                          SmartDialog.showAttach(
                                              targetContext: context,
                                              builder: (c) {
                                                return Container(
                                                  constraints: BoxConstraints(
                                                      maxWidth: 200.w),
                                                  padding: const EdgeInsets
                                                      .symmetric(
                                                      horizontal: 16,
                                                      vertical: 16)
                                                      .r,
                                                  decoration: BoxDecoration(
                                                      color: AppColors.white,
                                                      borderRadius:
                                                      const BorderRadius
                                                          .all(Radius
                                                          .circular(
                                                          16))
                                                          .r),
                                                  child: Obx(()=>Text(
                                                    '${controller.miningTotal.value.data?.sumall ?? 0}',
                                                    style: TextStyle(
                                                      fontSize: 18.sp,
                                                    ),
                                                  )),
                                                );
                                              });
                                        },
                                        child: ConstrainedBox(
                                          constraints: BoxConstraints(
                                            maxWidth: 200.w,
                                          ),
                                          child: Obx(()=>Text(
                                            formatDouble(
                                                controller.miningTotal.value
                                                    .data?.sumall ??
                                                    0,
                                                2),
                                            style: TextStyle(
                                              color: Colors.white,
                                              fontSize: 23.sp,
                                              fontWeight: FontWeight.w500,
                                            ),
                                            maxLines: 1,
                                            overflow: TextOverflow.ellipsis,
                                          )),
                                        ),
                                      );
                                    }),
                                    SizedBox(
                                      width: 5.w,
                                    ),
                                    const Text(
                                      '3T',
                                      style: TextStyle(
                                        color: Colors.white,
                                      ),
                                    ),
                                  ],
                                ),
                                SizedBox(
                                  height: 10.h,
                                ),
                                Row(
                                  children: [
                                    Text(
                                      L.has_bean_withdrawn.tr,
                                      style: TextStyle(
                                          color: Colors.white,
                                          fontSize: 14.sp),
                                    ),
                                  ],
                                ),
                                SizedBox(
                                  height: 5.h,
                                ),
                                Row(
                                  children: [
                                    Builder(builder: (context) {
                                      return GestureDetector(
                                          onLongPress: () {
                                            SmartDialog.showAttach(
                                                targetContext: context,
                                                builder: (c) {
                                                  return Container(
                                                    constraints:
                                                    BoxConstraints(
                                                        maxWidth: 200.w),
                                                    padding: const EdgeInsets
                                                        .symmetric(
                                                        horizontal: 16,
                                                        vertical: 16)
                                                        .r,
                                                    decoration: BoxDecoration(
                                                        color:
                                                        AppColors.white,
                                                        borderRadius:
                                                        const BorderRadius
                                                            .all(
                                                            Radius.circular(
                                                                16))
                                                            .r),
                                                    child: Text(
                                                      '${controller.miningTotal.value.data?.vesting ?? 0}',
                                                      style: TextStyle(
                                                        fontSize: 18.sp,
                                                      ),
                                                    ),
                                                  );
                                                });
                                          },
                                          child: ConstrainedBox(
                                            constraints: BoxConstraints(
                                              maxWidth: 200.w,
                                            ),
                                            child: Obx(()=>Text(
                                              formatDouble(
                                                  controller.miningTotal.value
                                                      .data?.vesting ??
                                                      0,
                                                  2),
                                              style: TextStyle(
                                                color: Colors.white,
                                                fontSize: 23.sp,
                                                fontWeight: FontWeight.w500,
                                              ),
                                              maxLines: 1,
                                              overflow: TextOverflow.ellipsis,
                                            )),
                                          ));
                                    }),
                                    SizedBox(
                                      width: 5.w,
                                    ),
                                    const Text(
                                      '3T',
                                      style: TextStyle(
                                        color: Colors.white,
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            )
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                Obx(() {
                  var statues = controller.isHostingDId.value
                      ? L.ai_hosting_state_1.tr
                      : L.ai_hosting_state_2.tr;
                  var statuesColor = controller.isHostingDId.value
                      ? AppColors.colorFFFFE400
                      : AppColors.white;
                  return Visibility(
                      visible: controller.showDId.value,
                      child: Container(
                        margin: EdgeInsets.only(
                            top: 10.r, bottom: 20.r, left: 16.r, right: 16.r),
                        child: Stack(
                          alignment: AlignmentDirectional.bottomStart,
                          children: [
                            Image.asset(
                              currentLanguageIsSimpleChinese()
                                  ? R.bgMiningComputingZ
                                  : R.bgMiningComputingC,
                              width: double.infinity,
                            ),
                            Container(
                              margin: EdgeInsets.only(
                                  top: 10.r,
                                  bottom: 16.r,
                                  left: 90.r,
                                  right: 16.r),
                              child: Row(
                                children: [
                                  Text(
                                    '${controller.totalComputing}',
                                    style: TextStyle(
                                        fontSize: 12.sp, color: Colors.white),
                                  ),
                                  const Spacer(),
                                  Text(
                                    'AI ${L.computing_power_hosting.tr}丨',
                                    style: TextStyle(
                                        fontSize: 12.sp, color: Colors.white),
                                  ),
                                  Text(
                                    statues,
                                    style: TextStyle(
                                        fontSize: 12.sp, color: statuesColor),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ));
                }),
                Visibility(
                  visible: controller.showDId.value,
                  child: const DividerCus(
                    thickness: 10,
                  ),
                ),
                Container(
                  height: 30.r,
                  margin: EdgeInsets.only(top: 15.r),
                  padding: const EdgeInsets.only(left: 0, right: 15).r,
                  child: TabBar(
                    padding: EdgeInsets.zero,
                    // isScrollable: true,
                    labelPadding: const EdgeInsets.only(left: 20, right: 20).r,
                    indicatorColor: Theme.of(context).colorScheme.primary,
                    indicatorWeight: 2.h,
                    isScrollable: true,
                    indicatorSize: TabBarIndicatorSize.label,
                    labelColor: Colors.black,
                    labelStyle: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.bold,
                    ),
                    unselectedLabelColor: AppColors.colorFF999999,
                    controller: controller.tabController,
                    tabs: _createTableView(),
                  ),
                ),
                const DividerCus(
                  thickness: 1,
                ),
                Expanded(
                  child: RefreshIndicator(
                    displacement: 5,
                    onRefresh: () async {
                      await controller.refreshData();
                    },
                    notificationPredicate: (_) {
                      return true;
                    },
                    child: loadSuccessView(),
                  ),
                ),
              ],
            ),
          ],
        );});
      }),
    );
  }

  List<Tab> _createTableView() {
    var listTab = <Tab>[];
    listTab
      ..add(Tab(
        text: L.computing_power_contribution.tr,
      ))
      ..add(Tab(
        text: L.invite_new.tr,
      ))
      ..add(Tab(
        text: L.task.tr,
      ));
    return listTab;
  }

  Widget _createDetailItem(num number, String desc) {
    String v = '0';
    if (number.runtimeType == double) {
      v = (number.toDouble()).toStringAsEffective(3);
    } else if (number.runtimeType == int) {
      v = '$number';
    }
    return Expanded(
      flex: 1,
      child: Builder(
        builder: (context) {
          return GestureDetector(
            onLongPress: () {
              SmartDialog.showAttach(
                  targetContext: context,
                  builder: (c) {
                    return Container(
                      constraints: BoxConstraints(maxWidth: 200.w),
                      padding: const EdgeInsets.symmetric(
                              horizontal: 16, vertical: 16)
                          .r,
                      decoration: BoxDecoration(
                          color: AppColors.white,
                          borderRadius:
                              const BorderRadius.all(Radius.circular(16)).r),
                      child: Text(
                        '$number',
                        style: TextStyle(
                          fontSize: 18.sp,
                        ),
                      ),
                    );
                  });
            },
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text(
                  v,
                  style: TextStyle(
                      fontSize: 18.sp, color: AppColors.colorFFFFE400),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                SizedBox(height: 10.r),
                Text(desc,
                    style: TextStyle(fontSize: 12.sp, color: AppColors.white)),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget loadSuccessView() {
    return Container(
      color: AppColors.white,
      padding: const EdgeInsets.only(
        left: 15,
        right: 15,
      ).r,
      child: TabBarView(
        controller: controller.tabController,
        children: [
          Obx(() => _loadChatListView()),
          Obx(() => _loadInviteNewView()),
          Obx(() => _loadTaskView()),
        ],
      ),
    );
  }

  Widget _loadChatListView() {
    var allData = controller.chatList;
    Widget w;
    if (controller.isLoadError()) {
      w = loadErrorView(reloading: controller.getChatMiningData);
    }
    if (controller.isLoading()) {
      w = loadingView();
    } else {
      w = Stack(
        children: [
          Visibility(
            visible: allData.isEmpty,
            child: buildNoData(),
          ),
          ListView.builder(
            padding: EdgeInsets.only(bottom: 5.r),
            physics: const AlwaysScrollableScrollPhysics(),
            itemCount: allData.length,
            itemBuilder: (context, index) {
              var sessionData = allData[index];
              if (index == 0) {
                return _createChatTopView(context);
              }
              return GetBuilder<MiningController>(
                builder: (control) {
                  return MiningChatItem(
                    data: sessionData,
                  );
                },
              );
            },
          ),
        ],
      );
    }
    return w;
  }

  Widget _loadInviteNewView() {
    var allData = controller.inviteNewList;
    Widget w;
    if (controller.isLoadErrorInvite()) {
      w = loadErrorView(reloading: controller.getInviteMiningData);
    }
    if (controller.isLoadingInvite()) {
      w = loadingView();
    } else {
      w = Stack(
        children: [
          Visibility(
            visible: allData.isEmpty,
            child: buildNoData(),
          ),
          ListView.builder(
            padding: const EdgeInsets.only(top: 16).r,
            controller: controller.inviteScrollController,
            physics: const AlwaysScrollableScrollPhysics(),
            itemCount: allData.length,
            itemBuilder: (context, index) {
              if (index == 0) {
                return _createInviteNewTopView();
              } else {
                var sessionData = allData[index];
                return GetBuilder<MiningController>(
                  builder: (control) {
                    return MiningInviteNewItem(
                      data: sessionData,
                    );
                  },
                );
              }
            },
          ),
        ],
      );
    }
    return w;
  }

  Widget _loadTaskView() {
    var allData = controller.taskList;
    Widget w;
    if (controller.isLoadErrorTask()) {
      w = loadErrorView(reloading: controller.getChatMiningData);
    }
    if (controller.isLoadingTask()) {
      w = loadingView();
    } else {
      w = Stack(
        children: [
          Visibility(
            visible: allData.isEmpty,
            child: buildNoData(),
          ),
          ListView.builder(
            padding: const EdgeInsets.only(bottom: 5, top: 10).r,
            physics: const AlwaysScrollableScrollPhysics(),
            itemCount: allData.length,
            itemBuilder: (context, index) {
              if (index == 0) {
                w = _createWithdrawItem();
                return w;
              } else if (index == 1) {
                w = _createSignTastItem();
                return w;
              }
              var sessionData = allData[index];
              return GetBuilder<MiningController>(
                builder: (control) {
                  return MiningTaskItem(
                    data: sessionData,
                  );
                },
              );
            },
          ),
        ],
      );
    }
    return w;
  }

  Widget _createSignTastItem() {
    return Obx(() {
      return SizedBox(
        height: 80.r,
        child: Row(
          children: [
            MAvatarCircle(
              diameter: 44,
              textStyle: TextStyle(
                fontSize: 20.sp,
                color: Colors.white,
                fontWeight: FontWeight.normal,
              ),
              imagePath: R.iconMiningTaskHead,
              isNet: true,
            ),
            SizedBox(
              width: 11.r,
            ),
            Expanded(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    L.sign_in_today.tr,
                    style: TextStyle(
                      fontSize: 16.sp,
                      color: Colors.black,
                    ),
                  ),
                  SizedBox(
                    height: 8.r,
                  ),
                  // RichText(
                  //   textAlign: TextAlign.center,
                  //   text: TextSpan(
                  //     text: '+20',
                  //     style: TextStyle(
                  //       fontSize: 14.sp,
                  //       color: AppColors.colorFF3474d1,
                  //     ),
                  //     children: [
                  //       TextSpan(
                  //         text: " ${L.points.tr}",
                  //         style: TextStyle(
                  //           fontSize: 14.sp,
                  //           color: AppColors.colorFF333333,
                  //         ),
                  //       ),
                  //     ],
                  //   ),
                  // ),
                ],
              ),
            ),
            TextButton(
              style: ButtonStyle(
                shape: MaterialStateProperty.all(
                  RoundedRectangleBorder(
                    borderRadius: const BorderRadius.all(Radius.circular(20)).r,
                  ),
                ),
                backgroundColor: MaterialStateProperty.all(
                    controller.hasSign.value
                        ? AppColors.colorFFF2F2F2
                        : AppColors.colorFF3474d1),
                fixedSize: MaterialStateProperty.all(Size(90.w, 35.h)),
                padding: MaterialStateProperty.all(
                    const EdgeInsets.symmetric(horizontal: 10).r),
              ),
              onPressed: controller.canSign.value && !controller.hasSign.value
                  ? () {
                      controller.signIn();
                    }
                  : null,
              child: Text(
                controller.hasSign.value
                    ? L.has_sign_in_mining.tr
                    : L.sign_in_mining.tr,
                style: TextStyle(
                    fontSize: 12.sp,
                    color: controller.hasSign.value
                        ? AppColors.colorFF333333
                        : AppColors.white),
              ),
            ),
          ],
        ),
      );
    });
  }

  Widget _createWithdrawItem() {
    return Obx(() {
      return SizedBox(
        height: 80.r,
        child: Row(
          children: [
            MAvatarCircle(
              diameter: 44,
              textStyle: TextStyle(
                fontSize: 20.sp,
                color: Colors.white,
                fontWeight: FontWeight.normal,
              ),
              imagePath: R.iconMiningTaskHead,
              isNet: true,
            ),
            SizedBox(
              width: 11.r,
            ),
            Expanded(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    L.withdrawal_520_3t.tr,
                    style: TextStyle(
                      fontSize: 16.sp,
                      color: Colors.black,
                    ),
                  ),
                ],
              ),
            ),
            TextButton(
              style: ButtonStyle(
                shape: MaterialStateProperty.all(
                  RoundedRectangleBorder(
                    borderRadius: const BorderRadius.all(Radius.circular(20)).r,
                  ),
                ),
                backgroundColor: MaterialStateProperty.all(
                    controller.hasWithdraw.value
                        ? AppColors.colorFFF2F2F2
                        : AppColors.colorFF3474d1),
                fixedSize: MaterialStateProperty.all(Size(90.w, 35.h)),
                padding: MaterialStateProperty.all(
                    const EdgeInsets.symmetric(horizontal: 10).r),
              ),
              onPressed: null,
              child: Text(
                controller.hasWithdraw.value ? L.completed.tr : L.undone.tr,
                style: TextStyle(
                    fontSize: 12.sp,
                    color: controller.hasWithdraw.value
                        ? AppColors.colorFF333333
                        : AppColors.white),
              ),
            ),
          ],
        ),
      );
    });
  }

  Widget _createChatTopView(BuildContext context) {
    return Column(
      children: [
        Container(
          margin: const EdgeInsets.only(top: 16, bottom: 16).r,
          decoration: BoxDecoration(
              color: AppColors.colorFFF0F2F5,
              borderRadius: const BorderRadius.all(Radius.circular(10)).r),
          padding: const EdgeInsets.only(left: 15, right: 15, top: 0).r,
          child: Column(
            children: [
              Row(
                children: [
                  Obx(
                    () => Text(
                      L.mini_amount.trParams(
                        {
                          'amount': '${controller.periods.value}',
                        },
                      ),
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        color: AppColors.colorFF333333,
                      ),
                    ),
                  ),
                  const Spacer(),
                  TextButton(
                    onPressed: controller.pastState() > 0
                        ? () {
                            Get.to(
                              ContributionDetailPage(
                                  period: (controller.recentStage())),
                            );
                          }
                        : null,
                    child: Text(
                      L.past_data.tr,
                      style: TextStyle(
                        fontWeight: FontWeight.w500,
                        color: controller.pastState() > 0
                            ? AppColors.colorFF3474d1
                            : AppColors.colorFF919499,
                      ),
                    ),
                  )
                ],
              ),
              const DividerCus(),
              Container(
                padding: const EdgeInsets.symmetric(vertical: 10).r,
                height: 70.h,
                child: Row(
                  children: [
                    Expanded(
                      child: Container(
                        color: Colors.transparent,
                        alignment: Alignment.center,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Obx(
                              () => Text(
                                '${controller.thePeriodTotalValue.value}',
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.colorFF333333,
                                  fontSize: 24.sp,
                                ),
                              ),
                            ),
                            SizedBox(
                              height: 10.h,
                            ),
                            Text(
                              L.chat_contribution.tr,
                              style: TextStyle(
                                fontWeight: FontWeight.w500,
                                color: AppColors.colorFF333333,
                                fontSize: 12.sp,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    VerticalDivider(
                      width: 1.w,
                      color: AppColors.colorDivider,
                    ),
                    Expanded(
                      child: Container(
                        color: Colors.transparent,
                        alignment: Alignment.center,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Obx(
                              () => Text(
                                "${controller.miningChat.value.data?.signin ?? 0}",
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.colorFF333333,
                                  fontSize: 24.sp,
                                ),
                              ),
                            ),
                            SizedBox(
                              height: 10.h,
                            ),
                            Text(
                              L.sign_in_reward.tr,
                              style: TextStyle(
                                fontWeight: FontWeight.w500,
                                color: AppColors.colorFF333333,
                                fontSize: 12.sp,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        SizedBox(
          height: 44.r,
          child: Row(
            children: [
              Text(
                L.effective_time_period.tr,
                style: TextStyle(color: Colors.black, fontSize: 14.sp),
              ),
              const Spacer(),
              TextButton(
                onPressed: () {
                  controller.onSelectDay();
                },
                child: Text(
                    '${controller.chatDay.value} ${L.main_total.tr}:${decimalData(controller.todayTotal.value, 1)}',
                    style: TextStyle(color: Colors.black, fontSize: 14.sp)),
              ),
            ],
          ),
        ),
        const DividerCus(
          thickness: 1,
        ),
      ],
    );
  }

  Widget _createInviteNewTopView() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          height: 55.h,
          decoration: BoxDecoration(
              color: AppColors.colorFFF0F2F5,
              borderRadius: const BorderRadius.all(Radius.circular(10)).r),
          padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 15).r,
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Text(
                "${L.the_computing_power_of_this_period_is_divided_into.tr}:",
                style: TextStyle(
                  fontSize: 12.sp,
                  color: AppColors.colorFF333333,
                ),
              ),
              SizedBox(
                width: 5.w,
              ),
              Expanded(
                child: Obx(
                  () => Text(
                    "${controller.miningInviteLevelData.value.total ?? "0"}",
                    style: TextStyle(
                      fontSize: 20.sp,
                      color: AppColors.colorFF333333,
                      fontWeight: FontWeight.bold,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ),
              SizedBox(
                width: 5.w,
              ),
              Obx(
                () => TextButton(
                  onPressed: controller.pastComputingPowerState() > 0
                      ? () {
                          Get.to(
                            DivideIntoDetailPage(
                                period: (controller.pastComputingPowerState())),
                          );
                        }
                      : null,
                  child: Text(
                    L.detail.tr,
                    style: TextStyle(
                      fontSize: 14.sp,
                      fontWeight: FontWeight.w500,
                      color: controller.pastComputingPowerState() > 0
                          ? AppColors.colorFF3777d5
                          : AppColors.colorFFF7F7F7,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
        SizedBox(
          height: 5.h,
        ),
        Container(
          height: 90.h,
          decoration: BoxDecoration(
              color: AppColors.colorFFF0F2F5,
              borderRadius: const BorderRadius.all(Radius.circular(10)).r),
          padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 15).r,
          child: Row(
            children: [
              Expanded(
                child: Container(
                  color: Colors.transparent,
                  alignment: Alignment.center,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Obx(
                        () => Text(
                          '${controller.miningInvite.value.data?.level1 ?? 0}',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: AppColors.colorFF333333,
                            fontSize: 24.sp,
                          ),
                        ),
                      ),
                      SizedBox(
                        height: 15.h,
                      ),
                      Text(
                        L.total_lower_level_rewards.tr,
                        style: TextStyle(
                          fontWeight: FontWeight.w500,
                          color: AppColors.colorFF333333,
                          fontSize: 12.sp,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              VerticalDivider(
                width: 1.w,
                color: AppColors.colorDivider,
              ),
              Expanded(
                child: Container(
                  color: Colors.transparent,
                  alignment: Alignment.center,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Obx(
                        () => Text(
                          '${controller.miningInvite.value.data?.level2 ?? 0}',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: AppColors.colorFF333333,
                            fontSize: 24.sp,
                          ),
                        ),
                      ),
                      SizedBox(
                        height: 15.h,
                      ),
                      Text(
                        L.total_activity_rewards.tr,
                        style: TextStyle(
                          fontWeight: FontWeight.w500,
                          color: AppColors.colorFF333333,
                          fontSize: 12.sp,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        )
      ],
    );
  }
}
