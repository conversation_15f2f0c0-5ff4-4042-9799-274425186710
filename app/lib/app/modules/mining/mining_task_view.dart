//FileName mining_chat_item
// <AUTHOR>
//@Date 2023/3/28 17:01
import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/data/models/res/mining/mining_task.dart';
import 'package:flutter_metatel/core/utils/util.dart';
import 'package:flutter_metatel/core/values/colors.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../core/languages/l.dart';
import '../../../r.dart';
import '../../widgets/mavatar_circle_avatar.dart';

class MiningTaskItem extends StatelessWidget {
  const MiningTaskItem({this.data, super.key, this.callBack});

  final MiningTaskData? data;
  final VoidCallback? callBack;

  @override
  Widget build(BuildContext context) {
    String title =
        '${currentLanguageIsSimpleChinese() ? data?.tasktext : data?.etasktext ?? ''}';
    String number = '${data?.value ?? ''}';
    bool isFinish = data?.state ?? false;
    return SizedBox(
      height: 80.r,
      child: Row(
        children: [
          MAvatarCircle(
            diameter: 44,
            textStyle: TextStyle(
              fontSize: 20.sp,
              color: Colors.white,
              fontWeight: FontWeight.normal,
            ),
            imagePath: R.iconMiningTaskHead,
            isNet: true,
          ),
          SizedBox(
            width: 11.r,
          ),
          Expanded(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: Colors.black,
                  ),
                ),
                SizedBox(
                  height: 8.r,
                ),
                /*RichText(
                  textAlign: TextAlign.center,
                  text: TextSpan(
                    text: '+$number',
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: AppColors.colorFF3474d1,
                    ),
                    children: [
                      TextSpan(
                        text: " 3T",
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: AppColors.colorFF333333,
                        ),
                      ),
                    ],
                  ),
                ),*/
              ],
            ),
          ),
          TextButton(
            style: ButtonStyle(
              shape: MaterialStateProperty.all(
                RoundedRectangleBorder(
                  borderRadius: const BorderRadius.all(Radius.circular(20)).r,
                ),
              ),
              backgroundColor:
                  MaterialStateProperty.all(isFinish
                      ? AppColors.colorFFF2F2F2
                      : AppColors.colorFF3474d1),
              fixedSize: MaterialStateProperty.all(Size(90.w, 35.h)),
              padding: MaterialStateProperty.all(
                  const EdgeInsets.symmetric(horizontal: 10).r),
            ),
            onPressed: callBack,
            child: Text(
              isFinish ? L.completed.tr : L.undone.tr,
              style: TextStyle(
                  fontSize: 12.sp,
                  color: isFinish
                      ? AppColors.colorFF333333
                      : AppColors.white),
            ),
          ),
        ],
      ),
    );
  }
}
