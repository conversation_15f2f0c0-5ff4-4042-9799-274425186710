import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/widgets/divider_cus.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../core/languages/l.dart';
import '../../../../core/utils/util.dart';
import '../../../../core/values/colors.dart';
import '../../../../r.dart';
import '../mining_invant_new.dart';
import 'my_3t_detail_controller.dart';

class My3TDetailPage extends StatefulWidget {
  const My3TDetailPage({super.key});

  @override
  State<StatefulWidget> createState() => _My3TDetailPageState();
}

class _My3TDetailPageState extends State<My3TDetailPage> {
  final My3TDetailController controller = Get.put(My3TDetailController());

  @override
  void dispose() {
    Get.delete<My3TDetailController>();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          Container(
            height: 300.h,
            decoration: BoxDecoration(
              image: DecorationImage(
                image: AssetImage(
                  R.bg3tDetailOversea,
                ),
                fit: BoxFit.cover,
              ),
            ),
            child: Column(
              children: [
                AppBar(
                  title: Text(
                    L.detail_3t.tr,
                    style: TextStyle(
                      color: AppColors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 16.sp,
                    ),
                  ),
                  centerTitle: true,
                  leading: const BackButton(
                    color: AppColors.white,
                  ),
                  backgroundColor: Colors.transparent,
                  elevation: 0,
                ),
                Container(
                  margin: EdgeInsets.only(top: 50.h),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        L.obtained_today.tr,
                        style: TextStyle(
                          fontSize: 12.sp,
                          fontWeight: FontWeight.w500,
                          color: AppColors.white,
                        ),
                      ),
                      SizedBox(
                        width: 11.w,
                      ),
                      Obx(
                        () => Text(
                          "${controller.earnCount.value}",
                          style: TextStyle(
                            fontSize: 20.sp,
                            fontWeight: FontWeight.bold,
                            color: AppColors.colorFFFFE400,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                SizedBox(
                  height: 30.h,
                ),
                Container(
                  height: 107.h,
                  width: 290.w,
                  alignment: Alignment.center,
                  decoration: BoxDecoration(
                      color: AppColors.color669eacc5,
                      borderRadius:
                          const BorderRadius.all(Radius.circular(10)).r),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        L.my_3t_value.tr,
                        style: TextStyle(
                          color: AppColors.white,
                          fontSize: 16.sp,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      SizedBox(
                        height: 19.h,
                      ),
                      Text(
                        '00.00',
                        style: TextStyle(
                          color: AppColors.white,
                          fontSize: 30.sp,
                          fontWeight: FontWeight.bold,
                        ),
                      )
                    ],
                  ),
                )
              ],
            ),
          ),
          Container(
            height: 49.h,
            alignment: Alignment.centerLeft,
            color: Colors.white,
            padding: const EdgeInsets.symmetric(horizontal: 16).r,
            child: Text(
              L.detail_3t_list.tr,
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 16.sp,
              ),
            ),
          ),
          const DividerCus(),
          Expanded(
            child: RefreshIndicator(
              displacement: 5,
              onRefresh: () async {
                await controller.refreshData();
              },
              notificationPredicate: (_) {
                return true;
              },
              child: Obx(
                () {
                  if (controller.isLoading()) {
                    return loadingView();
                  } else if (controller.isLoadError()) {
                    return loadErrorView();
                  }
                  return _loadDateView();
                },
              ),
            ),
          ),
        ],
      ),
    );
  }
  Widget _loadDateView() {
    var allData = controller.dataList;
    return ListView.builder(
        padding: EdgeInsets.only(bottom: 5.r),
        physics: const AlwaysScrollableScrollPhysics(),
        itemCount: allData.length,
        itemBuilder: (context, index) {
          var sessionData = allData[index];
          return GetBuilder<My3TDetailController>(
            builder: (control) {
              return Container(
                color: AppColors.white,
                padding: const EdgeInsets.symmetric(horizontal: 16).r,
                child: MiningInviteNewItem(
                  data: sessionData,
                ),
              );
            },
          );
        });
  }
}
