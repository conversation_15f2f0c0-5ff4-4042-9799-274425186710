import 'package:flutter_metatel/app/data/models/res/mining/mining_invite.dart';
import 'package:get/get.dart';

import '../../../../core/values/config.dart';

class My3TDetailController extends GetxController{
  RxInt loadType = LoadType.defaultState.obs;
  var earnCount=0.obs;
  RxList<MiningInviteValue> dataList=RxList<MiningInviteValue>();
  refreshData() {}

  @override
  void onInit() {
    loadType.value=LoadType.success;
    super.onInit();
  }
  reloading(){

  }
  bool isLoading() {
    return loadType.value == LoadType.loading;
  }

  bool isLoadError() {
    return loadType.value == LoadType.error;
  }

  bool isLoadSuccess() {
    return loadType.value == LoadType.success;
  }
}