import 'package:flutter_metatel/app/data/providers/api/invite.dart';
import 'package:get/get.dart';

import '../../../data/models/res/mining/mining_chat_more.dart';

class ContributionDetailController extends GetxController {
  ///期数
  var amount = 1.obs;

  ///选择的日期
  var date = "3月23日".obs;

  ///当日贡献值
  var todayValue = 0.obs;
  RxList<MiningChatMoreValue> chatList = RxList<MiningChatMoreValue>();
  var miningChatMoreValue = MiningChatMoreValue().obs;

  var totalAmount = 0.obs;
  @override
  void onInit() {
    super.onInit();
  }

   refreshData(int stage) async {
     amount.value = stage;
    var m =
        await Get.find<InviteApi>().getMiningResult(stage, size: 20,page: 1);
    if (m.data != null) {
      var more=m.data;
      if(more!=null&&more.data!=null){
        totalAmount.value =more.data?.total??0;
        if(more.data?.value?.isNotEmpty??false){
          miningChatMoreValue.value=more.data!.value!.first;
        }
      }
      update();
    }
  }

  reloading() {}
}
