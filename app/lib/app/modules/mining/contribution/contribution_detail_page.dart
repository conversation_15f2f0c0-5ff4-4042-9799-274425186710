import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/modules/mining/contribution/contribution_detail_controller.dart';
import 'package:flutter_metatel/app/widgets/app_bar_cus.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../core/languages/l.dart';
import '../../../../core/values/colors.dart';
import '../../../../r.dart';
import '../../../widgets/custom_picker.dart';
import '../../../widgets/divider_cus.dart';

class ContributionDetailPage extends StatefulWidget {
  const ContributionDetailPage({super.key, required this.period});

  final int period;

  @override
  State<StatefulWidget> createState() => _ContributionDetailPageState();
}

class _ContributionDetailPageState extends State<ContributionDetailPage> {
  final ContributionDetailController controller =
      Get.put(ContributionDetailController());

  @override
  void initState() {
    controller.amount.value=widget.period;
    controller.refreshData(widget.period);
    super.initState();
  }

  @override
  void dispose() {
    Get.delete<ContributionDetailController>();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.white,
      appBar: AppBarCommon()
          .build(context, title: L.contribution_value_details.tr, actions: [
        Container(
          alignment: Alignment.center,
          margin: const EdgeInsets.only(
            right: 20,
          ),
          child: GestureDetector(
            onTap: () {showCutomPicker(context);},
            child: Text(
              L.select_mini_amount.tr,
              style: TextStyle(fontSize: 14.sp, color: AppColors.colorFF3474d1),
            ),
          ),
        )
      ]),
      body: Column(
        children: [
          const DividerCus(),

          ///统计
          Container(
            margin: const EdgeInsets.only(left: 16, right: 16, top: 16).r,
            height: 100.h,
            width: double.infinity,
            decoration: BoxDecoration(
              image: DecorationImage(
                  image: AssetImage(R.bgMinePt), fit: BoxFit.cover),
              borderRadius: const BorderRadius.all(
                Radius.circular(15),
              ).r,
            ),
            padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 20).r,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Obx(() => Expanded(
                      child: Text(
                        L.mini_amount.trParams(
                          {
                            'amount': '${controller.amount}',
                          },
                        ),
                        style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 14.sp,
                            color: Colors.white),
                      ),
                    )),
                Expanded(
                    child: Obx(
                  () => Text(
                    '${controller.miningChatMoreValue.value.stagevalue??0}',
                    style: TextStyle(
                      fontSize: 30.sp,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                )),
              ],
            ),
          ),
          Container(
            margin: const EdgeInsets.only(left: 16, right: 16, top: 16).r,
            width: double.infinity,
            decoration: BoxDecoration(
              color: AppColors.colorFFF0F2F5,
              borderRadius: const BorderRadius.all(
                Radius.circular(15),
              ).r,
            ),
            padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 20).r,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  L.data_overview.tr,
                  style: TextStyle(
                    fontWeight: FontWeight.w500,
                    fontSize: 14.sp,
                    color: AppColors.colorFF333333,
                  ),
                ),
                Obx(
                  () => Padding(
                    padding: EdgeInsets.only(top: 8.r, bottom: 8.r),
                    child: Text(
                      '${L.start_and_end_time.tr}${controller.miningChatMoreValue.value.stagetime?.first??''} | ${controller.miningChatMoreValue.value.stagetime?.last??''}',
                      style: TextStyle(
                        fontSize: 11.sp,
                        color: AppColors.colorFF999999,
                      ),
                    ),
                  ),
                ),
                const DividerCus(),
                Padding(
                    padding: EdgeInsets.only(top: 20.r, bottom: 16.r),
                    child: Row(
                      children: [
                        SizedBox(
                          width: 125.r,
                          child: Text(
                            L.the_number_of_participants.tr,
                            style: TextStyle(
                                color: AppColors.colorFF333333, fontSize: 12.sp),
                          ),
                        ),
                        Obx(() => Text(
                          controller.miningChatMoreValue.value.stageamount??'',
                          style: TextStyle(
                            fontWeight: FontWeight.w500,
                            fontSize: 16.sp,
                            color: AppColors.colorFF333333,
                          ),
                        )),
                      ],
                    )),
                Row(
                  children: [
                    SizedBox(
                      width: 125.r,
                      child: Text(
                        L.mining_points.tr,
                        style: TextStyle(
                            color: AppColors.colorFF333333, fontSize: 12.sp),
                      ),
                    ),
                    Obx(() => Text(
                      controller.miningChatMoreValue.value.reward??'',
                      style: TextStyle(
                        fontWeight: FontWeight.w500,
                        fontSize: 16.sp,
                        color: AppColors.colorFF333333,
                      ),
                    ),),

                  ],
                ),
                Padding(
                  padding: EdgeInsets.only(top: 16.r, bottom: 0.r),
                  child: Row(
                    children: [
                      SizedBox(
                        width: 125.r,
                        child: Text(
                          L.total_ore.tr,
                          style: TextStyle(
                              color: AppColors.colorFF333333, fontSize: 12.sp),
                        ),
                      ),

                      Obx(() => Text(
                        controller.miningChatMoreValue.value.total??'',
                        style: TextStyle(
                          fontWeight: FontWeight.w500,
                          fontSize: 16.sp,
                          color: AppColors.colorFF333333,
                        ),
                      )),

                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
  int curIndex=0;
  void showCutomPicker(BuildContext? context){
    var total=controller.totalAmount.value;

    if(context==null||total==0){
      return;
    }
    List<String> data=[];
    for(int i=1;i<=total;i++){
      data.add(i.toString());
    }

    CustomPicker.show(
      context,
      list: data,
      curIndex: curIndex,
      onSelectedItemChanged: (v) {
      },
      confirmClick: (v) {
        curIndex = v;
        int value= int.parse(data[v]);
        controller.refreshData(value);
      },
    );
  }
}
