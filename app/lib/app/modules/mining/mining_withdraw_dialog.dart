import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/data/services/config_service.dart';
import 'package:flutter_metatel/app/modules/mining/mining_controller.dart';
import 'package:flutter_metatel/core/languages/l.dart';
import 'package:flutter_metatel/core/utils/util.dart';
import 'package:flutter_metatel/core/values/colors.dart';
import 'package:flutter_metatel/r.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:flutter_web3/app/widgets/text_number_formatter.dart';
import 'package:flutter_web3/web3dart/src/utils/typed_data.dart';
import 'package:get/get.dart';
import 'package:decimal/decimal.dart';

class MiningWithdrawDialog extends StatefulWidget {
  const MiningWithdrawDialog({super.key, this.totalValue, this.canEditAddress=false,});

  final double? totalValue;
  final bool canEditAddress;

  @override
  State<MiningWithdrawDialog> createState() => _MiningWithdrawDialogState();
}

class _MiningWithdrawDialogState extends State<MiningWithdrawDialog> {
  final TextEditingController edCtl=TextEditingController();
  final TextEditingController edCtlAddress=TextEditingController(text:Get.find<AppConfigService>().readMiningMiningInfo());
  var loading = false.obs;
  @override
  Widget build(BuildContext context) {
    loading.value=false;
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: Center(
        child: Stack(
          alignment: Alignment.center,
          children: [
            Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  height: 360.h,
                  margin: const EdgeInsets.only(left: 16, right: 16).r,
                  decoration: BoxDecoration(
                      color: AppColors.white,
                      borderRadius:
                          const BorderRadius.all(Radius.circular(16)).r),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        margin: const EdgeInsets.only(
                          top: 25,
                        ).r,
                        alignment: Alignment.center,
                        child: Text(
                          "3T${L.withdraw.tr}",
                          style: TextStyle(
                            color: AppColors.colorFF000000,
                            fontSize: 18.sp,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      Container(
                        margin:
                        EdgeInsets.only(top: 35.h, left: 20, right: 20).r,
                        child: Text(
                          L.withdrawal_address.tr,
                          style: TextStyle(
                            fontSize: 14.sp,
                            color: AppColors.colorFF333333,
                          ),
                        ),
                      ),
                      _buildEditText(
                        edCtlAddress,
                        hintText: L.please_enter_the_withdrawal_address.tr,
                        fontSize: 12.sp,
                        maxLine: 2,
                        readOnly: !widget.canEditAddress
                      ),
                      Container(
                        margin:
                            EdgeInsets.only(top: 10.h, left: 20, right: 20).r,
                        child: Text(
                          L.withdraw.tr+L.mining_withdraw_amount.tr,
                          style: TextStyle(
                            fontSize: 14.sp,
                            color: AppColors.colorFF333333,
                          ),
                        ),
                      ),
                      _buildEditText(edCtl,
                          hintText: L.please_enter_the_withdrawal_amount.tr),
                      Container(
                        margin:
                            const EdgeInsets.only(top: 0, left: 20, right: 20)
                                .r,
                        child: Row(
                          children: [
                            Text(
                              L.currently_available.tr,
                              style: TextStyle(
                                color: AppColors.colorFF808080,
                                fontSize: 12.sp,
                              ),
                            ),
                            ConstrainedBox(
                              constraints: BoxConstraints(
                                maxWidth: 80.w,
                              ),
                              child:Text(
                                " ${formatDouble(widget.totalValue??0, 2) } 3T,",
                                style: TextStyle(
                                  color: AppColors.colorFF808080,
                                  fontSize: 12.sp,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            TextButton(
                              style: ButtonStyle(
                                padding:
                                    MaterialStateProperty.all(EdgeInsets.zero),
                              ),
                              onPressed: () {///小数点后9位
                                // String text="${widget.totalValue}";
                                String text = Decimal.parse(widget.totalValue.toString()).toString();
                                var split = text.split(".");
                                String integer=split[0];
                                String decimal=split.length==2?split[1]:"";
                                decimal=decimal.length>9?decimal.substring(0,9):decimal;
                                text="$integer${decimal.isNotEmpty?".$decimal":""}";
                                if(text!="0"){
                                  edCtl.value = TextEditingValue(
                                      text: text,
                                      selection: TextSelection.fromPosition(
                                          TextPosition(
                                              affinity: TextAffinity.downstream,
                                              offset: text.length)));
                                  setState(() {
                                  });
                                }
                              },
                              child: Text(
                                L.uni_all.tr + L.withdraw.tr,
                                style: TextStyle(
                                  color: AppColors.colorFF3474d1,
                                  fontSize: 12.sp,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      Container(
                        margin:
                            const EdgeInsets.only(top: 0, right: 20, left: 20)
                                .r,
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Image.asset(
                              R.iconMiningWithdrawHint,
                              width: 13.r,
                              height: 12.r,
                            ),
                            SizedBox(
                              width: 5.r,
                            ),
                            Expanded(
                              child: Text(
                                L.withdraw_hint.tr,
                                style: TextStyle(
                                  color: AppColors.colorFF333333,
                                  fontSize: 12.sp,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      const Spacer(),
                      Padding(
                        padding: const EdgeInsets.only(
                                left: 20, right:20,bottom: 30)
                            .r,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Flexible(
                              child: TextButton(
                                style: ButtonStyle(
                                  fixedSize: MaterialStateProperty.all(
                                      Size.fromHeight(40.h)),
                                  shape: MaterialStateProperty.all(
                                    RoundedRectangleBorder(
                                      borderRadius: BorderRadius.all(
                                              Radius.circular(5.r).r)
                                          .r,
                                    ),
                                  ),
                                  backgroundColor: MaterialStateProperty.all(
                                      AppColors.colorFFF2F2F2),
                                ),
                                onPressed: () {
                                  SmartDialog.dismiss();
                                },
                                child: Container(
                                  height: 40.h,
                                  alignment: Alignment.center,
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Text(
                                        L.cancel.tr,
                                        style: TextStyle(
                                          color: AppColors.colorFF333333,
                                          fontSize: 14.sp,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                            SizedBox(
                              width: 10.w,
                            ),
                            Flexible(
                              child: TextButton(
                                style: ButtonStyle(
                                  fixedSize: MaterialStateProperty.all(
                                      Size.fromHeight(40.h)),
                                  shape: MaterialStateProperty.all(
                                    RoundedRectangleBorder(
                                      borderRadius: BorderRadius.all(
                                              Radius.circular(5.r).r)
                                          .r,
                                    ),
                                  ),
                                  backgroundColor: MaterialStateProperty.all(
                                      AppColors.appDefault),
                                ),
                                onPressed: ()  {
                                  _onWithdraw();
                                },
                                child: Container(
                                  height: 40.h,
                                  alignment: Alignment.center,
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Text(
                                        L.confirm.tr,
                                        style: TextStyle(
                                          color: AppColors.white,
                                          fontSize: 14.sp,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            Obx(() => Visibility(
                  visible: loading.value,
                  child: Container(
                    margin: const EdgeInsets.only(left: 16, right: 16).r,
                    decoration: BoxDecoration(
                        color: const Color.fromRGBO(0, 0, 0, 0.46),
                        borderRadius:
                            const BorderRadius.all(Radius.circular(16)).r),
                    height: 360.h,
                    child: loadingView(),
                  ),
                )),
          ],
        ),
      ),
    );
  }

  _buildEditText(
    TextEditingController edCtl, {
    String? hintText,
    double? marginLeft,
    double? marginRight,
    double? fontSize,
        int maxLine=1,
        bool readOnly=false,
  }) {
    return Container(
      margin: EdgeInsets.only(
              left: marginLeft ?? 20, right: marginRight ?? 20, top: 10)
          .r,
      decoration: BoxDecoration(
        color: AppColors.colorFFF2F2F2,
        borderRadius: const BorderRadius.all(Radius.circular(10)).r,
      ),
      child: TextField(
        readOnly: readOnly,
        controller: edCtl,
        textAlignVertical: TextAlignVertical.center,
        inputFormatters: [
          // FilteringTextInputFormatter.allow(RegExp(r'[0-9.]')),
          TextNumberLimitFormatter(10, 9),
        ],
        maxLines: 2,
        keyboardType: TextInputType.number,
        style: TextStyle(
          fontSize: fontSize??14.sp,
          color: readOnly?AppColors.colorFF808080:null,
        ),
        decoration: InputDecoration(
          contentPadding: const EdgeInsets.only(left: 14, right: 14).r,
          fillColor: AppColors.colorFFF2F2F2,
          hintText: hintText,
          hintMaxLines: 1,
          hintStyle: TextStyle(fontSize:14.sp, color: AppColors.colorFF808080),
          border: OutlineInputBorder(
            borderRadius: const BorderRadius.all(Radius.circular(10)).r,
            borderSide: BorderSide.none,
          ),
        ),
      ),
    );
  }
  _onWithdraw() async {
    if(loading.value){
      toast("Withdrawal request in progress...");
      return;
    }
    loading.value = true;
    var amount = edCtl.text;
    if (amount.isEmpty) {
      toast(L.amount_is_required.tr);
      loading.value = false;
      return;
    }
    var contains = amount.contains(RegExp(r'[1-9]'));
    if (!contains) {
      edCtl.text="";
      setState(() {

      });
      loading.value = false;
      return;
    }
    bool ret = await Get.find<MiningController>()
        .withdraw(
      edCtl.text,
      address: edCtlAddress.text,
    );
    loading.value = false;
    if(ret){
      SmartDialog.dismiss(result: true);
    }
  }
}
