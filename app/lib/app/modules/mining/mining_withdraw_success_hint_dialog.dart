import 'package:flutter/material.dart';
import 'package:flutter_metatel/core/languages/l.dart';
import 'package:flutter_metatel/core/values/colors.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:get/get.dart';

class MiningWithdrawSuccessHintDialog extends StatefulWidget {
  const MiningWithdrawSuccessHintDialog({super.key,});


  @override
  State<MiningWithdrawSuccessHintDialog> createState() => _MiningWithdrawSuccessHintDialogState();
}

class _MiningWithdrawSuccessHintDialogState extends State<MiningWithdrawSuccessHintDialog> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: Center(
        child: Stack(
          alignment: Alignment.center,
          children: [
            Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  height: 220.h,
                  margin: const EdgeInsets.only(left: 16, right: 16).r,
                  decoration: BoxDecoration(
                      color: AppColors.white,
                      borderRadius:
                          const BorderRadius.all(Radius.circular(16)).r),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Container(
                        margin: const EdgeInsets.only(
                          top: 25,
                        ).r,
                        alignment: Alignment.center,
                        child: Text(
                          L.mining_withdraw_apply_submitted.tr,
                          style: TextStyle(
                            color: AppColors.colorFF000000,
                            fontSize: 18.sp,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      Expanded(
                        child: Container(
                          alignment: Alignment.center,
                          margin:
                              const EdgeInsets.only(left: 20, right: 20).r,
                          child: Text(
                            L.mining_withdraw_apply_submitted_hint.tr,
                            style: TextStyle(
                              fontSize: 14.sp,
                              color: AppColors.colorFF333333,
                            ),
                          ),
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.only(
                                left: 20, right:20,bottom: 30)
                            .r,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Flexible(
                              child: TextButton(
                                style: ButtonStyle(
                                  fixedSize: MaterialStateProperty.all(
                                      Size(120.w,40.h)),
                                  shape: MaterialStateProperty.all(
                                    RoundedRectangleBorder(
                                      borderRadius: BorderRadius.all(
                                              Radius.circular(5.r).r)
                                          .r,
                                    ),
                                  ),
                                  backgroundColor: MaterialStateProperty.all(
                                      AppColors.appDefault),
                                ),
                                onPressed: () async {
                                    SmartDialog.dismiss();
                                },
                                child: Container(
                                  height: 40.h,
                                  alignment: Alignment.center,
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Text(
                                        L.confirm.tr,
                                        style: TextStyle(
                                          color: AppColors.white,
                                          fontSize: 14.sp,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
