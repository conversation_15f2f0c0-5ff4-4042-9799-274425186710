//FileName mining_chat_item
// <AUTHOR>
//@Date 2023/3/28 17:01
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_web3/web3dart/src/utils/typed_data.dart';

import '../../../r.dart';
import '../../data/models/res/mining/mining_chat.dart';

class MiningChatItem extends StatelessWidget {
  const MiningChatItem({this.data, super.key});

  final MiningChatDetail? data;

  @override
  Widget build(BuildContext context) {
    int startTime = data?.time ?? 0;
    int endTime = startTime + 1;
    endTime = endTime == 24 ? 0 : endTime;
    String title =
        '${startTime > 9 ? startTime : '0$startTime'}:00-${endTime > 9 ? endTime : '0$endTime'}:00';
    String number=decimalData(data?.value??0,1);
    return SizedBox(
      height: 44.r,
      child: Row(
        children: [
          Image.asset(
            R.icoMiningTime,
            width: 16.r,
            height: 16.r,
          ),
          SizedBox(width: 11.r,),
          Expanded(child: Text(title,style: TextStyle(fontSize: 16.sp,color: Colors.black),)),
          Text(number,style: TextStyle(fontSize: 18.sp,color: Colors.black),)
        ],
      ),
    );
  }
}
