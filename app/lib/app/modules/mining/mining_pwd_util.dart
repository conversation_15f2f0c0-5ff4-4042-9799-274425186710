//FileName mining_pwd_util
// <AUTHOR>
//@Date 2023/3/29 11:51

import 'dart:convert';
import 'dart:typed_data';

import 'package:flutter_curve25519/flutter_curve25519.dart';
import 'package:flutter_web3/web3dart/src/crypto/formatting.dart';
import 'package:flutter_web3/web3dart/src/utils/typed_data.dart';
import 'package:get/get.dart';

import '../../../core/utils/app_log.dart';
import '../../../core/utils/base_58.dart';
import '../../../core/utils/pwd_util.dart';
import '../../../core/values/config.dart';
import '../../data/services/config_service.dart';

class MiningPwdUtil {
  static const tag = 'MiningPwdUtil';

  static Future<String?> encryptForData(String data,{String? rand}) async {
    var privateKey = await Get.find<AppConfigService>().getPrivateKey();
    var private = base64.decode(privateKey);
    var newKey = Curve25519.sharedKey(
        private, Uint8List.fromList(hexToBytes(rand??Config.random)));
    if (newKey.length != 32) {
      AppLogger.e('$tag encryptForData error=');
      return null;
    }
    var pwd = newKey;
    // AppLogger.d('encryptForData newKey ${toHex(newKey)}');
    var rad = Uint8List.sublistView(newKey, 16, 32);
    AppLogger.d('$tag encryptForData data=${data}');
    String body = PwdUtil.encryptForData(pwd, rad, data);
    // String decode = PwdUtil.decryptForData(pwd, rad, body);
    // AppLogger.d('$tag encryptForData decode=${decode}');
    return body;
  }

  static Future<String?> decryptForData(String data,{String? rand}) async {
    AppLogger.d('$tag decryptForData data=$data');
    // var info =String.fromCharCodes(Uint8List.fromList(base64.decode(data)));
    // AppLogger.d('$tag proBeMapList info=$info');
    AppLogger.d('decryptForData getPrivateKey');
    var privateKey = await Get.find<AppConfigService>().getPrivateKey();
    var private = base64.decode(privateKey);
    var newKey = Curve25519.sharedKey(
        private, Uint8List.fromList(hexToBytes(rand??Config.random)));
    if (newKey.length != 32) {
      AppLogger.e('$tag decryptForData error newKey.length not 32');
      return null;
    }
    var pwd = newKey;
    var rad = Uint8List.sublistView(newKey, 16, 32);
    String decode=PwdUtil.decryptForData(pwd, rad, data);
    AppLogger.d('$tag decryptForData decode2222=${decode}');
    return decode;
  }
}
