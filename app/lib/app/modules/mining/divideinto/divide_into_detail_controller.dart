import 'package:flutter_metatel/app/data/models/res/mining/mining_invite_level.dart';
import 'package:flutter_metatel/app/data/providers/api/invite.dart';
import 'package:get/get.dart';

class DivideIntoDetailController extends GetxController {
  ///期数
  var amount = 2.obs;

  ///当前选择期总收益
  final todayValue = "0.0".obs;

  var infoDirectSub = Info().obs;
  var infoIndirectSub = Info().obs;
  var infoIndirectSub2 = Info().obs;
  var infoCreateNode = Info().obs;
  var infoCityNode = Info().obs;

  refreshData(int stage) async {
    amount.value = stage;
    var m =
        await Get.find<InviteApi>().getMiningApiMinerInviteLevel(stage);
    var more = m.data;
    if (more != null && more.data != null) {
      var info = more.data?.info;
      var value = "${more.data?.total ?? 0}";
      todayValue.value = value;
      if (info != null) {
        for (Info i in info) {
          String type = i.type ?? "";
          switch (type) {
            case "C":
              infoDirectSub.value = i;
              break;
            case "B":
              infoIndirectSub.value = i;
              break;
            case "A":
              infoIndirectSub2.value = i;
              break;
            case "CITYNODE":
              infoCityNode.value = i;
              break;
            case "CREATENODE":
              infoCreateNode.value = i;
              break;
          }
        }
      }else{
        _reset();
      }
    }else{
      todayValue.value = "0.0";
      _reset();
    }
  }

  _reset() {
    infoDirectSub.value = Info();
    infoIndirectSub.value = Info();
    infoIndirectSub2.value = Info();
    infoCreateNode.value = Info();
    infoCityNode.value = Info();
  }
  reloading() {}
}
