import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/data/models/res/mining/mining_invite_level.dart';
import 'package:flutter_metatel/app/modules/mining/contribution/contribution_detail_controller.dart';
import 'package:flutter_metatel/app/modules/mining/divideinto/divide_into_detail_controller.dart';
import 'package:flutter_metatel/app/widgets/app_bar_cus.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../core/languages/l.dart';
import '../../../../core/values/colors.dart';
import '../../../../r.dart';
import '../../../widgets/custom_picker.dart';
import '../../../widgets/divider_cus.dart';

class DivideIntoDetailPage extends StatefulWidget {
  const DivideIntoDetailPage({super.key, required this.period});

  final int period;

  @override
  State<StatefulWidget> createState() => _DivideIntoDetailPageState();
}

class _DivideIntoDetailPageState extends State<DivideIntoDetailPage> {
  final DivideIntoDetailController controller =
      Get.put(DivideIntoDetailController());

  @override
  void initState() {
    controller.amount.value = widget.period;
    controller.refreshData(widget.period);
    super.initState();
  }

  @override
  void dispose() {
    Get.delete<ContributionDetailController>();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.white,
      appBar: AppBarCommon()
          .build(context, title: L.divide_into_detail.tr, actions: [
        Container(
          alignment: Alignment.center,
          margin: const EdgeInsets.only(
            right: 20,
          ),
          child: GestureDetector(
            onTap: () {
              showCutomPicker(context);
            },
            child: Text(
              L.select_mini_amount.tr,
              style: TextStyle(fontSize: 14.sp, color: AppColors.colorFF3474d1),
            ),
          ),
        )
      ]),
      body: ListView(
        padding: const EdgeInsets.only(bottom: 10).r,
        children: [
          const DividerCus(),

          ///统计
          Container(
            margin: const EdgeInsets.only(left: 16, right: 16, top: 16).r,
            height: 110.h,
            width: double.infinity,
            decoration: BoxDecoration(
              image: DecorationImage(
                  image: AssetImage(R.bgMinePt), fit: BoxFit.cover),
              borderRadius: const BorderRadius.all(
                Radius.circular(15),
              ).r,
            ),
            padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 15).r,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Obx(
                  () => Expanded(
                    flex: 1,
                    child: Text(
                      "${L.power_sharing.tr}(${L.mini_amount.trParams(
                        {
                          'amount': '${controller.amount}',
                        },
                      )})",
                      style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 14.sp,
                          color: Colors.white),
                    ),
                  ),
                ),
                Expanded(
                  flex: 2,
                  child: Obx(
                    () {
                      return Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            controller.todayValue.value,
                            style: TextStyle(
                              fontSize:  25.sp,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                        ],
                      );
                    },
                  ),
                ),
              ],
            ),
          ),

          ///数据概览
          Container(
            margin: const EdgeInsets.only(left: 16, right: 16, top: 16).r,
            width: double.infinity,
            decoration: BoxDecoration(
              color: AppColors.colorFFF0F2F5,
              borderRadius: const BorderRadius.all(
                Radius.circular(15),
              ).r,
            ),
            padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 20).r,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  L.data_overview.tr,
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 14.sp,
                    color: AppColors.colorFF333333,
                  ),
                ),
                SizedBox(
                  height: 14.h,
                ),
                const DividerCus(),

                ///直接
                _dataOverContainer(
                  L.direct_subordinate.tr,
                  L.divide_directly_into.tr,
                  controller.infoDirectSub,
                ),
                ///间接
                _dataOverContainer(
                  L.indirect_subordinate.tr,
                  L.indirect_share.tr,
                  controller.infoIndirectSub,
                  info2: controller.infoIndirectSub2,
                ),
              ],
            ),
          ),

          ///算力收益
          Container(
            margin: const EdgeInsets.only(left: 16, right: 16, top: 10).r,
            width: double.infinity,
            height: 214.h,
            decoration: BoxDecoration(
              color: AppColors.colorFFF0F2F5,
              borderRadius: const BorderRadius.all(
                Radius.circular(15),
              ).r,
            ),
            padding:
                const EdgeInsets.only(left: 14, right: 14, top: 16, bottom: 13)
                    .r,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  L.node_income.tr,
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 14.sp,
                    color: AppColors.colorFF333333,
                  ),
                ),
                SizedBox(
                  height: 14.h,
                ),
                const DividerCus(),
                Expanded(
                  child: Obx(
                    () {
                      // bool noData = controller.infoCreateNode.value&&
                      //     controller.infoCityNode.value == null;
                      return Stack(
                        alignment: Alignment.center,
                        children: [
                          // if (noData)
                          //   Positioned(
                          //     left: 0,
                          //     right: 0,
                          //     top: 0,
                          //     bottom: 0,
                          //     child: Text(
                          //       L.no_data.tr,
                          //       textAlign: TextAlign.center,
                          //     ),
                          //   ),
                          Column(
                            mainAxisSize: MainAxisSize.max,
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              _computerAdd(
                                R.icoCreateNodeMedal,
                                L.genesis_node.tr,
                                controller.infoCreateNode,
                              ),
                              _computerAdd(
                                R.icoCityNodeMedal,
                                L.city_node.tr,
                                controller.infoCityNode,
                              ),
                            ],
                          )
                        ],
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  int curIndex = 0;

  void showCutomPicker(BuildContext? context) {
    var total = widget.period;

    if (context == null || total == 0) {
      return;
    }
    List<String> data = [];
    for (int i = 2; i <= total; i++) {
      data.add(i.toString());
    }

    CustomPicker.show(
      context,
      list: data,
      curIndex: curIndex,
      onSelectedItemChanged: (v) {},
      confirmClick: (v) {
        curIndex = v;
        int value = int.parse(data[v]);
        controller.refreshData(value);
      },
    );
  }

  ///数据概览
  Widget _dataOverContainer(String title1, String title2, Rx<Info> info,
      {Rx<Info>? info2}) {
    return Container(
      margin: const EdgeInsets.only(top: 15),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: Get.width / 2 - 31.w,
            child: Column(
              mainAxisSize: MainAxisSize.max,
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  title1,
                  style: TextStyle(
                    color: AppColors.colorFF333333,
                    fontSize: 12.sp,
                  ),
                ),
                SizedBox(
                  height: 5.h,
                ),
                Obx(
                    () => Container(
                      alignment: Alignment.topLeft,
                      child: Text(
                        '${(info.value.number ?? 0) + (info2?.value.number ?? 0)}',
                        style: TextStyle(
                            color: AppColors.colorFF333333,
                            fontSize: 16.sp,
                            fontWeight: FontWeight.w500),
                      ),
                    ),
                  ),

              ],
            ),
          ),
          SizedBox(
            width: 10.w,
          ),
          SizedBox(
            width: Get.width / 2 - 41.w,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  title2,
                  style: TextStyle(
                    color: AppColors.colorFF333333,
                    fontSize: 12.sp,
                  ),
                ),
                SizedBox(
                  height: 5.h,
                ),
            Obx(
                    () => Container(
                      alignment: Alignment.topLeft,
                      child: Text(
                        '${(info.value.value ?? 0) + (info2?.value.value ?? 0)}',
                        style: TextStyle(
                            color: AppColors.colorFF333333,
                            fontSize: 16.sp,
                            fontWeight: FontWeight.w500),
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  ///算力收益
  Widget _computerAdd(
    String icon,
    String type,
    Rx<Info> info,
  ) {
    return Expanded(
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisSize: MainAxisSize.max,
        children: [
          Image.asset(
            icon,
            width: 64.r,
            height: 64.r,
          ),
          SizedBox(
            width: 16.w,
          ),
          Expanded(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      L.bonus_type.tr,
                      style: TextStyle(
                          fontSize: 12.sp, color: AppColors.colorFF333333),
                    ),
                    SizedBox(
                      width: 17.w,
                    ),
                    Expanded(
                      child: Text(
                        type,
                        style: TextStyle(
                          fontSize: 16.sp,
                          color: AppColors.colorFF333333,
                          fontWeight: FontWeight.bold,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
                SizedBox(
                  height: 10.h,
                ),
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      L.current_bonus.tr,
                      style: TextStyle(
                          fontSize: 12.sp, color: AppColors.colorFF333333),
                    ),
                    SizedBox(
                      width: 17.w,
                    ),
                    Expanded(
                      child: Text(
                        "${info.value.value ?? 0.0}",
                        style: TextStyle(
                          fontSize: 16.sp,
                          color: AppColors.colorFF333333,
                          fontWeight: FontWeight.bold,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
