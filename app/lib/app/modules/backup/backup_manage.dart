import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';

import 'package:external_path/external_path.dart';
import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/data/services/chatio_service.dart';
import 'package:flutter_metatel/app/data/services/config_service.dart';
import 'package:flutter_metatel/app/modules/backup/backup_image.dart';
import 'package:flutter_metatel/app/modules/backup/backup_recover_page.dart';
import 'package:flutter_metatel/app/modules/message/components/attachment_dialog.dart';
import 'package:flutter_metatel/app/widgets/chat_widget/photos_widget.dart';
import 'package:flutter_metatel/core/languages/l.dart';
import 'package:flutter_metatel/core/task/time_task.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:flutter_metatel/core/utils/util.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:wechat_camera_picker/wechat_camera_picker.dart';

import '../../../core/utils/crypto_util.dart';
import '../../../core/utils/pwd_util.dart';
import '../../../core/utils/screen.dart';
import '../../../core/values/config.dart';
import '../../data/models/backup_model.dart';
import '../../data/providers/db/database.dart';
import '../../data/services/event_service.dart';
import 'backup_recover_pwd_page.dart';

const String backupSuffix = 'backup';
const fileStart = 'This is custom data 3tchat start; ';
// const fileEnd = 'This is custom data 3tchat end; ';
const fileEnd = [0xFF,0xD9];
class BackupManage {
  static String tag = 'BackupManage';

  static Future<String?> getBackUpData(String path,
      {Uint8List? rawData}) async {
    AppLogger.d('$tag rawData is null ? ${rawData==null} getBackUpData path=$path');
    if (rawData != null && rawData.isNotEmpty) {
      return _getBackUpData(rawData);
    } else {
      var file = File(path);
      if (file.existsSync()) {
        var data = file.readAsBytesSync();
        return _getBackUpData(data);
      } else {
        AppLogger.d('$tag not exists');
      }
    }
    return null;
  }

  static Future<String?> _getBackUpData(Uint8List rawData) async {
    String encBackupData = utf8.decode(rawData, allowMalformed: true);
    if (encBackupData.contains(fileStart)) {
      var data = encBackupData.substring(
          encBackupData.indexOf(fileStart), encBackupData.length);
      data = data.replaceAll(fileStart, '');
      data = data.substring(0, data.length - 2);
      AppLogger.d('$tag getBackUpData data=$data');
      return data;
    }
    return null;
  }

  static Future<String?> setBackUpData(String data, Uint8List oldImageBytes) async {
    AppLogger.d('$tag setBackUpData data=$data');
    String timeText = DateFormat("yyyyMMddHHmmss").format(TimeTask.instance.getNowDateTime());
    String filePath = appTempAbsolutePath('backup_$timeText.jpg') ?? '';
    if (filePath.isEmpty) {
      toast(L.backup_file_fail.tr);
      return null;
    }
    try {
      var file = File(filePath);
      final buf = BytesBuilder();
      buf.add(Uint8List.fromList(oldImageBytes));
      buf.add(fileStart.codeUnits);
      buf.add(Uint8List.fromList(data.codeUnits));
      if (buf.isNotEmpty) {
        buf.add([0xFF,0xD9]);
        await file.writeAsBytes(buf.toBytes(), flush: true);
        return filePath;
      }
    } catch (e) {
      return null;
    }
    return null;
  }

  /// 备份到pc
  static Future<void> backup(
    String password,
    List<ContactData> contacts,List<ContactData> blackcontacts,{bool isLocal = false}
  ) async {

    var encBackupData = await generateTrueBackupData(password, contacts,blackcontacts);
    if (encBackupData.isEmpty) {
      toast(L.backup_data_enc_fail.tr);
      return;
    }

    AppLogger.d('BackupManage encBackupData=$encBackupData');

    String filePath = appTempAbsolutePath('${uuid()}.$backupSuffix') ?? '';
    if (filePath.isEmpty) {
      toast(L.backup_file_fail.tr);
      return;
    }


    var file = await File(filePath).writeAsString(encBackupData, flush: true);
    if (!file.existsSync()) {
      toast(L.backup_file_fail.tr);
      return;
    }
    var userName= await Get.find<AppConfigService>().getUserName()??'';

    DateTime now = TimeTask.instance.getNowDateTime();
    if(!isLocal){
    MessageEvent event = MessageEvent(
      uuid(),
      chatType: ChatType.singleChat,
      dateTime: now,
      direction: Direction.outGoing,
      fileName: 'backup_${now.toString()}.$backupSuffix',
      fileSize: file.lengthSync(),
      owner: userName,
      state: BubbleItemState.LOAD,
      type: MessageType.file,
    );


    await sendMessage(
      file,
      event,
      pushType: PushType.message,
    );
    toast(L.backup_file_send.tr);
    } else {
      if (await Permission.storage.request().isGranted ||
          await Permission.photos.request().isGranted ||
          await Permission.videos.request().isGranted) {
        // Either the permission was already granted before or the user just granted it.
        var savePathDir = await ExternalPath.getExternalStoragePublicDirectory(
            ExternalPath.DIRECTORY_DOCUMENTS);
        String decPath =
            "$savePathDir/${'backup_${now.toString()}.$backupSuffix'}";
        file.copySync(decPath);
        if (!Platform.isIOS) {
          Screen.updateFile(decPath);
        }
        AppLogger.d('BackupManage decPath=$decPath');
        file.delete();
        if (File(decPath).existsSync()) {
          toast(L.has_save_to.tr + decPath, toastLength: Toast.LENGTH_LONG);
        } else {
          toast(L.save_failed.tr);
        }
      } else {
        toast(L.chat_storage_permission_refuse_then_cannot_operate_file.tr);
      }
    }
  }

  static Future<String> generateTrueBackupData(String pwd,List<ContactData> contacts,List<ContactData> blackList,{List<SessionBackInfo>? sessions}) async {
    List<Map<String, dynamic>> datas = [];
    for (var element in contacts) {
      var mapData = element.toJson();
      datas.add(mapData);
    }

    var username = Get.find<AppConfigService>().getUserName();
    var mapData = BackupModel(username: username, contacts: datas,blackContacts: blackList,sessions: sessions).toJson();
    var backupData = jsonEncode(mapData);
    AppLogger.d('generateTrueBackupData backupData=$backupData');
    var encBackupData = PwdUtil.encryptData(pwd, backupData);
    return encBackupData;
  }

  /// 恢复
  static Future<void> recover(
    String password,
    String? filePath,
    String? fragment,
  ) async {
    var file = File(filePath ?? '');
    if (!file.existsSync()) {
      toast(L.backup_file_no_exists.tr);
      return;
    }

    if (isCipherFile(filePath)) {
      var tempFilePath = appTempAbsolutePath(uuid());
      var result = await mtDecryptFile(filePath, tempFilePath, fragment);

      file = File(tempFilePath ?? '');
      if (result != 0 || !file.existsSync()) {
        toast(L.backup_file_dec_fail.tr);
        return;
      }
    }

    var encBackupData = file.readAsStringSync();
    _recover(password,encBackupData);
  }

  static Future<void> _recover(String pwd, String data) async {
    var backupData = PwdUtil.decryptData(pwd, data);
    if (backupData.isEmpty) {
      toast(L.backup_data_dec_fail.tr);
      return;
    }

    var model = BackupModel.fromJson(jsonDecode(backupData));
    var username = Get.find<AppConfigService>().getUserName();
    if (model.username != username) {
      toast(L.not_current_account_backup_data.tr);
      return;
    }
    AppLogger.d('_recover =${model.blackContacts}');
    var contacts = await Get.find<AppDatabase>().allFriendContact().get();
    List<String> existUsernames = []; // 数据库存在的好友
    for (var element in contacts) {
      existUsernames.add(element.username);
    }

    List<ContactCompanion> updateContacts = [];
    List<dynamic> datas = model.contacts ?? [];
    for (var element in datas) {
      var data = ContactData.fromJson(element);
      if (existUsernames.contains(data.username) ||
          data.type == ContactType.fileHelper) {
        continue;
      }

      var companion = ContactCompanion.insert(
        username: data.username,
        displayname: ofNullable(data.displayname),
        localname: ofNullable(data.localname),
        avatarPath: ofNullable(data.avatarPath),
        avatarUrl: ofNullable(data.avatarUrl),
        chatBackgroundPath: ofNullable(data.chatBackgroundPath),
        chatBackgroundUrl: ofNullable(data.chatBackgroundUrl),
        fragment: ofNullable(data.fragment),
        edit: ofNullable(data.edit),
        read: ofNullable(data.read),
        state: ofNullable(data.state),
        fistname: ofNullable(data.fistname),
        lastname: ofNullable(data.lastname),
        mobile: ofNullable(data.mobile),
        type: ofNullable(data.type),
        createTime:
            ofNullable(TimeTask.instance.getNowTime().toDouble()),
        updateTime:
            ofNullable(TimeTask.instance.getNowTime().toDouble()),
      );
      updateContacts.add(companion);
    }

    if (updateContacts.isNotEmpty) {
      await Get.find<AppDatabase>().insertOrUpdateContactDatas(updateContacts);
    }
    if(model.blackContacts!=null&&model.blackContacts!.isNotEmpty){
      List<dynamic> datas = model.blackContacts ?? [];
      for (var element in datas) {
        var data = ContactData.fromJson(element);
        if (existUsernames.contains(data.username) ||
            data.type == ContactType.fileHelper) {
          continue;
        }

        var companion = ContactCompanion.insert(
          username: data.username,
          displayname: ofNullable(data.displayname),
          localname: ofNullable(data.localname),
          avatarPath: ofNullable(data.avatarPath),
          avatarUrl: ofNullable(data.avatarUrl),
          chatBackgroundPath: ofNullable(data.chatBackgroundPath),
          chatBackgroundUrl: ofNullable(data.chatBackgroundUrl),
          fragment: ofNullable(data.fragment),
          edit: ofNullable(data.edit),
          read: ofNullable(data.read),
          state: ofNullable(data.state),
          fistname: ofNullable(data.fistname),
          lastname: ofNullable(data.lastname),
          mobile: ofNullable(data.mobile),
          type: ofNullable(data.type),
          isBlack: ofNullable(data.isBlack),
          createTime:
          ofNullable(TimeTask.instance.getNowTime().toDouble()),
          updateTime:
          ofNullable(TimeTask.instance.getNowTime().toDouble()),
        );
        updateContacts.add(companion);
      }
      if (updateContacts.isNotEmpty) {
        await Get.find<AppDatabase>().insertOrUpdateContactDatas(updateContacts);
      }
    }
    if(model.sessions!=null && model.sessions?.isNotEmpty == true){
      for(var s in model.sessions!){
        var data = SessionBackInfo.fromJson(s);
        await Get.find<AppConfigService>().saveUserMessageSilenceState(data.username??'', true);
        await Get.find<AppDatabase>().updateSessionSilence(true, data.username??'');
      }

    }
    toast(L.data_recover_success.tr);
  }

  /// 是否是备份文件
  static bool isBackupFile(String filePath) {
    var suffix = fileSuffix(filePath);
    if (isCipherFile(filePath)) {
      var newFilePath = filePath.replaceAll(Config.enc, '');
      suffix = fileSuffix(newFilePath);
    }

    if (backupSuffix == suffix) {
      return true;
    }
    return false;
  }

  static Future<void> showBackupWidget({bool isLocal = false}) async {
    Get.to(() =>
      const BackupRecoverPage(),
    );
  }
  static Future<void> localBackupImage() async {
   var blackList = await Get.find<AppDatabase>().allBlackedContact().get();
    var contacts = await Get.find<AppDatabase>().allFriendContact().get();
    var sessionInfo = await Get.find<AppDatabase>().allSessionIsSilence().get();
    if (contacts.isEmpty&&blackList.isEmpty) {
      toast(L.no_friend_backup.tr);
      return;
    }
    var result = await Get.to(() =>
      const BackupRecoverPwdPage(type: BackupRecoverType.backup),
    );
    if (result == null) {
      return;
    }
    var trueBackupData = await generateTrueBackupData(result, contacts,blackList,sessions: sessionInfo);
    Get.to(() => BackupImage(backupTrueData: trueBackupData));
    // SmartDialog.show(
    //   builder: (
    //     c
    //   ) {
    //     return BackupImage(
    //       backupTrueData: trueBackupData,
    //     );
    //   },
    //   useAnimation: false,
    //   useSystem: true,
    //   clickMaskDismiss: false,
    //   backDismiss: false,
    // );
  }

  backupToPc(List<ContactData> contacts) async {
    var result = await Get.to(() =>
      const BackupRecoverPwdPage(type: BackupRecoverType.backup),
    );
    if (result == null) {
      return;
    }
    var blackList = await Get.find<AppDatabase>().allBlackedContact().get();

    showLoadingDialog();
    await BackupManage.backup(result.toString(), contacts,blackList, isLocal:false);
    dismissLoadingDialog();
  }

  static Future<void> showRecoverWidget(
    String? filePath,
    String? fragment,
  ) async {
    var result = await Get.to(() =>
      const BackupRecoverPwdPage(type: BackupRecoverType.recover),
    );
    if (result == null) {
      return;
    }

    showLoadingDialog();
    await BackupManage.recover(result.toString(), filePath, fragment);
    dismissLoadingDialog();
  }

  static Future chooseImageForRecover(BuildContext context) async {
    showDialog(
      barrierDismissible: false,
      context: context,
      useSafeArea: false,
      builder: (_) {
        return PhotosWidget(
            useDCIM: true,
            type: RequestType.image,
            haveCamera: false,
            isMultiple: false,
            rawData: true,
            onAttachmentSelect: (List<AttachmentEntity> attachments) async{
              Get.back();
              AttachmentEntity attachment = attachments[0];
              if (attachment.filePath?.isNotEmpty ?? false) {
                var data =await getBackUpData(attachment.filePath!,rawData: attachment.originBytes);
                if(data?.isNotEmpty??false){
                  _fromImgRecover(data!);
                }else{
                  toast(L.recover_restore_fail_2.tr);
                }
              }
            });
      },
    );
  }

  static void _fromImgRecover(String data) async {
    var result = await Get.to(() =>
      const BackupRecoverPwdPage(type: BackupRecoverType.recover),
    );
    if (result == null) {
      return;
    }
    showLoadingDialog();
    await _recover(result.toString(), data);
    dismissLoadingDialog();
  }
}
