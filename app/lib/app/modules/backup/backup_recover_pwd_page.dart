import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_metatel/core/languages/l.dart';
import 'package:flutter_metatel/core/values/colors.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_secure_keyboard/flutter_secure_keyboard.dart';
import 'package:get/get.dart';

import '../../../core/utils/util.dart';
import '../../widgets/verification/verification_box.dart';
import '../../widgets/verification/verification_box_item.dart';

enum BackupRecoverType {
  backup,
  recover,
}

class BackupRecoverPwdPage extends StatefulWidget {
  const BackupRecoverPwdPage({
    super.key,
    required this.type,
  });

  final BackupRecoverType type;

  @override
  State<BackupRecoverPwdPage> createState() => _BackupRecoverPwdPageState();
}

class _BackupRecoverPwdPageState extends State<BackupRecoverPwdPage> {
  final _secureKeyboardController = SecureKeyboardController();

  @override
  void initState() {
    super.initState();
    _show();
  }

  @override
  void dispose() {
    _secureKeyboardController.dispose();
    super.dispose();
  }

  void _show() {
    final schedulerPhase = SchedulerBinding.instance.schedulerPhase;
    if (schedulerPhase == SchedulerPhase.persistentCallbacks) {
      SchedulerBinding.instance.addPostFrameCallback((_) {
        _showSecureKeyBoardView();
      });
    } else {
      _showSecureKeyBoardView();
    }
  }

  _showSecureKeyBoardView() {
    verificationBoxKey.currentState?.onValueChange('');
    showSecureKeyBoard(_secureKeyboardController, (value) {
      verificationBoxKey.currentState
          ?.onValueChange(String.fromCharCodes(value));
    });
  }

  Widget _buildBody() {
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 25).r,
        child: Column(
          children: [        
            if(widget.type==BackupRecoverType.backup)
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                SizedBox(height: 30.r),
                Center(
                  child: Text(
                    L.backup_data_recovery_password_title.tr,
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 20.sp,
                      color: Colors.black,                    
                    ),
                  ),
                ),
                SizedBox(height: 26.r),
                Text(
                  L.backup_recover_hint1.tr,
                  style: TextStyle(
                    fontSize: 13.sp,
                    color: Colors.black,
                  ),
                ),
                SizedBox(height: 8.r),
                Text(
                  L.backup_recover_hint2.tr,
                  style: TextStyle(
                    fontSize: 13.sp,
                    color: Colors.black,
                  ),
                ),
                SizedBox(height: 24.r),
                Text(
                  L.note_this_operation_only_backs_up_close_friend_data_label.tr,
                  style: TextStyle(
                    fontSize: 13.sp,
                    fontWeight: FontWeight.bold,
                    color: AppColors.colorFFCD3A3A,
                  ),
                ),
                SizedBox(height: 3.r),
                Text(
                  L.note_this_operation_only_backs_up_close_friend_data.tr,
                  style: TextStyle(
                    fontSize: 13.sp,
                  ),
                ),
              ],
            ),          
            SizedBox(height: 40.r),        
            Text(
              L.input_recover_pwd.tr,
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 15.sp,
                color: Colors.black,
              ),
            ),
            SizedBox(height: 12.r),
            // 输入框
            VerificationBox(
              obscureText: true,
              borderColor: Colors.grey,
              borderRadius: 0,
              borderWidth: 1.r,
              key: verificationBoxKey,
              autoFocus: false,
              focusBorderColor: Colors.lightBlue,
              count: 6,
              type: VerificationBoxItemType.box,
              onSubmitted: test,
              textStyle: TextStyle(color: Colors.grey, fontSize: 20.sp),
            ),
          ],
        ),
      ),
    );
  }
  test(String value,Function? f){
    if (!_secureKeyboardController.isShowing) {
      Get.back(result: value);
    } else {
      Get.back(result: value);
    }
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      child: Scaffold(
        backgroundColor: Colors.white,
        appBar: AppBar(
          elevation: 0.8,
          title: Text(
            widget.type == BackupRecoverType.backup
                ? L.data_backup.tr
                : L.data_recover.tr,
            style: TextStyle(
              fontSize: 15.sp,
              color: Colors.black,
              fontWeight: FontWeight.bold,
            ),
          ),
          centerTitle: true,
          backgroundColor: Colors.white,
        ),
        // 键盘
        body: WithSecureKeyboard(
          keyboardHeight: kKeyboardDefaultHeight,
          controller: _secureKeyboardController,
          child: _buildBody(),
        ),
      ),
      onWillPop: () async {
        if (_secureKeyboardController.isShowing) {
          Get.back();
        }
        return true;
      },
    );
  }
}
