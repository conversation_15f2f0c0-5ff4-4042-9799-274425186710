import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/modules/backup/backup_manage.dart';
import 'package:flutter_metatel/core/languages/l.dart';
import 'package:flutter_metatel/core/values/colors.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_secure_keyboard/flutter_secure_keyboard.dart';
import 'package:get/get.dart';




class BackupRecoverPage extends StatefulWidget {
  const BackupRecoverPage({
    super.key,
  });


  @override
  State<BackupRecoverPage> createState() => _BackupRecoverPageState();
}

class _BackupRecoverPageState extends State<BackupRecoverPage> {
  final _secureKeyboardController = SecureKeyboardController();

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    _secureKeyboardController.dispose();
    super.dispose();
  }

  Widget _buildBody() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 25).r,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,      
        children: [
          SizedBox(height: 25.r),
          Center(
            child: Text(
              L.backup_data_title.tr,
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 19.sp,
                color: Colors.black,
              ),
            ),
          ),
          SizedBox(height: 30.r),
          Text(
            L.backup_hint_1.tr,
            style: TextStyle(
              fontSize: 13.sp,
              color: Colors.black,
            ),
          ),
          SizedBox(height: 20.r),
          Text(
            L.backup_hint_2.tr,
            style: TextStyle(
              fontSize: 13.sp,
              color: Colors.black,
            ),
          ),
          SizedBox(height: 20.r),
          Text(
            L.backup_hint_3.tr,
            style: TextStyle(
              fontSize: 13.sp,
              color: Colors.black,
            ),
          ),
          SizedBox(height: 20.r),
          Text(
            L.backup_hint_4.tr,
            style: TextStyle(
              fontSize: 13.sp,
              color: Colors.black,
            ),
          ),
          const Spacer(),
          Center(
            child: ElevatedButton(
              onPressed: (){
                BackupManage.localBackupImage();
              }, 
              child: Text(
                L.backup_data.tr,
                style: TextStyle(
                  fontSize: 14.sp,
                  color: AppColors.white,
                ),
              ),
            ),
          ),
          Center(
            child: TextButton(
              style: TextButton.styleFrom(
                foregroundColor: AppColors.primaryBgColor1,
              ),
              onPressed: (){
                BackupManage.chooseImageForRecover(context);
              }, 
              child: Text(
                L.import_data.tr,
                style: TextStyle(
                  fontSize: 14.sp,
                ),
              ),
            ),
          ),
          SizedBox(height: 30.r),            
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: Colors.white,
        appBar: AppBar(
          elevation: 0.8,
          title: Text(
            L.data_backup_recover.tr,
            style: TextStyle(
              fontSize: 15.sp,
              color: Colors.black,
              fontWeight: FontWeight.bold,
            ),
          ),
          centerTitle: true,
          backgroundColor: Colors.white,
        ),
        // 键盘
        body: _buildBody(),
    );
  }
}
