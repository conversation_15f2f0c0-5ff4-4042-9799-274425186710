import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:drift/drift.dart';
import 'package:flutter/material.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:flutter_metatel/app/data/enums/enum.dart';
import 'package:flutter_metatel/app/data/enums/path.dart';
import 'package:flutter_metatel/app/data/services/chatio_service.dart';
import 'package:flutter_metatel/app/data/services/config_service.dart';
import 'package:flutter_metatel/app/data/services/event_service.dart';
import 'package:flutter_metatel/app/modules/backup/backup_manage.dart';
import 'package:flutter_metatel/core/languages/l.dart';
import 'package:flutter_metatel/core/task/time_task.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:flutter_metatel/core/utils/file_util.dart';
import 'package:flutter_metatel/core/utils/save_image_to_photo.dart';
import 'package:flutter_metatel/core/utils/util.dart';
import 'package:get/get.dart';

class BackupImageController extends GetxController {
  final AppConfigService conf = Get.find();
  final GlobalKey backupImageKey = GlobalKey();
  var loading = true.obs;
  late String qrcode;
  late String dataBackupTrue;
  String? imageBackupPath;

  @override
  onInit() {
    qrCodeData();
    super.onInit();
  }

  @override
  onClose() {
    deleteFileIsExists(imageBackupPath);
    super.onClose();
  }

  copyLink() async {}

  share() async {
    var file = File(imageBackupPath ?? "");
    if (file.existsSync()) {
      // Share.shareXFiles([XFile(imageBackupPath!)]);
      var path = await mtCopyFile(file, appSupportDir);
      var messageEvent = MessageEvent(uuid(),
          owner: '',
          type: MessageType.image,
          // 转发时会根据选择对象修改，daen
          chatType: ChatType.singleChat,
          fileName: path?.split('/').last,
          direction: 1,
          filePath: path,
          dateTime: TimeTask.instance.getNowDateTime());
      await shareForwardMessage(Get.context!, [messageEvent]);
    }
  }

  saveToLoc() async {
    var file = File(imageBackupPath ?? "");
    if (file.existsSync()) {
      loading.value=true;
      await saveFileLoc(
        filePath: file.path,
        saveFileType: SaveFileType.image,
        // saveSuccessToast: L.backup_contact_backup_success.tr,
      );
      loading.value=false;

    }
  }

  qrCodeData() {
    String? userName = conf.getUserName();
    Map<String, dynamic> mapJson = {};
    mapJson['firstName'] = '';
    mapJson['lastName'] = '';
    mapJson['nick'] = conf.getMySelfDisplayName();
    mapJson['number'] = userName ?? '';
    mapJson['type'] = '4';
    qrcode = json.encode(mapJson);
  }

  void generateImageBackup(String dataBackupTrue,{bool compress=false}) async {
    await Future.delayed(const Duration(milliseconds: 300));
    AppLogger.d("generateImageBackup 1");
    var imageBytes =
        await RepaintBoundaryUtils.savePhotoByGlobalKey(backupImageKey);
    AppLogger.d("generateImageBackup 2");
    if (imageBytes != null) {
      Uint8List imageByteList=imageBytes.buffer.asUint8List();
      File? needCompressFile;
      if(compress){
        try {
          needCompressFile = File("${appTempDir?.path ?? ""}/${uuid()}.jpg");
          needCompressFile.writeAsBytesSync(imageBytes.buffer.asUint8List(), flush: true);
          AppLogger.d("generateImageBackup 3");
          var path = needCompressFile.path;
          String ext = fileSuffix(path);
          String pathNoSuffix = path.split(".$ext").first;
          XFile? result = await FlutterImageCompress.compressAndGetFile(
            path,
            '${pathNoSuffix}_compress.jpg',
          );
          AppLogger.d("generateImageBackup 4");
          File? f;
          if(result!=null){
            f=File(result.path);
          }
          if (f != null) {
            deleteFileIsExists(path);
            f=await f.rename(path);
          }
          if (f != null) {
            needCompressFile = f;
          }
          AppLogger.d("generateImageBackup 5 needCompressFile==${needCompressFile.path}");
          if(needCompressFile.existsSync()){
            imageByteList=needCompressFile.readAsBytesSync();
          }
          AppLogger.d("generateImageBackup 6");
        } catch (e) {}
      }
      imageBackupPath =
          await BackupManage.setBackUpData(dataBackupTrue, imageByteList);
      deleteFileIsExists(needCompressFile?.path);
      AppLogger.d("generateImageBackup 7");
      toast(L.backup_data_generated.tr);
      loading.value = false;
    }else{
      loading.value = false;
    }
  }
}
