import 'package:extended_image/extended_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/data/services/config_service.dart';
import 'package:flutter_metatel/app/modules/backup/backup_image_controller.dart';
import 'package:flutter_metatel/app/widgets/loading_view.dart';
import 'package:flutter_metatel/app/widgets/mavatar_circle_avatar.dart';
import 'package:flutter_metatel/core/task/time_task.dart';
import 'package:flutter_metatel/core/utils/util.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:qr_flutter/qr_flutter.dart';

import '../../../../../core/languages/l.dart';
import '../../../../../core/values/Config.dart';
import '../../../../../core/values/colors.dart';
import '../../../../../r.dart';

class BackupImage extends StatefulWidget {
  const BackupImage({super.key, required this.backupTrueData});

  final String backupTrueData;

  @override
  State<BackupImage> createState() => _BackupImageState();
}

class _BackupImageState extends State<BackupImage> {
  final BackupImageController _controller = Get.put(BackupImageController());
  final _bgImageHeight = 590.r;
  final _bgImageWidth = 330.r;

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      _controller.generateImageBackup(widget.backupTrueData, compress: true);
    });
  }

  @override
  void dispose() {
    Get.delete<BackupImageController>();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          L.data_backup.tr,
          style: TextStyle(
            fontSize: 15.sp,
            color: Colors.black,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
        backgroundColor: Colors.white,
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    return Stack(
      children: [
        Container(
          height: 1.sh,
          width: 1.sw,        
          padding: EdgeInsets.symmetric(horizontal: 35.r),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                AppColors.primaryBgColor2,
                AppColors.primaryBgColor1,                            
              ],
              begin: Alignment(-1,-1),
              end: Alignment(1,1),      
            ),
          ),
          child: SingleChildScrollView(
            physics: const BouncingScrollPhysics(),
            child: Column(
              children: [
                SizedBox(height: 62.r,),
                _buildShareSection(),
                SizedBox(height: 28.r),
                _buildActionSection(),
                SizedBox(height: 95.r),
              ],
            ),
          ),
        ),
        /// 加载显示
        Center(
          child: Obx(
            () => Visibility(
              visible: _controller.loading.value,
              child: const LoadingView(
                color: AppColors.white,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildShareSection(){
    return RepaintBoundary(
      key: _controller.backupImageKey,
      child: SizedBox(
        height: _bgImageHeight,
        width: _bgImageWidth,
        child: Stack(
          children: [
            /// 背景图
            Container(
              height: _bgImageHeight,
              width: _bgImageWidth,             
              child: ExtendedImage.asset(
                R.bgDataBackupCard,
                fit: BoxFit.cover,
                imageCacheName: "backup_cache",
                loadStateChanged: (state) {
                  if (state.extendedImageLoadState ==
                      LoadState.failed) {
                    Get.find<AppConfigService>()
                        .saveInvitePosterPath("");
                    return ExtendedImage.asset(
                      R.icoPicLoadFailed,
                      fit: BoxFit.cover,
                      imageCacheName: R.icoPicLoadFailed,
                      clearMemoryCacheWhenDispose: true,
                      maxBytes: Config.maxImageBytes,
                    );
                  }
                  return null;
                },
              ),
            ),
            /// 资料显示
            _personalInfo(),
          ],
        ),
      ),
    );
  }

  Widget _personalInfo() {
    return SizedBox(
      width: _bgImageWidth,
      child: Column(
        children: [
          SizedBox(height: _bgImageHeight/2 + 40.r),
          /// 信息框
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 10).r,
            child: Container(
              // width: _bgImageWidth,
              padding:
                  const EdgeInsets.only(left: 27, right: 48, top: 20, bottom: 15).r,
              // alignment: Alignment.center,
              decoration: BoxDecoration(
                image: DecorationImage(
                    image: AssetImage(R.bgDataBackupPersonalInfo), fit: BoxFit.fill),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Row(
                    children: [
                      MAvatarCircle(
                        imagePath: appSupporAbsolutePath(
                            _controller.conf.getMySelfAvatarInfoModel().path),
                        diameter: 63,
                        text: _controller.conf.getMySelfDisplayName(),
                      ),
                      SizedBox(
                        width: 10.r,
                      ),
                      Column(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          SizedBox(
                            width: 135.r,
                            child: Text(
                              _controller.conf.getMySelfDisplayName(),
                              style: TextStyle(
                                color: AppColors.white,
                                fontSize: 17.sp,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          SizedBox(
                            height: 8.h,
                          ),
                          Text(
                            DateFormat("yyyy/MM/dd HH:mm:ss").format(TimeTask.instance.getNowDateTime()),
                            style: TextStyle(
                                color: AppColors.colorFF808080, fontSize: 12.sp),
                          ),
                        ],
                      ),
                    ],
                  ),
                  SizedBox(
                    height: 16.h,
                  ),
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Expanded(
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              "Invitation Code",
                              style: TextStyle(
                                color: AppColors.white,
                                fontSize: 14.sp,
                              ),
                            ),
                            Text(
                              "邀请码",
                              style: TextStyle(
                                  color: AppColors.colorFFB3B3B3, fontSize: 12.sp),
                            ),
                            SizedBox(
                              height: 10.h,
                            ),
                            Text(
                              _controller.conf.readInviteCode(),
                              style: TextStyle(
                                  color: AppColors.colorFFF4BC28, fontSize: 25.sp),
                            ),
                          ],
                        ),
                      ),
            
                      // SizedBox(
                      //   width: 54.w,
                      // ),
                      QrImageView(
                        backgroundColor: AppColors.white,
                        padding: EdgeInsets.zero,
                        size: 80.r,
                        data: _controller.qrcode,
                        gapless: false,
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionSection() {
    return Container(  
      padding: EdgeInsets.symmetric(horizontal: 30.r),
      child: Center(
        child: _buildButton(
          icon: R.iconMomentSave, 
          text: L.save_to_local.tr,
          onTap: _controller.saveToLoc,
        ),
      ),  
    );
  }

  Widget _buildButton({required String icon, required String text, Function()? onTap}){
    return ElevatedButton(
      onPressed: onTap, 
      style: ElevatedButton.styleFrom(
        backgroundColor: AppColors.white,
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Image.asset(
            R.iconDownload,
            width: 17.r,
            height: 17.r,
          ),
          SizedBox(width: 10.r),
          Text(
            text,
            style: TextStyle(              
              fontSize: 15.sp,
              color: AppColors.primaryBgColor1,
            ),
          ),
        ],
      ),      
    );
  }
}
