// import 'package:flutter/material.dart';
// import 'package:flutter_secure_keyboard/flutter_secure_keyboard.dart';
// import 'package:get/get.dart';
//
// import '../../../../../core/languages/l.dart';
// import '../../../../../core/utils/pwd_util.dart';
// import '../../../../../core/utils/util.dart';
//
// class WalletPwdModifyController extends GetxController{
//   String? errorInfo;
//   final GlobalKey _formKey = GlobalKey<FormState>();
//   final GlobalKey _formKey2 = GlobalKey<FormState>();
//   final GlobalKey _formKey3 = GlobalKey<FormState>();
//   final _secureKeyboardController = SecureKeyboardController();
//
//
//   final FocusNode focusNode = FocusNode();
//   final FocusNode focusNode2 = FocusNode();
//   final FocusNode focusNode3 = FocusNode();
//
//   final TextEditingController textEditControllerOld = TextEditingController();
//   final TextEditingController textEditControllerNew = TextEditingController();
//   final TextEditingController textEditingControllerAgain =
//   TextEditingController();
//
//
//   GlobalKey get formKey=> _formKey;
//
//   GlobalKey get formKey2 => _formKey2;
//
//   GlobalKey get formKey3 => _formKey3;
//
//   get secureKeyboardController => _secureKeyboardController;
//
//   onTapPwdTextFiled(FocusNode focusNode,TextEditingController controller){
//     controller.text = "";
//     if(controller==textEditControllerNew){
//       textEditingControllerAgain.text="";
//     }
//     _secureKeyboardController.hide();
//     showSecureKeyBoard(_secureKeyboardController,(valueList) {
//       var value = String.fromCharCodes(valueList);
//       controller.text = String.fromCharCodes(valueList);
//       controller.selection = TextSelection.fromPosition(TextPosition(
//           affinity: TextAffinity.downstream, offset: value.length));
//     });
//     focusNode.requestFocus();
//   }
//
//   onConfirm(PwdType pwdType) async {
//     String pwdNew = textEditControllerNew.text;
//     String pwdAgain = textEditingControllerAgain.text;
//     String oldPwd = textEditControllerOld.text;
//     if(errorInfo == null){
//       if(pwdNew.isEmpty){
//         errorInfo=L.change_password_need_new_password.tr;
//       }else if(await PwdUtil.isSameWalletPwd(oldPwd)){
//         errorInfo=L.password_check_old_password_error.tr;
//       }else if(pwdNew!=pwdAgain){
//         errorInfo = L.password_check_password_not_same.tr;
//       }else if(pwdNew==oldPwd){
//         errorInfo = L.password_no_same_to_old.tr;
//       }
//     }
//     if (errorInfo == null ) {
//       showLoadingDialog(isBack: false);
//       bool success=false;
//       if(pwdType==PwdType.annotative){
//         success = await PwdUtil.updateAnnotativeWords(oldPwd,pwdNew);
//       }else if(pwdType==PwdType.destruction){
//         success = await PwdUtil.saveDestructionPwd(pwdNew);
//       }else{
//         success = await PwdUtil.saveWalletPwd(pwdNew);
//       }
//       dismissLoadingDialog();
//       if(success){
//         Get.back(result: true);
//       }else{
//
//       }
//     } else {
//       toast(errorInfo ?? "");
//     }
//   }
//   String? validator(String value, bool isAgainInput){
//     errorInfo = null;
//     if (value.trim().length > 5) {
//       if (isAgainInput &&
//           textEditingControllerAgain.text != textEditControllerNew.text) {
//         errorInfo = L.password_check_password_not_same.tr;
//       }
//     } else {
//       errorInfo = L.must_be_a_6_digit_password.tr;
//     }
//     return errorInfo;
//   }
// }