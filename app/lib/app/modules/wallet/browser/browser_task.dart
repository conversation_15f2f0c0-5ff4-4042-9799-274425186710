//FileName browser_task
// <AUTHOR>
//@Date 2023/7/18 15:44
import 'dart:convert';

import 'package:flutter_metatel/app/data/providers/api/invite.dart';
import 'package:flutter_metatel/app/data/providers/api/own.dart';
import 'package:flutter_metatel/app/data/services/config_service.dart';
import 'package:flutter_metatel/app/modules/mining/mining_pwd_util.dart';
import 'package:flutter_metatel/core/task/time_task.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:get/get.dart';

class BrowserTask {
  static final instance = BrowserTask();
  final tag = 'BrowserTask';
  int _adnonce = 0;



  void addCount() async {
    var map = getBrowser();
    if (map.isNotEmpty) {
      if (map.keys.contains('value')) {
        map['value'] = (map['value'] ?? 0) + 1;
      } else {
        map['value'] = 1;
      }
      if (map.keys.contains('total')) {
        map['total'] = (map['total'] ?? 0) + 1;
      } else {
        map['total'] = 1;
      }
    } else {
      map = {'value': 1, 'total': 1};
    }
    // AppLogger.d('$tag addCount map=$map');
    saveCount(map);
  }

  void saveCount(Map map) {
    Get.find<AppConfigService>().saveBrowserClickCount(map);
  }

  Map<String, dynamic> getBrowser() {
    var map = Get.find<AppConfigService>().readBrowserClickCount();
    // AppLogger.d('$tag getBrowser map=$map');
    return map;
  }

  void updateBrowserCount() {
    var map = getBrowser();
    if (map.isNotEmpty) {
      map['value'] = 0;
    }
    Get.find<AppConfigService>().saveBrowserClickCount(map);
  }

  void submitBrowserCount() {
    var time = Get.find<AppConfigService>().readBrowserClickTime();
    var data = TimeTask.instance.getNowDateTime();
    String t = '${data.year}-${data.month}-${data.day}';
    if (t != time) {
      _submit(t);
    }
  }

  _submit(String time) async {
    Map<String, dynamic> map = {};
    var m = getBrowser();
    if (m.isEmpty || m['value'] == 0) {
      AppLogger.d('$tag m is null');
      return;
    }
    map['key'] = 'open_browser';
    map['value'] = m['value'];
    map['total'] = m['total'];
    map['desc'] = 'click';
    map['create_time'] = TimeTask.instance.getNowTime();

    var data = json.encode(map);
    // AppLogger.d('$tag data=$data');
    var res = await submitBrowser(data);
    if (res) {
      Get.find<AppConfigService>().saveBrowserClickTime(time);
      updateBrowserCount();
    }
  }
}
