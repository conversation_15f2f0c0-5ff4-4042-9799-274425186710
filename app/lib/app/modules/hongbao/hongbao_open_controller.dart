import 'dart:convert';

import 'package:flutter_metatel/app/data/enums/enum.dart';
import 'package:flutter_metatel/app/data/models/avatar_model.dart';
import 'package:flutter_metatel/app/data/models/hongbao_check_status_model.dart';
import 'package:flutter_metatel/app/data/providers/api/hongbao_api.dart';
import 'package:flutter_metatel/app/data/services/config_service.dart';
import 'package:flutter_metatel/core/languages/l.dart';
import 'package:flutter_metatel/core/task/hongbao_task.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:flutter_metatel/core/utils/audio_play_helper.dart';
import 'package:flutter_metatel/core/utils/util.dart';
import 'package:flutter_web3/app/core/hw_wallet_manager.dart';
import 'package:get/get.dart';

class HongbaoOpenController extends GetxController {
  String? msgId;
  String? signInfo;
  String? extension;
  String? groupId;
  ChatType? chatType;
  Rx<HongBaoOperateState?> hongBaoOperateState = Rx<HongBaoOperateState?>(null);
  RxDouble amount = 0.0.obs;
  RxString tokenName = ''.obs;
  HongbaoCheckStatusModelData? data;

  void setCurrentData(
      String? msgId,
      String? signInfo,
      String? extension,
      String? groupId,
      ChatType? chatType,
      HongBaoOperateState? hongBaoOperateState,HongbaoCheckStatusModelData data) {
    this.msgId = msgId;
    this.signInfo = signInfo;
    this.extension = extension;
    this.groupId = groupId;
    this.chatType = chatType;
    this.hongBaoOperateState.value = hongBaoOperateState;
    this.data = data;
    HongBaoTask().getNameByToken(
        data.redPacket?.tokenAddress ?? '').then((value) {
      tokenName.value = value ??'';

    });
  }

  // 持续检查红包状态, 当状态返回 2 时 返回 true 否则返回 false
  Future<dynamic> checkRedPacketStatus(
      String account, String msgId, ChatType chatType, String to,
      {String? type, int maxTry = 0}) async {
    var response = await RedPacketApi().checkRedPacketStatus(account, msgId);
    AppLogger.d('checkRedPacketStatus response: $response');
    var status = response?.data?.redPacket?.status;
    if (status == '2') {
      return true;
    }
    if (maxTry > 0) {
      await Future.delayed(Duration(seconds: 3));
      return await checkRedPacketStatus(account, msgId, chatType, to,
          type: type, maxTry: maxTry - 1);
    }
    return false;
  }

  // 按钮点击后触发的抢红包逻辑
  void qiangHongBao() async {
    var fromAddress = await HwWalletManager().getWalletAddress();
    var accountDomain = Get.find<AppConfigService>().getUserName();
    var nickName = Get.find<AppConfigService>().getMySelfDisplayName();
    AvatarModel avatar = AvatarModel.fromJson(
        Get.find<AppConfigService>().getMySelfAvatarInfo() ?? {});
    var account = Get.find<AppConfigService>().getUserNameWithoutDomain();

    var info = {
      'topic': msgId.toString(),
      'address': fromAddress.toString(),
      'account': accountDomain.toString(),
      'name': nickName.toString(),
      'head': avatar.avatarUrl.toString(),
    };
    AppLogger.d('qiangHongBao info = $info');
    var signInfo = await HongBaoTask().signDataToString(jsonEncode(info));
    AppLogger.d('qiangHongBao signInfo: $signInfo');
    AudioPlayHelper.instance.playHongBao1();

    if (signInfo != null) {
      var response = await RedPacketApi()
          .qiangHongBaoRequest(account.toString(), signInfo);
      if (response != null) {
        AppLogger.d('Received response: ${response.toJson()}');
        if(response.code == 607) {
          toast(L.not_activate_wallet_yet.tr);
          return;
        }
        if (response.code == 618 || response.code == 200) {
          hongBaoOperateState.value = HongBaoOperateState.myselfRob;
          var log = response.data?.log?.firstWhere((e) => e.account == accountDomain);
          if (log != null) {
            amount.value = log.amount?.toDouble() ??0.0;
          }
        } else if (response.code == 640) {//被抢完了
          hongBaoOperateState.value = HongBaoOperateState.robbed;
        } else {//其他情况都是被领取完了
          hongBaoOperateState.value = HongBaoOperateState.robbed;
        }
        var finishData = {
          'type': 3,
          'msgId': msgId.toString(),
          'groupId': groupId,
          'chatType': chatType?.index,
        };
        HongBaoTask().jumpHongBaoDetailHtml(finishData);
        AppLogger.d('qiangHongBao finishData = $finishData');
      }
    }
  }
}
