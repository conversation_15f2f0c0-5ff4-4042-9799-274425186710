// import 'dart:convert';
// import 'dart:io';
// import 'dart:typed_data';

// import 'package:flutter/material.dart';
// import 'package:flutter_metatel/app/data/enums/enum.dart';
// import 'package:flutter_metatel/app/data/events/events.dart';
// import 'package:flutter_metatel/app/data/providers/api/invite.dart';
// import 'package:flutter_metatel/app/data/providers/api/operation_center_api.dart';
// import 'package:flutter_metatel/app/data/providers/db/database.dart';
// import 'package:flutter_metatel/app/data/services/chatio_service.dart';
// import 'package:flutter_metatel/app/data/services/probe_service.dart';
// import 'package:flutter_metatel/app/modules/home/<USER>/mine_controller.dart';
// import 'package:flutter_metatel/app/modules/password/pay/trx/trx_pay_pwd.dart';
// import 'package:flutter_metatel/app/modules/wallet/browser/browser_task.dart';
// import 'package:flutter_metatel/app/widgets/code/code_view.dart';
// import 'package:flutter_metatel/core/task/hongbao_task.dart';
// import 'package:flutter_metatel/core/task/time_task.dart';
// import 'package:flutter_metatel/core/utils/app_log.dart';
// import 'package:flutter_metatel/core/utils/audio_play_helper.dart';
// import 'package:flutter_metatel/core/utils/events_bus.dart';
// import 'package:flutter_metatel/core/utils/jump.dart';
// import 'package:flutter_metatel/core/utils/save_image_to_photo.dart';
// import 'package:flutter_metatel/core/utils/util.dart';

// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
// import 'package:flutter_web3/app/core/open_wallet_helper.dart';
// import 'package:flutter_web3/app/data/enums/enum.dart';
// import 'package:flutter_web3/app/data/models/wallet_model.dart';
// import 'package:flutter_web3/app/data/models/wallet_node.dart';
// import 'package:flutter_web3/app/data/models/wallet_transaction.dart';
// import 'package:flutter_web3/app/modules/wallet/wallet_main.dart';
// import 'package:get/get.dart';
// import 'package:lecle_flutter_absolute_path/lecle_flutter_absolute_path.dart';

// import '../../../core/languages/l.dart';
// import '../../../core/utils/screen.dart';
// import '../../../core/values/config.dart';
// import '../../../routes/pages.dart';
// import '../../data/models/avatar_model.dart';
// import '../../data/models/user_message_model.dart';
// import '../../data/providers/api/api.dart';
// import '../../data/services/config_service.dart';
// import '../../data/services/event_service.dart';
// import '../../data/services/secure_store_service.dart';
// import '../home/<USER>/set/set_oversea.dart';
// import '../mining/mining_pwd_util.dart';
// import '../password/pay/pay_pwd.dart';
// import 'package:flutter_metatel/core/utils/file_util.dart';
// import 'package:path/path.dart' as path;

// typedef MainRequestCallback = Future<dynamic>? Function(dynamic);

// class HongBaoManage {
//   // 单例公开访问点
//   factory HongBaoManage() => _HongBaoManageInstance();

//   // bool? isOpenMore;

//   Future<bool> getIsOpenMore() async {
//     if (Platform.isAndroid) {
//       return await Screen.isOpenedMore;
//     }
//     return false;
//   }

//   // 静态私有成员，没有初始化
//   static HongBaoManage? _instance;

//   // 私有构造函数
//   HongBaoManage._() {
//     // 具体初始化代码
//   }

//   // 静态、同步、私有访问点
//   static HongBaoManage _HongBaoManageInstance() {
//     _instance ??= HongBaoManage._();
//     return _instance!;
//   }

//   Future<bool> pay(WalletModel value) async {
//     String? walletPwd =
//     await Get.find<SecureStoreService>().secureReadWalletPwd();
//     if (walletPwd == null || walletPwd.isEmpty) {
//       // toast(L.no_wallet_password_need_set.tr);
//       await SmartDialog.show(builder: (context) {
//         return AlertDialog(
//           content: Text(L.no_wallet_password_need_set.tr),
//           actions: [
//             TextButton(
//               child: Text(L.cancel.tr),
//               onPressed: () => SmartDialog.dismiss(), //关闭对话框,
//             ),
//             TextButton(
//               child: Text(L.module_activity_system_setting_button_text.tr),
//               onPressed: () {
//                 SmartDialog.dismiss();
//                 Get.toNamed(Routes.WalletPwdReset);
//               }, //关闭对话框,
//             ),
//           ],
//           actionsAlignment: MainAxisAlignment.end,
//         );
//       });
//       return false;
//     }
//     var result = await showModalBottomSheet(
//       context: Get.context!,
//       isScrollControlled: true,
//       shape: RoundedRectangleBorder(
//         borderRadius: BorderRadius.vertical(
//             top: const Radius.circular(12).r,
//             bottom: const Radius.circular(0).r),
//       ),
//       builder: (context) {
//         return value.chainType == 0
//             ? PayPwdPage(
//           walletModel: value,
//           title: L.please_enter_payment_password.tr,
//         )
//             : TrxPayPwdPage(
//           walletModel: value,
//           title: L.please_enter_payment_password.tr,
//         );
//       },
//     );
//     return result ?? false;
//   }

//   Future<String?> checkPwd() async {
//     String? walletPwd =
//     await Get.find<SecureStoreService>().secureReadWalletPwd();
//     if (walletPwd == null || walletPwd.isEmpty) {
//       return await SmartDialog.show(builder: (context) {
//         return AlertDialog(
//           content: Text(L.no_wallet_password_need_set.tr),
//           actions: [
//             TextButton(
//               child: Text(L.cancel.tr),
//               onPressed: () => SmartDialog.dismiss(), //关闭对话框,
//             ),
//             TextButton(
//               child: Text(L.module_activity_system_setting_button_text.tr),
//               onPressed: () {
//                 SmartDialog.dismiss();
//                 Get.toNamed(Routes.WalletPwdReset);
//               }, //关闭对话框,
//             ),
//           ],
//           actionsAlignment: MainAxisAlignment.end,
//         );
//       });
//     }
//     var result = await showModalBottomSheet(
//       context: Get.context!,
//       isScrollControlled: true,
//       shape: RoundedRectangleBorder(
//         borderRadius: BorderRadius.vertical(
//             top: const Radius.circular(12).r,
//             bottom: const Radius.circular(0).r),
//       ),
//       builder: (context) {
//         return PayPwdPage(
//           walletModel: null,
//           title: L.please_enter_wallet_password.tr,
//         );
//       },
//     );
//     return result ? "" : L.cancel.tr;
//   }

//   Future<String?> checkAllConditionIsSetting() async {
//     // 检测是否设置交易密码
//     String? walletPwd = await Get.find<SecureStoreService>().secureReadWalletPwd();
//     if (walletPwd == null || walletPwd.isEmpty) {
//       // 未设置交易密码，检测是否启用钱包
//       if(_checkHasActivateWallet()){  // 已启用钱包，弹出设置交易密码提示
//         return await _showConfirmationDialog(
//             L.no_wallet_password_need_set.tr,
//             () {
//               SmartDialog.dismiss();
//               Get.toNamed(Routes.WalletPwdReset);
//             },
//         );
//       } else {  // 未启用钱包，弹出启用钱包提示
//         return await _showConfirmationDialog(
//             L.activate_wallet_confirmation_desc.tr,
//             () {
//               SmartDialog.dismiss();
//               Get.to(SetOverSeaPage(Get.find<MineController>()));
//             },
//         );
//       }
//     }
//     return 'ok';
//   }

//   bool _checkHasActivateWallet() {
//     return Get.find<AppConfigService>().hasActivateWallet() ?? false;
//   }

//   Future _showConfirmationDialog(String desc, Function()? onPositiveBtnPressed) async {
//       return await SmartDialog.show(builder: (context) {
//         return AlertDialog(
//         content: Text(desc),
//           actions: [
//             TextButton(
//               child: Text(L.cancel.tr),
//               onPressed: () => SmartDialog.dismiss(), //关闭对话框,
//             ),
//             TextButton(
//               child: Text(L.module_activity_system_setting_button_text.tr),
//             onPressed: onPositiveBtnPressed, //关闭对话框,
//             ),
//           ],
//           actionsAlignment: MainAxisAlignment.end,
//         );
//       });
//   }

//   sendImg(ByteData? byteData) async {
//     var pathurl = await RepaintBoundaryUtils.savePhoto(byteData,
//         toastSuccess: L.save_success.tr, toastError: L.save_failed.tr);
//     String? p = await LecleFlutterAbsolutePath.getAbsolutePath(uri: pathurl);
//     if ((p?.isNotEmpty ?? false) && File(p!).existsSync()) {
//       AppLogger.d('sendImg filePath=$p');
//       var messageEvent = MessageEvent(uuid(),
//           owner: '',
//           type: MessageType.image,

//           /// 转发时会根据选择对象修改，daen
//           chatType: ChatType.singleChat,
//           direction: Direction.outGoing,
//           filePath: p,
//           dateTime: TimeTask.instance.getNowDateTime());
//       shareForwardMessage(Get.context!, [messageEvent]);
//     }
//   }

//   sendWalletAddress(WalletModel walletModel) {
//     _shareWalletAddress(walletModel);
//   }

//   _shareWalletAddress(WalletModel walletModel) {
//     var mapJson = walletModel.toJson();
//     var jsonStr = json.encode(mapJson);
//     var messageEvent = MessageEvent(uuid(),
//         owner: '',
//         body: jsonStr,
//         type: MessageType.walletAddress,

//         /// 转发时会根据选择对象修改，daen
//         chatType: ChatType.singleChat,
//         direction: Direction.outGoing,
//         filePath: walletModel.picture,
//         dateTime: TimeTask.instance.getNowDateTime());
//     shareForwardMessage(Get.context!, [messageEvent]);
//   }

//   sendMoneyExchangeInfoStr(List jsonList) {
//     if (jsonList.isNotEmpty) {
//       var ad = jsonList[0];
//       var messageEvent = MessageEvent(uuid(),
//           owner: '',
//           body: ad,
//           type: MessageType.moneyExchange,

//           /// 转发时会根据选择对象修改，daen
//           chatType: ChatType.singleChat,
//           direction: Direction.outGoing,
//           dateTime: TimeTask.instance.getNowDateTime());
//       shareForwardMessage(Get.context!, [messageEvent]);
//     }
//   }

//   int _clickCurrentTime = 0;

//   toTransfer(int chainType, String toAddress,
//       ToWalletTransactionModel toWalletTransactionModel,
//       {TransactionType? transactionType, BuildContext? context}) async {
//     int t = DateTime.now().millisecondsSinceEpoch;
//     if (t - _clickCurrentTime < 2000) {
//       return;
//     }
//     _clickCurrentTime = t;
//     var result = await checkAllConditionIsSetting();
//     if (result == 'ok') {
//       rxWalletTimerUpdate.value = true;
//       await jumToHongbao(chainType,
//           toAddress: toAddress,
//           toWalletTransactionModel: toWalletTransactionModel,
//           transactionType: transactionType,
//           context: context);
//       rxWalletTimerUpdate.value = false;
//     }
//   }

//   sendToken(BuildContext context, String userName, String nickName) async {
//     var result = await checkAllConditionIsSetting();
//     if (result == 'ok') {
//       AppLogger.d('sendToken start');
//       rxWalletTimerUpdate.value = true;
//       await sendTransferHongBao(context,
//           receiveUserName: userName,
//           receiveNickName: nickName, reqCallBack: (chainType) async {
//             List<WalletModel> m = [];
//             var list = await Get.find<AppDatabase>()
//                 .contactWalletByChainType(userName, chainType)
//                 .get();
//             list.forEach((element) {
//               m.add(WalletModel(
//                 address: element.address,
//                 chainType: element.chainType,
//                 chainId: element.chainId,
//               ));
//             });
//             return m;
//           });
//       rxWalletTimerUpdate.value = false;
//       AppLogger.d('sendToken end');
//     }
//   }
//   sendTokenMoneyPacket(BuildContext context, String userName, String nickName) async {
//     var result = await checkAllConditionIsSetting();
//     if (result == 'ok') {
//       AppLogger.d('sendTokenMoneyPacket start');
//       rxWalletTimerUpdate.value = true;
//       await sendTransferHongBao(context,
//           receiveUserName: userName,
//           receiveNickName: nickName, reqCallBack: (chainType) async {
//             List<WalletModel> m = [];
//             var list = await Get.find<AppDatabase>()
//                 .contactWalletByChainType(userName, chainType)
//                 .get();
//             list.forEach((element) {
//               m.add(WalletModel(
//                 address: element.address,
//                 chainType: element.chainType,
//                 chainId: element.chainId,
//               ));
//             });
//             return m;
//           });
//       rxWalletTimerUpdate.value = false;
//       AppLogger.d('sendToken end');
//     }
//   }
  
//   changeConnectMoney(
//       BuildContext context,
//       String userName,
//       ) async {
//     var result = await checkAllConditionIsSetting();
//     if (result == 'ok') {
//       connectMoney(context, reqCallBack: (wallet) {
//         if (wallet is WalletModel) {
//           var mapJson = wallet.toJson();
//           var jsonStr = json.encode(mapJson);
//           var messageEvent = MessageEvent(uuid(),
//               owner: userName,
//               body: jsonStr,
//               type: MessageType.walletAddress,

//               /// 转发时会根据选择对象修改，daen
//               chatType: ChatType.singleChat,
//               direction: Direction.outGoing,
//               filePath: wallet.picture,
//               dateTime: TimeTask.instance.getNowDateTime());
//           Get.find<EventBus>().fire(MessageForwardEvent(messageEvent));
//           sendMessage(null, messageEvent);
//         }
//         return null;
//       });
//     }
//   }

//   changeWallet(BuildContext context,
//       {MainRequestCallback? reqCallBack, bool? isEth}) async {
//     connectMoney(context, reqCallBack: (wallet) {
//       reqCallBack?.call(wallet);
//     }, isEth: isEth);
//   }

//   getAllWallet(MainRequestCallback? reqCallBack, {bool? isEth}) async {
//     getAllWallets(reqCallBack,isEth: isEth);
//   }

//   String? getWalletKeyByAddress(String address) {
//     return getKeyByAddress(address);
//   }

//   getDisposit() {
//     Get.find<InviteApi>().getMiningApiDisposit().then((value) => null);
//   }

//   void setAddBrowserClick() {
//     BrowserTask.instance.addCount();
//   }

//   dynamic setBrowserBack(index, {dynamic m}) {
//     AppLogger.d('setBrowserBack m=$m');
//     var value = WalletOrBrowserBackType.values[index];
//     switch (value) {
//       case WalletOrBrowserBackType.ad:
//         Get.find<ProbeService>().submitAd(index, m: m);
//         break;
//       case WalletOrBrowserBackType.money_exchange:
//         sendMoneyExchangeInfoStr(m);
//         break;
//       case WalletOrBrowserBackType.wallet_transaction:
//         _sendWalletTransMessage(m);
//         break;
//       case WalletOrBrowserBackType.message:
//         submitMessage(m);
//         break;
//       case WalletOrBrowserBackType.saveFile:
//         _saveFile(m);
//         break;

//       case WalletOrBrowserBackType.message02:
//         submitMessage02(m);
//         break;
//       case WalletOrBrowserBackType.getUserInfo:
//       case WalletOrBrowserBackType.getUserInfoCer:
//       case WalletOrBrowserBackType.signData:
//       case WalletOrBrowserBackType.imgByPath:
//         break;
//       case WalletOrBrowserBackType.getHongBaoState:
//         _getHongBaoState(m);
//         break;
//       case WalletOrBrowserBackType.pageLoadSuccess:
//         _pageLoadSuccess();
//         break;
//       case WalletOrBrowserBackType.playVoice:
//         playVoice();
//         break;
//       case WalletOrBrowserBackType.verifyHuman:
//       // TODO: Handle this case.
//     }
//   }

//   String? getUserInfo() {
//     Map map = {};
//     AvatarModel avatar = AvatarModel.fromJson(
//         Get.find<AppConfigService>().getMySelfAvatarInfo() ?? {});
//     map['user_name'] = Get.find<AppConfigService>().getUserName();
//     map['nick_name'] = Get.find<AppConfigService>().getMySelfDisplayName();
//     map['icon'] = avatar.avatarUrl;
//     map['language'] = currentLanguageIsSimpleChinese() ? 'zh' : 'en';
//     var data = json.encode(map);
//     AppLogger.d('getUserInfo info=$data');
//     return data;
//   }

//   Future<String?> getUserInfoCer() async {
//     AppLogger.d('getUserInfoCer state=');

//     Map map = {};
//     AvatarModel avatar = AvatarModel.fromJson(
//         Get.find<AppConfigService>().getMySelfAvatarInfo() ?? {});
//     map['user_name'] = Get.find<AppConfigService>().getUserName();
//     map['nick_name'] = Get.find<AppConfigService>().getMySelfDisplayName();
//     map['icon'] = avatar.avatarUrl;
//     map['device_id'] = await getDeviceCode();
//     map['token'] = Get.find<AppConfigService>().getToken();
//     map['is_open_more'] = await getIsOpenMore();
//     var data = json.encode(map);
//     AppLogger.d('getUserInfoCer info=$data');
//     return data;
//   }

//   Future<String?> signData(List data) async {
//     if (data.isNotEmpty) {
//       var info = data.first;
//       AppLogger.d('signData info=$info');
//       if (info is String) {
//         var map = json.decode(info);
//         var data = map['data'];
//         String? radom = map['key'];
//         AppLogger.d('signData data=$data');
//         AppLogger.d('signData radom=$radom');
//         if ((data != null) && (radom?.isNotEmpty ?? false)) {
//           // var checkSum = sha256.convert(json.encode(data!).codeUnits).toString();
//           String? body = await MiningPwdUtil.encryptForData(data!, rand: radom);
//           AppLogger.d('signData body=$body');
//           return body;
//         }
//       }
//     }
//     return null;
//   }

//   _saveFile(List data) async {
//     if (data.isNotEmpty) {
//       String info = data.first;
//       info = info.replaceFirst('data:image/png;base64,', '');
//       AppLogger.d('_saveFile info=$info');
//       RepaintBoundaryUtils.savePhoto02(base64Decode(info),
//           toastSuccess: L.image_saved_successfully.tr,
//           toastError: L.image_saved_failed.tr);
//     }
//   }

//   _sendWalletTransMessage(Map<String, dynamic> jsonMap) async {
//     String? userName;
//     var walletTransactionModel = WalletTransactionModel.fromJson(jsonMap);
//     var robotUserName = walletTransactionModel.robotUsername ?? "";
//     var address = walletTransactionModel.address;
//     userName = await Get.find<AppDatabase>()
//         .userNameByWalletAddress(address)
//         .getSingleOrNull();
//     if (robotUserName.isNotEmpty) {
//       userName = robotUserName;
//     }
//     // userName = '<EMAIL>';
//     if ((userName?.isNotEmpty ?? false) && (userName?.length ?? 0) >= 44) {
//       var json2 = walletTransactionModel.toJson();
//       var message = json.encode(json2);
//       MessageEvent event = MessageEvent(
//         uuid(),
//         owner: userName ?? '',
//         chatType: ChatType.singleChat,
//         type: MessageType.walletTransaction,
//         body: message,
//         direction: Direction.outGoing,
//         dateTime: TimeTask.instance.getNowDateTime(),
//       );
//       Get.find<EventBus>().fire(MessageForwardEvent(event));
//       sendMessage(
//         null,
//         event,
//         pushType: PushType.message,
//       );
//     }
//   }

//   submitMessage(List message) async {
//     if (message.isNotEmpty) {
//       var info = json.decode(message.first);
//       String user = info['user_name'] ?? '';
//       int chatType = info['chat_type'] ?? 0;
//       String displayName = '';
//       if (user.isNotEmpty) {
//         if (user.length > 5) {
//           displayName = user.substring(0, 5);
//         }
//         AppLogger.d('submitMessage displayName=$displayName');
//         var userMessage = UserMessage(
//             chatType: chatType,
//             displayName: displayName,
//             userName: user,
//             avatarPath: '');
//         // Get.toNamed(Routes.MESSAGE, arguments: userMessage);
//         JumpPage.messgae(userMessage);
//       }
//     }
//   }

//   submitMessage02(List message) async {
//     if (message.isNotEmpty) {
//       var info = json.decode(message.first);
//       String user = info['user_name'] ?? '';
//       int chatType = info['chat_type'] ?? 0;
//       String displayName = '';

//       if (user.isNotEmpty) {
//         var contact = await Get.find<AppDatabase>()
//             .searchContactByUserName(user ?? '')
//             .getSingleOrNull();
//         if (contact != null) {
//           displayName = contact.localname ?? contact.displayname ?? '';
//           user = contact.username;
//         }
//         if (!user.contains('@')) {
//           var response = await Get.find<OperationCenterApiProvider>()
//               .getComNode(account: user);
//           String node = response.data?.nodeModelData?.node?.node ?? "";
//           if (node.isNotEmpty) {
//             user = "$user@$node";
//           } else {
//             //没有节点就不往下走
//             return;
//           }
//         }
//         if (displayName.isEmpty && user.length > 5) {
//           displayName = user.substring(0, 5);
//         }
//         AppLogger.d('submitMessage displayName=$displayName');
//         var userMessage = UserMessage(
//             chatType: chatType,
//             displayName: displayName,
//             userName: user,
//             avatarPath: '');
//         // Get.toNamed(Routes.MESSAGE, arguments: userMessage);
//         JumpPage.messgae(userMessage);
//       }
//     }
//   }

//   _getHongBaoState(List data) async {
//     if (data.isNotEmpty) {
//       var info = json.decode(data.first);
//       AppLogger.d('_getHongBaoState info=$info');
//       HongBaoTask().jumpHongBaoDetailHtml(info);
//     }
//   }

//   _pageLoadSuccess() async {
//     Get.find<EventBus>().fire(PageLoadSuccessEvent());
//   }

//   final Map<String, String> _currentImg = {};

//   Future<String?> getImgByPath(List data) async {
//     AppLogger.d('getImgByPath info=$data');
//     var info = data.first;
//     String? base64;
//     if (info is String) {
//       var key = getPathByName(info);
//       var img = _currentImg[key];
//       if (img == null) {
//         base64 = await loadAssetByPath(key);
//         if (base64 != null) {
//           _currentImg.addEntries({key: base64}.entries);
//         }
//       } else {
//         base64 = img;
//       }
//     }
//     return base64;
//   }

//   String getPathByName(String name) {
//     String path = '';
//     switch (name) {
//       case 'hb_bg':
//         path = 'assets/images/hb_bg.png';
//         break;
//       case 'hb_banner':
//         path = 'assets/images/hb_info_bg.png';
//         break;
//     }
//     return path;
//   }

//   playVoice() {
//     AudioPlayHelper.instance.playHongBao1();
//   }

//   Future<bool> verifyHuman() async {
//     var ok = await Get.to(const CodeView(
//       1,
//       showOpen: false,
//     ));
//     return ok ?? false;
//   }

//   updateWalletNodeName(List<WalletNode> list) {
//     updateWalletNods(list);
//   }

//   getBalance(String rpc,String address) {
//     return getBalanceByRpc(rpc,address);
//   }
// }
