import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_metatel/app/data/enums/enum.dart';
import 'package:flutter_metatel/app/data/models/hongbao_tokens_model.dart';
import 'package:flutter_metatel/core/languages/l.dart';
import 'package:flutter_metatel/core/task/hongbao_task.dart';
import 'package:flutter_metatel/core/values/colors.dart';
import 'package:flutter_metatel/r.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_web3/app/core/hw_wallet_manager.dart';
import 'package:flutter_web3/app/core/language/s.dart';
import 'package:flutter_web3/app/core/utils/util.dart';
import 'package:flutter_web3/app/core/values/config.dart';
import 'package:flutter_web3/app/widgets/ic_widget.dart';
import 'package:flutter_web3/app/widgets/text_number_formatter.dart';
import 'package:flutter_web3/web3dart/src/utils/typed_data.dart';
import 'package:get/get.dart';

import '../../../core/task/token_task.dart';
import 'hongbao_transfer_controller.dart';

class HongbaoTransferPage extends StatefulWidget {
  const HongbaoTransferPage({
    Key? key,
    required this.msgId,
    required this.to,
    required this.chatType,
    this.ext1,
  }) : super(key: key);
  final String msgId;
  final String to;
  final ChatType chatType;
  final String? ext1;

  @override
  State<HongbaoTransferPage> createState() => _HongbaoTransferPageState();
}

class _HongbaoTransferPageState extends State<HongbaoTransferPage> {
  final HongBaoTransferController _controller =
      Get.put(HongBaoTransferController());

  @override
  void initState() {
    super.initState();
    _controller.totalAmountController.addListener(_controller.onTextChanged);
    _controller.amountpacketController.addListener(_controller.onTextChanged);

    _controller.packetType.value = "random";
  }

  @override
  void dispose() {
    Get.delete<HongBaoTransferController>();
    _controller.totalAmountController.dispose();
    _controller.balanceTipsNotifier.dispose();
    _controller.buttonNotifier.dispose();
    _controller.remarkController.dispose();
    _controller.amountpacketController.dispose();
    _controller.focusNode.dispose();
    _controller.focusNode2.dispose();
    _controller.focusNode3.dispose();
    _controller.focusNode4.dispose();
    _controller.focusNode5.dispose();

    super.dispose();
  }

  Widget _buildTokenList() {
    return Container(
      padding: const EdgeInsets.only(bottom: 16).r,
      width: double.infinity,
      constraints: BoxConstraints(maxHeight: 463.h),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.only(top: 21, bottom: 21).r,
            child: Text(
              S.current.tokens,
              style: TextStyle(
                fontSize: 18.sp,
              ),
            ),
          ),
          Expanded(
            child: Obx(
              () {
                var tokenDatas = _controller.hongbaoModelList;
                return ListView.separated(
                  itemBuilder: (context, index) {
                    return _buildTokenItem(tokenDatas[index]);
                  },
                  itemCount: tokenDatas.length,
                  separatorBuilder: (BuildContext context, int index) =>
                      Divider(height: 0.5.h, color: Color(0xFFE6E6E6)),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTokenItem(HongbaoTokensModelData data) {

    Widget walletItem = Column(
      children: [
        Material(
          color: Colors.transparent,
          child: InkWell(
            borderRadius: null,
            onTap: () {
              _controller.changeTokenSelected(data);
              Get.back();
            },
            child: Container(
              height: 60.r,
              padding: const EdgeInsets.only(
                left: 16,
                right: 16,
              ).r,
              child: Row(
                children: [
                  FutureBuilder(
                      future:
                          TokenTask().getIcByToken(data.tokenAddress ?? ''),
                      builder: (_, snapshot) {
                        String? iconPath;
                        if (snapshot.connectionState == ConnectionState.done) {
                          if (snapshot.data?.isNotEmpty ?? false) {
                            iconPath = snapshot.data as String;
                          }
                        }
                        return IcWidget(
                          diameter: 42,
                          isNet: false,
                          isMain: false,
                          filePath: iconPath,
                        );
                      }),
                  SizedBox(
                    width: 10.r,
                  ),
                  Text(
                    data.symbol ?? '',
                    style: TextStyle(
                      fontSize: 13.sp,
                    ),
                  ),
                  Spacer(),
                  Padding(
                    padding:
                        EdgeInsets.symmetric(horizontal: 8.h, vertical: 8.h),
                    child: Image.asset(
                      data.isSelected == true ? R.icoSelected : R.icoUnselected,
                      width: 16.r,
                      height: 16.r,
                      package: WalletConfig.packageName,
                    ),
                  ),
                  SizedBox(
                    width: 16.r,
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );

    return walletItem;
  }

  Widget _buildTokens() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      mainAxisSize: MainAxisSize.min,
      children: [
        Row(
          children: [
            Text(
              L.token.tr,
              style: TextStyle(
                fontSize: 14.sp,
              ),
            ),
            const Spacer(),
            _buildAddress(),
            SizedBox(height: 30.r),
          ],
        ),
        InkWell(
          onTap: () {
            showModalBottomSheetUtil(
              widget: _buildTokenList(),
              context: context,
            );
          },
          child: Container(
            height: 47.r,
            padding: EdgeInsets.only(left: 20.r, right: 20.r),
            decoration: BoxDecoration(
              color: AppColors.colorFFF1F1F1,
              borderRadius: BorderRadius.all(Radius.circular(6.r)),
            ),
            child: Obx(() {
              var token = _controller.currentToken.value;

              return Row(
                children: [
                  FutureBuilder(
                      future:
                      TokenTask().getIcByToken(token.tokenAddress ?? ''),
                      builder: (_, snapshot) {
                        String? iconPath;
                        if (snapshot.connectionState == ConnectionState.done) {
                          if (snapshot.data?.isNotEmpty ?? false) {
                            iconPath = snapshot.data as String;
                          }
                        }
                        return IcWidget(
                          diameter: 30,
                          isNet: false,
                          isMain: false,
                          filePath: iconPath,
                        );
                      }),
                  SizedBox(width: 12.r),
                  Text(
                    token.symbol ?? '',
                    style: TextStyle(
                      fontSize: 14.sp,
                    ),
                  ),
                  const Spacer(),
                  Icon(
                    Icons.keyboard_arrow_down,
                    color: AppColors.colorFF656565,
                    size: 14.r,
                  )
                ],
              );
            }),
          ),
        ),
      ],
    );
  }

  Widget _buildAddress() {
    // 创建 HwWalletManager 的实例
    final hwWalletManager = HwWalletManager();

    // 使用 FutureBuilder 来异步获取钱包地址
    return FutureBuilder<String>(
      future: hwWalletManager.getWalletAddress(), // 通过实例调用 getWalletAddress
      builder: (context, snapshot) {
        // 如果获取地址还在加载中，显示加载状态
        if (snapshot.connectionState == ConnectionState.waiting) {
          return CircularProgressIndicator(); // 或者可以显示其它加载动画
        }

        // 如果请求失败，显示一个提示
        if (snapshot.hasError) {
          return Text('Error: ${snapshot.error}');
        }

        // 获取钱包地址
        String address = snapshot.data ?? '';

        // 格式化地址，确保不会显示过长的地址
        String displayAddress = address;
        if (address.length > 30) {
          displayAddress =
              '${address.substring(0, 26)}...${address.substring(address.length - 4)}'; // 显示前10个字符和后4个字符
        }

        return GestureDetector(
          child: Container(
            padding: EdgeInsets.symmetric(vertical: 5.h),
            child: Text(
              displayAddress, // 在显示的地址后添加一些自定义文本
              style: TextStyle(
                fontSize: 11.sp,
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildAmountPackets() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          L.amount_of_packet.tr,
          style: TextStyle(
            fontSize: 14.sp,
            color: AppColors.colorFF000000,
          ),
        ),
        Obx(() {
          // 检查 balanceText 是否为空，决定输入框是否可用
          bool isEnabled = _controller.currentTotalAmount.value > 0;
          return TextField(
            focusNode: _controller.focusNode4,
            controller: _controller.amountpacketController,
            enabled: isEnabled,
            keyboardType: TextInputType.number,
            inputFormatters: [
              FilteringTextInputFormatter.digitsOnly,
              LengthLimitingTextInputFormatter(3),
            ],
            onChanged: _controller.amountPacketChange,
            decoration: InputDecoration(
                contentPadding: EdgeInsets.only(left: 5.w),
                hintText: '0',
                hintStyle: TextStyle(
                  fontSize: 14.sp,
                ),
                enabledBorder: const UnderlineInputBorder(
                    borderSide: BorderSide(color: AppColors.colorFFCBCBCB)),
                focusedBorder: const UnderlineInputBorder(
                    borderSide: BorderSide(color: AppColors.primaryBgColor1))),
          );
        }),
        SizedBox(height: 6.r),
        Obx(() {
          if (_controller.maxNumber.value.isEmpty) {
            return SizedBox.shrink();
          }
          return Text(
            _controller.maxNumber.value.isNotEmpty
                ? '${L.max_number_sent.tr} ${_controller.maxNumber.value}'
                : '',
            style: TextStyle(
              fontSize: 12.sp,
              color: Colors.red,
            ),
          );
        }),
      ],
    );
  }

  //红包金额
  Widget _buildAmount() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              L.amount_per_packet.tr,
              style: TextStyle(
                fontSize: 14.sp,
              ),
            ),
            const Spacer(),
            GestureDetector(
              onTap: () {
                _controller.changeBalance();
              },
              child: Obx(() {
              var balance = _controller.currentTotalAmount.value;
              return Text(
                '${L.balance.tr}: ${decimalData(balance, 8)}',
                style: TextStyle(
                  fontSize: 12.sp,
                ),
              );
            }),),
          ],
        ),
        Obx(() {
          var token = _controller.currentToken.value;
          var decimals = token.decimals;
          var dec = 0;
          if (decimals?.isNotEmpty ?? false) {
            dec = int.parse(decimals!);
          }
          return TextField(
            focusNode: _controller.focusNode3,
            controller: _controller.totalAmountController,
            keyboardType: TextInputType.numberWithOptions(decimal: true),
            inputFormatters: [
              TextNumberLimitFormatter(10, dec),
            ],
            readOnly: dec <= 0,
            onChanged: _controller.onAmountChanged,
            decoration: InputDecoration(
              // isDense: true,
              contentPadding: EdgeInsets.only(left: 5.w),
              hintText: '0.00',
              hintStyle: TextStyle(
                fontSize: 14.sp,
              ),
              enabledBorder: const UnderlineInputBorder(
                  borderSide: BorderSide(color: AppColors.colorFFCBCBCB)),
              focusedBorder: const UnderlineInputBorder(
                  borderSide: BorderSide(color: AppColors.primaryBgColor1)),
              suffix: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    token.symbol ?? '',
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: Color(0xFF333333),
                    ),
                  ),
                  SizedBox(width: 10.w),
                ],
              ),
            ),
          );
        }),
        SizedBox(height: 6.r),
        ValueListenableBuilder<String>(
          valueListenable: _controller.balanceTipsNotifier,
          builder: (_, value, __) {
            return Visibility(
              visible: value.isNotEmpty,
              child: Text(
                value,
                style: const TextStyle(color: Colors.red),
              ),
            );
          },
        ),
      ],
    );
  }

  Widget _buildServiceFee() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        Text(
          '${L.service_fee.tr}(1%): ',
          style: TextStyle(
            fontSize: 12.sp,
            color: Color(0xFF249ED9),
          ),
        ),
        Obx(() {
          var fee = _controller.feeRx.value;
          var token = _controller.currentToken.value;
          return Text(
            '$fee ${token.symbol}',
            style: TextStyle(
              fontSize: 12.sp,
              color: Color(0xFF249ED9),
            ),
          );
        }),
      ],
    );
  }

  Widget _buildTotal() {
    return Obx(() {
      _controller.totalAmountRx.value;
      _controller.packetType;
      var serviceFee = _controller.feeRx.value;
      var totalAmountWithFee = _controller.totalAmount() + serviceFee;
      return Padding(
        padding: EdgeInsets.symmetric(vertical: 16.h),
        child: Container(
          height: 47.r,
          padding: EdgeInsets.symmetric(horizontal: 10.w),
          decoration: BoxDecoration(
            color: Color(0xFFF7F7F7),
            borderRadius: BorderRadius.circular(6.r),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                L.total.tr,
                style: TextStyle(
                  fontSize: 16.sp,
                ),
              ),
              Text(
                '${totalAmountWithFee.toStringAsFixed(6)} ${_controller.currentToken.value.symbol}',
                style: TextStyle(
                  fontSize: 19.sp,
                  color: AppColors.colorFF249ED9,
                ),
              ),
            ],
          ),
        ),
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    var defaultHeight = 24.r;
    return Scaffold(
      backgroundColor: AppColors.white,
      appBar: AppBar(
        title: Text(
          L.money_packet.tr,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        centerTitle: true,
      ),
      body: ListView(
        padding: EdgeInsets.symmetric(horizontal: 16.r),
        children: [
          SizedBox(height: 10.r),
          _buildTokens(),
          SizedBox(height: defaultHeight),
          _buildMoneyPacketType(),
          SizedBox(height: defaultHeight),
          _buildAmount(), //红包金额
          SizedBox(height: defaultHeight),
          _buildAmountPackets(),
          SizedBox(height: 10.r),
          _buildServiceFee(),
          SizedBox(height: defaultHeight),
          _buildRemark(),
          SizedBox(height: defaultHeight),
          _buildTotal(),
          const SizedBox(
            height: 10,
          ),
          ValueListenableBuilder<bool>(
            valueListenable: _controller.buttonNotifier,
            builder: (_, value, old) {
              return ElevatedButton(
                onPressed: value
                    ? () {
                        if (_controller.buttonNotifier.value) {
                          _controller.send(context,widget.chatType, widget.to);
                        }
                      }
                    : null,
                child: Text(
                  L.confirm.tr,
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: Color(0xFFF7F7F7),
                  ),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildMoneyPacketType() {
    return InkWell(
      onTap: () {
        showModalBottomSheetUtil(
          widget: _buildPacketTypeList(),
          context: context,
        );
      },
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            L.money_packet_type.tr,
            style: TextStyle(
              fontSize: 14.sp,
            ),
          ),
          SizedBox(height: 6.r),
          Container(
            height: 47.r,
            padding: EdgeInsets.only(left: 20.r, right: 20.r),
            decoration: BoxDecoration(
              color: AppColors.colorFFF1F1F1,
              borderRadius: BorderRadius.all(Radius.circular(6.r)),
            ),
            child: Obx(() {
              var map = {
                'equal': L.equal.tr,
                'random': L.random.tr,
              };
              return Row(
                children: [
                  Text(
                    map[_controller.packetType.value] ?? '',
                    style: TextStyle(
                      fontSize: 13.sp,
                    ),
                  ),
                  const Spacer(),
                  Icon(
                    Icons.keyboard_arrow_down,
                    color: AppColors.colorFF656565,
                    size: 14.r,
                  ),
                ],
              );
            }),
          ),
        ],
      ),
    );
  }

  Widget _buildPacketTypeList() {
    return Container(
      padding: EdgeInsets.all(16.w),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildPacketTypeOption(L.equal.tr, 'equal'),
          _buildPacketTypeOption(L.random.tr, 'random'),
        ],
      ),
    );
  }

  Widget _buildRemark() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          L.what_is_the_red_packet_for.tr,
          style: TextStyle(
            fontSize: 14.sp,
          ),
        ),
        SizedBox(height: 6.r),
        TextField(
          focusNode: _controller.focusNode5,
          controller: _controller.remarkController,
          decoration: InputDecoration(
              contentPadding: EdgeInsets.only(left: 5.w),
              hintText: L.the_hongbao_title.tr,
              hintStyle: TextStyle(
                fontSize: 14.sp,
              ),
              enabledBorder: const UnderlineInputBorder(
                  borderSide: BorderSide(color: AppColors.colorFFCBCBCB)),
              focusedBorder: const UnderlineInputBorder(
                  borderSide: BorderSide(color: AppColors.primaryBgColor1))),
        ),
      ],
    );
  }

  Widget _buildPacketTypeOption(String title, String type) {
    return ListTile(
      title: Text(
        title,
        style: TextStyle(
          fontSize: 16.sp,
          fontWeight: type == _controller.packetType.value
              ? FontWeight.bold
              : FontWeight.normal,
        ),
      ),
      onTap: () async {
        _controller.changePacketTypeOption(type);
        Navigator.pop(context);
      },
    );
  }
}
