import 'dart:convert';

import 'package:auto_size_text/auto_size_text.dart';
// import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/data/enums/enum.dart';
import 'package:flutter_metatel/app/data/models/hongbao_check_status_model.dart';
import 'package:flutter_metatel/app/data/providers/api/hongbao_api.dart';
import 'package:flutter_metatel/app/data/services/config_service.dart';
import 'package:flutter_metatel/app/modules/hongbao/hongbao_details_page.dart';
import 'package:flutter_metatel/app/modules/hongbao/hongbao_open_controller.dart';
import 'package:flutter_metatel/app/widgets/mavatar_circle_avatar.dart';
import 'package:flutter_metatel/app/widgets/number_flipper.dart';
import 'package:flutter_metatel/core/languages/l.dart';
import 'package:flutter_metatel/core/task/hongbao_task.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:flutter_metatel/r.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class HongbaoOpenPage extends StatefulWidget {
  const HongbaoOpenPage({
    Key? key,
    required this.msgId,
    this.extension,
    required this.data,
    this.groupId,
    this.chatType,
    this.signalData,
    this.hongBaoOperateState,
  }) : super(key: key);

  final HongbaoCheckStatusModelData data;
  final String msgId;
  final String? extension;
  final String? groupId;
  final ChatType? chatType;
  final String? signalData;
  final HongBaoOperateState? hongBaoOperateState;

  @override
  State<HongbaoOpenPage> createState() => _HongbaoOpenPageState();
}

class _HongbaoOpenPageState extends State<HongbaoOpenPage>
    with TickerProviderStateMixin {
  final HongbaoOpenController _controller = Get.put(HongbaoOpenController());
  late AnimationController _bgController;

  // 两个独立的 AnimationController
  late AnimationController _page2ScaleController;
  late Animation<double> _page2ScaleAnimation;

  @override
  void initState() {
    super.initState();
    if (widget.data.isOk == true) {
      var username = Get.find<AppConfigService>().getUserName();
      var log = widget.data.log?.firstWhere((e) => e?.account == username);
      if (log != null) {
        _controller.amount.value = log.amount?.toDouble() ?? 0.0;
      }
    }
    AppLogger.d('hongBaoOperateState =${_controller.amount.value}');

    _controller.setCurrentData(
        widget.msgId,
        widget.signalData,
        widget.extension,
        widget.groupId,
        widget.chatType,
        widget.hongBaoOperateState,
        widget.data);
    _bgController = AnimationController(
      duration: const Duration(milliseconds: 100),
      vsync: this,
    )..repeat(reverse: true);

    _bgController.addStatusListener((status) {
      if (status == AnimationStatus.completed ||
          status == AnimationStatus.dismissed) {
        if (_bgController.isAnimating) {
          _bgController.stop();
          AppLogger.d('Background animation stopped.');
        }
      }
    });

    // 初始化 Page2 放大动画控制器
    _page2ScaleController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    // 定义 Page2 放大动画
    _page2ScaleAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _page2ScaleController,
        curve: Curves.easeInOut,
      ),
    );

    // 可选：监听 Page2 动画完成
    _page2ScaleController.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        AppLogger.d('Page2 scaling up completed.');
        // 这��可以添加动画完成后的逻辑
      }
    });

    // 在初始化后5秒停止背景动画
    Future.delayed(const Duration(milliseconds: 100), () {
      if (_bgController.isAnimating) {
        _bgController.stop();
      }
    });
  }

  @override
  void dispose() {
    _bgController.dispose();
    _page2ScaleController.dispose();
    Get.delete<HongbaoOpenController>();
    super.dispose();
  }

  // 触发动画：等待0.1秒后，依次触发 Page1 缩小和 Page2 放大
  Future<void> _triggerQiangHongBao() async {
    _page2ScaleController.forward();
  }

  // 打开红包详情页面
  void openHongbaoDetail() async {
    var account = Get.find<AppConfigService>().getUserName();
    var response = await RedPacketApi()
        .checkRedPacketStatus(account.toString(), widget.msgId);
    if (response != null) {
      Get.to(() => HongbaoDetailsPage(
            data: response.data!,
          ));
    }
  }

  // 构建 Page3（未抢到红包）
  Widget _buildPage3() {
    String title = '';
    if ((widget.extension?.isNotEmpty ?? false)) {
      try {
        var info = json.decode(widget.extension ?? '');
        var desc = info['desc'];
        if (desc != null) {
          title = desc ?? '';
        }
      } catch (e) {}
    }
    if (title.isEmpty) {
      title = L.the_hongbao_title.tr;
    }
    var _redPacketHeight = 0.9.sh;
    var _redPacketWidth = 0.9.sh/2.45;
    return Container(
      // width: double.infinity,
      // height: double.infinity,
      // width: 300.r,
      height: _redPacketHeight,
      // margin: EdgeInsets.only(left: 20.r, right: 20.r, bottom: 50.r),
      // padding:
      //     // EdgeInsets.only(top: 30.r, left: 23.r, right: 20.r, bottom: 60.r), CNY
      //     EdgeInsets.only(top: 187.r, left: 23.r, right: 20.r, bottom: 60.r),
      // decoration: const BoxDecoration(
      //     image: DecorationImage(
      //         image: AssetImage(R.icMoneyPacketOpen), fit: BoxFit.fitHeight)),
      child: Stack(
        children: [
          Center(
            child: Image.asset(
              R.icMoneyPacketOpen,
              fit: BoxFit.fitHeight,
            ),
          ),
          Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              SizedBox(height: _redPacketHeight * 0.15),
              _buildRedPacketAvatar(_redPacketHeight),
              SizedBox(
                // height: 10.r, CNY
                height: _redPacketHeight * 0.05,
              ),
              Container(
                height: _redPacketHeight * 0.05,
                constraints: BoxConstraints(maxWidth: _redPacketWidth * 0.7),
                child: AutoSizeText(
                  L.try_again_later.tr,
                  softWrap: true,
                  maxLines: 1,
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: Colors.red,
                    fontSize: 16.sp,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              // const Spacer(),
              SizedBox(height: _redPacketHeight * 0.26),
              Container(
                height: _redPacketHeight * 0.11,
                constraints: BoxConstraints(maxWidth: _redPacketWidth * 0.8),
                child: AutoSizeText(
                  'packet_from_whom'
                      .trParams({'name': widget.data.redPacket?.name ?? ''}),
                  // (widget.data.redPacket?.name ?? '') + L.of_money_packet.tr,
                  textAlign: TextAlign.center,
                  maxLines: 2,
                  // minFontSize: 14,
                  maxFontSize: 18,
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              SizedBox(
                height: _redPacketHeight * 0.03,
              ),
              Container(
                height: _redPacketHeight * 0.07,
                constraints: BoxConstraints(maxWidth: _redPacketWidth * 0.8),
                child: AutoSizeText(
                  title,
                  softWrap: true,
                  maxLines: 2,
                  // minFontSize: 14,
                  maxFontSize: 22,
                  textAlign: TextAlign.center,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 22,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              SizedBox(
                height: _redPacketHeight * 0.04,
              ),
              Center(
                child: SizedBox(
                  width: _redPacketWidth*0.6,
                  height: _redPacketHeight * 0.07,
                  child: ElevatedButton(
                    onPressed: () {
                      openHongbaoDetail();
                    },
                    style: ElevatedButton.styleFrom(
                      // backgroundColor: Color(0xFFFFC30C), CNY
                      backgroundColor: Colors.red,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(30.0),
                      ),
                    ),
                    child: AutoSizeText(
                      L.view_detail.tr,
                      maxLines: 1,
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              )
            ],
          ),
        ],
      ),
    );
  }

  // 构建红包头像
  Widget _buildRedPacketAvatar(double redPacketHeight) {
    String? head = widget.data.redPacket?.head;
    // String name = widget.data.redPacket?.name ?? '';

    // 检查 head 是否有效
    bool isHeadValid = head != null && head.isNotEmpty && head != "null";
    var avatarSize = (redPacketHeight * 0.1);
    return Container(
      width: avatarSize.r,
      height: avatarSize.r,
      alignment: Alignment.center,
      padding: EdgeInsets.all(5.r),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular((avatarSize/2).r),
      ),
      child: MAvatarCircle(
        diameter: avatarSize - 5.r,
        isNet: isHeadValid,
        imagePath: widget.data.redPacket?.head ?? '',
        text: widget.data.redPacket?.name ?? '',
        textStyle: TextStyle(
          color: Colors.white,
          fontSize: 40.0.sp,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  // 构建 Page1
  Widget _buildPage1() {
    String title = '';
    if ((widget.extension?.isNotEmpty ?? false)) {
      try {
        var info = json.decode(widget.extension ?? '');
        var desc = info['desc'];
        if (desc != null) {
          title = desc ?? '';
        }
      } catch (e) {}
    }
    if (title.isEmpty) {
      title = L.the_hongbao_title.tr;
    }
    var _redPacketHeight = 0.7.sh;
    var _redPacketWidth = _redPacketHeight/1.8;
    return Container(
      color: Colors.transparent,
      width: double.infinity,
      height: double.infinity,
      child: Stack(
        alignment: Alignment.center,
        children: [
          Container(
            height: _redPacketHeight,
            width: _redPacketWidth,
            decoration: BoxDecoration(
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.3),
                  offset: const Offset(5, 5),
                  blurRadius: 25,
                  spreadRadius: 1,
                ),
              ],
              borderRadius: BorderRadius.circular(10),
            ),
            child: Image.asset(
              R.icMoneyPacketUnopen,
              fit: BoxFit.fitHeight,
            ),
          ),
          // Positioned(
          //   top: MediaQuery.of(context).size.height * 0.1,
          //   left: 0.0,
          //   right: 0.0,
          //   child: Transform.scale(
          //     scale: 0.85,
          //     child: Container(
          //       decoration: BoxDecoration(
          //         boxShadow: [
          //           BoxShadow(
          //             color: Colors.black.withOpacity(0.3),
          //             offset: const Offset(5, 5),
          //             blurRadius: 25,
          //             spreadRadius: 1,
          //           ),
          //         ],
          //         borderRadius: BorderRadius.circular(10),
          //       ),
          //       child: Image.asset(
          //         R.icMoneyPacketUnopen,
          //         fit: BoxFit.cover,
          //       ),
          //     ),
          //   ),
          // ),
          // Positioned(
          //     top: MediaQuery.of(context).size.height * 0.5,
          //     left: 0.0,
          //     right: 0.0,
          //     child:
          //         Row(mainAxisAlignment: MainAxisAlignment.center, children: [
          //       Container(
          //         constraints: BoxConstraints(maxWidth: 220.r),
          //         child: AutoSizeText(
          //           'packet_from_whom'
          //               .trParams({'name': widget.data.redPacket?.name ?? ''}),
          //           // (widget.data.redPacket?.name ?? '') + L.of_money_packet.tr,
          //           textAlign: TextAlign.center,
          //           maxLines: 2,
          //           minFontSize: 14,
          //           style: const TextStyle(
          //             color: Colors.white,
          //             fontSize: 20.0,
          //             fontWeight: FontWeight.bold,
          //           ),
          //         ),
          //       ),
          //     ])),
          SizedBox(
            height: _redPacketHeight,
            width: _redPacketWidth,
            child: Column(
              // mainAxisSize: MainAxisSize.min,
              children: [
                SizedBox(height: _redPacketHeight * 0.5),
                Container(
                  height: _redPacketHeight * 0.1,
                  constraints: BoxConstraints(maxWidth: _redPacketWidth * 0.75),
                  child: AutoSizeText(
                    'packet_from_whom'
                        .trParams({'name': widget.data.redPacket?.name ?? ''}),
                    // (widget.data.redPacket?.name ?? '') + L.of_money_packet.tr,
                    textAlign: TextAlign.center,
                    maxLines: 2,
                    // minFontSize: 14,
                    maxFontSize: 18,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 18.0,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                SizedBox(height: _redPacketHeight * 0.05),
                Container(
                  // padding: const EdgeInsets.symmetric(horizontal: 10),
                  // height: _redPacketHeight * 0.07,
                  child: Container(
                    constraints: BoxConstraints(maxWidth: _redPacketWidth * 0.85, maxHeight: _redPacketHeight * 0.11),
                    child: AutoSizeText(
                      title,
                      textAlign: TextAlign.center,
                      maxLines: 2,
                      minFontSize: 14,
                      maxFontSize: 20,
                      overflow: TextOverflow.ellipsis,
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
                SizedBox(height: _redPacketHeight * 0.07),
                SizedBox(
                  width: _redPacketWidth * 0.6,
                  height: _redPacketHeight * 0.08,
                  child: Container(
                    decoration: BoxDecoration(
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.3),
                          offset: const Offset(5, 5),
                          blurRadius: 25,
                          spreadRadius: 1,
                        ),
                      ],
                      borderRadius: BorderRadius.circular(30.0),
                    ),
                    child: ElevatedButton(
                      onPressed: () async {
                        _controller.qiangHongBao();
                        _triggerQiangHongBao();
                      },
                      style: ElevatedButton.styleFrom(
                        // backgroundColor: Color(0xFFFFC30C),
                        backgroundColor: Colors.red,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(30.0),
                        ),
                      ),
                      child: Text(
                        L.snatch_red_envelopes.tr,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 20.0,
                          fontWeight: FontWeight.bold,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          // Positioned(
          //   top: MediaQuery.of(context).size.height * 0.6,
          //   left: 0.0,
          //   right: 0.0,
          //   child: Row(
          //     mainAxisAlignment: MainAxisAlignment.center,
          //     children: [
          //       Container(
          //         padding: const EdgeInsets.symmetric(horizontal: 10),
          //         child: Container(
          //           constraints: BoxConstraints(maxWidth: 220.r),
          //           child: AutoSizeText(
          //             title,
          //             textAlign: TextAlign.center,
          //             maxLines: 2,
          //             minFontSize: 14,
          //             overflow: TextOverflow.ellipsis,
          //             style: TextStyle(
          //               color: Colors.white,
          //               fontSize: 20.sp,
          //               fontWeight: FontWeight.bold,
          //             ),
          //           ),
          //         ),
          //       ),
          //     ],
          //   ),
          // ),
          // Positioned(
          //   top: MediaQuery.of(context).size.height * 0.7,
          //   left: 0.0,
          //   right: 0.0,
          //   child: Center(
          //     child: SizedBox(
          //       width: 200.w,
          //       child: Container(
          //         decoration: BoxDecoration(
          //           boxShadow: [
          //             BoxShadow(
          //               color: Colors.black.withOpacity(0.3),
          //               offset: const Offset(5, 5),
          //               blurRadius: 25,
          //               spreadRadius: 1,
          //             ),
          //           ],
          //           borderRadius: BorderRadius.circular(30.0),
          //         ),
          //         child: ElevatedButton(
          //           onPressed: () async {
          //             _controller.qiangHongBao();
          //             _triggerQiangHongBao();
          //           },
          //           style: ElevatedButton.styleFrom(
          //             // backgroundColor: Color(0xFFFFC30C),
          //             backgroundColor: Colors.red,
          //             shape: RoundedRectangleBorder(
          //               borderRadius: BorderRadius.circular(30.0),
          //             ),
          //           ),
          //           child: Text(
          //             L.snatch_red_envelopes.tr,
          //             style: const TextStyle(
          //               color: Colors.white,
          //               fontSize: 20.0,
          //               fontWeight: FontWeight.bold,
          //             ),
          //             textAlign: TextAlign.center,
          //           ),
          //         ),
          //       ),
          //     ),
          //   ),
          // ),
        ],
      ),
    );
  }

  // 构建 Page2
  Widget _buildPage2() {
    String title = '';
    if ((widget.extension?.isNotEmpty ?? false)) {
      try {
        var info = json.decode(widget.extension ?? '');
        var desc = info['desc'];
        if (desc != null) {
          title = desc ?? '';
        }
      } catch (e) {}
    }
    if (title.isEmpty) {
      title = L.the_hongbao_title.tr;
    }
    var _redPacketHeight = 0.9.sh;
    var _redPacketWidth = 0.9.sh/2.45;
    return Container(
        // width: double.infinity,
        // height: double.infinity,
        height: _redPacketHeight,
        // margin: EdgeInsets.only(left: 20.r, right: 20.r, bottom: 50.r),
        // padding: EdgeInsets.only(
        //     top: 30.r, left: 24.r, right: 20.r, bottom: 80.r), CNY
        // padding: EdgeInsets.only(
        //     top: 187.r, left: 24.r, right: 20.r, bottom: 80.r),
        // decoration: const BoxDecoration(
        //     image: DecorationImage(
        //         image: AssetImage(R.icMoneyPacketOpen), fit: BoxFit.cover)),
        child: Stack(
          children: [
            Center(
              child: Image.asset(
                R.icMoneyPacketOpen,
                fit: BoxFit.fitHeight,
              ),
            ),
            Center(
              child: Column(
                mainAxisSize: MainAxisSize.max, 
                children: [
                  SizedBox(height: _redPacketHeight * 0.15),
                  _buildRedPacketAvatar(_redPacketHeight),
                  SizedBox(
                    // height: 2.r, CNY
                    height: _redPacketHeight * 0.01,
                  ),
                  Container(
                    // height: _redPacketHeight * 0.08,
                    constraints: BoxConstraints(maxWidth: _redPacketWidth * 0.7, maxHeight: _redPacketHeight * 0.08),
                    child: AutoSizeText(
                      'packet_from_whom'
                          .trParams({'name': widget.data.redPacket?.name ?? ''}),
                      // (widget.data.redPacket?.name ?? '') + L.of_money_packet.tr,
                      maxLines: 2,
                      softWrap: true,
                      minFontSize: 14,
                      maxFontSize: 18,
                      textAlign: TextAlign.center,
                      style: const TextStyle(
                        color: Colors.black,
                        fontSize: 18.0,
                      ),
                    ),
                  ),
                  SizedBox(
                    // height: 5.r, CNY
                    height: _redPacketHeight * 0.02,
                  ),
                  SizedBox(
                    height: _redPacketHeight * 0.04,
                    child: Obx(
                      () => AutoSizeText(
                        _controller.tokenName.value,
                        style: const TextStyle(
                          color: Colors.black,
                          fontSize: 24.0,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      // Text.rich(
                      //   TextSpan(
                      //     children: [
                      //       TextSpan(
                      //         text: _controller.tokenName.value,
                      //         style: const TextStyle(
                      //           color: Colors.black,
                      //           fontSize: 24.0,
                      //           fontWeight: FontWeight.bold,
                      //         ),
                      //       ),
                      //     ],
                      //   ),
                      //   textAlign: TextAlign.center,
                      // ),
                    ),
                  ),
                  // SizedBox(
                  //   height: _redPacketHeight * 0.005,
                  // ),
                  SizedBox(
                    child: Obx(() => NumberFlipper(
                          isAnimated: widget.hongBaoOperateState ==
                              HongBaoOperateState.canBeRobbed,
                          value: _controller.amount.value,
                          style: const TextStyle(
                            color: Colors.red,
                            fontSize: 36.0,
                            fontWeight: FontWeight.bold,
                          ),
                          textAlign: TextAlign.center,
                        )),
                  ),
                  // const Spacer(),
                  SizedBox(height: _redPacketHeight * 0.2),
                  Container(
                    height: _redPacketHeight * 0.1,
                      constraints: BoxConstraints(maxWidth: _redPacketWidth * 0.8),
                      child: AutoSizeText(
                        title,
                        maxLines: 2,
                        textAlign: TextAlign.center,
                        minFontSize: 18,
                        maxFontSize: 23,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 23,
                          fontWeight: FontWeight.bold,
                        ),
                      )),
                  SizedBox(
                    height: _redPacketHeight * 0.05,
                  ),
                  Container(
                    width: _redPacketWidth*0.6,
                    height: _redPacketHeight * 0.07,
                    decoration: BoxDecoration(
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.3),
                          offset: const Offset(5, 5),
                          blurRadius: 25,
                          spreadRadius: 1,
                        ),
                      ],
                      borderRadius: BorderRadius.circular(30.0),
                    ),
                    child: ElevatedButton(
                      onPressed: () async {
                        openHongbaoDetail();
                      },
                      style: ElevatedButton.styleFrom(
                        // backgroundColor: Color(0xFFFFC30C), CNY
                        backgroundColor: Colors.red,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(30.0),
                        ),
                      ),
                      child: Text(
                        L.view_detail.tr,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 18.0,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  )
                ],
              ),
            ),
          ],
        ));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        alignment: Alignment.center,
        children: [
          // 背景图
          Image.asset(
            R.icMoneyPacketBg,
            fit: BoxFit.fill,
            width: 1.sw,
            height: 1.sh,
          ),
          // Page2 或 Page3 ScaleTransition
          Obx(() {
            var state = _controller.hongBaoOperateState.value;
            AppLogger.d('hongBaoOperateState =$state');
            _page2ScaleController.forward();

            return ScaleTransition(
              scale: _page2ScaleAnimation,
              child: state == HongBaoOperateState.canBeRobbed
                  ? _buildPage1()
                  : state == HongBaoOperateState.myselfRob
                      ? _buildPage2()
                      : _buildPage3(),
            );
          }),

          // 关闭按钮
          Positioned(
            top: 40.0,
            left: 10.0,
            child: IconButton(
              icon: const Icon(
                Icons.close,
                // color: Colors.black, CNY
                color: Colors.white,
                size: 30.0,
              ),
              onPressed: () {
                Get.back();
              },
            ),
          ),
        ],
      ),
    );
  }
}
