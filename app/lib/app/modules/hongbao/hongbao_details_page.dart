import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/data/models/hongbao_check_status_model.dart';
import 'package:flutter_metatel/app/widgets/mavatar_circle_avatar.dart';
import 'package:flutter_metatel/core/languages/l.dart';
import 'package:flutter_metatel/core/task/hongbao_task.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:flutter_metatel/r.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_web3/web3dart/src/utils/typed_data.dart';
import 'package:get/get.dart';

class HongbaoDetailsPage extends StatefulWidget {
  final HongbaoCheckStatusModelData data;

  const HongbaoDetailsPage({
    super.key,
    required this.data,
  });

  @override
  State<HongbaoDetailsPage> createState() => _HongbaoDetailsPageState();
}

class _HongbaoDetailsPageState extends State<HongbaoDetailsPage> {
  final int totalLink = 25;
  final int remainingLink = 20;

  String? account;
  String? address;
  double? amount;
  String? backhash;
  String? createtime;
  int? fee;
  int? fixedAmount;
  String? head;
  int? id;
  String? nick_name;
  String? accountDomain;
  int? number;
  int? status;
  String? tokenAddress;
  String? topic;
  String? txhash;
  RxString tokenName = ''.obs;

  getTokenName() {
    HongBaoTask().getNameByToken(widget.data.redPacket?.tokenAddress??'').then((value) {
      tokenName.value = value ??'';
      AppLogger.d('tokenName.value  =${tokenName.value }');
    });
  }

  Widget _buildRedPacketAvatar() {
    String? head = widget.data.redPacket?.head;
    String name = widget.data.redPacket?.name ?? '';

    // 检查head是否为空或者为字符串 "null"
    bool isHeadValid = head != null && head.isNotEmpty && head != "null";

    return Container(
      width: 80.0,
      height: 80.0,
      child: Center(
        child: isHeadValid
            ? CachedNetworkImage(
                imageUrl: head,
                imageBuilder: (context, imageProvider) => Container(
                  width: 80.0,
                  height: 80.0,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: Colors.blue,
                    image: DecorationImage(
                      image: imageProvider,
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
                placeholder: (context, url) =>
                    const CircularProgressIndicator(),
                errorWidget: (context, url, error) => const Icon(Icons.error),
              )
            : Container(
                width: 100.0.r,
                height: 100.0.r,
                child: MAvatarCircle(
                  imagePath: widget.data.redPacket?.head ?? '',
                  text: widget.data.redPacket?.name ?? '',
                  textStyle: TextStyle(
                    color: Colors.white,
                    fontSize: 40.0.sp,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    getTokenName();
    return Scaffold(
      appBar: AppBar(
        title: Text(
          L.money_packet_details.tr,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        centerTitle: true,
      ),
      body: Stack(
        children: [
          Positioned.fill(
            child: Image.asset(
              R.icMoneyPacketBg,
              fit: BoxFit.cover,
            ),
          ),
          Positioned(
            top: 0.0,
            left: 0.0,
            right: 0.0,
            bottom: 0.0,
            child: Column(
              children: [
                Container(
                  margin: const EdgeInsets.only(top: 20.0),
                  child: Column(
                    children: [
                      _buildRedPacketAvatar(),
                      Container(
                        margin: const EdgeInsets.only(top: 10.0),
                        child: Text(
                          'packet_from_whom'.trParams(
                              {'name': widget.data.redPacket?.name ?? ''}),
                          // '${widget.data.redPacket?.name ?? ''}' +
                          //     L.of_money_packet.tr,
                          style: const TextStyle(
                            color: Colors.black,
                            fontSize: 12.0,
                          ),
                        ),
                      ),
                      Container(
                        margin: const EdgeInsets.only(top: 10.0),
                        child: Obx(() => Text.rich(
                              TextSpan(
                                children: [
                                  TextSpan(
                                    text:
                                        '${decimalData(widget.data.mylog?.amount?.toDouble() ?? 0.0, 5)} ',
                                    style: const TextStyle(
                                      color: Colors.red,
                                      fontSize: 20.0,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  TextSpan(
                                    text: tokenName.value,
                                    style: const TextStyle(
                                      color: Colors.black,
                                      fontSize: 20.0,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ],
                              ),
                            )),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 20.0),
                Expanded(
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(10.0),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.grey.withOpacity(0.5),
                          spreadRadius: 2,
                          blurRadius: 5,
                          offset: const Offset(0, 3),
                        ),
                      ],
                    ),
                    child: Column(
                      children: [
                        Padding(
                          padding: const EdgeInsets.only(top: 5.0),
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Row(
                                mainAxisSize: MainAxisSize.min,
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Container(
                                    child: const Center(
                                      child: Icon(
                                        Icons.info_outline,
                                        size: 14.0,
                                        color: Colors.black,
                                      ),
                                    ),
                                  ),
                                  const SizedBox(
                                    width: 5.0,
                                  ),
                                  Obx(()=>  Text(
                                    '${widget.data.log?.length ?? 0}/${widget.data.redPacket?.number ?? 0} ${tokenName.value} ${L.has_been_snatched.tr}, ${L.total.tr} ${widget.data.redPacket?.amount ?? 0} ${tokenName.value}',
                                    style: const TextStyle(
                                      color: Colors.black,
                                      fontSize: 12.0,
                                    ),
                                    textAlign: TextAlign.center,
                                  ))
                                 ,
                                ],
                              ),
                              const SizedBox(height: 5.0),
                              const Divider(
                                color: Colors.black12,
                                height: 5.0,
                              ),
                            ],
                          ),
                        ),
                        Expanded(
                          child: ListView.separated(
                            itemCount: widget.data.log?.length ?? 0,
                            separatorBuilder: (context, index) {
                              return const Divider(
                                height: 5.0,
                                color: Colors.black12,
                              );
                            },
                            itemBuilder: (context, index) {
                              final packet = widget.data.log![index];
                              return Column(
                                children: [
                                  const SizedBox(
                                    height: 5.0,
                                  ),
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceEvenly,
                                    children: [
                                      packet!.head != null &&
                                              packet.head != "null" &&
                                              packet.head!.isNotEmpty
                                          ? CachedNetworkImage(
                                              imageUrl: packet.head!,
                                              imageBuilder:
                                                  (context, imageProvider) =>
                                                      Container(
                                                width: 50.0.r,
                                                height: 50.0.r,
                                                decoration: BoxDecoration(
                                                  shape: BoxShape.circle,
                                                  image: DecorationImage(
                                                    image: imageProvider,
                                                    fit: BoxFit.cover,
                                                  ),
                                                ),
                                              ),
                                              placeholder: (context, url) =>
                                                  const CircularProgressIndicator(),
                                              errorWidget:
                                                  (context, url, error) =>
                                                      const Icon(Icons.error),
                                            )
                                          : Container(
                                              width: 50.0.r,
                                              height: 50.0.r,
                                              child: MAvatarCircle(
                                                imagePath: widget
                                                        .data.redPacket?.head ??
                                                    '',
                                                text: widget
                                                        .data.redPacket?.name ??
                                                    '',
                                                textStyle: TextStyle(
                                                  color: Colors.white,
                                                  fontSize: 20.0.sp,
                                                  fontWeight: FontWeight.bold,
                                                ),
                                              ),
                                            ),
                                      Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            packet.name!,
                                            style: const TextStyle(
                                              color: Colors.black,
                                              fontSize: 15.0,
                                            ),
                                          ),
                                          const SizedBox(height: 5.0),
                                          Text(
                                            packet.createtime!,
                                            style: const TextStyle(
                                              color: Colors.grey,
                                              fontSize: 10.0,
                                            ),
                                          ),
                                        ],
                                      ),
                                      // Amount
                                      Obx(() => Text(
                                            "${decimalData(packet.amount?.toDouble() ?? 0.0, 6)} ${tokenName.value ?? ''}",
                                            style: const TextStyle(
                                              color: Color(0xFF249ED9),
                                              fontSize: 16.0,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          )),
                                    ],
                                  ),
                                  const SizedBox(height: 5.0),
                                ],
                              );
                            },
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
