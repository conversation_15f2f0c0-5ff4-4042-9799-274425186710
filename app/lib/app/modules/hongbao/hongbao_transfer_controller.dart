import 'dart:async';
import 'dart:convert';
import 'dart:ffi';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/data/enums/enum.dart';
import 'package:flutter_metatel/app/data/models/avatar_model.dart';
import 'package:flutter_metatel/app/data/models/hongbao_tokens_model.dart';
import 'package:flutter_metatel/app/data/providers/api/hongbao_api.dart';
import 'package:flutter_metatel/app/data/services/config_service.dart';
import 'package:flutter_metatel/core/languages/l.dart';
import 'package:flutter_metatel/core/task/hongbao_task.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:flutter_web3/app/core/hw_wallet_manager.dart';
import 'package:flutter_web3/app/core/language/s.dart';
import 'package:flutter_web3/app/core/utils/util.dart';
import 'package:get/get.dart';

class HongBaoTransferController extends GetxController {
  var rxBalance = (.0).obs;
  var rxGas = BigInt.zero.obs;
  final FocusNode focusNode = FocusNode();
  final FocusNode focusNode2 = FocusNode();
  final FocusNode focusNode3 = FocusNode();
  final FocusNode focusNode4 = FocusNode();
  final FocusNode focusNode5 = FocusNode();
  final ValueNotifier<bool> buttonNotifier = ValueNotifier<bool>(false);

  final TextEditingController totalAmountController = TextEditingController();
  final TextEditingController remarkController = TextEditingController();
  final TextEditingController amountpacketController =
      TextEditingController(); //红包数量
  final ValueNotifier<String> balanceTipsNotifier = ValueNotifier<String>('');

  final List<StreamSubscription> _subscription = [];
  final HwWalletManager _hwWalletManager = HwWalletManager();
  RxList<HongbaoTokensModelData> hongbaoModelList =
      <HongbaoTokensModelData>[].obs;
  Rx<HongbaoTokensModelData> currentToken = HongbaoTokensModelData().obs;
  RxString packetType = 'random'.obs;
  RxDouble amount = 0.0.obs;
  RxDouble fixedAmount = 0.0.obs;
  RxString feeInfo = ''.obs;
  RxString receivingAddress = ''.obs;

  var totalAmountRx = 0.0.obs;
  var currentTotalAmount = 0.0.obs;
  var feeRx = 0.0.obs;
  RxString maxNumber = ''.obs;

  double totalAmount() {
    double value = 0.0;
    if (totalAmountController.text.isNotEmpty) {
      try {
        value = double.parse(totalAmountController.text);
      } catch (e) {
        AppLogger.d('Invalid balance input: ${totalAmountController.text}');
        value = 0.0;
      }
    }
    if (packetType.value != 'random') {
      //固定红包
      var number = amountpacketController.text.trim();
      if (number.isNotEmpty) {
        int n = int.parse(number);
        value = value * n;
      }
    }
    return value;
  }

  @override
  void onInit() {
    super.onInit();
    getTokenList();
  }

  _updateNetWork(String chainid) async {
    try {
      int chId = int.parse(chainid);
      var currentchanId = await HwWalletManager().getChainId();
      if (chId != currentchanId) {
        await HwWalletManager().onWalletSwitchEthereumChain(chId);
      }
      _getTokenBlance();
    } catch (e) {
      e.toString();
    }
  }

  changeBalance() {
    if (currentTotalAmount.value <= 0) {
      _getTokenBlance();
    }
  }

  @override
  void onClose() {
    for (var element in _subscription) {
      element.cancel();
    }
    _subscription.clear();
    super.onClose();
  }

  onAmountChanged(String value) async {
    var v = value.trim();
    if (v.isNotEmpty) {
      double inputAmount = 0.0;
      try {
        inputAmount = double.parse(v);
      } catch (e) {
        totalAmountController.text = '0.00';
      }
      await fetchFeeAndInfo();
      totalAmountRx.value = inputAmount;
      _changeFee();
    } else {
      totalAmountController.text = '';
      totalAmountRx.value = 0;
    }
    _changeSubmitState();
  }

  _changeFee() {
    var amount = totalAmountController.text.trim();
    var number = amountpacketController.text.trim();
    var fee = feeInfo.value;
    if (packetType.value != 'random') {
      if (amount.isNotEmpty && number.isNotEmpty && fee.isNotEmpty) {
        double a = double.parse(amount);
        int n = int.parse(number);
        double f = double.parse(fee);
        if (a > 0 && n > 0) {
          feeRx.value = a * n * f;
        } else {
          feeRx.value = 0;
        }
      } else {
        feeRx.value = 0;
      }
    } else {
      if (amount.isNotEmpty && fee.isNotEmpty) {
        double a = double.parse(amount);
        double f = double.parse(fee);
        if (a > 0 && f > 0) {
          feeRx.value = a * f;
        } else {
          feeRx.value = 0;
        }
      } else {
        feeRx.value = 0;
      }
    }
  }

  void changeTokenSelected(HongbaoTokensModelData data) {
    for (HongbaoTokensModelData a in hongbaoModelList) {
      if (a == data) {
        if (currentToken.value != data) {
          _resetData();
          _changeSubmitState();
        }
        a.isSelected = true;
        currentToken.value = data;
        _updateNetWork(currentToken.value.chainid ?? '');
      } else {
        a.isSelected = false;
      }
    }
  }

  _resetData() {
    buttonNotifier.value = false;
    currentTotalAmount.value = 0;
    totalAmountRx.value = 0;
    maxNumber.value = '';
    totalAmountController.text = '';
    balanceTipsNotifier.value = '';
    amountpacketController.text = '';
    receivingAddress.value = '';
  }

  void getTokenList() async {
    var response = await RedPacketApi().getTokens();
    if (response.data != null) {
      hongbaoModelList.clear();
      hongbaoModelList.addAll(response.data!);
      if (hongbaoModelList.isNotEmpty) {
        currentToken.value = hongbaoModelList.first;
        _updateNetWork(currentToken.value.chainid ?? '');
      }
    }
  }

  amountPacketChange(String value) async {
    if (value.isNotEmpty) {
      int? number = int.tryParse(value);
      if (number != null) {
        // 请求更新 maxNumber
        await fetchFeeAndInfo();
      }
    } else {
      amountpacketController.clear();
    }
    _changeSubmitState();
  }

  _changeSubmitState() {
    var tokenAddress = currentToken.value.tokenAddress ?? '';
    var totalAmount = totalAmountController.text.trim();
    var number = amountpacketController.text.trim();
    AppLogger.d('_changeSubmitState tokenAddress=$tokenAddress totalAmount=$totalAmount number=$number');
    if (receivingAddress.value.isNotEmpty &&
        tokenAddress.isNotEmpty &&
        totalAmount.isNotEmpty &&
        number.isNotEmpty &&
        currentTotalAmount.value > 0) {
      buttonNotifier.value = true;
    } else {
      buttonNotifier.value = false;
    }
  }

  _getTokenBlance() async {
    if (currentToken.value.tokenAddress?.isNotEmpty ?? false) {
      var address = await HwWalletManager().getWalletAddress();
      var t = await HwWalletManager()
          .getErc20BalanceByAddress(currentToken.value.tokenAddress!, address);
      AppLogger.d('_getTokenBlance ${t}');
      currentTotalAmount.value = t ?? 0;
    }
  }

  Future<bool> fetchFeeAndInfo() async {
    var tokenAddress = currentToken.value.tokenAddress ?? '';
    var totalAmount = totalAmountController.text.trim();
    if (totalAmount.isNotEmpty) {
      try {
        var total = double.parse(totalAmount);
        if (total > 0) {
          var response = await RedPacketApi().feeAndInfo(
            total,
            tokenAddress,
            packetType.value == 'random' ?  0.0 : total,
          );

          if (response.data != null) {
            AppLogger.d('Fee and Info Response: ${response.data}');
            feeInfo.value = response.data!['data']['fee']?.toString() ?? '0';
            receivingAddress.value = response.data!['data']['to'];
            maxNumber.value =
                response.data!['data']['maxNumber']?.toString() ?? '0';
          }
        }
      } catch (e) {
        AppLogger.e("Error Catch <fetchFeeAndInfo>: $e");
        receivingAddress.value = '';
        return false;
      }
    }

    return true;
  }

  double serviceFee() {
    return totalAmount() * 0.01;
  }

  Future<void> send(BuildContext context,ChatType chatType, String to) async {
    focusNode.unfocus();
    focusNode2.unfocus();
    focusNode3.unfocus();
    focusNode4.unfocus();
    focusNode5.unfocus();
    showLoadingDialog(context,isBack: false,timeOutSecond: 30,showTimeOutBack: (b){
      if(b) {
         Get.back();
      }
    });
    var feeAndInfoResult = await fetchFeeAndInfo();
    if (feeAndInfoResult == false) {
      AppLogger.e("Cannot send red packet due to feeAndInfo failure.");
      toast(S.current.send_cancel);
      dismissLoadingDialog();
      return;
    }
    var packetNumber = amountpacketController.text.isNotEmpty
        ? int.parse(amountpacketController.text)
        : 0;
    int max = int.parse(maxNumber.value);
    if (packetNumber > max) {
      toast(L.exceeded_the_aximum_number_of_sends.tr);
      dismissLoadingDialog();
      return;
    }

    AppLogger.d('my== send main 0');
    // FocusManager.instance.primaryFocus?.unfocus();
    // 执行转账操作
    var fromAddress = await _hwWalletManager.getWalletAddress();
    var base_amount = totalAmountController.text.isNotEmpty
        ? double.parse(totalAmountController.text)
        : 0.0;
    if(packetType.value != 'random') {
      base_amount = base_amount * packetNumber;
    }

    var _totalAmount = totalAmount() + serviceFee();
    var _tokenAddress = currentToken.value.tokenAddress ?? '';
    try{
      var result = await _hwWalletManager.sendTransfer(
        fromAddress,
        receivingAddress.value,
        _tokenAddress,
        _totalAmount,
      );
      AppLogger.d('sendTransfer result: $result');
      if (result?.isEmpty ?? true) {
        // toast(S.current.send_cancel);
        dismissLoadingDialog();
        return;
      }

      var hashtx = result.toString();
      var msgId = uuid();
      var account = Get.find<AppConfigService>().getUserNameWithoutDomain();
      var accountDomain = Get.find<AppConfigService>().getUserName();
      var nick_name = Get.find<AppConfigService>().getMySelfDisplayName();
      AvatarModel avatar = AvatarModel.fromJson(
          Get.find<AppConfigService>().getMySelfAvatarInfo() ?? {});
      var equalAmount =
          double.parse(base_amount.toStringAsFixed(6)) / packetNumber;
      Map<String, dynamic> info = {
        'topic': msgId.toString(),
        'address': fromAddress.toString(),
        'account': accountDomain.toString(),
        'name': nick_name.toString(),
        'head': avatar.avatarUrl.toString(),
        'amount': base_amount.toString(),
        'number': packetNumber.toString(),
        'txhash': hashtx.toString(),
        'token_address': _tokenAddress.toString(),
        'fixed_amount':
        packetType.value == 'random' ? '0.0' : equalAmount.toString(),
      };
      // 签名数据
      AppLogger.d('redpacket info: ${jsonEncode(info)}');
      var signInfo = await HongBaoTask().signDataToString(jsonEncode(info));
      AppLogger.d('redpacket signInfo: $signInfo');
      var firstData = {
        'type': 1,
        'msgId': msgId.toString(),
        'groupId': to,
        'chatType': chatType.index,
        'data': signInfo,
        'tokenAddress': _tokenAddress.toString(),
        'desc': remarkController.text
      };
      // 提示前端红包初始化
      HongBaoTask().jumpHongBaoDetailHtml(firstData);
      // 调用发送红包接口
      var res = await RedPacketApi()
          .createRedPacket(account.toString(), signInfo.toString());
      if (res?.data?['code'] != 200) {
        toast(res?.data?['msg'] ?? 'error');
        dismissLoadingDialog();
        return;
      }
      // 使用msgId轮询红包状态 当状态返回 2 时 调用发送红包信息
      AppLogger.d(
          'redpacket response: ${res?.data} account: $accountDomain  msgId: $msgId');
      var redPacketRes = await checkRedPacketStatus(
          accountDomain.toString(), msgId, chatType, to,
          maxTry: 6);
      if (redPacketRes == false) {
        toast('查询红包状态失败， 超过最大尝试次数');
        dismissLoadingDialog();
        return;
      }

      var sendMsg = {
        'type': 6,
        'msgId': msgId.toString(),
        'groupId': to,
        'chatType': chatType.index,
        'data': null,
        'tokenAddress': _tokenAddress.toString(),
      };
      HongBaoTask().jumpHongBaoDetailHtml(sendMsg);
      toast('红包发送成功');
      onSuccess(msgId, hashtx, accountDomain!);
    } catch(e) {
      AppLogger.e('转账失败 e=${e.toString()}');
      if(e.toString().contains("exceeds balance")){
        toast(L.insufficient_balance.tr);
      }
    }
    dismissLoadingDialog();

  }

  void onSuccess(String msgId, String hashtx, String accountDomain) {
    // 在这里处理成功逻辑，比如更新 UI 或发起下一步操作
    AppLogger.d(
        '红包发送成功！msgId: $msgId, txhash: $hashtx, account: $accountDomain');
    Get.back();
  }

  // 持续检查红包状态, 当状态返回 2 时 返回 true 否则返回 false
  Future<dynamic> checkRedPacketStatus(
      String account, String msgId, ChatType chatType, String to,
      {String? type, int maxTry = 0}) async {
    var response = await RedPacketApi().checkRedPacketStatus(account, msgId);
    AppLogger.d('checkRedPacketStatus response: $response');
    var status = response?.data?.redPacket?.status;
    if (status == '2') {
      return true;
    }
    if (maxTry > 0) {
      await Future.delayed(Duration(seconds: 3));
      return await checkRedPacketStatus(account, msgId, chatType, to,
          type: type, maxTry: maxTry - 1);
    }
    return false;
  }

  void onTextChanged() {
    _changeFee();
  }

  void changePacketTypeOption(String type) async {
    packetType.value = type;
    await fetchFeeAndInfo();
    _changeFee();
  }
}
