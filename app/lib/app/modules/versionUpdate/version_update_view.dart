import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_metatel/core/languages/l.dart';
import 'package:flutter_metatel/r.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';

import 'package:get/get.dart';
import 'package:wakelock_plus/wakelock_plus.dart';

import '../../../core/values/colors.dart';
import '../../../core/values/config.dart';
import '../../data/services/config_service.dart';
import '../../widgets/ota_update/dialog.dart';
import '../../widgets/ota_update/update.dart';
import '../../widgets/ota_update/wave_view.dart';

class VersionUpdateView extends StatefulWidget {
  const VersionUpdateView({Key? key, required this.entity}) : super(key: key);
  final OTAUpdateEntity entity;

  @override
  State<VersionUpdateView> createState() => _VersionUpdateViewState();
}

class _VersionUpdateViewState extends State<VersionUpdateView> {
  double progress = 0.0;
  OTAStatus? otaStatus;
  bool toBrowserDownload = false;

  @override
  void dispose() {
    WakelockPlus.disable();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async => false,
      child: Scaffold(
        body: Stack(
          children: [            
            Container(
              constraints: BoxConstraints.expand(),
              padding: EdgeInsets.symmetric(horizontal: 24.r),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    AppColors.primaryBgColor2,
                    AppColors.primaryBgColor1,                            
                  ],
                  begin: Alignment(-0.5,-1),
                  end: Alignment(1,1),      
                ),
              ),
              child: Column(
                children: [
                  /// Status bar height
                  SizedBox(
                    height: MediaQuery.of(context).padding.top,
                  ),
                  SizedBox(height: 14.r),
                  /// 关闭按钮
                  Visibility(
                    visible: widget.entity.type != 0,
                    child: Align(
                      alignment: Alignment.centerRight,
                      child: IconButton(
                        onPressed: () async{   
                          if(otaStatus == OTAStatus.downloading) return;                 
                          Get.find<AppConfigService>().saveUpdateVersion(widget.entity.versionCode);
                          SmartDialog.dismiss(tag: OTADialog.dialogTag); /* SystemNavigator.pop()*/
                        },
                        icon: Icon(Icons.close_rounded, size: 22.r,),
                        color: AppColors.white,
                      ),
                    ),
                  ),
                  SizedBox(height: 12.r),
                  /// 图标
                  Image.asset(
                    R.logoVersionUpdate,
                    height: 0.3.sh,
                  ),
                  SizedBox(height: 80.r),
                  /// 版本号
                  Text(
                    "${widget.entity.version}",
                    textAlign: TextAlign.center,
                    style: TextStyle(              
                      color: AppColors.white,
                      fontWeight: FontWeight.w600,
                      fontSize: 16.sp,
                    ),
                  ),
                  SizedBox(height: 4.r),
                  /// 更新标题
                  Text(
                    L.new_update_available.tr,
                    textAlign: TextAlign.center,
                    style: TextStyle(              
                      color: AppColors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 19.sp,
                    ),
                  ),
                  SizedBox(height: 16.r),
                  /// 更新描述
                  Text(
                    L.new_update_available_desc.tr,
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      color: AppColors.white,
                      fontSize: 14.sp,
                    ),
                  ),
                  Spacer(),
                  /// 现在更新 按钮
                  _buildUpdateNowBtn(),              
                  SizedBox(height: 6.r),
                  /// 浏览器下载 按钮
                  _buildBrowserDownloadBtn(),              
                  SizedBox(height: 30.r),
                ],
              ),
            ),
            // _buildWaveBg(),
          ],
        ),
      ),
    );
  }

  Widget _buildWaveBg() {
    if (otaStatus == OTAStatus.downloading) {
      return WaveProgress(Size(1.sw, 1.sh),
          Theme.of(context).colorScheme.primary.withAlpha(50), progress);
    }
    return Container();
  }

  Widget _buildUpdateNowBtn() {
    String buttonText = L.update_immediately.tr;
    VoidCallback? action = startOtaInner;
    if (otaStatus == OTAStatus.downloading) {
      buttonText = L.chat_downing_2.tr + "...  ${progress.toInt()}%";
      action = null;
    } else if (otaStatus == OTAStatus.permissionNotGrantedError) {
      buttonText = L.please_open_permissions.tr;
      action = null;
    }
    bool isGoogle = Config.isGooglePlay;
    return Visibility(
      visible: !isGoogle,
      child: ElevatedButton(
        onPressed: action, 
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.white,
          foregroundColor: AppColors.primaryBgColor1,              
        ),
        child: Text(
          buttonText,  
          style: TextStyle(
            fontSize: 15.sp,
          ),              
        ),
      ),
    );
  }

  Widget _buildBrowserDownloadBtn() {
    bool isGoogle = Config.isGooglePlay;
    if (Platform.isAndroid && !isGoogle)
      return TextButton(
        onPressed: startOtaToBrowser,
        child: Text(
          L.browser_download.tr,
          style: TextStyle(
            color: AppColors.white,
            fontSize: 14.sp,
            fontWeight: FontWeight.w300,
          ),
        ),
      );
       
    if(Platform.isAndroid && isGoogle)
      return ElevatedButton(
        onPressed: startOtaToBrowser, 
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.white,
          foregroundColor: AppColors.primaryBgColor1,              
        ),
        child: Text(
          L.browser_download.tr,  
          style: TextStyle(
            fontSize: 15.sp,
          ),              
        ),
      );
    
    return SizedBox.shrink();
  }

  void startOtaToBrowser() {
    if(otaStatus == OTAStatus.downloading) return;
    toBrowserDownload = true;
    startOta();
  }

  void startOtaInner() {
    toBrowserDownload = false;
    startOta();
  }

  void startOta() async {
    WakelockPlus.enable();
    await OTAUtil.startUpdate(widget.entity.url, (event) {
      otaStatus = event.status;
      var value = event.value;
      if (event.status == OTAStatus.downloading) {
        if (value != null) progress = int.parse(value).toDouble();
      } else if (event.status == OTAStatus.installing) {
        // Navigator.of(context).pop();
      }
      setState(() {});
    }, toBrowserDownload: toBrowserDownload);
    WakelockPlus.disable();
  }
}
