import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/modules/account/account_service.dart';
import 'package:flutter_metatel/core/task/private_task.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:flutter_metatel/core/values/colors.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:get/get.dart';

import '../../../../../core/languages/l.dart';
import '../../../../../core/utils/util.dart';
import '../../../../../r.dart';
import '../../../../data/models/res/node_info.model.dart';
import '../../../../data/providers/api/operation_center_api.dart';
import '../../../../data/services/config_service.dart';
import 'create_account_controller.dart';

class CreateAccountGenerateMnemonicPage extends GetView<CreateAccountController> {
  CreateAccountGenerateMnemonicPage({Key? key}) : super(key: key);
  @override
  final CreateAccountController controller =
  Get.put(CreateAccountController());
  TextEditingController _textEditingController =  TextEditingController();
  @override
  Widget build(BuildContext context) {
    // FlutterBugly.postCatchedException(() {
    //   if (!Config.isGooglePlay) {
    //     FlutterBugly.init(
    //       androidAppId: "abf9ee4857",
    //       iOSAppId: "1993a92e74",
    //     );
    //   }
    // });
    WidgetsBinding.instance.addPostFrameCallback((_) {
      controller.generateMnemonic();
     });
    return Scaffold(
          // appBar: AppBarCommon().build(context, title: L.create_account.tr),
      // body: Center(
      // child: CircularProgressIndicator(), // 加载时显示的进度指示器
      // ),
        body: Obx(() {
      if (controller.isNavigating.value) {
        // 当正在导航时，隐藏页面内容
        // showLoadingDialog( isBack: false);
        return SizedBox.shrink();
      } else {
        // 正常显示页面内容
        return Container(
          color: Theme.of(context).primaryColor,
          padding: const EdgeInsets.only(left: 16, top: 16, right: 20, bottom: 0).r,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            height: 25.h,
          ),
          Container(
            margin: EdgeInsets.only(left: 30.r,right: 30.r),
            child:Image.asset( currentLanguageIsSimpleChinese()
              ? R.iconCreateNoteWords1
              : R.iconCreateNoteWords1En) ,),
          SizedBox(
            height: 42.h,
          ),
          Expanded(child: ListView(
            children: [
              Text(
                L.create_account_generate_mnemonic_hint.tr,
                softWrap: true,
                style:  TextStyle(color: AppColors.colorFF333333,fontSize: 14.sp),
              ),
              SizedBox(
                height: 48.h,
              ),
            ],
          )),
          ElevatedButton(
            onPressed: () async {
              importMnemonic();
            },
            child: Text(
              L.import_mnemonic.tr,
              style: TextStyle(fontSize: 16.sp),
            ),
          ),
          SizedBox(height: 20.r,),
          ElevatedButton(
            onPressed: () async {
              controller.generateMnemonic();
            },
            child: Text(
              L.generate_mnemonic.tr,
              style: TextStyle(fontSize: 16.sp),
            ),
          ),
          SizedBox(height: 50.r,),
        ],

    ),);
        }
          }),
    );
  }

importMnemonic() async {
  _textEditingController.text ='';
  await SmartDialog.show(builder: (context) {
    return AlertDialog(
      content: Container(
        padding: const EdgeInsets.only(left: 10, right: 10),
        constraints: const BoxConstraints(minHeight: 200),
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10).r,
            border: Border.all(color: AppColors.colorFFF8F8F8, width: 1)),
        child: TextField(
          controller: _textEditingController,
          textAlignVertical: TextAlignVertical.bottom,
          keyboardType: TextInputType.multiline,
          maxLines: null,
          decoration: InputDecoration(
              hintMaxLines: 4,
              hintText: L.please_enter_mnemonic_words_separated_by_spaces.tr,
              border: InputBorder.none,
              hintStyle: const TextStyle(color: AppColors.colorFFB3B3B3)),
        ),
      ),
      actions: [
        TextButton(
          child: Text(L.cancel.tr),
          onPressed: () => SmartDialog.dismiss(), //关闭对话框,
        ),
        TextButton(
          child: Text(L.import.tr),
          onPressed: () {
            SmartDialog.dismiss();
            checkWorks();
          }, //关闭对话框,
        ),
      ],
      actionsAlignment: MainAxisAlignment.end,
    );
  });
}
// 导入助记词
checkWorks() async{
      var works = _textEditingController.value.text;
      if (works.isEmpty) {
        toast(L.authorization_code_exception_1.tr);
        return;
      }
      var arrays = works.split(' ');
      showLoadingDialog(timeOutSecond: 60, isBack: false);
      var legitimate = mnemonicLegality(arrays);
      AppLogger.d('checkWorks arrays=$arrays');
      if (!legitimate) {
        dismissLoadingDialog();
        toast(L.authorization_code_exception_1.tr);
        return;
      }
      //
      var userName = await PrivateConfigTask.fromMnemonicToUser(works);
      if (userName?.isEmpty ?? true) {
        toast('Mnemonic is not Legality');
        dismissLoadingDialog();

        return;
      }
      var response = await Get.find<OperationCenterApiProvider>()
          .getNodeInfoRes(user: userName);
      if (response?.statusCode == 200) {
        NodeInfoModel body = NodeInfoModel.fromJson(response!.data);
        if (body.code == 200 &&
            (body.data?.node?.isNotEmpty ?? false) &&
            (body.data?.port != 0)) {
          Get.find<AppConfigService>().saveNode(body.data?.node);
          Get.find<AppConfigService>().saveNodePort('${body.data?.port}');
          Get.find<AccountService>().login(
            works,
          );
          return;
        }
      }
      dismissLoadingDialog();
      // controller.generateMnemonic02(works);
    }
}