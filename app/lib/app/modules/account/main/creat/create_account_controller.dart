import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:flutter_metatel/routes/pages.dart';
import 'package:get/get.dart';
import 'package:bip39/bip39.dart' as bip39;

import '../../account_service.dart';



class CreateAccountController extends GetxController {
  var isNavigating = false.obs;

  void generateMnemonic() async {
    if (isNavigating.value) return; // 防止重复导航
    isNavigating.value = true;
    String secureStoreReadMnemonic = bip39.generateMnemonic();
    AppLogger.d("secureStoreReadMnemonic==$secureStoreReadMnemonic");
    Map<String,dynamic> map={};
    map[AccountService.mnemonicTAG]=secureStoreReadMnemonic;
    Get.toNamed(Routes.MnemonicPage,arguments: map);
  }
  void generateMnemonic02(String works) async {
    Map<String,dynamic> map={};
    map[AccountService.mnemonicTAG]=works;
    Get.toNamed(Routes.MnemonicPage,arguments: map);
    // Get.toNamed(Routes.SelectComNodeDialog,arguments:Get.arguments);



  }
}

//导入助记词
// Future<void> importMnemonic(String mnemonic, String walletname, String password) async {
//   print('助记词====      ' + mnemonic);
//   String seed = bip39.generateMnemonic();
//   print('seed====    ' + seed);
//   var root = bip32.BIP32.fromSeed(hex.decode(seed));
//   var child = root.derivePath("m/44'/60'/0'/0/0");
//   String privateKey = '0x' + hex.encode(child.privateKey);
//   print("私钥:" + privateKey);
//   // String privateKey ='0x'+ bytesToHex(child.privateKey);
//   // print("公钥:" + bytesToHex(child1.publicKey));
//   Credentials credentials = EthPrivateKey.fromHex(privateKey);
//   EthereumAddress address = await credentials.extractAddress();
//   String mAddress = address.hexEip55;
//   print("地址   ====   " + mAddress);
//   var random = Random();
//   Wallet wallet = Wallet.createNew(credentials, password, random);
//   String keystore = wallet.toJson();
//   print("keystore==== " + keystore);
//
// }
//
// //导入keystore
// Future<void> importKetystore(String keystore, String walletname, String password) async {
//   print('解析keystore====     ' + keystore);
//   Wallet wallet = Wallet.fromJson(keystore, password);
//   EthereumAddress address = await wallet.privateKey.extractAddress();
//   String mAddress = address.hexEip55;
//   print("地址   ====   " + mAddress);
//   String privateKey = bytesToHex(wallet.privateKey.privateKey);
//   print("私钥====     " + privateKey);
// }
//
// //导入私钥
// Future<void> importPrivate( String mprivate, String walletname, String password) async {
//   print('私钥====     ' + mprivate);
//   Credentials credentials = EthPrivateKey.fromHex(mprivate);
//   EthereumAddress address = await credentials.extractAddress();
//   String mAddress = address.hexEip55;
//   print("地址   ====   " + mAddress);
//   var random = new Random.secure();
//   Wallet wallet = Wallet.createNew(credentials, password, random);
//   String keystore = wallet.toJson();
//   print("keystore====     " + keystore);
// }