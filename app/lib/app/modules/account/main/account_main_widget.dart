import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';


class AccountMainWidget extends StatelessWidget {
  const AccountMainWidget(
      {Key? key,
      required this.iconLeading,
      required this.title,
      required this.subTitle, required this.tabToPage,this.bgGradient, this.bgColor, this.titleColor, this.subTitleColor, this.bgImage, this.radius, })
      : super(key: key);
  final String iconLeading;
  final String title;
  final String subTitle;
  final Gradient? bgGradient;
  final Color? bgColor;
  final String? bgImage;
  final Color? titleColor;
  final Color? subTitleColor;
  final double? radius;
  final dynamic tabToPage;
  @override
  Widget build(BuildContext context) {
    return Container(
        decoration:  BoxDecoration(
            borderRadius: BorderRadius.all(Radius.circular(radius??20.0).r).r,
            image:bgImage==null?null:DecorationImage(
                image: AssetImage(bgImage!), fit: BoxFit.cover),
            color: bgColor,
            gradient:bgGradient),
        alignment: Alignment.center,
        margin: const EdgeInsets.only(left: 30,right: 30).r,
        padding: const EdgeInsets.only(top:20,bottom: 20).r,
        child: ListTile(
          contentPadding:
              const EdgeInsets.only(left: 15, right: 15).r,
          leading: Image.asset(
            iconLeading,
            height: 50.h,
            width: 50.w,
          ),
          title: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: TextStyle(
                    color: titleColor,
                    fontSize: 16,
                    fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 10.h,),
              Text(
                subTitle,
                style:
                TextStyle(color: subTitleColor, fontSize: 12),
              ),
            ],
          ),
          // subtitle: Container(
          //
          //   child: Text(
          //     subTitle,
          //     style:
          //     TextStyle(color: subTitleColor, fontSize: 12),
          //   ),
          // ),
          trailing: Container(
            alignment: Alignment.center,
            height: double.infinity,
            width: 12.h,
            child: Image.asset(
              'assets/images/skin_next_icon.png',
              width: 8.w,
              height: 12.h,
            ),
          ),
          onTap: () {
            Get.to(tabToPage);
          },
        ));
  }
}
