import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/modules/account/main/creat/creat_account_generate_mnemonic.dart';
import 'package:flutter_metatel/app/modules/home/<USER>/mine_view.dart';
import 'package:flutter_metatel/app/modules/home/<USER>/proxy/proxy_page.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../core/languages/l.dart';
import '../../../../core/values/colors.dart';
import '../../../../core/values/config.dart';
import '../../../../r.dart';
import '../mnemonic/restore/mnemonic_restore_view.dart';
import 'account_main_controller.dart';
import 'package:animate_do/animate_do.dart';

class AccountMainImPage extends StatefulWidget {
  const AccountMainImPage({Key? key}) : super(key: key);

  @override
  State<AccountMainImPage> createState() => _AccountMainImPageState();
}

class _AccountMainImPageState extends State<AccountMainImPage> {
  final controller = Get.put(AccountMainController());
  Timer? timer;

  void startTimer() {
    timer?.cancel();
    timer = Timer.periodic(Duration(milliseconds: 4500), (timer) {
      int currentPage = controller.currentPage.value;
      if (currentPage == 2) {
        controller.pageController.jumpToPage(0);
      } else {
        controller.pageController.nextPage(
          duration: Duration(milliseconds: 700),
          curve: Curves.easeInOut,
        );
      }
    });
  }

  @override
  void initState() {
    super.initState();
    controller.pageController.addListener(() {
      int currentPage = controller.pageController.page!.round();
      if (currentPage != controller.currentPage.value) {
        controller.currentPage.value = currentPage;
      }
    });

    Future.delayed(Duration(milliseconds: 1000), () {
      // startTimer();
    });
  }

  @override
  void dispose() {
    timer?.cancel();
    controller.pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        // alignment: AlignmentDirectional.topCenter,
        children: [
          SizedBox(height: 40.r,),
          Image.asset(
            R.logoTitle,
            height: 20.r,
          ),
          SizedBox(height: 20.r,),
          ConstrainedBox(
            constraints: BoxConstraints(maxHeight: 0.45.sh),
            child: PageView(
              physics: BouncingScrollPhysics(),
              controller: controller.pageController,
              children: [
                _buildPageContent(
                  circleAsset: R.circle,
                  AssetFirst: Positioned(
                    left: 110.r,
                    bottom: 15.r,
                    // height: 120.h,
                    // width: 110.w,
                    child: FadeInLeftBig(
                      delay: Duration(milliseconds: 700),
                      child: Image.asset(
                        R.tabWarning,
                        height: 125.r,
                      ),
                    ),
                  ),
                  AssetSecond: Positioned(
                    right: 103.r,
                    bottom: 15.r,
                    // width: 70.w,
                    child: JelloIn(
                      delay: Duration(milliseconds: 1800),
                      child: Image.asset(
                        R.phoneLock,
                        height: 90.r,
                      ),
                    ),
                  ),
                  title: L.building_your_digital_fortress.tr,
                  description: L
                      .comprehensive_protection_for_your_peace_of_mind_online
                      .tr,
                ),
                _buildPageContent(
                  circleAsset: R.circle,
                  AssetFirst: Positioned(
                    left: 130.r,
                    // height: 120.h,
                    // width: 110.w,
                    child: FadeInLeftBig(
                      delay: Duration(milliseconds: 300),
                      child: Image.asset(
                        R.phoneKeyholeWithKey,
                        height: 110.r,
                      ),
                    ),
                  ),
                  AssetSecond: Positioned(
                    right: 110.r,
                    bottom: 15.r,
                    // width: 120.w,
                    child: JelloIn(
                      delay: Duration(milliseconds: 1500),
                      child: Image.asset(
                        R.folder,
                        height: 60.r,
                      ),
                    ),
                  ),
                  title: L.your_privacy_our_mission.tr,
                  description: L
                      .take_control_of_your_personal_data_with_linksay.tr,
                ),
                _buildPageContent(
                  circleAsset: R.circle,
                  // AssetFirst: Positioned(
                  //   left: 110.w,
                  //   height: 120.h,
                  //   bottom: 35.h,
                  //   width: 90.w,
                  //   child: JelloIn(
                  //     delay: Duration(milliseconds: 1200),
                  //     child: Image.asset(
                  //       R.hornbill,
                  //     ),
                  //   ),
                  // ),
                  AssetSecond: Positioned(
                    right: 110.r,
                    bottom: 50.r,
                    // width: 70.w,
                    child: JelloIn(
                      delay: Duration(milliseconds: 2000),
                      child: Image.asset(
                        R.textbubble1,
                        height: 60.r,
                      ),
                    ),
                  ),
                  // AssetThird: Positioned(
                  //   left: 110.w,
                  //   height: 120.h,
                  //   bottom: 35.h,
                  //   width: 90.w,
                  //   child: JelloIn(
                  //     delay: Duration(milliseconds: 2000),
                  //     child: Image.asset(
                  //       R.linksayBubble,
                  //     ),
                  //   ),
                  // ),
                  AssetFourth: Positioned(
                    right: 160.r,
                    bottom: 50.r,
                    // width: 80.w,
                    child: JelloIn(
                      // duration: Duration(milliseconds: 2000),
                      delay: Duration(milliseconds: 1000),
                      child: Image.asset(
                        R.textbubble2,
                        height: 70.r,
                      ),
                    ),
                  ),
                  AssetFifth: Positioned(
                    right: 160.r,
                    bottom: 35.r,
                    // width: 45.w,
                    child: BounceInDown(
                      // duration: Duration(milliseconds: 2000),
                      delay: Duration(milliseconds: 3000),
                      child: Image.asset(
                        R.padlock,
                        height: 40.r,
                      ),
                    ),
                  ),
                  title: L
                      .unleash_digital_freedom_enjoy_boundless_experiences
                      .tr,
                  description: L
                      .breaking_barries_unlocking_possibilities_with_linksay
                      .tr,
                ),
              ],
              onPageChanged: (index) {
                controller.currentPage.value = index;
                startTimer();
              },
            ),
          ),
          Obx(() => Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: List.generate(3, (index) {
                  return _buildDot(index == controller.currentPage.value);
                }),
              )),
          const Spacer(),
          // SizedBox(height: 20.r),
          // Positioned(
          //   left: 0,
          //   top: 0,
          //   right: 0,
          //   child: Container(
          //     height: 274.h,
          //     width: double.infinity,
          //     child: Column(
          //       mainAxisAlignment: MainAxisAlignment.start,
          //       children: [
          //         SizedBox(height: 10.h),
          //         Center(
          //           child: Image.asset(
          //             R.logoTitle,
          //             height: 20.r,
          //           ),
          //         ),
          //       ],
          //     ),
          //   ),
          // ),
          // Positioned(
          //   left: 0,
          //   top: 100.h,
          //   right: 0,
          //   child: Column(
          //     children: [
          //       SizedBox(
          //         height: 290.h,
          //         child: PageView(
          //           physics: BouncingScrollPhysics(),
          //           controller: controller.pageController,
          //           children: [
          //             _buildPageContent(
          //               circleAsset: R.circle,
          //               AssetFirst: Positioned(
          //                 left: 100.w,
          //                 height: 120.h,
          //                 width: 110.w,
          //                 child: FadeInLeftBig(
          //                   delay: Duration(milliseconds: 700),
          //                   child: Image.asset(
          //                     R.tabWarning,
          //                   ),
          //                 ),
          //               ),
          //               AssetSecond: Positioned(
          //                 right: 95.r,
          //                 bottom: 20.h,
          //                 width: 70.w,
          //                 child: JelloIn(
          //                   delay: Duration(milliseconds: 1800),
          //                   child: Image.asset(
          //                     R.phoneLock,
          //                   ),
          //                 ),
          //               ),
          //               title: L.building_your_digital_fortress.tr,
          //               description: L
          //                   .comprehensive_protection_for_your_peace_of_mind_online
          //                   .tr,
          //             ),
          //             _buildPageContent(
          //               circleAsset: R.circle,
          //               AssetFirst: Positioned(
          //                 left: 100,
          //                 height: 120.h,
          //                 width: 110.w,
          //                 child: FadeInLeftBig(
          //                   delay: Duration(milliseconds: 300),
          //                   child: Image.asset(
          //                     R.phoneKeyholeWithKey,
          //                   ),
          //                 ),
          //               ),
          //               AssetSecond: Positioned(
          //                 right: 100.r,
          //                 bottom: 10.h,
          //                 width: 120.w,
          //                 child: JelloIn(
          //                   delay: Duration(milliseconds: 1500),
          //                   child: Image.asset(
          //                     R.folder,
          //                   ),
          //                 ),
          //               ),
          //               title: L.your_privacy_our_mission.tr,
          //               description: L
          //                   .take_control_of_your_personal_data_with_linksay.tr,
          //             ),
          //             _buildPageContent(
          //               circleAsset: R.circle,
          //               // AssetFirst: Positioned(
          //               //   left: 110.w,
          //               //   height: 120.h,
          //               //   bottom: 35.h,
          //               //   width: 90.w,
          //               //   child: JelloIn(
          //               //     delay: Duration(milliseconds: 1200),
          //               //     child: Image.asset(
          //               //       R.hornbill,
          //               //     ),
          //               //   ),
          //               // ),
          //               AssetSecond: Positioned(
          //                 right: 110.r,
          //                 bottom: 50.h,
          //                 width: 70.w,
          //                 child: JelloIn(
          //                   delay: Duration(milliseconds: 2000),
          //                   child: Image.asset(
          //                     R.textbubble1,
          //                   ),
          //                 ),
          //               ),
          //               // AssetThird: Positioned(
          //               //   left: 110.w,
          //               //   height: 120.h,
          //               //   bottom: 35.h,
          //               //   width: 90.w,
          //               //   child: JelloIn(
          //               //     delay: Duration(milliseconds: 2000),
          //               //     child: Image.asset(
          //               //       R.linksayBubble,
          //               //     ),
          //               //   ),
          //               // ),
          //               AssetFourth: Positioned(
          //                 right: 160.r,
          //                 bottom: 50.h,
          //                 width: 80.w,
          //                 child: JelloIn(
          //                   // duration: Duration(milliseconds: 2000),
          //                   delay: Duration(milliseconds: 1000),
          //                   child: Image.asset(
          //                     R.textbubble2,
          //                   ),
          //                 ),
          //               ),
          //               AssetFifth: Positioned(
          //                 right: 155.r,
          //                 bottom: 25.h,
          //                 width: 45.w,
          //                 child: BounceInDown(
          //                   // duration: Duration(milliseconds: 2000),
          //                   delay: Duration(milliseconds: 3000),
          //                   child: Image.asset(
          //                     R.padlock,
          //                   ),
          //                 ),
          //               ),
          //               title: L
          //                   .unleash_digital_freedom_enjoy_boundless_experiences
          //                   .tr,
          //               description: L
          //                   .breaking_barries_unlocking_possibilities_with_linksay
          //                   .tr,
          //             ),
          //           ],
          //           onPageChanged: (index) {
          //             controller.currentPage.value = index;
          //             startTimer();
          //           },
          //         ),
          //       ),
          //       Obx(() => Row(
          //             mainAxisAlignment: MainAxisAlignment.center,
          //             children: List.generate(3, (index) {
          //               return _buildDot(index == controller.currentPage.value);
          //             }),
          //           )),
          //       SizedBox(height: 20.h),
          //     ],
          //   ),
          // ),
          SizedBox(
            height: 0.33.sh,
            child: CustomPaint(
              painter: CurvedPainter(),
              child: Container(
                width: double.infinity,
                // height: 300.r, // Adjust the height according to your design
                padding: EdgeInsets.symmetric(horizontal: 70.0.r),
                // margin: EdgeInsets.only(
                //     bottom: 40.r + MediaQuery.of(context).padding.bottom),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    SizedBox(height: 15.r),
                    SizedBox(
                      width: 1.sw,
                      child: ElevatedButton(
                        onPressed: () {
                          Get.to(() => CreateAccountGenerateMnemonicPage());
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.white, // White background
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(30.0),
                          ),
                        ),
                        child: Text(
                          L.create_account.tr,
                          style: TextStyle(fontSize: 14.sp, color: AppColors.primaryBgColor1, fontWeight: FontWeight.w500),
                        ),
                      ),
                    ),
                    SizedBox(height: 20.r),
                    // Restore Account Button
                    SizedBox(
                      width: 1.sw,
                      height: 44.r,
                      child: OutlinedButton(
                        onPressed: () {
                          Get.to(() => MnemonicRestorePage());
                        },
                        style: OutlinedButton.styleFrom(
                          side: BorderSide(color: AppColors.white),
                          backgroundColor: Colors.transparent,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(30.0),
                          ),
                        ),
                        child: Text(
                          L.restore_account.tr,
                          style: TextStyle(
                            color: AppColors.white,
                            fontSize: 14.sp,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ),
                    // SizedBox(height: 30.r),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDot(bool isActive) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 2.0.w),
      height: 10.h,
      width: 5.h,
      decoration: BoxDecoration(
        color: isActive
            ? Color.lerp(Color(0xff02A3F3), Color(0xffB33D84), 0.5)
            : Colors.grey,
        shape: BoxShape.circle,
      ),
    );
  }

  Widget _buildPageContent({
    required String circleAsset,
    Positioned? AssetFirst,
    Positioned? AssetSecond,
    Positioned? AssetThird,
    Positioned? AssetFourth,
    Positioned? AssetFifth,
    required String title,
    required String description,
  }) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Stack(
          alignment: Alignment.center,
          children: [
            // Image.asset(
            //   circleAsset,
            //   height: 155.h,
            //   width: 350.w,
            // ),
            ZoomIn(
              delay: Duration(milliseconds: 700),
              duration: Duration(milliseconds: 1000),
              curve: Curves.easeInOut,
              child: Image.asset(
                circleAsset,
                height: 155.r,
                width: 350.r,
              ),
            ),

            if (AssetFirst != null) AssetFirst,
            if (AssetSecond != null) AssetSecond,
            if (AssetThird != null) AssetThird,
            if (AssetFourth != null) AssetFourth,
            if (AssetFifth != null) AssetFifth,
          ],
        ),
        SizedBox(height: 20.r),
        Center(
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 20.r),
            child: Text(
              title,
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 16.sp,
                color: Colors.black,
              ),
            ),
          ),
        ),
        SizedBox(height: 10.r),
        Center(
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 20.r),
            child: Text(
              description,
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 12.sp,
                color: Colors.black,
              ),
              softWrap: true,
            ),
          ),
        ),
      ],
    );
  }
}

class BottomWaveClipper extends CustomClipper<Path> {
  @override
  Path getClip(Size size) {
    var path = Path();
    // Start from the top-left corner
    path.lineTo(0, size.height - 100);

    // Create the first curve
    var firstControlPoint = Offset(size.width / 4, size.height);
    var firstEndPoint = Offset(size.width / 2, size.height - 50);
    path.quadraticBezierTo(firstControlPoint.dx, firstControlPoint.dy,
        firstEndPoint.dx, firstEndPoint.dy);

    // Create the second curve
    var secondControlPoint = Offset(3 * size.width / 4, size.height - 150);
    var secondEndPoint = Offset(size.width, size.height - 100);
    path.quadraticBezierTo(secondControlPoint.dx, secondControlPoint.dy,
        secondEndPoint.dx, secondEndPoint.dy);

    // Complete the path by drawing to the top-right corner
    path.lineTo(size.width, 0);
    path.close();

    return path;
  }

  @override
  bool shouldReclip(CustomClipper<Path> oldClipper) {
    return false;
  }
}

class CurvedPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    var paint = Paint()
      ..shader = LinearGradient(
        colors: [AppColors.primaryBgColor1, AppColors.primaryBgColor2].reversed.toList(),
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
      ).createShader(Rect.fromLTWH(0, 0, size.width, size.height))
      ..style = PaintingStyle.fill;

    var path = Path();
    path.moveTo(0, 0);
    path.quadraticBezierTo(
        size.width / 2, size.height * 0.2, size.width, 0);
    path.lineTo(size.width, size.height);
    path.lineTo(0, size.height);
    path.close();

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}
