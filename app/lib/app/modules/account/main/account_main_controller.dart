import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/data/services/chatio_service.dart';
import 'package:flutter_metatel/r.dart';
import 'package:get/get.dart';

class AccountMainController extends GetxController {
  final PageController pageController = PageController();
  var currentPage = 0.obs;

  @override
  void onInit() {
    Get.find<ChatioService>().initApiProvider();
    super.onInit();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _precacheImages();
    });

    pageController.addListener(() {
      int next = pageController.page!.round();
      if (currentPage.value != next) {
        currentPage.value = next;
      }
    });
  }

  void _precacheImages() {
    final context = Get.context;
    if (context != null) {
      List<String> images = [
        R.logoTitle,
        R.circle,
        R.tabWarning,
        R.phoneLock,
        R.phoneKeyholeWithKey,
        R.folder,
        <PERSON><PERSON><PERSON>,
        <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
        <PERSON><PERSON><PERSON>,
        <PERSON><PERSON>padlock,
      ];

      for (var imagePath in images) {
        precacheImage(AssetImage(imagePath), context);
      }
    }
  }

  @override
  void onClose() {
    pageController.dispose();
    super.onClose();
  }
}

//导入助记词
// Future<void> importMnemonic(String mnemonic, String walletname, String password) async {
//   print('助记词====      ' + mnemonic);
//   String seed = bip39.generateMnemonic();
//   print('seed====    ' + seed);
//   var root = bip32.BIP32.fromSeed(hex.decode(seed));
//   var child = root.derivePath("m/44'/60'/0'/0/0");
//   String privateKey = '0x' + hex.encode(child.privateKey);
//   print("私钥:" + privateKey);
//   // String privateKey ='0x'+ bytesToHex(child.privateKey);
//   // print("公钥:" + bytesToHex(child1.publicKey));
//   Credentials credentials = EthPrivateKey.fromHex(privateKey);
//   EthereumAddress address = await credentials.extractAddress();
//   String mAddress = address.hexEip55;
//   print("地址   ====   " + mAddress);
//   var random = Random();
//   Wallet wallet = Wallet.createNew(credentials, password, random);
//   String keystore = wallet.toJson();
//   print("keystore==== " + keystore);
//
// }
//
// //导入keystore
// Future<void> importKetystore(String keystore, String walletname, String password) async {
//   print('解析keystore====     ' + keystore);
//   Wallet wallet = Wallet.fromJson(keystore, password);
//   EthereumAddress address = await wallet.privateKey.extractAddress();
//   String mAddress = address.hexEip55;
//   print("地址   ====   " + mAddress);
//   String privateKey = bytesToHex(wallet.privateKey.privateKey);
//   print("私钥====     " + privateKey);
// }
//
// //导入私钥
// Future<void> importPrivate( String mprivate, String walletname, String password) async {
//   print('私钥====     ' + mprivate);
//   Credentials credentials = EthPrivateKey.fromHex(mprivate);
//   EthereumAddress address = await credentials.extractAddress();
//   String mAddress = address.hexEip55;
//   print("地址   ====   " + mAddress);
//   var random = new Random.secure();
//   Wallet wallet = Wallet.createNew(credentials, password, random);
//   String keystore = wallet.toJson();
//   print("keystore====     " + keystore);
// }
