/*
 * <AUTHOR> <PERSON><PERSON>
 * @Date         : 2022-06-14 19:49:40
 * @Description  : 助记词验证码页面
 * @LastEditors  : <PERSON><PERSON>
 * @LastEditTime : 2022-06-16 17:06:26
 * @FilePath     : /flutter_metatel/lib/app/modules/account/mnemonic/mnemonic_view_controller.dart
 */

import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/data/providers/api/operation_center_api.dart';
import 'package:flutter_metatel/app/data/services/config_service.dart';
import 'package:flutter_metatel/core/task/private_task.dart';
import 'package:flutter_metatel/core/task/time_task.dart';
import 'package:flutter_metatel/core/values/colors.dart';
import 'package:flutter_metatel/core/values/config.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../../core/languages/l.dart';
import '../../../../../core/utils/app_log.dart';
import '../../../../../core/utils/save_image_to_photo.dart';
import '../../../../../core/utils/util.dart';
import '../../../../../core/values/code.dart';
import '../../../../../routes/pages.dart';
import '../../../../data/enums/enum.dart';
import '../../account_service.dart';

class MnemonicViewAfterController extends GetxController {
  String? mnemonic;
  MnemonicViewType? mnemonicViewType;
  ByteData? imageBytes;
  GlobalKey key = GlobalKey();
  bool _firstTip = false;
  var userName = "".obs;
  var nickName = "".obs;

  @override
  void onClose() {
    super.onClose();
  }

  @override
  void onInit() {
    var map = Get.arguments;
    AppLogger.d("Get.arguments.runtimeType==${Get.arguments.runtimeType}");
    mnemonic = map[AccountService.mnemonicTAG];
    mnemonicViewType =
        map[AccountService.mnemonicViewTypeTAG] ?? MnemonicViewType.registered;
    Get.find<AccountService>().mnemonic = mnemonic;
    AppLogger.d(
        "Get.arguments.mnemonic==$mnemonic mnemonicViewType==${mnemonicViewType?.index}");
    super.onInit();
  }

  @override
  void onReady() async {
    if(mnemonicViewType==MnemonicViewType.registered){
      Config.privateKeyBase64 = "";
    }
    await PrivateConfigTask().configPrivateKeyFromMnemonic(mnemonic: mnemonic);
    nickName.value=Get.find<AppConfigService>().getMySelfDisplayName();
    userName.value=Get.find<AppConfigService>().getUserName()??"";
    imageBytes = await RepaintBoundaryUtils.savePhotoByGlobalKey(key);
    super.onReady();
  }

  void onHaveAuthCode() async {
    if (mnemonic == null) {
      AppLogger.d("无助记词");
    } else {
      if (!_firstTip) {
        var result = await _showBottomDialog();
        if (result != 'ok') {
          return;
        }
      }

      // ByteData? imageBytes = await RepaintBoundaryUtils.capturePhoto(context);
      Map<String, String> map = {};
      map[AccountService.mnemonicTAG] = mnemonic!;
      // Get.toNamed(Routes.MnemonicVerifyPage,arguments:imageBytes,parameters: map);
      Get.toNamed(Routes.CreateAccountInputRegisterCodePage,
          arguments: imageBytes, parameters: map);
    }
  }

  void copy() {
    saveClipboard(mnemonic);
    AppLogger.d('copy mnemonic=$mnemonic');
    toast(L.copy_success.tr);
  }

  void save(BuildContext context) async {
    imageBytes ??= await RepaintBoundaryUtils.savePhotoByGlobalKey(key);
    var millisecondsSinceEpoch = TimeTask.instance.getNowDateTime().millisecondsSinceEpoch;
    String name = "${L.app_name.tr}-${L.mnemonic.tr}-$millisecondsSinceEpoch";
    RepaintBoundaryUtils.savePhoto(imageBytes,
        name: name,
        toastSuccess: L.save_the_mnemonic_successfully.tr,
        toastError: L.save_the_mnemonic_failed.tr);
  }

  String? authCode;
  void onMobileVerify() async {
    if (mnemonic == null) {
      AppLogger.d("无助记词");
      return;
    }

    if (!_firstTip) {
      var result = await _showBottomDialog();
      if (result != 'ok') {
        return;
      }
    }

    showLoadingDialog( isBack: false);
    var response1 = await Get.find<OperationCenterApiProvider>().isGoogleConnected();
    if(authCode?.isEmpty ??true){
      var response = await Get.find<OperationCenterApiProvider>().getAuthorizationCode();
      if (response.statusCode != Code.code200) {
        toast('code:${response.statusCode}');
        dismissLoadingDialog();
        return;
      }

      if (response.data?.code != VerifyCode.code0000) {
        toast('code:${response.data?.code} msg:${response.data?.msg}');
        dismissLoadingDialog();
        return;
      }
      authCode = response.data?.data?['auth_code'] ?? '';/*'${generateRandomString(6)}n001';*/
    }

    if (authCode?.isEmpty??true) {
      toast('register code is empty');
      dismissLoadingDialog();
      return;
    }

    AppLogger.d('========ly registerCode:$authCode');

    // InputCodeController? controller;
    // try {
    //   controller = Get.find<InputCodeController>();
    // } catch (e) {
    // }
    // controller = Get.put(InputCodeController(),tag:uuid());
    // controller?.onVerify(Get.context!, registerCode: registerCode);
    var accountService = Get.find<AccountService>();
    accountService.selectNode(
      registerCode: authCode!,
      callBack: () {
        accountService.login(
          mnemonic!,
          imageBytes: imageBytes,
          registerCode: authCode,
        );
      },
    );
  }

  Future<T?> _showBottomDialog<T>() {
    _firstTip = true;
    return showModalBottomSheet(
      context: Get.context!,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(12),
        ),
      ),
      builder: (context) {
        return Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.w),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              SizedBox(height: 25.h),
              Text(
                L.tips.tr,
                style: TextStyle(
                  fontSize: 18.sp,
                  fontWeight: FontWeight.bold,
                  color: AppColors.colorFF333333,
                ),
              ),
              SizedBox(height: 32.h),
              Text(
                L.register_mnemonic_tips.tr,
                style: TextStyle(
                  fontSize: 16.sp,
                  color: AppColors.colorFF333333,
                ),
              ),
              SizedBox(height: 50.h),
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // 取消按钮
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () => Get.back(),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.colorFFF2F2F2,
                        shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(18.r)),
                      ),
                      child: Text(
                        L.cancel.tr,
                        style: TextStyle(
                          fontSize: 16.sp,
                          color: AppColors.colorFF333333,
                        ),
                      ),
                    ),
                  ),
                  SizedBox(width: 15.w),
                  // 下一步按钮
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () => Get.back(result: 'ok'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.colorFF4863F0,
                        shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(18.r)),
                      ),
                      child: Text(
                        L.next_step.tr,
                        style: TextStyle(fontSize: 16.sp),
                      ),
                    ),
                  ),
                ],
              ),
              SizedBox(height: 25.h),
            ],
          ),
        );
      },
    );
  }
  void onNext(){
    Get.toNamed(Routes.MnemonicVerifyPage,arguments:Get.arguments);
  }
}
