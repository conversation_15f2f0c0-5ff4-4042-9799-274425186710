import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/data/enums/enum.dart';
import 'package:flutter_metatel/app/widgets/app_bar_cus.dart';
import 'package:flutter_metatel/app/widgets/text_middle_overlow.dart';
import 'package:flutter_metatel/core/values/colors.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../../core/languages/l.dart';
// import '../../../../../core/utils/util.dart';
// import '../../../../../core/values/config.dart';
import '../../../../../r.dart';
// import 'mnemonic_view_controller.dart';
import 'mnemonic_view_controller_after.dart';
class MnemonicPageAfter extends StatefulWidget{
  const MnemonicPageAfter({Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() =>_MnemonicPageAfter();

}
class _MnemonicPageAfter extends State<MnemonicPageAfter> {
  final controller= Get.put(MnemonicViewAfterController());

  @override
  void dispose() {
    Get.delete<MnemonicViewAfterController>();
    super.dispose();
  }
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBarCommon().build(context, title: controller.mnemonicViewType==MnemonicViewType.registered?L.mnemonic.tr:L.mnemonic_export.tr),
      body: Container(
        color: Theme.of(context).primaryColor,
        padding:
        const EdgeInsets.only(left: 16, top: 16, right: 20, bottom: 0).r,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            SizedBox(
              height: 8.r,
            ),
            // Container(
            //   margin: EdgeInsets.only(left: 30.r, right: 30.r),
            //   child: Image.asset(currentLanguageIsSimpleChinese()
            //       ? R.iconCreateNoteWords2
            //       : R.iconCreateNoteWords2En),
            // ),
            _buildStepIndicator(),
            SizedBox(
              height: 34.r,
            ),
            Expanded(
              child: ListView(
                children: [
                  Text(
                    L.mnemonic_view_hint.tr,
                    style: TextStyle(
                        color: Colors.black, fontSize: 14.sp),
                  ),
                  SizedBox(
                    height: 18.r,
                  ),
                  RepaintBoundary(
                    key: controller.key,
                    child: Container(
                      color: AppColors.white,
                      child:Column(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Container(
                            padding: const EdgeInsets.only(
                                top: 15, bottom: 15, left: 8, right: 8)
                                .r,
                            alignment: Alignment.center,
                            decoration: BoxDecoration(
                              color: AppColors.white,
                              borderRadius:
                              const BorderRadius.all(Radius.circular(10)).r,
                              border: Border.all(
                                color: AppColors.colorFFF8F8F8,
                                width: 1.w,
                              ),
                            ),
                            width: double.infinity,
                            child: GridView.count(
                              physics: const NeverScrollableScrollPhysics(),
                              shrinkWrap: true,
                              crossAxisCount: 3,
                              mainAxisSpacing: 15,
                              crossAxisSpacing: 20,
                              childAspectRatio: 1 / 0.35,
                              children: controller.mnemonic == null
                                  ? const <Widget>[]
                                  : _buildMnemonicItemList(
                                  (controller.mnemonic!.split(" "))),
                            ),
                          ),
                          SizedBox(height: 10.h,),
                          Obx(
                                () => MiddleText(
                              controller.nickName.value,
                              WXTextOverflow.ellipsisMiddle,
                              style: TextStyle(
                                fontSize: 11.sp,
                                color: AppColors.colorFF7F7F7F,
                              ),
                            ),
                          ),
                          SizedBox(
                            height: 6.r,
                          ),
                          Obx(
                                () => MiddleText(
                              controller.userName.value,
                              WXTextOverflow.ellipsisMiddle,
                              style: TextStyle(
                                fontSize: 11.sp,
                                color: AppColors.colorFF7F7F7F,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(
              height: 20.r,
            ),
            Row(
              children: [
                Expanded(
                  child: GestureDetector(
                    onTap: () async {
                      controller.copy();
                    },
                    child: Container(
                      height: 39.r,
                      margin: EdgeInsets.only(right: 5.r),
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(20.r),
                          color: AppColors.backgroundGray),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Image.asset(R.iconCopyNew,
                              height: 12.5.r, width: 12.5.r),
                          SizedBox(
                            width: 5.r,
                          ),
                          Text(
                            L.other_copy_word.tr,
                            style: TextStyle(
                                fontSize: 14.sp, color: AppColors.appDefault),
                          )
                        ],
                      ),
                    ),
                  ),
                ),
                Expanded(
                  child: GestureDetector(
                    onTap: () async {
                      controller.save(context);
                    },
                    child: Container(
                      margin: EdgeInsets.only(left: 5.r),
                      height: 39.r,
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(20.r),
                          color: AppColors.backgroundGray),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Image.asset(
                            R.iconSaveWord,
                            height: 12.5.r,
                            width: 12.5.r,
                          ),
                          SizedBox(
                            width: 5.r,
                          ),
                          Text(
                            L.save_to_local.tr,
                            style: TextStyle(fontSize: 14.sp),
                          )
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
            Visibility(
              visible: controller.mnemonicViewType ==
                  MnemonicViewType.registered,
              child: SizedBox(
                height: 30.r,
              ),
            ),
            Visibility(
              visible: controller.mnemonicViewType ==
                  MnemonicViewType.registered,
              child: ElevatedButton(
                onPressed: (){
                  controller.onMobileVerify();
                },
                child: Text(
                  L.next_step.tr,
                  style: TextStyle(fontSize: 16.sp),
                ),
              ),
            ),
            const SizedBox(height: 5),
            SizedBox(
              height: 50.r,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStepIndicator() {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 20.r,
              height: 20.r,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: AppColors.primaryBgColor1,
              ),
              child: Center(
                child: Text(
                  "1",
                  style: TextStyle(color: AppColors.white, fontSize: 14.sp, fontWeight: FontWeight.w500),
                ),
              ),
            ),
            SizedBox(
              width: 0.25.sw,
              child: Image.asset(R.iconStepPath),
            ),
            SizedBox(
              width: 0.25.sw,
              child: Image.asset(R.iconStepPath),
            ),
            Container(
              width: 20.r,
              height: 20.r,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: AppColors.primaryBgColor1,
              ),
              child: Center(
                child: Text(
                  "2",
                  style: TextStyle(color: AppColors.white, fontSize: 14.sp, fontWeight: FontWeight.w500),
                ),
              ),
            ),
          ],
        ),
        SizedBox(height: 6.r),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SizedBox(
              width: 70.r,
              child: Center(
                child: Text(
                  L.export_mnemonic_step_1.tr,
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: 11.sp,
                    color: AppColors.primaryBgColor1,
                  ),
                ),
              ),
            ),
            SizedBox(width: 0.5.sw - 50.r,),
            SizedBox(
              width: 70.r,
              child: Center(
                child: Text(
                  L.export_mnemonic_step_2.tr,
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: 11.sp,
                    color: AppColors.primaryBgColor1,
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Container _buildMnemonicItem(String word, int index) => Container(
    decoration: BoxDecoration(
      color: AppColors.backgroundGray,
      borderRadius: const BorderRadius.all(Radius.circular(5)).r,
    ),
    padding: const EdgeInsets.all(5).r,
    height: 29.h,
    width: 95.w,
    alignment: Alignment.center,
    child: Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          "$index",
          style: TextStyle(color: AppColors.primaryBgColor1, fontSize: 9.sp),
        ),
        Expanded(
            child: Center(
              child: Text(
                word,
                style:
                TextStyle(color: AppColors.colorFF333333, fontSize: 12.sp),
              ),
            )),
      ],
    ),
  );

  List<Widget> _buildMnemonicItemList(List<String> items) {
    List<Widget> widgets = [];
    for (int i = 0; i < items.length; i++) {
      Widget widget = _buildMnemonicItem(items[i], i + 1);
      widgets.add(widget);
    }
    return widgets;
  }
}
