import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/data/enums/enum.dart';
import 'package:flutter_metatel/app/widgets/app_bar_cus.dart';
import 'package:flutter_metatel/app/widgets/text_middle_overlow.dart';
import 'package:flutter_metatel/core/values/colors.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../../core/languages/l.dart';
import '../../../../../core/utils/util.dart';
import '../../../../../core/values/config.dart';
import '../../../../../r.dart';
import 'mnemonic_view_controller.dart';
class MnemonicPage extends StatefulWidget{
  const MnemonicPage({Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() =>_MnemonicPage();

}
class _MnemonicPage extends State<MnemonicPage> {
  final controller= Get.put(MnemonicViewController());

  @override
  void dispose() {
    Get.delete<MnemonicViewController>();
    super.dispose();
  }
  @override
  Widget build(BuildContext context) {
    // WidgetsBinding.instance.addPostFrameCallback((_) {
    //   controller.onReady();
    // });
    return WillPopScope(
      onWillPop: () async { 
        return false;
       },
      child: Obx(() {
        if (controller.isProcessing.value) {
          Future.delayed(const Duration(seconds: 5), () {
            controller.isProcessing.value = true; // Stop the GIF after 5 seconds
          });
          // return Container(
          //   alignment: Alignment.center,
          //   child: Container(
          //     width: double.infinity,
          //     height: double.infinity,
          //     decoration: const BoxDecoration(
          //       image: DecorationImage(
          //         image: AssetImage('assets/images/motion_logo3.gif'),
          //         fit: BoxFit.cover,
          //       ),
          //     ),
          //   ),
          //
          // );
          return Container(
            alignment: Alignment.center,
            child: Stack(
              children: [
                Container(
                  width: double.infinity,
                  height: double.infinity,
                  decoration: const BoxDecoration(
                    image: DecorationImage(
                      image: AssetImage('assets/images/motion_logo3.gif'),
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
                // Overlaying text on top of the GIF
                Positioned(
                  // top: Get.height * 0.6, // Adjust the position according to your need
                  left: 0,
                  right: 0,
                  bottom: 0.15.sh,
                  child: Text(
                    L.generating_account.tr,// Your custom text
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      color: Colors.white, // Set the text color, e.g., white
                      fontSize: 16.sp, // Adjust the font size as needed
                      fontWeight: FontWeight.bold, // Make the text bold
                    ),
                  ),
                ),
              ],
            ),
          );
      
        } else {
          // 正常显示页面内容
          return Container(
            color: Theme.of(context).primaryColor,
            padding: const EdgeInsets.only(left: 16, top: 16, right: 20, bottom: 0).r,
            child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              SizedBox(
                height: 20.h,
              ),
              Container(
                margin: EdgeInsets.only(left: 30.r, right: 30.r),
                child: Image.asset(currentLanguageIsSimpleChinese()
                    ? R.iconCreateNoteWords2
                    : R.iconCreateNoteWords2En),
              ),
              SizedBox(
                height: 35.h,
              ),
              Expanded(
                child: ListView(
                  children: [
                    Text(
                      L.mnemonic_view_hint.tr,
                      style: TextStyle(
                          color: AppColors.colorFF333333, fontSize: 14.sp),
                    ),
                    SizedBox(
                      height: 40.h,
                    ),
                    RepaintBoundary(
                      key: controller.key,
                      child: Container(
                        color: AppColors.white,
                        child:Column(
                          mainAxisSize: MainAxisSize.min,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Container(
                              padding: const EdgeInsets.only(
                                  top: 15, bottom: 15, left: 8, right: 8)
                                  .r,
                              alignment: Alignment.center,
                              decoration: BoxDecoration(
                                color: AppColors.white,
                                borderRadius:
                                const BorderRadius.all(Radius.circular(5)).r,
                                border: Border.all(
                                  color: AppColors.colorFFF8F8F8,
                                  width: 1.w,
                                ),
                              ),
                              width: double.infinity,
                              child: GridView.count(
                                physics: const NeverScrollableScrollPhysics(),
                                shrinkWrap: true,
                                crossAxisCount: 3,
                                mainAxisSpacing: 15,
                                crossAxisSpacing: 20,
                                childAspectRatio: 1 / 0.35,
                                children: controller.mnemonic == null
                                    ? const <Widget>[]
                                    : _buildMnemonicItemList(
                                    (controller.mnemonic!.split(" "))),
                              ),
                            ),
                            SizedBox(height: 10.h,),
                            Obx(
                              () => MiddleText(
                                controller.nickName.value,
                                WXTextOverflow.ellipsisMiddle,
                                style: TextStyle(
                                  fontSize: 12.sp,
                                  color: AppColors.colorFF333333,
                                ),
                              ),
                            ),
                            SizedBox(
                              height: 10.h,
                            ),
                            Obx(
                                  () => MiddleText(
                                controller.userName.value,
                                WXTextOverflow.ellipsisMiddle,
                                style: TextStyle(
                                  fontSize: 12.sp,
                                  color: AppColors.colorFF333333,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(
                height: 20.r,
              ),
              Row(
                children: [
                  Expanded(
                    child: GestureDetector(
                      onTap: () async {
                        controller.copy();
                      },
                      child: Container(
                        height: 39.r,
                        margin: EdgeInsets.only(right: 5.r),
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(5.r),
                            color: AppColors.backgroundGray),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Image.asset(R.iconCopyWord,
                                height: 12.5.r, width: 12.5.r),
                            SizedBox(
                              width: 5.r,
                            ),
                            Text(
                              L.other_copy_word.tr,
                              style: TextStyle(
                                  fontSize: 16.sp, color: AppColors.appDefault),
                            )
                          ],
                        ),
                      ),
                    ),
                  ),
                  Expanded(
                    child: GestureDetector(
                      onTap: () async {
                        controller.save(context);
                      },
                      child: Container(
                        margin: EdgeInsets.only(left: 5.r),
                        height: 39.r,
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(5.r),
                            color: AppColors.backgroundGray),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Image.asset(
                              R.iconSaveWord,
                              height: 12.5.r,
                              width: 12.5.r,
                            ),
                            SizedBox(
                              width: 5.r,
                            ),
                            Text(
                              L.save_to_local.tr,
                              style: TextStyle(fontSize: 16.sp),
                            )
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              Visibility(
                visible: controller.mnemonicViewType ==
                    MnemonicViewType.registered,
                child: SizedBox(
                  height: 30.r,
                ),
              ),
              Visibility(
                visible: controller.mnemonicViewType ==
                    MnemonicViewType.registered,
                child: ElevatedButton(
                  onPressed: (){
                    controller.onMobileVerify();
                  },
                  child: Text(
                    L.next_step.tr,
                    style: TextStyle(fontSize: 16.sp),
                  ),
                ),
              ),
              const SizedBox(height: 5),

              SizedBox(
                height: 50.r,
              ),
                ],
      
              ),);
        }
          }
      ),
    );
  }

Container _buildMnemonicItem(String word, int index) => Container(
      decoration: BoxDecoration(
        color: AppColors.backgroundGray,
        borderRadius: const BorderRadius.all(Radius.circular(5)).r,
      ),
      padding: const EdgeInsets.all(5).r,
      height: 29.h,
      width: 95.w,
      alignment: Alignment.center,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Expanded(
              child: Center(
            child: Text(
              word,
              style:
                  TextStyle(color: AppColors.colorFF333333, fontSize: 14.sp),
            ),
          )),
          Text(
            "$index",
            style: TextStyle(color: Colors.grey, fontSize: 10.sp),
          )
        ],
      ),
    );

List<Widget> _buildMnemonicItemList(List<String> items) {
  List<Widget> widgets = [];
  for (int i = 0; i < items.length; i++) {
    Widget widget = _buildMnemonicItem(items[i], i + 1);
    widgets.add(widget);
  }
  return widgets;
}
}
