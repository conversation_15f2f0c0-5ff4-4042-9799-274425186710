import 'package:flutter/material.dart';
import 'package:flutter_metatel/core/values/colors.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../../core/languages/l.dart';
import '../../../../../core/utils/util.dart';
import '../../../../widgets/app_bar_cus.dart';
import 'mnemonic_restore_controller.dart';

class MnemonicRestorePage extends StatefulWidget {
  const MnemonicRestorePage({Key? key}) : super(key: key);
  @override
  State<StatefulWidget> createState() =>_MnemonicRestorePage();

}

class _MnemonicRestorePage extends State<MnemonicRestorePage> {
  final MnemonicRestoreController controller =
  Get.put(MnemonicRestoreController());
  final ThrottleUtil throttleUtil = ThrottleUtil();

  @override
  void dispose() {
    Get.delete<MnemonicRestoreController>();
    super.dispose();
  }
  @override
  Widget build(BuildContext context) {
    double bottom = MediaQuery
        .of(context)
        .viewInsets
        .bottom;
    controller.bottomHeightChange(context);
    controller.margeBottom.value=bottom<controller.margeBottom.value?(controller.margeBottom.value-bottom):(bottom==0?50.h:0);
    return Scaffold(
      appBar: AppBarCommon().build(
        context,
        title: L.restore_account.tr,
      ),
      body: Column(
        children: [
          Expanded(
            child: Container(
              color: Theme.of(context).primaryColor,
              padding: const EdgeInsets.only(
                  left: 20, top: 20, right: 20, bottom: 0),
              child: LayoutBuilder(
                builder:(BuildContext context, BoxConstraints constraints){
                  return SingleChildScrollView(
                    child: ConstrainedBox(
                      constraints: constraints.copyWith(
                          minHeight: constraints.maxHeight,
                          maxHeight: double.infinity),
                      child: IntrinsicHeight(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const SizedBox(
                              height: 20,
                            ),
                            Text(
                              L.please_enter_mnemonic_words_separated_by_spaces.tr,
                              style: const TextStyle(color: AppColors.colorFF666666),
                            ),
                            const SizedBox(
                              height: 18,
                            ),
                            Container(
                              padding: const EdgeInsets.only(left: 10, right: 10),
                              constraints: const BoxConstraints(minHeight: 200),
                              decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(12),
                                  border: Border.all(color: AppColors.colorFFF8F8F8, width: 1)),
                              child: TextField(
                                controller: controller.textEditControllerMnemonic,
                                textAlignVertical: TextAlignVertical.bottom,
                                keyboardType: TextInputType.multiline,
                                maxLines: null,
                                decoration: InputDecoration(
                                    hintMaxLines: 4,
                                    hintText:
                                    L.please_enter_mnemonic_words_separated_by_spaces.tr,
                                    border: InputBorder.none,
                                    hintStyle: const TextStyle(color: AppColors.colorFFB3B3B3)),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
          ),
          ElevatedButton(
            onPressed: throttleUtil.throttle(() async {
              FocusScope.of(context).requestFocus(FocusNode());
              controller.onRestore(controller.textEditControllerMnemonic.text);
            }),
            child: Text(
              L.restore_identity.tr,
              style: TextStyle(fontSize: 16.sp),
            ),
          ),
          Obx(() => SizedBox(height:controller.margeBottom.value,)),
        ],
      ),
    );
  }

}

