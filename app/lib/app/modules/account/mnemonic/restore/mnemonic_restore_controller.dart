import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter_metatel/app/data/events/events.dart';
import 'package:flutter_metatel/app/data/providers/api/operation_center_api.dart';
import 'package:flutter_metatel/app/data/services/network_connect_service.dart';
import 'package:flutter_metatel/core/task/private_task.dart';
import 'package:flutter_metatel/core/utils/events_bus.dart';
import 'package:flutter_metatel/core/utils/keboard_util.dart';
import 'package:flutter_metatel/core/values/config.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../../core/languages/l.dart';
import '../../../../../core/utils/app_log.dart';
import '../../../../../core/utils/util.dart';
import '../../../../data/models/node_model.dart';
import '../../../../data/providers/api/api.dart';
import '../../../../data/services/config_service.dart';
import '../../account_service.dart';

class MnemonicRestoreController extends GetxController {
  final TextEditingController _textEditControllerMnemonic =
      TextEditingController();
  String? mnemonic;

  TextEditingController get textEditControllerMnemonic =>
      _textEditControllerMnemonic;
  RxDouble margeBottom = (50.h).obs;
  Timer? timer;
  KeyBoardUtil keyBoardUtil = KeyBoardUtil();
  StreamSubscription? subscription;
  final List<StreamSubscription> _subscriptions = [];

  @override
  void onInit() {
    AppLogger.d("onInit restore mnnnn");
    subscription=Get.find<EventBus>().on<MnemonicRestoreReplaceContinueEvent>().listen((event) {
      if(mnemonic!=null){
        showLoadingDialog(isBack: false);
        Get.find<AccountService>().login(mnemonic!,action: 1);
      }
    });
    _subscriptions.add(subscription!);
    Config.privateKeyBase64 = "";
    super.onInit();
  }

  @override
  void onClose() {
    AppLogger.d("onClose");
    for (var element in _subscriptions) {
      element.cancel();
    }
    _subscriptions.clear();
    super.onClose();
  }

  //恢复身份
  onRestore(String mnemonic) async {
    if (!await Get.find<NetWorkConnectService>().networkConnected()) {
      toast(L.network_unavailable.tr);
      // return;
    }
    showLoadingDialog(isBack: false);
    mnemonic = processMnemonic(mnemonic);
    bool isLegality = mnemonicLegality(mnemonic.split(" "));
    if (!isLegality) {
      toast(
          L.the_mnemonic_is_invalid_please_check_it_carefully_and_try_again.tr);
      dismissLoadingDialog();
      return;
    }
    Get.find<AccountService>().mnemonic = mnemonic;
    await PrivateConfigTask().configPrivateKeyFromMnemonic(mnemonic: mnemonic);
    if (Get.find<AppConfigService>().getUserNameWithoutDomain().isEmpty) {
      AppLogger.e("mnemonic restore userName is Empty");
      dismissLoadingDialog();
      return;
    }
    var response = await Get.find<OperationCenterApiProvider>().getComNode(
        account: Get.find<AppConfigService>().getUserNameWithoutDomain());
    if (response.statusCode != 200) {
      dismissLoadingDialog();
      return;
    }
    int? code = response.data?.code;
    String? msg = response.data?.message;
    NodeModelData? body = response.data?.nodeModelData;
    if (code != 200 || body == null) {
      dismissLoadingDialog();
      toast(msg ?? L.unknown.tr);
    } else {
      var node = body.node;
      if (node == null) {
        dismissLoadingDialog();
        toast(L.no_relevant_node_data.tr);
      } else if (node.publicKey == null && node.port == 0) {
        dismissLoadingDialog();
        toast(L
            .sorry_the_current_node_is_not_on_the_chain_please_try_again_later
            .tr);
      } else {
        Get.find<AppConfigService>().saveNode(node.node);
        Get.find<AppConfigService>().saveNodePort('${node.port}');
        this.mnemonic = mnemonic;
        Get.find<AccountService>().login(mnemonic);
      }
    }
  }

  bottomHeightChange(BuildContext context) {
    keyBoardUtil.startListener(context);
  }
}
