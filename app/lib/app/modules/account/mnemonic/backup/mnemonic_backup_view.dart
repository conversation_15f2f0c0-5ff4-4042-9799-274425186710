import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/modules/account/mnemonic/backup/mnemonic_backup_controller.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../../core/languages/l.dart';
import '../../../../../core/values/colors.dart';
import '../../../../../r.dart';
import '../../../../widgets/app_bar_cus.dart';

class MnemonicBackupView extends GetView<MnemonicBackupController> {
  const MnemonicBackupView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBarCommon().build(context, title: L.backup_mnemonic.tr),
      body: Container(
        color: Theme.of(context).primaryColor,
        padding:
        const EdgeInsets.only(left: 16, top: 16, right: 20, bottom: 0).r,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [           
            Expanded(
              child: ListView(
                physics: const BouncingScrollPhysics(),
                children: [               
                  RepaintBoundary(
                    key: controller.key,
                    child: Container(
                      color: AppColors.white,
                      child:Column(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Container(
                            height: 230.r,
                            padding: const EdgeInsets.only(
                                top: 15, bottom: 15, left: 8, right: 8)
                                .r,
                            alignment: Alignment.center,
                            decoration: BoxDecoration(
                              color: AppColors.white,
                              borderRadius:
                              const BorderRadius.all(Radius.circular(5)).r,
                              border: Border.all(
                                color: AppColors.colorFFF8F8F8,
                                width: 1.w,
                              ),
                            ),
                            width: double.infinity,
                            child: LayoutBuilder(
                              builder: (context, constraints) {
                                // 获取父 Widget 的最大宽度和高度
                                double parentWidth = constraints.maxWidth;
                                double parentHeight = constraints.maxHeight;
                                return GridView.count(
                                  physics: const NeverScrollableScrollPhysics(),
                                  shrinkWrap: true,
                                  crossAxisCount: 3,
                                  mainAxisSpacing: 15,
                                  crossAxisSpacing: 20,
                                  // childAspectRatio: 1 / 0.35,
                                  childAspectRatio: ((parentWidth - 40)/3) / ((parentHeight - 45)/4),
                                  children: controller.mnemonic == null
                                      ? const <Widget>[]
                                      : _buildMnemonicItemList(
                                      (controller.mnemonic!.split(" "))),
                                );
                              }
                            ),
                          ),                          
                        ],
                      ),
                    ),
                  ),
                  SizedBox(
                    height: 20.h,
                  ),
                  Text(
                    L.mnemonic_view_hint.tr,
                    style: TextStyle(
                        color: AppColors.colorFF333333, fontSize: 14.sp),
                  ), 
                  SizedBox(
                    height: 25.h,
                  ),
                  Row(
                    children: [
                      Expanded(
                        child: GestureDetector(
                          onTap: () async {
                            controller.copy();
                          },
                          child: Container(
                            height: 39.r,
                            margin: EdgeInsets.only(right: 5.r),
                            decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(5.r),
                                color: AppColors.backgroundGray),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Image.asset(R.iconCopyWordFill,
                                    height: 16.r, width: 16.r),
                                SizedBox(
                                  width: 5.r,
                                ),
                                Text(
                                  L.other_copy_word.tr,
                                  style: TextStyle(
                                      fontSize: 15.sp, color: AppColors.appDefault),
                                )
                              ],
                            ),
                          ),
                        ),
                      ),
                      Expanded(
                        child: GestureDetector(
                          onTap: () async {
                            controller.save(context);
                          },
                          child: Container(
                            margin: EdgeInsets.only(left: 5.r),
                            height: 39.r,
                            decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(5.r),
                                color: AppColors.backgroundGray),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Image.asset(
                                  R.iconSaveWordFill,
                                  height: 16.r,
                                  width: 16.r,
                                ),
                                SizedBox(
                                  width: 5.r,
                                ),
                                Text(
                                  L.save_to_local.tr,
                                  style: TextStyle(fontSize: 15.sp),
                                )
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                  SizedBox(
                    height: 20.r,
                  ),
                  ElevatedButton(
                    onPressed: () {
                      controller.onVerifyBackupTap();
                    },
                    child: Text(
                      L.verify_backup.tr,
                      style: TextStyle(fontSize: 16.sp),
                    ),
                  ),
                  SizedBox(
                    height: 50.r,
                  ),                    
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Container _buildMnemonicItem(String word, int index) => Container(
    decoration: BoxDecoration(
      color: AppColors.backgroundGray,
      borderRadius: const BorderRadius.all(Radius.circular(5)).r,
    ),
    padding: const EdgeInsets.all(5).r,
    height: 29.h,
    width: 95.w,
    alignment: Alignment.center,
    child: Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Expanded(
            child: Center(
              child: Text(
                word,
                style:
                TextStyle(color: AppColors.colorFF333333, fontSize: 14.sp),
              ),
            )),
        Text(
          "$index",
          style: TextStyle(color: Colors.grey, fontSize: 10.sp),
        )
      ],
    ),
  );

  List<Widget> _buildMnemonicItemList(List<String> items) {
    List<Widget> widgets = [];
    for (int i = 0; i < items.length; i++) {
      Widget widget = _buildMnemonicItem(items[i], i + 1);
      widgets.add(widget);
    }
    return widgets;
  }
}