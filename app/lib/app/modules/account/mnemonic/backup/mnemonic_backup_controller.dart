import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../../core/languages/l.dart';
import '../../../../../core/task/private_task.dart';
import '../../../../../core/task/time_task.dart';
import '../../../../../core/utils/app_log.dart';
import '../../../../../core/utils/save_image_to_photo.dart';
import '../../../../../core/utils/util.dart';
import '../../../../../core/values/config.dart';
import '../../../../../routes/pages.dart';
import '../../account_service.dart';

class MnemonicBackupController extends GetxController {

  String? mnemonic;
  ByteData? imageBytes;
  GlobalKey key = GlobalKey();

  @override
  void onClose() {
    super.onClose();
  }

  @override
  void onInit() {
    var map = Get.arguments;
    AppLogger.d("Get.arguments.runtimeType==${Get.arguments.runtimeType}");
    mnemonic = map[AccountService.mnemonicTAG];
    Get.find<AccountService>().mnemonic = mnemonic;
    AppLogger.d("Get.arguments.mnemonic==$mnemonic");
    super.onInit();
  }

  @override
  void onReady() async {
    await PrivateConfigTask().configPrivateKeyFromMnemonic(mnemonic: mnemonic);
    imageBytes = await RepaintBoundaryUtils.savePhotoByGlobalKey(key);
    super.onReady();
  }

  void copy() {
    saveClipboard(mnemonic);
    AppLogger.d('copy mnemonic=$mnemonic');
    toast(L.copy_success.tr);
  }

  void save(BuildContext context) async {
    imageBytes ??= await RepaintBoundaryUtils.savePhotoByGlobalKey(key);
    var millisecondsSinceEpoch = TimeTask.instance.getNowDateTime().millisecondsSinceEpoch;
    String name = "${L.app_name.tr}-${L.mnemonic.tr}-$millisecondsSinceEpoch";
    RepaintBoundaryUtils.savePhoto(imageBytes,
        name: name,
        toastSuccess: L.save_the_mnemonic_successfully.tr,
        toastError: L.save_the_mnemonic_failed.tr);
  }

  void onVerifyBackupTap() async{
    Map<String, dynamic> map = {};
    map[AccountService.mnemonicTAG] = Config.mnemonic;
    bool verifySuccess = await Get.toNamed(Routes.MnemonicVerifyPage,arguments: map) as bool? ?? false;
    if(verifySuccess) {
      Get.back(result: true);
    }
  }
}