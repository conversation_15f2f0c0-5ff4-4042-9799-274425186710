import 'package:async_task/async_task_extension.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_metatel/core/languages/l.dart';
import 'package:flutter_metatel/core/utils/util.dart';
import 'package:flutter_metatel/core/values/colors.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../data/models/mobile_verify_model.dart';
import 'phone_controller.dart';

class PhonePage extends StatefulWidget {
  const PhonePage({
    super.key,
    required this.data,
  });

  final MobileVerifyModel data;

  @override
  State<PhonePage> createState() => _PhonePageState();
}

class _PhonePageState extends State<PhonePage> {
  final _controller = Get.put(PhoneController());
  final ValueNotifier<int> _countdown = ValueNotifier<int>(0);
  Timer? _timer;

  @override
  void initState() {
    super.initState();
    _controller.mobileData = widget.data;
  }

  @override
  void dispose() {
    _countdown.dispose();
    _timer?.cancel();
    Get.delete<PhoneController>();
    super.dispose();
  }

  void _onGetCode() {
    var phone = _controller.phoneController.text.trim();
    if (phone.isEmpty) {
      toast(L.phone_not_empty.tr);
      return;
    }

    if (phone.length != 11) {
      toast(L.phone_format_war.tr);
      return;
    }

    _timer?.cancel();
    _controller.onCode();
    _countdown.value = 60;
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      --_countdown.value;
      if (_countdown.value < 1) {
        _timer?.cancel();
      }
    });
  }

  Widget _buildTextField({
    String? hintText,
    List<TextInputFormatter>? inputFormatters,
    int? maxLength,
    TextEditingController? controller,
    Widget? suffixIcon,
    TextAlignVertical? textAlignVertical,
  }) {
    return TextField(
      controller: controller,
      inputFormatters: inputFormatters,
      maxLength: maxLength,
      keyboardType: TextInputType.number,
      textAlignVertical: textAlignVertical,
      decoration: InputDecoration(
        contentPadding: EdgeInsets.zero,
        hintText: hintText,
        hintStyle: TextStyle(
          fontSize: 16.sp,
          color: AppColors.colorFF999999,
        ),
        counterText: '',
        suffixIcon: suffixIcon,
        focusedBorder: const UnderlineInputBorder(
          borderSide: BorderSide(color: Colors.blue),
        ),
        enabledBorder: const UnderlineInputBorder(
          borderSide: BorderSide(color: AppColors.colorFFF8F8F8),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: const Text(''),
        backgroundColor: Colors.white,
      ),
      body: ListView(
        padding: EdgeInsets.symmetric(horizontal: 16.w),
        children: [
          SizedBox(height: 25.h),
          Text(
            L.create_account_r.tr,
            style: TextStyle(
              color: AppColors.colorFF333333,
              fontSize: 24.sp,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 45.h),
          _buildTextField(
            hintText: L.please_phone_hint.tr,
            maxLength: 11,
            controller: _controller.phoneController,
            textAlignVertical: TextAlignVertical.center,
            inputFormatters: [
              FilteringTextInputFormatter.allow(RegExp('[0-9]')),
            ],
            suffixIcon: ValueListenableBuilder<int>(
              valueListenable: _countdown,
              builder: (ct, value, child) {
                bool enable = false;
                String text = '${L.reacquire.tr}(${value}s)';

                if (value <= 0) {
                  text = L.get_verifycode.tr;
                  enable = true;
                }

                return TextButton(
                  onPressed: enable ? _onGetCode : null,
                  child: Text(
                    text,
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: enable ? AppColors.colorFF4863F0 : Colors.grey,
                    ),
                  ),
                );
              },
            ),
          ),
          SizedBox(height: 20.h),
          _buildTextField(
            hintText: L.hint_verifycode.tr,
            maxLength: 6,
            controller: _controller.codeController,
            inputFormatters: [
              FilteringTextInputFormatter.allow(RegExp('[0-9]')),
            ],
          ),
          SizedBox(height: 50.h),
          Obx(() {
            return ElevatedButton(
              onPressed: _controller.rxButtonEnable.value
                  ? _controller.onConfirm
                  : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.colorFF4863F0,
                minimumSize: Size(double.infinity, 39.h),
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(5.r)),
              ),
              child: Text(
                L.backup_confirm.tr,
                style: TextStyle(
                  fontSize: 16.sp,
                  color: Colors.white,
                ),
              ),
            );
          }),
        ],
      ),
    );
  }
}
