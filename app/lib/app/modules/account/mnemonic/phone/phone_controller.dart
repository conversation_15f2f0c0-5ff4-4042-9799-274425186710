import 'package:flutter/widgets.dart';
import 'package:flutter_metatel/app/data/providers/api/operation_center_api.dart';
import 'package:get/get.dart';

import '../../../../../core/utils/util.dart';
import '../../../../../core/values/code.dart';
import '../../../../../core/values/config.dart';
import '../../../../data/models/mobile_verify_model.dart';
import '../../../../data/providers/api/api.dart';
import '../../../../data/services/config_service.dart';

class VerifyAction {
  static const String request = 'request';
  static const String verify = 'verify';
}

class PhoneController extends GetxController {
  final TextEditingController phoneController = TextEditingController();
  final TextEditingController codeController = TextEditingController();

  final rxButtonEnable = false.obs;

  late MobileVerifyModel _data;
  set mobileData(MobileVerifyModel data) => _data = data;

  @override
  void onInit() {
    super.onInit();

    phoneController.addListener(_isEnable);
    codeController.addListener(_isEnable);
  }

  @override
  void onClose() {
    phoneController.dispose();
    codeController.dispose();
    super.onClose();
  }

  void onConfirm() async {
    showLoadingDialog( isBack: false);
    var result = await _verify(VerifyAction.verify, code: codeController.text);
    dismissLoadingDialog();
    if (result) {
      Get.back(result: StrKey.mobileSuccess);
    }
  }

  void onCode() {
    _verify(VerifyAction.request);
  }

  void _isEnable() {
    var phone = phoneController.text;
    var code = codeController.text;

    if (phone.trim().isNotEmpty && code.trim().isNotEmpty) {
      rxButtonEnable.value = true;
    } else {
      rxButtonEnable.value = false;
    }
  }

  Future<bool> _verify(String action, {String code = ''}) async {
    var phone = phoneController.text;

    var account = await Get.find<AppConfigService>().getUserNameWithoutDomain();
    var response = await Get.find<OperationCenterApiProvider>().mobileVerify(
      _data.verifyurl ?? '',
      _data.keyboxid ?? '',
      _data.signature ?? '',
      account ?? '',
      _data.timestamp ?? 0,
      action,
      phone,
      code,
    );

    if (response.statusCode != Code.code200) {
      toast('code:${response.statusCode}');
      return false;
    }

    if (response.data?.code != VerifyCode.code0000) {
      toast('code:${response.data?.code} msg:${response.data?.msg}');
      return false;
    }

    return true;
  }
}
