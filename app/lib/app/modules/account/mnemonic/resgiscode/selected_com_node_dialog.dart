import 'package:flutter/material.dart';
import 'package:flutter_metatel/core/languages/l.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../../core/values/colors.dart';
import '../../../../../r.dart';
import '../../../../data/models/node_model.dart';
import '../../../../widgets/divider_cus.dart';
import 'input_reg_code_controller.dart';

extension ListComNodeSelectExtension on RxList<ComNode> {
  void setSelected(ComNode node) {
    for (ComNode c in this) {
      if (c != node) {
        c.isSelect = false;
      } else {
        c.isSelect = true;
      }
    }
  }
}

class SelectComNodeDialog extends StatefulWidget {
  const SelectComNodeDialog({
    Key? key,
    required this.comNodePublicList,
    required this.comNodePrivateList,
    required this.defaultNode,
  }) : super(key: key);

  final RxList<ComNode> comNodePublicList;
  final RxList<ComNode> comNodePrivateList;
  final ComNode? defaultNode;

  @override
  State<StatefulWidget> createState() => _SelectComNodeDialogState();
}

class _SelectComNodeDialogState extends State<SelectComNodeDialog> {
  ComNode? selectComNode;

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Container(
        decoration: BoxDecoration(
          borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(16.0), topRight: Radius.circular(16.0))
              .r,
          color: Theme.of(context).primaryColor,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Container(
              margin: const EdgeInsets.only(top: 14).r,
              alignment: Alignment.center,
              child: Text(
                L.node_selection.tr,
                style: TextStyle(
                    fontSize: 18.sp,
                    color: AppColors.colorFF333333,
                    fontWeight: FontWeight.bold),
              ),
            ),
            SizedBox(height: 18.h),
            DividerCus(indent: 16.w, endIndent: 16.w),
            SizedBox(height: 18.h),
            Obx(() => Visibility(
              visible: widget.comNodePublicList.isNotEmpty,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Container(
                    padding: const EdgeInsets.only(left: 16).r,
                    width: double.infinity,
                    alignment: Alignment.centerLeft,
                    child: Text(
                      L.public_node.tr,
                      style: TextStyle(
                          fontSize: 14.sp, color: AppColors.colorFF666666),
                    ),
                  ),
                  SizedBox(height: 15.h),
                  ListView.builder(
                      shrinkWrap: true,
                      itemCount: widget.comNodePublicList.length,
                      itemBuilder: (BuildContext context, int index) {
                        var model = widget.comNodePublicList[index];
                        return _buildNodeItemWidget(model);
                      }),
                ],
              ),
            )),
            SizedBox(height: 18.h),
            Obx(() => Visibility(
              visible: widget.comNodePrivateList.isNotEmpty,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Container(
                    padding: const EdgeInsets.only(left: 16).r,
                    width: double.infinity,
                    alignment: Alignment.centerLeft,
                    child: Text(
                      L.private_node.tr,
                      style: TextStyle(
                          fontSize: 14.sp, color: AppColors.colorFF666666),
                    ),
                  ),
                  SizedBox(height: 15.h),
                  ListView.builder(
                      shrinkWrap: true,
                      itemCount: widget.comNodePrivateList.length,
                      itemBuilder: (BuildContext context, int index) {
                        var model = widget.comNodePrivateList[index];
                        return _buildNodeItemWidget(model);
                      }),
                ],
              ),
            )),
            ElevatedButton(
              onPressed: () {
                selectComNode ??= widget.defaultNode;
                Get.back(result: selectComNode);
              },
              child: Text(L.confirm.tr),
            ),
            SizedBox(height: 20.h),
          ],
        ),
      ),
    );
  }

  Widget _buildNodeItemWidget(ComNode node) {
    return Container(
      color: node.isSelect ? AppColors.colorFFEBECF2 : Colors.white,
      alignment: Alignment.center,
      padding: const EdgeInsets.only(left: 16, right: 16, top: 5, bottom: 5).r,
      child: InkWell(
        onTap: () {
          setState(() {
            node.isSelect = true;
            widget.comNodePrivateList.setSelected(node);
            widget.comNodePublicList.setSelected(node);
            selectComNode = node;
          });
        },
        child: Row(
          children: [
            Image.asset(
              node.isSelect ? R.icoComNodeSelected : R.icoComNodeDefault,
              width: 36.h,
              height: 36.h,
            ),
            SizedBox(width: 10.w),
            Text(
              node.node ?? "",
              style: TextStyle(fontSize: 16.sp, color: AppColors.colorFF666666),
            ),
          ],
        ),
      ),
    );
  }
}
