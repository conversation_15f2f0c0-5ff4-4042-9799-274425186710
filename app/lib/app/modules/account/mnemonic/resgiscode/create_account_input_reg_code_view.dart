/*
 * <AUTHOR> <PERSON><PERSON>
 * @Date         : 2022-06-16 16:44:27
 * @Description  : 
 * @LastEditors  : <PERSON><PERSON>
 * @LastEditTime : 2022-06-16 17:07:23
 * @FilePath     : /flutter_metatel/lib/app/modules/account/main/create_account_input_reg_code_view.dart
 */
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_metatel/app/widgets/app_bar_cus.dart';
import 'package:flutter_metatel/core/values/colors.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../../core/languages/l.dart';
import '../../../../../core/utils/app_log.dart';
import '../../../../../core/utils/util.dart';
import 'input_reg_code_controller.dart';
class CreateAccountInputRegisterCodePage extends StatefulWidget {
  const CreateAccountInputRegisterCodePage({Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() =>_CreateAccountInputRegisterCodePage();
}
class _CreateAccountInputRegisterCodePage extends State<CreateAccountInputRegisterCodePage> {
  final TextEditingController _textEditControllerRegisterCode =
      TextEditingController();

  final InputCodeController controller = Get.put(InputCodeController());
  final ThrottleUtil throttleUtil = ThrottleUtil();

  @override
  void dispose() {
    // TODO: implement dispose
    Get.delete<InputCodeController>();
    super.dispose();
  }
  @override
  Widget build(BuildContext context) {
    double bottom = MediaQuery
        .of(context)
        .viewInsets
        .bottom;
    controller.bottomHeightChange(context);
    controller.margeBottom.value=bottom<controller.margeBottom.value?(controller.margeBottom.value-bottom):(bottom==0?67.h:0);
    return Scaffold(
      appBar: AppBarCommon().build(context, title: L.enter_the_authorization_code.tr),
      body:Column(
        children: [
          Expanded(
            child: Container(
              color: Theme.of(context).primaryColor,
              padding:
                  const EdgeInsets.only(left: 20, top: 20, right: 20, bottom: 0)
                      .r,
              child: LayoutBuilder(
                builder: (BuildContext context, BoxConstraints constraints) {
                  return SingleChildScrollView(
                    child: ConstrainedBox(
                      constraints: constraints.copyWith(
                          minHeight: constraints.maxHeight,
                          maxHeight: double.infinity),
                      child: IntrinsicHeight(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            SizedBox(
                              height: 25.h,
                            ),
                            Text(
                              L.verify_credentials_hint.tr,
                              style: const TextStyle(
                                  color: AppColors.colorFF666666),
                            ),
                            SizedBox(
                              height: 30.h,
                            ),
                            Container(
                              constraints: BoxConstraints(maxHeight: 150.h),
                              padding: const EdgeInsets.only(
                                      left: 10, right: 10, top: 0, bottom: 0)
                                  .r,
                              width: double.infinity,
                              height: 150.h,
                              decoration: BoxDecoration(
                                  borderRadius: BorderRadius.all(
                                          const Radius.circular(12).r)
                                      .r,
                                  border: Border.all(
                                      color: AppColors.colorFFF8F8F8,
                                      width: 1)),
                              child: TextField(
                                controller: _textEditControllerRegisterCode,
                                textAlignVertical: TextAlignVertical.bottom,
                                inputFormatters: [
                                  FilteringTextInputFormatter.allow(RegExp("[a-z,A-Z,0-9]")),//只能输入汉字或者字母或数字//最大长度
                                ],
                                keyboardType:TextInputType.multiline,
                                maxLines: null,
                                decoration: InputDecoration(
                                    hintText: L
                                        .please_enter_the_identity_credentials
                                        .tr,
                                    hintMaxLines: 4,
                                    hintStyle: const TextStyle(
                                        fontSize: 16,
                                        color: AppColors.colorFFB3B3B3),
                                    border: InputBorder.none),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
          ),
          ElevatedButton(
            onPressed: throttleUtil.throttle(() async {
              if(_textEditControllerRegisterCode.text.isEmpty){
                toast(L.code_not_empty.tr);
                return;
              }
              if (controller.mnemonic == null) {
                AppLogger.d("无助记词");
              } else {
                FocusScope.of(context).requestFocus(FocusNode());
                await const Duration(milliseconds: 200).delay();
                controller.onVerify(registerCode:_textEditControllerRegisterCode.text);
              }
            }),
            child: Text(
              L.next_step.tr,
            ),
          ),
          // SizedBox(height: 20.h,),
          // ElevatedButton(
          //   onPressed: throttleUtil.throttle(() async {
          //     if (controller.mnemonic == null) {
          //       AppLogger.d("无助记词");
          //     } else {
          //       FocusScope.of(context).requestFocus(FocusNode());
          //       await const Duration(milliseconds: 200).delay();
          //       controller.onSkip(context);
          //
          //
          //
          //     }
          //   }),
          //   style: ButtonStyle(
          //       side: MaterialStateProperty.all(BorderSide(color: AppColors.colorFFE6E6E6, width: 0.5.w),),
          //       shape: MaterialStateProperty.all(
          //         RoundedRectangleBorder(
          //           borderRadius: BorderRadius.circular(20.0).r,
          //         ),
          //       ),
          //     fixedSize: MaterialStateProperty.all(
          //       Size(343.w, 44.h),
          //     ),
          //     backgroundColor: MaterialStateProperty.all(Colors.white),
          //   ),
          //   child: Text(
          //     L.skip.tr,
          //     style: const TextStyle(color: AppColors.colorFF333333),
          //   ),
          // ),
          Obx(() =>  SizedBox(height: controller.margeBottom.value,)),

        ],
      ),
    );
  }
}
