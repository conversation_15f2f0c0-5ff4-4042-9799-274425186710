/*
 * <AUTHOR> <PERSON><PERSON>
 * @Date         : 2022-06-14 19:49:40
 * @Description  : 助记词验证码页面
 * @LastEditors  : <PERSON><PERSON>
 * @LastEditTime : 2022-06-16 17:06:26
 * @FilePath     : /flutter_metatel/lib/app/modules/account/mnemonic/mnemonic_view_controller.dart
 */
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_metatel/core/utils/keboard_util.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import '../../../../../core/utils/app_log.dart';
import '../../account_service.dart';


class InputCodeController extends GetxController {
  String? mnemonic;
  ByteData? imageBytes;
  RxDouble margeBottom = (67.h).obs;
  KeyBoardUtil keyBoardUtil = KeyBoardUtil();


  @override
  void onInit() {
    var map = Get.parameters;
    var arguments = Get.arguments;
    mnemonic = map[AccountService.mnemonicTAG];
    AppLogger.d("onInit mnemonic==$mnemonic");
    imageBytes = arguments;
    AppLogger.d('Init mnemonic==$mnemonic imageBytes=$imageBytes');
    super.onInit();
  }
  @override
  void onClose() {
    keyBoardUtil.cancel();
    super.onClose();
  }
  bottomHeightChange(BuildContext context) {
    keyBoardUtil.startListener(context);
  }

  void onVerify({required String registerCode}) {
    if(mnemonic==null){
      return;
    }
    var accountService = Get.find<AccountService>();
    accountService.selectNode(
      registerCode: registerCode,
      callBack: () {
        accountService.login(mnemonic!,
            registerCode: registerCode, imageBytes: imageBytes);
      },
    );
  }
  // onSkip(BuildContext context) async {
  //   showLoadingDialog(, isBack: false);
  //   await configPrivateKeyFromMnemonic(mnemonic: mnemonic);
  //   String? userName =
  //       await Get.find<AppConfigService>().getUserNameWithoutDomain();
  //   if (userName == null) {
  //     dismissLoadingDialog();
  //     return;
  //   }
  //   Response<ComNodeModel> response =
  //       await Get.find<OperationCenterApiProvider>().getComNode();
  //   if (response.hasError) {
  //     dismissLoadingDialog();
  //     return;
  //   }
  //   int? code = response.body?.code;
  //   String? msg = response.body?.message;
  //   NodeModelData? body = response.body?.nodeModelData;
  //   if (code != 200 || body == null) {
  //     dismissLoadingDialog();
  //     toast(msg ?? L.unknown.tr);
  //   } else {
  //     var node = body.node;
  //     if (node == null) {
  //       dismissLoadingDialog();
  //       toast(L.no_relevant_node_data.tr);
  //     } else if (node.port == 0) {
  //       dismissLoadingDialog();
  //       toast(L
  //           .sorry_the_current_node_is_not_on_the_chain_please_try_again_later
  //           .tr);
  //     } else {
  //       Get.find<AppConfigService>().saveNode(node.node);
  //       Get.find<AppConfigService>().saveNodePort('${node.port}');
  //       AccountService().login(mnemonic!,
  //           imageBytes: imageBytes,
  //           registerCode:
  //               registerCode /*_textEditControllerRegisterCode.text*/);
  //     }
  //   }
  // }
  //


  // void _realNameVerify(RealNameVerifyEvent event) async {
  //   String username =
  //       await Get.find<AppConfigService>().getUserNameWithoutDomain() ?? '';

  //   bool success = RealNameVerify.mRealNameSuccess[username] ?? false;
  //   RegisterStatus status =
  //       RealNameVerify.mRealNameSuccessStatus[username] ?? RegisterStatus.none;

  //   register(RegisterStatus status) {
  //     String? text;
  //     switch (status) {
  //       case RegisterStatus.none:
  //         text = L.trying_register.tr;
  //         RealNameVerify.mRealNameSuccessStatus[username] =
  //             RegisterStatus.trying;
  //         break;
  //       case RegisterStatus.trying:
  //         text = L.trying_wait.tr;
  //         RealNameVerify.mRealNameSuccessStatus[username] =
  //             RegisterStatus.registering;
  //         break;
  //       case RegisterStatus.registering:
  //         RealNameVerify.mRealNameSuccessStatus[username] = RegisterStatus.none;
  //         toast(L.register_network_exception.tr);
  //         return;
  //     }

  //     showLoadingDialog( isBack: false, text: text);
  //     AppLogger.d("RegisterStatus ==$status");
  //     Future.delayed(Duration(seconds: Config.registerRetryWait), () {
  //       AccountService().login(
  //         mnemonic!,
  //         imageBytes: imageBytes,
  //         registerCode: registerCode,
  //       );
  //     });
  //   }

  //   if (success) {
  //     register(status);
  //   } else {
  //     var value = await Get.to(RealNameAuthenticatePage(
  //       data: event.data,
  //       waitSeconds: Config.registerRetryWait,
  //     ));

  //     if (StrKey.realNameSuccess == value) {
  //       RealNameVerify.mRealNameSuccess[username] = true;
  //       register(RegisterStatus.none);
  //     }
  //   }
  // }


}
