import 'package:flutter/material.dart';
import '../../../../r.dart';

class SwitchTabWidget extends StatefulWidget {
  const SwitchTabWidget({Key? key, required this.tab1,required this.tab2}) : super(key: key);
  final String tab1;
  final String tab2;
  @override
  State<StatefulWidget> createState() => _SwitchTabWidget();
}

class _SwitchTabWidget extends State<SwitchTabWidget> {
  late String selectText;
  @override
  Widget build(BuildContext context) {
    selectText=widget.tab2;
    return Row(
      children: [
        // GestureDetector(
        //   child: Text("私钥",style: selectText!="私钥"?const TextStyle(fontSize: 16,color: Colors.grey):const TextStyle(fontSize: 20,color: Colors.white),),
        //   onTap:(){
        //     setState(() {
        //       selectText="私钥";
        //     });
        //   } ,
        // ),
        // const SizedBox(
        //   width: 80,
        // ),
        GestureDetector(
          child: Text(widget.tab2,style: selectText!=widget.tab2?const TextStyle(fontSize: 16,color: Colors.grey):const TextStyle(fontSize: 20,color: Colors.white),),
          onTap:(){
            setState(() {
              selectText=widget.tab2;
            });
          }
        ),
        const Spacer(),
        Image.asset(R.mainSettingIcon)
      ],
    );
  }
}
