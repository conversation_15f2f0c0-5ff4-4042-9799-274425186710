import 'dart:typed_data';

import 'package:flutter_metatel/core/languages/l.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:get/get.dart';

import '../../../../../core/utils/util.dart';
import '../../../../data/models/mnemonic_verify_source_model.dart';
import '../../account_service.dart';

class MnemonicVerifyController extends GetxController {
  List<String> sourceMnemonicList = [];
  RxList<MnemonicVerifySourceModel> mnemonicData=<MnemonicVerifySourceModel>[].obs;
  RxList<MnemonicVerifySourceModel> mnemonicSelectData=<MnemonicVerifySourceModel>[].obs;

  ByteData? mnemonicImage;

  @override
  void onInit() {
    var map = Get.arguments;
    var strMnemonic = map[AccountService.mnemonicTAG];
    if(strMnemonic!=null){
      sourceMnemonicList = strMnemonic.split(" ");
      List<MnemonicVerifySourceModel> m = [];
      for (int i = 0; i < sourceMnemonicList.length; i++) {
        var el = sourceMnemonicList[i];
        m.add(MnemonicVerifySourceModel(el, i + 1));
      }
      m.shuffle();
      mnemonicData.value = m;
    }
    super.onInit();
  }


  void clickWord(index){
    if(isSelectedMnemonicIncorrectExist() && !mnemonicData[index].isSelected){
      return;
    }
    var data = mnemonicData[index];
    data.isSelected=!(data.isSelected);
    mnemonicData[index]=data;
    if(mnemonicData[index].isSelected){
      mnemonicSelectData.add(
        MnemonicVerifySourceModel(
          mnemonicData[index].data, 
          mnemonicSelectData.length + 1, 
          isSelected: true,
        ),
      );
    }else{
      mnemonicSelectData.removeWhere((element) => element.data == mnemonicData[index].data);
    }
    verifyEachMnemonic();
  }

  void clickSelectWord(index){
    var data = mnemonicSelectData[index];
    var mnemonicDataItem = mnemonicData.lastWhere((element) => element.data == data.data);
    var indexOf = mnemonicData.indexOf(mnemonicDataItem);
    mnemonicDataItem.isSelected=false;
    mnemonicData[indexOf]=mnemonicDataItem;
    mnemonicSelectData.removeAt(index);
    verifyEachMnemonic();
  }

  void verifyEachMnemonic() {
    for(var i=0; i<mnemonicSelectData.length; i++){
      if(mnemonicSelectData[i].data != sourceMnemonicList[i]){
        mnemonicSelectData[i].isCorrect = false;
      }
    }
  }

  bool isSelectedMnemonicIncorrectExist() {
    bool isExist = false;
    mnemonicSelectData.forEach((element) { 
      if(!element.isCorrect){
        isExist = true;
      }
    });
    return isExist;
  }

  bool verifyMnemonic(){
    String source="";
    String selected="";    
    for(String el in sourceMnemonicList){
      source="$source $el";
    }
    for(MnemonicVerifySourceModel el in mnemonicSelectData){
      selected="$selected ${el.data}";
    }
    AppLogger.d("source==$source  selected==$selected");
    if(source==selected){
      toast(L.mnemonic_verify_success.tr);
      Get.back(result: true);
    }else{
      toast(L.mnemonic_verify_failed_please_check.tr);
    }
    return source==selected;
  }
}
