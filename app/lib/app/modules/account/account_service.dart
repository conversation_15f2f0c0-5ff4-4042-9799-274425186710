import 'dart:async';
import 'dart:typed_data';

import 'package:dart_ping/dart_ping.dart';
import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/data/providers/api/operation_center_api.dart';
import 'package:flutter_metatel/core/task/private_task.dart';
import 'package:flutter_metatel/core/utils/comm_util.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../core/languages/l.dart';
import '../../../core/utils/app_log.dart';
import '../../../core/utils/events_bus.dart';
import '../../../core/utils/util.dart';
import '../../../core/values/config.dart';
import '../../../routes/pages.dart';
import '../../data/events/events.dart';
import '../../data/models/node_model.dart';
import '../../data/models/res/auth_v3_get_token.dart';
import '../../data/providers/api/api.dart';
import '../../data/providers/api/own.dart';
import '../../data/services/chatio_service.dart';
import '../../data/services/config_service.dart';
import '../../data/services/network_connect_service.dart';
import '../../data/services/secure_store_service.dart';
import '../home/<USER>/setmyselfinfo/first/first_my_self_info.dart';

class AccountService extends GetxService {
  static const String mnemonicTAG = "mnemonic";
  static const String mnemonicViewTypeTAG = "mnemonicViewTypeTAG";
  ByteData? mnemonicImageBytes;
  String? registerCode;
  String? mnemonic;

  Future<AccountService> init() async {
    _listenEvent();
    return this;
  }

  void login(
      String mnemonic, {
        String? registerCode,
        ByteData? imageBytes,
        int? action, ///1->顶替登陆 0->默认
        VoidCallback? onError,
      }) async {
    mnemonicImageBytes = imageBytes;
    this.registerCode = registerCode;
    bool isLegality = mnemonicLegality(mnemonic.split(" "));
    if (!isLegality) {
      toast(
          L.the_mnemonic_is_invalid_please_check_it_carefully_and_try_again.tr);
      dismissLoadingDialog();
      onError?.call();
      return;
    }
    await PrivateConfigTask().configPrivateKeyFromMnemonic(mnemonic: mnemonic);
    if (!await Get.find<NetWorkConnectService>().networkConnected()) {
      toast(L.network_unavailable.tr);
      dismissLoadingDialog();
      onError?.call();
      return;
    }
    try {
      AuthV3GetToken authV3GetToken = await initToken(
        registerCode: registerCode,
        action: action,
        isRestore: imageBytes == null,
        autoRetry: false,
      );
      AppLogger.d('_initTokenResultProcess....');
      await _initTokenResultProcess(authV3GetToken, mnemonic, imageBytes,
          isRestore: imageBytes == null);
    } catch (e) {
      AppLogger.e('Login error: $e');
      toast(L.server_error_retry.tr);
      onError?.call();
      dismissLoadingDialog();
    }
  }

  Future<void> _initTokenResultProcess(AuthV3GetToken authV3GetToken, String mnemonic,
      ByteData? imageBytes, {bool isRestore = false}) async {
    final AppConfigService configService = Get.find();
    if (authV3GetToken.token != null && authV3GetToken.token!.isNotEmpty) {
      var oldMnemonic =
      await Get.find<SecureStoreService>().secureStoreReadMnemonic();
      if ((oldMnemonic?.isEmpty ?? true) || oldMnemonic != mnemonic) {
        await Get.find<SecureStoreService>().secureStoreSaveMnemonic(mnemonic);
      }
      var userName = configService.getUserName();
      var privateKey = Config.privateKeyBase64;
      if (privateKey.isNotEmpty) {
        configService.changeNotFistLogin(true);
        // RepaintBoundaryUtils.savePhoto(imageBytes,
        //     toastSuccess: L.save_the_mnemonic_successfully.tr,
        //     toastError: L.save_the_mnemonic_failed.tr);

        if (configService.getMySelfDisplayName().isEmpty) {
          var infos = await getOtherInfo([userName ?? ""]);
          if (infos == null || infos.isEmpty) {
            configService.saveMySelfDisplayName(userName ?? "");
          } else {
            for (var element in infos) {
              configService.saveMySelfDisplayName(element.nickname ?? "");
            }
          }
        }
        await CommUtil.instance.setFirstLoginTime();
        dismissLoadingDialog();
        Get.offNamed(Routes.HOME);
      }
    } else {
      if (authV3GetToken.error != null) {
        toast(authV3GetToken.error ?? L.server_error_retry.tr);
      }
      dismissLoadingDialog();
      throw Exception('Login failed');
    }
  }

  Future<void> selectNode({required String registerCode, VoidCallback? callBack, VoidCallback? onError}) async {
    AppLogger.d("selectNode .....");
    if (!await Get.find<NetWorkConnectService>().networkConnected()) {
      toast(L.network_unavailable.tr);
      onError?.call();
      return;
    }
    Config.nodeDefault.value = "";
    Config.nodeDefaultPort = "";
    await Get.find<AppConfigService>().saveNode(null);
    await Get.find<AppConfigService>().saveNodePort(null);

    try {
      // showLoadingDialog(isBack: false, timeOutSecond: 45);
      await PrivateConfigTask().configPrivateKeyFromMnemonic(mnemonic: mnemonic);
      if (Get.find<AppConfigService>().getUserNameWithoutDomain().isEmpty) {
        AppLogger.e("account service selectNode userName is Empty");
        dismissLoadingDialog();
        onError?.call();
        return;
      }

      String? keyBoxId;
      if (registerCode.length > RegisterCodeInnerKeyBoxId.registerCodeLength) {
        keyBoxId = registerCode.substring(RegisterCodeInnerKeyBoxId.registerCodeLength);
      }
      AppLogger.d("account keyBoxId=$keyBoxId");

      var response =
      await Get.find<OperationCenterApiProvider>().getComNode(keyBoxID: keyBoxId);
      if (response.statusCode != 200) {
        AppLogger.e("getComNode failed with status code: ${response.statusCode}");
        dismissLoadingDialog();
        toast(L.server_error_retry.tr);
        onError?.call();
        return;
      }
      int? code = response.data?.code;
      String? msg = response.data?.message;
      NodeModelData? body = response.data?.nodeModelData;
      // 检查所有节点ping时间
      var nodeList = await checkAllNodePingTime(body?.publicNode ?? []);
      if (code != 200 || body == null) {
        AppLogger.e("getComNode failed with code: $code, msg: $msg");
        toast(msg ?? L.unknown.tr);
        dismissLoadingDialog();
        onError?.call();
        return;
      } else {
        ComNode? defaultNode;
        if (Config.isOverseasNetWork) {
          // 如果是海外网络，从public_node中选择
          defaultNode = nodeList.first;
        } else {
          // 如果是本地网络，优先选择privateNode，如果不存在，则选择public_node中的第一个
          defaultNode = body.privateNode ?? nodeList.first;
        }

        if (defaultNode.node?.isEmpty ?? true) {
          AppLogger.e("No default node available");
          dismissLoadingDialog();
          onError?.call();
          return;
        }

        // 保存默认节点信息
        await Get.find<AppConfigService>().saveNode(defaultNode.node);
        await Get.find<ChatioService>().saveDomain(mnemonic ?? '', defaultNode.node ?? '');
        await Get.find<AppConfigService>().saveNodePort('${defaultNode.port}');

        // dismissLoadingDialog();

        // 调用回调
        if (callBack != null) {
          showLoadingDialog(isBack: false, timeOutSecond: 900);
          callBack.call();
          dismissLoadingDialog();
        }

        // // 导航至 FirstMySelfInfoPage
        // AppLogger.d("Navigating to FirstMySelfInfoPage");
        // await Get.to(const FirstMySelfInfoPage());
        // AppLogger.d("Navigation to FirstMySelfInfoPage completed");
      }
    } catch (e) {
      onError?.call();
      AppLogger.e("selectNode error: $e");
      toast(L.server_error_retry.tr);
      dismissLoadingDialog();
    }
  }

  void _listenEvent() {
    CurrentRealNameType type = Get.find<AppConfigService>().getNotFirstLogin()
        ? CurrentRealNameType.login
        : CurrentRealNameType.register;
    var bus = Get.find<EventBus>();
    AppLogger.d('InputCodeController RealNameVerifyEvent type=$type');

    bus.on<RealNameVerifyEvent>().listen((event) {
      realNameVerify(
        event,
        type,
        mnemonic: mnemonic,
        imageBytes: mnemonicImageBytes,
        registerCode: registerCode,
      );
    });

    bus.on<MobileVerifyEvent>().listen((event) {
      mobileVerifyRegister(
        event,
        type,
        mnemonic: mnemonic,
        registerCode: registerCode,
      );
    });
  }


  /// 检查所有节点ping时间
  Future<List<ComNode>> checkAllNodePingTime(List<ComNode> nodeList) async {
    for (var node in nodeList) {
      var pingUrl =
      node.node?.replaceAll('http://', '').replaceAll('https://', '');
      // remove port
      var subStart = pingUrl?.lastIndexOf(":");
      if (subStart != null && subStart > -1) {
        pingUrl = pingUrl?.substring(0, subStart);
      }
      var ping = await Ping(pingUrl!, count: 1).stream.first;
      if (ping.response?.time != null) {
        final ms = ping.response!.time!.inMicroseconds /
            Duration.millisecondsPerSecond;
        node.pingTime = ms.toInt();
      }
    }
    nodeList.sort((a, b) => (a.pingTime ?? 0).compareTo(b.pingTime ?? 0));
    return nodeList;
  }
}
