import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';

import 'package:extended_image/extended_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/data/services/event_service.dart';
import 'package:flutter_metatel/core/task/time_task.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:flutter_metatel/core/utils/util.dart';
import 'package:flutter_metatel/core/values/config.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../core/languages/l.dart';
import '../../../core/task/down_load_file_task.dart';
import '../../../core/utils/crypto_util.dart';
import '../../../core/utils/events_bus.dart';
import '../../../core/utils/file_util.dart';
import '../../data/enums/enum.dart';
import '../../data/events/events.dart';
import '../../data/models/merge_message_body_model.dart';
import '../../data/models/weak_image_bean.dart';
import '../../widgets/chat_widget/photos_video_preview_view_merge.dart';
import '../../widgets/down_load_file_view.dart';
import '../message/message_controller.dart';

class MergeMessageController extends GetxController {
  RxList<MergeBean> rxMergeBeanList = RxList();
  late MessageEvent msgEvent;
  late String mergeFileDirPathMainDir;
  late String userIconPathDir;
  late String fileOrImagePathDir;
  final List<WeakImageBean> imagesCache = [];
  final Map<String, double> imgHeightMaps = <String, double>{};
  bool zipFileExist = false;
  bool jsonMessageFileExist = false;
  StreamSubscription<UpdateFileState>? subscription;


  @override
  void dispose() {
    subscription?.cancel();
    super.dispose();
  }
  @override
  void onInit() {
    super.onInit();
    subscription = Get.find<EventBus>().on<UpdateFileState>().listen((event) {
      AppLogger.d(
          "MergeMessageController msgId==${event.event.msgId}");
      if(event.mergeMsg){
        var msgId=event.event.msgId;
        var e = rxMergeBeanList
            .firstWhere((element) => msgId == element.msgId);
        if (event.event.filePath?.isNotEmpty ?? false) {
          e.filePath = event.event.filePath?.replaceAll(fileOrImagePathDir, '');
        }
        if (event.event.thumbnailPath?.isNotEmpty ?? false) {
          e.thumbnailPath = event.event.thumbnailPath?.replaceAll(fileOrImagePathDir, '');
        }
        for(int i=0;i<rxMergeBeanList.length;i++){
          if(rxMergeBeanList[i].msgId==msgId){
            rxMergeBeanList[i]=e;
          }
        }
        update([event.event.msgId]);
      }
    });
  }

  @override
  void onReady() {
    initData();
    super.onReady();
  }

  void initData() async {
    showLoadingDialog();
    mergeFileDirPathMainDir =
        getMergeFileDirPath(msgEvent.owner, msgEvent.resourceUuid ?? "");
    userIconPathDir = getMergeFileUserIconFileName(mergeFileDirPathMainDir);
    fileOrImagePathDir =
        getMergeFileFileOrImageFileName(mergeFileDirPathMainDir);
    String zipPath = "$mergeFileDirPathMainDir.zip";
    File jsonMessageFile = File(
        '$mergeFileDirPathMainDir/${MergeMessageStaticParams.messageJsonFileName}');
    jsonMessageFileExist = jsonMessageFile.existsSync();
    if (!jsonMessageFileExist) {
      Directory(mergeFileDirPathMainDir).createSync(recursive: true);
      zipFileExist = File(zipPath).existsSync();
      if (zipFileExist) {
        unZip(jsonMessageFile);
      } else {
        if (!File(msgEvent.filePath ?? "").existsSync()) {
          var ev = await DownLoadFileTask().loadFile(msgEvent);
          if (ev.filePath?.isEmpty ?? true) {
            dismissLoadingDialog();
            return;
          }
        }
        unZip(jsonMessageFile);
      }
    } else {
      analyzeData(jsonMessageFile);
    }
  }

  unZip(File jsonMessageFile) async {
    if (isCipherFile(msgEvent.filePath ?? "") && !zipFileExist) {
      String decPath = '$mergeFileDirPathMainDir.zip';
      await mtDecryptFile(
          msgEvent.filePath ?? '', decPath, msgEvent.fileFragment ?? '');
      Uint8List? dataByte = File(decPath).readAsBytesSync();
      await unZipFiles(
        msgEvent.filePath ?? '',
        Directory(mergeFileDirPathMainDir),
        dataBytes: dataByte,
      );
    } else {
      await unZipFiles(
          msgEvent.filePath ?? '', Directory(mergeFileDirPathMainDir));
    }
    analyzeData(jsonMessageFile);
  }

  analyzeData(File jsonMessageFile) async {
    if (jsonMessageFile.existsSync()) {
      String? messageJsonStr = await readFileToString(jsonMessageFile);
      List? list = json.decode(messageJsonStr ?? "");
      List<MergeBean>? mergeBeanList =
          list?.map((e) => MergeBean.fromJson(e)).toList();
      rxMergeBeanList.addAll(mergeBeanList ?? []);
    }
    dismissLoadingDialog();
  }

  @override
  void onClose() {
    AppLogger.d(
        "MergeMessageController onClose msgEvent.direction==${msgEvent.direction}");
    if (rxMergeBeanList.isNotEmpty) {
      List<MergeBean> value = rxMergeBeanList;
      String jsonStringMessage = json.encode(value);
      deleteFileIsExists(
          "$mergeFileDirPathMainDir/${MergeMessageStaticParams.messageJsonFileName}");
      writeFileFromString(
          File(
              "$mergeFileDirPathMainDir/${MergeMessageStaticParams.messageJsonFileName}"),
          jsonStringMessage);
    }
    _clearImageCache();
    super.onClose();
  }

  void _clearImageCache() {
    for (var element in imagesCache) {
      clearMemoryImageCache(element.path);
    }
    imagesCache.clear();
    imgHeightMaps.clear();
  }

  void onLongPress(BuildContext context, MergeBean mergeBean, Widget item,
      {double? height}) {
    showBottomDialogCommonWithCancel(context, widgets: [
      Container(
        height: (height != null && height > 220.h) ? 220.h : height,
        padding: const EdgeInsets.all(16).r,
        alignment: Alignment.center,
        child: item,
      ),
      getBottomSheetItemSimple(context, L.message_dialog_forward.tr, radius: 12,
          itemCallBack: () {
        String filePath = mergeBean.filePath ?? "";
        filePath =
            filePath.isNotEmpty ? (fileOrImagePathDir + filePath) : filePath;
        String thumbnailPath = mergeBean.thumbnailPath ?? "";
        thumbnailPath = thumbnailPath.isNotEmpty
            ? (fileOrImagePathDir + thumbnailPath)
            : thumbnailPath;
        MessageEvent event = MessageEvent("",
            type: MessageType.values[mergeBean.type ?? 0],
            body: mergeBean.body,
            fileName: mergeBean.fileName,
            fileSize: mergeBean.fileSize,
            fileUrl: mergeBean.fileUrl,
            fileFragment: mergeBean.fileFragment,
            filePath: filePath,
            thumbnailFragment: mergeBean.thumbnailFragment,
            thumbnailPath: thumbnailPath,
            thumbnailUrl: mergeBean.thumbnailUrl,

            ///下面这些没用
            owner: '',
            chatType: ChatType.singleChat,
            dateTime: TimeTask.instance.getNowDateTime());
        Get.find<MessageController>().onForwardMessage([event]);
      }),
    ]);
  }

  onTapImageItem(int index,BuildContext context){
    List<MergeBean> pathList = [];
    int currentIndex = 0;

    for (int i = rxMergeBeanList.length - 1; i > -1; i--) {
      MergeBean data = rxMergeBeanList[i];
      if (data.type == MessageType.image.index ||
          data.type == MessageType.sticker.index) {
        pathList.insert(0, data);
        if (index == i) {
          currentIndex = 0;
        } else {
          currentIndex++;
        }
      }
    }
    showDialog(
        barrierDismissible: false,
        context: context,
        useSafeArea: false,
        builder: (_) {
          return PhotosPreviewViewMerge(
            messageList: pathList,
            currentIndex: currentIndex,
            weakList: imagesCache,
            fileOrImagePathDir: fileOrImagePathDir, isOriginalSelected: false,
          );
        });
  }
  onTapVideoItem(MergeBean mergeBean,BuildContext context){
    downLoadFileShowDialog(
      barrierDismissible: true,
      parentContext: context,
      event: MessageEvent(
        mergeBean.msgId ?? "",
        owner: mergeBean.owner ?? "",
        chatType: ChatType.singleChat,
        dateTime: DateTime(1),
        fileUrl: mergeBean.fileUrl,
        fileFragment: mergeBean.fileFragment,
        filePath: fileOrImagePathDir + (mergeBean.filePath??''),
        fileName: mergeBean.fileName,
      ),
      saveDirPath: fileOrImagePathDir,
      mergeMsg:true,
      body: Container(),
    );
  }

}
