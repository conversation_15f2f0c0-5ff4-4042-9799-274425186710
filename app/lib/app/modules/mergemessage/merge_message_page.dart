import 'dart:convert';

import 'package:extended_text_field/extended_text_field.dart';
import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/modules/mergemessage/merge_message_controller.dart';
import 'package:flutter_metatel/app/modules/mergemessage/merge_message_item.dart';
import 'package:flutter_metatel/app/modules/message/components/attachment_dialog.dart';
import 'package:flutter_metatel/app/widgets/chat_bubbles/bubbles/bubble.dart';
import 'package:flutter_metatel/app/widgets/chat_bubbles/bubbles/bubble_money_exchange.dart';
import 'package:flutter_metatel/core/task/time_task.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_web3/app/data/models/mony_exchange_model.dart';
import 'package:get/get.dart';

import '../../../core/languages/l.dart';
import '../../../core/values/colors.dart';
import '../../../core/values/config.dart';
import '../../../r.dart';
import '../../data/models/merge_message_body_model.dart';
import '../../data/services/event_service.dart';
import '../../widgets/at_widget/my_special_text_span_builder.dart';
import '../../widgets/chat_bubbles/algo/algo.dart';
import '../../widgets/divider_cus.dart';
import '../../widgets/down_load_view.dart';
import '../../widgets/mavatar_circle_avatar.dart';

class MergeMessagePage extends StatefulWidget {
  const MergeMessagePage(this.msgEvent, this.title, {Key? key})
      : super(key: key);
  final MessageEvent msgEvent;
  final String title;

  @override
  State<StatefulWidget> createState() => _MergeMessagePage();
}

class _MergeMessagePage extends State<MergeMessagePage> {
  final controller = Get.put(MergeMessageController());
  late MessageEvent msgEvent;
  late String title;

  @override
  void initState() {
    super.initState();
    msgEvent = widget.msgEvent;
    title = widget.title;
    controller.msgEvent = msgEvent;
  }

  @override
  void dispose() {
    Get.delete<MergeMessageController>();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(L.marge_msg_history.trParams({
          'title': title,
        })),
        centerTitle: false,
      ),
      body: Container(
        color: Theme.of(context).primaryColor,
        padding: const EdgeInsets.all(16).r,
        child: Obx(
          () => ListView.builder(
            itemCount: controller.rxMergeBeanList.length,
            reverse: false,
            shrinkWrap: true,
            itemBuilder: (context, index) {
              return _buildItem(context, index);
            },
          ),
        ),
      ),
    );
  }

  Widget _buildItem(BuildContext context, index) {
    bool isVisibleHeadIcon = false;
    bool isVisibleDivider = true;
    MergeBean data = controller.rxMergeBeanList[index];
    if (index > 0 &&
        controller.rxMergeBeanList[index - 1].userName == (data.userName)) {
      isVisibleHeadIcon = false;
    } else {
      isVisibleHeadIcon = true;
    }
    if (index == controller.rxMergeBeanList.length - 1) {
      isVisibleDivider = false;
    } else {
      isVisibleDivider = true;
    }
    return GetBuilder<MergeMessageController>(
        id: data.msgId,
        builder: (controller) {
          return Container(
            padding: const EdgeInsets.only(bottom: 14).r,
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Opacity(
                  opacity: isVisibleHeadIcon ? 1 : 0,
                  child: Container(
                    margin: const EdgeInsets.only(right: 6).r,
                    child: MAvatarCircle(
                      imagePath: data.avatarPath == null
                          ? null
                          : controller.userIconPathDir + (data.avatarPath ?? ""),
                      text: data.displayName,
                    ),
                  ),
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              data.displayName ?? "",
                              style: TextStyle(
                                  color: AppColors.colorFF808080, fontSize: 12.sp),
                            ),
                          ),
                          Text(
                            Algo.dateChipText(data.time ?? TimeTask.instance.getNowDateTime()),
                            style: TextStyle(
                                color: AppColors.colorFF808080, fontSize: 12.sp),
                          ),
                        ],
                      ),
                      SizedBox(height: 8.h),
                      _buildContentWidget(data, index, context),
                      SizedBox(height: 8.h),
                      Visibility(
                        visible: isVisibleDivider,
                        child: const DividerCus(),
                      )
                    ],
                  ),
                ),
              ],
            ),
          );
        });
  }

  Widget _buildContentWidget(
      MergeBean mergeBean, int index, BuildContext context) {
    Widget? resultWidget;
    if (mergeBean.type == MessageType.text.index) {
      resultWidget = Row(
        children: [
          Flexible(
            child: ExtendedSelectableText(
              mergeBean.body ?? "",
              specialTextSpanBuilder: MySpecialTextSpanBuilder(
                  showAtBackground: false,
                  showLinkColor: true,
                  linkColor: AppColors.appDefault,
                  atTextColor: AppColors.appDefault),
            ),
          ),
        ],
      );
    } else if(mergeBean.type ==MessageType.moneyExchange.index){
      MoneyExchangeModel? bean;
      try {
        bean =
            MoneyExchangeModel.fromJson(jsonDecode(mergeBean.body ?? "{}"));
      } catch (e) {
        bean = null;
      }
      if(bean!=null){
        resultWidget=bubbleMoneyExchangeChild(bean);
      }
    }else if (mergeBean.type == MessageType.image.index ||
        mergeBean.type == MessageType.sticker.index) {
      MergeMessageItemImage item = MergeMessageItemImage(
        imgHeightMaps: controller.imgHeightMaps,
        fileOrImagePathDir: controller.fileOrImagePathDir,
        fileUrl: mergeBean.fileUrl,
        imagePath: mergeBean.filePath ?? "",
        thumbnailPath: mergeBean.thumbnailPath ?? "",
        fileFragment: mergeBean.fileFragment ?? "",
        thumbnailFragment: mergeBean.thumbnailFragment ?? "",
        width: mergeBean.type == MessageType.sticker.index ? 100 : null,
        height: mergeBean.type == MessageType.sticker.index ? 100 : null,
        imagesCache: controller.imagesCache,
        onTap: (){
          controller.onTapImageItem(index, context);
        },
        onLongPress: (height,item){
          controller.onLongPress(context, mergeBean, item, height: height);
        },
      );
      resultWidget=Container(
        constraints: BoxConstraints(maxHeight: 300.h),
        child:item ,
      );
    } else if (mergeBean.type == MessageType.video.index) {
      MergeMessageItemVideo item = MergeMessageItemVideo(
        fileOrImagePathDir:controller.fileOrImagePathDir,
        imagesCache: controller.imagesCache,
        imgHeightMaps: controller.imgHeightMaps,
        imagePath:
            controller.fileOrImagePathDir + (mergeBean.thumbnailPath ?? ""),
        fileFragment: mergeBean.thumbnailFragment,
        onTap: (){
          DownLoadShowDialog(
            barrierDismissible: true,
            context: context,
            event: MessageEvent(
              mergeBean.msgId ?? "",
              owner: mergeBean.owner ?? "",
              chatType: ChatType.singleChat,
              dateTime: DateTime(1),
              fileUrl: mergeBean.fileUrl,
              fileFragment: mergeBean.fileFragment,
              filePath:controller.fileOrImagePathDir + (mergeBean.filePath??''),
              fileName: mergeBean.fileName,
            ),
            saveDirPath: controller.fileOrImagePathDir,
            mergeMsg:true,
            body: Container(),
          );
        },
        onLongPress: (height,item){
          controller.onLongPress(context, mergeBean, item, height: height);
        },
      );
      resultWidget =Container(
        constraints: BoxConstraints(maxHeight: 300.h),
        child:item ,
      );

    } else if (mergeBean.type == MessageType.file.index) {
      Widget item = Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Image.asset(
            R.file,
            width: 40.w,
            height: 40.w,
          ),
          SizedBox(
            width: 5.w,
          ),
          Container(
            constraints: BoxConstraints(maxWidth: 200.w, maxHeight: 40.w),
            child: Text(
              mergeBean.fileName ?? "",
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
            ),
          ),
        ],
      );
      resultWidget = Builder(builder: (ctx) {
        return GestureDetector(
          onTap: () {
            controller.onTapVideoItem(mergeBean, context);
          },
          onLongPress: () => controller.onLongPress(context, mergeBean, item),
          child: item,
        );
      });
    } else if (mergeBean.type == MessageType.stickerDefault.index ||
        mergeBean.type == MessageType.stickerDefaultRabbit.index ||
        mergeBean.type == MessageType.stickerDefaultEmoji.index) {
      resultWidget = Image.asset(
        (mergeBean.filePath ?? ""),
        fit: BoxFit.contain,
        width: 100.w,
        height: 100.w,
      );
    }
    return resultWidget??Container();
  }
}
