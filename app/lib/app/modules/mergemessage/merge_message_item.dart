import 'dart:io';
import 'dart:typed_data';

import 'package:extended_image/extended_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/widgets/chat_bubbles/chat_bubbles.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../core/utils/app_log.dart';
import '../../../core/utils/util.dart';
import '../../../core/values/config.dart';
import '../../../r.dart';
import '../../data/models/weak_image_bean.dart';
import '../../data/providers/native/chatio/chatio_async.dart';

typedef  MergeMessageItemOnTap = void Function();
typedef  MergeMessageItemOnLongPress = void Function(double itemHeight,Widget item);
class MergeMessageItemImage extends StatefulWidget {
  final String imagePath;
  final String thumbnailPath;
  final String? fileUrl;
  final String fileFragment;
  final String thumbnailFragment;
  final double? width;
  final double? height;
  final String fileOrImagePathDir;
  final Map<String, double> imgHeightMaps;
  final List<WeakImageBean> imagesCache;
  final MergeMessageItemOnTap? onTap;
  final MergeMessageItemOnLongPress? onLongPress;

  const MergeMessageItemImage({
    Key? key,
    required this.imagePath,
    required this.thumbnailPath,
    required this.imgHeightMaps,
    required this.fileFragment,
    required this.thumbnailFragment,
    required this.fileOrImagePathDir,
    required this.imagesCache,
    this.onLongPress,
    this.onTap,
    this.fileUrl,
    this.width,
    this.height,
  }) : super(key: key);

  @override
  State<MergeMessageItemImage> createState() => _MergeMessageItemImageState();
}

class _MergeMessageItemImageState extends State<MergeMessageItemImage> {
  double? vHeight;
  ImageStreamListener? listener;
  ImageStream? stream;
  late String useImagePath;

  late String useFragment;

  List<WeakImageBean> getWeakList() {
    return widget.imagesCache;
  }

  @override
  Widget build(BuildContext context) {
    Widget? child;
    Uint8List? data;
    Widget childFailed = Image.asset(
      R.icoPicLoadFailed,
    );
    useImagePath =
        widget.imagePath.isNotEmpty ? widget.imagePath : widget.thumbnailPath;
    useFragment = widget.imagePath.isNotEmpty
        ? widget.fileFragment
        : widget.thumbnailFragment;

    if (widget.imgHeightMaps.containsKey(useImagePath)) {
      vHeight = widget.imgHeightMaps[useImagePath];
    }
    if (vHeight == null || vHeight == 0.0) {
      listener = ImageStreamListener((ImageInfo info, bool _) {
        double h = getImageHeight(info.image.width, info.image.height,
            limitMaxHeight: true);
        AppLogger.d('MergeImage imagePath==h$h');
        widget.imgHeightMaps[useImagePath] = h.h;
        stream?.removeListener(listener!);
      });
    }
    data = getImageData(image: widget.fileOrImagePathDir + useImagePath);
    if (data == null) {
      AppLogger.d('merge msg image data==null');
      if (isCipher()) {
        if (useFragment.isEmpty) {
          return childFailed;
        } else {
          return createImageWidget(
              widget.fileOrImagePathDir + useImagePath, useFragment);
        }
      } else {
        data = loadNonEncryFile(widget.fileOrImagePathDir + useImagePath);
      }
    }
    child = createImageView(widget.fileOrImagePathDir + useImagePath, data);
    return child;
  }

  Uint8List? loadNonEncryFile(String imagePath) {
    File file = File(imagePath);
    AppLogger.d('MergeImage file..${file.existsSync()}');
    AppLogger.d('MergeImage imagePath..$imagePath');

    Uint8List? data = file.readAsBytesSync();
    widget.imagesCache.add(WeakImageBean(imagePath, data));
    return data;
  }

  bool isCipher() {
    return isCipherFile(useImagePath);
  }

  Widget createImageWidget(String imagePath, String fragment) {
    return FutureBuilder(
      future: ChatioNative.utilFileDecryptToMemory(imagePath, fragment),
      builder: (_, snapshot) {
        Uint8List? imageDatas;
        if (snapshot.connectionState == ConnectionState.done) {
          if (snapshot.data != null) {
            imageDatas = snapshot.data as Uint8List;
            widget.imagesCache.add(WeakImageBean(imagePath, imageDatas));
          }
        }
        Widget child = createImageView(imagePath, imageDatas);
        return child;
      },
    );
  }

  Uint8List? getImageData({String? image}) {
    Uint8List? data;
    image = image ?? widget.imagePath;
    try {
      data = getWeakList().firstWhere((element) => element.path == image).data;
    } catch (e) {
      return data;
    }
    return data;
  }

  Widget createImageView(String imagePath, Uint8List? data) {
    Widget? child;
    try {
      if (data == null) {
        child = imageAsset(R.icImageLoading);
      } else {
        child = createExtendedImageView(data, imagePath,
            w: widget.width, h: widget.height);
        if (listener != null && (vHeight == null || vHeight == 0.0)) {
          stream =
              (child as ExtendedImage).image.resolve(const ImageConfiguration());
          stream?.addListener(listener!);
        }
      }
    } catch (e) {
      AppLogger.e("createImageView exception==${e.toString()}");
    }
    return GestureDetector(
      child: child,
      onTap: (){
        widget.onTap?.call();
      },
      onLongPress: (){
        widget.onLongPress?.call(context.height,child!);
      },
    );
  }

  Widget imageAsset(String name) {
    return ExtendedImage.asset(
      name,
      fit: BoxFit.cover,
      imageCacheName: name,
      clearMemoryCacheWhenDispose: true,
      maxBytes: Config.maxImageBytes,
    );
  }

  Widget createExtendedImageView(Uint8List data, String imagePath,
      {double? h, double? w}) {
    AppLogger.d('MergeImage h=$h vH=$vHeight');
    return ExtendedImage.memory(
      data,
      fit: BoxFit.cover,
      imageCacheName: imagePath,
      height: h ?? vHeight,
      width: w,
      maxBytes: Config.maxImageBytes,
      loadStateChanged: (state) {
        if (state.extendedImageLoadState == LoadState.failed) {
          return imageAsset(R.icoPicLoadFailed);
        }
        return null;
      },
    );
  }
}

class MergeMessageItemVideo extends StatefulWidget {
  final String? imagePath;
  final String? fileFragment;
  final Map<String, double> imgHeightMaps;
  final List<WeakImageBean> imagesCache;
  final String fileOrImagePathDir;
  final MergeMessageItemOnTap? onTap;
  final MergeMessageItemOnLongPress? onLongPress;
  const MergeMessageItemVideo({
    Key? key,
    required this.imagePath,
    required this.fileFragment,
    required this.imgHeightMaps,
    required this.fileOrImagePathDir,
    required this.imagesCache,
    this.onTap,
    this.onLongPress,
  }) : super(key: key);

  @override
  State<MergeMessageItemVideo> createState() => _MergeMessageItemVideoState();
}

class _MergeMessageItemVideoState extends State<MergeMessageItemVideo> {
  ImageStream? stream;
  double? vHeight;
  ImageStreamListener? listener;

  List<WeakImageBean> getWeakList() {
    return widget.imagesCache;
  }

  Uint8List? getImageData({String? image}) {
    Uint8List? data;
    image = image ?? widget.imagePath;
    try {
      data = getWeakList().firstWhere((element) => element.path == image).data;
    } catch (e) {
      return data;
    }
    return data;
  }

  @override
  void initState() {
    if (widget.imgHeightMaps.containsKey(widget.imagePath)) {
      vHeight = widget.imgHeightMaps[widget.imagePath];
    }
    if (vHeight == null || vHeight == 0.0) {
      listener = ImageStreamListener((ImageInfo info, bool _) {
        double h = getImageHeight(info.image.width, info.image.height,
            limitMaxHeight: true);
        AppLogger.d('MergeImageVideo imagePath h==$h');
        widget.imgHeightMaps[widget.imagePath!] = h.h;
        stream?.removeListener(listener!);
      });
    }
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    String? thumbnailPath = widget.imagePath;
    Uint8List? data;
    Widget? child;
    Widget childFailed = Image.asset(
      R.icoPicLoadFailed,
    );
    if (thumbnailPath?.isNotEmpty ?? false) {
      data = getImageData();
      if (data == null) {
        AppLogger.d('merge msg video thumb data==null');
        if (isCipher()) {
          if (widget.fileFragment == null) {
            return childFailed;
          } else {
            return createImageWidget(thumbnailPath!, widget.fileFragment!);
          }
        } else {
          data = File(thumbnailPath!).readAsBytesSync();
          widget.imagesCache.add(WeakImageBean(thumbnailPath, data));
        }
      }
      child = createImageView(thumbnailPath!, data);
    }

    if (child == null) {
      child = createVideoItemDefaultWidget();
    } else {
      var videoOpen = createVideoItemOpenWidget(child);
      child = GestureDetector(
        child: videoOpen,
        onTap: (){
          widget.onTap?.call();
        },
        onLongPress: (){
          widget.onLongPress?.call(context.height,videoOpen);
        },
      );
    }
    return child;
  }

  bool isCipher() {
    return isCipherFile(widget.imagePath);
  }

  Widget createImageWidget(String imagePath, String fragment) {
    return FutureBuilder(
      future: ChatioNative.utilFileDecryptToMemory(imagePath, fragment),
      builder: (_, snapshot) {
        Uint8List? imageDatas;
        if (snapshot.connectionState == ConnectionState.done) {
          if (snapshot.data != null) {
            imageDatas = snapshot.data as Uint8List;
            widget.imagesCache.add(WeakImageBean(imagePath, imageDatas));
          }
        }

        Widget child = createImageView(imagePath, imageDatas);
        var videoOpen = createVideoItemOpenWidget(child);
        return  GestureDetector(
          child: videoOpen,
          onTap: (){
            widget.onTap?.call();
          },
          onLongPress: (){
            widget.onLongPress?.call(context.height,videoOpen);
          },
        );
      },
    );
  }

  Widget createImageView(String imagePath, Uint8List? data) {
    Widget? child;
    try {
      if (data == null) {
        child = imageAsset(R.icImageLoading);
      } else {
        child = createExtendedImageView(data, imagePath);
      }
      if (listener != null && (vHeight == null || vHeight == 0.0)) {
        stream =
            (child as ExtendedImage).image.resolve(const ImageConfiguration());
        stream?.addListener(listener!);
      }
    } catch (e) {
      AppLogger.e("createImageView exception==${e.toString()}");
    }
    return child!;
  }

  Widget imageAsset(String name) {
    return ExtendedImage.asset(
      name,
      fit: BoxFit.cover,
      imageCacheName: name,
      clearMemoryCacheWhenDispose: true,
      maxBytes: Config.maxImageBytes,
    );
  }

  Widget createExtendedImageView(Uint8List data, String imagePath,
      {double? h, double? w}) {
    AppLogger.d('MergeImageVideo h=$h vH=$vHeight');
    return ExtendedImage.memory(
      data,
      fit: BoxFit.cover,
      imageCacheName: imagePath,
      height: h ?? vHeight,
      width: w,
      maxBytes: Config.maxImageBytes,
      loadStateChanged: (state) {
        if (state.extendedImageLoadState == LoadState.failed) {
          return imageAsset(R.icoPicLoadFailed);
        }
        return null;
      },
    );
  }
}
