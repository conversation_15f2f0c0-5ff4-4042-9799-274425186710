import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/modules/virtualcard/record_item.dart';
import 'package:flutter_metatel/app/widgets/app_bar_cus.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../core/languages/l.dart';
import '../../../core/utils/util.dart';

class RechargeRecord extends StatelessWidget {
  const RechargeRecord({super.key, this.s});

  final List<String>? s;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBarCommon().build(context, title: L.recharge_record.tr),
      body: _loadRechargeRecordListView(),
    );
  }

  _loadRechargeRecordListView() {
    var allData = s ?? [];
    Widget w;
    w = Stack(
      children: [
        Visibility(
          visible: allData.isEmpty,
          child: buildNoData(),
        ),
        ListView.builder(
          padding: EdgeInsets.only(bottom: 5.r),
          physics: const AlwaysScrollableScrollPhysics(),
          itemCount: allData.length,
          itemBuilder: (context, index) {
            var carData = allData[index];
            // String state = carData.status == VirtualCardTransStatus.finish.index
            //     ? L.completed.tr
            //     : L.pending.tr;
            // Color color = carData.status == VirtualCardTransStatus.finish.index
            //     ? AppColors.colorFF808080
            //     : AppColors.colorFF3474d1;
            return RecordItem(
              title: carData,
              amount: "-12",
              time: "2023-05-05 18:50:00",
              status: "已支付",
              stateColor: Colors.blue,
              titleLabel: "${L.transaction_hash.tr}: ",
            );
          },
        ),
      ],
    );
    return w;
  }
}