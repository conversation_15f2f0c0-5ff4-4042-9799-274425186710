import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_metatel/app/modules/virtualcard/manager/add_card_dialog_controller.dart';
import 'package:flutter_metatel/app/modules/virtualcard/util/util.dart';
import 'package:flutter_metatel/app/widgets/app_bar_cus.dart';
import 'package:flutter_metatel/app/widgets/hide_keyboard_widget.dart';
import 'package:flutter_metatel/core/utils/util.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../core/languages/l.dart';
import '../../../../core/values/colors.dart';

class AddCardDialog extends StatefulWidget {
  const AddCardDialog({
    super.key,
  });

  @override
  State<AddCardDialog> createState() => _AddCardDialogState();
}

class _AddCardDialogState extends State<AddCardDialog> {
  final AddCardDialogController controller = Get.put(AddCardDialogController());

  @override
  void dispose() {
    Get.delete<AddCardDialogController>();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    var loading = false.obs;
    return HideKeyboard(
      child: Stack(
        children: [
          Scaffold(
            appBar: AppBarCommon().build(context, title: L.add_card_information.tr),
            body: Stack(
              children: [
                ListView(
                  padding: EdgeInsets.zero,
                  shrinkWrap: true,
                  children: [
                    _buildEditTextTitle(
                      L.recharge_amount.tr,
                      marginTop: 20,
                    ),
                    _buildEditText(
                      controller.edRechargeAmount,
                      hintText: L.recharge_amount_hint.tr,
                      inputFormatters: [
                        FilteringTextInputFormatter.allow(RegExp(r'[0-9.]')),
                        NumLengthInputFormatter(decimalLength: 2),
                      ],
                      textInputType: TextInputType.number,
                    ),
                    Container(
                      margin: const EdgeInsets.only(
                        left: 30,
                        right: 30,
                        top: 5,
                      ).r,
                      child: Text(
                        L.create_card_amount_input_hint.tr,
                        style: TextStyle(
                            fontSize: 10.sp, color: AppColors.colorFF808080),
                      ),
                    ),
                    _buildEditTextTitle(
                      L.card_segment.tr,
                    ),
                    Stack(
                      alignment: Alignment.centerLeft,
                      children: [
                        Builder(builder: (context) {
                          return Obx(() {
                            controller.edCardSegment.text =
                                controller.selectCarSegment.value;
                            return _buildEditText(
                              controller.edCardSegment,
                              hintText: L.card_segment_hint.tr,
                              marginTop: 0,
                              readOnly: true,
                              onTap: () {
                                controller.onSelectCarSegment(context);
                              },
                            );
                          });
                        }),
                        Positioned(
                            right: 30.r,
                            child: const Icon(Icons.keyboard_arrow_down_rounded))
                      ],
                    ),
                    _buildEditTextTitle(
                      L.chat_contact_note_first_name.tr,
                    ),
                    _buildEditText(
                      controller.edCtlFirstName,
                      hintText: L.chat_contact_note_first_name.tr,
                    ),
                    _buildEditTextTitle(
                      L.chat_contact_note_last_name.tr,
                    ),
                    _buildEditText(
                      controller.edCtlLastName,
                      hintText: L.chat_contact_note_last_name.tr,
                    ),
                    _buildEditTextTitle(
                      L.billing_address.tr,
                    ),
                    Container(
                      margin: const EdgeInsets.only(left: 20, right: 20, top: 10).r,
                      child: controller.buildCscPicker(),
                    ),
                    _buildEditText(
                      controller.edCtlPostCode,
                      hintText: L.postal_code.tr,
                    ),
                    _buildEditText(
                      controller.edCtlAddress1,
                      hintText: L.address_line1.tr,
                    ),
                    _buildEditText(
                      controller.edCtlAddress2,
                      hintText: L.address_line2.tr,
                    ),
                    Padding(
                      padding:
                      const EdgeInsets.symmetric(horizontal: 20, vertical: 35).r,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Flexible(
                            child: TextButton(
                              style: ButtonStyle(
                                fixedSize:
                                MaterialStateProperty.all(Size.fromHeight(40.h)),
                                shape: MaterialStateProperty.all(
                                  RoundedRectangleBorder(
                                    borderRadius:
                                    BorderRadius.all(Radius.circular(5.r).r).r,
                                  ),
                                ),
                                // overlayColor:
                                // MaterialStateProperty.all(Colors.transparent),
                                // foregroundColor:
                                // MaterialStateProperty.resolveWith((states) {
                                //   return states.contains(MaterialState.pressed)
                                //       ? Colors.black54
                                //       : Colors.black38;
                                // }),
                                backgroundColor: MaterialStateProperty.all(
                                    AppColors.colorFFF2F2F2),
                              ),
                              onPressed: () async {
                                loading.value=true;
                                await controller.randomGenerateCardInfo();
                                controller.buildCscPicker().currentCity =
                                    controller.virtualCardInfo.value.city;
                                controller.buildCscPicker().currentState =
                                    controller.virtualCardInfo.value.stateFull;
                                controller.cscPicketKey.currentState?.configDefault();
                                setState(() {});
                              },
                              child: Container(
                                height: 40.h,
                                alignment: Alignment.center,
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Text(
                                      L.randomly_generated.tr,
                                      style: TextStyle(
                                        color: AppColors.colorFF333333,
                                        fontSize: 14.sp,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                          SizedBox(
                            width: 10.w,
                          ),
                          Flexible(
                            child: TextButton(
                              style: ButtonStyle(
                                fixedSize:
                                MaterialStateProperty.all(Size.fromHeight(40.h)),
                                shape: MaterialStateProperty.all(
                                  RoundedRectangleBorder(
                                    borderRadius:
                                    BorderRadius.all(Radius.circular(5.r).r).r,
                                  ),
                                ),
                                // overlayColor:
                                // MaterialStateProperty.all(Colors.transparent),
                                // foregroundColor:
                                // MaterialStateProperty.resolveWith((states) {
                                //   return states.contains(MaterialState.pressed)
                                //       ? Colors.black54
                                //       : Colors.black38;
                                // }),
                                backgroundColor:
                                MaterialStateProperty.all(AppColors.appDefault),
                              ),
                              onPressed: () async {
                                loading.value=true;
                                await controller.submit();
                                loading.value=false;
                              },
                              child: Container(
                                height: 40.h,
                                alignment: Alignment.center,
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Text(
                                      L.submit.tr,
                                      style: TextStyle(
                                        color: AppColors.white,
                                        fontSize: 14.sp,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          Obx(
                () => Visibility(
              visible: loading.value,
              child: Container(
                decoration: const BoxDecoration(
                  color: Color.fromRGBO(0, 0, 0, 0.46),
                ),
                child: loadingView(),
              ),
            ),
          ),
        ],
      ),
    );
  }

  _buildEditText(
    TextEditingController edCtl, {
    String? hintText,
    double? marginLeft,
    double? marginRight,
    double? marginTop,
    bool? readOnly,
    GestureTapCallback? onTap,
    List<TextInputFormatter>? inputFormatters,
    TextInputType? textInputType,
  }) {
    return  Container(
      margin: EdgeInsets.only(
              left: marginLeft ?? 20,
              right: marginRight ?? 20,
              top: marginTop ?? 10)
          .r,
      decoration: BoxDecoration(
        color: AppColors.colorFFF2F2F2,
        borderRadius: const BorderRadius.all(Radius.circular(10)).r,
      ),
      child: TextField(
        readOnly: readOnly ?? false,
        inputFormatters: inputFormatters,
        controller: edCtl,
        textAlignVertical: TextAlignVertical.center,
        keyboardType: textInputType ?? TextInputType.multiline,
        style: TextStyle(
          fontSize: 14.sp,
        ),
        decoration: InputDecoration(
          contentPadding: const EdgeInsets.only(left: 14, right: 14).r,
          fillColor: AppColors.colorFFF2F2F2,
          hintText: hintText,
          hintMaxLines: 1,
          hintStyle: TextStyle(fontSize: 14.sp, color: AppColors.colorFF808080),
          border: OutlineInputBorder(
            borderRadius: const BorderRadius.all(Radius.circular(10)).r,
            borderSide: BorderSide.none,
          ),
        ),
        onTap: onTap,
      ),
    );
  }

  _buildEditTextTitle(String title, {double? marginTop}) {
    return Padding(
      padding: EdgeInsets.only(top: marginTop ?? 16, left: 20, right: 20).r,
      child: Row(
        children: [
          Text(
            title,
            style: TextStyle(
                color: AppColors.colorFF333333,
                fontSize: 14.sp,
                fontWeight: FontWeight.w500),
          ),
          Text(
            "*",
            style: TextStyle(
                color: AppColors.colorFFCD3A3A,
                fontSize: 14.sp,
                fontWeight: FontWeight.w500),
          )
        ],
      ),
    );
  }
}
