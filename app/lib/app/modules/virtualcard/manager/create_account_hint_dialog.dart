import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/modules/virtualcard/model/static_data.dart';
import 'package:flutter_metatel/app/widgets/pdf_view.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:get/get.dart';

import '../../../../core/languages/l.dart';
import '../../../../core/values/colors.dart';

class CreateVirtualAccountDialog extends StatefulWidget {
  const CreateVirtualAccountDialog({
    super.key,
  });

  @override
  State<CreateVirtualAccountDialog> createState() => _CreateVirtualAccountDialogState();
}

class _CreateVirtualAccountDialogState extends State<CreateVirtualAccountDialog> {
  final TapGestureRecognizer _registerRecognizer = TapGestureRecognizer();

  @override
  void dispose() {
    _registerRecognizer.dispose();
    super.dispose();
  }
  @override
  Widget build(BuildContext context) {
    var isCheckUserTerms = false.obs;
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Container(
          margin: const EdgeInsets.only(left: 16, right: 16).r,
          decoration: BoxDecoration(
              color: AppColors.white,
              borderRadius: const BorderRadius.all(Radius.circular(16)).r),
          child: Column(
            children: [
              Container(
                margin: const EdgeInsets.only(
                  // left: 20,
                  // right: 20,
                  top: 25,
                ).r,
                alignment: Alignment.center,
                child: Text(
                  L.whether_create_virtual_card_account.tr,
                  style: TextStyle(
                    color: AppColors.colorFF000000,
                    fontSize: 18.sp,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              Container(
                margin: EdgeInsets.only(top: 44.h, left: 20, right: 20).r,
                child: Text(
                  L.create_virtual_card_account_hint.tr,
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: AppColors.colorFF333333,
                    height: 1.5,
                  ),
                ),
              ),
              SizedBox(
                height: 15.h,
              ),
              Container(
                margin: const EdgeInsets.symmetric(horizontal: 20).r,
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(padding: const EdgeInsets.only(top: 1).r,child:   SizedBox(
                      width: 20.r,
                      height: 20.r,
                      child: Obx(
                            () => Checkbox(
                          value: isCheckUserTerms.value,
                          onChanged: (value) {
                            isCheckUserTerms.value = value ?? false;
                          },
                        ),
                      ),
                    ),),

                    SizedBox(
                      width: 5.w,
                    ),
                    Expanded(
                      child: RichText(
                        text: TextSpan(
                          text: L.has_read_3t_card_user_terms.tr,
                          style: TextStyle(
                            color: Colors.blue,
                            fontSize: 15.sp,
                          ),
                          recognizer: _registerRecognizer
                            ..onTap = () {
                              Get.to(
                                const PdfView(
                                  title: "3TCard User Terms",
                                  url: VirtualCardStaticData.userCardTermsUrl,
                                ),
                              );
                            },
                        ),
                      ),
                    )
                  ],
                ),
              ),
              SizedBox(
                height: 15.h,
              ),
              Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 20, vertical: 35).r,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Flexible(
                      child: TextButton(
                        style: ButtonStyle(
                          fixedSize:
                              MaterialStateProperty.all(Size.fromHeight(40.h)),
                          shape: MaterialStateProperty.all(
                            RoundedRectangleBorder(
                              borderRadius:
                                  BorderRadius.all(Radius.circular(5.r).r).r,
                            ),
                          ),
                          // overlayColor:
                          // MaterialStateProperty.all(Colors.transparent),
                          // foregroundColor:
                          // MaterialStateProperty.resolveWith((states) {
                          //   return states.contains(MaterialState.pressed)
                          //       ? Colors.black54
                          //       : Colors.black38;
                          // }),
                          backgroundColor: MaterialStateProperty.all(
                              AppColors.colorFFF2F2F2),
                        ),
                        onPressed: () {
                          SmartDialog.dismiss();
                        },
                        child: Container(
                          height: 40.h,
                          alignment: Alignment.center,
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Text(
                                L.cancel.tr,
                                style: TextStyle(
                                  color: AppColors.colorFF333333,
                                  fontSize: 14.sp,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                    SizedBox(
                      width: 10.w,
                    ),
                    Flexible(
                      child: Obx(
                        () {
                          MaterialStateProperty<Color?>? backgroundColor=MaterialStateProperty.all(
                              isCheckUserTerms.value?AppColors.appDefault:AppColors.colorFFF2F2F2);
                          return TextButton(
                            style: ButtonStyle(
                              fixedSize: MaterialStateProperty.all(
                                  Size.fromHeight(40.h)),
                              shape: MaterialStateProperty.all(
                                RoundedRectangleBorder(
                                  borderRadius:
                                      BorderRadius.all(Radius.circular(5.r).r)
                                          .r,
                                ),
                              ),
                              // overlayColor:
                              // MaterialStateProperty.all(Colors.transparent),
                              // foregroundColor:
                              // MaterialStateProperty.resolveWith((states) {
                              //   return states.contains(MaterialState.pressed)
                              //       ? Colors.black54
                              //       : Colors.black38;
                              // }),
                              backgroundColor: backgroundColor,
                            ),
                            onPressed: isCheckUserTerms.value
                                ? () {
                                    SmartDialog.dismiss(result: true);
                                  }
                                : null,
                            child: Container(
                              height: 40.h,
                              alignment: Alignment.center,
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Text(
                                    L.confirm.tr,
                                    style: TextStyle(
                                      color: isCheckUserTerms.value?AppColors.white:null,
                                      fontSize: 14.sp,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
