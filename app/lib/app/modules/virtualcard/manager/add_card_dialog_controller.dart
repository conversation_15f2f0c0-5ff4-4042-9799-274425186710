import 'dart:convert';

import 'package:csc_picker/csc_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_metatel/app/modules/virtualcard/api/api.dart';
import 'package:flutter_metatel/app/modules/virtualcard/api/model/resp/card_seg_list_resp.dart';
import 'package:flutter_metatel/app/modules/virtualcard/model/static_data.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:flutter_metatel/core/utils/util.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:get/get.dart';

import '../../../../core/languages/l.dart';
import '../../../../core/values/colors.dart';
import '../api/model/req.dart';
import '../model/virtual_card_default_list.dart';

class AddCardDialogController extends GetxController {
  final TextEditingController edCtlFirstName = TextEditingController();
  final TextEditingController edCtlLastName = TextEditingController();
  final TextEditingController edCtlAddress1 = TextEditingController();
  final TextEditingController edCtlAddress2 = TextEditingController();
  final TextEditingController edCtlPostCode = TextEditingController();
  final TextEditingController edRechargeAmount = TextEditingController();
  final TextEditingController edCardSegment = TextEditingController();
  Rx<VirtualCardDefault> virtualCardInfo = VirtualCardDefault().obs;
  var updateAll = false.obs;
  CSCPicker? _cscPicket;
  final GlobalKey<CSCPickerState> cscPicketKey = GlobalKey<CSCPickerState>();
  List<VirtualCardDefault>? virtualCardDefaultList;
  CarSegmentData? carSegmentData;
  var selectCarSegment="".obs;
  int rechargeAmount=0;
  @override
  void onReady() {
    getCardSegmentList();
    rootBundle.loadString('assets/lottie/v_card_random_info.json').then((s) {
      final jsonResult = jsonDecode(s.toString());
      virtualCardDefaultList = VirtualCardDefaultList.fromJson(jsonResult).data;
    });
    super.onReady();
  }

  @override
  void onClose() {
    edCtlFirstName.dispose();
    edCtlLastName.dispose();
    edCtlAddress1.dispose();
    edCtlAddress2.dispose();
    edCtlPostCode.dispose();
    edRechargeAmount.dispose();
    edCardSegment.dispose();
    super.onClose();
  }

  submit() async {
    try{
      rechargeAmount=(double.parse(edRechargeAmount.text)*100).toInt();
    }catch(e){
      rechargeAmount=0;
    }
    AppLogger.d("rechargeAmount:$rechargeAmount");
    bool canSubmit = submitCheck();
    if(!canSubmit){
      return;
    }
    var fName = edCtlFirstName.text;
    var lName = edCtlLastName.text;
    var address1 = edCtlAddress1.text;
    var address2 = edCtlAddress2.text;
    var postCode = edCtlPostCode.text;
    var cardSegment = edCardSegment.text;
    AddReq addReq = AddReq(
        firstName: fName,
        lastName: lName,
        cardSegment: cardSegment,
        city: virtualCardInfo.value.city,
        countryCode: virtualCardInfo.value.countryCode,
        state: virtualCardInfo.value.state,
        street: address1 + address2,
        zip: postCode,
        deposit: rechargeAmount);
    var response = await Get.find<VirtualCardApiProvider>().cardAdd(addReq);
    if (response.data?.code == 0000) {
      Get.back(result: true);
    } else if (response.data?.code == 0003) {
      toast(L.operation_failed_please_try_again.tr);
    } else {
      AppLogger.d("${response.data?.msg}");
      toast(L.operation_failed_please_contact_admin.tr);
      // if (response.body?.msg?.isNotEmpty ?? false) {
      //   toast(response.body?.msg ?? '');
      // }
    }
  }

  ///随机产生虚拟卡信息
  Future<VirtualCardDefault?> randomGenerateCardInfo() async {
    if (virtualCardDefaultList == null) {
      var s =
          await rootBundle.loadString('assets/lottie/v_card_random_info.json');
      final jsonResult = jsonDecode(s.toString());
      virtualCardDefaultList = VirtualCardDefaultList.fromJson(jsonResult).data;
    }
    if (virtualCardDefaultList?.isNotEmpty ?? false) {
      var index = randomInt(virtualCardDefaultList!.length);
      VirtualCardDefault randomCardInfo = virtualCardDefaultList![index];
      virtualCardInfo.value = randomCardInfo;
      virtualCardInfo.value.stateFull =
          VirtualCardStaticData.mapState[virtualCardInfo.value.state];
      edCtlFirstName.text = randomCardInfo.firstName ?? "";
      edCtlLastName.text = randomCardInfo.lastName ?? "";
      edCtlAddress1.text = randomCardInfo.street ?? "";
      edCtlPostCode.text = "${randomCardInfo.zip}";
      updateAll.value = !updateAll.value;
      return randomCardInfo;
    }
    return null;
  }

  getCardSegmentList() async {
    var data = await Get.find<VirtualCardApiProvider>().cardSegmentList();
    carSegmentData = data.data?.data;
    String segData = "${carSegmentData?.segData?[0].cardSegment??""}";
    if(segData.isNotEmpty){
      selectCarSegment.value=segData;
      edCardSegment.text=segData;
    }
  }

  CSCPicker buildCscPicker() {
    _cscPicket ??= CSCPicker(
      key: cscPicketKey,
      // disableCountry: true,
      currentCountry: "United States",
      currentCity: virtualCardInfo.value.city,
      currentState: virtualCardInfo.value.stateFull,
      layout: Layout.vertical,

      ///Enable disable state dropdown [OPTIONAL PARAMETER]
      showStates: true,

      /// Enable disable city drop down [OPTIONAL PARAMETER]
      showCities: true,

      ///Enable (get flag with country name) / Disable (Disable flag) / ShowInDropdownOnly (display flag in dropdown only) [OPTIONAL PARAMETER]
      flagState: CountryFlag.DISABLE,

      ///Dropdown box decoration to style your dropdown selector [OPTIONAL PARAMETER] (USE with disabledDropdownDecoration)
      dropdownDecoration: BoxDecoration(
        borderRadius: const BorderRadius.all(Radius.circular(10)).r,
        color: AppColors.colorFFF2F2F2,
      ),

      ///Disabled Dropdown box decoration to style your dropdown selector [OPTIONAL PARAMETER]  (USE with disabled dropdownDecoration)
      disabledDropdownDecoration: BoxDecoration(
        borderRadius: const BorderRadius.all(Radius.circular(10)).r,
        color: AppColors.colorFFF2F2F2,
      ),

      ///placeholders for dropdown search field
      countrySearchPlaceholder: L.country.tr,
      stateSearchPlaceholder: L.state_county_province_or_region.tr,
      citySearchPlaceholder: L.city.tr,

      ///labels for dropdown
      countryDropdownLabel: L.country.tr,
      stateDropdownLabel: L.state_county_province_or_region.tr,
      cityDropdownLabel: L.city.tr,

      ///Default Country
      ///defaultCountry: CscCountry.India,

      ///Country Filter [OPTIONAL PARAMETER]
      countryFilter: const [CscCountry.United_States],

      ///Disable country dropdown (Note: use it with default country)
      //disableCountry: true,

      ///selected item style [OPTIONAL PARAMETER]
      selectedItemStyle: TextStyle(
        color: Colors.black,
        fontSize: 14.sp,
      ),

      ///DropdownDialog Heading style [OPTIONAL PARAMETER]
      dropdownHeadingStyle: TextStyle(
        color: Colors.black,
        fontSize: 17.sp,
        fontWeight: FontWeight.bold,
      ),

      ///DropdownDialog Item style [OPTIONAL PARAMETER]
      dropdownItemStyle: TextStyle(
        color: Colors.black,
        fontSize: 14.sp,
      ),

      ///Dialog box radius [OPTIONAL PARAMETER]
      dropdownDialogRadius: 10.0,

      ///Search bar radius [OPTIONAL PARAMETER]
      searchBarRadius: 10.0,

      ///triggers once country selected in dropdown
      onCountryChanged: (value) {
        ///store value in country variable

        virtualCardInfo.value.countryCode = VirtualCardStaticData.stateCountry(value);
      },

      ///triggers once state selected in dropdown
      onStateChanged: (value) {
        ///store value in state variable
        if (value?.isNotEmpty ?? false) {
          virtualCardInfo.value.stateFull = value;
          virtualCardInfo.value.state = VirtualCardStaticData.stateCode(value!);
        }
      },

      ///triggers once city selected in dropdown
      onCityChanged: (value) {
        ///store value in city variable
        virtualCardInfo.value.city = value;
      },
    );
    return _cscPicket!;
  }

  onSelectCarSegment(BuildContext ctx) async {
    if(carSegmentData?.segData?.isNotEmpty??false){
      String? carSegment=await SmartDialog.showAttach(
          targetContext: ctx,
          maskColor: AppColors.transparent,
          builder: (c) {
            return Padding(
              padding: const EdgeInsets.only(
                left: 25,
                right: 25,
              ).r,
              child: ListView.builder(
                padding: EdgeInsets.zero,
                shrinkWrap: true,
                itemBuilder: (cc, index) {
                  var segData = carSegmentData?.segData?[index];
                  return Material(
                    color: AppColors.white,
                    child: InkWell(
                      onTap: () {
                        SmartDialog.dismiss(
                            result: "${segData?.cardSegment ?? ""}");
                      },
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                            vertical: 15, horizontal: 15)
                            .r,
                        child: Text("${segData?.cardSegment ?? ""}"),
                      ),
                    ),
                  );
                },
                itemCount: carSegmentData?.segData?.length,
              ),
            );
          });
      if(carSegment?.isNotEmpty??false){
        selectCarSegment.value=carSegment!;
      }
    }else{
      getCardSegmentList();
    }
  }

  bool submitCheck(){
    bool ret=false;
    if(edRechargeAmount.text.isEmpty){
      toast(L.recharge_amount_must_be_entered.tr);
      return ret;
    }
    if(rechargeAmount<1000){
      toast(L.card_opening_amount_must_not_be_less_than_10.tr);
      return ret;
    }
    if(edCtlFirstName.text.isEmpty){
      toast(L.first_name_must_be_entered.tr);
      return ret;
    }
    if(edCtlLastName.text.isEmpty){
      toast(L.last_name_must_be_entered.tr);
      return ret;
    }
    if(_cscPicket?.currentState?.isEmpty??true){
      toast(L.state_must_be_selected.tr);
      return ret;
    }
    if(_cscPicket?.currentCity?.isEmpty??true){
      toast(L.city_must_be_selected.tr);
      return ret;
    }
    if(_cscPicket?.currentCountry?.isEmpty??true){
      toast(L.country_must_be_selected.tr);
      return ret;
    }
    if(edCtlPostCode.text.isEmpty){
      toast(L.post_code_must_be_entered.tr);
      return ret;
    }
    if(edCtlAddress1.text.isEmpty&&edCtlAddress2.text.isEmpty){
      toast(L.address_less_one.tr);
      return ret;
    }
    return ret=true;
  }
}
