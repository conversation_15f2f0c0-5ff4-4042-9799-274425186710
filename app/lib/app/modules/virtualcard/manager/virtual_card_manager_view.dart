import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_metatel/app/modules/virtualcard/detail/virtual_card_detail_view.dart';
import 'package:flutter_metatel/app/modules/virtualcard/manager/virtual_card_manager_controller.dart';
import 'package:flutter_metatel/app/modules/virtualcard/model/virtual_card_model.dart';
import 'package:flutter_metatel/app/modules/virtualcard/util/util.dart';
import 'package:flutter_metatel/app/widgets/text_middle_overlow.dart';
import 'package:flutter_metatel/core/values/config.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

import '../../../../core/languages/l.dart';
import '../../../../core/utils/app_log.dart';
import '../../../../core/utils/util.dart';
import '../../../../core/values/colors.dart';
import '../../../../r.dart';
import '../../../widgets/app_bar_cus.dart';
import '../../../widgets/divider_cus.dart';
import 'add_card_dialog.dart';
import 'create_account_hint_dialog.dart';

class VirtualCardManagerView extends StatefulWidget {
  const VirtualCardManagerView({super.key});

  @override
  State<StatefulWidget> createState() => _VirtualCardManagerState();
}

class _VirtualCardManagerState extends State<VirtualCardManagerView> {
  VirtualCardManagerController controller =
      Get.put(VirtualCardManagerController());

  @override
  void dispose() {
    Get.delete<VirtualCardManagerController>();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Stack(
        children: [
          Image.asset(
            R.bgMining,
            width: double.infinity,
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              AppBarCommon().build(
                context,
                systemUiOverlayStyle: SystemUiOverlayStyle.light
                    .copyWith(statusBarColor: Colors.transparent),
                leading: const BackButton(
                  color: AppColors.white,
                ),
                titleColor: AppColors.white,
                title: L.virtual_card.tr,
                backgroundColor: AppColors.transparent,
              ),

              ///基本信息
              Container(
                height: 176.r,
                width: double.infinity,
                margin: EdgeInsets.only(left: 16.r, right: 16.r, top: 20.r),
                decoration: BoxDecoration(
                  image: DecorationImage(
                    image: AssetImage(R.bgVirtualAccount),
                    fit: BoxFit.fill,
                  ),
                ),
                child: Obx(() {
                  return (controller.needCreate.value == 0 ||
                          controller.needCreate.value == -1)
                      ? _createAccount()
                      : _accountInfo();
                }),
              ),
              Visibility(
                visible: true /*controller.needCreate.value == 1*/,
                child: Expanded(
                  child: Column(
                    children: [
                      ///卡列表
                      Container(
                        height: 30.r,
                        alignment: Alignment.center,
                        margin: const EdgeInsets.only(top: 30, bottom: 13).r,
                        padding: const EdgeInsets.only(left: 15, right: 15).r,
                        child: Row(
                          children: [
                            Obx(
                              () {
                                return Text(
                                  "${L.card_list.tr}(${L.can_create_card_amount.tr}: ${controller.maxCarNum.value})",
                                  style: TextStyle(
                                    color: AppColors.colorFF000000,
                                    fontSize: 16.sp,
                                    fontWeight: FontWeight.bold,
                                  ),
                                );
                              },
                            ),
                            const Spacer(),
                            Obx(() {
                              return TextButton.icon(
                                style: ButtonStyle(
                                  fixedSize: MaterialStateProperty.all(
                                    Size.fromHeight(
                                      30.r,
                                    ),
                                  ),
                                  padding: MaterialStateProperty.all(
                                      EdgeInsets.zero),
                                ),
                                onPressed: (controller.needCreate.value == 0||controller.needCreate.value == -1||controller.status.value!=VirtualCardAccountStatus.normal.index)
                                    ? null
                                    : () {
                                        if (controller.maxCarNum.value > 0) {
                                          Get.to(
                                            const AddCardDialog(),
                                          )?.then((value) {
                                            if (value == true) {
                                              controller.refreshData();
                                            }
                                          });
                                        } else {
                                          toast(L.can_not_create_card_max.tr);
                                        }
                                      },
                                icon: Icon(
                                  Icons.add,
                                  size: 25.r,
                                ) /* Image.asset(
                                      R.icoVirtualCardAdd,
                                      width: 15.r,
                                      height: 15.r,
                                    )*/
                                ,
                                label: Text(
                                  L.add.tr,
                                  style: TextStyle(
                                      fontSize: 14.sp,
                                      // color: AppColors.colorFF3474d1,
                                      fontWeight: FontWeight.w500),
                                ),
                              );
                            }),
                          ],
                        ),
                      ),
                      const DividerCus(
                        thickness: 1,
                      ),
                      Expanded(
                        child: RefreshIndicator(
                          displacement: 5,
                          onRefresh: () async {
                            await controller.refreshData();
                          },
                          notificationPredicate: (_) {
                            return true;
                          },
                          child: Obx(() => _loadCardListView()),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _loadCardListView() {
    AppLogger.d("_loadCardListView ${controller.loadType.value}");
    var allData = controller.carList;
    Widget w;
    if (controller.isLoadError()) {
      w = loadErrorView(reloading: controller.refreshData);
    }else if (controller.isLoading()) {
      w = loadingView();
    } else {
      w = Stack(
        children: [
          Visibility(
            visible: allData.isEmpty,
            child: buildNoData(),
          ),
          ListView.builder(
            controller: controller.scrollController,
            padding: EdgeInsets.only(bottom: 5.r),
            physics: const AlwaysScrollableScrollPhysics(),
            itemCount: allData.length,
            itemBuilder: (context, index) {
              var cardData = allData[index];
              String state = getCardStateText(
                  VirtualCardStatus.values[cardData.state ?? 0]);
              String createTime = cardData.createTime ?? "0";
              try {
                int time = int.parse(createTime) * 1000;
                createTime = DateFormat("yyyy-MM-dd HH:mm:ss")
                    .format(DateTime.fromMillisecondsSinceEpoch(time));
              } catch (e) {
                AppLogger.d("$e");
              }
              Color color = cardData.state == VirtualCardStatus.using.index
                  ? AppColors.colorFF0fdba3
                  : AppColors.colorFF3474d1;
              String s = cardData.cardNo ?? "";
              var carNum = s.isEmpty ? L.no_yet.tr : getCarNumIncomplete(s);
              return Padding(
                  padding: const EdgeInsets.symmetric(vertical: 10).r,
                  child: Material(
                    color: AppColors.transparent,
                    child: InkWell(
                      onTap: () {
                        Get.to(const VirtualCardDetailView(),
                            arguments: cardData);
                      },
                      child: Container(
                        height: 44.h,
                        padding: const EdgeInsets.only(
                                left: 16, top: 2, right: 16, bottom: 2)
                            .r,
                        child: Row(
                          children: [
                            Image.asset(R.icoVirtualCard),
                            SizedBox(
                              width: 14.5.w,
                            ),
                            Text(
                              carNum,
                              style: TextStyle(
                                color: AppColors.colorFF333333,
                                fontSize: 14.sp,
                              ),
                            ),
                            Expanded(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                crossAxisAlignment: CrossAxisAlignment.end,
                                children: [
                                  Row(
                                    mainAxisSize: MainAxisSize.min,
                                    mainAxisAlignment: MainAxisAlignment.end,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    children: [
                                      CircleAvatar(
                                        radius: 4.0.r,
                                        backgroundColor: color,
                                      ),
                                      SizedBox(
                                        width: 8.5.w,
                                      ),
                                      Text(
                                        state,
                                        style: TextStyle(
                                          color: color,
                                          fontSize: 12.sp,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                    ],
                                  ),
                                  SizedBox(
                                    height: 8.h,
                                  ),
                                  Text(
                                    createTime,
                                    style: TextStyle(
                                      color: AppColors.colorFF666666,
                                      fontSize: 12.sp,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ));
            },
          ),
        ],
      );
    }
    return w;
  }

  ///眼睛按钮
  Widget _buildViewButton() {
    return Obx(
      () => Padding(
        padding:
            EdgeInsets.only(bottom: controller.valueVisible.value ? 0 : 17).r,
        child: Image.asset(
          controller.valueVisible.value
              ? "assets/images/ico_eye.png"
              : "assets/images/ico_eye_close.png",
          package: 'flutter_web3',
          width: 17.w,
        ),
      ),
    );
  }

  Widget _createAccount() {
    return Stack(
      alignment: Alignment.center,
      children: [
        Positioned(
          top: 0,
          left: 0,
          child: Container(
            margin: const EdgeInsets.only(left: 20, right: 20, top: 20).r,
            child: Row(
              children: [
                Text(
                  L.base_account.tr,
                  style: TextStyle(
                    color: AppColors.white,
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ),
        Visibility(
          visible: controller.needCreate.value == 0,
          child: TextButton(
            onPressed: () async {
              bool? ret = await SmartDialog.show(builder: (context) {
                return const CreateVirtualAccountDialog();
              });
              if (ret ?? false) {
                controller.crateAccount();
              }
            },
            style: ButtonStyle(
                backgroundColor:
                    MaterialStateProperty.all(AppColors.colorFFE5EBFF),
                shape: MaterialStateProperty.all(
                  RoundedRectangleBorder(
                    borderRadius: const BorderRadius.all(Radius.circular(18)).r,
                  ),
                ),
                fixedSize: MaterialStateProperty.all(Size(155.w, 36.h))),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Image.asset(
                //   R.icoBindRecommender,
                //   width: 12.r,
                //   height: 12.r,
                // ),
                // SizedBox(
                //   width: 5.w,
                // ),
                Text(
                  L.open_an_account.tr,
                  style: TextStyle(
                    color: AppColors.colorFF2D58C9,
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _accountInfo() {
    return Stack(
      children: [
        Positioned(
          right: 0,
          bottom: 26.h,
          child: Obx(
            () => TextButton(
              style: ButtonStyle(
                backgroundColor:controller.status.value ==
                    VirtualCardAccountStatus.normal.index?MaterialStateProperty.all(Colors.white): MaterialStateProperty.all(AppColors.white),
                shape: MaterialStateProperty.all(
                  RoundedRectangleBorder(
                      borderRadius: const BorderRadius.only(
                              topLeft: Radius.circular(20),
                              bottomLeft: Radius.circular(20))
                          .r),
                ),
                alignment: Alignment.center,
              ),
              onPressed: controller.status.value ==
                      VirtualCardAccountStatus.normal.index
                  ? () {
                      controller.onRecharge();
                    }
                  : null,
              child: Text(
                L.recharge.tr,
                style: TextStyle(
                  color: controller.status.value ==
                      VirtualCardAccountStatus.normal.index?AppColors.colorFF3474d1:AppColors.txtColor,
                ),
              ),
            ),
          ),
        ),
        Container(
          margin: const EdgeInsets.only(left: 20, right: 20, top: 20).r,
          child: Row(
            children: [
              Text(
                L.base_account.tr,
                style: TextStyle(
                  color: AppColors.white,
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w500,
                ),
              ),
              Expanded(
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.end,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Obx(() {
                      var color = controller.status.value ==
                              VirtualCardAccountStatus.normal.index
                          ? AppColors.colorFF0fdba3
                          : Colors.grey;
                      return CircleAvatar(
                        radius: 4.0.r,
                        backgroundColor: color,
                      );
                    }),
                    SizedBox(
                      width: 8.5.w,
                    ),
                    Obx(() {
                      String state = getBaseAccountStateText(
                          VirtualCardAccountStatus
                              .values[controller.status.value]);
                      return Text(
                        state,
                        style: TextStyle(
                          color: AppColors.white,
                          fontSize: 12.sp,
                          fontWeight: FontWeight.w500,
                        ),
                      );
                    }),
                  ],
                ),
              ),
            ],
          ),
        ),
        Container(
          margin: const EdgeInsets.only(left: 20, right: 20, top: 64).r,
          child: GestureDetector(
            onTap: () {
              controller.valueVisible.value = !controller.valueVisible.value;
            },
            child: Obx(
              () => Container(
                color: Colors.transparent,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    ConstrainedBox(
                      constraints: BoxConstraints(maxWidth: 300.w),
                      child: Text(
                        controller.valueVisible.value
                            ? "${controller.balance.value}" /*decimalData(
                            controller.balance.value, 8)*/
                            : "*********",
                        style: TextStyle(
                            fontSize: 40.sp,
                            fontWeight: FontWeight.w500,
                            color: AppColors.white),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    SizedBox(
                      width: 10.w,
                    ),
                    _buildViewButton(),
                    SizedBox(
                      width: 15.w,
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
        Positioned(
          bottom: 20.h,
          left: 16.r,
          child: Row(
            children: [
              Text(
                "${L.account_name.tr}:",
                style: TextStyle(
                  color: AppColors.colorFFcce0ff,
                  fontSize: 12.sp,
                ),
              ),
              SizedBox(
                width: 180.w,
                child: MiddleText(
                  Config.userNameWithoutDomain,
                  WXTextOverflow.ellipsisMiddle,
                  style: TextStyle(
                    color: AppColors.colorFFcce0ff,
                    fontSize: 12.sp,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
