
import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/modules/virtualcard/api/api.dart';
import 'package:flutter_metatel/app/modules/virtualcard/model/static_data.dart';
import 'package:flutter_metatel/app/modules/virtualcard/model/virtual_card_model.dart';
import 'package:flutter_metatel/core/languages/l.dart';
import 'package:flutter_metatel/core/utils/util.dart';
import 'package:flutter_web3/app/modules/home/<USER>';
import 'package:get/get.dart';

import '../../../../core/values/config.dart';
import '../api/model/resp/virtual_card_list_resp.dart';

class VirtualCardManagerController extends GetxController {
  var needCreate=(-1).obs; ///-1啥都不显示 0->需要开通账户 1->显示获取账户数据
  var valueVisible = false.obs;
  var balance = 0.0.obs;
  var address = "".obs;
  var status = VirtualCardAccountStatus.none.index.obs;
  var maxCarNum=0.obs;
  var hasCarNum=0.obs;
  VirtualCardAccountModel? accountInfo;
  RxList<CarListItemInfo> carList = RxList();
  RxInt loadType = LoadType.defaultState.obs;
  int pageSize=2;
  int pageNumber =1;
  late ScrollController scrollController;
  final VirtualCardApiProvider virtualCardApi=Get.find<VirtualCardApiProvider>();
  refreshData({bool refresh=true}) async {
    if(needCreate.value==-1){
      isRegister();
      return;
    }
    try{
      var login = await loginAccount();
      if(login){
        loadAccountInfo();
        loadCardList(refresh: refresh);
        dismissLoadingDialog();
      }else {
        status.value=VirtualCardAccountStatus.failed.index;
        dismissLoadingDialog();
      }
    }catch(e){
      status.value = VirtualCardAccountStatus.failed.index;
      dismissLoadingDialog();
      e.printError();
    }

  }
  @override
  void onInit() {
    scrollController = ScrollController();
    scrollController.addListener(() {
      if (scrollController.position.pixels ==
          scrollController.position.maxScrollExtent) {
        if (pageSize == carList.length~/pageNumber) {
          refreshData(refresh: false);
        }
      }
    });
    super.onInit();
  }
  @override
  void onReady() {
    showLoadingDialog();
    isRegister();
    super.onReady();
  }
  bool isLoadError() {
    return loadType.value == LoadType.error;
  }
  bool isLoading() {
    return loadType.value == LoadType.loading;
  }
  loadAccountInfo() async {
    var data = await Get.find<VirtualCardApiProvider>().virtualCardAccountInfo(/*cardId: cardId*/);
    balance.value=data.data?.data?.accountBal??0.0;
    maxCarNum.value=data.data?.data?.openCard??0;
    int? state=data.data?.data?.userState;
    if(state!=null){
      status.value=state;
    }

  }
  loadCardList({bool refresh=true}) async {
    if(needCreate.value==0){
      return;
    }
    if(refresh){
      loadType.value = LoadType.loading;
      pageNumber=1;
      pageSize=2;
    }else{
      pageNumber++;
    }
    var data = await Get.find<VirtualCardApiProvider>().virtualCardList(pageSize: pageSize,pageNum: pageNumber,status: 0);
    if(refresh){
      carList.clear();
    }
    if (data.data != null) {
      var cardList = data.data?.data?.data;
      if(cardList?.isNotEmpty??false){
        carList.addAll(cardList!);
      }
      loadType.value=LoadType.success;
      hasCarNum.value=data.data?.data?.count??0;
    }else{
      loadType.value=LoadType.error;
    }
  }
  Future<bool> crateAccount() async {
    var ret = await virtualCardApi.createVirtualAccount();
    if(ret.data?.code==0000){
       needCreate.value=1;
       refreshData();
       return true;
    }else if(ret.data?.code==0112){
      toast(L.virtual_open_failed_toast.tr);
    }else {
      toast(L.open_an_account_failed.tr);
    }
    return false;
  }
  Future<bool> loginAccount() async {
    var ret =await virtualCardApi.loginVirtualAccount();
    if(ret.data?.code==0000){
      needCreate.value=1;
      return true;
    }
    return false;
  }
  Future<bool> isRegister() async {
    var ret =await virtualCardApi.isRegister();
    if(ret.data?.code==0000){
      int isRegister=ret.data?.data?.isRegister??0;
      needCreate.value=isRegister;
      // needCreate.value=0;
      if(needCreate.value==1){
        refreshData();
      }else{
        dismissLoadingDialog();
      }
      return isRegister==1;
    }else {
      status.value=VirtualCardAccountStatus.failed.index;
      dismissLoadingDialog();
      if(ret.statusCode==-200){
        loadType.value = LoadType.error;
      }
    }
    return false;
  }
  onRecharge(){
    if(VirtualCardStaticData.rechargeDappUrl?.isNotEmpty??false){
      Get.to(
        BrowserPage(
          homeUrl: VirtualCardStaticData.rechargeDappUrl,
        ),
      )?.then((value){
        refreshData();
      });
    }
  }
}
