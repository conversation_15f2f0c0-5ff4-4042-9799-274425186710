import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/modules/virtualcard/detail/trans_out_card_dialog.dart';
import 'package:flutter_metatel/app/modules/virtualcard/detail/virtual_card_detail_controller.dart';
import 'package:flutter_metatel/app/modules/virtualcard/util/util.dart';
import 'package:flutter_metatel/app/widgets/app_bar_cus.dart';
import 'package:flutter_metatel/app/widgets/divider_cus.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:get/get.dart';

import '../../../../core/languages/l.dart';
import '../../../../core/utils/util.dart';
import '../../../../core/values/colors.dart';
import '../../../../r.dart';
import '../api/model/resp/virtual_card_info_rep.dart';
import '../model/virtual_card_model.dart';
import '../record_item.dart';
import 'del_card_dialog.dart';
import 'virtual_card_detail__more_view.dart';

class VirtualTradeDetailView extends StatefulWidget {
  VirtualTradeDetailView(this.mbUserCardTrade, {super.key});

  MbUserCardTrade mbUserCardTrade;

  @override
  State<StatefulWidget> createState() => _VirtualCardDetailState();
}

class _VirtualCardDetailState extends State<VirtualTradeDetailView> {
  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      appBar: AppBarCommon().build(
        context,
        title: L.trade_detail.tr,
      ),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const DividerCus(),
          _buildCardInfo(),
        ],
      ),
    );
  }

  _buildCardInfo() {
    MbUserCardTrade trade = widget.mbUserCardTrade;
    String state = trade.status == VirtualCardTransStatus.finish.index
        ? L.completed.tr
        : L.pending.tr;
    String _tradeType = trade.tradeType == 1 ? '+' : '-';
    String type = trade.tradeType == 0
        ? L.transfer_in.tr
        : trade.tradeType == 1
            ? L.transfer_in.tr
            : L.consumption.tr;
    return Container(
      margin: const EdgeInsets.only(left: 25, right: 25, top: 30).r,
      decoration: BoxDecoration(
          color: AppColors.colorFFf5f7fa,
          borderRadius: const BorderRadius.all(Radius.circular(10)).r),
      padding: const EdgeInsets.symmetric(vertical: 14, horizontal: 25).r,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Center(
            child: Text('$_tradeType ${trade.money}',
                style: TextStyle(
                  color: AppColors.colorFF333333,
                  fontSize: 25.sp,
                )),
          ),
          SizedBox(
            height: 25.h,
          ),
          Row(
            children: [
              Text(
                L.trade_status.tr,
                style: TextStyle(
                  color: AppColors.colorFF666666,
                  fontSize: 12.sp,
                ),
              ),
              Text(
                state,
                style: TextStyle(
                  color: AppColors.colorFF333333,
                  fontSize: 12.sp,
                ),
              ),
            ],
          ),
          SizedBox(
            height: 10.h,
          ),
          Row(
            children: [
              Text(
                L.trade_id.tr,
                style: TextStyle(
                  color: AppColors.colorFF666666,
                  fontSize: 12.sp,
                ),
              ),
              Text(
                trade.tradeId ?? '',
                style: TextStyle(
                  color: AppColors.colorFF333333,
                  fontSize: 12.sp,
                ),
              ),
            ],
          ),
          SizedBox(
            height: 10.h,
          ),
          Row(
            crossAxisAlignment:CrossAxisAlignment.start,
            children: [
              Text(
                L.transaction_description.tr,
                style: TextStyle(
                  color: AppColors.colorFF666666,
                  fontSize: 12.sp,
                ),
              ),
              Expanded(
                  child: Text(
                trade.description ?? '',
                style: TextStyle(
                  color: AppColors.colorFF333333,
                  fontSize: 12.sp,
                ),
              )),
            ],
          ),
          SizedBox(
            height: 10.h,
          ),
          Row(
            children: [
              Text(
                L.pre_authorization_amount.tr,
                style: TextStyle(
                  color: AppColors.colorFF666666,
                  fontSize: 12.sp,
                ),
              ),
              Text(
                '${trade.preAuthAmount ?? ''}',
                style: TextStyle(
                  color: AppColors.colorFF333333,
                  fontSize: 12.sp,
                ),
              ),
            ],
          ),
          SizedBox(
            height: 10.h,
          ),
          Row(
            children: [
              Text(
                L.pre_authorization_currency.tr,
                style: TextStyle(
                  color: AppColors.colorFF666666,
                  fontSize: 12.sp,
                ),
              ),
              Text(
                trade.preAuthCurrency ?? '',
                style: TextStyle(
                  color: AppColors.colorFF333333,
                  fontSize: 12.sp,
                ),
              ),
            ],
          ),
          SizedBox(
            height: 10.h,
          ),
          Row(
            children: [
              Text(
                L.currency.tr,
                style: TextStyle(
                  color: AppColors.colorFF666666,
                  fontSize: 12.sp,
                ),
              ),
              Text(
                trade.currency ?? '',
                style: TextStyle(
                  color: AppColors.colorFF333333,
                  fontSize: 12.sp,
                ),
              ),
            ],
          ),
          SizedBox(
            height: 10.h,
          ),
          Row(
            children: [
              Text(
                L.trade_type.tr,
                style: TextStyle(
                  color: AppColors.colorFF666666,
                  fontSize: 12.sp,
                ),
              ),
              Text(
                type,
                style: TextStyle(
                  color: AppColors.colorFF333333,
                  fontSize: 12.sp,
                ),
              ),
            ],
          ),
          SizedBox(
            height: 10.h,
          ),
          Row(
            children: [
              Text(
                L.third_party_transaction_status.tr,
                style: TextStyle(
                  color: AppColors.colorFF666666,
                  fontSize: 12.sp,
                ),
              ),
              Text(
                trade.tradeStatus ?? '',
                style: TextStyle(
                  color: AppColors.colorFF333333,
                  fontSize: 12.sp,
                ),
              ),
            ],
          ),
          SizedBox(
            height: 10.h,
          ),
          Row(
            children: [
              Text(
                L.balance_at_time_of_change.tr,
                style: TextStyle(
                  color: AppColors.colorFF666666,
                  fontSize: 12.sp,
                ),
              ),
              Text(
                '${trade.balance ?? ''}',
                style: TextStyle(
                  color: AppColors.colorFF333333,
                  fontSize: 12.sp,
                ),
              ),
            ],
          ),
          SizedBox(
            height: 10.h,
          ),
          Row(
            children: [
              Text(
                L.create_time.tr,
                style: TextStyle(
                  color: AppColors.colorFF666666,
                  fontSize: 12.sp,
                ),
              ),
              Text(
                trade.createTime ?? '',
                style: TextStyle(
                  color: AppColors.colorFF333333,
                  fontSize: 12.sp,
                ),
              ),
            ],
          ),
          SizedBox(
            height: 10.h,
          ),
        ],
      ),
    );
  }
}
