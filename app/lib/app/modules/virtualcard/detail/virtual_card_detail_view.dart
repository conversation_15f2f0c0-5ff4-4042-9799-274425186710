import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/modules/virtualcard/detail/trans_out_card_dialog.dart';
import 'package:flutter_metatel/app/modules/virtualcard/detail/virtual_card_detail_controller.dart';
import 'package:flutter_metatel/app/modules/virtualcard/util/util.dart';
import 'package:flutter_metatel/app/widgets/app_bar_cus.dart';
import 'package:flutter_metatel/app/widgets/divider_cus.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:get/get.dart';

import '../../../../core/languages/l.dart';
import '../../../../core/utils/util.dart';
import '../../../../core/values/colors.dart';
import '../../../../r.dart';
import '../model/virtual_card_model.dart';
import '../record_item.dart';
import 'del_card_dialog.dart';
import 'virtual_card_detail__more_view.dart';
import 'virtual_trade_detail_view.dart';

class VirtualCardDetailView extends StatefulWidget {
  const VirtualCardDetailView({super.key});

  @override
  State<StatefulWidget> createState() => _VirtualCardDetailState();
}

class _VirtualCardDetailState extends State<VirtualCardDetailView> {
  final VirtualCardDetailController controller =
      Get.put(VirtualCardDetailController());

  @override
  void dispose() {
    Get.delete<VirtualCardDetailController>();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      appBar: AppBarCommon().build(context, title: L.virtual_card.tr, actions: [
        Obx((){
          Widget w=const SizedBox.shrink();
          var state = controller.rxCardInfo.value.state??0;
          if(state!=VirtualCardStatus.del.index){
            w=InkWell(
              onTap: () {
                SmartDialog.show(builder: (context) {
                  return DelCardDialog(
                    title: L.delete_card.tr,
                    content: L.delete_card_hint.tr,
                    confirmText:  L.chat_contact_del.tr,
                  );
                });
              },
              child: Container(
                alignment: Alignment.center,
                padding: const EdgeInsets.symmetric(horizontal: 10).r,
                child: Text(
                  L.chat_contact_del.tr,
                  style:
                  TextStyle(color: AppColors.bgButtonDefault, fontSize: 14.sp),
                ),
              ),
            );
          }
          return w;
        }),
      ]),
      body:Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const DividerCus(),
            Obx(() => _buildCard()),
            Obx(() => _buildCardInfo()),
            Container(
              margin: const EdgeInsets.only(left: 16, right: 16, top: 20).r,
              child: Row(
              children: [
                Text(
                  L.transaction_record.tr,
                  style: TextStyle(
                    color: AppColors.colorFF000000,
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: () {
                    Get.to(const VirtualCardDetailMoreView());
                  },
                  child: Text(L.more02.tr,
                      style: TextStyle(
                        color: AppColors.colorFF000000,
                        fontSize: 14.sp,
                        fontWeight: FontWeight.w500,
                      )),
                )
              ],
            ),
          ),
            SizedBox(
              height: 10.h,
            ),
            const DividerCus(),
            Expanded(
              child: RefreshIndicator(
                displacement: 5,
                onRefresh: () async {
                  await controller.refreshData();
                },
                notificationPredicate: (_) {
                  return true;
                },
                child: Obx(() => _loadTransRecordListView()),
              ),
            ),
            const DividerCus(),
          Obx(
            () {
              var state = controller.rxCardInfo.value.state ?? 0;
              return Visibility(
                visible: state != VirtualCardStatus.del.index,
                child: Padding(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 5).r,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      ///转出
                      Flexible(
                        child: TextButton(
                          style: ButtonStyle(
                            fixedSize: MaterialStateProperty.all(
                                Size.fromHeight(36.h)),
                            shape: MaterialStateProperty.all(
                              RoundedRectangleBorder(
                                borderRadius:
                                    BorderRadius.all(Radius.circular(5.r).r).r,
                              ),
                            ),
                            // overlayColor:
                            // MaterialStateProperty.all(Colors.transparent),
                            // foregroundColor:
                            // MaterialStateProperty.resolveWith((states) {
                            //   return states.contains(MaterialState.pressed)
                            //       ? Colors.black54
                            //       : Colors.black38;
                            // }),
                            backgroundColor:
                                MaterialStateProperty.all(AppColors.appDefault),
                          ),
                          onPressed: () {
                            SmartDialog.show(builder: (context) {
                              controller.edTransOut.text = "";
                              return CardInputInfoDialog(
                                title: L.transfer_out.tr,
                                content: L.transfer_out_hint.tr,
                                buttonText: L.backup_confirm.tr,
                                edCtl: controller.edTransOut,
                              );
                            });
                          },
                          child: Container(
                            height: 36.h,
                            alignment: Alignment.center,
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Text(
                                  L.transfer_out.tr,
                                  style: TextStyle(
                                    color: AppColors.white,
                                    fontSize: 14.sp,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                      SizedBox(
                        width: 10.w,
                      ),

                      ///划转
                      Flexible(
                        child: TextButton(
                          style: ButtonStyle(
                            fixedSize: MaterialStateProperty.all(
                                Size.fromHeight(36.h)),
                            shape: MaterialStateProperty.all(
                              RoundedRectangleBorder(
                                borderRadius:
                                    BorderRadius.all(Radius.circular(5.r).r).r,
                              ),
                            ),
                            // overlayColor:
                            // MaterialStateProperty.all(Colors.transparent),
                            // foregroundColor:
                            // MaterialStateProperty.resolveWith((states) {
                            //   return states.contains(MaterialState.pressed)
                            //       ? Colors.black54
                            //       : Colors.black38;
                            // }),
                            backgroundColor:
                                MaterialStateProperty.all(AppColors.appDefault),
                          ),
                          onPressed: () {
                            SmartDialog.show(builder: (context) {
                              controller.edTransfer.text = "";
                              return CardInputInfoDialog(
                                title: L.transfer.tr,
                                content: L.transfer_hint.tr,
                                buttonText: L.backup_confirm.tr,
                                edCtl: controller.edTransfer,
                              );
                            });
                          },
                          child: Container(
                            height: 36.h,
                            alignment: Alignment.center,
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Text(
                                  L.transfer.tr,
                                  style: TextStyle(
                                    color: AppColors.white,
                                    fontSize: 14.sp,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                      SizedBox(
                        width: 10.w,
                      ),

                      ///解冻
                      Flexible(
                        child: TextButton(
                          style: ButtonStyle(
                            fixedSize: MaterialStateProperty.all(
                                Size.fromHeight(36.h)),
                            shape: MaterialStateProperty.all(
                              RoundedRectangleBorder(
                                borderRadius:
                                    BorderRadius.all(Radius.circular(5.r).r).r,
                              ),
                            ),
                            // overlayColor:
                            // MaterialStateProperty.all(Colors.transparent),
                            // foregroundColor:
                            // MaterialStateProperty.resolveWith((states) {
                            //   return states.contains(MaterialState.pressed)
                            //       ? Colors.black54
                            //       : Colors.black38;
                            // }),
                            backgroundColor: MaterialStateProperty.all(
                                AppColors.colorFF303640),
                          ),
                          onPressed: () {
                            SmartDialog.show(builder: (context) {
                              return DelCardDialog(
                                title: L.unfreeze.tr,
                                content: L.unfreeze_hint.tr,
                                confirmText: L.unfreeze.tr,
                              );
                            });
                          },
                          child: Container(
                            height: 36.h,
                            alignment: Alignment.center,
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Text(
                                  L.unfreeze.tr,
                                  style: TextStyle(
                                    color: AppColors.white,
                                    fontSize: 14.sp,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        ],
        ),
    );
  }

  _buildCard() {
    return Container(
      height: 190.h,
      margin: const EdgeInsets.only(left: 16, right: 16, top: 16).r,
      decoration: BoxDecoration(
          color: AppColors.appDefault,
          image: DecorationImage(
              image: AssetImage(
                R.bgVirtualCard,
              ),
              fit: BoxFit.fill),
          borderRadius: const BorderRadius.all(Radius.circular(10)).r),
      child: Stack(
        children: [
          Positioned(
            right: 21.w,
            top: 15.h,
            child: Text(
              "${L.balance.tr}: ${controller.rxCardInfo.value.balance??0}",
              style: TextStyle(
                color: AppColors.white,
                fontSize: 14.sp,
              ),
            ),
          ),
          Positioned(
            left: 21.w,
            top: 90.h,
            child: Obx(
              () {
                String carNumber = controller.rxCardInfo.value.cardNo ??"";
                String carNumberSimple = L.no_yet.tr;
                if(carNumber.isEmpty){
                  carNumber=L.no_yet.tr;
                }else{
                  carNumberSimple=getCarNumIncomplete(carNumber);
                }
                return Container(
                  color: Colors.transparent,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      controller.valueVisible.value
                          ? ConstrainedBox(
                              constraints: BoxConstraints(maxWidth: 260.w),
                              child: SelectableText(
                                carNumber,
                                style: TextStyle(
                                  color: AppColors.white,
                                  fontSize: 20.sp,
                                ),
                                maxLines: 1,
                              ),
                            )
                          : ConstrainedBox(
                              constraints: BoxConstraints(maxWidth: 260.w),
                              child: Text(
                                carNumberSimple,
                                style: TextStyle(
                                  color: AppColors.white,
                                  fontSize: 20.sp,
                                ),
                                maxLines: 1,
                              ),
                            ),
                      SizedBox(
                        width: 10.w,
                      ),
                      _buildViewButton(),
                      SizedBox(
                        width: 15.w,
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
          Positioned(
            left: 21.w,
            top: 130.h,
            child: Obx(() {
              String exp=controller.rxCardInfo.value.expirationDate??"";
              return Row(
                children: [
                  Text(
                    "EXP: ",
                    style: TextStyle(
                      color: AppColors.white,
                      fontSize: 10.sp,
                    ),
                  ),
                  controller.valueVisible.value
                      ? SelectableText(
                          exp,
                          style: TextStyle(
                            color: AppColors.white,
                            fontSize: 10.sp,
                          ),
                        )
                      : Text(
                          "**/**",
                          style: TextStyle(
                            color: AppColors.white,
                            fontSize: 10.sp,
                          ),
                        ),
                  SizedBox(
                    width: 32.w,
                  ),
                  Text(
                    "CVV: ",
                    style: TextStyle(
                      color: AppColors.white,
                      fontSize: 10.sp,
                    ),
                  ),
                  controller.valueVisible.value
                      ? SelectableText(
                          controller.rxCardInfo.value.safetyCode??"",
                          style: TextStyle(
                            color: AppColors.white,
                            fontSize: 10.sp,
                          ),
                        )
                      : Text(
                          "***",
                          style: TextStyle(
                            color: AppColors.white,
                            fontSize: 10.sp,
                          ),
                        ),
                ],
              );
            }),
          ),
          Positioned(
            left: 21.w,
            bottom: 15.h,
            child: Text(
              controller.rxCardInfo.value.name??"",
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: TextStyle(
                color: AppColors.white,
                fontSize: 12.sp,
              ),
            ),
          ),
        ],
      ),
    );
  }

  _buildCardInfo() {
    VirtualCardStatus status = VirtualCardStatus.values[controller.rxCardInfo.value.state??0];
    String state=getCardStateText(VirtualCardStatus.values[controller.rxCardInfo.value.state??0]);
    Color color = status == VirtualCardStatus.using
        ? AppColors.colorFF0fdba3
        : AppColors.colorFF3474d1;
    return Container(
      // constraints: const BoxConstraints(maxHeight:60 ).r,
      margin: const EdgeInsets.only(left: 16, right: 16, top: 10).r,
      decoration: BoxDecoration(
          color: AppColors.colorFFf5f7fa,
          borderRadius: const BorderRadius.all(Radius.circular(10)).r),
      padding: const EdgeInsets.symmetric(vertical: 14, horizontal: 19).r,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Row(
            children: [
              Text(
                "${L.status.tr}: ",
                style: TextStyle(
                  color: AppColors.colorFF666666,
                  fontSize: 12.sp,
                ),
              ),
              Text(
                state,
                style: TextStyle(
                  color: color,
                  fontSize: 12.sp,
                ),
              ),
            ],
          ),
          SizedBox(
            height: 10.h,
          ),
          Row(
            children: [
              Text(
                "${L.address.tr} ",
                style: TextStyle(
                  color: AppColors.colorFF666666,
                  fontSize: 12.sp,
                ),
              ),
              Text(
                "${controller.rxCardInfo.value.cardAddress1 ?? ""}${controller.rxCardInfo.value.cardAddress2??''}",
                style: TextStyle(
                  color: AppColors.colorFF666666,
                  fontSize: 12.sp,
                ),
              ),
            ],
          ),
          SizedBox(
            height: 10.h,
          ),
          Row(
            children: [
              Text(
                "${L.time.tr}: ",
                style: TextStyle(
                  color: AppColors.colorFF666666,
                  fontSize: 12.sp,
                ),
              ),
              Text(
                controller.rxCardInfo.value.createTime??'',
                style: TextStyle(
                  color: AppColors.colorFF666666,
                  fontSize: 12.sp,
                ),
              ),
            ],
          ),
          SizedBox(
            height: 10.h,
          ),
          Row(
            children: [
              Text(
                "${L.postal_code.tr}: ",
                style: TextStyle(
                  color: AppColors.colorFF666666,
                  fontSize: 12.sp,
                ),
              ),
              Text(
                controller.rxCardInfo.value.zipCode??'',
                style: TextStyle(
                  color: AppColors.colorFF666666,
                  fontSize: 12.sp,
                ),
              ),
            ],
          ),SizedBox(
            height: 10.h,
          ),
          Row(
            children: [
              Text(
                "${L.city.tr}: ",
                style: TextStyle(
                  color: AppColors.colorFF666666,
                  fontSize: 12.sp,
                ),
              ),
              Text(
                controller.rxCardInfo.value.city??'',
                style: TextStyle(
                  color: AppColors.colorFF666666,
                  fontSize: 12.sp,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _loadTransRecordListView() {
    var allData = controller.recordList;
    Widget w;
    if (controller.isLoadError()) {
      w = loadErrorView(reloading: controller.refreshData);
    }else if (controller.isLoading()) {
      w = loadingView();
    } else {
      w = Stack(
        children: [
          Visibility(
            visible: allData.isEmpty,
            child: buildNoData(),
          ),
          ListView.builder(
            controller: controller.scrollController,
            padding: EdgeInsets.only(bottom: 5.r),
            physics: const AlwaysScrollableScrollPhysics(),
            itemCount: allData.length,
            itemBuilder: (context, index) {
              var carData = allData[index];
              String state =
                  carData.tradeStatus??''/* == VirtualCardTransStatus.finish.index
                      ? L.completed.tr
                      : L.pending.tr*/;
              Color color =
                  carData.status == VirtualCardTransStatus.finish.index
                      ? AppColors.colorFF808080
                      : AppColors.colorFF3474d1;
              String type = carData.tradeType ==0? L.transfer_in.tr:carData.tradeType ==1?L.transfer_in.tr:L.consumption.tr;
              String amount="$type ${carData.money}";
          AppLogger.d('tradeStatus =${carData.tradeStatus}');
              return RecordItem(
                title: carData.tradeId??"",
                amount:amount,
                time: carData.createTime??"",
                status: state,
                stateColor: color,
                changedCallback: (a) {
                  Get.to(VirtualTradeDetailView(carData));
                },
              );
            },
          ),
        ],
      );
    }
    return w;
  }

  ///眼睛按钮
  Widget _buildViewButton() {
    return Obx(
      () => GestureDetector(
        onTap: () {
          controller.valueVisible.value = !controller.valueVisible.value;
        },
        child: Container(
          color: Colors.transparent,
          padding: const EdgeInsets.all(10).r,
          child: Image.asset(
            controller.valueVisible.value
                ? "assets/images/ico_eye.png"
                : "assets/images/ico_eye_close.png",
            package: 'flutter_web3',
            width: 17.w,
          ),
        ),
      ),
    );
  }
}
