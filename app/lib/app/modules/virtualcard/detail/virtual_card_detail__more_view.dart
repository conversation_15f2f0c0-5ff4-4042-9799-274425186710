import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/modules/virtualcard/detail/trans_out_card_dialog.dart';
import 'package:flutter_metatel/app/modules/virtualcard/detail/virtual_card_detail_controller.dart';
import 'package:flutter_metatel/app/modules/virtualcard/util/util.dart';
import 'package:flutter_metatel/app/widgets/app_bar_cus.dart';
import 'package:flutter_metatel/app/widgets/divider_cus.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:get/get.dart';

import '../../../../core/languages/l.dart';
import '../../../../core/utils/util.dart';
import '../../../../core/values/colors.dart';
import '../../../../r.dart';
import '../model/virtual_card_model.dart';
import '../record_item.dart';
import 'del_card_dialog.dart';
import 'virtual_trade_detail_view.dart';

class VirtualCardDetailMoreView extends StatefulWidget {
  const VirtualCardDetailMoreView({super.key});

  @override
  State<StatefulWidget> createState() => _VirtualCardDetailState();
}

class _VirtualCardDetailState extends State<VirtualCardDetailMoreView> {
  final VirtualCardDetailController controller =
      Get.find<VirtualCardDetailController>();

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      appBar: AppBarCommon().build(
        context,
        title: L.transaction_record.tr,
      ),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            child: RefreshIndicator(
              displacement: 5,
              onRefresh: () async {
                await controller.refreshData();
              },
              notificationPredicate: (_) {
                return true;
              },
              child: Obx(() => _loadTransRecordListView()),
            ),
          ),
        ],
      ),
    );
  }

  Widget _loadTransRecordListView() {
    var allData = controller.recordList;
    Widget w;
    if (controller.isLoadError()) {
      w = loadErrorView(reloading: controller.refreshData);
    } else if (controller.isLoading()) {
      w = loadingView();
    } else {
      w = Stack(
        children: [
          Visibility(
            visible: allData.isEmpty,
            child: buildNoData(),
          ),
          ListView.builder(
            controller: controller.scrollControllerMore,
            padding: EdgeInsets.only(bottom: 5.r),
            physics: const AlwaysScrollableScrollPhysics(),
            itemCount: allData.length,
            itemBuilder: (context, index) {
              var carData = allData[index];
              String state = carData.tradeStatus ??
                      '' /* == VirtualCardTransStatus.finish.index
                      ? L.completed.tr
                      : L.pending.tr*/
                  ;
              Color color =
                  carData.status == VirtualCardTransStatus.finish.index
                      ? AppColors.colorFF808080
                      : AppColors.colorFF3474d1;
              String type = carData.tradeType == 0
                  ? L.transfer_in.tr
                  : carData.tradeType == 1
                      ? L.transfer_in.tr
                      : L.consumption.tr;
              String amount = "$type ${carData.money}";
              AppLogger.d('tradeStatus =${carData.tradeStatus}');
              return RecordItem(
                title: carData.tradeId ?? "",
                amount: amount,
                time: carData.createTime ?? "",
                status: state,
                stateColor: color,
                changedCallback: (a) {
                  Get.to(VirtualTradeDetailView(carData));
                },
              );
            },
          ),
        ],
      );
    }
    return w;
  }


}
