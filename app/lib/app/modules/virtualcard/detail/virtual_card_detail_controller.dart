import 'package:flutter/cupertino.dart';
import 'package:flutter_metatel/app/modules/virtualcard/manager/virtual_card_manager_controller.dart';
import 'package:flutter_metatel/core/languages/l.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

import '../../../../core/utils/app_log.dart';
import '../../../../core/utils/util.dart';
import '../../../../core/values/config.dart';
import '../api/api.dart';
import '../api/model/resp/virtual_card_info_rep.dart';
import '../api/model/resp/virtual_card_list_resp.dart';

class VirtualCardDetailController extends GetxController {
  RxInt loadType = LoadType.defaultState.obs;
  RxList<MbUserCardTrade> recordList = RxList();
  Rx<CarListItemInfo> rxCardInfo=CarListItemInfo().obs;
  String? cardId;
  var valueVisible = false.obs;
  TextEditingController edTransOut = TextEditingController();
  TextEditingController edTransfer = TextEditingController();
  int pageSize = 10;
  int pageNumber = 1;
  late ScrollController scrollController,scrollControllerMore;
  final VirtualCardApiProvider _apiProvider =
      Get.find<VirtualCardApiProvider>();

  @override
  void onInit() {
    var arguments = Get.arguments;
    if (arguments.runtimeType == CarListItemInfo) {
      CarListItemInfo cardListItem=arguments;
      String times= cardListItem.createTime??"0";
      try{
        int time=int.parse(times)*1000;
        times = DateFormat("yyyy-MM-dd HH:mm:ss").format(DateTime.fromMillisecondsSinceEpoch(time));
      }catch(e){
        AppLogger.d("$e");
      }
      cardListItem.createTime=times;
      rxCardInfo.value=cardListItem;
      cardId = cardListItem.cardId;
    }
    scrollController = ScrollController();
    scrollController.addListener(() {
      if (scrollController.position.pixels ==
          scrollController.position.maxScrollExtent) {
        AppLogger.d('rxCardInfo 002 =${pageSize == recordList.length ~/ pageNumber}');
        if (pageSize == recordList.length ~/ pageNumber) {
          refreshData(refresh: false);
        }
      }
    });
    scrollControllerMore =  ScrollController();
    scrollControllerMore.addListener(() {
      if (scrollControllerMore.position.pixels ==
          scrollControllerMore.position.maxScrollExtent) {
        AppLogger.d('rxCardInfo 003 =${pageSize == recordList.length ~/ pageNumber}');
        if (pageSize == recordList.length ~/ pageNumber) {
          refreshData(refresh: false);
        }
      }
    });
    super.onInit();
  }
  @override
  void onReady() {
    refreshData();
    super.onReady();

  }
  bool isLoadError() {
    return loadType.value == LoadType.error;
  }

  bool isLoading() {
    return loadType.value == LoadType.loading;
  }

  loadCardInfo(bool refresh) async {
    if (cardId?.isNotEmpty ?? false) {
      var response = await Get.find<VirtualCardApiProvider>().cardDetail(
        cardId: cardId!,
        pageSize: pageSize,
        pageNum: pageNumber,
      );
      if(refresh){
        recordList.clear();
      }
      VirtualCardInfo? cardInfo = response.data?.data;
      if (cardInfo != null) {
          CarListItemInfo old = rxCardInfo.value;
          old.balance=cardInfo.balance;
          rxCardInfo.value=CarListItemInfo();
          rxCardInfo.value = old;
          if (cardInfo.mbUserCardTrade != null) {
            var recordList = cardInfo.mbUserCardTrade;
            if (recordList?.isNotEmpty ?? false) {
              this.recordList.addAll(recordList!);
              this.recordList.sort((a, b) => (b.id??0).compareTo((a.id??0)));
            }
          }
          loadType.value = LoadType.success;
      }else{
        loadType.value=LoadType.error;
      }
    }
  }

  refreshData({bool refresh = true}) async {
    if (refresh) {
      loadType.value=LoadType.loading;
      pageSize = 20;
      pageNumber = 1;
    } else {
      pageNumber++;
    }
    loadCardInfo(refresh);
  }

  Future<bool> delCard() async {
    bool ret=false;
    if (cardId != null) {
      var response =await _apiProvider.cardDel(cardId: cardId!);
      if(response.data?.code==0000){
        SmartDialog.dismiss();
        refreshData();
        ret=true;
      }else{
        AppLogger.d("${response.data?.msg}");
        if(response.data?.msg?.isNotEmpty??false){
          toast(response.data?.msg??'');
        }
      }
    }
    return ret;
  }

  Future<bool> freeze() async {
    bool ret=false;
    if (cardId != null) {
      var response = await _apiProvider.cardFreeze(cardId: cardId!);
      if(response.data?.code==0000){
        ret=true;
        SmartDialog.dismiss(result: ret);
        refreshData();
      }else{
        AppLogger.d("${response.data?.msg}");
        if(response.data?.msg?.isNotEmpty??false){
          toast(response.data?.msg??'');
        }
      }
    }
    return ret;
  }
  Future<bool> activation() async {
    bool ret=false;
    if (cardId != null) {
      var response = await _apiProvider.cardActive(cardId: cardId!);
      if(response.data?.code==0000){
        SmartDialog.dismiss();
        refreshData();
        ret=true;
      }else{
        AppLogger.d("${response.data?.msg}");
        if(response.data?.msg?.isNotEmpty??false){
          toast(response.data?.msg??'');
        }
      }
    }
    return ret;
  }
  Future<bool> transIn() async {
    bool ret=false;
    double text=0;
    try{
      text = double.parse(edTransfer.text)*100;
    }catch(e){
      text=0;
    }
    if(text<1){
      toast(L.trans_the_amount_not_be_less_than.tr);
      return false;
    }
    var amount = "$text";
    if (amount.isNotEmpty && cardId != null) {
      var response = await _apiProvider.cardRecharge(cardId: cardId!, amount: amount);
      if(response.data?.code==0000){
        ret=true;
        SmartDialog.dismiss();
        refreshData();
        Get.find<VirtualCardManagerController>().refreshData();
      }else{
        AppLogger.d("${response.data?.msg}");
        if(response.data?.msg?.isNotEmpty??false){
          toast(response.data?.msg??'');
        }
      }
    }
    return ret;
  }

  Future<bool> transOut() async {
    bool ret=false;
    double text=0;
    try{
      text = double.parse(edTransOut.text)*100;
    }catch(e){
      text=0;
    }
    if(text<1){
      toast(L.trans_the_amount_not_be_less_than.tr);
      return false;
    }
    var amount = "$text";
    if (amount.isNotEmpty && cardId != null) {
      var response = await _apiProvider.cardRollOut(cardId: cardId!, amount: amount);
      if(response.data?.code==0000){
        ret=true;
        SmartDialog.dismiss();
        refreshData();
        Get.find<VirtualCardManagerController>().refreshData();
      }else{
        AppLogger.d("${response.data?.msg}");
        if(response.data?.msg?.isNotEmpty??false){
          toast(response.data?.msg??'');
        }
      }
    }
    return ret;
  }
  @override
  void onClose() {
    edTransOut.dispose();
    edTransfer.dispose();
    super.onClose();
  }
}
