import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/modules/virtualcard/detail/virtual_card_detail_controller.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:get/get.dart';

import '../../../../core/languages/l.dart';
import '../../../../core/utils/util.dart';
import '../../../../core/values/colors.dart';

class DelCardDialog extends StatelessWidget{
  const DelCardDialog({super.key, required this.title, required this.content,required this.confirmText});
  final String title;
  final String content;
  final String confirmText;

  @override
  Widget build(BuildContext context) {
    var loading = false.obs;
    return Stack(
      alignment: Alignment.center,
      children: [
        Container(
          height: 280.h,
          margin: const EdgeInsets.only(left: 16, right: 16).r,
          decoration: BoxDecoration(
              color: AppColors.white,
              borderRadius: const BorderRadius.all(Radius.circular(16)).r),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                margin: const EdgeInsets.only(
                  top: 25,
                ).r,
                alignment: Alignment.center,
                child: Text(
                  title,
                  style: TextStyle(
                    color: AppColors.colorFF000000,
                    fontSize: 18.sp,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              Container(
                margin: EdgeInsets.only(top: 44.h,left: 20,right: 20).r,
                child: Text(
                  content,
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: AppColors.colorFF333333,
                  ),
                ),
              ),
              const Spacer(),
              Padding(
                padding:
                const EdgeInsets.symmetric(horizontal: 20, vertical: 35)
                    .r,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Flexible(
                      child: TextButton(
                        style: ButtonStyle(
                          fixedSize: MaterialStateProperty.all(
                              Size.fromHeight(40.h)),
                          shape: MaterialStateProperty.all(
                            RoundedRectangleBorder(
                              borderRadius:
                              BorderRadius.all(Radius.circular(5.r).r).r,
                            ),
                          ),
                          // overlayColor:
                          // MaterialStateProperty.all(Colors.transparent),
                          // foregroundColor:
                          // MaterialStateProperty.resolveWith((states) {
                          //   return states.contains(MaterialState.pressed)
                          //       ? Colors.black54
                          //       : Colors.black38;
                          // }),
                          backgroundColor: MaterialStateProperty.all(
                              AppColors.colorFFF2F2F2),
                        ),
                        onPressed: () {
                          SmartDialog.dismiss();
                        },
                        child: Container(
                          height: 40.h,
                          alignment: Alignment.center,
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Text(
                                L.cancel.tr,
                                style: TextStyle(
                                  color: AppColors.colorFF333333,
                                  fontSize: 14.sp,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                    SizedBox(
                      width: 10.w,
                    ),
                    Flexible(
                      child: TextButton(
                        style: ButtonStyle(
                          fixedSize: MaterialStateProperty.all(
                              Size.fromHeight(40.h)),
                          shape: MaterialStateProperty.all(
                            RoundedRectangleBorder(
                              borderRadius:
                              BorderRadius.all(Radius.circular(5.r).r).r,
                            ),
                          ),
                          // overlayColor:
                          // MaterialStateProperty.all(Colors.transparent),
                          // foregroundColor:
                          // MaterialStateProperty.resolveWith((states) {
                          //   return states.contains(MaterialState.pressed)
                          //       ? Colors.black54
                          //       : Colors.black38;
                          // }),
                          backgroundColor:
                          MaterialStateProperty.all(AppColors.appDefault),
                        ),
                        onPressed: () async {
                          loading.value=true;
                          if(title==L.delete_card.tr){
                            await Get.find<VirtualCardDetailController>().delCard();
                          }else if(title==L.unfreeze.tr){
                            await Get.find<VirtualCardDetailController>().activation();
                          }
                          loading.value=false;
                        },
                        child: Container(
                          height: 40.h,
                          alignment: Alignment.center,
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Text(
                                confirmText,
                                style: TextStyle(
                                  color: AppColors.white,
                                  fontSize: 14.sp,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        Obx(() => Visibility(
          visible: loading.value,
          child: Container(
            margin: const EdgeInsets.only(left: 16, right: 16).r,
            decoration: BoxDecoration(
                color: const Color.fromRGBO(0, 0, 0, 0.46),
                borderRadius:
                const BorderRadius.all(Radius.circular(16)).r),
            height: 280.h,
            child: loadingView(),
          ),
        )),
      ],
    );
  }
}