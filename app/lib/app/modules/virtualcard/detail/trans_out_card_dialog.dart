import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_metatel/app/modules/virtualcard/detail/virtual_card_detail_controller.dart';
import 'package:flutter_metatel/app/modules/virtualcard/util/util.dart';
import 'package:flutter_metatel/core/utils/util.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:get/get.dart';

import '../../../../core/languages/l.dart';
import '../../../../core/values/colors.dart';

class CardInputInfoDialog extends StatelessWidget {
  const CardInputInfoDialog(
      {super.key,
      required this.title,
      required this.content,
      required this.buttonText,
      required this.edCtl});

  final String title;
  final String content;
  final String buttonText;
  final TextEditingController edCtl;

  @override
  Widget build(BuildContext context) {
    var loading = false.obs;
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: Center(
        child: Stack(
          alignment: Alignment.center,
          children: [
            Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  height: 300.h,
                  margin: const EdgeInsets.only(left: 16, right: 16).r,
                  decoration: BoxDecoration(
                      color: AppColors.white,
                      borderRadius:
                          const BorderRadius.all(Radius.circular(16)).r),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        margin: const EdgeInsets.only(
                          top: 25,
                        ).r,
                        alignment: Alignment.center,
                        child: Text(
                          title,
                          style: TextStyle(
                            color: AppColors.colorFF000000,
                            fontSize: 18.sp,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      Container(
                        margin:
                            EdgeInsets.only(top: 44.h, left: 20, right: 20).r,
                        child: Text(
                          content,
                          style: TextStyle(
                            fontSize: 14.sp,
                            color: AppColors.colorFF333333,
                          ),
                        ),
                      ),
                      _buildEditText(edCtl,
                          hintText: L.transfer_out_et_hint.tr),
                      Container(
                        margin: const EdgeInsets.only(
                          left: 30,
                          right: 30,
                          top: 5,
                        ).r,
                        child: Text(
                          title == L.transfer_out.tr?"":L.trans_fee_hint.tr,
                          style: TextStyle(fontSize: 10.sp, color: AppColors.colorFF808080),
                        ),
                      ),
                      const Spacer(),
                      Padding(
                        padding: const EdgeInsets.symmetric(
                                horizontal: 20, vertical: 35)
                            .r,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Flexible(
                              child: TextButton(
                                style: ButtonStyle(
                                  fixedSize: MaterialStateProperty.all(
                                      Size.fromHeight(40.h)),
                                  shape: MaterialStateProperty.all(
                                    RoundedRectangleBorder(
                                      borderRadius: BorderRadius.all(
                                              Radius.circular(5.r).r)
                                          .r,
                                    ),
                                  ),
                                  // overlayColor:
                                  // MaterialStateProperty.all(Colors.transparent),
                                  // foregroundColor:
                                  // MaterialStateProperty.resolveWith((states) {
                                  //   return states.contains(MaterialState.pressed)
                                  //       ? Colors.black54
                                  //       : Colors.black38;
                                  // }),
                                  backgroundColor: MaterialStateProperty.all(
                                      AppColors.colorFFF2F2F2),
                                ),
                                onPressed: () {
                                  SmartDialog.dismiss();
                                },
                                child: Container(
                                  height: 40.h,
                                  alignment: Alignment.center,
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Text(
                                        L.cancel.tr,
                                        style: TextStyle(
                                          color: AppColors.colorFF333333,
                                          fontSize: 14.sp,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                            SizedBox(
                              width: 10.w,
                            ),
                            Flexible(
                              child: TextButton(
                                style: ButtonStyle(
                                  fixedSize: MaterialStateProperty.all(
                                      Size.fromHeight(40.h)),
                                  shape: MaterialStateProperty.all(
                                    RoundedRectangleBorder(
                                      borderRadius: BorderRadius.all(
                                              Radius.circular(5.r).r)
                                          .r,
                                    ),
                                  ),
                                  // overlayColor:
                                  // MaterialStateProperty.all(Colors.transparent),
                                  // foregroundColor:
                                  // MaterialStateProperty.resolveWith((states) {
                                  //   return states.contains(MaterialState.pressed)
                                  //       ? Colors.black54
                                  //       : Colors.black38;
                                  // }),
                                  backgroundColor: MaterialStateProperty.all(
                                      AppColors.appDefault),
                                ),
                                onPressed: () async {
                                  if (edCtl.text.isEmpty) {
                                    toast(L.amount_is_required.tr);
                                    return;
                                  }
                                  // FocusScope.of(context).requestFocus(FocusNode());
                                  // await Future.delayed(const Duration(milliseconds: 500));
                                  loading.value=true;
                                  if (title == L.transfer_out.tr) {
                                    await Get.find<VirtualCardDetailController>()
                                        .transOut();
                                  } else if (title == L.transfer.tr) {
                                    await Get.find<VirtualCardDetailController>()
                                        .transIn();
                                  }
                                  loading.value=false;
                                },
                                child: Container(
                                  height: 40.h,
                                  alignment: Alignment.center,
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Text(
                                        buttonText,
                                        style: TextStyle(
                                          color: AppColors.white,
                                          fontSize: 14.sp,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            Obx(() => Visibility(
                  visible: loading.value,
                  child: Container(
                    margin: const EdgeInsets.only(left: 16, right: 16).r,
                    decoration: BoxDecoration(
                        color: const Color.fromRGBO(0, 0, 0, 0.46),
                        borderRadius:
                            const BorderRadius.all(Radius.circular(16)).r),
                    height: 300.h,
                    child: loadingView(),
                  ),
                )),
          ],
        ),
      ),
    );
  }

  _buildEditText(
    TextEditingController edCtl, {
    String? hintText,
    double? marginLeft,
    double? marginRight,
  }) {
    return Container(
      margin: EdgeInsets.only(
              left: marginLeft ?? 20, right: marginRight ?? 20, top: 10)
          .r,
      decoration: BoxDecoration(
        color: AppColors.colorFFF2F2F2,
        borderRadius: const BorderRadius.all(Radius.circular(10)).r,
      ),
      child: TextField(
        controller: edCtl,
        textAlignVertical: TextAlignVertical.center,
        inputFormatters: [
          FilteringTextInputFormatter.allow(RegExp(r'[0-9.]')),
          NumLengthInputFormatter(decimalLength: 2,),
        ],
        keyboardType: TextInputType.number,
        decoration: InputDecoration(
          contentPadding: const EdgeInsets.only(left: 14, right: 14).r,
          fillColor: AppColors.colorFFF2F2F2,
          hintText: hintText,
          hintMaxLines: 1,
          hintStyle: TextStyle(fontSize: 14.sp, color: AppColors.colorFF808080),
          border: OutlineInputBorder(
            borderRadius: const BorderRadius.all(Radius.circular(10)).r,
            borderSide: BorderSide.none,
          ),
        ),
      ),
    );
  }
}
