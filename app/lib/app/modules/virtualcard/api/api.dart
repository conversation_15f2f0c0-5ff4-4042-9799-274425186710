import 'dart:io';

import 'package:dio/dio.dart';
import 'package:flutter_metatel/app/data/providers/api/base_dio_api.dart';
import 'package:flutter_metatel/app/data/services/config_service.dart';
import 'package:flutter_metatel/app/modules/mining/mining_pwd_util.dart';
import 'package:flutter_metatel/app/modules/virtualcard/api/model/resp/account_info_rep.dart';
import 'package:flutter_metatel/app/modules/virtualcard/api/model/resp/is_register.dart';
import 'package:flutter_metatel/app/modules/virtualcard/api/model/resp/virtual_card_transaction_record_record_rep.dart';
import 'package:flutter_metatel/core/languages/l.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:flutter_metatel/core/utils/util.dart';
import 'package:get/get.dart'as g;

import '../../../data/models/res/base_res_model.dart';
import 'model/req.dart';
import 'model/resp/card_seg_list_resp.dart';
import 'model/resp/virtual_card_info_rep.dart';
import 'model/resp/virtual_card_list_resp.dart';
import 'model/resp/virtual_card_login_resp.dart';

class VirtualCardApiProvider extends BaseDioClient {

  String? token;

  Dio _createDio(){
    BaseOptions options = BaseOptions();
    options.connectTimeout = const Duration(seconds: 15);
    options.sendTimeout = const Duration(seconds: 15);
    options.contentType = 'application/json; charset=utf-8';
    var token = g.Get.find<AppConfigService>().getToken();
    options.headers = {
      'token': "$token",
    };
    var dio = super.createDio(options: options);
    return dio;
  }

  ///创建虚拟卡
  Future<Response<BaseRes>> cardAdd(AddReq addReq) async {
    var reqBody = addReq.toJson();
    AppLogger.d("cardAdd reqBody:$reqBody");
    try {
      var response = await _createDio().post(VirtualCardApi.cardAdd, data : reqBody);
      BaseRes baseRes = BaseRes.fromJson(response.data);
      if (baseRes.code==0005) {
        await loginVirtualAccount();
        var response = await _createDio().post(VirtualCardApi.cardAdd, data: reqBody);
        BaseRes baseRes = BaseRes.fromJson(response.data);
        return Response(
          statusCode: response.statusCode,
          statusMessage: response.statusMessage,
          data: baseRes, requestOptions: response.requestOptions,
        );
      }
      return Response(
        statusCode: response.statusCode,
        statusMessage: response.statusMessage,
        data: baseRes, requestOptions: response.requestOptions,
      );
    } catch (e) {
      AppLogger.e('Response data url CARADD $e');
      toast(L.service_unavailable.tr);
      return Response(
          statusCode: -200,
          statusMessage: '$e',
          data: null,
          requestOptions: RequestOptions(path: ''));
    }

  }

  ///转出
  Future<Response<BaseRes>> cardRollOut(
      {required String cardId, required String amount}) async {
    Map<String, dynamic> map = {"cardId": cardId, "amount": amount};
    var formData = FormData.fromMap(map);
    try {

      var response = await _createDio().post(VirtualCardApi.cardRollOut, data: formData,);
      BaseRes baseRes = BaseRes.fromJson(response.data);
      if (baseRes.code==0005) {
        await loginVirtualAccount();
        var response = await _createDio().post(VirtualCardApi.cardRollOut, data: formData,);
        BaseRes baseRes = BaseRes.fromJson(response.data);
        return Response(
          statusCode: response.statusCode,
          statusMessage: response.statusMessage,
          data: baseRes, requestOptions: response.requestOptions,
        );
      }
      return Response(
        statusCode: response.statusCode,
        statusMessage: response.statusMessage,
        data: baseRes, requestOptions: response.requestOptions,
      );
    } catch (e) {
      AppLogger.e('Response data url Our $e');
      toast(L.service_unavailable.tr);
      return Response(
          statusCode: -200,
          statusMessage: '$e',
          data: null,
          requestOptions: RequestOptions(path: ''));
    }
  }
  ///转入
  Future<Response<BaseRes>> cardRecharge(
      {required String cardId, required String amount}) async {
    Map<String, dynamic> map = {"cardId": cardId, "amount": amount};
    var formData = FormData.fromMap(map);
    try {

      var response = await _createDio().post(VirtualCardApi.carRecharge, data: formData,);
      BaseRes baseRes = BaseRes.fromJson(response.data);
      if (baseRes.code==0005) {
        await loginVirtualAccount();
        var response = await _createDio().post(VirtualCardApi.carRecharge, data: formData,);
        BaseRes baseRes = BaseRes.fromJson(response.data);
        return Response(
          statusCode: response.statusCode,
          statusMessage: response.statusMessage,
          data: baseRes, requestOptions: response.requestOptions,
        );
      }
      return Response(
        statusCode: response.statusCode,
        statusMessage: response.statusMessage,
        data: baseRes, requestOptions: response.requestOptions,
      );
    } catch (e) {
      AppLogger.e('Response data url recharge$e');
      toast(L.service_unavailable.tr);
      return Response(
          statusCode: -200,
          statusMessage: '$e',
          data: null,
          requestOptions: RequestOptions(path: ''));
    }
  }

  ///冻结
  Future<Response<BaseRes>> cardFreeze({
    required String cardId,
  }) async {
    Map<String, dynamic> map = {
      "cardId": cardId,
    };
    try {
      var response = await _createDio().post(VirtualCardApi.cardFreeze, data: map,);
      BaseRes baseRes = BaseRes.fromJson(response.data);
      if (baseRes.code==0005) {
        await loginVirtualAccount();
        var response = await _createDio().post(VirtualCardApi.cardFreeze, data: map,);
        BaseRes baseRes = BaseRes.fromJson(response.data);
        return Response(
          statusCode: response.statusCode,
          statusMessage: response.statusMessage,
          data: baseRes, requestOptions: response.requestOptions,
        );
      }
      return Response(
        statusCode: response.statusCode,
        statusMessage: response.statusMessage,
        data: baseRes, requestOptions: response.requestOptions,
      );
    } catch (e) {
      AppLogger.e('Response data url freeze $e');
      toast(L.service_unavailable.tr);
      return Response(
          statusCode: -200,
          statusMessage: '$e',
          data: null,
          requestOptions: RequestOptions(path: ''));
    }

  }
  ///激活
  Future<Response<BaseRes>> cardActive({
    required String cardId,
  }) async {
    Map<String, dynamic> map = {
      "cardId": cardId,
    };
    try {
      var response = await _createDio().post(VirtualCardApi.cardActive, data: map,);
      BaseRes baseRes = BaseRes.fromJson(response.data);
      if (baseRes.code==0005) {
        await loginVirtualAccount();
        var response = await _createDio().post(VirtualCardApi.cardActive, data: map,);
        BaseRes baseRes = BaseRes.fromJson(response.data);
        return Response(
          statusCode: response.statusCode,
          statusMessage: response.statusMessage,
          data: baseRes, requestOptions: response.requestOptions,
        );
      }
      return Response(
        statusCode: response.statusCode,
        statusMessage: response.statusMessage,
        data: baseRes, requestOptions: response.requestOptions,
      );
    } catch (e) {
      AppLogger.e('Response data url active $e');
      toast(L.service_unavailable.tr);
      return Response(
          statusCode: -200,
          statusMessage: '$e',
          data: null,
          requestOptions: RequestOptions(path: ''));
    }

  }

  ///删除
  Future<Response<BaseRes>> cardDel({
    required String cardId,
  }) async {
    Map<String, dynamic> map = {
      "cardId": cardId,
    };
    try {
      var response = await _createDio().post(VirtualCardApi.cardDel, data: map,);
      BaseRes baseRes = BaseRes.fromJson(response.data);
      if (baseRes.code==0005) {
        await loginVirtualAccount();
        var response = await _createDio().post(VirtualCardApi.cardDel, data: map,);
        BaseRes baseRes = BaseRes.fromJson(response.data);
        return Response(
          statusCode: response.statusCode,
          statusMessage: response.statusMessage,
          data: baseRes, requestOptions: response.requestOptions,
        );
      }
      return Response(
        statusCode: response.statusCode,
        statusMessage: response.statusMessage,
        data: baseRes, requestOptions: response.requestOptions,
      );
    } catch (e) {
      AppLogger.e('Response data url del $e');
      toast(L.service_unavailable.tr);
      return Response(
          statusCode: -200,
          statusMessage: '$e',
          data: null,
          requestOptions: RequestOptions(path: ''));
    }

  }

  ///卡片详情
  Future<Response<VirtualCardInfoRep>> cardDetail({
    required String cardId,int? pageSize,int? pageNum}) async {
    Map<String, dynamic> map = {
      "cardId": cardId,
      "pageSize": pageSize,
      "pageNum": pageNum,
    };
    try {

      var response = await _createDio().post(VirtualCardApi.cardDetail, data: map,);
      VirtualCardInfoRep? data;
      if (response.statusCode == 200) {
        data = VirtualCardInfoRep.fromJson(response.data);
      }
      if (data?.code==0005) {
        await loginVirtualAccount();
        var response = await _createDio().post(VirtualCardApi.cardDetail, data: map,);
        VirtualCardInfoRep? data;
        if (response.statusCode == 200) {
          data = VirtualCardInfoRep.fromJson(response.data);
        }
        return Response(
          statusCode: response.statusCode,
          statusMessage: response.statusMessage,
          data: data, requestOptions: response.requestOptions,
        );
      }
      return Response(
        statusCode: response.statusCode,
        statusMessage: response.statusMessage,
        data: data, requestOptions: response.requestOptions,
      );
    } catch (e) {
      AppLogger.d('Response data url cardDetail $e');
      toast(L.service_unavailable.tr);
      return Response(
          statusCode: -200,
          statusMessage: '$e',
          data: null,
          requestOptions: RequestOptions(path: ''));
    }
  }

  ///虚拟卡账户信息
  Future<Response<AccountInfoRep>> virtualCardAccountInfo() async {
    try {
      var response = await _createDio().post(VirtualCardApi.getUserInfo, data: "",);
      AccountInfoRep? data;
      if (response.statusCode == 200) {
        data = AccountInfoRep.fromJson(response.data);
      }
      if (data?.code==0005) {
        await loginVirtualAccount();
        var response = await _createDio().post(VirtualCardApi.getUserInfo, data: "",);
        AccountInfoRep? data;
        if (response.statusCode == 200) {
          data = AccountInfoRep.fromJson(response.data);
        }
        return Response(
          statusCode: response.statusCode,
          statusMessage: response.statusMessage,
          data: data, requestOptions: response.requestOptions,
        );
      }
      return Response(
        statusCode: response.statusCode,
        statusMessage: response.statusMessage,
        data: data, requestOptions: response.requestOptions,
      );
    } catch (e) {
      AppLogger.d('Response data url virtualCardAccountInfo $e');
      toast(L.service_unavailable.tr);
      return Response(
          statusCode: -200,
          statusMessage: '$e',
          data: null,
          requestOptions: RequestOptions(path: ''));
    }

  }

  ///虚拟卡交易记录
  Future<Response<VirtualCardTransactionRecordRep>> virtualCardTransRecord({
    required TransRecordReq req
  }) async {
    try {
      var response = await _createDio().post(VirtualCardApi.cardTradeList, data : req.toJson(),);
      VirtualCardTransactionRecordRep? data;
      if (response.statusCode == 200) {
        data = VirtualCardTransactionRecordRep.fromJson(response.data);
      }
      if (data?.code==0005) {
        await loginVirtualAccount();
        var response = await _createDio().post(VirtualCardApi.cardTradeList, data : req.toJson(),);
        VirtualCardTransactionRecordRep? data;
        if (response.statusCode == 200) {
          data = VirtualCardTransactionRecordRep.fromJson(response.data);
        }
        return Response(
          statusCode: response.statusCode,
          statusMessage: response.statusMessage,
          data: data, requestOptions: response.requestOptions,
        );
      }
      return Response(
        statusCode: response.statusCode,
        statusMessage: response.statusMessage,
        data: data, requestOptions: response.requestOptions,
      );
    } catch (e) {
      AppLogger.e('Response data url TR $e');
      toast(L.service_unavailable.tr);
      return Response(
          statusCode: -200,
          statusMessage: '$e',
          data: null,
          requestOptions: RequestOptions(path: ''));
    }

  }

  ///虚拟卡列表
  Future<Response<VirtualCardListResp>> virtualCardList({
    required int pageSize,
    required int pageNum,
    required int status,
  }) async {
    Map<String, dynamic> map = {
      "pageSize": pageSize,
      "pageNum": pageNum,
      "status": status,
    };
    try {
      var response = await _createDio().post(VirtualCardApi.carList, data: map,);
      VirtualCardListResp? data;
      if (response.statusCode == 200) {
        data = VirtualCardListResp.fromJson(response.data);
      }
      if (data?.code==0005) {
        await loginVirtualAccount();
        var response = await _createDio().post(VirtualCardApi.carList, data: map,);
        VirtualCardListResp? data;
        if (response.statusCode == 200) {
          data = VirtualCardListResp.fromJson(response.data);
        }
        return Response(
          statusCode: response.statusCode,
          statusMessage: response.statusMessage,
          data: data, requestOptions: response.requestOptions,
        );
      }
      return Response(
        statusCode: response.statusCode,
        statusMessage: response.statusMessage,
        data: data, requestOptions: response.requestOptions,
      );
    } catch (e) {
      AppLogger.e('Response data url CL $e');
      toast(L.service_unavailable.tr);
      return Response(
          statusCode: -200,
          statusMessage: '$e',
          data: null,
          requestOptions: RequestOptions(path: ''));
    }

  }

  ///虚拟卡开卡可用卡段
  Future<Response<CardSegListResp>> cardSegmentList() async {
    var response = await _createDio().post(VirtualCardApi.carSegmentList,data : "");
    CardSegListResp? data;
    if (response.statusCode == 200) {
      data = CardSegListResp.fromJson(response.data);
    }
    if (data?.code==0005) {
      await loginVirtualAccount();
      var response = await _createDio().post(VirtualCardApi.carSegmentList, data : "");
      CardSegListResp? data;
      if (response.statusCode == 200) {
        data = CardSegListResp.fromJson(response.data);
      }
      return Response(
        statusCode: response.statusCode,
        statusMessage: response.statusMessage,
        data: data, requestOptions: response.requestOptions,
      );
    }
    return Response(
      statusCode: response.statusCode,
      statusMessage: response.statusMessage,
      data: data, requestOptions: response.requestOptions,
    );
  }
  ///虚拟卡创建账号 Dio
  Future<Response<BaseRes>> createVirtualAccount() async {
    String userNameWithoutDomain = g.Get.find<AppConfigService>().getUserNameWithoutDomain();
    String dataEn = await MiningPwdUtil.encryptForData(userNameWithoutDomain) ?? '';
    Map<String, dynamic> map={
      "address":userNameWithoutDomain,
      "body":dataEn,
    };
    try {
      var response =  await _createDio().post(VirtualCardApi.createAccount,data: map);
      AppLogger.d("Response data url=${VirtualCardApi.createAccount} resp:${response.data}  req:${response.requestOptions.data}");
      BaseRes? data;
      if (response.statusCode == 200) {
        data = BaseRes.fromJson(response.data);
      }
      return Response(
        statusCode: response.statusCode,
        statusMessage: response.statusMessage,
        data: data, requestOptions: response.requestOptions,
      );
    } catch (e) {
      AppLogger.e('Response data url CA');
      toast(L.service_unavailable.tr);
      return Response(
          statusCode: -200,
          statusMessage: '$e',
          data: null,
          requestOptions: RequestOptions(path: ''));
    }

  }
  ///虚拟卡登陆账号
  Future<Response<VirtualCardLogin>> loginVirtualAccount() async {
     String userNameWithoutDomain = g.Get.find<AppConfigService>().getUserNameWithoutDomain();
    String dataEn = await MiningPwdUtil.encryptForData(userNameWithoutDomain) ?? '';
    Map<String, dynamic> map={
      "address":userNameWithoutDomain,
      "body":dataEn,
    };
    try {
      var response = await _createDio().post(VirtualCardApi.loginAccount, data:map,);
      AppLogger.d("Response data url=${VirtualCardApi.loginAccount} resp:${response.data}  req:${response.requestOptions.data}");
      VirtualCardLogin? data;
      if (response.statusCode == 200) {
        data = VirtualCardLogin.fromJson(response.data);
        String? token=data.data?.token;
        this.token=token;
      }
      return Response(
        statusCode: response.statusCode,
        statusMessage: response.statusMessage,
        data: data, requestOptions: response.requestOptions,
      );
    } catch (e) {
      AppLogger.e('Response data url LA');
      toast(L.service_unavailable.tr);
      return Response(
          statusCode: -200,
          statusMessage: '$e',
          data: null,
          requestOptions: RequestOptions(path: ''));
    }

  }
  ///是否注册
  Future<Response<IsRegister>> isRegister() async {
    String userNameWithoutDomain = g.Get.find<AppConfigService>().getUserNameWithoutDomain();
    Map<String, dynamic> map={
      "address":userNameWithoutDomain,
    };
    try {
      var response = await _createDio().post(VirtualCardApi.isRegister, data:map,);
      AppLogger.d("Response data url=${VirtualCardApi.isRegister} resp:${response.data}  req:${response.requestOptions.data}");
      IsRegister? data;
      if (response.statusCode == 200) {
        data = IsRegister.fromJson(response.data);
      }
      return Response(
        statusCode: response.statusCode,
        statusMessage: response.statusMessage,
        data: data, requestOptions: response.requestOptions,
      );
    } catch (e) {
      AppLogger.d("Response data url Register $e");
      toast(L.service_unavailable.tr);
      return Response(
          statusCode: -200,
          statusMessage: '$e',
          data: null,
          requestOptions: RequestOptions(path: ''));
    }

  }

}

class VirtualCardApi {
  static String base =
      "http://*************:25000";
  static String cardAdd = "$base/api/bq/create_card";
  static String cardRollOut = "$base/api/bq/transfer_out_card";
  static String cardFreeze = "$base/api/bq/freeze_card";
  static String cardActive = "$base/api/bq/activate_card";
  static String cardDel = "$base/api/bq/delete_card";
  static String cardDetail = "$base/api/bq/get_card_detail";
  static String cardTradeList = "$base/api/bq/get_card_transaction_list";
  static String getUserInfo = "$base/api/bq/get_user_info";
  static String carList = "$base/api/bq/get_card_list";
  static String carSegmentList = "$base/api/bq/get_card_segment";
  static String carRecharge = "$base/api/bq/recharge_card";
  static String createAccount = "$base/api/bq/register";
  static String loginAccount = "$base/api/bq/login";
  static String isRegister = "$base/api/bq/is_register";
}

