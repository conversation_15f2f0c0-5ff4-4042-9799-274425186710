import '../../../../../data/models/res/base_res_model.dart';

/// data : {"seg_data":[{"id":4,"vid":"vcbi_BBC3BD666D4D4E97BD0F","cardSegment":531847,"issuer":"visa","cardType":"Visa","avsVerify":1,"createTime":"2023-04-03 21:16:55"}],"card_recharge_price":100,"card_out_price":100,"open_card_price":100}

class CardSegListResp extends BaseRes{
  CardSegListResp({
      this.data,});

  CardSegListResp.fromJson(dynamic json):super.fromJson(json){
    data = json['data'] != null ? CarSegmentData.fromJson(json['data']) : null;
  }
  CarSegmentData? data;
}

/// seg_data : [{"id":4,"vid":"vcbi_BBC3BD666D4D4E97BD0F","cardSegment":531847,"issuer":"visa","cardType":"Visa","avsVerify":1,"createTime":"2023-04-03 21:16:55"}]
/// card_recharge_price : 100
/// card_out_price : 100
/// open_card_price : 100

class CarSegmentData {
  CarSegmentData({
      this.segData, 
      this.cardRechargePrice, 
      this.cardOutPrice, 
      this.openCardPrice,});

  CarSegmentData.fromJson(dynamic json) {
    if (json['seg_data'] != null) {
      segData = [];
      json['seg_data'].forEach((v) {
        segData?.add(CardSegment.fromJson(v));
      });
    }
    cardRechargePrice = json['card_recharge_price'];
    cardOutPrice = json['card_out_price'];
    openCardPrice = json['open_card_price'];
  }
  List<CardSegment>? segData;
  int? cardRechargePrice;
  int? cardOutPrice;
  int? openCardPrice;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    if (segData != null) {
      map['seg_data'] = segData?.map((v) => v.toJson()).toList();
    }
    map['card_recharge_price'] = cardRechargePrice;
    map['card_out_price'] = cardOutPrice;
    map['open_card_price'] = openCardPrice;
    return map;
  }

}

/// id : 4
/// vid : "vcbi_BBC3BD666D4D4E97BD0F"
/// cardSegment : 531847
/// issuer : "visa"
/// cardType : "Visa"
/// avsVerify : 1
/// createTime : "2023-04-03 21:16:55"

class CardSegment {
  CardSegment({
      this.id, 
      this.vid, 
      this.cardSegment, 
      this.issuer, 
      this.cardType, 
      this.avsVerify, 
      this.createTime,});

  CardSegment.fromJson(dynamic json) {
    id = json['id'];
    vid = json['vid'];
    cardSegment = json['cardSegment'];
    issuer = json['issuer'];
    cardType = json['cardType'];
    avsVerify = json['avsVerify'];
    createTime = json['createTime'];
  }
  int? id;
  String? vid;
  int? cardSegment;
  String? issuer;
  String? cardType;
  int? avsVerify;
  String? createTime;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['id'] = id;
    map['vid'] = vid;
    map['cardSegment'] = cardSegment;
    map['issuer'] = issuer;
    map['cardType'] = cardType;
    map['avsVerify'] = avsVerify;
    map['createTime'] = createTime;
    return map;
  }

}