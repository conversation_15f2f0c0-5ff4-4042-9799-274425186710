import 'package:flutter_metatel/app/data/models/res/base_res_model.dart';

/// data : {"account_bal":0,"open_card":0,"card_sum_bal":3}

class AccountInfoRep extends BaseRes{
  AccountInfoRep({
      this.data,});

  AccountInfoRep.fromJson(dynamic json):super.fromJson(json) {
    data = json['data'] != null ? AccountInfo.fromJson(json['data']) : null;
  }
  AccountInfo? data;
}

/// account_bal : 0
/// open_card : 0
/// card_sum_bal : 3

class AccountInfo {
  AccountInfo({
    this.accountBal,
    this.openCard,
    this.cardSumBal,
    this.userState,
  });

  AccountInfo.fromJson(dynamic json) {
    int accountBal = json['account_bal']??0;
    this.accountBal = accountBal/100;
    openCard = json['open_card'];
    cardSumBal = json['card_sum_bal'];
    userState = json['user_state'];
  }

  double? accountBal;
  int? openCard;
  int? cardSumBal;
  int? userState;
}
