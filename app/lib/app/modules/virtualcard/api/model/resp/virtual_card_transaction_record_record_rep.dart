import 'package:flutter_metatel/app/data/models/res/base_res_model.dart';

/// code : 0
/// data : {"Data":[{"id":1,"userId":4,"cardId":1,"cardSegment":531847,"label":"默认","last4":"9565","name":"<PERSON>","tradeId":"20230415150837158007","description":"","money":10,"sxMoney":0,"currency":"USD","status":1,"tradeType":1,"createTime":"2023-04-15 15:08:37"}],"Count":1}
/// msg : "成功"

class VirtualCardTransactionRecordRep extends BaseRes{
  VirtualCardTransactionRecordRep({
      this.data,});

  VirtualCardTransactionRecordRep.fromJson(dynamic json):super.fromJson(json) {
    data = json['data'] != null ? TransactionRecordList.from<PERSON>son(json['data']) : null;
  }
  TransactionRecordList? data;

  @override
  Map<String, dynamic> to<PERSON>son() {
    super.toJson();
    final map = <String, dynamic>{};
    if (data != null) {
      map['data'] = data?.toJson();
    }
    return map;
  }

}

/// Data : [{"id":1,"userId":4,"cardId":1,"cardSegment":531847,"label":"默认","last4":"9565","name":"Milton Schmitt","tradeId":"20230415150837158007","description":"","money":10,"sxMoney":0,"currency":"USD","status":1,"tradeType":1,"createTime":"2023-04-15 15:08:37"}]
/// Count : 1

class TransactionRecordList {
  TransactionRecordList({
      this.data, 
      this.count,});

  TransactionRecordList.fromJson(dynamic json) {
    if (json['Data'] != null) {
      data = [];
      json['Data'].forEach((v) {
        data?.add(TransactionRecord.fromJson(v));
      });
    }
    count = json['Count'];
  }
  List<TransactionRecord>? data;
  int? count;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    if (data != null) {
      map['Data'] = data?.map((v) => v.toJson()).toList();
    }
    map['Count'] = count;
    return map;
  }

}

/// id : 1
/// userId : 4
/// cardId : 1
/// cardSegment : 531847
/// label : "默认"
/// last4 : "9565"
/// name : "Milton Schmitt"
/// tradeId : "20230415150837158007"
/// description : ""
/// money : 10
/// sxMoney : 0
/// currency : "USD"
/// status : 1
/// tradeType : 1
/// createTime : "2023-04-15 15:08:37"

class TransactionRecord {
  TransactionRecord({
      this.id, 
      this.userId, 
      this.cardId, 
      this.cardSegment, 
      this.label, 
      this.last4, 
      this.name, 
      this.tradeId, 
      this.description, 
      this.money, 
      this.sxMoney, 
      this.currency, 
      this.status, 
      this.tradeType, 
      this.createTime,});

  TransactionRecord.fromJson(dynamic json) {
    id = json['id'];
    userId = json['userId'];
    cardId = json['cardId'];
    cardSegment = json['cardSegment'];
    label = json['label'];
    last4 = json['last4'];
    name = json['name'];
    tradeId = json['tradeId'];
    description = json['description'];
    int money = json['money']??0;
    this.money = money/100;
    int sxMoney = json['sxMoney']??0;
    this.sxMoney = sxMoney/100;
    currency = json['currency'];
    status = json['status'];
    tradeType = json['tradeType'];
    createTime = json['createTime'];
  }
  int? id;
  String? userId;
  int? cardId;
  int? cardSegment;
  String? label;
  String? last4;
  String? name;
  String? tradeId;
  String? description;
  double? money;
  double? sxMoney;
  String? currency;
  int? status;
  int? tradeType;
  String? createTime;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['id'] = id;
    map['userId'] = userId;
    map['cardId'] = cardId;
    map['cardSegment'] = cardSegment;
    map['label'] = label;
    map['last4'] = last4;
    map['name'] = name;
    map['tradeId'] = tradeId;
    map['description'] = description;
    map['money'] = money;
    map['sxMoney'] = sxMoney;
    map['currency'] = currency;
    map['status'] = status;
    map['tradeType'] = tradeType;
    map['createTime'] = createTime;
    return map;
  }

}