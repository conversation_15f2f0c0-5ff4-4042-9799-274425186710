import 'package:flutter_metatel/app/data/models/res/base_res_model.dart';

/// data : {"is_register":0}

class IsRegister extends BaseRes{
  IsRegister({
      this.data,});

  IsRegister.fromJson(dynamic json):super.fromJson(json) {
    data = json['data'] != null ? Data.fromJson(json['data']) : null;
  }
  Data? data;
}

/// is_register : 0

class Data {
  Data({
      this.isRegister,});

  Data.fromJson(dynamic json) {
    isRegister = json['is_register'];
  }
  int? isRegister;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['is_register'] = isRegister;
    return map;
  }

}