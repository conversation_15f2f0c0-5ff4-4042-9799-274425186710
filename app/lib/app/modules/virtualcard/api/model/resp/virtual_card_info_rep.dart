import '../../../../../data/models/res/base_res_model.dart';

/// data : {"name":"<PERSON>","cardId":"c_75ae080e242c48dbb8aa","cardType":"Visa","state":4,"expirationDate":"2024-5","cardNo":"****************","label":"默认","safety_code":"324","zipCode":"90037","city":"Los Angeles","countryCode":"US","zhou":"CA","cardAddress1":"145 W 48th St","cardAddress2":"","consume":0,"balance":0,"recharge":0,"refund":0,"roll_out":0,"MbUserCardTrade":null,"Count":0}

class VirtualCardInfoRep extends BaseRes{
  VirtualCardInfoRep({
    this.data,});

  VirtualCardInfoRep.fromJson(dynamic json) :super.fromJson(json){
    data = json['data'] != null ? VirtualCardInfo.fromJson(json['data']) : null;
  }
  VirtualCardInfo? data;
}

/// name : "<PERSON>"
/// cardId : "c_75ae080e242c48dbb8aa"
/// cardType : "Visa"
/// state : 4
/// expirationDate : "2024-5"
/// cardNo : "****************"
/// label : "默认"
/// safety_code : "324"
/// zipCode : "90037"
/// city : "Los Angeles"
/// countryCode : "US"
/// zhou : "CA"
/// cardAddress1 : "145 W 48th St"
/// cardAddress2 : ""
/// consume : 0
/// balance : 0
/// recharge : 0
/// refund : 0
/// roll_out : 0
/// MbUserCardTrade : null
/// Count : 0

class VirtualCardInfo {
  VirtualCardInfo({
    this.name,
    this.cardId,
    this.cardType,
    this.state,
    this.expirationDate,
    this.cardNo,
    this.label,
    this.safetyCode,
    this.zipCode,
    this.city,
    this.countryCode,
    this.zhou,
    this.cardAddress1,
    this.cardAddress2,
    this.consume,
    this.balance,
    this.recharge,
    this.refund,
    this.rollOut,
    this.mbUserCardTrade,
    this.createTime,
    this.count,});

  VirtualCardInfo.fromJson(dynamic json) {
    name = json['name'];
    cardId = json['cardId'];
    cardType = json['cardType'];
    state = json['state'];
    expirationDate = json['expirationDate'];
    cardNo = json['cardNo'];
    label = json['label'];
    safetyCode = json['safety_code'];
    zipCode = json['zipCode'];
    city = json['city'];
    countryCode = json['countryCode'];
    zhou = json['zhou'];
    cardAddress1 = json['cardAddress1'];
    cardAddress2 = json['cardAddress2'];
    int consume = json['consume']??0;
    this.consume = consume/100;
    int balance = json['balance']??0;
    this.balance = balance/100;
    int recharge = json['recharge']??0;
    this.recharge = recharge/100;
    int refund = json['refund']??0;
    this.refund = refund/100;
    int rollOut = json['rollOut']??0;
    this.rollOut = rollOut/100;
    if (json['MbUserCardTrade'] != null) {
      mbUserCardTrade = [];
      json['MbUserCardTrade'].forEach((v) {
        mbUserCardTrade?.add(MbUserCardTrade.fromJson(v));
      });
    }
    count = json['Count'];
  }
  String? name;
  String? cardId;
  String? cardType;
  int? state;
  String? expirationDate;
  String? cardNo;
  String? label;
  String? safetyCode;
  String? zipCode;
  String? city;
  String? countryCode;
  String? zhou;
  String? cardAddress1;
  String? cardAddress2;
  double? consume;
  double? balance;
  double? recharge;
  double? refund;
  double? rollOut;
  List<MbUserCardTrade>? mbUserCardTrade;
  String? createTime;
  int? count;


}


/// id : 48
/// userId : "t_b74493c5772"
/// cardId : 0
/// sfCardId : "c_959eb561e5c34c219da4"
/// sfTradeId : ""
/// cardSeqno : "CB2790B31"
/// cardNo : "****************"
/// cardSegment : 531847
/// label : "默认"
/// last4 : "7136"
/// name : "Wu Ying"
/// tradeId : "20230508131744552560"
/// description : "量子卡首次开卡,充值10 -开卡手续费1 -充值手续费1 卡余额8"
/// preAuthAmount : 0
/// preAuthCurrency : ""
/// money : 1000
/// sxMoney : 200
/// currency : "USD"
/// status : 1
/// tradeType : 1
/// tradeStatus : ""
/// balance : 800
/// createTime : "2023-05-08 13:17:44"
/// updateTime : 0

class MbUserCardTrade {
  MbUserCardTrade({
    this.id,
    this.userId,
    this.cardId,
    this.sfCardId,
    this.sfTradeId,
    this.cardSeqno,
    this.cardNo,
    this.cardSegment,
    this.label,
    this.last4,
    this.name,
    this.tradeId,
    this.description,
    this.preAuthAmount,
    this.preAuthCurrency,
    this.money,
    this.sxMoney,
    this.currency,
    this.status,
    this.tradeType,
    this.tradeStatus,
    this.balance,
    this.createTime,
    this.updateTime,});

  MbUserCardTrade.fromJson(dynamic json) {
    id = json['id'];
    userId = json['userId'];
    cardId = json['cardId'];
    sfCardId = json['sfCardId'];
    sfTradeId = json['sfTradeId'];
    cardSeqno = json['cardSeqno'];
    cardNo = json['cardNo'];
    cardSegment = json['cardSegment'];
    label = json['label'];
    last4 = json['last4'];
    name = json['name'];
    tradeId = json['tradeId'];
    description = json['description'];
    int preAuthAmount = json['preAuthAmount']??0;
    this.preAuthAmount = preAuthAmount/100;
    preAuthCurrency = json['preAuthCurrency'];
    int money = json['money']??0;
    this.money = money/100;
    currency = json['currency'];
    int sxMoney = json['sxMoney']??0;
    this.sxMoney = sxMoney/100;
    status = json['status'];
    tradeType = json['tradeType'];
    tradeStatus = json['tradeStatus'];
    int balance = json['balance']??0;
    this.balance = balance/100;
    createTime = json['createTime'];
    updateTime = json['updateTime'];
  }
  int? id;
  String? userId;
  int? cardId;
  String? sfCardId;
  String? sfTradeId;
  String? cardSeqno;
  String? cardNo;
  int? cardSegment;
  String? label;
  String? last4;
  String? name;
  String? tradeId;
  String? description;
  double? preAuthAmount;
  String? preAuthCurrency;
  double? money;
  double? sxMoney;
  String? currency;
  int? status;
  int? tradeType;
  String? tradeStatus;
  double? balance;
  String? createTime;
  int? updateTime;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['id'] = id;
    map['userId'] = userId;
    map['cardId'] = cardId;
    map['sfCardId'] = sfCardId;
    map['sfTradeId'] = sfTradeId;
    map['cardSeqno'] = cardSeqno;
    map['cardNo'] = cardNo;
    map['cardSegment'] = cardSegment;
    map['label'] = label;
    map['last4'] = last4;
    map['name'] = name;
    map['tradeId'] = tradeId;
    map['description'] = description;
    map['preAuthAmount'] = preAuthAmount;
    map['preAuthCurrency'] = preAuthCurrency;
    map['money'] = money;
    map['sxMoney'] = sxMoney;
    map['currency'] = currency;
    map['status'] = status;
    map['tradeType'] = tradeType;
    map['tradeStatus'] = tradeStatus;
    map['balance'] = balance;
    map['createTime'] = createTime;
    map['updateTime'] = updateTime;
    return map;
  }

}