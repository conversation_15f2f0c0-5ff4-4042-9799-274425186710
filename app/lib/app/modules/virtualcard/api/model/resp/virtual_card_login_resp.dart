import 'package:flutter_metatel/app/data/models/res/base_res_model.dart';

/// data : {"Token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE2ODMzMDE0NTAsImlhdCI6MTY4MzI5NDI1MCwidXNlcl9pZCI6InRfZTU2Zjc5ZDgxNzcifQ.fc0BblcY3mLUdhkXPxRbo0oOsFt9zVCXDJXgwkGCRgI","TokenExp":"2023-05-05 23:44:10","UserName":"wuyingbin","Email":"<EMAIL>","LastTime":"2023-05-05 21:44:10","LastIp":"127.0.0.1"}

class VirtualCardLogin extends BaseRes{
  VirtualCardLogin({
      this.data,});

  VirtualCardLogin.fromJson(Map<String, dynamic> json) :super.fromJson(json){
    data = json['data'] != null ? LoginResp.fromJson(json['data']) : null;
  }
  LoginResp? data;


}

/// Token : "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE2ODMzMDE0NTAsImlhdCI6MTY4MzI5NDI1MCwidXNlcl9pZCI6InRfZTU2Zjc5ZDgxNzcifQ.fc0BblcY3mLUdhkXPxRbo0oOsFt9zVCXDJXgwkGCRgI"
/// TokenExp : "2023-05-05 23:44:10"
/// UserName : "wuyingbin"
/// Email : "<EMAIL>"
/// LastTime : "2023-05-05 21:44:10"
/// LastIp : "127.0.0.1"

class LoginResp {
  LoginResp({
      this.token, 
      this.tokenExp, 
      this.userName, 
      this.email, 
      this.lastTime, 
      this.lastIp,});

  LoginResp.fromJson(Map<String, dynamic> json) {
    token = json['Token'];
    tokenExp = json['TokenExp'];
    userName = json['UserName'];
    email = json['Email'];
    lastTime = json['LastTime'];
    lastIp = json['LastIp'];
  }
  String? token;
  String? tokenExp;
  String? userName;
  String? email;
  String? lastTime;
  String? lastIp;


}