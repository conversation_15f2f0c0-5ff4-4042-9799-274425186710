import 'package:flutter_metatel/app/data/models/res/base_res_model.dart';

/// data : {"Data":[{"id":3,"userId":4,"cardSeqno":"CF9542DD5","cardId":"c_99da0abd03ea4baf9a03","cardNo":"****************","cardSegment":531847,"name":"<PERSON>","firstName":"<PERSON>","lastName":"Schmitt","cardAddress1":"1010 Pine St LOT 12-E","cardAddress2":"","city":"Vienna","zipCode":"31092","label":"默认","state":4,"consume":0,"recharge":0,"refund":0,"rollOut":0,"balance":0,"safetyCode":"","expirationDate":"2024-4","last4":"1711","createTime":"1680857612"},{"id":2,"userId":4,"cardSeqno":"CF5D58525","cardId":"ch_9b4f0ddd2b71417a9fce","cardNo":"****************","cardSegment":531847,"name":"Milton Schmitt","firstName":"Milton","lastName":"Schmitt","cardAddress1":"1010 Pine St LOT 12-E","cardAddress2":"","city":"Vienna","zipCode":"31092","label":"默认","state":1,"consume":0,"recharge":0,"refund":0,"rollOut":0,"balance":100,"safetyCode":"","expirationDate":"2024-4","last4":"9510","createTime":"1680857507"},{"id":1,"userId":4,"cardSeqno":"CF33BBD26","cardId":"c_454977f96ff74d7785d9","cardNo":"****************","cardSegment":531847,"name":"Milton Schmitt","firstName":"Milton","lastName":"Schmitt","cardAddress1":"1010 Pine St LOT 12-E","cardAddress2":"","city":"Vienna","zipCode":"31092","label":"默认","state":1,"consume":0,"recharge":0,"refund":0,"rollOut":0,"balance":190,"safetyCode":"","expirationDate":"2024-4","last4":"9565","createTime":"1680857431"}],"Count":3}

class VirtualCardListResp extends BaseRes{
  VirtualCardListResp({
      this.data,});

  VirtualCardListResp.fromJson(dynamic json):super.fromJson(json){
    data = json['data'] != null ? CardList.fromJson(json['data']) : null;
  }
  CardList? data;
  
}

/// Data : [{"id":3,"userId":4,"cardSeqno":"CF9542DD5","cardId":"c_99da0abd03ea4baf9a03","cardNo":"****************","cardSegment":531847,"name":"Milton Schmitt","firstName":"Milton","lastName":"Schmitt","cardAddress1":"1010 Pine St LOT 12-E","cardAddress2":"","city":"Vienna","zipCode":"31092","label":"默认","state":4,"consume":0,"recharge":0,"refund":0,"rollOut":0,"balance":0,"safetyCode":"","expirationDate":"2024-4","last4":"1711","createTime":"1680857612"},{"id":2,"userId":4,"cardSeqno":"CF5D58525","cardId":"ch_9b4f0ddd2b71417a9fce","cardNo":"****************","cardSegment":531847,"name":"Milton Schmitt","firstName":"Milton","lastName":"Schmitt","cardAddress1":"1010 Pine St LOT 12-E","cardAddress2":"","city":"Vienna","zipCode":"31092","label":"默认","state":1,"consume":0,"recharge":0,"refund":0,"rollOut":0,"balance":100,"safetyCode":"","expirationDate":"2024-4","last4":"9510","createTime":"1680857507"},{"id":1,"userId":4,"cardSeqno":"CF33BBD26","cardId":"c_454977f96ff74d7785d9","cardNo":"****************","cardSegment":531847,"name":"Milton Schmitt","firstName":"Milton","lastName":"Schmitt","cardAddress1":"1010 Pine St LOT 12-E","cardAddress2":"","city":"Vienna","zipCode":"31092","label":"默认","state":1,"consume":0,"recharge":0,"refund":0,"rollOut":0,"balance":190,"safetyCode":"","expirationDate":"2024-4","last4":"9565","createTime":"1680857431"}]
/// Count : 3

class CardList {
  CardList({
      this.data, 
      this.count,});

  CardList.fromJson(dynamic json) {
    if (json['Data'] != null) {
      data = [];
      json['Data'].forEach((v) {
        data?.add(CarListItemInfo.fromJson(v));
      });
    }
    count = json['Count'];
  }
  List<CarListItemInfo>? data;
  int? count;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    if (data != null) {
      map['Data'] = data?.map((v) => v.toJson()).toList();
    }
    map['Count'] = count;
    return map;
  }

}

/// id : 3
/// userId : 4
/// cardSeqno : "CF9542DD5"
/// cardId : "c_99da0abd03ea4baf9a03"
/// cardNo : "****************"
/// cardSegment : 531847
/// name : "Milton Schmitt"
/// firstName : "Milton"
/// lastName : "Schmitt"
/// cardAddress1 : "1010 Pine St LOT 12-E"
/// cardAddress2 : ""
/// city : "Vienna"
/// zipCode : "31092"
/// label : "默认"
/// state : 4
/// consume : 0
/// recharge : 0
/// refund : 0
/// rollOut : 0
/// balance : 0
/// safetyCode : ""
/// expirationDate : "2024-4"
/// last4 : "1711"
/// createTime : "1680857612"

class CarListItemInfo {

  CarListItemInfo({
      this.id, 
      this.userId, 
      this.cardSeqno, 
      this.cardId, 
      this.cardNo, 
      this.cardSegment, 
      this.name, 
      this.firstName, 
      this.lastName, 
      this.cardAddress1, 
      this.cardAddress2, 
      this.city, 
      this.zipCode, 
      this.label, 
      this.state, 
      this.consume, 
      this.recharge, 
      this.refund, 
      this.rollOut, 
      this.balance, 
      this.safetyCode, 
      this.expirationDate, 
      this.last4, 
      this.createTime,});

  CarListItemInfo.fromJson(dynamic json) {
    id = json['id'];
    userId = json['userId'];
    cardSeqno = json['cardSeqno'];
    cardId = json['cardId'];
    cardNo = json['cardNo'];
    cardSegment = json['cardSegment'];
    name = json['name'];
    firstName = json['firstName'];
    lastName = json['lastName'];
    cardAddress1 = json['cardAddress1'];
    cardAddress2 = json['cardAddress2'];
    city = json['city'];
    zipCode = json['zipCode'];
    label = json['label'];
    state = json['state'];
    consume = json['consume'];
    int recharge = json['recharge']??0;
    this.recharge = recharge/100;
    int refund = json['refund']??0;
    this.refund = refund/100;
    int rollOut = json['rollOut']??0;
    this.rollOut = rollOut/100;
    int balance = json['balance']??0;
    this.balance = balance/100;
    safetyCode = json['safetyCode'];
    expirationDate = json['expirationDate'];
    last4 = json['last4'];
    createTime = json['createTime'];
  }
  int? status;
  int? id;
  String? userId;
  String? cardSeqno;
  String? cardId;
  String? cardNo;
  int? cardSegment;
  String? name;
  String? firstName;
  String? lastName;
  String? cardAddress1;
  String? cardAddress2;
  String? city;
  String? zipCode;
  String? label;
  int? state;
  int? consume;
  double? recharge;
  double? refund;
  double? rollOut;
  double? balance;
  String? safetyCode;
  String? expirationDate;
  String? last4;
  String? createTime;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['id'] = id;
    map['userId'] = userId;
    map['cardSeqno'] = cardSeqno;
    map['cardId'] = cardId;
    map['cardNo'] = cardNo;
    map['cardSegment'] = cardSegment;
    map['name'] = name;
    map['firstName'] = firstName;
    map['lastName'] = lastName;
    map['cardAddress1'] = cardAddress1;
    map['cardAddress2'] = cardAddress2;
    map['city'] = city;
    map['zipCode'] = zipCode;
    map['label'] = label;
    map['state'] = state;
    map['consume'] = consume;
    map['recharge'] = recharge;
    map['refund'] = refund;
    map['rollOut'] = rollOut;
    map['balance'] = balance;
    map['safetyCode'] = safetyCode;
    map['expirationDate'] = expirationDate;
    map['last4'] = last4;
    map['createTime'] = createTime;
    return map;
  }

}