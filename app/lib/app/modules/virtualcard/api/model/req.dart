/// firstName : "Milton"
/// lastName : "Schmitt"
/// card_segment : "531847"
/// city : "Vienna"
/// country_code : "US"
/// state : "GA"
/// street : "1010 Pine St LOT 12-E"
/// zip : "31092"
/// deposit : 1000

class AddReq {
  AddReq({
      this.firstName, 
      this.lastName, 
      this.cardSegment, 
      this.city, 
      this.countryCode, 
      this.state, 
      this.street, 
      this.zip, 
      this.deposit,});

  AddReq.fromJson(dynamic json) {
    firstName = json['firstName'];
    lastName = json['lastName'];
    cardSegment = json['card_segment'];
    city = json['city'];
    countryCode = json['country_code'];
    state = json['state'];
    street = json['street'];
    zip = json['zip'];
    deposit = json['deposit'];
  }
  String? firstName;
  String? lastName;
  String? cardSegment;
  String? city;
  String? countryCode;
  String? state;
  String? street;
  String? zip;
  int? deposit;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['firstName'] = firstName;
    map['lastName'] = lastName;
    map['card_segment'] = cardSegment;
    map['city'] = city;
    map['country_code'] = countryCode;
    map['state'] = state;
    map['street'] = street;
    map['zip'] = zip;
    map['deposit'] = deposit;
    return map;
  }

}
class DetailReq {
  DetailReq({
    this.cardId,
    this.pageSize,
    this.pageNum,
    this.startTime,
    this.endTime,
    this.tradeId,
    this.tradeType,
    this.status,});
  String? cardId;
  String? pageSize;
  String? pageNum;
  String? startTime;
  String? endTime;
  String? tradeId;
  String? tradeType;
  String? status;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['cardId'] = cardId;
    map['pageSize'] = pageSize;
    map['pageNum'] = pageNum;
    map['startTime'] = startTime;
    map['endTime'] = endTime;
    map['tradeId'] = tradeId;
    map['trade_type'] = tradeType;
    map['status'] = status;
    return map;
  }

}
class RecordReq {
  RecordReq({
    this.pageSize,
    this.pageNum,
    this.startTime,
    this.endTime,
    this.tradeId,
    this.tradeType,
    this.status,});
  String? pageSize;
  String? pageNum;
  String? startTime;
  String? endTime;
  String? tradeId;
  String? tradeType;
  String? status;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['pageSize'] = pageSize;
    map['pageNum'] = pageNum;
    map['startTime'] = startTime;
    map['endTime'] = endTime;
    map['tradeId'] = tradeId;
    map['trade_type'] = tradeType;
    map['status'] = status;
    return map;
  }

}
class TransInOrOutReq{
  final String carId;
  final String amount;
  TransInOrOutReq(this.carId, this.amount);
  Map<String, dynamic> toJson(){
    Map<String, dynamic> m={
      "cardId":carId,
      "amount":amount,
    };
    return m;
  }
}
class TransRecordReq{
  final int pageSize;
  final int pageNum;

  String? startTime;
  String? endTime;
  String? tradeId;
  String? tradeType;
  String? status;
  TransRecordReq(this.pageSize, this.pageNum);
  Map<String,dynamic> toJson(){
    Map<String,dynamic> map={
      "pageSize":pageSize,
      "pageNum":pageNum,
      "startTime":startTime,
      "endTime":endTime,
      "tradeId":tradeId,
      "tradeType":tradeType,
      "status":status,
    };
    return map;
  }
}
