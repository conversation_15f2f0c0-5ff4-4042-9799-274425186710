import 'dart:core';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../core/values/colors.dart';
import '../../../r.dart';
import '../../widgets/divider_cus.dart';
import '../../widgets/text_middle_overlow.dart';
 class RecordItem extends StatelessWidget{
  final String title;
  final String amount;
  final String time;
  final String status;
  final Color? stateColor;
  final String? titleLabel;
  final String? icon;
  final ValueChanged? changedCallback;

  const RecordItem({
    super.key,
    required this.title,
    required this.amount,
    required this.time,
    required this.status,
    this.stateColor,
    this.titleLabel,
    this.icon,
    this.changedCallback,
  });
  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        InkWell(
          onTap: () {changedCallback?.call(null);},
          child: Container(
            padding: const EdgeInsets.only(left: 16,right: 16,top: 10).r,
            child: Row(
              children: [
                Image.asset(
                  icon ?? R.icoVirtualCardRecord,
                  width: 44.r,
                  height: 44.r,
                ),
                SizedBox(
                  width: 10.r,
                ),
                Expanded(child:  Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          titleLabel ?? "",
                          style: TextStyle(
                            color: AppColors.colorFF333333,
                            fontSize: 14.sp,
                          ),
                        ),
                        ConstrainedBox(
                          constraints: BoxConstraints(maxWidth: 150.w),
                          child: MiddleText(
                            title,
                            WXTextOverflow.ellipsisMiddle,
                            style: TextStyle(
                              color: AppColors.colorFF333333,
                              fontSize: 14.sp,
                            ),
                          ),
                        ),
                        SizedBox(
                          width: 10.w,
                        ),
                        Expanded(
                          child: Text(
                            amount,
                            textAlign: TextAlign.end,
                            style: TextStyle(
                              color: AppColors.colorFF000000,
                              fontSize: 16.sp,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ],
                    ),
                    SizedBox(
                      height: 9.h,
                    ),
                    Row(
                      children: [
                        Text(
                          time,
                          style: TextStyle(
                            color: AppColors.colorFF808080,
                            fontSize: 12.sp,
                          ),
                        ),
                        SizedBox(
                          width: 10.w,
                        ),
                        Expanded(
                          child: Text(
                            status,
                            textAlign: TextAlign.end,
                            style: TextStyle(
                              color: stateColor??AppColors.colorFF000000,
                              fontSize: 12.sp,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),),
              ],
            ),
          ),
        ),
        SizedBox(
          height: 14.h,
        ),
        DividerCus(
          indent: 64.r,
        ),
      ],
    );
  }
}