import 'package:flutter/services.dart';
import 'package:flutter_metatel/app/modules/virtualcard/model/virtual_card_model.dart';
import 'package:flutter_metatel/core/languages/l.dart';
import 'package:get/get.dart';

///获取卡号取前6位和后四位中间省略
String getCarNumIncomplete(String s){
  String carPre=s.length>6?s.substring(0,6):s;
  var re = s.substring(carPre.length);
  String carAfter=re.length>4?re.substring(re.length-4,re.length):re;
  String carNum="$carPre****$carAfter";
  return carNum;
}
String getCardStateText(VirtualCardStatus state){
  String text="";
  switch(state){
    case VirtualCardStatus.freeze:
      text=L.card_freeze.tr;
      break;
    case VirtualCardStatus.sysFreeze:
      text=L.card_sys_freeze.tr;
      break;
    case VirtualCardStatus.using:
      text=L.card_using.tr;
      break;
    case VirtualCardStatus.del:
      text=L.card_del.tr;
      break;

    case VirtualCardStatus.protect:
      text=L.card_protect.tr;
      break;
    case VirtualCardStatus.createIng:
      text=L.card_ing.tr;
      break;
    default:
      text=L.unknown.tr;
      break;
  }

  return text;
}
String getBaseAccountStateText(VirtualCardAccountStatus state){
  String text="";
  switch(state){
    case VirtualCardAccountStatus.freeze:
      text=L.card_freeze.tr;
      break;
    case VirtualCardAccountStatus.normal:
      text=L.normal.tr;
      break;
    case VirtualCardAccountStatus.banned:
      text=L.banned.tr;
      break;
    case VirtualCardAccountStatus.reviewing:
      text=L.reviewing.tr;
      break;
    case VirtualCardAccountStatus.failed:
      text = L.connection_failed.tr;
      break;
    default:
      text=L.connect_tips.tr;
      break;
  }

  return text;
}


class NumLengthInputFormatter extends TextInputFormatter {
  int? decimalLength;
  int? integerLength;
  bool? allowInputDecimal;

  NumLengthInputFormatter({this.decimalLength, this.integerLength}) : super();

  @override
  TextEditingValue formatEditUpdate(TextEditingValue oldValue, TextEditingValue newValue) {
    String value = newValue.text;
    int selectionIndex = newValue.selection.end;
    if (newValue.text.contains('.')) {
      int pointIndex = newValue.text.indexOf('.');
      String beforePoint = newValue.text.substring(0, pointIndex);
      //小数点前内容大于integerLength
      if (integerLength!=null&&beforePoint.length > integerLength!) {
        value = oldValue.text;
        selectionIndex = oldValue.selection.end;
      } else
        //小数点前内容小于等于integerLength
          {
        String afterPoint = newValue.text.substring(pointIndex + 1, newValue.text.length);
        if (decimalLength!=null&&afterPoint.length > decimalLength!) {
          value = oldValue.text;
          selectionIndex = oldValue.selection.end;
        }
      }
    } else {
      if (integerLength!=null&&newValue.text.length > integerLength!) {
        value = oldValue.text;
        selectionIndex = oldValue.selection.end;
      }
    }
    return TextEditingValue(
      text: value,
      selection: TextSelection.collapsed(offset: selectionIndex),
    );
  }
}

