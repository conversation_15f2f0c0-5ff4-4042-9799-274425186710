class VirtualCardAccountModel {
  int? status;
  String? address;
  double? balance;
  List<VirtualCardModel>? cardList;

  VirtualCardAccountModel({
    this.status,
    this.address,
    this.balance,
    this.cardList,
  });
  VirtualCardAccountModel.formJson(Map<String,dynamic> json){
    status=json['status'];
    address=json['address'];
    balance=json['balance'];
    if (json['card'] != null) {
      cardList = [];
      json['card'].forEach((v) {
        cardList?.add(VirtualCardModel.fromJson(v));
      });
    }
  }
}

class VirtualCardModel {
  String? carNum;
  int? status;

  ///有效期
  String? exp;

  ///CVV
  String? cvv;

  ///持有人
  String? own;

  ///账单地址
  String? billingAddress;

  ///创建时间
  String? createTime;

  List<VirtualCardTransRecord>? transRecordList;

  VirtualCardModel({
    this.carNum,
    this.status,
    this.exp,
    this.cvv,
    this.own,
    this.billingAddress,
    this.createTime,
    this.transRecordList,
  });
  VirtualCardModel.fromJson(Map<String,dynamic> json){
    carNum=json['car_num'];
    status=json['status'];
    exp=json['exp'];
    cvv=json['cvv'];
    own=json['own'];
    billingAddress=json['billingAddress'];
    createTime=json['createTime'];
    if (json['record'] != null) {
      transRecordList = [];
      json['record'].forEach((v) {
        transRecordList?.add(VirtualCardTransRecord.fromJson(v));
      });
    }
  }
}

class VirtualCardTransRecord {
  String? name;
  String? time;
  String? money;
  int? status;

  VirtualCardTransRecord({
    this.name,
    this.time,
    this.money,
    this.status,
  });
  VirtualCardTransRecord.fromJson(Map<String,dynamic> json){
    name=json['name'];
    time=json['time'];
    money=json['money'];
    status=json['status'];
  }
}

enum VirtualCardAccountStatus {
  normal,
  reviewing,
  banned,
  freeze,
  none,
  failed,
}

enum VirtualCardStatus {
  createIng,
  using,
  protect,
  freeze,
  del,
  sysFreeze
}

enum VirtualCardTransStatus {
  none,
  finish,
  ing,
}
enum VirtualCardTransType {
  none,
  income,
  out,
}
