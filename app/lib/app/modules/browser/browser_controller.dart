import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/data/services/config_service.dart';
import 'package:flutter_metatel/core/values/config.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:share_plus/share_plus.dart';

import '../../../core/languages/l.dart';
import '../../../core/utils/app_log.dart';
import '../../../core/utils/util.dart';
import '../../../core/values/colors.dart';
import '../../../r.dart';
import '../../data/models/res/oauth2_authorize.model.dart';
import '../../data/providers/api/api.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
class BrowserController extends GetxController {
  InAppWebViewController? _webViewController;
  RxString webInitUrl = "".obs;
  String?  webCurUrl;

  get webViewController => _webViewController;

  set setWebViewController(InAppWebViewController webViewController) {
    _webViewController = webViewController;
  }

  final count = 0.obs;

  @override
  void onInit() async {
    super.onInit();
    String? userName = Get.find<AppConfigService>().getUserName();
    if (userName != null) {
      webInitUrl.value=Config.webViewStare+Config.node();
      AppLogger.d('webInitUrl.value url: ${webInitUrl.value}');

    }
  }

  @override
  void onClose() {}

  void increment() => count.value++;

  ///是否直接回到上级界面
  Future<bool> goBack() async {
    if (_webViewController == null) {
      return true;
    }
    var canBackWebView = await _webViewController!.canGoBack();
    if (canBackWebView) {
      _webViewController!.goBack();
      return false;
    } else {
      return true;
    }
  }

  share() async {
    if(webCurUrl==null){
      AppLogger.e("页面未加载完成无法分享！");
      return;
    }
    AppLogger.d('share original url: $webCurUrl');
    var callAsyncJavaScriptResult = await _webViewController
        ?.callAsyncJavaScript(functionBody: "document.title");
    String? title = callAsyncJavaScriptResult?.value;
    title = title??"".replaceAll("\"", "");
    String shareUrl = webCurUrl!.split("?token=").first;
    AppLogger.d('share shareUrl: $shareUrl');
    AppLogger.d('share title: $title');
    Share.share(L.share_to_other.trParams({"title": title, "url": shareUrl}),
        subject: title);
  }

  ///根据webView 传过来的 clientId 获取授权code 并给到webView
  getCode(String clientId) async {
    if(clientId.isEmpty){
      toast(L.authorization_failed_please_try_again_late.tr);
      return;
    }
    var response =
        await Get.find<ApiProvider>().getAuth2Authorize(clientId, "all");
    Oauth2AuthorizeModelData? data = response.data?.data;
    if (data == null || _webViewController == null) {
      AppLogger.d("getCode Oauth2AuthorizeModelData==$data  _webViewController=$_webViewController");
      toast(L.authorization_failed_please_try_again_late.tr);
      return;
    }
    String jsonData = json.encode(data.toJson());
    AppLogger.d("getCode jsonData==$jsonData");
    _webViewController!.evaluateJavascript(source: 'callJS($jsonData)');
    Get.back();
  }

  Widget buildBottomGetTokenWidget(BuildContext context, String clientId) {
    return Center(
      child: Column(
        children: [
          Container(
            margin: const EdgeInsets.only(top: 20).r,
            child: Image.asset(
              R.icoStare,
              width: 48.h,
              height: 48.h,
            ),
          ),
          Container(
            margin: const EdgeInsets.only(top: 20).r,
            child: Text(L.stare.tr),
          ),
          Container(
            margin: const EdgeInsets.only(top: 30).r,
            child: Text(
              L.apply_for_your_nickname_and_avatar.tr,
              style: TextStyle(fontSize: 16.sp, fontWeight: FontWeight.bold),
            ),
          ),
          Container(
            margin: const EdgeInsets.only(top: 43, bottom: 26).r,
            child: Row(
              children: [
                const Spacer(),
                TextButton(
                  style: ButtonStyle(
                    shape: MaterialStateProperty.all(
                      RoundedRectangleBorder(
                        borderRadius:
                            BorderRadius.all(Radius.circular(19.r).r).r,
                      ),
                    ),
                    side: MaterialStateProperty.all(
                      BorderSide(color: AppColors.colorFFd9d9d9, width: 0.5.w),
                    ),
                    fixedSize: MaterialStateProperty.all(Size(132.w, 39.h)),
                  ),
                  onPressed: () {
                    Get.back();
                  },
                  child: Text(
                    L.refuse.tr,
                    overflow: TextOverflow.ellipsis,
                    style: TextStyle(
                      fontSize: 16.sp,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                  ),
                ),
                SizedBox(
                  width: 27.w,
                ),
                TextButton(
                  style: ButtonStyle(
                    shape: MaterialStateProperty.all(
                      RoundedRectangleBorder(
                        borderRadius:
                            BorderRadius.all(Radius.circular(19.r).r).r,
                      ),
                    ),
                    side: MaterialStateProperty.all(
                      BorderSide(
                          color: Theme.of(context).colorScheme.primary,
                          width: 0.5.w),
                    ),
                    fixedSize: MaterialStateProperty.all(Size(132.w, 39.h)),
                    backgroundColor: MaterialStateProperty.all(
                      Theme.of(context).colorScheme.primary,
                    ),
                  ),
                  onPressed: () async {
                    getCode(clientId);
                  },
                  child: Text(
                    L.allow.tr,
                    overflow: TextOverflow.ellipsis,
                    style: TextStyle(fontSize: 16.sp, color: AppColors.white),
                  ),
                ),
                const Spacer(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void installListener(InAppWebViewController c, BuildContext context) {
    c.addJavaScriptHandler(
        handlerName: WebViewJsChannel.GetToken,
        callback: (args) {
          AppLogger.d("DaoBrowserController ClientID====${args.toString()}");
          if (args.isNotEmpty) {
            getCode(args[0]);
          }
        });
  }
}
