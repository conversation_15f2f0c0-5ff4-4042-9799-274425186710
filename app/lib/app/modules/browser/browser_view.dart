import 'dart:async';
import 'dart:io';
import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_metatel/core/values/colors.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:get/get.dart';

import '../../../r.dart';
import 'browser_controller.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
class BrowserView extends GetView<BrowserController> {
  BrowserView({Key? key}) : super(key: key);

  @override
  final BrowserController controller =
  Get.put(BrowserController(), permanent: true);

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
        onWillPop: Platform.isIOS ? null : () => controller.goBack(),
        child: Scaffold(
          appBar: PreferredSize(
            preferredSize:
            Size.fromHeight(MediaQueryData
                .fromView(window)
                .padding
                .top),
            child: const SafeArea(
              top: true,
              child: Offstage(),
            ),
          ),
          body: Obx(() {
            return controller.webInitUrl.value.isEmpty ? Container() : Center(
                child: InAppWebView(
                  initialUrlRequest: URLRequest(url:WebUri(controller.webInitUrl.value)),
                  initialSettings: InAppWebViewSettings(
                    javaScriptCanOpenWindowsAutomatically: true,
                    supportMultipleWindows: true,
                    isFraudulentWebsiteWarningEnabled: true,
                    safeBrowsingEnabled: true,
                    mediaPlaybackRequiresUserGesture: false,
                    allowsInlineMediaPlayback: true,
                  ),
                  onWebViewCreated: (webViewController) {
                    controller.setWebViewController = webViewController;
                    controller.installListener(webViewController,context);
                    },

                  onLoadStop: (c, url) {
                    controller.webCurUrl=url?.toString();
                  },
                ) /*Text(
          '敬请期待！',
          style: TextStyle(fontSize: 20),
        ),*/
            );
          }),
          floatingActionButton: Container(
            margin: const EdgeInsets.only(top: 9).r,
            padding: const EdgeInsets.only(left: 10, right: 10).r,

            width: 75.w,
            height: 25.h,
            decoration: BoxDecoration(
                color: AppColors.colorFFFFFFFB,
                borderRadius: BorderRadius.all(
                  const Radius.circular(16).r,
                ),
                border: Border.all(color: AppColors.colorFFd9d9d9, width: 0.5.w)
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                GestureDetector(
                  onTap: () {
                    controller.share();
                  },
                  child: Image.asset(R.icoShare, width: 17.w, height: 17.h,),
                ),
                Container(
                  height: 17.5.h,
                  width: 0.5.w,
                  color: AppColors.colorFFF8F8F8,
                ),
                GestureDetector(
                  onTap: () {
                    Get.back();
                  },
                  child: Image.asset(R.icoClose, width: 17.w, height: 17.h,),
                ),
              ],
            ),
          ),
          floatingActionButtonLocation: FloatingActionButtonLocation.endTop,
        )) /*)*/;
  }


}
