import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:flutter_metatel/app/data/models/hongbao_check_status_model.dart';
import 'package:flutter_metatel/app/data/models/hongbao_qiang_response_model.dart';
import 'package:flutter_metatel/app/data/models/hongbao_tokens_model.dart';
import 'package:flutter_metatel/core/values/config.dart';
import 'package:flutter_metatel/core/values/hongbao.dart';
import '../../../../core/utils/app_log.dart';
import 'base_dio_api.dart';

class RedPacketApi extends BaseDioClient {
  static Future<String> getRedPacketApiUrl() async {
    var url = await Hongbao.url();
    AppLogger.d('RedPacketApi URL: $url');
    return Config.handleUrl(url);
  }

// 获取代币
  Future<Response<List<HongbaoTokensModelData>>> getTokens() async {
    var url = await getRedPacketApiUrl();
    if (url.isEmpty) {
      AppLogger.e('getTokens url is null');
      return Response(
        statusCode: -200,
        statusMessage: '',
        data: [],
        requestOptions: RequestOptions(path: ''),
      );
    }
    url = url + HongbaoApiUrl.getTokens;
    try {
      var response = await createDio().get(url);

      List<HongbaoTokensModelData> tokens = <HongbaoTokensModelData>[];
      if (response.statusCode == 200) {
        if (response.data is Map<String, dynamic> &&
            response.data['data'] != null) {
          AppLogger.d('getTokens tokens: in if ');
          final jsonData = response.data['data'];
          for (var item in jsonData) {
            tokens.add(HongbaoTokensModelData.fromJson(item));
          }
        }
      }

      AppLogger.d('getTokens tokens: ${tokens}');
      AppLogger.d('getTokens response: ${response.data}');
      return Response(
        data: tokens,
        statusCode: response.statusCode,
        statusMessage: response.statusMessage,
        requestOptions: response.requestOptions,
      );
    } catch (e) {
      AppLogger.e('getTokens $e');
      return Response(
        statusCode: -200,
        statusMessage: '$e',
        data: [],
        requestOptions: RequestOptions(path: ''),
      );
    }
  }

  Future<Response<Map<String, dynamic>>> feeAndInfo(
      double amount, String token_address, double fixed_amount) async {
    AppLogger.e('fixed_amount =$fixed_amount ');

    var url = await getRedPacketApiUrl();
    if (url.isEmpty) {
      AppLogger.e('feeAndInfo url is null');
      return Response(
        statusCode: -200,
        statusMessage: 'URL is null or empty',
        data: {},
        requestOptions: RequestOptions(path: ''),
      );
    }
    url = url + HongbaoApiUrl.FeeAndInfo;

    try {
      // if (amount < getMinAmountForToken(token_address)) {
      //   AppLogger.e('Amount is less than the minimum allowed for this token');
      //   return Response(
      //     statusCode: -200,
      //     statusMessage: 'Amount is too small',
      //     data: {},
      //     requestOptions: RequestOptions(path: ''),
      //   );
      // }
      var response = await createDio().post(url, data: {
        'amount': amount,
        'token_address': token_address,
        'fixed_amount': fixed_amount,
      });

      Map<String, dynamic> feeInfo = {};
      if (response.statusCode == 200) {
        feeInfo = Map<String, dynamic>.from(response.data);
      }
      AppLogger.d('feeAndInfo response: ${json.encode(feeInfo)}');
      return Response(
        statusCode: response.statusCode,
        statusMessage: response.statusMessage,
        data: feeInfo,
        requestOptions: response.requestOptions,
      );
    } catch (e) {
      AppLogger.e('feeAndInfo error: $e');
      return Response(
        statusCode: -200,
        statusMessage: 'Error: $e',
        data: {},
        requestOptions: RequestOptions(path: ''),
      );
    }
  }

  double getMinAmountForToken(String tokenAddress) {
    return 0.01;
  }

  Future<Response?> createRedPacket(String account, String info) async {
    var url = await getRedPacketApiUrl();
    if (url.isEmpty) {
      AppLogger.e('createRedPacket url is null');
      return Response(
        statusCode: -200,
        statusMessage: 'URL is null or empty',
        data: {},
        requestOptions: RequestOptions(path: ''),
      );
    }
    url = url + HongbaoApiUrl.createRedPacket;

    try {
      Map<String, dynamic> requestData = {
        'account': account.toString(),
        'info': info
      };
      var response = await createDio().post(url, data: requestData);
      if (response.statusCode == 200) {
        AppLogger.d('createRedPacket success: ${response.data}');
      } else {
        AppLogger.e('createRedPacket failed: ${response.statusMessage}');
      }
      return response;
    } catch (e) {
      AppLogger.e('createRedPacket error: $e');
      return null;
    }
  }

  Future<HongbaoCheckStatusModel?> checkRedPacketStatus(
      String account, String msgId) async {
    var url = await getRedPacketApiUrl();
    url = url + HongbaoApiUrl.checkRedPacketInfo;
    if (url.isEmpty) {
      AppLogger.e('checkRedPacketStatus url is null');
    }
    try {
      var response = await createDio()
          .post(url, data: {'account': account, 'topic': msgId});
      if (response.statusCode == 200) {
        AppLogger.d('checkRedPacketStatus success: ${jsonEncode(response.data)}');
      } else {
        AppLogger.e('checkRedPacketStatus failed: ${response.statusMessage}');
      }
      return HongbaoCheckStatusModel.fromJson(response.data);
    } catch (e) {
      AppLogger.e('checkRedPacketStatus error: $e');
      return null;
    }
  }

  Future<HongbaoQiangResponseModelData?> qiangHongBaoRequest(
      String account, String info) async {
    var url = await getRedPacketApiUrl();
    url = url + HongbaoApiUrl.playGame;
    if (url.isEmpty) {
      AppLogger.e('qiangHongBaoRequest url is null');
      return null;
    }
    try {
      var response =
          await createDio().post(url, data: {'account': account, 'info': info});
      if (response.statusCode == 200) {
        AppLogger.d(
            'qiangHongBaoRequest success: ${jsonEncode(response.data)}');
        return HongbaoQiangResponseModelData.fromJson(response.data);
      } else {
        AppLogger.e('qiangHongBaoRequest failed: ${response.statusMessage}');
        return null;
      }

    } catch (e) {
      AppLogger.e('qiangHongBaoRequest error: $e');
      return null;
    }
  }
}
