/*
* 网络接口请求
*
*/
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:dio/io.dart';
import 'package:flutter_metatel/certificate/verify_certificate.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:flutter_metatel/core/utils/proxy_util.dart';
import 'package:get/get.dart'as g;

import '../../../../core/utils/util.dart';
import '../../../../core/values/config.dart';
import '../../enums/path.dart';
import '../../services/chatio_service.dart';
import '../../services/config_service.dart';


class BaseDioClient with DioMixin implements Dio{
  static bool isRequest = false;
  Dio createDio({BaseOptions? options}){

    return createBaseDio(options: options);
  }


  @override
  Future<Response<T>> post<T>(
      String path, {
        data,
        Map<String, dynamic>? queryParameters,
        Options? options,
        CancelToken? cancelToken,
        ProgressCallback? onSendProgress,
        ProgressCallback? onReceiveProgress,
      })async{
    Response<T> response = await super.post(
      path,
      data:data,
      queryParameters: queryParameters,
      options: options,
      cancelToken: cancelToken,
      onSendProgress: onSendProgress,
      onReceiveProgress: onReceiveProgress,
    );

    if (response.statusCode == 401) {
      AppLogger.e("net request get 401 initToken");
      await initToken();
      response = response = await super.post(
        path,
        data:data,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
        onSendProgress: onSendProgress,
        onReceiveProgress: onReceiveProgress,
      );
    }

    return response;
  }

  @override
  Future<Response<T>> get<T>(String path, {
    Map<String, dynamic>? queryParameters,
    Options? options,
    Object? data,
    CancelToken? cancelToken,
    ProgressCallback? onReceiveProgress,
  }) async{
    Response<T> response = await super.get(
      path,
      queryParameters: queryParameters,
      options: options,
      data: data,
      cancelToken: cancelToken,
      onReceiveProgress: onReceiveProgress,
    );

    if (response.statusCode == 401) {
      AppLogger.e("net request get 401 initToken");
      await initToken();
      response = await super.get(
        path,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
        onReceiveProgress: onReceiveProgress,
      );
    }
    return response;
  }

}