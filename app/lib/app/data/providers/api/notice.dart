//FileName notice
// <AUTHOR>
//@Date 2023/4/25 15:47
import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:get/get.dart'as g;

import '../../../../core/utils/app_log.dart';
import '../../../../core/utils/util.dart';
import '../../../../core/values/channel.dart';
import '../../models/channel_response_model.dart';
import '../../models/notice_msg_model.dart';
import '../../services/config_service.dart';

/// 发布公告
Future<String?> sendNotice(String channelId, String body,
    {List<String>? ats}) async {
  Map<String, dynamic> mapData = {};
  var userName = g.Get.find<AppConfigService>().getUserName();
  mapData['channel_id'] = channelId;
  mapData['username'] = userName;
  mapData['body'] = body;
  mapData['ats'] = ats;
  Response result;
  try {
    AppLogger.d("发布公告 text: ${json.encode(mapData)}");
    result =
        await getConnect().post(await Own.noticeApi(), data:json.encode(mapData));
  } catch (e) {
    logger.e(e);
    return null;
  }
  logger.e('发布公告  request body:${result.data}');

  if (result.statusCode != 200) {
    logger.e('发布公告  request code:${result.statusCode}');
    return null;
  }
  var model = ChannelDataRes.fromJson(result.data);
  if (model.code != 200) {
    logger.e('发布公告 response code:${model.code} msg:${model.msg}');
    return null;
  }
  return model.data;
}

/// 获取公告
Future<NoticeBean?> getNotice({int? page, int? size}) async {
  Response result;
  try {
    var url = '${await Own.noticeApi()}?page=$page&size=${size ?? 10}';
    AppLogger.d("获取公告 text:  page=$page api=$url");
    result = await getConnect().get(url);
  } catch (e) {
    logger.e(e);
    return null;
  }
  if (result.statusCode != 200) {
    logger.e('获取公告  request code:${result.statusCode}');
    return null;
  }
  logger.d('获取公告  request body:${result.data}');
  var model = NoticeMsgModel.fromJson(result.data);
  if (model.code != 200) {
    logger.e('获取公告 response code:${model.code} msg:${model.message}');
    return null;
  }
  return model.data;
}

/// 删除公告
Future<String?> deleteNoticeById(String id) async {
  Response result;
  try {
    AppLogger.d("删除公告 id: $id");
    var url = '${await Own.noticeApi()}/$id';
    AppLogger.d("删除公告 url: $url");
    result = await getConnect().delete(url);
  } catch (e) {
    logger.e(e);
    return null;
  }
  if (result.statusCode != 200) {
    logger.e('删除公告  request code:${result.statusCode}');
    return null;
  }
  var model = ChannelDataRes.fromJson(result.data);
  if (model.code != 200) {
    logger.e('删除公告 response code:${model.code} msg:${model.msg}');
    return null;
  }
  return model.data;
}

/// 公告权限获取
Future<bool?> getUserNoticeRole() async {
  Response result;
  try {
    AppLogger.d("公告权限获取");
    result = await getConnect().get('${await Own.noticeApi()}/role');
  } catch (e) {
    logger.e(e);
    return null;
  }
  if (result.statusCode != 200) {
    logger.e('公告权限获取  request code:${result.statusCode}');
    return null;
  }
  var model = NoticeRoleRes.fromJson(result.data);
  if (model.code != 200) {
    logger.e('公告权限获取 response code:${model.code} msg:${model.msg}');
    return null;
  }
  return model.data;
}
