import 'dart:convert';

import 'package:flutter_metatel/app/data/models/res/base_res_model_data.dart';
import 'package:flutter_metatel/app/data/providers/api/base_dio_api.dart';
import 'package:get/get.dart';

import '../../../../core/utils/app_log.dart';
import '../../../modules/mining/mining_pwd_util.dart';
import '../../services/config_service.dart';

class Plan21Api extends BaseDioClient {
  static String random = 'dc03364e82a210c1dadafb610dd2f1391393d03527ab59e5af8bd1efd9f23c6d';
  static String planApiUrl = ''; //"http://192.168.139.200:3092";
  static const baseOprate = '/api/basic-task/operate/update'; //上传基础任务
  static const clockIn = '/api/ci/clockIn';//打卡

  get21PlanApiUrl() {
    AppLogger.d('Plan21Api apiUrl=$planApiUrl');
    return planApiUrl;
    // return "http://192.168.139.200:3092";
  }

  Future<Response<BaseResDate>> submitBaseOperate(Map body) async {
    if (get21PlanApiUrl().isEmpty) {
      return const Response(
        statusCode: -100,
        body: null,
      );
    }
    var user = Get.find<AppConfigService>().getUserNameWithoutDomain();
    AppLogger.d('submitBaseOperate body=$body');
    String sigdata =
        await MiningPwdUtil.encryptForData(json.encode(body),rand:random) ?? '';
    Map<String, dynamic> query = {'account': user, 'info': sigdata};
    AppLogger.d('submitBaseOperate query=$query');

    var url = get21PlanApiUrl() + baseOprate;
    AppLogger.d('submitBaseOperate url=$url');

    BaseResDate? data;
    int? statusCode;
    String? statusText;
    try {
      var response = await createDio().post(
        url,
        data: query,
      );
      AppLogger.d('submitBaseOperate body =${response.data}');
      if (response.statusCode == 200) {
        data = BaseResDate.fromJson(response.data);
      }
    } catch (e) {
      AppLogger.e('createDidApi e =${e.toString()}');
    }

    /////

    return Response(
      statusCode: statusCode,
      statusText: statusText,
      body: data,
    );
  }


  //打卡 打卡类型，群聊：GroupChat，单聊：SingleChat
  Future<Response<BaseResDate>> submitClock(bool isGroup) async {
    if (get21PlanApiUrl().isEmpty) {
      return const Response(
        statusCode: -100,
        body: null,
      );
    }
    var user = Get.find<AppConfigService>().getUserNameWithoutDomain();

    Map<String,dynamic> map = {'address':user,'type':isGroup ? "GroupChat" : "SingleChat"};
    AppLogger.d('clockIn body=$map');
    String sigdata =
        await MiningPwdUtil.encryptForData(json.encode(map),rand:random) ?? '';
    Map<String, dynamic> query = {'account': user, 'info': sigdata};
    AppLogger.d('clockIn query=$query');

    var url = get21PlanApiUrl() + clockIn;
    AppLogger.d('clockIn url=$url');

    BaseResDate? data;
    int? statusCode;
    String? statusText;
    try {
      var response = await createDio().post(
        url,
        data: query,
      );
      AppLogger.d('clockIn body =${response.data}');
      if (response.statusCode == 200) {
        data = BaseResDate.fromJson(response.data);
      }
    } catch (e) {
      AppLogger.e('clockIn e =${e.toString()}');
    }

    return Response(
      statusCode: statusCode,
      statusText: statusText,
      body: data,
    );
  }

  Future<Response<BaseResDate>> getUserInfo() async {
    if (get21PlanApiUrl().isEmpty) {
      return const Response(
        statusCode: -100,
        body: null,
      );
    }
    var user = Get.find<AppConfigService>().getUserNameWithoutDomain();
    Map<String, dynamic> query = {'address': user};
    AppLogger.d('submit query=$query');

    var url = get21PlanApiUrl() + '/api/user/info';
    AppLogger.d('submit url=$url');

    BaseResDate? data;
    int? statusCode;
    String? statusText;
    try {
      var response = await createDio().get(
        url,
        data: query,
      );
      AppLogger.d('submit body =${response.data}');
      if (response.statusCode == 200) {
        data = BaseResDate.fromJson(response.data);
      }
    } catch (e) {
      AppLogger.e('submit e =${e.toString()}');
    }


    return Response(
      statusCode: statusCode,
      statusText: statusText,
      body: data,
    );
  }
}
