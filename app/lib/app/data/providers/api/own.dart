import 'dart:convert';

import 'package:get/get.dart' as g;

import 'package:dio/dio.dart';

import '../../../../core/utils/app_log.dart';
import '../../../../core/utils/util.dart';
import '../../../../core/values/channel.dart';
import '../../../../core/values/config.dart';
import '../../../modules/square/bean/square_banner_bean.dart';
import '../../models/avatar_model.dart';
import '../../models/channel_response_model.dart';
import '../../models/own_info_model.dart';
import '../../models/own_myself_model.dart';
import '../../services/config_service.dart';

////处理数据被清理了那种情况在提交信息会把之前设置的内容清理掉
Future<void> oneSubmitOwnInfo({bool isShowLoading = true}) async {
  var conf = g.Get.find<AppConfigService>();
  var userName = conf.getUserName();
  if(userName!=null){
    if(userName.contains('@')){
      userName = userName.substring(0,userName.indexOf('@')+1);
      List<OwnInfoModelData>? ownList = await getOtherInfoNew([userName]);
      var needUpload = true;
      if(ownList?.isNotEmpty??false){
        var own = ownList!.first;
        if((own.name?.isNotEmpty??false)&&(own.name?.contains('@')??false)&&!(own.name?.endsWith('@')??true)){
          needUpload = false;
        }
      }
      if(needUpload){
       await submitOwnInfo(isShowLoading: false);
      }
      conf.saveOneMySelfInfoNeedUpload(false);
    }

  }


}

/// 提交个人信息
Future<bool> submitOwnInfo(
    {String? nickName,
    String? ioiID,
    String? avatarUrl,
    bool isShowLoading = true}) async {
  if (isShowLoading) {
    showLoadingDialog( isBack: false);
  }
  bool resultBool = false;
  Map<String, dynamic> mapData = {};
  var conf = g.Get.find<AppConfigService>();
  var userName = conf.getUserName();
  var avatar = AvatarModel.fromJson(conf.getMySelfAvatarInfo() ?? {});
  if(!(userName?.contains('@')??false)){
    return false;
  }
  mapData['name'] = userName;
  mapData['nickname'] = nickName ?? conf.getMySelfDisplayName();
  mapData['ioiID'] = ioiID ?? conf.readIOIId()??"";
  mapData['avatar'] = avatarUrl ?? avatar.avatarUrl;

  conf.saveMySelfInfoNeedUpload(true);
  Response result;
  var createApi = await Own.createApi();
  try {
    if(createApi.isEmpty) {
      toast('server Failed to obtain service configuration');
      return false;
    }
    logger.d('提交个人信息  url =$createApi mapData:${json.encode(mapData)}');

    result = await getConnect().post(createApi, data:json.encode(mapData));
    if (result.statusCode != 200) {
      logger.e('提交个人信息  request code:${result.statusCode}');
    } else {
      var model = ChannelDataRes.fromJson(result.data);
      if (model.code != 200) {
        logger.e('提交个人信息 response code:${model.code} msg:${model.msg}');
      } else {
        conf.saveMySelfInfoNeedUpload(false);
        resultBool = true;
      }
    }
  } catch (e) {
    logger.e('createApi = $createApi error = $e');
  }
  if (isShowLoading) {
    dismissLoadingDialog();
  }
  return resultBool;
}

/// 获取别人信息
Future<List<OwnInfoModelData>?> getOtherInfo(List<String> names) async {
  if (names.isEmpty) {
    return null;
  }

  Map<String, dynamic> mapData = {};
  mapData['names'] = names;

  Response result;
  try {
    var listApi = await Own.listApi();
    logger.d('getOtherInfo  url =$listApi mapData:${json.encode(mapData)}');

    result = await getConnect().post(listApi, data : json.encode(mapData));
  } catch (e) {
    logger.e('getOtherInfo ${e.toString()}');
    return null;
  }

  if (result.statusCode != 200) {
    logger.e('获取别人信息  request code:${result.statusCode}');
    return null;
  }

  var model = OwnInfoModel.fromJson(result.data);
  if (model.code != 200) {
    logger.e('获取别人信息 response code:${model.code}');
    return null;
  }
  AppLogger.d("获取别人信息 success ${result.data}");
  return model.infos;
}

/// 获取别人信息new
Future<List<OwnInfoModelData>?> getOtherInfoNew(List<String> names) async {
  if (names.isEmpty) {
    return null;
  }

  Map<String, dynamic> mapData = {};
  mapData['names'] = names;

  Response result;
  try {
    var listApi = await Own.listApiNew();
    result = await getConnect().post(listApi,  data : json.encode(mapData));
  } catch (e) {
    logger.e(e);
    return null;
  }

  if (result.statusCode != 200) {
    logger.e('获取别人信息  request code:${result.statusCode}');
    return null;
  }

  var model = OwnInfoModel.fromJson(result.data);
  if (model.code != 200) {
    logger.e('获取别人信息 response code:${model.code}');
    return null;
  }
  AppLogger.d("获取别人信息 success ${result.data}");
  return model.infos;
}
/// 通过ioiID获取别人信息
Future<List<OwnInfoModelData>?> getOtherInfoByIoIID(String ioiId) async {
  Response result;
  try {
    String searchByIOIIDApi = await Own.searchByIOIIDApi();
    result = await getConnect().get("$searchByIOIIDApi$ioiId");
  } catch (e) {
    logger.e(e);
    return null;
  }

  if (result.statusCode != 200) {
    logger.e('获取别人信息  request code:${result.statusCode}');
    return null;
  }

  var model = OwnInfoModel.fromJson(result.data);

  if (model.code != 200) {
    logger.e('获取别人信息 response code:${model.code}');
    return null;
  }
  return model.infos;
}

/// 举报
Future<bool> reportSubmit(String data) async {

  Response result;
  try {
    result = await getConnect()
        .post(await Own.reportApi(),  data : data);
  } catch (e) {
    logger.e(e);
    return false;
  }

  if (result.statusCode != 200) {
    logger.e('举报失败  request code:${result.statusCode}');
    return false;
  }
  logger.d('举报  request result.data:${result.data}');
  //
  // var model = ChannelApplyListModel.fromJson(result.data);
  // if (model.code != 200) {
  //   toast('code:${model.code}', textColor: Colors.red);
  //   logger.e('举报失败  response code:${model.code} msg:${model.message}');
  //   return false;
  // }
  return true;
}

/// 浏览器请求次数
Future<bool> submitBrowser(String data) async {
  Response result;
  try {
    result = await getConnect().post(await Own.browserApi(),  data : data);
  } catch (e) {
    logger.e(e);
    return false;
  }

  if (result.statusCode != 200) {
    logger.e('浏览器请求次数  request code:${result.statusCode}');
    return false;
  }
  logger.d('浏览器请求次数  request result.data:${result.data}');
  return true;
}

/// 获取时间
Future<int?> getTime() async {
  Response result;
  try {
    result = await getConnect().get(
      await Own.getTimeApi(),
    );
  } catch (e) {
    logger.e(e);
    return 0;
  }

  if (result.statusCode != 200) {
    logger.e('获取时间  request code:${result.statusCode}');
    logger.e('获取时间  request statusText:${result.statusMessage}');
    return -1;
  }
  int ? time = result.data['data'];
  logger.d('获取时间  request result.time:$time');
  return time;
}

/// 获取domain
Future<OwnInfoModelData?> geDomainInfo(String userName) async {
  var url = '${await Own.getOwnApi()}$userName';
  logger.d('geDomainInfo url=$url');

  Response result;
  try {
    result = await getConnect().get(
      url,
    );
  } catch (e) {
    logger.e('geDomainInfo $e');
    return null;
  }
  logger.d('geDomainInfo data=${result.data}');

  if (result.statusCode != 200) {
    logger.e('geDomainInfo  request code:${result.statusCode}');
    return null;
  }

  OwnMySelfModel? model = OwnMySelfModel.fromJson(result.data);

  if (model.code != 200) {
    logger.e('geDomainInfo response code:${model.code}');
    return null;
  }
  return model.data;
}

/// 获取广告
Future<SquareBannerReq?> getAdsInfo() async {
  var url = await Own.getAdsApi();
  logger.i('geDomainInfo url=$url');
  Response result;
  try {
    result = await getConnect().get(
      url,
    );
  } catch (e) {
    logger.e('geDomainInfo $e');
    return null;
  }

  if (result.statusCode != 200) {
    logger.e('geDomainInfo  request code:${result.statusCode}');
    return null;
  }

  SquareBannerReq? data;
  if (result.statusCode == 200) {
    data = SquareBannerReq.fromJson(result.data);
  }
  if (data?.code != 200) {
    logger.e('geDomainInfo response code:${data}');
    return null;
  }
  return data;
}