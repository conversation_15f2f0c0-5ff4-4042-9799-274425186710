import 'dart:convert';
import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:dio/dio.dart';
import 'package:dio/io.dart';
import 'package:flutter_browser/flutter_browser.dart';
import 'package:flutter_metatel/app/data/events/events.dart';
import 'package:flutter_metatel/app/data/models/res/mex_get_user_info_res_model.dart';
import 'package:flutter_metatel/app/data/models/res/node_conf.dart';
import 'package:flutter_metatel/app/data/models/res/oauth2_authorize.model.dart';
import 'package:flutter_metatel/app/data/models/sts_info_model.dart';
import 'package:flutter_metatel/app/data/providers/api/21_plan.dart';
import 'package:flutter_metatel/app/data/providers/api/invite.dart';
import 'package:flutter_metatel/app/data/services/emoji_manage_service.dart';
import 'package:flutter_metatel/app/modules/dao/dao_config.dart';
import 'package:flutter_metatel/app/modules/home/<USER>/mine_controller.dart';
import 'package:flutter_metatel/app/modules/virtualcard/api/api.dart';
import 'package:flutter_metatel/app/modules/virtualcard/model/static_data.dart';
import 'package:flutter_metatel/core/push/push_token_model.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:flutter_metatel/core/utils/proxy_util.dart';
import 'package:flutter_metatel/core/utils/util.dart';
import 'package:flutter_web3/app/core/values/config.dart';
import 'package:get/get.dart' as g;
import 'package:package_info_plus/package_info_plus.dart';

import '../../../../core/utils/device_util.dart';
import '../../../../core/utils/events_bus.dart';
import '../../../../core/values/config.dart';
import '../../models/res/base_res_model.dart';
import '../../models/res/node_info.model.dart';
import '../../models/res/oss_token_res_model.dart';
import '../../models/res/user_model.dart';
import '../../services/config_service.dart';
import '../native/chatio/chatio/chatio_ffi.dart';
import 'base_dio_api.dart';
import 'did.dart';
import 'other_api.dart';
import 'staking_api.dart';

class ApiProvider extends BaseDioClient {
  Dio _createDio() {
    BaseOptions options = BaseOptions();
    options.connectTimeout = const Duration(seconds: 30);
    options.sendTimeout = const Duration(seconds: 30);
    options.contentType = 'application/json; charset=utf-8';
    var token = g.Get.find<AppConfigService>().getToken();
    options.headers = {
      HttpHeaders.authorizationHeader: "Bearer $token",
      HttpHeaders.contentTypeHeader: 'application/json; charset=utf-8',
    };
    var dio = super.createDio(options: options);
    return dio;
  }

  /*
  * 获取awstoken
  *
  * */
  Future<Response<OssTokenResModel>> getOssToken() async {
    AppLogger.d("getOssToken url==${Config.getRequestOssTokenUrl()}");
    var response = await _createDio().post(
      Config.getRequestOssTokenUrl(),
    );
    OssTokenResModel? data;
    if (response.statusCode == 200) {
      data = OssTokenResModel.fromJson(response.data);
    }
    return Response(
      statusCode: response.statusCode,
      statusMessage: response.statusMessage,
      data: data,
      requestOptions: response.requestOptions,
    );
  }

  /// 获取存储信息
  Future<Response<StsInfoModel>> getStsInfo() async {
    var url = '${Config.nodeApiUrl()}/oss/v2/sts';
    try {
      var response = await _createDio().get(url);
      AppLogger.i(
          'getStsInfo  statusCode:${response.statusCode}   body:${response.data}');
      StsInfoModel? data;
      if (response.statusCode == 200) {
        data = StsInfoModel.fromJson(response.data);
      }

      return Response(
        statusCode: response.statusCode,
        statusMessage: response.statusMessage,
        data: data,
        requestOptions: response.requestOptions,
      );
    } catch (e) {
      return Response(
        statusCode: -100,
        statusMessage: '',
        data: null,
        requestOptions: RequestOptions(path: ''),
      );
    }
  }

  /*
  * 通过电话号码查询联系人
  *
  * */
  Future<Response<UserModel>> searchUserByPhone(String phone) async {
    final Map<String, dynamic> body = {"phone_number": phone};
    var response = await _createDio().post(Config.REQUEST_SEARCH, data: body);
    UserModel? data;
    if (response.statusCode == 200) {
      data = UserModel.fromJson(response.data);
    }
    return Response(
      statusCode: response.statusCode,
      statusMessage: response.statusMessage,
      data: data,
      requestOptions: response.requestOptions,
    );
  }

  /*
  * 通过靓号查询联系人
  *
  * */
  Future<Response<UserModel>> searchUserByNftNumber(String nftNumber) async {
    final Map<String, dynamic> body = {
      "name": nftNumber,
      "tag": "靓号",
      "page": 1,
      "length": 1000,
    };
    var response =
        await _createDio().post(Config.getUserByNftNumber, data: body);
    UserModel? data;
    if (response.statusCode == 200) {
      data = UserModel.fromJson(response.data);
    }
    return Response(
      statusCode: response.statusCode,
      statusMessage: response.statusMessage,
      data: data,
      requestOptions: response.requestOptions,
    );
  }

  /*
  * 推送token
  *
  * */

  Future<Response<BaseRes>> putToken(
      Map<String, PushTokenModel> tokenMap) async {
    var name = await g.Get.find<ChatioFFI>().getDeviceName();
    var platform = DeviceUtil.getClientType();
    var tokenType = await DeviceUtil.getTokenType();
    var packageInfo = await PackageInfo.fromPlatform();
    var pack = packageInfo.packageName;
    var msgToken = tokenMap["msg"];
    var callToken = tokenMap["call"] ?? tokenMap["msg"];
    final Map<String, dynamic> body = {
      // "token": token,
      "msg_token":
          msgToken == null ? "" : '${msgToken.pushPlatform}:${msgToken.token}',
      "call_token": callToken == null
          ? ""
          : '${callToken.pushPlatform}:${callToken.token}',
      "deviceUsername": name,
      "platform": platform,
      "type": tokenType,
      "serve_mark": '',
      "ringtone": '',
      "version": '',
      "apk_version": '100',
      "language": currentLanguageIsSimpleChinese() ? 'zh' : 'en',
      "pack": pack,
    };
    AppLogger.d("_push body==$body");

    var response =
        await _createDio().post(Config.getRequestPushTokenUrl(), data: body);
    BaseRes? data;
    if (response.statusCode == 200) {
      data = BaseRes.fromJson(response.data);
    }
    return Response(
      statusCode: response.statusCode,
      statusMessage: response.statusMessage,
      data: data,
      requestOptions: response.requestOptions,
    );
  }

  Future<Response<MexGetUserInfoModel>> getMexUserInfo(String userName,
      {String? phoneNumber}) async {
    final Map<String, dynamic> body = {
      "ioi_user_name": userName,
      "mobile": phoneNumber ?? "",
    };
    var response = await _createDio()
        .post("${Config.getMexUrl()}${Config.GET_USER_INFO_MEX}", data: body);
    // await post("http://**************:8080/server/client/get_user_info", body);

    AppLogger.d("response getMexUserInfo ${response.data}");
    AppLogger.d("response getMexUserInfo ${response.statusCode}");
    MexGetUserInfoModel? data;
    if (response.statusCode == 200) {
      if (response.data.runtimeType != String) {
        data = MexGetUserInfoModel.fromJson(response.data);
      } else {
        var decode = json.decode(
          response.data,
        );
        data = MexGetUserInfoModel.fromJson(decode);
      }
    }
    return Response(
      statusCode: response.statusCode,
      statusMessage: response.statusMessage,
      data: data,
      requestOptions: response.requestOptions,
    );
  }

  ///密宇宙 授权
  Future<Response<Oauth2AuthorizeModel>> getAuth2Authorize(
      String clientID, String scope,
      {String? state}) async {
    final Map<String, dynamic> query = {
      "response_type": "code",
      "client_id": clientID,
      "scope": scope,
      "state": state ?? "",
    };
    var response = await _createDio().get(
      "${Config.nodeApiUrl()}${Config.OAUTH2_AUTHORIZE}",
      queryParameters: query,
    );
    AppLogger.d("_onRefresh response${response.data}");
    AppLogger.d("_onRefresh response${response.statusCode}");
    Oauth2AuthorizeModel? data;
    if (response.statusCode == 200) {
      if (response.data.runtimeType != String) {
        data = Oauth2AuthorizeModel.fromJson(response.data);
      } else {
        var decode = json.decode(
          response.data,
        );
        data = Oauth2AuthorizeModel.fromJson(decode);
      }
    } else {
      toast(response.data["message"] ?? "Unauthorized");
    }
    return Response(
      statusCode: response.statusCode,
      statusMessage: response.statusMessage,
      data: data,
      requestOptions: response.requestOptions,
    );
  }

  bool isRequestConf = false;

  ///获取服务器相关的配置

  Future<Response<NodeConf>> getNodeConf({bool all = true}) async {
    var conf = g.Get.find<AppConfigService>();
    if (conf.getToken().isEmpty) {
      AppLogger.w("getNodeConf token is Empty!!!");
      return Response(
        statusCode: null,
        statusMessage: null,
        data: null,
        requestOptions: RequestOptions(path: ''),
      );
    }
    if (isRequestConf) {
      //重复请求
      if (!all) {
        if (Config.ownUrl.isEmpty) {
          //如果配置为空说明还没有拿到配置，就等待
          await Future.delayed(const Duration(seconds: 2));
        }
        if (Config.ownUrl.isNotEmpty) {
          //等待两秒钟过后，该参数不为空，说明已经拿到配置了，就可以直接返回保存的参数，也是更新了的参数
          String nodeConfigJsonStr =
              g.Get.find<AppConfigService>().getNodeConfigJsonStr() ?? "";
          if (nodeConfigJsonStr.isNotEmpty) {
            var nodeConfJson = json.decode(
              nodeConfigJsonStr,
            );
            if (nodeConfJson != null) {
              return Response(
                  statusCode: 200,
                  statusMessage: '',
                  data: _nodeConfSet(nodeConfJson: nodeConfJson),
                  requestOptions: RequestOptions(path: ''));
            }
          }
        }
      }
    }
    isRequestConf = true;
    int? versionCode = await DeviceUtil.getVersionCode();
    var platform = Platform.operatingSystem;
    AppLogger.d("nodeApiUrl ");
    try {
      var url = "${Config.nodeApiUrl()}/ota/conf/$platform/$versionCode";
      AppLogger.d("nodeApiUrl url=$url");
      var response = await _createDio().get(url);
      AppLogger.d("nodeApiUrl response.bodyString=${response.data}");
      NodeConf? data;
      Map<String, dynamic>? nodeConfJson;
      var d = response.data;
      if (response.statusCode == 200 && d.toString().isNotEmpty) {
        g.Get.find<AppConfigService>().saveNodeConfig(json.encode(response.data));
        if (response.data.runtimeType != String) {
          nodeConfJson = response.data;
        } else {
          nodeConfJson = json.decode(
            response.data,
          );
        }
      } else {
        if (response.statusCode != 401) {
          AppLogger.d(
              "response.statusCode != 401 response.bodyString=${response.data}");
        }
        String nodeConfigJsonStr =
            g.Get.find<AppConfigService>().getNodeConfigJsonStr() ?? "";
        if (nodeConfigJsonStr.isNotEmpty) {
          nodeConfJson = json.decode(
            nodeConfigJsonStr,
          );
        }
      }
      if (nodeConfJson != null) {
        data = _nodeConfSet(nodeConfJson: nodeConfJson);
        AppLogger.d('getNodeConf data=$data');

        // 检查没下载的emoji
        EmojiManageService service = g.Get.find();
        service.checkAllEmoji();
      }
      isRequestConf = false;
      return Response(
        statusCode: response.statusCode,
        statusMessage: response.statusMessage,
        data: data,
        requestOptions: response.requestOptions,
      );
    } catch (e) {
      AppLogger.d('getNodeConf error=$e');
      return Response(
        statusCode: null,
        statusMessage: null,
        data: null,
        requestOptions: RequestOptions(path: ''),
      );
    }
  }

  Future<bool> testNode() async {
    int? versionCode = await DeviceUtil.getVersionCode();
    var platform = Platform.operatingSystem;
    try {
      BaseOptions options = BaseOptions();
      options.connectTimeout = const Duration(seconds: 15);
      options.sendTimeout = const Duration(seconds: 15);
      options.contentType = 'application/json; charset=utf-8';
      var token = g.Get.find<AppConfigService>().getToken();
      options.headers = {
        HttpHeaders.authorizationHeader: "Bearer $token",
        // HttpHeaders.authorizationHeader: token,
        HttpHeaders.contentTypeHeader: 'application/json; charset=utf-8',
      };
      var dio = Dio(options);
      dio.httpClientAdapter = IOHttpClientAdapter(
        createHttpClient: () {
          SecurityContext sc = SecurityContext();
          sc.setTrustedCertificatesBytes(Config.certData!.codeUnits);
          final client = HttpClient(context: sc);
          client.findProxy =  null;
          client.badCertificateCallback =
              (X509Certificate cert, String host, int port) {
            return true;
          };
          return client;
        },
      );
      var url = "${Config.testNodeApiUrl()}/ota/conf/$platform/$versionCode";
      AppLogger.d("testNode url=$url");

      var response = await dio.get(
        url,
      );
      AppLogger.d("testNode response.statusCode=${response.statusCode}");

      if (response.statusCode == 200) {
        return true;
      }
    } catch (e) {
      if (e.toString().contains(Config.proxyError)) {}
      AppLogger.e("testNode error =${e.toString()}");

    }
    return false;
  }

  NodeConf _nodeConfSet({required Map<String, dynamic> nodeConfJson}) {
    var data = NodeConf.fromJson(nodeConfJson);
    Config.connectUrl = (data.data?.base?.e2e) ?? "";
    Config.meetingUrl = data.data?.base?.meetingUrl ?? '';
    Plan21Api.planApiUrl = data.data?.base?.plan21Url ?? '';
    OtherApi.dolphinApiUrl = data.data?.base?.dolphinApiUrl ?? '';

    Config.channelUrl = (data.data?.base?.channel) ?? "";
    Config.ownUrl = (data.data?.base?.own) ?? "";
    Config.mexUrl = (data.data?.base?.mex) ?? "";
    DidApi.apiUrl = (data.data?.base?.hostingUrl) ?? "";
    Config.hongBaoUrl = (data.data?.base?.hongBaoUrl) ?? "";
    Config.maxNumberCount = (data.data?.base?.maxNumberCount) ?? 1000;
    Config.exchangeArray = (data.data?.base?.exchangeArray) ?? [3000, 100000];
    Config.redeemable = (data.data?.base?.redeemable) ?? false;
    Config.verifyHuman = (data.data?.base?.verifyHuman) ?? false;
    bool? isBrowser = data.data?.homeConf?.isBrowser;
    if (data.data?.base?.fullScreenList?.isNotEmpty ?? false) {
      BrowserConfig.fullScreenList = data.data?.base?.fullScreenList ?? [];
    }
    StakingApi.stakingApiUrl.value = data.data?.base?.stakingUrl ?? '';
    WalletConfig.threeRc20TokenUrl = (data.data?.base?.threeRc20TokenUrl) ?? "";
    WalletConfig.cstCardsTokenAddress =
        (data.data?.base?.cstNftCardsAddress) ?? "";
    VirtualCardStaticData.visible =
        data.data?.virtualCardConf?.visible ?? false;
    BrowserWidget.browserConf = data.data?.browserConf;
    BrowserWidget.count.value ++;

    if (DeviceUtil.isIOS()) {
      WalletConfig.iosShopping.value = !(isBrowser ?? true);
    }
    WalletConfig.quickRedemptionUrl.value = (data.data?.base?.quickRedemptionUrl) ?? "";
    WalletConfig.netWorkBaseUrl.value = (data.data?.base?.netWorkUrl) ?? "";
    WalletConfig.rxWalletClicked.value ++;

    if (isBrowser != null && Config.isBrowser != isBrowser) {
      Config.isBrowser = isBrowser;
      g.Get.find<AppConfigService>().setShowBrowser(isBrowser);
      g.Get.find<EventBus>().fire(HomePageUpdate());
    }
    try {
      g.Get.find<MineController>().virtualCardVisible.value =
          VirtualCardStaticData.visible;
    } catch (e) {
      // AppLogger.e("$e");
    }
    VirtualCardApi.base = data.data?.virtualCardConf?.apiUrl ?? "";
    VirtualCardStaticData.rechargeDappUrl =
        data.data?.virtualCardConf?.rechargeDappUrl;
    InviteApi.apiUrl =
        (data.data?.inviteConf?.inviteUrl) ?? data.data?.base?.inviteUrl ?? "";

    ///测试
    // InviteApi.apiUrl = 'http://**************:8703';

    InviteApi.posterUrl = (data.data?.inviteConf?.invitePosterUrl) ??
        (data.data?.base?.invitePosterUrl) ??
        "";
    InviteApi.miningUrl = data.data?.inviteConf?.miningUrl ?? '';
    InviteApi.miningWithdrawAddressModify =
        data.data?.inviteConf?.miningWithdrawAddressModify ?? false;
    // InviteApi.miningUrl = 'http://**************:3082';
    Config.requestPushTokenUrl = (data.data?.base?.push) ?? "";

    Config.sipUrl = (data.data?.base?.sip?.wss) ?? "";
    Config.sipDomain = (data.data?.base?.sip?.domain) ?? "";
    Config.daoViewConf = data.data?.daoViewConf ??
        DaoViewConf(
            create: true,
            pubDynamic: true,
            pubWork: false,
            createCenter: false,
            pointsManagement: false,
            myAccount: false,
            pledge: false,
            myOrder: false);
    Config.inviteCodeMust = data.data?.inviteConf?.inviteCodeMust ?? false;
    // Config.daoViewConf =
    //     DaoViewConf(
    //         create: true,
    //         pubDynamic: true,
    //         pubWork: true,
    //         createCenter: true,
    //         pointsManagement: true,
    //         myAccount: true,
    //         pledge: true,
    //         myOrder: true);
    var stareUrl = data.data?.base?.stareUrl;
    if (stareUrl != null && stareUrl.isNotEmpty) {
      Config.webViewStare = stareUrl;
    }
    var daoConf = data.data?.daoConf;
    if (daoConf != null) {
      var daoAndroidUrl = daoConf.daoAndroidUrl;
      var daoBaseUrl = daoConf.daoBaseUrl;
      var daoIosUrl = daoConf.daoIosUrl;
      var daoBanner = daoConf.banner;
      if (daoAndroidUrl != null && daoAndroidUrl.isNotEmpty) {
        DaoConfig.daoAndroidUrl = daoAndroidUrl;
      }
      if (daoBaseUrl != null && daoBaseUrl.isNotEmpty) {
        DaoConfig.daoBaseUrl = daoBaseUrl;
      }
      if (daoIosUrl != null && daoIosUrl.isNotEmpty) {
        DaoConfig.daoIosUrl = daoIosUrl;
      }
      if (daoBanner != null && daoBanner.isNotEmpty) {
        DaoConfig.bannerList = daoBanner;
      }
    }
    S3? s3 = (data.data?.s3);
    Turn? turn = (data.data?.turn);
    AppConfigService configS = g.Get.find<AppConfigService>();
    if (s3 != null) {
      Config.requestOssTokenUrl = s3.sts ?? "";
      configS.saveAwsInfo(s3.toJson());
    }
    if (turn != null) {
      configS.saveTurn(turn.toJson());
      configS.saveIceTransportPolicy(turn.iceTransportPolicy);
    }
    return data;
  }
}

Future<String> getUserAgent() async {
  String result = "";
  var info = DeviceInfoPlugin();
  if (Platform.isAndroid) {
    var build = await info.androidInfo;
    String osIncremental = (build.version.incremental).isNotEmpty
        ? "${build.version.incremental} "
        : "";
    String osVersion =
        (build.version.release).isNotEmpty ? "${build.version.release} " : "";

    String brand = (build.brand).isNotEmpty ? "${build.brand} " : "";
    String manufacturer =
        (build.manufacturer).isNotEmpty ? "${build.manufacturer} " : "";
    String model = (build.model).isNotEmpty ? "${build.model} " : "";
    result = result +
        // "Platform-Android " +
        // osBaseOs +
        // osCodeName +
        osIncremental +
        osVersion +
        brand +
        manufacturer +
        model;
  } else if (Platform.isIOS) {
    var build = await info.iosInfo;
    String systemVersion =
        (build.systemVersion).isNotEmpty ? "${build.systemVersion} " : "";
    String iosUtsNameNodeName =
        (build.utsname.sysname).isNotEmpty ? "${build.utsname.nodename} " : "";
    String iosUtsNameMachine =
        (build.utsname.sysname).isNotEmpty ? "${build.utsname.machine} " : "";

    result = result +
        // "Platform-apple " +
        // name +
        // systemName +
        systemVersion +
        // model +
        // localizedModel +
        // identifierForVendor +
        // isPhysicalDevice +
        // iosUtsNameSysName +
        iosUtsNameNodeName +
        // iosUtsNameRelease +
        // iosUtsNameVersion +
        iosUtsNameMachine;
  }
  var packageInfo = await PackageInfo.fromPlatform();
  //APP名称
  // String appName = packageInfo.appName;
  //包名
  String packageName = packageInfo.packageName;
  //版本名
  String version = packageInfo.version;
  //版本号
  String buildNumber = packageInfo.buildNumber;
  String appVersion = "$version-$buildNumber";
  // int appVersionCode = int.parse(buildNumber);
  result = "$result($appVersion $packageName)";
  // result=result.replaceAll(":", "-");
  result = chineseUrlEncode(result);
  AppLogger.d("userAgentAAAAAA==$result");

  return result;
}
