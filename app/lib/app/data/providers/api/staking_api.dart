import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:flutter_metatel/app/data/services/config_service.dart';
import 'package:get/get.dart' as g;

import '../../../../core/utils/app_log.dart';
import '../../../../core/values/config.dart';
import '../../../modules/mining/mining_pwd_util.dart';
import '../../models/res/base_res_model.dart';
import '../../models/res/domain_info_res.dart';
import '../../models/res/keybox_info_res.dart';
import '../../models/res/staking_create_validator_state.dart';
import '../../models/res/staking_list_model_res.dart';
import '../../models/res/staking_node_info_res.dart';
import '../../models/res/staking_service_list_res.dart';
import 'base_dio_api.dart';

class StakingApi extends BaseDioClient {
  static g.RxString stakingApiUrl = "".obs;
  static const rand = '0xdc03364e82a210c1dadafb610dd2f1391393d03527ab59e5af8bd1efd9f23c6d';

  static const stakingAdd = '/api/v1/app/staking/add'; //提交质押
  static const stakingApplyList = '/api/v1/app/staking/applys'; //节点质押申请列表
  static const stakingConf = '/api/v1/conf/node'; //节点配置信息
  static const keyBoxUpdate = '/api/v1/app/keybox-status/update'; //更新keybox激活状态
  static const stakingServiceAdd = '/api/v1/app/account/service/add'; //提交数据
  static const stakingServiceList = '/api/v1/app/account/service/list'; //获取通讯账户关联Service列表
  static const domainInfo = '/api/v1/app/domain/info?domain='; //获取节点域名信息
  static const stakingBlockAdd = '/api/v1/app/staking/block-add'; //区块节点质押申请


  static const test = '0';

  static const keyboxInfo = '/api/keybox/getinfo'; //keybox信息
  static const keyboxKeyUpdload = '/api/keybox/admin/upload'; //keybox密钥
  static const keyboxActive = '/api/keybox/admin/active'; //激活keybox
  static const keyboxResetPin = '/api/keybox/admin/reset'; //修改pin
  static const keyBoxGetCreateValidatorState = '/api/keybox/getcreateValidatorstate'; //修改pin


  static String getStakingApiUrl() {
    // var url = 'https://staking-api.zoomlion2048.store';
    var url = stakingApiUrl.value;
    return Config.handleUrl(url);
  }


  /// 提交质押
  Future<Response<BaseRes?>> getStakingAddResult(
      Map<String, dynamic> map) async {
    var url = getStakingApiUrl();
    if (url.isEmpty) {
      AppLogger.e('getStakingAddResult url is null');
      return Response(
          statusCode: -200,
          statusMessage: '',
          data: null,
          requestOptions: RequestOptions(path: ''));
    }
    url = url + stakingAdd;
    var user = g.Get.find<AppConfigService>().getUserNameWithoutDomain();
    String data =
        await MiningPwdUtil.encryptForData(json.encode(map), rand: rand) ?? '';
    Map<String, String> bodyMap = {'account': user, 'info': data};
    try {
      var response = await createDio().post(url, data: bodyMap);
      BaseRes? body;
      if (response.statusCode == 200) {
        body = BaseRes.fromJson(response.data);
      }
      AppLogger.d('getStakingAddResult body str=${response.data}');
      return Response(
        statusCode: response.statusCode,
        statusMessage: response.statusMessage,
        data: body,
        requestOptions: response.requestOptions,
      );
    } catch (e) {
      AppLogger.e('getStakingAddResult $e');
      return Response(
          statusCode: -200,
          statusMessage: '$e',
          data: null,
          requestOptions: RequestOptions(path: ''));
    }
  }

  /// 节点质押申请列表
  Future<Response<StakingListModelRes>> getStakingApplyList(
      Map<String, dynamic> map) async {
    var url = getStakingApiUrl();
    if (url.isEmpty) {
      AppLogger.e('getStakingApplyList url is null');
      return Response(
          statusCode: -200,
          statusMessage: '',
          data: null,
          requestOptions: RequestOptions(path: ''));
    }
    url = url + stakingApplyList;
    AppLogger.d('getStakingApplyList url=$url');
    var user = g.Get.find<AppConfigService>().getUserNameWithoutDomain();
    String data =
        await MiningPwdUtil.encryptForData(json.encode(map), rand: rand) ?? '';
    Map<String, String> bodyMap = {'account': user, 'info': data};
    AppLogger.d('getStakingApplyList query=$map');
    try {
      var response = await createDio().post(url, data: bodyMap);
      StakingListModelRes? body;
      if (response.statusCode == 200) {
        body = StakingListModelRes.fromJson(response.data);
      }
      AppLogger.d('getStakingApplyList body str=${response.data}');
      return Response(
        statusCode: response.statusCode,
        statusMessage: response.statusMessage,
        data: body,
        requestOptions: response.requestOptions,
      );
    } catch (e) {
      AppLogger.e('getStakingApplyList $e');
      return Response(
          statusCode: -200,
          statusMessage: '$e',
          data: null,
          requestOptions: RequestOptions(path: ''));
    }
  }

  /// 更新keyBox激活状态
  Future<Response<StakingListModelRes>> updateKeyBoxStatus(
      Map<String, dynamic> map) async {
    var url = getStakingApiUrl();
    if (url.isEmpty) {
      AppLogger.e('updateKeyBoxStatus url is null');
      return Response(
          statusCode: -200,
          statusMessage: '',
          data: null,
          requestOptions: RequestOptions(path: ''));
    }
    url = url + keyBoxUpdate;
    AppLogger.d('updateKeyBoxStatus url=$url');
    var user = g.Get.find<AppConfigService>().getUserNameWithoutDomain();
    String data =
        await MiningPwdUtil.encryptForData(json.encode(map), rand: rand) ?? '';
    Map<String, String> bodyMap = {'account': user, 'info': data};
    AppLogger.d('updateKeyBoxStatus query=$map');
    try {
      var response = await createDio().post(url, data: bodyMap);
      StakingListModelRes? body;
      if (response.statusCode == 200) {
        body = StakingListModelRes.fromJson(response.data);
      }
      AppLogger.d('updateKeyBoxStatus body str=${response.data}');
      return Response(
        statusCode: response.statusCode,
        statusMessage: response.statusMessage,
        data: body,
        requestOptions: response.requestOptions,
      );
    } catch (e) {
      AppLogger.e('getStakingApplyList $e');
      return Response(
          statusCode: -200,
          statusMessage: '$e',
          data: null,
          requestOptions: RequestOptions(path: ''));
    }
  }

  /// 提交钱包域名地址到服务器
  Future<Response<StakingListModelRes>> addWalletInfo(
      Map<String, dynamic> map) async {
    var url = getStakingApiUrl();
    if (url.isEmpty) {
      AppLogger.e('addWalletInfo url is null');
      return Response(
          statusCode: -200,
          statusMessage: '',
          data: null,
          requestOptions: RequestOptions(path: ''));
    }
    url = url + stakingServiceAdd;
    AppLogger.d('addWalletInfo url=$url');
    var user = g.Get.find<AppConfigService>().getUserNameWithoutDomain();
    String data =
        await MiningPwdUtil.encryptForData(json.encode(map), rand: rand) ?? '';
    Map<String, String> bodyMap = {'account': user, 'info': data};
    AppLogger.d('addWalletInfo query=$map');
    try {
      var response = await createDio().post(url, data: bodyMap);
      StakingListModelRes? body;
      if (response.statusCode == 200) {
        body = StakingListModelRes.fromJson(response.data);
      }
      AppLogger.d('addWalletInfo body str=${response.data}');
      return Response(
        statusCode: response.statusCode,
        statusMessage: response.statusMessage,
        data: body,
        requestOptions: response.requestOptions,
      );
    } catch (e) {
      AppLogger.e('addWalletInfo $e');
      return Response(
          statusCode: -200,
          statusMessage: '$e',
          data: null,
          requestOptions: RequestOptions(path: ''));
    }
  }

  //
  Future<Response<StakingServiceListRes>> getServiceList() async {
    var url = getStakingApiUrl();
    if (url.isEmpty) {
      AppLogger.e('getServiceList url is null');
      return Response(
          statusCode: -200,
          statusMessage: '',
          data: null,
          requestOptions: RequestOptions(path: ''));
    }
    var user = g.Get.find<AppConfigService>().getUserNameWithoutDomain();

    Map<String, dynamic> map = {'account':user};

    url = url + stakingServiceList;
    String data =
        await MiningPwdUtil.encryptForData(json.encode(map), rand: rand) ?? '';
    Map<String, String> bodyMap = {'account': user, 'info': data};

    try {
      var response = await createDio().post(url, data: bodyMap);
      StakingServiceListRes? body;
      if (response.statusCode == 200) {
        body = StakingServiceListRes.fromJson(response.data);
      }
      AppLogger.d('getServiceList body str=${response.data}');
      return Response(
        statusCode: response.statusCode,
        statusMessage: response.statusMessage,
        data: body,
        requestOptions: response.requestOptions,
      );
    } catch (e) {
      AppLogger.e('getServiceList $e');
      return Response(
          statusCode: -200,
          statusMessage: '$e',
          data: null,
          requestOptions: RequestOptions(path: ''));
    }
  }

  //域名信息
  Future<Response<DomainInfoRes>> getDomainInfo(String domain) async {
    var url = getStakingApiUrl();
    if (url.isEmpty) {
      AppLogger.e('getDomainInfo url is null');
      return Response(
          statusCode: -200,
          statusMessage: '',
          data: null,
          requestOptions: RequestOptions(path: ''));
    }
    url = url + domainInfo + domain;
    AppLogger.d('getDomainInfo url=$url');
    try {
      var response = await createDio().get(url);
      DomainInfoRes? body;
      if (response.statusCode == 200) {
        body = DomainInfoRes.fromJson(response.data);
      }
      AppLogger.d('getDomainInfo body str=${response.data}');
      return Response(
        statusCode: response.statusCode,
        statusMessage: response.statusMessage,
        data: body,
        requestOptions: response.requestOptions,
      );
    } catch (e) {
      AppLogger.e('getDomainInfo $e');
      return Response(
          statusCode: -200,
          statusMessage: '$e',
          data: null,
          requestOptions: RequestOptions(path: ''));
    }
  }

  /// 节点配置信息
  Future<Response<StakingNodeInfoRes>> getStakingConf() async {
    var url = getStakingApiUrl();
    if (url.isEmpty) {
      AppLogger.e('getStakingConf url is null');
      return Response(
          statusCode: -200,
          statusMessage: '',
          data: null,
          requestOptions: RequestOptions(path: ''));
    }
    url = url + stakingConf;
    AppLogger.d('getStakingConf url=$url');
    try {
      var response = await createDio().get(url);
      StakingNodeInfoRes? body;
      if (response.statusCode == 200) {
        body = StakingNodeInfoRes.fromJson(response.data);
      }
      AppLogger.d('getStakingConf body str=${response.data}');
      return Response(
        statusCode: response.statusCode,
        statusMessage: response.statusMessage,
        data: body,
        requestOptions: response.requestOptions,
      );
    } catch (e) {
      AppLogger.e('getStakingConf $e');
      return Response(
          statusCode: -200,
          statusMessage: '$e',
          data: null,
          requestOptions: RequestOptions(path: ''));
    }
  }

  ///////////keybox相关接口
  /// 获取keybox信息
  Future<Response<KeyBoxInfoRes>> getKeyBoxInfo(String domain,) async {
    var url = getKeyBoxApiUrl(domain);
    if (url.isEmpty) {
      AppLogger.e('getKeyBoxInfo url is null');
      return Response(
          statusCode: -200,
          statusMessage: '',
          data: null,
          requestOptions: RequestOptions(path: ''));
    }
    url = url + keyboxInfo;
    AppLogger.d('getKeyBoxInfo url= ${url}');

    try {
      var response = await createDio().get(
        url,
      );
      KeyBoxInfoRes? body;
      if (response.statusCode == 200) {
        body = KeyBoxInfoRes.fromJson(response.data);
      }
      AppLogger.d('getKeyBoxInfo body str=${response.data}');
      return Response(
        statusCode: response.statusCode,
        statusMessage: response.statusMessage,
        data: body,
        requestOptions: response.requestOptions,
      );
    } catch (e) {
      AppLogger.e('getKeyBoxInfo $e');
      return Response(
          statusCode: -200,
          statusMessage: '$e',
          data: null,
          requestOptions: RequestOptions(path: ''));
    }
  }

  static String getKeyBoxApiUrl(String domain,) {
    return 'https://$domain:8443';
  }

  ///提交Pin
  Future<Response<KeyBoxInfoRes>> submitUpdate(String domain,
      Map<String, dynamic> map) async {
    var url = getKeyBoxApiUrl(domain);
    if (url.isEmpty) {
      AppLogger.e('submitUpdate url is null');
      return Response(
          statusCode: -200,
          statusMessage: '',
          data: null,
          requestOptions: RequestOptions(path: ''));
    }
    AppLogger.d('submitUpdate map=$map');
    url = url + keyboxKeyUpdload;
    AppLogger.d('submitUpdate url=$url');
    var user = g.Get.find<AppConfigService>().getUserNameWithoutDomain();
    String data =
        await MiningPwdUtil.encryptForData(json.encode(map), rand: rand) ?? '';
    Map<String, String> bodyMap = {'account': user, 'info': data};
    AppLogger.d('submitUpdate bodyMap=$bodyMap');
    try {
      var response = await createDio().post(
        url,data: bodyMap
      );
      KeyBoxInfoRes? body;
      if (response.statusCode == 200) {
        body = KeyBoxInfoRes.fromJson(response.data);
      }
      AppLogger.d('submitUpdate body str=${response.data}');
      return Response(
        statusCode: response.statusCode,
        statusMessage: response.statusMessage,
        data: body,
        requestOptions: response.requestOptions,
      );
    } catch (e) {
      AppLogger.e('getKeyBoxInfo $e');
      return Response(
          statusCode: -200,
          statusMessage: '$e',
          data: null,
          requestOptions: RequestOptions(path: ''));
    }
  }

  /// 激活keybox
  Future<Response<BaseRes>> keyBoxActive(String domain,
      Map<String, dynamic> map) async {
    var url = getKeyBoxApiUrl(domain);
    if (url.isEmpty) {
      AppLogger.e('keyBoxActive url is null');
      return Response(
          statusCode: -200,
          statusMessage: '',
          data: null,
          requestOptions: RequestOptions(path: ''));
    }
    url = url + keyboxActive;
    var user = g.Get.find<AppConfigService>().getUserNameWithoutDomain();
    String data =
        await MiningPwdUtil.encryptForData(json.encode(map), rand: rand) ?? '';
    Map<String, String> bodyMap = {'account': user, 'info': data};
    AppLogger.d('keyBoxActive query=$map');
    try {
      var response = await createDio().post(
          url,data: bodyMap
      );
      BaseRes? body;
      if (response.statusCode == 200) {
        body = BaseRes.fromJson(response.data);
      }
      AppLogger.d('keyBoxActive body str=${response.data}');
      return Response(
        statusCode: response.statusCode,
        statusMessage: response.statusMessage,
        data: body,
        requestOptions: response.requestOptions,
      );
    } catch (e) {
      AppLogger.e('keyBoxActive $e');
      return Response(
          statusCode: -200,
          statusMessage: '$e',
          data: null,
          requestOptions: RequestOptions(path: ''));
    }
  }
  /// 设置Pin
  Future<Response<BaseRes?>> keyBoxResetPin(String domain,
      Map<String, dynamic> map) async {
    var url = getKeyBoxApiUrl(domain);
    if (url.isEmpty) {
      AppLogger.e('keyBoxResetPin url is null');
      return Response(
          statusCode: -200,
          statusMessage: '',
          data: null,
          requestOptions: RequestOptions(path: ''));
    }
    url = url + keyboxResetPin;
    var user = g.Get.find<AppConfigService>().getUserNameWithoutDomain();
    String data =
        await MiningPwdUtil.encryptForData(json.encode(map), rand: rand) ?? '';
    Map<String, String> bodyMap = {'account': user, 'info': data};
    AppLogger.d('keyBoxResetPin bodyMap=$bodyMap');
    try {
      var response = await createDio().post(
          url,data: bodyMap
      );
      BaseRes? body;
      if (response.statusCode == 200) {
        body = BaseRes.fromJson(response.data);
      }
      AppLogger.d('keyBoxResetPin body str=${response.data}');
      return Response(
        statusCode: response.statusCode,
        statusMessage: response.statusMessage,
        data: body,
        requestOptions: response.requestOptions,
      );
    } catch (e) {
      AppLogger.e('keyBoxResetPin $e');
      return Response(
          statusCode: -200,
          statusMessage: '$e',
          data: null,
          requestOptions: RequestOptions(path: ''));
    }
  }

  /// 获取状态
  Future<Response<CreateValidatorStateRes?>> getCreateValidatorState(String domain) async {
    var url = getKeyBoxApiUrl(domain);
    if (url.isEmpty) {
      AppLogger.e('getCreateValidatorState url is null');
      return Response(
          statusCode: -200,
          statusMessage: '',
          data: null,
          requestOptions: RequestOptions(path: ''));
    }
    url = url + keyBoxGetCreateValidatorState;
    AppLogger.d('getCreateValidatorState url=$url');
    try {
      var response = await createDio().get(url);
      CreateValidatorStateRes? body;
      if (response.statusCode == 200) {
        body = CreateValidatorStateRes.fromJson(response.data);
      }
      AppLogger.d('getCreateValidatorState body str=${response.data}');
      return Response(
        statusCode: response.statusCode,
        statusMessage: response.statusMessage,
        data: body,
        requestOptions: response.requestOptions,
      );
    } catch (e) {
      AppLogger.e('getCreateValidatorState $e');
      return Response(
          statusCode: -200,
          statusMessage: '$e',
          data: null,
          requestOptions: RequestOptions(path: ''));
    }
  }


}
