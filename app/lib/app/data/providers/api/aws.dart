import 'dart:convert';
import 'dart:io';

import 'package:flutter_metatel/app/data/models/res/aws_info_model.dart';
import 'package:flutter_metatel/app/data/models/res/oss_token_res_model.dart';
import 'package:flutter_metatel/app/data/services/config_service.dart';
import 'package:flutter_metatel/core/task/time_task.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:flutter_metatel/core/values/config.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:minio/io.dart';
import 'package:minio/minio.dart';

/// 不在使用，使用[OssProvider]
class AwsProvider {
  AwsProvider() {
    _createMinio();
  }

  AwsProvider.test() {
    // _test();
  }

  AwsInfoData? _awsInfo;
  OssTokenData? _ossInfo;
  Minio? _minio;

  String getTemBucket() {
    var timeStr = DateFormat("yyyy-MM-dd").format(DateTime.now());
    var bucket = '${Config.AWS_FILE_STORAGE_DIR}/${Config.node()}/$timeStr';
    AppLogger.d('getTemBucket =$bucket');
    return bucket;
  }

  Future<String> upload(String objectName, String filePath) async {
    if (_minio == null) {
      return '';
    }

    try {
      String bucket = _awsInfo!.bucket ?? '';
      String object = '${getTemBucket()}/$objectName';

      await _minio!.fPutObject(bucket, object, filePath);

      String prefix = _minio!.useSSL ? 'https' : 'http';
      String url =
          '$prefix://${_minio!.endPoint}:${_minio!.port}/$bucket/$object';
      AppLogger.d('upload url:$url');
      return url;
    } catch (e, s) {
      AppLogger.e('Exception details:\n$e');
      AppLogger.e('Stack trace:\n$s');
      return '';
    }
  }

  // Future<bool> download(String objectName, String filePath) async {
  //   if (_minio == null) {
  //     return false;
  //   }
  //
  //   try {
  //     String bucket = _awsInfo!.bucket ?? '';
  //     String object = '${Config.AWS_FILE_STORAGE_DIR}/$objectName';
  //
  //     var byteStream = await _minio!.getObject(bucket, object);
  //     var listData = await byteStream.toList();
  //     byteStream.listen((value) {});
  //
  //     var file = File(filePath);
  //     for (var data in listData) {
  //       file.writeAsBytesSync(data, mode: FileMode.writeOnlyAppend);
  //     }
  //     return true;
  //   } catch (e, s) {
  //     AppLogger.e('Exception details:\n$e');
  //     AppLogger.e('Stack trace:\n$s');
  //     return false;
  //   }
  // }

  void _createMinio() {
    var confs = Get.find<AppConfigService>();
    var awsJson = confs.getAwsInfo();
    var ossJson = confs.getOssInfo();
    if (awsJson == null || ossJson == null) {
      return;
    }
    AppLogger.d('awsJson =${awsJson.toString()}');
    AppLogger.d('awsJson ossJson=${ossJson.toString()}');

    _awsInfo = AwsInfoData.fromJson(awsJson);
    _ossInfo = OssTokenData.fromJson(ossJson);

    String endpoint = _awsInfo!.endpoint ?? '';
    String ip = '';
    int port = 0;

    if (endpoint.isNotEmpty) {
      var splitStr = endpoint.split(":");
      ip = splitStr[0];

      if (splitStr.length > 1) {
        port = int.parse(splitStr[1]);
      }
    }

    _minio = Minio(
      endPoint: ip,
      accessKey: _ossInfo!.accessKeyID ?? '',
      secretKey: _ossInfo!.secretAccessKey ?? '',
      port: port,
      useSSL: _awsInfo!.ssl ?? false,
      sessionToken: _ossInfo!.sessionToken ?? '',
      region: _awsInfo!.region ?? '',
      dataTimeCallback: (d) {
        return TimeTask.instance.getNowDateTime();
      },
      enableTrace: true,
    );
  }


}
