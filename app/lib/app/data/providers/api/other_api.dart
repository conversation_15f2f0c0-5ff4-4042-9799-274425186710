import 'dart:convert';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:flutter_metatel/app/data/models/res/base_res_model_data.dart';
import 'package:flutter_metatel/app/data/providers/api/base_dio_api.dart';
import 'package:get/get.dart';

import '../../../../core/utils/app_log.dart';
import '../../../modules/mining/mining_pwd_util.dart';
import '../../services/config_service.dart';

class OtherApi extends BaseDioClient {
  static String dolphinApiUrl = ''; //"http://192.168.139.200:3092";
  static const getRandomApi = '/api/pendingOrder/random'; //获取随机数
  static String random =
      'dc03364e82a210c1dadafb610dd2f1391393d03527ab59e5af8bd1efd9f23c6d';

  getRandomApiUrl() {
    AppLogger.d('getRandomApiUrl apiUrl=$dolphinApiUrl');
    return dolphinApiUrl;
    // return "https://dolphin.zoomlion2048.store";
  }

  getTestRandomApiUrl() {
    return "https://dolphin.zoomlion2048.store";
  }

  Future<String?> getRandom(String token, String type) async {
    var u = type == '0' ? getTestRandomApiUrl() : getRandomApiUrl();
    if (u.isEmpty) {
      return null;
    }
    var user = Get.find<AppConfigService>().getUserNameWithoutDomain();
    Map<String, dynamic> query = {'account': user};

    var url = u + getRandomApi;
    AppLogger.d('getRandom url=$url');
    var options = BaseOptions();
    options.connectTimeout = const Duration(seconds: 30);
    options.sendTimeout = const Duration(seconds: 30);
    options.contentType = 'application/json; charset=utf-8';
    options.headers = {
      HttpHeaders.authorizationHeader: "Bearer $token",
      HttpHeaders.contentTypeHeader: 'application/json; charset=utf-8',
    };
    String sigdata =
        await MiningPwdUtil.encryptForData(json.encode(query), rand: random) ??
            '';
    Map<String, dynamic> body = {'account': user, 'info': sigdata};
    AppLogger.d('submitBaseOperate query=$query');
    BaseResDate? data;
    try {
      var response = await createDio(options: options).post(
        url,
        data: body,
      );
      AppLogger.d('getRandom body =${response.data}');
      if (response.statusCode == 200) {
        data = BaseResDate.fromJson(response.data);
      }
    } catch (e) {
      AppLogger.e('getRandom e =${e.toString()}');
    }
    var dataBean = data?.data;
    if (dataBean != null && (dataBean is Map)) {
      var random = dataBean['random'];
      return random;
    }

    return null;
  }
}
