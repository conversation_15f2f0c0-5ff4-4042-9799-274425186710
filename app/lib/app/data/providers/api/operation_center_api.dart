//FileName operation_center_api
// <AUTHOR>
//@Date 2023/5/24 11:39

import 'dart:convert';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:dio/io.dart';
import 'package:flutter_metatel/app/data/models/authorization_code_result_model.dart';
import 'package:flutter_metatel/app/data/models/mobile_verify_result_model.dart';
import 'package:flutter_metatel/app/data/models/node_model.dart';
import 'package:flutter_metatel/app/data/models/real_name_verify_result_model.dart';
import 'package:flutter_metatel/app/data/models/res/node_forward.model.dart';
import 'package:flutter_metatel/app/data/models/res/node_info.model.dart';
import 'package:flutter_metatel/app/data/services/chatio_service.dart';
import 'package:flutter_metatel/app/data/services/config_service.dart';
import 'package:flutter_metatel/core/task/center_proxy_task.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:flutter_metatel/core/utils/device_util.dart';
import 'package:flutter_metatel/core/utils/proxy_util.dart';
import 'package:flutter_metatel/core/utils/util.dart';
import 'package:get/get.dart' as g;
import 'package:package_info_plus/package_info_plus.dart';
import '../../../../core/values/config.dart';
import '../../models/res/base_res_model.dart';
import 'base_dio_api.dart';

class OperationCenterApiProvider extends BaseDioClient {
  Dio _createDio({BaseOptions? o}) {
    Dio dio = Dio(o);
    dio.interceptors.add(InterceptorsWrapper(onError: (error, handler) {
      AppLogger.d(
          'BaseDioClient onError code=${error.response?.statusCode} onError =${error.error} response=${error.response.toString()}');
      // if (error.error.toString().contains(Config.proxyError)) {
      // }
      AppLogger.d(
          'BaseDioClient onError uri=${error.requestOptions.uri} ');

      CenterProxyTask.instance.changeCurrentProxy();
      return handler.next(error);
    }));
    dio.httpClientAdapter = IOHttpClientAdapter(
      createHttpClient: () {
        SecurityContext sc = SecurityContext();
        sc.setTrustedCertificatesBytes(Config.certData!.codeUnits);
        final client = HttpClient(context: sc);
        var proxy = CenterProxyTask.instance.getCurrentProxy();
        AppLogger.d('isOverseas=${Config.isOverseasNetWork} ');
        if (!Config.isOverseasNetWork) {
          client.findProxy = (proxy.isEmpty)
              ? null
              : (uri) {
                  AppLogger.d('net request findProxy=$proxy');
                  return 'PROXY $proxy';
                };
          if (proxy.isNotEmpty) {
            client.connectionFactory = (url, proxyHost, proxyPort) {
              return SecureSocket.startConnect(proxyHost!, proxyPort!,
                  onBadCertificate: (cert) {
                return true;
              });
            };
          }
        } else {
          client.findProxy = null;
        }
        client.badCertificateCallback =
            (X509Certificate cert, String host, int port) {
          AppLogger.d('net request host=$host:$port');
          return true;
        };
        return client;
      },
    );
    return dio;
  }

  /*
  * 获取通讯节点
  * */
  Future<Response<ComNodeModel>> getComNode(
      {String? account, String? keyBoxID}) async {
    Map<String, dynamic> body = {};
    if (account != null && account.isNotEmpty) {
      body['account'] = account;
    } else {
      body['account'] = "";
    }
    if (keyBoxID != null && keyBoxID.isNotEmpty) {
      body['keyboxid'] = keyBoxID;
    }
    AppLogger.d('getComNode body== $body');
    var url = Config.getOperationCenterGetNode();
    AppLogger.d('getComNode url== $url');
    try {
      BaseOptions _options =
          BaseOptions(headers: {HttpHeaders.userAgentHeader: Config.userAgent});
      _options.sendTimeout = const Duration(seconds: 30);
      _options.connectTimeout = const Duration(seconds: 30);
      var response = await _createDio(o: _options).post(url, data: body);
      AppLogger.d('Response data url===getComNode response== ${response.data}');
      AppLogger.d(
          'Response data url===getComNode statusText== ${response.statusMessage}');

      ComNodeModel? data;
      if (response.statusCode == 200) {
        data = ComNodeModel.fromJson(response.data);
      }
      return Response(
        statusCode: response.statusCode,
        statusMessage: response.statusMessage,
        data: data, requestOptions: response.requestOptions, //data
      );
    } catch (e) {
      AppLogger.e('$e');
      return Response(
          statusCode: -200,
          statusMessage: '$e',
          data: null,
          requestOptions: RequestOptions(path: ''));
    }
  }

  /// 实名认证
  Future<Response<RealNameVerifyResultModel>> realNameAuthentication(
    String verifyurl,
    String keyboxid,
    String signature,
    String account,
    String name,
    String idcard,
    int serverTimestamp,
    String phone,
  ) async {
    String data =
        'keyboxid=$keyboxid&signature=$signature&account=$account&name=$name&idcard=$idcard&timestamp=$serverTimestamp&phone=$phone';
    RealNameVerifyResultModel? body;
    try {
      BaseOptions _options = BaseOptions();
      _options.sendTimeout = const Duration(seconds: 15);
      _options.headers[HttpHeaders.contentTypeHeader] =
          'application/x-www-form-urlencoded';
      _options.headers[HttpHeaders.userAgentHeader] = Config.userAgent;
      var response = await _createDio(o: _options).post(verifyurl, data: data);
      AppLogger.d('realNameAuthentication body:${response.data}');

      if (response.statusCode == 200) {
        body = RealNameVerifyResultModel.fromJson(json.decode(response.data));
      }

      return Response(
          statusCode: response.statusCode,
          statusMessage: response.statusMessage,
          data: body,
          requestOptions: response.requestOptions);
    } catch (e) {
      AppLogger.e('$e');
      return Response(
          statusCode: -200,
          statusMessage: '$e',
          data: null,
          requestOptions: RequestOptions(path: ''));
    }
  }

  /// 手机/验证码 验证
  Future<Response<MobileVerifyResultModel>> mobileVerify(
    String verifyurl,
    String keyboxid,
    String signature,
    String account,
    int serverTimestamp,
    String action,
    String mobile,
    String code,
  ) async {
    String data =
        'keyboxid=$keyboxid&signature=$signature&account=$account&timestamp=$serverTimestamp&action=$action&mobile=$mobile&code=$code';

    MobileVerifyResultModel? body;
    try {
      BaseOptions options = BaseOptions();
      options.sendTimeout = const Duration(seconds: 15);
      options.contentType = 'application/x-www-form-urlencoded';
      options.headers[HttpHeaders.userAgentHeader] = Config.userAgent;

      var response = await _createDio(o: options).post(
        verifyurl,
        data: data,
      );
      AppLogger.d('mobileVerify body:${response.data}');

      if (response.statusCode == 200) {
        body = MobileVerifyResultModel.fromJson(json.decode(response.data));
      }
      return Response(
          statusCode: response.statusCode,
          statusMessage: response.statusMessage,
          data: body,
          requestOptions: response.requestOptions);
    } catch (e) {
      AppLogger.e('$e');
      return Response(
          statusCode: -200,
          statusMessage: '$e',
          data: null,
          requestOptions: RequestOptions(path: ''));
    }
  }

  /// 获取授权码
  Future<Response<AuthorizationCodeResultModel>> getAuthorizationCode() async {
    var client = DeviceUtil.getClientType().toLowerCase();
    var packageInfo = await PackageInfo.fromPlatform();
    var version = packageInfo.buildNumber;
    String data = 'client=$client&version=$version';
    var url = Config.getVerifyVodeUrl();
    AppLogger.d('getAuthorizationCode url=$url');
    AppLogger.d('getAuthorizationCode data=$data');
    AuthorizationCodeResultModel? body;
    BaseOptions _options =
        BaseOptions(headers: {HttpHeaders.userAgentHeader: Config.userAgent});
    _options.sendTimeout = const Duration(seconds: 20);
    _options.connectTimeout = const Duration(seconds: 20);
    _options.contentType = Headers.formUrlEncodedContentType;
    try {
      var response = await _createDio(o: _options).post(
        url,
        data: data,
      );
      if (response.statusCode == 200) {
        body =
            AuthorizationCodeResultModel.fromJson(json.decode(response.data));
      }
      AppLogger.d('getAuthorizationCode body=${response.data}');
      AppLogger.d('getAuthorizationCode statusCode=${response.statusCode}');
      if (response.statusCode == null) {
        AppLogger.d(
            'getAuthorizationCode statusText=${response.statusMessage}');
      }
      return Response(
          statusCode: response.statusCode,
          statusMessage: response.statusMessage,
          data: body,
          requestOptions: response.requestOptions);
    } catch (e) {
      AppLogger.e('getAuthorizationCode error$e');
      if (e.toString().contains(Config.proxyError)) {
        // ProxyUtil.instance.setDefaultProxy();
      }
      return Response(
          statusCode: -200,
          statusMessage: '$e',
          data: null,
          requestOptions: RequestOptions(path: ''));
    }
  }

  /// 获取节点
  Future<Map<String,dynamic>?> getNodeInfo({String? user}) async {
    NodeInfoModel? body;
    try {
      var response = await getNodeInfoRes(user: user);
      if (response?.statusCode == 200) {
        body = NodeInfoModel.fromJson(response?.data);
      }
      AppLogger.d('getNodeInfo body=${body?.data?.toJson()}');
      return {'node':body?.data?.node,'port':body?.data?.port};
    } catch (e) {
      AppLogger.e('getNodeInfo $e');
      return null;
    }
  }

  Future<Response?> getNodeInfoRes({String? user}) async {
    Map<String, dynamic> map = {'account': user??Config.userNameWithoutDomain};
    var url = Config.getNodeInfoUrl();
    AppLogger.d('getNodeInfoRes url=$url');
    AppLogger.d('getNodeInfoRes map=$map');
    BaseOptions _options =
    BaseOptions(headers: {HttpHeaders.userAgentHeader: Config.userAgent});
    _options.contentType = Headers.formUrlEncodedContentType;
    _options.sendTimeout = const Duration(seconds: 30);
    _options.contentType = 'application/json; charset=utf-8';
    try {
      var response = await _createDio(o: _options).post(
        url,
        data: map,
      );
      AppLogger.d('getNodeInfoRes body=${response.data}');
      return response;
    } catch (e) {
      AppLogger.e('getNodeInfo $e');
      return null;
    }
  }

  Future<bool> isGoogleConnected() async {
    Config.isOverseasNetWork = await _isGoogleConnectedApi();
    AppLogger.d('isGoogleConnected =${Config.isOverseasNetWork}');
    return Config.isOverseasNetWork;
  }

  Future<bool> _isGoogleConnectedApi() async {
    var isOk = false;
    try {
      var dio = Dio(BaseOptions(
          connectTimeout: const Duration(seconds: 3),
          sendTimeout: const Duration(seconds: 3)));
      dio.httpClientAdapter = IOHttpClientAdapter(createHttpClient: () {
        final client = HttpClient();
        client.findProxy = null;
        return client;
      });
      var respone = await dio.get('https://www.google.com/');
      AppLogger.d(
          'isGoogleConnected respone.statusCode =${respone.statusCode}');
      if (respone.statusCode == 200) {
        isOk = true;
      }
    } catch (e) {
      AppLogger.d('isGoogleConnected e=${e.toString()}');
      if (e.toString().contains('connection timeout')) {
      }
    }
    return isOk;
  }

  Future<Response> updateAppVersion() async {
    String url = Config.otaUrl();

    var packageInfo = await PackageInfo.fromPlatform();
    //版本号
    String? versionCode = packageInfo.buildNumber;
    AppLogger.d('checkVersion versionCode||||$versionCode');
    var platform = Platform.operatingSystem;
    var packName = packageInfo.packageName;
    var p = '$platform/$versionCode?package=$packName';
    var u = '$url/$p';
    AppLogger.d('checkVersion u=$u');
    try {
      var response = await _createDio(
          o: BaseOptions(
              sendTimeout: const Duration(seconds: 5),
              headers: {"user-agent": "metatel"})).get(u);
      AppLogger.d('checkVersion ${response.toString()}');

      return response;
    } catch (e) {
      AppLogger.d('checkVersion error=$e');
      return Response(
          statusCode: -200,
          statusMessage: '$e',
          data: null,
          requestOptions: RequestOptions(path: ''));
    }
  }

  Future<Response<NodeForward>> updateNodeForward() async {
    String url = Config.getNodeForwardUrl();

    var packageInfo = await PackageInfo.fromPlatform();
    //版本号
    int? versionCode = int.parse(packageInfo.buildNumber);
    AppLogger.d('updateNodeForward url||||$url');
    var node = Config.node();
    if (node.isEmpty) {
      AppLogger.e('updateNodeForward node is null');
      return Response(
          statusCode: -200,
          statusMessage: 'node is null',
          data: null,
          requestOptions: RequestOptions(path: ''));
      ;
    }
    if (node.contains('.')) {
      node = node.substring(0, node.indexOf('.'));
    }
    var map = {'version': versionCode, 'keyboxid': node};
    AppLogger.d('updateNodeForward map=$map');
    try {
      BaseOptions _options =
          BaseOptions(headers: {HttpHeaders.userAgentHeader: Config.userAgent});
      _options.contentType = Headers.formUrlEncodedContentType;
      _options.sendTimeout = const Duration(seconds: 30);
      _options.contentType = 'application/json; charset=utf-8';
      var response = await _createDio(o: _options).post(
        url,
        data: map,
      );
      AppLogger.d('updateNodeForward response=${response.data}');
      NodeForward? body;
      if (response.statusCode == 200) {
        body = NodeForward.fromJson(response.data);
        if (body.data?.port != null && body.data!.port! > 0) {
          g.Get.find<AppConfigService>()
              .saveNodePortNew('${body.data?.port ?? 0}');
        }
        if (body.data?.uri != null && (body.data!.uri?.isNotEmpty ?? false)) {
          g.Get.find<AppConfigService>().saveNodeNEW(body.data?.uri ?? '');
        }
      }
      return Response(
          statusCode: response.statusCode,
          statusMessage: response.statusMessage,
          data: body,
          requestOptions: response.requestOptions);
    } catch (e) {
      AppLogger.e('updateNodeForward e=${e.toString()}');
      return Response(
          statusCode: -200,
          statusMessage: '$e',
          data: null,
          requestOptions: RequestOptions(path: ''));
    }
  }

  //切换节点
  Future<Response> switchNode(String node) async {
    String url = '${Config.nodeApiUrl()}/keybox/modify_user';
    var map = {'account': Config.userNameWithoutDomain, 'node': node};
    AppLogger.d('switchNode map=$map');
    AppLogger.d('switchNode url=$url');

    try {
      BaseOptions _options =
      BaseOptions(headers: {HttpHeaders.userAgentHeader: Config.userAgent});
      _options.contentType = Headers.jsonContentType;
      _options.sendTimeout = const Duration(seconds: 30);
      _options.contentType = 'application/json; charset=utf-8';
      var response =  await createDio().post(
        url,
        data: map,
      );
      AppLogger.d('switchNode response=${response.data}');
      return Response(
          statusCode: response.statusCode,
          statusMessage: response.statusMessage,
          data: response.data,
          requestOptions: response.requestOptions);
    } catch (e) {
      AppLogger.e('switchNode e=${e.toString()}');
      return Response(
          statusCode: -200,
          statusMessage: '$e',
          data: null,
          requestOptions: RequestOptions(path: ''));
    }
  }
  //查询节点状态
  Future<Response<BaseRes>> findNodeSate(Map map,{int count=0}) async {
    String url = Config.findNodeSateUrl();
     AppLogger.d('findNodeSate map=$map');
    try {
      BaseOptions _options =
      BaseOptions(headers: {HttpHeaders.userAgentHeader: Config.userAgent});
      _options.contentType = Headers.jsonContentType;
      _options.sendTimeout = const Duration(seconds: 30);
      _options.contentType = 'application/json; charset=utf-8';
      var response = await _createDio(o: _options).post(
        url,
        data: map,
      );
      AppLogger.d('findNodeSate response=${response.data}');
      BaseRes ? model;
      if (response.statusCode == 200) {
        var data = response.data;
        if(data!=null){
          model = BaseRes.fromJson(data);
        }
        AppLogger.d('findNodeSate model=${model?.code}');

        if (model == null || model.code != 200) {
          if (count == 0) {
            await Future.delayed(const Duration(seconds: 3));
            return await findNodeSate(map, count: 1);
          }
        }
      } else {
        if (count == 0) {
          await Future.delayed(const Duration(seconds: 3));
          return await findNodeSate(map, count: 1);
        }

      }
      return Response(
          statusCode: response.statusCode,
          statusMessage: response.statusMessage,
          data: model,
          requestOptions: response.requestOptions);
    } catch (e) {
      AppLogger.e('findNodeSate e=${e.toString()}');
      return Response(
          statusCode: -200,
          statusMessage: '$e',
          data: null,
          requestOptions: RequestOptions(path: ''));
    }
  }
}
