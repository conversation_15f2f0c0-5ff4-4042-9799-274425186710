import 'dart:convert';
import 'dart:io';

import 'package:drift/drift.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_metatel/app/data/models/channel_black_list_model.dart';
import 'package:flutter_metatel/app/data/models/poll.dart';
import 'package:flutter_metatel/app/data/models/res/base_res_model.dart';
import 'package:flutter_metatel/app/data/models/res/moment/base_moment_res_model.dart';
import 'package:flutter_metatel/app/data/models/res/moment/comment_res_model.dart';
import 'package:flutter_metatel/app/data/models/res/moment/moment_info_res_model.dart';
import 'package:flutter_metatel/app/data/models/res/moment/pre_sign_res_model.dart';
import 'package:flutter_metatel/app/data/models/res/moment/upload_img_res_model.dart';
import 'package:flutter_metatel/core/utils/dio/dio_util.dart';
import 'package:flutter_metatel/core/values/code.dart';
import 'package:flutter_web3/app/modules/wallet/ethereum/wallet_controller.dart';
import '../../../../core/utils/app_log.dart';
import '../../../../core/utils/util.dart';
import '../../../../core/values/channel.dart';
import '../../../../core/values/config.dart';
import '../../../modules/square/bean/square_banner_bean.dart';
import '../../models/channel_apply_list_model.dart';
import '../../models/channel_info_model.dart';
import '../../models/channel_info_model_data.dart';
import '../../models/channel_member_info_model.dart';
import '../../models/channel_members_model.dart';
import '../../models/channel_msg_model.dart';
import '../../models/channel_msglist_model.dart';
import '../../models/channel_response_model.dart';
import '../../models/channels_model.dart';
import '../../models/res/moment/post_res_model.dart';
import '../../models/pre_sign_model.dart';
import '../../services/config_service.dart';

import 'package:get/get.dart'as g;
import 'package:dio/dio.dart';
/// 获取频道信息请求
Future<ChannelInfoModelData> getChannelInfoRequest(String id,
    {bool dsc = false}) async {
  if (id.isEmpty) return ChannelInfoModelData(code: Code.code1002);

  Map<String, dynamic> mapData = {};
  mapData['id'] = id;
  var userName =g.Get.find<AppConfigService>().getUserName();
  mapData['username'] = userName;
  mapData['desc'] = dsc;

  Response result;
  try {
    result =
        await getConnect().post(await Channel.getApi(), data:json.encode(mapData));
    AppLogger.d('获取频道信息请求 result:${result}');  
  } catch (e) {
    logger.e(e);
    return ChannelInfoModelData(code: Code.code1001);
  }

  if (result.statusCode != 200) {
    logger.e('获取频道信息请求  request code:${result.statusCode}');
    return ChannelInfoModelData(code: Code.code1003);
  }

  var model = ChannelInfoModel.fromJson(result.data);
  if (model.code != 200) {
    logger.e('获取频道信息请求 response code:${model.code} msg:${model.message}');
  }

  ChannelInfoModelData? data = model.info;
  if (data == null) {
    data = ChannelInfoModelData(code: model.code);
  } else {
    data.code = model.code;
  }

  return data;
}

/// 获取频道列表请求
Future<List<ChannelInfoModelData>?> getChannelsRequest() async {
  Response result;
  try {
    var userName =  g.Get.find<AppConfigService>().getUserName();
    result = await getConnect()
        .post(await Channel.listApi(), data:'{"username":"$userName"}');
    AppLogger.d('获取频道列表请求 result:${result}');    
  } catch (e) {
    logger.e(e);
    return null;
  }

  if (result.statusCode != 200) {
    logger.e('获取频道列表请求. request statusText:${result.statusMessage}');
    return null;
  }

  var model = ChannelsModel.fromJson(result.data);
  if (model.code != 200) {
    logger.e('获取频道列表请求 response code:${model.code} msg:${model.message}');
    return null;
  }

  return model.channels;
}

/// 创建频道请求
Future<String?> createChannelRequest(
  String title,
  String avatar,
  String describe,
  List<String> users,
) async {
  if (users.isEmpty) {
    logger.w('创建频道，成员列表为空');
    return null;
  }

  String? owner = g.Get.find<AppConfigService>().getUserName();

  Map<String, dynamic> mapChannelData = {};
  mapChannelData['title'] = title;
  mapChannelData['avatar'] = avatar;
  mapChannelData['owner'] = owner;
  mapChannelData['describe'] = describe;

  Map<String, dynamic> mapData = {};
  mapData['channel'] = mapChannelData;
  mapData['invites'] = users;

  String jsonData = json.encode(mapData);

  Response result;
  try {
    result = await getConnect().post(await Channel.createApi(), data:jsonData);
  } catch (e) {
    logger.e(e);
    return null;
  }

  if (result.statusCode != 200) {
    logger.e('创建频道  request code:${result.statusCode}');
    return null;
  }

  var model = ChannelDataRes.fromJson(result.data);
  if (model.code != 200) {
    toast('code:${model.code}', textColor: Colors.red);
    logger.e('创建频道 response code:${model.code} msg:${model.msg}');
    return null;
  }

  var channelID = model.data;
  if (channelID == null) {
    logger.e('创建频道 response data is null');
    return null;
  }

  return model.data;
}

/// 成员邀请或踢出请求
/// [time] 撤回时间（单位s） ==0:不撤回   <0:全部撤回  >0:撤回多长时间
Future<bool> memberInviteOrKickRequest(
  String id,
  List<String> users,
  bool invite, {
  int time = 0,
}) async {
  if (id.isEmpty || users.isEmpty) {
    logger.d('成员邀请或踢出请求(invite:$invite)参数为空。 id:$id users:$users');
    return false;
  }

  Map<String, dynamic> mapData = {};
  mapData['id'] = id;
  var userName = g.Get.find<AppConfigService>().getUserName();
  mapData['username'] = userName;

  if (invite) {
    mapData['invites'] = users;
  } else {
    mapData['members'] = users;
    mapData['del_msg'] = time;
  }

  String url = invite ? await Channel.inviteApi() : await Channel.kickApi();

  Response result;
  try {
    result = await getConnect().post(url,data: json.encode(mapData));
  } catch (e) {
    logger.e(e);
    return false;
  }

  if (result.statusCode != 200) {
    logger.e('成员邀请或踢出请求(invite:$invite)  request code:${result.statusCode}');
    return false;
  }

  var model = ChannelResponse.fromJson(result.data);
  if (model.code != 200) {
    toast('code:${model.code}', textColor: Colors.red);
    logger.e(
        '成员邀请或踢出请求(invite:$invite) response code:${model.code} msg:${model.msg}');
    return false;
  }

  return true;
}

/// 频道解散或退出请求
Future<bool> channelDismissOrLeaveRequest(String id, bool dismiss) async {
  if (id.isEmpty) {
    logger.d('频道解散或退出请求(dismiss:$dismiss)，参数为空。 id:$id');
    return false;
  }

  Map<String, dynamic> mapData = {};
  mapData['id'] = id;
  var userName = await g.Get.find<AppConfigService>().getUserName();
  mapData['username'] = userName;

  String url = dismiss ? await Channel.dismissApi() : await Channel.leaveApi();

  Response result;
  try {
    result = await getConnect().post(url, data:json.encode(mapData));
  } catch (e) {
    logger.e(e);
    return false;
  }

  if (result.statusCode != 200) {
    logger.e('频道解散或退出请求(dismiss:$dismiss)  request code:${result.statusCode}');
    return false;
  }

  var model = ChannelResponse.fromJson(result.data);
  if (model.code != 200) {
    toast('code:${model.code}', textColor: Colors.red);
    logger.e(
        '频道解散或退出请求(dismiss:$dismiss) response code:${model.code} msg:${model.msg}');
    return false;
  }

  return true;
}

/// 更新频道信息
Future<bool> updateChannelInfoRequest(ChannelInfoModelData channel) async {
  Map<String, dynamic> mapChannel = {};
  mapChannel['title'] = channel.title;
  mapChannel['avatar'] = channel.avatar;
  mapChannel['describe'] = channel.describe;
  mapChannel['wallpaper'] = channel.wallpaper;
  mapChannel['announcement']=channel.announcement;
  mapChannel['options']=channel.options;

  Map<String, dynamic> mapData = {};
  mapData['id'] = channel.id;
  var userName = await g.Get.find<AppConfigService>().getUserName();
  mapData['username'] = userName;
  mapData['channel'] = mapChannel;

  Response result;
  try {
    result = await getConnect()
        .post(await Channel.updateApi(), data:json.encode(mapData));
  } catch (e) {
    logger.e(e);
    return false;
  }

  if (result.statusCode != 200) {
    logger.e('更新频道信息  request code:${result.statusCode}');
    return false;
  }

  var model = ChannelResponse.fromJson(result.data);
  if (model.code != 200) {
    toast('code:${model.code}', textColor: Colors.red);
    logger.e('更新频道信息 response code:${model.code} msg:${model.msg}');
    return false;
  }

  return true;
}

/// 发送频道消息
Future<String?> sendMessageRequest(String id, String body,
    {List<String>? ats,String push = ''}) async {
  Map<String, dynamic> mapData = {};
  mapData['channel_id'] = id;
  var userName = g.Get.find<AppConfigService>().getUserName();
  mapData['username'] = userName;
  mapData['body'] = body;
  mapData['ats'] = ats;
  mapData['status'] = push;

  Response result;
  try {
    // AppLogger.d("send text: ${json.encode(mapData)}");
    result = await getConnect()
        .post(await ChannelMessage.createApi(), data:json.encode(mapData));
  } catch (e) {
    logger.e(e);
    return null;
  }

  if (result.statusCode != 200) {
    logger.e('发送频道消息  response statusCode:${result.statusCode}');
    return null;
  }
  var model = ChannelDataRes.fromJson(result.data);
  if (model.code != 200) {
    logger.e('发送频道消息 response code:${model.code} msg:${model.msg}');

    return null;
  }

  return model.data;
}

/// 同步频道消息
Future<List<ChannelMsgModelData>?> syncMessageRequest(String id, int limit,
    {String? maxUuid, String? minUuid}) async {
  Map<String, dynamic> mapData = {};
  mapData['channel_id'] = id;
  var userName = g.Get.find<AppConfigService>().getUserName();
  mapData['username'] = userName;
  mapData['limit'] = limit;
  if (maxUuid?.isNotEmpty ?? false) {
    mapData['id'] = maxUuid;
  }
  if (minUuid?.isNotEmpty ?? false) {
    mapData['min_id'] = minUuid;
  }
  AppLogger.d('syncMessageRequest param = ${json.encode(mapData)}');
  Response result;
  try {
    result = await getConnect()
        .post(await ChannelMessage.syncApi(), data:json.encode(mapData));
  } catch (e) {
    logger.e(e);
    return null;
  }

  if (result.statusCode != 200) {
    logger.e('同步频道消息  request code:${result.statusCode}');
    return null;
  }

  var model = ChannelMsgModel.fromJson(result.data);
  if (model.code != 200) {
    logger.e('同步频道消息 response code:${model.code}');
    return null;
  }
  logger.w('同步频道消息 response length:${model.msgDatas?.length}');

  return model.msgDatas;
}

/// 获取频道消息
Future<ChannelListMsgModel?> getMessageRequest(List channels, int limit) async {
  Map<String, dynamic> mapData = {};
  mapData['channels'] = channels;
  var userName = g.Get.find<AppConfigService>().getUserName();
  mapData['username'] = userName;
  mapData['limit'] = limit;
  // AppLogger.d('getMessageRequest channels=${json.encode(channels)}');
  // AppLogger.d('getMessageRequest mapData=${json.encode(mapData)}');

  Response result;
  try {
    result = await getConnect()
        .post(await ChannelMessage.getApiList(), data:json.encode(mapData));
  } catch (e) {
    logger.e(e);
    return null;
  }

  if (result.statusCode != 200) {
    logger.e('getMessageRequest  request code:${result.statusCode}');
    return null;
  }
  var model = ChannelListMsgModel.fromJson(result.data);
  logger.w('getMessageRequest response code:${model.code}');
  return model;

  // if (model.code != 200) {
  //   logger.e('获取频道消息 response code:${model.code}');
  //   return null;
  // }

  // return model.msgDatas;
}

/// 撤回频道消息
Future<bool> undoMessageRequest(String id) async {
  Map<String, dynamic> mapData = {};
  mapData['id'] = id;
  var userName = g.Get.find<AppConfigService>().getUserName();
  mapData['username'] = userName;

  Response result;
  try {
    result = await getConnect()
        .post(await ChannelMessage.undoApi(), data:json.encode(mapData));
  } catch (e) {
    logger.e(e);
    return false;
  }

  if (result.statusCode != 200) {
    logger.e('撤回频道消息  request code:${result.statusCode}');
    return false;
  }

  var model = ChannelDataRes.fromJson(result.data);
  if (model.code != 200) {
    logger.e('撤回频道消息 response code:${model.code} msg:${model.msg}');
    return false;
  }

  return true;
}

/// 更新我的群昵称请求
Future<bool> updateNicknameRequest(String id, String nickname) async {
  Map<String, dynamic> mapData = {};
  mapData['id'] = id;
  var userName = g.Get.find<AppConfigService>().getUserName();
  mapData['username'] = userName;
  mapData['nickname'] = nickname;

  Response result;
  try {
    result = await getConnect()
        .post(await Channel.updateMemberApi(),data:json.encode(mapData));
  } catch (e) {
    logger.e(e);
    return false;
  }

  if (result.statusCode != 200) {
    logger.e('更新我的群昵称  request code:${result.statusCode}');
    return false;
  }

  var model = ChannelResponse.fromJson(result.data);
  if (model.code != 200) {
    toast('code:${model.code}', textColor: Colors.red);
    logger.e('更新我的群昵称 response code:${model.code} msg:${model.msg}');
    return false;
  }

  return true;
}

/// 获取成员列表
Future<ChannelMembersData?> membersRequest(String id, String username) async {
  Map<String, dynamic> mapData = {};
  mapData['id'] = id;
  mapData['username'] = username;

  Response result;
  try {
    result = await getConnect()
        .post(await Channel.membersApi(),data:json.encode(mapData));
  } catch (e) {
    logger.e(e);
    return null;
  }

  if (result.statusCode != 200) {
    logger.e('获取成员列表  request code:${result.statusCode}');
    return null;
  }

  var model = ChannelMembersModel.fromJson(result.data);
  if (model.code != 200) {
    logger.e('获取成员列表 response code:${model.code} msg:${model.message}');
    return null;
  }

  return model.data;
}

/// 获取成员信息
Future<List<ChannelMemberInfoData>?> memberInfoRequest(
    String id, String username, List<String>? members) async {
  if (members == null || members.isEmpty) return null;

  Map<String, dynamic> mapData = {};
  mapData['id'] = id;
  mapData['username'] = username;
  mapData['members'] = members;

  Response result;
  try {
    result = await getConnect()
        .post(await Channel.getMemberApi(),data:json.encode(mapData));
  } catch (e) {
    logger.e(e);
    return null;
  }

  if (result.statusCode != 200) {
    logger.e('获取成员信息  request code:${result.statusCode}');
    return null;
  }

  var model = ChannelMemberInfoModel.fromJson(result.data);
  if (model.code != 200) {
    logger.e('获取成员信息 response code:${model.code} msg:${model.message}');
    return null;
  }

  return model.data;
}

/// 更新群主 id 频道Id,newOwner 新群主
Future<BaseRes?> updateOwner(String id, String newOwner) async {
  var userName = g.Get.find<AppConfigService>().getUserName();
  Map<String, dynamic> mapData = {};
  mapData['username'] = userName;
  mapData['owner'] = newOwner;
  mapData['id'] = id;

  Response result;
  try {
    result = await getConnect()
        .post(await Channel.updateOwnerApi(), data: json.encode(mapData));
  } catch (e) {
    logger.e(e);
    return null;
  }

  if (result.statusCode != 200) {
    logger.e('更新群主  request code:${result.statusCode}');
    return null;
  }

  var model = BaseRes.fromJson(result.data);
  if (model.code != 200) {
    logger.e('更新群主 response code:${model.code} msg:${model.message}');
    return null;
  }

  return model;
}

/// 设置频道禁言
Future<bool> muteRequest(String id, String username, bool mute) async {
  Map<String, dynamic> mapData = {};
  mapData['id'] = id;
  mapData['username'] = username;
  mapData['mute'] = mute;

  Response result;
  try {
    result =
        await getConnect().post(await Channel.muteApi(),data:json.encode(mapData));
  } catch (e) {
    logger.e(e);
    return false;
  }

  if (result.statusCode != 200) {
    logger.e('设置频道禁言  request code:${result.statusCode}');
    return false;
  }

  var model = ChannelResponse.fromJson(result.data);
  if (model.code != 200) {
    toast('code:${model.code}', textColor: Colors.red);
    logger.e('设置频道禁言 response code:${model.code} msg:${model.msg}');
    return false;
  }
  return true;
}

/// 频道分享设置
Future<bool> shareRequest(String id, String username, bool share) async {
  Map<String, dynamic> mapData = {};
  mapData['id'] = id;
  mapData['username'] = username;
  mapData['share'] = share;

  Response result;
  try {
    result =
        await getConnect().post(await Channel.shareApi(),data:json.encode(mapData));
  } catch (e) {
    logger.e(e);
    return false;
  }

  if (result.statusCode != 200) {
    logger.e('频道分享设置  request code:${result.statusCode}');
    return false;
  }

  var model = ChannelResponse.fromJson(result.data);
  if (model.code != 200) {
    toast('code:${model.code}', textColor: Colors.red);
    logger.e('频道分享设置 response code:${model.code} msg:${model.msg}');
    return false;
  }
  return true;
}

/// 加入频道
Future<bool> joinRequest(String id) async {
  Map<String, dynamic> mapData = {};
  mapData['id'] = id;
  var username = g.Get.find<AppConfigService>().getUserName();
  mapData['username'] = username;

  Response result;
  try {
    result =
        await getConnect().post(await Channel.joinApi(),data:json.encode(mapData));
  } catch (e) {
    logger.e(e);
    return false;
  }

  if (result.statusCode != 200) {
    logger.e('加入频道  request code:${result.statusCode}');
    return false;
  }

  var model = ChannelResponse.fromJson(result.data);
  if (model.code != 200) {
    toast('code:${model.code}', textColor: Colors.red);
    logger.e('加入频道 response code:${model.code} msg:${model.msg}');
    return false;
  }
  return true;
}

/// 申请加入频道
Future<bool> applyJoinRequest(String id) async {
  Map<String, dynamic> mapData = {};
  mapData['id'] = id;
  var username = g.Get.find<AppConfigService>().getUserName();
  mapData['username'] = username;

  Response result;
  try {
    result =
        await getConnect().post(await Channel.applyApi(),data:json.encode(mapData));
  } catch (e) {
    logger.e(e);
    return false;
  }

  if (result.statusCode != 200) {
    logger.e('申请加入频道  request code:${result.statusCode}');
    return false;
  }

  var model = ChannelResponse.fromJson(result.data);
  if (model.code != 200) {
    toast('code:${model.code}', textColor: Colors.red);
    logger.e('申请加入频道 response code:${model.code} msg:${model.msg}');
    return false;
  }
  return true;
}

/// 审批加入频道
Future<bool> approveJoinRequest(String id, String member) async {
  Map<String, dynamic> mapData = {};
  mapData['id'] = id;
  var username = g.Get.find<AppConfigService>().getUserName();
  mapData['username'] = username;
  mapData['member'] = member;

  Response result;
  try {
    result = await getConnect()
        .post(await Channel.approveApi(),data:json.encode(mapData));
  } catch (e) {
    logger.e(e);
    return false;
  }

  if (result.statusCode != 200) {
    logger.e('审批加入频道  request code:${result.statusCode}');
    return false;
  }

  var model = ChannelResponse.fromJson(result.data);
  if (model.code != 200) {
    toast('code:${model.code}', textColor: Colors.red);
    logger.e('审批加入频道 response code:${model.code} msg:${model.msg}');
    return false;
  }
  return true;
}

/// 添加/移除频道管理员
Future<bool> adminOrRemoveSetRequest(
    String id, List<String> members, bool isAdd) async {
  if (members.isEmpty) return false;

  Map<String, dynamic> mapData = {};
  mapData['id'] = id;
  var username = g.Get.find<AppConfigService>().getUserName();
  mapData['username'] = username;
  mapData['members'] = members;

  Response result;
  try {
    result = await getConnect().post(
        isAdd ? await Channel.addAdminApi() : await Channel.removeAdminApi(),
        data:json.encode(mapData));
  } catch (e) {
    logger.e(e);
    return false;
  }

  if (result.statusCode != 200) {
    logger.e('添加/移除频道管理员(isAdd:$isAdd)  request code:${result.statusCode}');
    return false;
  }

  var model = ChannelResponse.fromJson(result.data);
  if (model.code != 200) {
    toast('code:${model.code}', textColor: Colors.red);
    logger.e(
        '添加/移除频道管理员(isAdd:$isAdd)  response code:${model.code} msg:${model.msg}');
    return false;
  }
  return true;
}

/// 进频道审核设置
Future<bool> approvalRequest(String id, bool approval) async {
  Map<String, dynamic> mapData = {};
  mapData['id'] = id;
  var username = g.Get.find<AppConfigService>().getUserName();
  mapData['username'] = username;
  mapData['approve'] = approval;

  Response result;
  try {
    result = await getConnect()
        .post(await Channel.approvalApi(),data:json.encode(mapData));
  } catch (e) {
    logger.e(e);
    return false;
  }

  if (result.statusCode != 200) {
    logger.e('进频道审核设置  request code:${result.statusCode}');
    return false;
  }

  var model = ChannelResponse.fromJson(result.data);
  if (model.code != 200) {
    toast('code:${model.code}', textColor: Colors.red);
    logger.e('进频道审核设置  response code:${model.code} msg:${model.msg}');
    return false;
  }
  return true;
}

/// 获取加入申请列表
Future<List<String>?> applyListRequest(String id) async {
  Map<String, dynamic> mapData = {};
  mapData['id'] = id;
  var username = g.Get.find<AppConfigService>().getUserName();
  mapData['username'] = username;

  Response result;
  try {
    result = await getConnect()
        .post(await Channel.applyListApi(),data:json.encode(mapData));
  } catch (e) {
    logger.e(e);
    return null;
  }

  if (result.statusCode != 200) {
    logger.e('获取加入申请列表  request code:${result.statusCode}');
    return null;
  }

  var model = ChannelApplyListModel.fromJson(result.data);
  if (model.code != 200) {
    toast('code:${model.code}', textColor: Colors.red);
    logger.e('获取加入申请列表  response code:${model.code} msg:${model.message}');
    return null;
  }
  return model.data;
}

/// 成员禁言设置
/// [mute] 0:不禁言  <0:永久禁言  >0:禁言到期时间戳(秒)
Future<bool> memberMuteRequest(String id, String targetID, int mute) async {
  Map<String, dynamic> mapData = {};
  mapData['id'] = id;
  var username = await g.Get.find<AppConfigService>().getUserName();
  mapData['username'] = username;
  mapData['member'] = targetID;
  mapData['mute'] = mute;

  Response result;
  try {
    result = await getConnect()
        .post(await Channel.memberMuteApi(),data:json.encode(mapData));
  } catch (e) {
    logger.e(e);
    return false;
  }

  if (result.statusCode != 200) {
    logger.e('成员禁言设置  request code:${result.statusCode}');
    return false;
  }

  var model = ChannelResponse.fromJson(result.data);
  if (model.code != 200) {
    toast('code:${model.code}', textColor: Colors.red);
    logger.e('成员禁言设置  response code:${model.code} msg:${model.msg}');
    return false;
  }
  return true;
}

/// 频道消息撤回
/// [targetID] 被撤回的对象
/// [time] 撤回时间（单位s） ==0:不撤回   <0:全部撤回  >0:撤回多长时间
Future<bool> messageUndoRequest(String id, String targetID, int time) async {
  Map<String, dynamic> mapData = {};
  mapData['id'] = id;
  var username = await g.Get.find<AppConfigService>().getUserName();
  mapData['username'] = username;
  mapData['member'] = targetID;
  mapData['time_len'] = time;

  Response result;
  try {
    result =
        await getConnect().post(await Channel.undoApi(),data:json.encode(mapData));
  } catch (e) {
    logger.e(e);
    return false;
  }

  if (result.statusCode != 200) {
    logger.e('频道消息撤回  request code:${result.statusCode}');
    return false;
  }

  var model = ChannelResponse.fromJson(result.data);
  if (model.code != 200) {
    toast('code:${model.code}', textColor: Colors.red);
    logger.e('频道消息撤回  response code:${model.code} msg:${model.msg}');
    return false;
  }
  return true;
}

/// 获取频道广场
Future<List<ChannelInfoModelData>?> getSquareChannelsRequest({String?appCategory}) async {
  Response result;
  try {
    Map<String, dynamic>? data;
    if (appCategory != null) {
      data = {'appCategory': appCategory};
    }
    var url = await Channel.showlistApi();

    result = await getConnect()
        .get(url,queryParameters: data);
    var model = ChannelsModel.fromJson(result.data);
    if (model.code != 200) {
      logger.e('获取频道广场 response code:${model.code} msg:${model.message}');
      return null;
    }
    return model.channels;
  } catch (e) {
    logger.e(e);
    return null;
  }
}
/// 搜索频道广场
Future<List<ChannelInfoModelData>?> searchSquareChannelsRequest(String key) async {
  Response result;
  try {
    var url = '${(await Channel.searchShowListApi())}${Uri.encodeComponent(key)}' ;
    // logger.d('搜索频道广场.  url:$url');
    result = await getConnect()
        .get(url);
  } catch (e) {
    logger.e(e);
    return null;
  }
  logger.d('搜索频道广场. request body:${result.data}');

  if (result.statusCode != 200) {
    logger.e('搜索频道广场. request code:${result.statusCode}');
    return null;
  }

  var model = ChannelsModel.fromJson(result.data);
  if (model.code != 200) {
    logger.e('搜索频道广场 response code:${model.code} msg:${model.message}');
    return null;
  }

  return model.channels;
}

/// 通过消息ids查询消息详情
Future<List<ChannelMsgModelData>?> getMsgInfoByMsgUuids(
    {required List<String> ids, required String channelId}) async {
  Response result;
  try {
    var url = await ChannelMessage.messageInfo() ;
    AppLogger.d('通过消息ids查询消息详情.  url:$url');
    Map<String,dynamic> map={
      "ids":ids,
      "channel_id":channelId,
      "username":g.Get.find<AppConfigService>().getUserName(),
    };
    var encode = json.encode(map);
    AppLogger.d('通过消息ids查询消息详情. request body:${encode}');
    result = await getConnect()
        .post(url,data:json.encode(map));
  } catch (e) {
    logger.e(e);
    return null;
  }
  if (result.statusCode != 200) {
    logger.e('通过消息ids查询消息详情  request code:${result.statusCode}');
    return null;
  }

  var model = ChannelMsgModel.fromJson(result.data);
  if (model.code != 200) {
    logger.e('通过消息ids查询消息详情 response code:${model.code}');
    return null;
  }
  logger.w('通过消息ids查询消息详情 response length:${model.msgDatas?.length}');

  return model.msgDatas;
}
//////黑名单管理

/// 加入黑名单
Future<bool> addBlackListRequest(String id,String userName) async {
  Map<String, dynamic> mapData = {};
  mapData['channel_id'] = id;
  mapData['username'] = userName;
  Response result;
  try {
    result = await getConnect()
        .post(await Channel.addBlackApi(), data:json.encode(mapData));
  } catch (e) {
    logger.e(e);
    return false;
  }

  if (result.statusCode != 200) {
    logger.e('加入黑名单  request code:${result.statusCode}');
    return false;
  }

  var model = ChannelResponse.fromJson(result.data);
  if (model.code != 200) {
    toast('code:${model.code}', textColor: Colors.red);
    logger.e('加入黑名单  response code:${model.code} msg:${model.msg}');
    return false;
  }
  return true;
}


/// 黑名单列表
/// [mute] 0:不禁言  <0:永久禁言  >0:禁言到期时间戳(秒)
Future<List<String>?> getBlackListRequest(String id) async {
  Map<String, dynamic> mapData = {};
  mapData['id'] = id;
  Response result;
  try {
    result = await getConnect()
        .post(await Channel.getBlackListApi(), data:json.encode(mapData));
  } catch (e) {
    logger.e(e);
    return null;
  }

  if (result.statusCode != 200) {
    logger.e('黑名单列表  request code:${result.statusCode}');
    return null;
  }

  var model = ChannelBlackListReq.fromJson(result.data);
  if (model.code != 200) {
    toast('code:${model.code}', textColor: Colors.red);
    logger.e('黑名单列表  response code:${model.code} msg:${model.msg}');
    return null;
  }
  var users = <String>[];
  if (model != null && model.code == 200 && (model.data?.isNotEmpty ?? false)) {
    model.data?.forEach((element) {
      users.add(element.username ?? '');
    });
  }
  return users;
}


Future<bool> isBlackListRequest(String id,String userName) async {
  Map<String, dynamic> mapData = {};
  mapData['id'] = id;
  mapData['username'] = userName;
  Response result;
  try {
    result = await getConnect()
        .post(await Channel.isBlackApi(), data:json.encode(mapData));
  } catch (e) {
    logger.e(e);
    return false;
  }

  if (result.statusCode != 200) {
    logger.e('黑名单列表  request code:${result.statusCode}');
    return false;
  }

  var model = ChannelBlackListReq.fromJson(result.data);
  if (model.code != 200) {
    toast('code:${model.code}', textColor: Colors.red);
    logger.e('黑名单列表  response code:${model.code} msg:${model.msg}');
    return false;
  }
  return true;
}
Future<bool> removeBlackListRequest(String id,String userName) async {
  Map<String, dynamic> mapData = {};
  mapData['id'] = id;
  mapData['username'] = userName;
  mapData['op'] = g.Get.find<AppConfigService>().getUserName();

  Response result;
  try {
    result = await getConnect()
        .post(await Channel.removeBlackApi(), data:json.encode(mapData));
  } catch (e) {
    logger.e(e);
    return false;
  }

  if (result.statusCode != 200) {
    logger.e('黑名单列表  request code:${result.statusCode}');
    return false;
  }

  var model = ChannelBlackListReq.fromJson(result.data);
  if (model.code != 200) {
    toast('code:${model.code}', textColor: Colors.red);
    logger.e('黑名单列表  response code:${model.code} msg:${model.msg}');
    return false;
  }
  return true;
}

Future<Response<SquareBannerReq>> getBannerList() async {
  var url = await ChannelMessage.bannerList();
  AppLogger.d('getBannerList url=$url');
  var response = await getConnect().get(url,);
  SquareBannerReq? data;
  if (response.statusCode == 200) {
    data = SquareBannerReq.fromJson(response.data);
  }
  AppLogger.d('getBannerList response=${response.data}');

  return Response(
    statusCode: response.statusCode,
    statusMessage: response.statusMessage,
    data: data, requestOptions: response.requestOptions,
  );
}

Future<Response?> translationFromServer(Map<String,dynamic> map) async {
  var url =  '${Config.getConnection()}/translate';
  AppLogger.d('translate url=$url');
  var response;
  try {
     response = await getConnect().get(
      url, queryParameters:map,
    );
    return response;
  } catch (e) {
    AppLogger.e('translate ${e.toString()}');
   }

  AppLogger.d('translate response=${response.data}');

  return Response(
    statusCode: response.statusCode,
    statusMessage: response.statusMessage,
    data: response.date, requestOptions: response.requestOptions,
  );
}

/// 用token查询DAO信息
Future<ChannelInfoModelData> getDaoInfoByToken(String? tokenAddress, int chainId) async {
  if (tokenAddress?.isEmpty ?? true) return ChannelInfoModelData(code: Code.code1002);
  Map<String, dynamic> mapData = {};
  mapData['token_address'] = tokenAddress;
  mapData['chain'] = chainId;

  Response result;
  try {
    result = await getConnect().get(
      await Channel.getDaoByTokenApi(), 
      queryParameters: mapData,
    );
  } catch (e) {
    logger.e('获取Dao频道信息请求 $e');
    return ChannelInfoModelData(code: Code.code1001);
  }
  if (result.statusCode != 200) {
    logger.e('获取Dao频道信息请求  request code:${result.statusCode}');
    return ChannelInfoModelData(code: Code.code1003);
  }
  logger.d('获取Dao频道信息请求  request data:${result.data}');

  var model = ChannelInfoModel.fromJson(result.data);
  if (model.code != 200) {
    logger.e('获取Dao频道信息请求 response code:${model.code} msg:${model.message}');
  }

  ChannelInfoModelData? data = model.info;
  if (data == null) {
    data = ChannelInfoModelData(code: model.code);
  } else {
    data.code = model.code;
  }

  return data;
}

/// 创建Dao请求
Future<String?> createDaoRequest(
  String title,
  String avatar,
  String describe,
  String chain,
  String tokenAddress,
  double minNumToken,
  double balance,
  int decimals,
) async {
  String? owner = g.Get.find<AppConfigService>().getUserName();

  Map<String, dynamic> mapChannelData = {};
  mapChannelData['title'] = title;
  mapChannelData['avatar'] = avatar;
  mapChannelData['owner'] = owner;
  mapChannelData['describe'] = describe;
  mapChannelData['attribute'] = 1;  // 频道属性，0-普通，1-DAO
  mapChannelData['chain'] = chain;  // 链ID,比如K链就是756
  mapChannelData['token_address'] = tokenAddress;  // DAO token
  mapChannelData['min_num_token'] = minNumToken;  // 加入DAO最小持有token数量

  String? walletAddress = g.Get.find<WalletController>().wallet.value.address;
  Map<String, dynamic> mapWalletData = {};
  mapWalletData['address'] = walletAddress;
  mapWalletData['balance'] = balance;

  Map<String, dynamic> mapData = {};
  mapData['channel'] = mapChannelData;
  mapData['wallet'] = mapWalletData;
  mapData['decimals'] = decimals;

  String jsonData = json.encode(mapData);

  Response result;
  try {
    result = await getConnect().post(await Channel.createDaoApi(), data:jsonData);
  } catch (e) {
    logger.e(e);
    return null;
  }

  if (result.statusCode != 200) {
    logger.e('创建Dao频道  request code:${result.statusCode}');
    return null;
  }

  var model = ChannelDataRes.fromJson(result.data);
  if (model.code != 200) {
    toast('code:${model.code}', textColor: Colors.red);
    logger.e('创建Dao频道 response code:${model.code} msg:${model.msg}');
    return null;
  }

  var channelID = model.data;
  if (channelID == null) {
    logger.e('创建Dao频道 response data is null');
    return null;
  }

  return model.data;
}

/// 加入Dao请求
Future<bool> joinDaoRequest(String id, double balance) async {
  Map<String, dynamic> mapData = {};
  mapData['id'] = id;
  var username = g.Get.find<AppConfigService>().getUserName();
  mapData['username'] = username;
  String? walletAddress = g.Get.find<WalletController>().wallet.value.address;
  mapData['address'] = walletAddress;
  mapData['balance'] = balance;

  Response result;
  try {
    result =
        await getConnect().post(await Channel.joinDaoApi(),data:json.encode(mapData));
  } catch (e) {
    logger.e(e);
    return false;
  }

  if (result.statusCode != 200) {
    logger.e('加入Dao  request code:${result.statusCode}');
    return false;
  }

  var model = ChannelResponse.fromJson(result.data);
  if (model.code != 200) {
    toast('code:${model.code}', textColor: Colors.red);
    logger.e('加入Dao response code:${model.code} msg:${model.msg}');
    return false;
  }
  return true;
}

/// 创建或更新Dao角色请求
Future<bool> createOrUpdateDaoRoleRequest(String id, // 频道ID	
  {
    String? roleId, // 更新角色标签，则字段不为空  
    required String roleName,
    required String roleColor,
    required List<String> assignedUsers,  
  }
) async {
  String? username = g.Get.find<AppConfigService>().getUserName();
  Map<String, dynamic> mapData = {};
  mapData['id'] = id;
  mapData['username'] = username;
  mapData['role_name'] = roleName;
  mapData['role_color'] = roleColor;
  mapData['assigned_users'] = assignedUsers;
  mapData['role_id'] = roleId;
  String jsonData = json.encode(mapData);
  Response result;
  try {
    result = await getConnect().post(await Channel.createOrUpdateDaoRoleApi(), data:jsonData);
    logger.d('创建或更新Dao角色  result:$result');
  } catch (e) {
    logger.e(e);
    return false;
  }
  if (result.statusCode != 200) {
    logger.e('创建或更新Dao角色  request code:${result.statusCode}');
    return false;
  }
  var model = ChannelDataRes.fromJson(result.data);
  if (model.code != 200) {
    toast('code:${model.code}', textColor: Colors.red);
    logger.e('创建或更新Dao角色 response code:${model.code} msg:${model.msg}');
    return false;
  }

  return true;
}

Future<bool> deleteDaoRoleRequest(String id, String roleId) async {
  String? username = g.Get.find<AppConfigService>().getUserName();
  Map<String, dynamic> mapData = {};
  mapData['id'] = id;
  mapData['username'] = username;
  mapData['role_id'] = roleId;

  Response result;
  try {
    result = await getConnect()
        .post(await Channel.deleteDaoRoleApi(), data:json.encode(mapData));
    AppLogger.d('删除Dao角色  result:$result');
  } catch (e) {
    logger.e(e);
    return false;
  }

  if (result.statusCode != 200) {
    logger.e('删除Dao角色  request code:${result.statusCode}');
    return false;
  }

  var model = ChannelBlackListReq.fromJson(result.data);
  if (model.code != 200) {
    toast('code:${model.code}', textColor: Colors.red);
    logger.e('删除Dao角色  response code:${model.code} msg:${model.msg}');
    return false;
  }
  return true;
}

/// 获取对应标签所属成员列表请求
Future<List<ChannelMemberInfoData>?> getDaoRoleMembersRequest(String id, String roleId) async {
  Map<String, dynamic> mapData = {};
  mapData['id'] = id;
  mapData['role_id'] = roleId;

  Response result;
  try {
    result = await getConnect().get(
      await Channel.getDaoRoleMembersApi(), 
      queryParameters: mapData,
    );
    AppLogger.d('获取对应标签所属成员列表请求 result:$result');
  } catch (e) {
    logger.e('获取对应标签所属成员列表请求 $e');
    return null;
  }
  if (result.statusCode != 200) {
    logger.e('获取对应标签所属成员列表请求  request code:${result.statusCode}');
    return null;
  }

  var model = ChannelMemberInfoModel.fromJson(result.data);
  if (model.code != 200) {
    logger.e('获取对应标签所属成员列表请求 response code:${model.code} msg:${model.message}');
    return null;
  }

  return model.data;
}

Future<bool> deleteDaoRoleMembersRequest(String id, String roleId, List<String> members) async {
  String? username = g.Get.find<AppConfigService>().getUserName();
  Map<String, dynamic> mapData = {};
  mapData['id'] = id;
  mapData['username'] = username;
  mapData['role_id'] = roleId;
  mapData['members'] = members;

  Response result;
  try {
    result = await getConnect()
        .post(await Channel.deleteDaoRoleMembersApi(), data:json.encode(mapData));        
    AppLogger.d('删除Dao角色成员  result:$result');
  } catch (e) {
    logger.e(e);
    return false;
  }

  if (result.statusCode != 200) {
    logger.e('删除Dao角色成员  request code:${result.statusCode}');
    return false;
  }

  var model = ChannelBlackListReq.fromJson(result.data);
  if (model.code != 200) {
    toast('code:${model.code}', textColor: Colors.red);
    logger.e('删除Dao角色成员  response code:${model.code} msg:${model.msg}');
    return false;
  }
  return true;
}

/// 获取Dao投票列表请求
Future<List<Poll>?> getDaoVotesRequest(String id, {int condition = 0}) async {
  /// Condition: 条件，等于0取全部，大于0取正在进行的
  Map<String, dynamic> mapData = {};
  mapData['channel_id'] = id;
  mapData['condition'] = condition;

  Response result;
  try {
    result = await getConnect().get(
      await Channel.getDaoVotesApi(), 
      queryParameters: mapData,
    );
    // AppLogger.d('获取Dao投票列表请求 result:$result');
  } catch (e) {
    logger.e('获取Dao投票列表请求 $e');
    return null;
  }
  if (result.statusCode != 200) {
    logger.e('获取Dao投票列表请求  request code:${result.statusCode}');
    return null;
  }

  var model = PollListResModel.fromJson(result.data);
  if (model.code != 200) {
    logger.e('获取Dao投票列表请求 response code:${model.code} msg:${model.message}');
    return null;
  }

  return model.data;
}

/// 获取单个Dao投票详情请求
Future<Poll?> getDaoVoteRequest(String id) async {  
  Map<String, dynamic> mapData = {};
  mapData['id'] = id;

  Response result;
  try {
    result = await getConnect().get(
      await Channel.getDaoVoteApi(), 
      queryParameters: mapData,
    );
    // AppLogger.d('获取单个Dao投票详情请求 result:$result');
  } catch (e) {
    logger.e('获取单个Dao投票详情请求 $e');
    return null;
  }
  if (result.statusCode != 200) {
    logger.e('获取单个Dao投票详情请求  request code:${result.statusCode}');
    return null;
  }

  var model = PollResModel.fromJson(result.data);
  if (model.code != 200) {
    logger.e('获取单个Dao投票详情请求 response code:${model.code} msg:${model.message}');
    return null;
  }

  return model.data;
}

/// 创建Dao投票请求
Future<String?> createDaoVoteRequest(String id, {required String title, String? describe,
    required int category, required int type, required List<String> options, required int duration}) async {
  String? username = g.Get.find<AppConfigService>().getUserName();
  Map<String, dynamic> mapData = {};
  mapData['channel_id'] = id;
  mapData['title'] = title;
  mapData['describe'] = describe;
  mapData['category'] = category; // 投票类别，1-普通投票，2-提案投票，3-DAO 投票，4-Leader 选举投票
  mapData['type'] = type; // 投票类型，1-一人一票，2-按照持仓比例投票
  mapData['username'] = username; // 发起人
  mapData['options'] = options; // 选项列表，为DAO投票时，这里是数量字符串；Leader选举时，这里为报名的用户账号
  mapData['duration'] = duration; // 投票时间，单位秒。1小时就是3600秒

  String jsonData = json.encode(mapData);
  Response result;
  try {
    result = await getConnect().post(await Channel.createDaoVoteApi(), data:jsonData);
    // AppLogger.d('创建Dao投票请求  result:$result');
  } catch (e) {
    logger.e(e);
    return null;
  }
  if (result.statusCode != 200) {
    logger.e('创建Dao投票请求  request code:${result.statusCode}');
    return null;
  }
  var model = ChannelDataRes.fromJson(result.data);
  if (model.code != 200) {
    toast('code:${model.code}', textColor: Colors.red);
    logger.e('创建Dao投票请求 response code:${model.code} msg:${model.msg}');
    return null;
  }

  return model.data;
}

/// Dao Leader报名请求
Future<bool> daoLeaderRegisterRequest(String id) async {
  String? username = g.Get.find<AppConfigService>().getUserName();
  Map<String, dynamic> mapData = {};
  mapData['id'] = id;
  mapData['username'] = username;

  String jsonData = json.encode(mapData);
  Response result;
  try {
    result = await getConnect().post(await Channel.daoLeaderRegisterApi(), data:jsonData);
    // AppLogger.d('Dao Leader报名请求  result:$result');
  } catch (e) {
    logger.e(e);
    return false;
  }
  if (result.statusCode != 200) {
    logger.e('Dao Leader报名请求  request code:${result.statusCode}');
    return false;
  }
  var model = ChannelDataRes.fromJson(result.data);
  if (model.code != 200) {
    toast('code:${model.code}', textColor: Colors.red);
    logger.e('Dao Leader报名请求 response code:${model.code} msg:${model.msg}');
    return false;
  }

  return true;
}

/// Dao投票提交请求
Future<bool> daoVoteSubmitRequest(String id, String optionId) async {
  String? username = g.Get.find<AppConfigService>().getUserName();
  Map<String, dynamic> mapData = {};
  mapData['id'] = id;
  mapData['optionID'] = optionId;
  mapData['username'] = username;

  String jsonData = json.encode(mapData);
  Response result;
  try {
    result = await getConnect().post(await Channel.daoVoteSubmitApi(), data:jsonData);
    // AppLogger.d('Dao投票提交请求  result:$result');
  } catch (e) {
    logger.e(e);
    return false;
  }
  if (result.statusCode != 200) {
    logger.e('Dao投票提交请求  request code:${result.statusCode}');
    return false;
  }
  var model = ChannelDataRes.fromJson(result.data);
  if (model.code != 200) {
    toast('code:${model.code}', textColor: Colors.red);
    logger.e('Dao投票提交请求 response code:${model.code} msg:${model.msg}');
    return false;
  }

  return true;
}



/// 频道动态相关接口
class ChannelMomentApi {

  static final _baseUrl = Config.momentApiBaseUrl();

  /// 获取频道动态列表请求
  static Future<PostPaginationModel?> getPosts({required Map<String, String> headers, String? pageNo="1"}) async {
    Response response;
    PostPaginationModel? model;
    try {
      Options options = getMomentOptions(headers);
      var data = { "pageNo": pageNo ?? "1" };
      // 调用接口
      response = await DioUtil().post(
        _baseUrl,
        ChannelMomentApiUrl.getPosts, 
        options: options,
        data: data,
      );
      // 对response进行解析
      var resModel = PostResModel.fromJson(response.data);
      if (resModel.status != true) {
        AppLogger.e('接口status false, msg:${resModel.message}');
        return null;
      }
      model = resModel.data;
      return model ?? null;
    } catch (e) {      
      if (!(e is DioException)) {
        // 只显示除DioException外的Exception, DioException由拦截器处理
        AppLogger.e("Error Catch<getPosts>: $e");  
      }
      return null;
    }
  }

  /// 创建新帖/更新帖
  static Future<bool?> addPost({required Map<String, String> headers, String? postId, String? content, List<String>? links, List<String>? imageUrls, String? videoUrl, String? videoThumbnail}) async {
    Response response;
    try {
      Options options = getMomentOptions(headers);
      var data = { 
        "postID": postId ?? '-new-',
        "content": content ?? '',
        "links": links ?? [],
        "imageUrls": imageUrls ?? [],
        "videoUrl": videoUrl ?? '',
        "videoThumbnail": videoThumbnail,
      };
      // 调用接口
      response = await DioUtil().post(
        _baseUrl,
        ChannelMomentApiUrl.addPost, 
        options: options,
        data: data,
      );
      // 对response进行解析
      var resModel = BaseMomentResModel.fromJson(response.data);
      if (resModel.status != true) {
        AppLogger.e('接口status false, msg:${resModel.message}');
        return false;
      }      
      return true;
    } catch (e) {      
      if (!(e is DioException)) {
        // 只显示除DioException外的Exception, DioException由拦截器处理
        AppLogger.e("Error Catch<addPost>: $e");  
      }
      return null;
    }
  }

  /// 上传图片并获取URL
  static Future<String?> uploadImage({required Map<String, String> headers, required File file, Function(int, int)? onSendProgress}) async {
    Response response;
    try {
      Options options = getMomentOptions(headers);
      var data = FormData.fromMap({
        'file': await MultipartFile.fromFile(file.path),
      });
      // 调用接口
      response = await DioUtil().post(
        _baseUrl,
        ChannelMomentApiUrl.uploadImage, 
        options: options,
        data: data,
        onSendProgress: onSendProgress,
      );
      // 对response进行解析
      var resModel = UploadImgResModel.fromJson(response.data);
      if (resModel.status != true) {
        AppLogger.e('接口status false, msg:${resModel.message}');
        return null;
      }      
      return resModel.data?.url;
    } catch (e) {      
      if (!(e is DioException)) {
        // 只显示除DioException外的Exception, DioException由拦截器处理
        AppLogger.e("Error Catch<uploadImage>: $e");  
      }
      return null;
    }
  }

  /// 删除帖
  static Future<bool?> deletePost({required Map<String, String> headers, required String postId}) async {
    Response response;
    try {
      Options options = getMomentOptions(headers);
      var data = { 
        "postID": postId
      };
      // 调用接口
      response = await DioUtil().post(
        _baseUrl,
        ChannelMomentApiUrl.deletePost, 
        options: options,
        data: data,
      );
      // 对response进行解析
      var resModel = BaseMomentResModel.fromJson(response.data);
      if (resModel.status != true) {
        AppLogger.e('接口status false, msg:${resModel.message}');
        return false;
      }      
      return true;
    } catch (e) {      
      if (!(e is DioException)) {
        // 只显示除DioException外的Exception, DioException由拦截器处理
        AppLogger.e("Error Catch<deletePost>: $e");  
      }
      return null;
    }
  }

  /// 点赞帖
  static Future<bool?> likePost({required Map<String, String> headers, required String postId}) async {
    Response response;
    try {
      Options options = getMomentOptions(headers);
      var data = { 
        "postID": postId
      };
      // 调用接口
      response = await DioUtil().post(
        _baseUrl,
        ChannelMomentApiUrl.likePost, 
        options: options,
        data: data,
      );
      // 对response进行解析
      var resModel = BaseMomentResModel.fromJson(response.data);
      if (resModel.status != true) {
        AppLogger.e('接口status false, msg:${resModel.message}');
        return false;
      }      
      return true;
    } catch (e) {      
      if (!(e is DioException)) {
        // 只显示除DioException外的Exception, DioException由拦截器处理
        AppLogger.e("Error Catch<likePost>: $e");  
      }
      return null;
    }
  }

  /// 回滚点赞帖
  static Future<bool?> unlikePost({required Map<String, String> headers, required String postId}) async {
    Response response;
    try {
      Options options = getMomentOptions(headers);
      var data = { 
        "postID": postId
      };
      // 调用接口
      response = await DioUtil().post(
        _baseUrl,
        ChannelMomentApiUrl.unLikePost, 
        options: options,
        data: data,
      );
      // 对response进行解析
      var resModel = BaseMomentResModel.fromJson(response.data);
      if (resModel.status != true) {
        AppLogger.e('接口status false, msg:${resModel.message}');
        return false;
      }      
      return true;
    } catch (e) {      
      if (!(e is DioException)) {
        // 只显示除DioException外的Exception, DioException由拦截器处理
        AppLogger.e("Error Catch<unlikePost>: $e");  
      }
      return null;
    }
  }

  /// 获取频道动态基本信息
  static Future<String?> getMomentInfo({required Map<String, String> headers}) async {
    Response response;
    try {
      Options options = getMomentOptions(headers);
      // 调用接口
      response = await DioUtil().post(
        _baseUrl,
        ChannelMomentApiUrl.getMomentInfo, 
        options: options,
      );
      // 对response进行解析
      var resModel = MomentInfoResModel.fromJson(response.data);
      if (resModel.status != true) {
        AppLogger.e('接口status false, msg:${resModel.message}');
        return null;
      }      
      return resModel.data?.description;
    } catch (e) {      
      if (!(e is DioException)) {
        // 只显示除DioException外的Exception, DioException由拦截器处理
        AppLogger.e("Error Catch<getMomentInfo>: $e");  
      }
      return null;
    }
  }

  /// 更新频道动态基本信息
  static Future<bool?> updateMomentInfo({required Map<String, String> headers, required String groupName, required String description }) async {
    Response response;
    try {
      Options options = getMomentOptions(headers);
      var data = { 
        "groupName": groupName,
        "description": description,
      };
      // 调用接口
      response = await DioUtil().post(
        _baseUrl,
        ChannelMomentApiUrl.updateMomentInfo, 
        options: options,
        data: data,
      );
      // 对response进行解析
      var resModel = BaseMomentResModel.fromJson(response.data);
      if (resModel.status != true) {
        AppLogger.e('接口status false, msg:${resModel.message}');
        return false;
      }      
      return true;
    } catch (e) {      
      if (!(e is DioException)) {
        // 只显示除DioException外的Exception, DioException由拦截器处理
        AppLogger.e("Error Catch<updateMomentInfo>: $e");  
      }
      return null;
    }
  }

  /// 添加评论
  static Future<bool?> commentPost({required Map<String, String> headers, required String postId, required String comment}) async {
    Response response;
    try {
      Options options = getMomentOptions(headers);
      var data = { 
        "postID": postId,
        "comment": comment,
      };
      // 调用接口
      response = await DioUtil().post(
        _baseUrl,
        ChannelMomentApiUrl.commentPost, 
        options: options,
        data: data,
      );
      // 对response进行解析
      var resModel = BaseMomentResModel.fromJson(response.data);
      if (resModel.status != true) {
        AppLogger.e('接口status false, msg:${resModel.message}');
        return false;
      }      
      return true;
    } catch (e) {      
      if (!(e is DioException)) {
        // 只显示除DioException外的Exception, DioException由拦截器处理
        AppLogger.e("Error Catch<commentPost>: $e");  
      }
      return null;
    }
  }

  /// 获取更多评论
  static Future<CommentPaginationModel?> getMoreComments({required Map<String, String> headers, required String postId, required String pageNo}) async {
    Response response;
    try {
      Options options = getMomentOptions(headers);
      var data = { 
        "postID": postId,
        "pageNo": pageNo,
      };
      // 调用接口
      response = await DioUtil().post(
        _baseUrl,
        ChannelMomentApiUrl.getMoreComments, 
        options: options,
        data: data,
      );
      // 对response进行解析
      var resModel = CommentResModel.fromJson(response.data);
      if (resModel.status != true) {
        AppLogger.e('接口status false, msg:${resModel.message}');
        return null;
      }      
      return resModel.data;
    } catch (e) {      
      if (!(e is DioException)) {
        // 只显示除DioException外的Exception, DioException由拦截器处理
        AppLogger.e("Error Catch<getMoreComments>: $e");  
      }
      return null;
    }
  }

  /// 删除评论
  static Future<bool?> deleteComment({required Map<String, String> headers, required String postId ,required String commentId}) async {
    Response response;
    try {
      Options options = getMomentOptions(headers);
      var data = { 
        "postID": postId,
        "commentID": commentId,
      };
      // 调用接口
      response = await DioUtil().post(
        _baseUrl,
        ChannelMomentApiUrl.deleteComment, 
        options: options,
        data: data,
      );
      // 对response进行解析
      var resModel = BaseMomentResModel.fromJson(response.data);
      if (resModel.status != true) {
        AppLogger.e('接口status false, msg:${resModel.message}');
        return false;
      }      
      return true;
    } catch (e) {      
      if (!(e is DioException)) {
        // 只显示除DioException外的Exception, DioException由拦截器处理
        AppLogger.e("Error Catch<deleteComment>: $e");  
      }
      return null;
    }
  }

  /// 获取预签名地址
  static Future<PreSignModel?> getPreSignUrl({required Map<String, String> headers, required String fileName}) async {
    Response response;
    try {
      Options options = getMomentOptions(headers);
      // 调用接口
      response = await DioUtil().post(
        _baseUrl,
        ChannelMomentApiUrl.getPreSignedUrl, 
        options: options,
      );
      // 对response进行解析
      var resModel = PreSignResModel.fromJson(response.data);
      if (resModel.status != true) {
        AppLogger.e('接口status false, msg:${resModel.message}');
        return null;
      }      
      return resModel.data;
    } catch (e) {      
      if (!(e is DioException)) {
        // 只显示除DioException外的Exception, DioException由拦截器处理
        AppLogger.e("Error Catch<getPreSignedUrl>: $e");  
      }
      return null;
    }
  }

  /// 保存图片/视频（预签名）
  static Future<bool?> storeMediaViaPreSignUrl({required String url ,required File file, Function(int,int)? onSendProgress}) async {
    Uint8List mediaBytes = file.readAsBytesSync();
    Response response;
    try {
      Options options = Options();
      // options.contentType = 'application/octet-stream';
      options.contentType = 'video/mp4';
      options.sendTimeout = const Duration(minutes: 3);
      options.headers = {
        'content-length': mediaBytes.length,
      };
      var chunks = await splitFileIntoChunk(file);
      if(chunks.isEmpty) throw "Chunk list is empty";
      // 调用接口
      response = await DioUtil().put(
        _baseUrl,
        url, 
        binary: Stream.fromIterable(chunks),
        options: options,
        onSendProgress: onSendProgress,
      );
      return response.statusCode == 200 || response.statusCode == 204;
    } catch (e) {      
      if (!(e is DioException)) {
        // 只显示除DioException外的Exception, DioException由拦截器处理
        AppLogger.e("Error Catch<preSignedStoreMedia>: $e");  
      }
      return null;
    }
  }
}

class ChannelTag{
  ///频道标签int型2进制，多标签使用与操作生成
  ///现有标签
  static const dao = 0x01;
  static const vip = 0x02;


  ChannelTag._internal();

  factory ChannelTag() => _instance;

  static final ChannelTag _instance = ChannelTag._internal();

  // int channelTag(){
  //   int channelTag = vip;
  //   return channelTag;
  // }

  bool hasVipTag(int tag){
    return tag&vip==vip;
  }
}