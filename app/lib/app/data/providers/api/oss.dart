import 'dart:convert';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:flutter_metatel/app/data/models/sts_info_model.dart';
import 'package:flutter_metatel/app/data/providers/api/api.dart';
import 'package:flutter_metatel/app/data/services/config_service.dart';
import 'package:flutter_metatel/core/task/time_task.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:minio/io.dart';
import 'package:minio/minio.dart';

import '../../../../core/values/code.dart';
import '../../../../core/values/config.dart';

class OssProvider {
  OssProvider() {
    _createMinio();
  }

  StsData? _stsInfo;
  Minio? _minio;

  /// 重试
  bool _retry = false;
  String getTemBucket() {
    var timeStr = DateFormat("yyyy-MM-dd").format(DateTime.now());
    var bucket = '/test/$timeStr';
    AppLogger.d('getTemBucket =$bucket');
    return bucket;
  }

  Future<String> upload(String objectName, String filePath,
      {bool isLastDir = false}) async {
    if (_minio == null) {
      _createMinio();
      return '';
    }

    try {
      String bucket = _stsInfo!.bucket ?? '';
      var b = '${_stsInfo!.dir}${getTemBucket()}';
      String object =
          '${isLastDir ? Config.lastMinioDir : b}/$objectName';
      AppLogger.d('upload object:$object');
      AppLogger.d('upload filePath:$filePath');

      await _minio!.fPutObject(bucket, object, filePath);

      String url = 'https://${_minio!.endPoint}/$bucket/$object';
      AppLogger.d('upload url:$url');
      return url;
    } catch (e, s) {
      AppLogger.e('Exception details:\n$e');
      AppLogger.e('Stack trace:\n$s');

      /// token过期，重新获取token
      if (!_retry) {
        _retry = true;
        var response = await Get.find<ApiProvider>().getStsInfo();
        if (response.data?.code == Code.code200) {
          Get.find<AppConfigService>()
              .saveStsInfo(response.data?.stsData?.toJson());
        }
        _createMinio();
        return upload(objectName, filePath);
      }
      return '';
    }
  }

  Future<bool> downloadEmoji(String objectName, String filePath) async {
    if (_minio == null) {
      return false;
    }

    try {
      String bucket = _stsInfo!.bucket ?? '';
      var f = bucket.endsWith('/')?'':'/';
      String object = '${f}emojis/$objectName';
      String url = 'https://${_minio!.endPoint}/$bucket$object';
      AppLogger.e('downloadEmoji url:\n$url');

      var response = await Dio().download(
        url,
        filePath,
      );

      if (response.statusCode != 200) {
        throw Exception(
            'downloadEmoji Download emoji failed. statusCode: ${response.statusCode}');
      }
      return true;
    } catch (e, s) {
      AppLogger.e('downloadEmoji Exception details:\n$e');
      AppLogger.e('downloadEmoji Stack trace:\n$s');
      return false;
    }
  }

  void _createMinio() {
    var data = Get.find<AppConfigService>().getStsInfo();
    if (data == null) {
      return;
    }
    AppLogger.d('ossData =${data.toString()}');

    _stsInfo = StsData.fromJson(data);
    // _stsInfo?.bucket = 'malai';
    // String endpoint = '${_stsInfo?.bucket}.${_stsInfo?.endPoint}';
    String endpoint = _stsInfo?.endPoint??'';
    // String endpoint = '47.236.147.223';

    _minio = Minio(
      endPoint: endpoint,
      accessKey: _stsInfo!.accessKeyId ?? '',
      secretKey: _stsInfo!.accessKeySecret ?? '',
      sessionToken: _stsInfo!.sessionToken ?? '',
      region: _stsInfo!.regionId ?? '',
      dataTimeCallback: (d) {
        return TimeTask.instance.getNowDateTime();
      },
      enableTrace: true,
      // useSSL: false,
      // port: 14201
    );
  }
  test(){
  _test();
}

  void _test() {
    String awsText =
        '{\"endPoint\":\"resource3t.top\",\"accessKeyId\":\"3sAncwyB\",\"accessKeySecret\":\"RD6lzQX7JowCmTU9\",\"regionId\":\"hk-1\",\"bucket\":\"myres\",\"sessionToken\":null,\"expiration\":null,\"dir\":\"temp\"}';
    _stsInfo = StsData.fromJson(json.decode(awsText));
    String endpoint = '${_stsInfo?.bucket}.${_stsInfo?.endPoint}';
    AppLogger.d('upload _stsInfo:${_stsInfo?.toJson()}');

    // String endpoint = '47.236.147.223';
    _minio = Minio(
        endPoint: endpoint,
        accessKey: _stsInfo!.accessKeyId ?? '',
        secretKey: _stsInfo!.accessKeySecret ?? '',
        sessionToken: _stsInfo!.sessionToken ?? '',
        region: _stsInfo!.regionId ?? '',
        dataTimeCallback: (d) {
          return TimeTask.instance.getNowDateTime();
        },
        enableTrace: true,
        // useSSL: false,
        // port: 14201
    );
  }
  Future<String> uploadTest(String objectName, String filePath,
      {bool isLastDir = false}) async {
    AppLogger.d('upload _minio:$_minio');

    // if (_minio == null) {
    //   return AwsProvider().upload(objectName, filePath);
    // }

    try {
      String bucket = _stsInfo!.bucket ?? '';
      var b = '${_stsInfo!.dir}${getTemBucket()}';
      String object =
          '${isLastDir ? Config.lastMinioDir : b}/$objectName';
      AppLogger.d('upload object:$filePath');
      AppLogger.d('upload filePath:${File(filePath).existsSync()}');

      await _minio!.fPutObject(bucket, object, filePath);

      String url = 'https://${_minio!.endPoint}/$bucket/$object';
      AppLogger.d('upload url:$url');
      return url;
    } catch (e, s) {
      AppLogger.e('Exception details:\n$e');
      AppLogger.e('Stack trace:\n$s');

    }
    return '';
  }
}
