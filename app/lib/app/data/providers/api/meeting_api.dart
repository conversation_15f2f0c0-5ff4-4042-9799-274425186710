import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:flutter_metatel/app/data/models/res/meeting/base_res_model.dart';
import 'package:flutter_metatel/core/utils/util.dart';
import 'package:flutter_metatel/core/values/meeting.dart';
import 'package:get/get.dart' hide Response;

import '../../../../core/utils/app_log.dart';
import '../../../../core/utils/dio/dio_util.dart';
import '../../../../core/values/config.dart';
import '../../models/meeting.dart';
import '../../models/res/meeting/meeting_res_model.dart';
import '../../services/config_service.dart';

/// 会议相关接口
class MeetingApi {
  static final _key =
      "A5ty4gCo4GRyuhW6wBlK5SV4pJFhqa84o18q3Y7HXDycSIi17yFpCElv0dqvWV1I";

  static final _baseUrl = Config.meetingApiBaseUrl();

  /// 获取Upcoming会议列表请求
  static Future<List<Meeting>?> getUpcomingMeetings() async {
    Response response;
    List<Meeting>? models;
    try {
      final userIdWithoutDomain =
          Get.find<AppConfigService>().getUserNameWithoutDomain();
      var data = {
        "userID": userIdWithoutDomain, // LINKSAY ID, 不要有节点名称在内.
        "checkCode": dataMD5(
          userIdWithoutDomain + _key, // MD5(userID+key)
        ),
      };
      // 调用接口
      response = await DioUtil().post(
        _baseUrl,
        MeetingApiUrl.getUpcomingMeetings,
        data: data,
      );
      // 对response进行解析
      var resModel = MeetingResModel.fromJson(json.decode(response.data));
      if (resModel.status != true) {
        AppLogger.e('接口status false, msg:${resModel.msg}');
        return null;
      }
      models = resModel.data;
      return models;
    } catch (e) {
      if (!(e is DioException)) {
        // 只显示除DioException外的Exception, DioException由拦截器处理
        AppLogger.e("Error Catch<getUpcomingMeetings>: $e");
      }
      return null;
    }
  }

  /// 获取Past会议列表请求
  static Future<List<Meeting>?> getPastMeetings() async {
    Response response;
    List<Meeting>? models;
    try {
      final userIdWithoutDomain =
          Get.find<AppConfigService>().getUserNameWithoutDomain();
      var data = {
        "userID": userIdWithoutDomain, // LINKSAY ID, 不要有节点名称在内.
        "checkCode": dataMD5(
          userIdWithoutDomain + _key, // MD5(userID+key)
        ),
      };
      // 调用接口
      response = await DioUtil().post(
        _baseUrl,
        MeetingApiUrl.getPastMeetings,
        data: data,
      );
      // 对response进行解析
      var resModel = MeetingResModel.fromJson(json.decode(response.data));
      if (resModel.status != true) {
        AppLogger.e('接口status false, msg:${resModel.msg}');
        return null;
      }
      models = resModel.data;
      return models;
    } catch (e) {
      if (!(e is DioException)) {
        // 只显示除DioException外的Exception, DioException由拦截器处理
        AppLogger.e("Error Catch<getPastMeetings>: $e");
      }
      return null;
    }
  }

  /// 创建新会议
  static Future<bool?> createMeeting({required Meeting meeting}) async {
    Response response;
    final userIdWithoutDomain =
        Get.find<AppConfigService>().getUserNameWithoutDomain();
    final duration = getDurationInMinute(meeting.startTime, meeting.endTime);
    try {
      var data = {
        "userID": userIdWithoutDomain, // LINKSAY ID, 不要有节点名称在内.
        "userFullName":
            Get.find<AppConfigService>().getMySelfDisplayName(), //  创建者名字
        "title": meeting.title, // 会议标题
        "description": meeting.description, // 会议内容
        "startTime":
            meeting.startTime, // String: format: YYYY-MM-DD HH:MM:SS 不能超过 10 天
        "duration": duration, // int: n分钟
        "passCode": meeting.passcode, // 会议密码
        "checkCode": dataMD5(
          userIdWithoutDomain +
              meeting.startTime! +
              "$duration" +
              (meeting.passcode ?? '') +
              _key,
        ),
        "anyUserCanOpenMeeting": "${meeting.isAnyUserCanOpenMeeting}",
      };
      // 调用接口
      response = await DioUtil().post(
        _baseUrl,
        MeetingApiUrl.createMeeting,
        data: data,
      );
      // 对response进行解析
      var resModel = BaseResModel.fromJson(json.decode(response.data));
      if (resModel.status != true) {
        AppLogger.e('接口status false, msg:${resModel.msg}');
        if (resModel.msg != null) {
          toast(resModel.msg!);
        }
        return false;
      }
      return true;
    } catch (e) {
      if (!(e is DioException)) {
        // 只显示除DioException外的Exception, DioException由拦截器处理
        AppLogger.e("Error Catch<createMeeting>: $e");
      }
      return null;
    }
  }

  /// 编辑会议
  static Future<bool?> updateMeeting({required Meeting meeting}) async {
    Response response;
    final userIdWithoutDomain =
        Get.find<AppConfigService>().getUserNameWithoutDomain();
    final duration = getDurationInMinute(meeting.startTime, meeting.endTime);
    try {
      var data = {
        "id": meeting.id,
        "userID": userIdWithoutDomain, // LINKSAY ID, 不要有节点名称在内.
        "userFullName":
            Get.find<AppConfigService>().getMySelfDisplayName(), //  创建者名字
        "title": meeting.title, // 会议标题
        "description": meeting.description, // 会议内容
        "startTime":
            meeting.startTime, // String: format: YYYY-MM-DD HH:MM:SS 不能超过 10 天
        "duration": duration, // int: n分钟
        "passCode": meeting.passcode, // 会议密码
        "checkCode": dataMD5(
          userIdWithoutDomain +
              meeting.startTime! +
              "$duration" +
              (meeting.passcode ?? '') +
              _key, // MD5(userID+start_time+duration+passCode+key)
        ),
        "anyUserCanOpenMeeting": "${meeting.isAnyUserCanOpenMeeting}",
      };
      // 调用接口
      response = await DioUtil().post(
        _baseUrl,
        MeetingApiUrl.updateMeeting,
        data: data,
      );
      // 对response进行解析
      var resModel = BaseResModel.fromJson(json.decode(response.data));
      if (resModel.status != true) {
        AppLogger.e('接口status false, msg:${resModel.msg}');
        if (resModel.msg != null) {
          toast(resModel.msg!);
        }
        return false;
      }
      return true;
    } catch (e) {
      if (!(e is DioException)) {
        // 只显示除DioException外的Exception, DioException由拦截器处理
        AppLogger.e("Error Catch<updateMeeting>: $e");
      }
      return null;
    }
  }

  /// 取消会议
  static Future<bool?> cancelMeeting({required String? meetingId}) async {
    Response response;
    final userIdWithoutDomain =
        Get.find<AppConfigService>().getUserNameWithoutDomain();
    try {
      var data = {
        "id": meetingId,
        "userID": userIdWithoutDomain, // LINKSAY ID, 不要有节点名称在内.
        "checkCode": dataMD5(userIdWithoutDomain +
            (meetingId ?? '') +
            _key), //MD5(userID+meetingID+key)
      };
      // 调用接口
      response = await DioUtil().post(
        _baseUrl,
        MeetingApiUrl.cancelMeeting,
        data: data,
      );
      // 对response进行解析
      var resModel = BaseResModel.fromJson(json.decode(response.data));
      if (resModel.status != true) {
        AppLogger.e('接口status false, msg:${resModel.msg}');
        if (resModel.msg != null) {
          toast(resModel.msg!);
        }
        return false;
      }
      return true;
    } catch (e) {
      if (!(e is DioException)) {
        // 只显示除DioException外的Exception, DioException由拦截器处理
        AppLogger.e("Error Catch<cancelMeeting>: $e");
      }
      return null;
    }
  }
}
