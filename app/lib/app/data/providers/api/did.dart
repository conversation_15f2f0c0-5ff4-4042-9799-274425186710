import 'dart:convert';

import 'package:flutter_metatel/app/data/providers/api/api.dart';
import 'package:flutter_metatel/app/data/providers/api/base_dio_api.dart';
import 'package:flutter_metatel/core/utils/proxy_util.dart';
import 'package:get/get.dart';
import '../../../../core/utils/app_log.dart';
import '../../../../core/utils/local_proxy_req_util.dart';
import '../../../modules/mining/mining_pwd_util.dart';
import '../../models/res/did/CreateInfo.dart';
import '../../models/res/did/check_devstatus_res.dart';
import '../../models/res/did/seartch_did_info.dart';
import '../../models/res/did/seartch_sbt_info.dart';
import '../../models/res/mining/repurchase_model.dart';
import '../../services/config_service.dart';

class DidApi extends BaseDioClient {
  static String apiUrl = ''; //"http://***************:3092";
  static const createDid = '/didserver/createbyDevExt'; //创建DID
  static const searchSbt = '/didserver/getSbtByAccount'; //通过账号查询sbt
  static const searchAccount = '/didserver/getAccountInfobySbt'; //通过sbt模糊查询通讯账号
  static const checkDevstatus = '/didserver/checkDevstatus'; //查询权益
  static const swapAddSwap = '/didserver/swap/AddSwap'; //申请兑换
  static const getMaxSwapAmount = '/didserver/swap/GetMaxSwapAmount'; //本周期最大可兑换3T数量
  static const getSwapAmount = '/didserver/swap/GetSwapAmount'; //已经兑换3T数量
  static const GetSwapData = '/didserver/swap/GetSwapData'; //查询兑换记录

  List<String>? hostingUrls;


  getApiUrl() {
    AppLogger.d('DidApi apiUrl=$apiUrl');
    return  apiUrl;
    // return "http://***************:3092";
  }

  Future<Response<CreateInfo>> createDidApi(Map body) async {
    if (getApiUrl().isEmpty) {
      return const Response(
        statusCode: -100,
        body: null,
      );
    }
    var user = Get.find<AppConfigService>().getUserNameWithoutDomain();
    AppLogger.d('createDidApi body=$body');
    String sigdata =
        await MiningPwdUtil.encryptForData(json.encode(body)) ?? '';
    Map<String, dynamic> query = {'account': user, 'info': sigdata};
    AppLogger.d('createDidApi query=$query');

    var url = getApiUrl() + createDid;
    CreateInfo? data;
    int? statusCode;
    String? statusText;
    if (isLocalAgent()) {
      String? resData = await LocalAgentUtil().runPoxyRequest(url,  map: query);
      if (resData == null) {
        statusCode = 500;
        statusText = 'error';
      } else {
        if (resData.isNotEmpty) {
          data = CreateInfo.fromJson(json.decode(resData));
        }
      }
    } else {
      try {
        var response = await createDio().post(
          url,
          data : query,
        );
        AppLogger.d('createDidApi body =${response.data}');
        if (response.statusCode == 200) {
          data = CreateInfo.fromJson(response.data);
        }
      } catch(e){
        AppLogger.e('createDidApi e =${e.toString()}');

      }

    }
    /////

    return Response(
      statusCode: statusCode,
      statusText: statusText,
      body: data,
    );
  }

  Future<Response<CheckDevstatusRes>> checkDidDevstatus(Map body) async {
    if (getApiUrl().isEmpty) {
      return const Response(
        statusCode: -100,
        body: null,
      );
    }
    var user = Get.find<AppConfigService>().getUserNameWithoutDomain();
    String sigdata =
        await MiningPwdUtil.encryptForData(json.encode(body)) ?? '';
    Map<String, dynamic> query = {'account': user, 'info': sigdata};

    /////
    var url = getApiUrl() + checkDevstatus;
    CheckDevstatusRes? data;
    int? statusCode;
    String? statusText;
    if (isLocalAgent()) {
      String? resData = await LocalAgentUtil().runPoxyRequest(url, map: query);
      if (resData == null) {
        statusCode = 500;
        statusText = 'error';
      } else {
        if (resData.isNotEmpty) {
          data = CheckDevstatusRes.fromJson(json.decode(resData));
        }
      }
    } else {
       try {
         var response = await createDio().post(
           url,
           data : query,
         );
         AppLogger.d('createDidApi body =${response.data}');
         if (response.statusCode == 200) {
           data = CheckDevstatusRes.fromJson(response.data);
         }
       } catch(e){
         AppLogger.e('createDidApi e =${e.toString()}');

       }

    }
    /////
    return Response(
      statusCode: statusCode,
      statusText: statusText,
      body: data,
    );
  }

  Future<Response<SearchDidInfo>> searchSbtDidByAccount(Map query) async {
    if (getApiUrl().isEmpty) {
      return const Response(
        statusCode: -100,
        body: null,
      );
    }
    /////
    var url = getApiUrl() + searchSbt;
    SearchDidInfo? data;
    int? statusCode;
    String? statusText;
    AppLogger.d('searchSbtDidByAccount url =$url');

    if (isLocalAgent()) {
      String? resData = await LocalAgentUtil().runPoxyRequest(url, map: query);
      if (resData == null) {
        statusCode = 500;
        statusText = 'error';
      } else {
        if (resData.isNotEmpty) {
          data = SearchDidInfo.fromJson(json.decode(resData));
        }
      }
    } else {
      try {
        var response = await createDio().post(
          url,
          data : query,
        );
        AppLogger.d('searchSbtDidByAccount body =${response.data}');
        if (response.statusCode == 200) {
          data = SearchDidInfo.fromJson(response.data);
        }
      } catch (e){
        AppLogger.e('searchSbtDidByAccount e =${e.toString()}');

      }

    }
    /////
    return Response(
      statusCode: statusCode,
      statusText: statusText,
      body: data,
    );
  }

  Future<bool?> findTidByAccount(String userName) async {
    if (getApiUrl().isEmpty) {
      return null;
    }
    if (userName.contains('@')) {
      userName = userName.substring(0, userName.indexOf('@'));
    }
    var query = {'account': userName};
    ///////
    var url = getApiUrl() + searchSbt;
    SearchDidInfo? data;
    bool? haveTid;
    if (isLocalAgent()) {
      String? resData = await LocalAgentUtil().runPoxyRequest(url, map: query);
      if (resData?.isNotEmpty ?? false) {
        if (resData!.isNotEmpty) {
          data = SearchDidInfo.fromJson(json.decode(resData));
        }
      }
    } else {
      try{
        AppLogger.d('findTidByAccount url =$url query=$query');

        var response = await createDio().post(
          url,
          data : query,
        );
        AppLogger.d('findTidByAccount body =${response.data}');
        if (response.statusCode == 200) {
          data = SearchDidInfo.fromJson(response.data);
        }
      } catch (e){
        AppLogger.e('findTidByAccount e =${e.toString()}');

      }

    }
    if (data != null && data.code == 200) {
      var list = data.data;
      if (list?.isNotEmpty ?? false) {
        if (list?.isNotEmpty ?? false) {
          list?.removeWhere((element) {
            var s = (element.status ?? 0) >= 90;
            return s;
          });
        }
        haveTid = list?.isNotEmpty ?? false;
      } else {
        haveTid = false;
      }
    }
    ///////
    AppLogger.d('findTidByAccount haveTid =$haveTid');
    return haveTid;
  }

  Future<Response<SearchSbtDidInfo>> searchAccountBySbt(Map query) async {
    if (getApiUrl().isEmpty) {
      return const Response(
        statusCode: -100,
        body: null,
      );
    }
    var url = getApiUrl() + searchAccount;
    SearchSbtDidInfo? data;
    int? statusCode;
    String? statusText;
    if (isLocalAgent()) {
      String? resData = await LocalAgentUtil().runPoxyRequest(url, map:query);
      if (resData == null) {
        statusCode = 500;
        statusText = 'error';
      } else {
        if (resData.isNotEmpty) {
          data = SearchSbtDidInfo.fromJson(json.decode(resData));
        }
      }
    } else {
      try {
        var response = await createDio().post(
          url,
          data : query,
        );
        AppLogger.d('findTidByAccount body =${response.data}');
        if (response.statusCode == 200) {
          data = SearchSbtDidInfo.fromJson(response.data);
        }
      } catch(e){
        AppLogger.e('findTidByAccount e =${e.toString()}');

      }

    }

    return Response(
      statusCode: statusCode,
      statusText: statusText,
      body: data,
    );
  }

  //申请兑换
  Future<Response<AddSwapInfo>> swapAddSwapApi(Map query) async {
    if (getApiUrl().isEmpty) {
      return const Response(
        statusCode: -100,
        body: null,
      );
    }
    var url = getApiUrl() + swapAddSwap;
    AddSwapInfo? data;
    int? statusCode;
    String? statusText;
    if (isLocalAgent()) {
      String? resData = await LocalAgentUtil().runPoxyRequest(url, map: query);
      if (resData == null) {
        statusCode = 500;
        statusText = 'error';
      } else {
        if (resData.isNotEmpty) {
          data = AddSwapInfo.fromJson(json.decode(resData));
        }
      }
    } else {
       try{
         var response = await createDio().post(
           url,
           data : query,
         );
         AppLogger.d('swapAddSwapApi body =${response.data}');
         if (response.statusCode == 200) {
           data = AddSwapInfo.fromJson(response.data);
         }
       } catch (e){
         AppLogger.e('createDidApi e =${e.toString()}');

       }

    }

    return Response(
      statusCode: statusCode,
      statusText: statusText,
      body: data,
    );
  }

  //本周期最大可兑换3T数量
  Future<Response<GetSwapAmountInfo>> getMaxSwapAmountApi(Map query) async {
    if (getApiUrl().isEmpty) {
      return const Response(
        statusCode: -100,
        body: null,
      );
    }
    var url = getApiUrl() + getMaxSwapAmount;
    GetSwapAmountInfo? data;
    int? statusCode;
    String? statusText;
    if (isLocalAgent()) {
      String? resData = await LocalAgentUtil().runPoxyRequest(url, map: query);
      if (resData == null) {
        statusCode = 500;
        statusText = 'error';
      } else {
        if (resData.isNotEmpty) {
          data = GetSwapAmountInfo.fromJson(json.decode(resData));
        }
      }
    } else {
       try{
         var response = await createDio().post(
           url,
           data : query,
         );
         AppLogger.d('getSwapAmountApi body =${response.data}');
         if (response.statusCode == 200) {
           data = GetSwapAmountInfo.fromJson(response.data);
         }
       } catch(e){
         AppLogger.e('getSwapAmountApi e =${e.toString()}');

       }

    }

    return Response(
      statusCode: statusCode,
      statusText: statusText,
      body: data,
    );
  }


  //查询兑换记录
  Future<Response<GetSwapInfo>> getSwapDataApi(Map query) async {
    if (getApiUrl().isEmpty) {
      return const Response(
        statusCode: -100,
        body: null,
      );
    }
    var url = getApiUrl() + GetSwapData;
    GetSwapInfo? data;
    int? statusCode;
    String? statusText;
    if (isLocalAgent()) {
      String? resData = await LocalAgentUtil().runPoxyRequest(url, map: query);
      if (resData == null) {
        statusCode = 500;
        statusText = 'error';
      } else {
        if (resData.isNotEmpty) {
          data = GetSwapInfo.fromJson(json.decode(resData));
        }
      }
    } else {
      try {
        var response = await createDio().post(
          url,
          data : query,
        );
        AppLogger.d('getSwapDataApi body =${response.data}');
        if (response.statusCode == 200) {
          data = GetSwapInfo.fromJson(response.data);
        }
      } catch(e){
        AppLogger.e('getSwapDataApi e =${e.toString()}');
      }

    }

    return Response(
      statusCode: statusCode,
      statusText: statusText,
      body: data,
    );
  }
  bool isLocalAgent() {
    var agent = Get.find<AppConfigService>().getLocalAgent();
    AppLogger.d('isLocalAgent =$isLocalAgent');
    return  false;//agent;
  }
}
