/*
 * @Author: your name
 * @Date: 2022-04-22 11:13:35
 * @LastEditTime : 2022-05-09 15:05:29
 * @LastEditors  : Daen
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath     : /flutter_metatel/lib/app/data/providers/native/chatio/auth/isolate.dart
 */

import 'dart:isolate';

import '../../isolate_data_channel.dart';
import 'auth.dart';

late final Auth _gAuth;
late final SendPort _gRootSendPort;

class IsolateEvent {
  static String createAuthHandle = 'createAuthHandle';
  static String getToken = 'getToken';
  static String getPrivateKey = 'getPrivateKey';
  static String destroy = 'destroy';
}

/// V2 参数
class CreateAuthParameter {
  CreateAuthParameter(
    this.privateBs64,
    this.phone,
    this.deviceInfo,
    this.authUrl,
    this.domain,
  );

  final String authUrl;
  final String privateBs64;
  final String domain;
  final String phone;
  final String deviceInfo;
}

/// 创建Endpoint隔离区
void createIsolate(dynamic isolateData) {
  ReceivePort newIsolateReceivePort = ReceivePort();
  newIsolateReceivePort.listen(messageParse);

  _gRootSendPort = isolateData as SendPort;
  _gAuth = Auth();

  // 把当前的sendPort发送到主Isolate中
  IsolateMessageEvent data =
      IsolateMessageEvent(sendPortResponse, '', newIsolateReceivePort.sendPort);
  _gRootSendPort.send(data);
}

/// 消息解析
void messageParse(dynamic message) async {
  var event = message as IsolateMessageEvent;

  Object? resultData;
  if (event.action == IsolateEvent.createAuthHandle) {
    var data = event.data as CreateAuthParameter;
    resultData = _gAuth.createAuthHandle(data.authUrl, data.privateBs64,
        data.domain, data.phone, data.deviceInfo);
  } //
  else if (event.action == IsolateEvent.getToken) {
    resultData = _gAuth.getToken(event.data as String);
  } //
  else if (event.action == IsolateEvent.getPrivateKey) {
    resultData = _gAuth.getPrivateKey();
  } //
  else if (event.action == IsolateEvent.destroy) {
    _gAuth.destroy();
  }

  IsolateMessageEvent sendData =
      IsolateMessageEvent(event.action, event.uuid, resultData);
  _gRootSendPort.send(sendData);
}
