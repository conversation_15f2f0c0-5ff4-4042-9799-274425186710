/*
 * @Author: your name
 * @Date: 2022-04-22 10:44:09
 * @LastEditTime : 2022-06-02 17:06:55
 * @LastEditors  : Daen
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath     : /flutter_metatel/lib/app/data/providers/native/chatio/auth/auth.dart
 */
import 'dart:convert';
import 'dart:ffi';

import 'package:ffi/ffi.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';

import '../chatio_generated_bindings.dart';
import '../../native_library.dart';

class Auth {
  Auth() {
    _init();
  }

  late final ChatIO _chatio;
  ChatIOAuthPtr _handle = nullptr;

  void _init() {
    _chatio = ChatIO(chatioNativeLibrary());
    _chatio.FFI_Util_Logger(chatioLogLevel);
  }

  bool createAuthHandle(String url, String privateKey, String domain,
      String phone, String deviceInfo) {
    var ptrProxy = ''.toNativeUtf8();

    var ptrUrl = url.toNativeUtf8();
    var ptrPhone = phone.toNativeUtf8();
    var ptrDomain = domain.toNativeUtf8();
    var ptrDeviceInfo = deviceInfo.toNativeUtf8();
    Pointer<Uint8> ptrPrivatekey = calloc<Uint8>(33);

    var privateKeyByte = base64.decode(privateKey);
    for (var i = 0; i < 32; i++) {
      ptrPrivatekey[i] = privateKeyByte[i];
    }

    try {
      _handle = _chatio.FFI_AuthV2_Create(ptrProxy.cast(),ptrUrl.cast(), ptrPrivatekey.cast(),
          ptrDomain.cast(), ptrPhone.cast(), ptrDeviceInfo.cast());
    } catch (e, s) {
      AppLogger.d('$e');
      AppLogger.d('$s');
    }

    _freePtr(ptrUrl);
    _freePtr(ptrPhone);
    _freePtr(ptrDomain);
    _freePtr(ptrDeviceInfo);
    _freePtr(ptrPrivatekey);
    _freePtr(ptrProxy);
    return _handle == nullptr ? false : true;
  }


  String getToken(String msgCode) {
    String result = '';
    if (_handle == nullptr) {
      return result;
    }

    var ptrMsgCode = msgCode.toNativeUtf8();

    try {
      var ptrResult = _chatio.FFI_AuthV2_GetToken(_handle, ptrMsgCode.cast());
      if (ptrResult != nullptr) {
        result = ptrResult.cast<Utf8>().toDartString();
        _chatio.FFI_Util_Free(ptrResult);
      }
    } catch (e, s) {
      AppLogger.d('$e');
      AppLogger.d('$s');
    }
    _freePtr(ptrMsgCode);

    return result;
  }

  String getPrivateKey() {
    String result = '';
    if (_handle == nullptr) {
      return result;
    }

    Pointer<Uint8> ptrResPrivateKey = calloc<Uint8>(33);

    try {
      var value =
          _chatio.FFI_AuthV2_PrivateKey(_handle, ptrResPrivateKey.cast());
      if (value == 0) {
        result = base64.encode(ptrResPrivateKey.asTypedList(32));
      }
    } catch (e, s) {
      AppLogger.d('$e');
      AppLogger.d('$s');
    }
    _freePtr(ptrResPrivateKey);

    return result;
  }

  /// 销毁句柄
  void destroy() {
    if (_handle == nullptr) {
      return;
    }
    _chatio.FFI_AuthV2_Delete(_handle);
  }

  /// 指针释放
  void _freePtr(Pointer ptr) {
    if (ptr != nullptr) {
      calloc.free(ptr);
    }
  }
}
