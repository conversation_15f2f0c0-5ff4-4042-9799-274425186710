/*
 * @Author: your name
 * @Date: 2022-04-22 11:36:18
 * @LastEditTime : 2022-05-09 15:05:32
 * @LastEditors  : Daen
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath     : /flutter_metatel/lib/app/data/providers/native/chatio/auth/auth_ffi.dart
 */

import '../../isolate_data_channel.dart';
import 'isolate.dart';

class AuthFFi {
  AuthFFi() {
    _init();
  }
  final IsolateDataChannel _channel = IsolateDataChannel('auth_channel');

  void _init() {
    _channel.createNewIsolate(createIsolate, _channel.rootSendPort);
  }

  Future<bool> createAuthHandle(String authUrl, String privateBs64,
      String domain, String phone, String deviceInfo) async {
    dynamic result = await _channel.sendData(IsolateEvent.createAuthHandle,
        CreateAuthParameter(privateBs64, phone, deviceInfo, authUrl, domain));
    return result;
  }

  Future<String> getToken(String msgCode) async {
    dynamic result = await _channel.sendData(IsolateEvent.getToken, msgCode);
    return result;
  }

  Future<String> getPrivateKey() async {
    dynamic result = await _channel.sendData(IsolateEvent.getPrivateKey, '');
    return result;
  }

  Future<void> destroy() async {
    await _channel.sendData(IsolateEvent.destroy, '');
    _channel.destroyNewIsolate();
  }
}
