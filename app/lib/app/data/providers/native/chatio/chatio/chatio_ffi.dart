/*
 * @Author: your name
 * @Date: 2022-04-22 11:36:18
 * @LastEditTime : 2022-06-02 17:13:39
 * @LastEditors  : Daen
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath     : /flutter_metatel/lib/app/data/providers/native/chatio/chatio/chatio_ffi.dart
 */

import 'package:flutter/foundation.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:get/get.dart';

import '../../isolate_data_channel.dart';
import 'isolate.dart';

class ChatioFFI {
  ChatioFFI({
    this.receiveMessageCallBack,
    this.messageErrorCallBack,
    this.unauthorizedCallBack,
    this.receiveMsgNumberCallBack,
  }) {
    countRecv.value = 0;
    _init();
  }

  final IsolateDataChannel _channel = IsolateDataChannel('chatio_channel');

  final ValueChanged<dynamic>? receiveMessageCallBack;
  final ValueChanged<dynamic>? messageErrorCallBack;
  final VoidCallback? unauthorizedCallBack;
  final ValueChanged<int>? receiveMsgNumberCallBack;
  late RxInt countRecv = 0.obs;

  void _init() {
    _channel.createNewIsolate(createIsolate, _channel.rootSendPort);

    _channel.setNewIsolateMessageCallback((message) {
      IsolateMessageEvent event = message as IsolateMessageEvent;
      // 接收到的消息
      if (IsolateEvent.receiveMessageCallBack == event.action) {
        receiveMessageCallBack?.call(event.data);
      }
      // 未授权
      else if (IsolateEvent.unauthorizedCallBack == event.action) {
        unauthorizedCallBack?.call();
      }
      // 消息错误
      else if (IsolateEvent.messageErrorCallBack == event.action) {
        messageErrorCallBack?.call(event.data);
      }
    });

    interval(countRecv, (newValue) {
      _recv();
    }, time: const Duration(seconds: 1));
  }

  Future<bool> connect(String privateKeyBs64, String token, String dataPath,
      String serverUrl, String domain) async {
    AppLogger.d("connect url==$serverUrl domain==$domain");
    dynamic result = await _channel.sendData(IsolateEvent.connect,
        ConnectParameter(privateKeyBs64, token, dataPath, serverUrl, domain));
    return result;
  }

  Future<String> getDeviceName() async {
    dynamic result = await _channel.sendData(IsolateEvent.getDeviceName, '');
    return result;
  }

  Future<int> cleanSession(String name, int deviceID) async {
    return await _channel.sendData(
        IsolateEvent.destroy, CleanSessionParameter(name, deviceID));
  }

  Future<String> sendMessage(List<String> toNumbers, String message,
      {int pushType = 0}) async {
    dynamic result = await _channel.sendData(IsolateEvent.sendMessage,
        SendMessageParameter(toNumbers, message, pushType));
    return result;
  }

  void recvMessage() {
    countRecv++;
    return;
  }

  /// 立即接收
  void recvMessageNow() {
    _recv();
  }

  Future<void> destroy() async {
    await _channel.sendData(IsolateEvent.destroy, '');
    _channel.destroyNewIsolate();
  }

  Future<void> _recv() async {
    int loop = 5;
    while (loop-- > 0) {
      int result = await _channel.sendData(IsolateEvent.recvMessage, '');
      receiveMsgNumberCallBack?.call(result);
      if (result <= 20) {
        break;
      }
    }
  }
}
