/*
 * @Author: your name
 * @Date: 2022-04-22 11:13:35
 * @LastEditTime : 2022-06-08 15:00:18
 * @LastEditors  : Daen
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath     : /flutter_metatel/lib/app/data/providers/native/chatio/chatio/isolate.dart
 */

import 'dart:convert';
import 'dart:ffi';
import 'dart:isolate';

import 'package:ffi/ffi.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_metatel/app/data/models/chatio_msg_model.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';

import '../../isolate_data_channel.dart';
import 'chatio.dart';

late final Chatio _gChatio;
late final SendPort _gRootSendPort;

class IsolateEvent {
  static String connect = 'connect';
  static String getDeviceName = 'getDeviceName';
  static String sendMessage = 'sendMessage';
  static String recvMessage = 'recvMessage';
  static String destroy = 'destroy';

  /// 主动回调
  static String unauthorizedCallBack = 'unauthorizedCallBack';
  static String messageErrorCallBack = 'messageErrorCallBack';
  static String receiveMessageCallBack = 'receiveMessageCallBack';
}

/// 连接参数
class ConnectParameter {
  ConnectParameter(
    this.privateBs64,
    this.token,
    this.dataPath,
    this.serverUrl,
    this.domain,
  );

  final String privateBs64;
  final String token;
  final String dataPath;
  final String serverUrl;
  final String domain;
}

/// 清除会话参数
class CleanSessionParameter {
  CleanSessionParameter(
    this.name,
    this.deviceId,
  );

  final String name;
  final int deviceId;
}

/// 发送消息参数
class SendMessageParameter {
  SendMessageParameter(this.toNumbers, this.message, this.pushType);
  final List<String> toNumbers;
  final String message;
  final int pushType;
}

/// 创建Endpoint隔离区
void createIsolate(dynamic isolateData) {
  ReceivePort newIsolateReceivePort = ReceivePort();
  newIsolateReceivePort.listen(messageParse);

  _gRootSendPort = isolateData as SendPort;
  _gChatio = Chatio();

  // 把当前的sendPort发送到主Isolate中
  IsolateMessageEvent data =
      IsolateMessageEvent(sendPortResponse, '', newIsolateReceivePort.sendPort);
  _gRootSendPort.send(data);
}

/// 消息解析
void messageParse(dynamic message) async {
  var event = message as IsolateMessageEvent;

  Object? resultData;
  if (event.action == IsolateEvent.connect) {
    var data = event.data as ConnectParameter;
    resultData = _gChatio.connect(
      data.privateBs64,
      data.token,
      data.dataPath,
      data.serverUrl,
      data.domain,
    );
  } //
  else if (event.action == IsolateEvent.getDeviceName) {
    resultData = _gChatio.getDeviceName();
  } //
  else if (event.action == IsolateEvent.sendMessage) {
    var data = event.data as SendMessageParameter;
    resultData =
        _gChatio.sendMessage(data.toNumbers, data.message, data.pushType);
  } //
  else if (event.action == IsolateEvent.recvMessage) {
    resultData = _gChatio.recvMessage();
  } //
  else if (event.action == IsolateEvent.destroy) {
    _gChatio.destroy();
  }

  IsolateMessageEvent sendData =
      IsolateMessageEvent(event.action, event.uuid, resultData);
  _gRootSendPort.send(sendData);
}

/// 消息回调
bool messageCallBack(Pointer<Uint8> ptrMessage, int messageLen,
    Pointer<Char> ptrFrom, Pointer<Char> ptrID) {
  String message = utf8.decode(ptrMessage.asTypedList(messageLen));
  String from = ptrFrom.cast<Utf8>().toDartString();
  String id = ptrID.cast<Utf8>().toDartString();
  AppLogger.d('messageCallBack: from:$from  message:$message  id:$id');
  ChatioMessageModel model =
      ChatioMessageModel(from: from, message: message, id: id);

  IsolateMessageEvent data =
      IsolateMessageEvent(IsolateEvent.receiveMessageCallBack, '', model);
  _gRootSendPort.send(data);

  return true;
}

/// 消息错误回调
bool messageErrorCallBack(
    Pointer<Char> ptrFromName, int fromDeviceID, Pointer<Char> ptrID) {
  String fromName = ptrFromName.cast<Utf8>().toDartString();
  String id = ptrID.cast<Utf8>().toDartString();

  AppLogger.d('messageErrorCallBack: id:$id  from:$fromName');

  ChatioErrorMessageModel model =
      ChatioErrorMessageModel(fromName, fromDeviceID, id);

  IsolateMessageEvent data =
      IsolateMessageEvent(IsolateEvent.messageErrorCallBack, '', model);
  _gRootSendPort.send(data);

  return true;
}

/// 未授权回调
bool unauthorizedCallBack() {
  AppLogger.d('run unauthorizedCallBack()');

  IsolateMessageEvent data =
      IsolateMessageEvent(IsolateEvent.unauthorizedCallBack, '', '');
  _gRootSendPort.send(data);

  return true;
}
