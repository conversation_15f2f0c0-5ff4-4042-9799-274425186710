/*
 * @Author: your name
 * @Date: 2022-04-22 10:44:09
 * @LastEditTime : 2022-06-02 17:09:27
 * @LastEditors  : Daen
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath     : /flutter_metatel/lib/app/data/providers/native/chatio/chatio/chatio.dart
 */
import 'dart:convert';
import 'dart:ffi';

import 'package:ffi/ffi.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';

import '../chatio_generated_bindings.dart';
import '../../native_library.dart';
import 'isolate.dart';

class Chatio {
  Chatio() {
    _init();
  }

  late final ChatIO _chatio;
  ChatIOFfiChatioPtr _handle = nullptr;

  void _init() {
    _chatio = ChatIO(chatioNativeLibrary());
    _chatio.FFI_Util_Logger(chatioLogLevel);
  }

  bool connect(
    String privateBs64,
    String token,
    String dataPath,
    String serverUrl,
    String domain,
  ) {
    destroy();

    Pointer<Utf8> ptrDataPath = dataPath.toNativeUtf8();
    Pointer<Utf8> ptrServerUrl = serverUrl.toNativeUtf8();
    Pointer<Utf8> ptrToken = token.toNativeUtf8();
    Pointer<Uint8> ptrPrivatekey = calloc<Uint8>(33);
    Pointer<Utf8> ptrDomain = domain.toNativeUtf8();

    bool result = false;
    try {
      do {
        var keyBytes = base64.decode(privateBs64);
        for (var i = 0; i < 32; i++) {
          ptrPrivatekey[i] = keyBytes[i];
        }

        // 创建句柄
        _handle = _chatio.FFI_Chatio_New(
            ptrDataPath.cast(),
            ptrServerUrl.cast(),
            ptrPrivatekey.cast(),
            ptrDomain.cast(),
            ptrToken.cast(),
            Pointer.fromFunction(messageCallBack, true),
            Pointer.fromFunction(messageErrorCallBack, true),
            Pointer.fromFunction(unauthorizedCallBack, true));
        if (_handle == nullptr) {
          break;
        }

        // 连接
        int ret = _chatio.FFI_Chatio_Connect(_handle);
        if (ret != 0) {
          break;
        }

        result = true;
      } while (false);
    } catch (e, s) {
      AppLogger.d('$e');
      AppLogger.d('$s');
    }
    _freePtr(ptrDataPath);
    _freePtr(ptrServerUrl);
    _freePtr(ptrToken);
    _freePtr(ptrPrivatekey);
    _freePtr(ptrDomain);

    return result;
  }

  String getDeviceName() {
    String result = '';
    if (_handle == nullptr) {
      return result;
    }

    try {
      var ptrName = _chatio.FFI_Chatio_GetDeviceName(_handle);
      if (ptrName != nullptr) {
        result = ptrName.cast<Utf8>().toDartString();
        _chatio.FFI_Util_Free(ptrName);
      }
    } catch (e, s) {
      AppLogger.d('$e');
      AppLogger.d('$s');
    }

    return result;
  }

  int cleanSession(String name, int deviceID) {
    int result = -1;
    if (_handle == nullptr) {
      return result;
    }

    try {
      result = _chatio.FFI_Chatio_CleanSession(
          _handle, name.toNativeUtf8().cast(), deviceID);
    } catch (e, s) {
      AppLogger.d('$e');
      AppLogger.d('$s');
    }

    return result;
  }

  String sendMessage(List<String> toNumbers, String message, int pushType) {
    String result = '';
    if (_handle == nullptr) {
      return result;
    }

    Pointer<Utf8> ptrMessage = message.toNativeUtf8();
    Pointer<Pointer<Utf8>> ptrToNumbers = calloc(toNumbers.length);
    for (int i = 0; i < toNumbers.length; i++) {
      ptrToNumbers[i] = toNumbers[i].toNativeUtf8();
    }

    try {
      var ptrMsgID = _chatio.FFI_Chatio_SendMessage(
          _handle,
          pushType,
          ptrToNumbers.cast(),
          toNumbers.length,
          ptrMessage.cast<Uint8>(),
          ptrMessage.length);

      if (ptrMsgID != nullptr) {
        result = ptrMsgID.cast<Utf8>().toDartString();
        _chatio.FFI_Util_Free(ptrMsgID);
      }
    } catch (e, s) {
      AppLogger.d('$e');
      AppLogger.d('$s');
    }

    for (int i = 0; i < toNumbers.length; i++) {
      _freePtr(ptrToNumbers[i]);
    }
    _freePtr(ptrToNumbers);
    _freePtr(ptrMessage);

    return result;
  }

  int recvMessage() {
    int result = -1;
    if (_handle == nullptr) {
      return result;
    }

    try {
      result = _chatio.FFI_Chatio_RecvMessage(_handle);
    } catch (e, s) {
      AppLogger.d('$e');
      AppLogger.d('$s');
    }

    return result;
  }

  /// 销毁句柄
  void destroy() {
    if (_handle == nullptr) {
      return;
    }
    _chatio.FFI_Chatio_Delete(_handle);
    _handle = nullptr;
  }

  /// 指针释放
  void _freePtr(Pointer ptr) {
    if (ptr != nullptr) {
      calloc.free(ptr);
    }
  }
}
