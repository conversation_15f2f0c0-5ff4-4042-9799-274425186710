/*
 * <AUTHOR> <PERSON><PERSON>
 * @Date         : 2022-04-22 14:46:54
 * @Description  : chatio 的任务工具类
 * @LastEditors  : <PERSON><PERSON>
 * @LastEditTime : 2022-08-22 12:14:47
 * @FilePath     : /flutter_metatel/lib/app/data/providers/native/chatio/chatio_async.dart
 */

import 'dart:convert';
import 'dart:ffi';

import 'package:drift/drift.dart';
import 'package:ffi/ffi.dart' show StringUtf8Pointer, Utf8, Utf8Pointer, calloc;
import 'package:flutter_metatel/core/utils/util.dart';
import 'package:worker_manager/worker_manager.dart';

import '../../../../../core/utils/app_log.dart';
import '../native_library.dart';
import 'chatio_generated_bindings.dart';

class ChatioNative {
  static Future<String?> authV1GetToken(String authUrl, String deviceInfo,
      String privateKeyBs64, String domain) async {
    try {
      var token = await Executor()
          .execute(
              arg1: authUrl,
              arg2: deviceInfo,
              arg3: privateKeyBs64,
              arg4: domain,
              fun4: _authV1GetToken)
          .then((value) => value, onError: (e) => {throw e});
      return token;
    } catch (e) {
      AppLogger.e(e.toString());
    }
    return null;
  }

  ///action=1顶替其他设备上线
  static Future<String?> authV3GetToken(String authUrl, String extendData,
      String deviceCode, String privateKeyBs64, int action) async {
    try {
      var arg = _AuthV3GetTokenArgs(
          authUrl: authUrl,
          extendData: extendData,
          deviceCode: deviceCode,
          privateKeyBs64: privateKeyBs64,
          action: action);
      var token = await Executor()
          .execute(arg1: arg, fun1: _authV3GetToken)
          .then((value) => value, onError: (e) => {throw e});
      return token;
    } catch (e) {
      AppLogger.e(e.toString());
      writeLogToCacheFile(e.toString());
    }
    return null;
  }

  static Future<int> authV2QRCodeScan(String authUrl, String uuid,
      String qrcodePublicKeyBs64, Uint8List privateKey, Uint8List data) async {
    try {
      _authV2QRCodeScanArgs args = _authV2QRCodeScanArgs(
        authUrl: authUrl,
        uuid: uuid,
        qrcodePublicKeyBs64: qrcodePublicKeyBs64,
        privateKey: privateKey,
        data: data,
      );
      return await Executor()
          .execute(arg1: args, fun1: _authV2QRCodeScan)
          .then((value) => value, onError: (e) => {throw e});
    } catch (e) {
      AppLogger.e(e.toString());
    }
    return Future.value(-1);
  }

  static Future<int> groupCreate(String inFile, String outFile, Uint8List body,
      Uint8List privateKey, String domain) async {
    _groupCreateCommitArgs args = _groupCreateCommitArgs(
      inFile: inFile,
      outFile: outFile,
      body: body,
      privateKey: privateKey,
      domain: domain,
    );

    try {
      return await Executor()
          .execute(arg1: args, fun1: _groupCreate)
          .then((value) => value, onError: (e) => {throw e});
    } catch (e) {
      AppLogger.e(e.toString());
    }
    return -1;
  }

  static Future<int> groupCommit(String inFile, String outFile, Uint8List body,
      Uint8List privateKey, String domain) async {
    _groupCreateCommitArgs args = _groupCreateCommitArgs(
      inFile: inFile,
      outFile: outFile,
      body: body,
      privateKey: privateKey,
      domain: domain,
    );

    try {
      return await Executor()
          .execute(arg1: args, fun1: _groupCommit)
          .then((value) => value, onError: (e) => {throw e});
    } catch (e) {
      AppLogger.e(e.toString());
    }
    return -1;
  }

  static Future<int> groupMerge(
      String inFile, String mergeFile, String outFile) async {
    try {
      return await Executor()
          .execute(
              arg1: inFile, arg2: mergeFile, arg3: outFile, fun3: _groupMerge)
          .then((value) => value, onError: (e) => {throw e});
    } catch (e) {
      AppLogger.e(e.toString());
    }
    return -1;
  }

  static Future<Uint8List?> utilPrivate(
      String privateKeyBs64, String password) async {
    try {
      return await Executor()
          .execute(arg1: privateKeyBs64, arg2: password, fun2: _utilPrivate)
          .then((value) => value, onError: (e) => {throw e});
    } catch (e) {
      AppLogger.e(e.toString());
    }
    return null;
  }

  static Future<Uint8List?> utilCreatePrivate() async {
    try {
      return await Executor()
          .execute(arg1: 0, fun1: _utilCreatePrivateKey)
          .then((value) => value, onError: (e) => {throw e});
    } catch (e) {
      AppLogger.e(e.toString());
    }
    return null;
  }

  static Future<FfiKeyPair?> utilCreateKeyPair() async {
    try {
      return await Executor().execute(arg1: 0, fun1: _utilCreateKeyPair).then(
            (value) => value,
            onError: (e) => {throw e},
          );
    } catch (e) {
      AppLogger.e(e.toString());
    }
    return null;
  }

  static Future<String?> utilName(Uint8List privateKey, String domain) async {
    try {
      return await Executor()
          .execute(arg1: privateKey, arg2: domain, fun2: _utilName)
          .then((value) => value, onError: (e) => {throw e});
    } catch (e) {
      AppLogger.e(e.toString());
    }
    return null;
  }

  static Future<String?> utilFileEncrypt(String src, String dst) async {
    try {
      return await Executor()
          .execute(arg1: src, arg2: dst, fun2: _utilFileEncrypt)
          .then((value) => value, onError: (e) => {throw e});
    } catch (e) {
      AppLogger.e(e.toString());
    }
    return null;
  }

  static Future<int> utilFileDecrypt(
      String src, String dst, String fragment) async {
    try {
      return await Executor()
          .execute(arg1: src, arg2: dst, arg3: fragment, fun3: _utilFileDecrypt)
          .then((value) => value, onError: (e) => {throw e});
    } catch (e) {
      AppLogger.e(e.toString());
    }
    return -1;
  }

  static Future<Uint8List> utilFileDecryptToMemory(
      String src, String fragment) async {
    try {
      return await Executor()
          .execute(arg1: src, arg2: fragment, fun2: _utilFileDecryptToMemory)
          .then((value) => value, onError: (e) => {throw e});
    } catch (e) {
      AppLogger.e(e.toString());
    }
    return Uint8List(0);
  }

  static Future<String?> utilDisplayableFingeprint(
      String remoteName, Uint8List privateKey) async {
    try {
      return await Executor()
          .execute(
              arg1: remoteName,
              arg2: privateKey,
              fun2: _utilDisplayableFingeprint)
          .then((value) => value, onError: (e) => {throw e});
    } catch (e) {
      AppLogger.e(e.toString());
    }
    return null;
  }

  static Future<String?> utilScannableFingeprint(
      String remoteName, Uint8List privateKey) async {
    try {
      return await Executor()
          .execute(
              arg1: remoteName,
              arg2: privateKey,
              fun2: _utilScannableFingeprint)
          .then((value) => value, onError: (e) => {throw e});
    } catch (e) {
      AppLogger.e(e.toString());
    }
    return null;
  }

  static Future<int> utilScannableFingeprintCompare(
      String remoteName, String combined, Uint8List privateKey) async {
    try {
      return await Executor()
          .execute(
              arg1: remoteName,
              arg2: combined,
              arg3: privateKey,
              fun3: _utilScannableFingeprintCompare)
          .then((value) => value, onError: (e) => {throw e});
    } catch (e) {
      AppLogger.e(e.toString());
    }
    return -1;
  }
  static Future<void>setProxy(String proxy)async{
    try {
       await Executor()
          .execute(
          arg1: proxy,
          fun1: _setProxy)
          .then((value) => value, onError: (e) => {throw e});
    } catch (e) {
      AppLogger.e(e.toString());
    }
  }

// Future<void> utilLogger(int level) async {
//   await Executor().warmUp(log: true, isolatesCount: 2);
//   try {
//     var tt = await Executor().execute(arg1: level, fun1: _utilLogger).next(
//         onValue: (value) => value,
//         onError: (e) => {AppLogger.e(e.toString()), throw e});
//   } catch (e) {
//     AppLogger.e(e.toString());
//   }
//   return;
// }

// int _utilLogger(int level) {
//   print('utilLogger $level');
//   ChatIO ch = ChatIO(chatioNativeLibrary());

//   print('utilLogger $level');
//   return 0;
// }
}

void ffiLoggerCallback(Pointer<Int8> target, int level, Pointer<Int8> file,
    int line, Pointer<Int8> message) {
  AppLogger.d(
      '${target.cast<Utf8>().toDartString()} $level ${file.cast<Utf8>().toDartString()} $line ${message.cast<Utf8>().toDartString()}');
}

void _setLoggerCallback(ChatIO ch) {
  ch.FFI_Util_Logger(chatioLogLevel);
}

String _authV1GetToken(String authUrl, String deviceInfo, String privateKeyBs64,
    String domain, TypeSendPort port) {
  ChatIO ch = ChatIO(chatioNativeLibrary());
  _setLoggerCallback(ch);
  var privateKeyBytes = base64.decode(privateKeyBs64);
  Pointer<Uint8> ptrPrivatekey = calloc<Uint8>(privateKeyBytes.length);
  for (var i = 0; i < privateKeyBytes.length; i++) {
    ptrPrivatekey[i] = privateKeyBytes[i];
  }

  var ptrAuthUrl = authUrl.toNativeUtf8();
  var ptrDeviceInfo = deviceInfo.toNativeUtf8();
  var ptrDomain = domain.toNativeUtf8();

  var tokenBytes = ch.FFI_AuthV1_GetToken(
      ptrAuthUrl.cast(), ptrDeviceInfo.cast(), ptrPrivatekey, ptrDomain.cast());
  var token = tokenBytes.cast<Utf8>().toDartString();

  ch.FFI_Util_Free(tokenBytes);
  calloc.free(ptrPrivatekey);
  calloc.free(ptrAuthUrl);
  calloc.free(ptrDeviceInfo);
  calloc.free(ptrDomain);

  return token;
}

class _AuthV3GetTokenArgs {
  String authUrl;
  String extendData;
  String deviceCode;
  String privateKeyBs64;
  int action;
  _AuthV3GetTokenArgs({
    required this.authUrl,
    required this.extendData,
    required this.deviceCode,
    required this.privateKeyBs64,
    required this.action,
  });
}

String _authV3GetToken(_AuthV3GetTokenArgs args, TypeSendPort port) {
  ChatIO ch = ChatIO(chatioNativeLibrary());
  _setLoggerCallback(ch);
  var privateKeyBytes = base64.decode(args.privateKeyBs64);
  Pointer<Uint8> ptrPrivatekey = calloc<Uint8>(privateKeyBytes.length);
  for (var i = 0; i < privateKeyBytes.length; i++) {
    ptrPrivatekey[i] = privateKeyBytes[i];
  }

  var ptrAuthUrl = args.authUrl.toNativeUtf8();
  var ptrExtendData = args.extendData.toNativeUtf8();
  var ptrDeviceCode = args.deviceCode.toNativeUtf8();

  var tokenBytes = ch.FFI_AuthV3_GetToken(ptrAuthUrl.cast(),
      ptrExtendData.cast(), ptrDeviceCode.cast(), ptrPrivatekey, args.action);
  var token = tokenBytes.cast<Utf8>().toDartString();

  ch.FFI_Util_Free(tokenBytes);
  calloc.free(ptrPrivatekey);
  calloc.free(ptrAuthUrl);
  calloc.free(ptrExtendData);
  calloc.free(ptrDeviceCode);

  return token;
}


// ignore: camel_case_types
class _authV2QRCodeScanArgs {
  String authUrl;
  String uuid;
  String qrcodePublicKeyBs64;
  Uint8List privateKey;
  Uint8List data;

  _authV2QRCodeScanArgs(
      {required this.authUrl,
      required this.uuid,
      required this.qrcodePublicKeyBs64,
      required this.privateKey,
      required this.data});
}

int _authV2QRCodeScan(_authV2QRCodeScanArgs args, TypeSendPort port) {
  ChatIO ch = ChatIO(chatioNativeLibrary());
  _setLoggerCallback(ch);

  Pointer<Uint8> ptrPrivatekey = calloc<Uint8>(args.privateKey.length);
  for (var i = 0; i < args.privateKey.length; i++) {
    ptrPrivatekey[i] = args.privateKey[i];
  }
  Pointer<Uint8> ptrData = calloc<Uint8>(args.data.length);
  for (var i = 0; i < args.data.length; i++) {
    ptrData[i] = args.data[i];
  }

  var ptrAuthUrl = args.authUrl.toNativeUtf8();
  var ptrUuid = args.uuid.toNativeUtf8();
  var ptrQrcode = args.qrcodePublicKeyBs64.toNativeUtf8();

  var result = ch.FFI_AuthV2_QRCodeScan(ptrAuthUrl.cast(), ptrUuid.cast(),
      ptrQrcode.cast(), ptrPrivatekey, ptrData, args.data.length);

  calloc.free(ptrPrivatekey);
  calloc.free(ptrData);
  calloc.free(ptrAuthUrl);
  calloc.free(ptrUuid);
  calloc.free(ptrQrcode);
  return result;
}

// ignore: camel_case_types
class _groupCreateCommitArgs {
  String inFile;
  String outFile;
  Uint8List body;
  Uint8List privateKey;
  String domain;

  _groupCreateCommitArgs(
      {required this.inFile,
      required this.outFile,
      required this.body,
      required this.privateKey,
      required this.domain});
}

int _groupCreate(_groupCreateCommitArgs args, TypeSendPort port) {
  ChatIO ch = ChatIO(chatioNativeLibrary());
  _setLoggerCallback(ch);

  Pointer<Uint8> ptrPrivatekey = calloc<Uint8>(args.privateKey.length);
  for (var i = 0; i < args.privateKey.length; i++) {
    ptrPrivatekey[i] = args.privateKey[i];
  }
  Pointer<Uint8> ptrBody = calloc<Uint8>(args.body.length);
  for (var i = 0; i < args.body.length; i++) {
    ptrBody[i] = args.body[i];
  }

  Pointer<Utf8> ptrInFile = nullptr;
  if (args.inFile.isNotEmpty) {
    ptrInFile = args.inFile.toNativeUtf8();
  }
  var ptrOutFile = args.outFile.toNativeUtf8();

  var result = ch.FFI_Group_Create(ptrInFile.cast(), ptrOutFile.cast(), ptrBody,
      args.body.length, ptrPrivatekey);

  calloc.free(ptrPrivatekey);
  calloc.free(ptrBody);
  calloc.free(ptrInFile);
  calloc.free(ptrOutFile);
  return result;
}

int _groupCommit(_groupCreateCommitArgs args, TypeSendPort port) {
  ChatIO ch = ChatIO(chatioNativeLibrary());
  _setLoggerCallback(ch);

  Pointer<Uint8> ptrPrivatekey = calloc<Uint8>(args.privateKey.length);
  for (var i = 0; i < args.privateKey.length; i++) {
    ptrPrivatekey[i] = args.privateKey[i];
  }
  Pointer<Uint8> ptrBody = calloc<Uint8>(args.body.length);
  for (var i = 0; i < args.body.length; i++) {
    ptrBody[i] = args.body[i];
  }

  var ptrInFile = args.inFile.toNativeUtf8();
  var ptrOutFile = args.outFile.toNativeUtf8();
  var ptrDomain = args.domain.toNativeUtf8();

  var result = ch.FFI_Group_Commit(ptrInFile.cast(), ptrOutFile.cast(), ptrBody,
      args.body.length, ptrPrivatekey);
  calloc.free(ptrPrivatekey);
  calloc.free(ptrBody);
  calloc.free(ptrInFile);
  calloc.free(ptrOutFile);
  calloc.free(ptrDomain);
  return result;
}

int _groupMerge(
    String inFile, String mergeFile, String outFile, TypeSendPort port) {
  ChatIO ch = ChatIO(chatioNativeLibrary());
  _setLoggerCallback(ch);

  var ptrInFile = inFile.toNativeUtf8();
  var ptrOutFile = outFile.toNativeUtf8();
  var ptrMergeFile = mergeFile.toNativeUtf8();

  var result = ch.FFI_Group_Merge(
      ptrInFile.cast(), ptrMergeFile.cast(), ptrOutFile.cast());
  calloc.free(ptrInFile);
  calloc.free(ptrOutFile);
  calloc.free(ptrMergeFile);
  return result;
}

Uint8List? _utilPrivate(
    String privateKeyBs64, String password, TypeSendPort port) {
  ChatIO ch = ChatIO(chatioNativeLibrary());
  _setLoggerCallback(ch);

  Pointer<Uint8> ptrResPrivatekey = calloc<Uint8>(32);
  var ptrPrivateKeyBs64 = privateKeyBs64.toNativeUtf8();
  var ptrPassword = password.toNativeUtf8();

  var result = ch.FFI_Util_Private(ptrPrivateKeyBs64.cast(), ptrPassword.cast(),
      Pointer.fromAddress(ptrResPrivatekey.address));
  if (result != 0) {
    return null;
  }
  var privateKey = ptrResPrivatekey.asTypedList(32);

  calloc.free(ptrResPrivatekey);
  calloc.free(ptrPrivateKeyBs64);
  calloc.free(ptrPassword);
  return privateKey;
}

Uint8List? _utilCreatePrivateKey(int arg, TypeSendPort port) {
  ChatIO ch = ChatIO(chatioNativeLibrary());
  _setLoggerCallback(ch);

  Pointer<Uint8> ptrResPrivatekey = calloc<Uint8>(32);

  var result = ch.FFI_Util_CreatePrivateKey(
      Pointer.fromAddress(ptrResPrivatekey.address));
  if (result != 0) {
    return null;
  }
  var privateKey = ptrResPrivatekey.asTypedList(32);
  calloc.free(ptrResPrivatekey);
  return privateKey;
}

class FfiKeyPair {
  Uint8List privateKey;
  Uint8List publicKey;

  FfiKeyPair({required this.privateKey, required this.publicKey});
}

FfiKeyPair? _utilCreateKeyPair(int arg, TypeSendPort port) {
  ChatIO ch = ChatIO(chatioNativeLibrary());
  _setLoggerCallback(ch);

  Pointer<Uint8> ptrPrivatekey = calloc<Uint8>(32);
  Pointer<Uint8> ptrPublickey = calloc<Uint8>(32);

  var result = ch.FFI_Util_CreateKeyPair(
      Pointer.fromAddress(ptrPrivatekey.address),
      Pointer.fromAddress(ptrPublickey.address));
  if (result != 0) {
    return null;
  }

  var privateKey = ptrPrivatekey.asTypedList(32);
  var publicKey = ptrPublickey.asTypedList(32);

  calloc.free(ptrPrivatekey);
  calloc.free(ptrPublickey);
  return FfiKeyPair(privateKey: privateKey, publicKey: publicKey);
}

String _utilName(Uint8List privateKey, String domain, TypeSendPort port) {
  ChatIO ch = ChatIO(chatioNativeLibrary());
  _setLoggerCallback(ch);

  Pointer<Uint8> ptrPrivatekey = calloc<Uint8>(privateKey.length);
  for (var i = 0; i < privateKey.length; i++) {
    ptrPrivatekey[i] = privateKey[i];
  }

  var result = ch.FFI_Util_DisplayName(ptrPrivatekey);
  var name = result.cast<Utf8>().toDartString();

  ch.FFI_Util_Free(result);
  calloc.free(ptrPrivatekey);

  return name;
}

String _utilFileEncrypt(String src, String dst, TypeSendPort port) {
  ChatIO ch = ChatIO(chatioNativeLibrary());
  _setLoggerCallback(ch);

  var ptrSrc = src.toNativeUtf8();
  var ptrDst = dst.toNativeUtf8();

  var result = ch.FFI_Util_FileEncrypt(ptrSrc.cast(), ptrDst.cast());
  var fragment = result.cast<Utf8>().toDartString();

  ch.FFI_Util_Free(result);
  calloc.free(ptrSrc);
  calloc.free(ptrDst);
  return fragment;
}

int _utilFileDecrypt(
    String src, String dst, String fragment, TypeSendPort port) {
  ChatIO ch = ChatIO(chatioNativeLibrary());
  _setLoggerCallback(ch);

  var ptrSrc = src.toNativeUtf8();
  var ptrDst = dst.toNativeUtf8();
  var ptrFragment = fragment.toNativeUtf8();

  var result =
      ch.FFI_Util_FileDecrypt(ptrSrc.cast(), ptrDst.cast(), ptrFragment.cast());
  calloc.free(ptrSrc);
  calloc.free(ptrDst);
  calloc.free(ptrFragment);
  return result;
}

Uint8List _utilFileDecryptToMemory(
    String src, String fragment, TypeSendPort port) {
  ChatIO ch = ChatIO(chatioNativeLibrary());
  _setLoggerCallback(ch);
  var ptrSrc = src.toNativeUtf8();

  var ptrFragment = fragment.toNativeUtf8();
  Uint8List data = Uint8List(0);

  ChatIOBytesPtr result =
      ch.FFI_Util_FileDecryptToMemory(ptrSrc.cast(), ptrFragment.cast());

  if (result != nullptr) {
    if (result.ref.ptr != nullptr) {
      data = Uint8List.fromList(
          result.ref.ptr.cast<Uint8>().asTypedList(result.ref.len));
    }
    ch.FFI_Util_FreeBytes(result);
  }

  calloc.free(ptrSrc);
  calloc.free(ptrFragment);

  return data;
}

String _utilDisplayableFingeprint(
    String remoteName, Uint8List privateKey, TypeSendPort port) {
  ChatIO ch = ChatIO(chatioNativeLibrary());
  _setLoggerCallback(ch);

  Pointer<Uint8> ptrPrivatekey = calloc<Uint8>(privateKey.length);
  for (var i = 0; i < privateKey.length; i++) {
    ptrPrivatekey[i] = privateKey[i];
  }
  var ptrRemoteName = remoteName.toNativeUtf8();

  var result =
      ch.FFI_Util_DisplayableFingeprint(ptrRemoteName.cast(), ptrPrivatekey);
  var displayableFingeprint = result.cast<Utf8>().toDartString();

  ch.FFI_Util_Free(result);
  calloc.free(ptrPrivatekey);
  calloc.free(ptrRemoteName);

  return displayableFingeprint;
}

String _utilScannableFingeprint(
    String remoteName, Uint8List privateKey, TypeSendPort port) {
  ChatIO ch = ChatIO(chatioNativeLibrary());
  _setLoggerCallback(ch);

  Pointer<Uint8> ptrPrivatekey = calloc<Uint8>(privateKey.length);
  for (var i = 0; i < privateKey.length; i++) {
    ptrPrivatekey[i] = privateKey[i];
  }
  var ptrRemoteName = remoteName.toNativeUtf8();

  var result =
      ch.FFI_Util_ScannableFingeprint(ptrRemoteName.cast(), ptrPrivatekey);
  var scannableFingeprint = result.cast<Utf8>().toDartString();

  ch.FFI_Util_Free(result);
  calloc.free(ptrPrivatekey);
  calloc.free(ptrRemoteName);

  return scannableFingeprint;
}

int _utilScannableFingeprintCompare(String remoteName, String combined,
    Uint8List privateKey, TypeSendPort port) {
  ChatIO ch = ChatIO(chatioNativeLibrary());
  _setLoggerCallback(ch);

  Pointer<Uint8> ptrPrivatekey = calloc<Uint8>(privateKey.length);
  for (var i = 0; i < privateKey.length; i++) {
    ptrPrivatekey[i] = privateKey[i];
  }

  var ptrRemoteName = remoteName.toNativeUtf8();
  var ptrCombined = combined.toNativeUtf8();

  var result = ch.FFI_Util_ScannableFingeprintCompare(
      ptrRemoteName.cast(), ptrCombined.cast(), ptrPrivatekey);

  calloc.free(ptrPrivatekey);
  calloc.free(ptrRemoteName);
  calloc.free(ptrCombined);

  return result;
}

void _setProxy(String proxy, TypeSendPort port)  {
  ChatIO ch = ChatIO(chatioNativeLibrary());
  var ptrProxy = proxy.toNativeUtf8();
  ch.FFI_Chatio_Proxy(ptrProxy.cast());
  calloc.free(ptrProxy);

}