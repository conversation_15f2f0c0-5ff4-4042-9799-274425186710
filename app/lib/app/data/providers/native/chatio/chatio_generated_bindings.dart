// ignore_for_file: camel_case_types, non_constant_identifier_names, unused_element, unused_field, constant_identifier_names

// AUTO GENERATED FILE, DO NOT EDIT.
//
// Generated by `package:ffigen`.
import 'dart:ffi' as ffi;

/// Bindings to `headers/chatio_ffi.h`.
class ChatIO {
  /// Holds the symbol lookup function.
  final ffi.Pointer<T> Function<T extends ffi.NativeType>(String symbolName)
      _lookup;

  /// The symbols are looked up in [dynamicLibrary].
  ChatIO(ffi.DynamicLibrary dynamicLibrary) : _lookup = dynamicLibrary.lookup;

  /// The symbols are looked up with [lookup].
  ChatIO.fromLookup(
      ffi.Pointer<T> Function<T extends ffi.NativeType>(String symbolName)
          lookup)
      : _lookup = lookup;

  ffi.Pointer<ffi.NativeFunction<ffi.Void Function(ffi.Int)>> signal(
    int arg0,
    ffi.Pointer<ffi.NativeFunction<ffi.Void Function(ffi.Int)>> arg1,
  ) {
    return _signal(
      arg0,
      arg1,
    );
  }

  late final _signalPtr = _lookup<
      ffi.NativeFunction<
          ffi.Pointer<ffi.NativeFunction<ffi.Void Function(ffi.Int)>> Function(
              ffi.Int,
              ffi.Pointer<
                  ffi.NativeFunction<ffi.Void Function(ffi.Int)>>)>>('signal');
  late final _signal = _signalPtr.asFunction<
      ffi.Pointer<ffi.NativeFunction<ffi.Void Function(ffi.Int)>> Function(
          int, ffi.Pointer<ffi.NativeFunction<ffi.Void Function(ffi.Int)>>)>();

  int getpriority(
    int arg0,
    int arg1,
  ) {
    return _getpriority(
      arg0,
      arg1,
    );
  }

  late final _getpriorityPtr =
      _lookup<ffi.NativeFunction<ffi.Int Function(ffi.Int, id_t)>>(
          'getpriority');
  late final _getpriority =
      _getpriorityPtr.asFunction<int Function(int, int)>();

  int getiopolicy_np(
    int arg0,
    int arg1,
  ) {
    return _getiopolicy_np(
      arg0,
      arg1,
    );
  }

  late final _getiopolicy_npPtr =
      _lookup<ffi.NativeFunction<ffi.Int Function(ffi.Int, ffi.Int)>>(
          'getiopolicy_np');
  late final _getiopolicy_np =
      _getiopolicy_npPtr.asFunction<int Function(int, int)>();

  int getrlimit(
    int arg0,
    ffi.Pointer<rlimit> arg1,
  ) {
    return _getrlimit(
      arg0,
      arg1,
    );
  }

  late final _getrlimitPtr = _lookup<
          ffi.NativeFunction<ffi.Int Function(ffi.Int, ffi.Pointer<rlimit>)>>(
      'getrlimit');
  late final _getrlimit =
      _getrlimitPtr.asFunction<int Function(int, ffi.Pointer<rlimit>)>();

  int getrusage(
    int arg0,
    ffi.Pointer<rusage> arg1,
  ) {
    return _getrusage(
      arg0,
      arg1,
    );
  }

  late final _getrusagePtr = _lookup<
          ffi.NativeFunction<ffi.Int Function(ffi.Int, ffi.Pointer<rusage>)>>(
      'getrusage');
  late final _getrusage =
      _getrusagePtr.asFunction<int Function(int, ffi.Pointer<rusage>)>();

  int setpriority(
    int arg0,
    int arg1,
    int arg2,
  ) {
    return _setpriority(
      arg0,
      arg1,
      arg2,
    );
  }

  late final _setpriorityPtr =
      _lookup<ffi.NativeFunction<ffi.Int Function(ffi.Int, id_t, ffi.Int)>>(
          'setpriority');
  late final _setpriority =
      _setpriorityPtr.asFunction<int Function(int, int, int)>();

  int setiopolicy_np(
    int arg0,
    int arg1,
    int arg2,
  ) {
    return _setiopolicy_np(
      arg0,
      arg1,
      arg2,
    );
  }

  late final _setiopolicy_npPtr =
      _lookup<ffi.NativeFunction<ffi.Int Function(ffi.Int, ffi.Int, ffi.Int)>>(
          'setiopolicy_np');
  late final _setiopolicy_np =
      _setiopolicy_npPtr.asFunction<int Function(int, int, int)>();

  int setrlimit(
    int arg0,
    ffi.Pointer<rlimit> arg1,
  ) {
    return _setrlimit(
      arg0,
      arg1,
    );
  }

  late final _setrlimitPtr = _lookup<
          ffi.NativeFunction<ffi.Int Function(ffi.Int, ffi.Pointer<rlimit>)>>(
      'setrlimit');
  late final _setrlimit =
      _setrlimitPtr.asFunction<int Function(int, ffi.Pointer<rlimit>)>();

  int wait1(
    ffi.Pointer<ffi.Int> arg0,
  ) {
    return _wait1(
      arg0,
    );
  }

  late final _wait1Ptr =
      _lookup<ffi.NativeFunction<pid_t Function(ffi.Pointer<ffi.Int>)>>('wait');
  late final _wait1 =
      _wait1Ptr.asFunction<int Function(ffi.Pointer<ffi.Int>)>();

  int waitpid(
    int arg0,
    ffi.Pointer<ffi.Int> arg1,
    int arg2,
  ) {
    return _waitpid(
      arg0,
      arg1,
      arg2,
    );
  }

  late final _waitpidPtr = _lookup<
      ffi.NativeFunction<
          pid_t Function(pid_t, ffi.Pointer<ffi.Int>, ffi.Int)>>('waitpid');
  late final _waitpid =
      _waitpidPtr.asFunction<int Function(int, ffi.Pointer<ffi.Int>, int)>();

  int waitid(
    int arg0,
    int arg1,
    ffi.Pointer<siginfo_t> arg2,
    int arg3,
  ) {
    return _waitid(
      arg0,
      arg1,
      arg2,
      arg3,
    );
  }

  late final _waitidPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int Function(
              ffi.Int32, id_t, ffi.Pointer<siginfo_t>, ffi.Int)>>('waitid');
  late final _waitid = _waitidPtr
      .asFunction<int Function(int, int, ffi.Pointer<siginfo_t>, int)>();

  int wait3(
    ffi.Pointer<ffi.Int> arg0,
    int arg1,
    ffi.Pointer<rusage> arg2,
  ) {
    return _wait3(
      arg0,
      arg1,
      arg2,
    );
  }

  late final _wait3Ptr = _lookup<
      ffi.NativeFunction<
          pid_t Function(
              ffi.Pointer<ffi.Int>, ffi.Int, ffi.Pointer<rusage>)>>('wait3');
  late final _wait3 = _wait3Ptr.asFunction<
      int Function(ffi.Pointer<ffi.Int>, int, ffi.Pointer<rusage>)>();

  int wait4(
    int arg0,
    ffi.Pointer<ffi.Int> arg1,
    int arg2,
    ffi.Pointer<rusage> arg3,
  ) {
    return _wait4(
      arg0,
      arg1,
      arg2,
      arg3,
    );
  }

  late final _wait4Ptr = _lookup<
      ffi.NativeFunction<
          pid_t Function(pid_t, ffi.Pointer<ffi.Int>, ffi.Int,
              ffi.Pointer<rusage>)>>('wait4');
  late final _wait4 = _wait4Ptr.asFunction<
      int Function(int, ffi.Pointer<ffi.Int>, int, ffi.Pointer<rusage>)>();

  ffi.Pointer<ffi.Void> alloca(
    int arg0,
  ) {
    return _alloca(
      arg0,
    );
  }

  late final _allocaPtr =
      _lookup<ffi.NativeFunction<ffi.Pointer<ffi.Void> Function(ffi.Size)>>(
          'alloca');
  late final _alloca =
      _allocaPtr.asFunction<ffi.Pointer<ffi.Void> Function(int)>();

  late final ffi.Pointer<ffi.Int> ___mb_cur_max =
      _lookup<ffi.Int>('__mb_cur_max');

  int get __mb_cur_max => ___mb_cur_max.value;

  set __mb_cur_max(int value) => ___mb_cur_max.value = value;

  ffi.Pointer<ffi.Void> malloc_type_malloc(
    int size,
    int type_id,
  ) {
    return _malloc_type_malloc(
      size,
      type_id,
    );
  }

  late final _malloc_type_mallocPtr = _lookup<
      ffi.NativeFunction<
          ffi.Pointer<ffi.Void> Function(
              ffi.Size, malloc_type_id_t)>>('malloc_type_malloc');
  late final _malloc_type_malloc = _malloc_type_mallocPtr
      .asFunction<ffi.Pointer<ffi.Void> Function(int, int)>();

  ffi.Pointer<ffi.Void> malloc_type_calloc(
    int count,
    int size,
    int type_id,
  ) {
    return _malloc_type_calloc(
      count,
      size,
      type_id,
    );
  }

  late final _malloc_type_callocPtr = _lookup<
      ffi.NativeFunction<
          ffi.Pointer<ffi.Void> Function(
              ffi.Size, ffi.Size, malloc_type_id_t)>>('malloc_type_calloc');
  late final _malloc_type_calloc = _malloc_type_callocPtr
      .asFunction<ffi.Pointer<ffi.Void> Function(int, int, int)>();

  void malloc_type_free(
    ffi.Pointer<ffi.Void> ptr,
    int type_id,
  ) {
    return _malloc_type_free(
      ptr,
      type_id,
    );
  }

  late final _malloc_type_freePtr = _lookup<
      ffi.NativeFunction<
          ffi.Void Function(
              ffi.Pointer<ffi.Void>, malloc_type_id_t)>>('malloc_type_free');
  late final _malloc_type_free = _malloc_type_freePtr
      .asFunction<void Function(ffi.Pointer<ffi.Void>, int)>();

  ffi.Pointer<ffi.Void> malloc_type_realloc(
    ffi.Pointer<ffi.Void> ptr,
    int size,
    int type_id,
  ) {
    return _malloc_type_realloc(
      ptr,
      size,
      type_id,
    );
  }

  late final _malloc_type_reallocPtr = _lookup<
      ffi.NativeFunction<
          ffi.Pointer<ffi.Void> Function(ffi.Pointer<ffi.Void>, ffi.Size,
              malloc_type_id_t)>>('malloc_type_realloc');
  late final _malloc_type_realloc = _malloc_type_reallocPtr.asFunction<
      ffi.Pointer<ffi.Void> Function(ffi.Pointer<ffi.Void>, int, int)>();

  ffi.Pointer<ffi.Void> malloc_type_valloc(
    int size,
    int type_id,
  ) {
    return _malloc_type_valloc(
      size,
      type_id,
    );
  }

  late final _malloc_type_vallocPtr = _lookup<
      ffi.NativeFunction<
          ffi.Pointer<ffi.Void> Function(
              ffi.Size, malloc_type_id_t)>>('malloc_type_valloc');
  late final _malloc_type_valloc = _malloc_type_vallocPtr
      .asFunction<ffi.Pointer<ffi.Void> Function(int, int)>();

  ffi.Pointer<ffi.Void> malloc_type_aligned_alloc(
    int alignment,
    int size,
    int type_id,
  ) {
    return _malloc_type_aligned_alloc(
      alignment,
      size,
      type_id,
    );
  }

  late final _malloc_type_aligned_allocPtr = _lookup<
      ffi.NativeFunction<
          ffi.Pointer<ffi.Void> Function(ffi.Size, ffi.Size,
              malloc_type_id_t)>>('malloc_type_aligned_alloc');
  late final _malloc_type_aligned_alloc = _malloc_type_aligned_allocPtr
      .asFunction<ffi.Pointer<ffi.Void> Function(int, int, int)>();

  int malloc_type_posix_memalign(
    ffi.Pointer<ffi.Pointer<ffi.Void>> memptr,
    int alignment,
    int size,
    int type_id,
  ) {
    return _malloc_type_posix_memalign(
      memptr,
      alignment,
      size,
      type_id,
    );
  }

  late final _malloc_type_posix_memalignPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int Function(ffi.Pointer<ffi.Pointer<ffi.Void>>, ffi.Size,
              ffi.Size, malloc_type_id_t)>>('malloc_type_posix_memalign');
  late final _malloc_type_posix_memalign =
      _malloc_type_posix_memalignPtr.asFunction<
          int Function(ffi.Pointer<ffi.Pointer<ffi.Void>>, int, int, int)>();

  ffi.Pointer<ffi.Void> malloc_type_zone_malloc(
    ffi.Pointer<malloc_zone_t> zone,
    int size,
    int type_id,
  ) {
    return _malloc_type_zone_malloc(
      zone,
      size,
      type_id,
    );
  }

  late final _malloc_type_zone_mallocPtr = _lookup<
      ffi.NativeFunction<
          ffi.Pointer<ffi.Void> Function(ffi.Pointer<malloc_zone_t>, ffi.Size,
              malloc_type_id_t)>>('malloc_type_zone_malloc');
  late final _malloc_type_zone_malloc = _malloc_type_zone_mallocPtr.asFunction<
      ffi.Pointer<ffi.Void> Function(ffi.Pointer<malloc_zone_t>, int, int)>();

  ffi.Pointer<ffi.Void> malloc_type_zone_calloc(
    ffi.Pointer<malloc_zone_t> zone,
    int count,
    int size,
    int type_id,
  ) {
    return _malloc_type_zone_calloc(
      zone,
      count,
      size,
      type_id,
    );
  }

  late final _malloc_type_zone_callocPtr = _lookup<
      ffi.NativeFunction<
          ffi.Pointer<ffi.Void> Function(ffi.Pointer<malloc_zone_t>, ffi.Size,
              ffi.Size, malloc_type_id_t)>>('malloc_type_zone_calloc');
  late final _malloc_type_zone_calloc = _malloc_type_zone_callocPtr.asFunction<
      ffi.Pointer<ffi.Void> Function(
          ffi.Pointer<malloc_zone_t>, int, int, int)>();

  void malloc_type_zone_free(
    ffi.Pointer<malloc_zone_t> zone,
    ffi.Pointer<ffi.Void> ptr,
    int type_id,
  ) {
    return _malloc_type_zone_free(
      zone,
      ptr,
      type_id,
    );
  }

  late final _malloc_type_zone_freePtr = _lookup<
      ffi.NativeFunction<
          ffi.Void Function(ffi.Pointer<malloc_zone_t>, ffi.Pointer<ffi.Void>,
              malloc_type_id_t)>>('malloc_type_zone_free');
  late final _malloc_type_zone_free = _malloc_type_zone_freePtr.asFunction<
      void Function(ffi.Pointer<malloc_zone_t>, ffi.Pointer<ffi.Void>, int)>();

  ffi.Pointer<ffi.Void> malloc_type_zone_realloc(
    ffi.Pointer<malloc_zone_t> zone,
    ffi.Pointer<ffi.Void> ptr,
    int size,
    int type_id,
  ) {
    return _malloc_type_zone_realloc(
      zone,
      ptr,
      size,
      type_id,
    );
  }

  late final _malloc_type_zone_reallocPtr = _lookup<
      ffi.NativeFunction<
          ffi.Pointer<ffi.Void> Function(
              ffi.Pointer<malloc_zone_t>,
              ffi.Pointer<ffi.Void>,
              ffi.Size,
              malloc_type_id_t)>>('malloc_type_zone_realloc');
  late final _malloc_type_zone_realloc =
      _malloc_type_zone_reallocPtr.asFunction<
          ffi.Pointer<ffi.Void> Function(
              ffi.Pointer<malloc_zone_t>, ffi.Pointer<ffi.Void>, int, int)>();

  ffi.Pointer<ffi.Void> malloc_type_zone_valloc(
    ffi.Pointer<malloc_zone_t> zone,
    int size,
    int type_id,
  ) {
    return _malloc_type_zone_valloc(
      zone,
      size,
      type_id,
    );
  }

  late final _malloc_type_zone_vallocPtr = _lookup<
      ffi.NativeFunction<
          ffi.Pointer<ffi.Void> Function(ffi.Pointer<malloc_zone_t>, ffi.Size,
              malloc_type_id_t)>>('malloc_type_zone_valloc');
  late final _malloc_type_zone_valloc = _malloc_type_zone_vallocPtr.asFunction<
      ffi.Pointer<ffi.Void> Function(ffi.Pointer<malloc_zone_t>, int, int)>();

  ffi.Pointer<ffi.Void> malloc_type_zone_memalign(
    ffi.Pointer<malloc_zone_t> zone,
    int alignment,
    int size,
    int type_id,
  ) {
    return _malloc_type_zone_memalign(
      zone,
      alignment,
      size,
      type_id,
    );
  }

  late final _malloc_type_zone_memalignPtr = _lookup<
      ffi.NativeFunction<
          ffi.Pointer<ffi.Void> Function(ffi.Pointer<malloc_zone_t>, ffi.Size,
              ffi.Size, malloc_type_id_t)>>('malloc_type_zone_memalign');
  late final _malloc_type_zone_memalign =
      _malloc_type_zone_memalignPtr.asFunction<
          ffi.Pointer<ffi.Void> Function(
              ffi.Pointer<malloc_zone_t>, int, int, int)>();

  ffi.Pointer<ffi.Void> malloc(
    int __size,
  ) {
    return _malloc(
      __size,
    );
  }

  late final _mallocPtr =
      _lookup<ffi.NativeFunction<ffi.Pointer<ffi.Void> Function(ffi.Size)>>(
          'malloc');
  late final _malloc =
      _mallocPtr.asFunction<ffi.Pointer<ffi.Void> Function(int)>();

  ffi.Pointer<ffi.Void> calloc(
    int __count,
    int __size,
  ) {
    return _calloc(
      __count,
      __size,
    );
  }

  late final _callocPtr = _lookup<
      ffi.NativeFunction<
          ffi.Pointer<ffi.Void> Function(ffi.Size, ffi.Size)>>('calloc');
  late final _calloc =
      _callocPtr.asFunction<ffi.Pointer<ffi.Void> Function(int, int)>();

  void free(
    ffi.Pointer<ffi.Void> arg0,
  ) {
    return _free(
      arg0,
    );
  }

  late final _freePtr =
      _lookup<ffi.NativeFunction<ffi.Void Function(ffi.Pointer<ffi.Void>)>>(
          'free');
  late final _free =
      _freePtr.asFunction<void Function(ffi.Pointer<ffi.Void>)>();

  ffi.Pointer<ffi.Void> realloc(
    ffi.Pointer<ffi.Void> __ptr,
    int __size,
  ) {
    return _realloc(
      __ptr,
      __size,
    );
  }

  late final _reallocPtr = _lookup<
      ffi.NativeFunction<
          ffi.Pointer<ffi.Void> Function(
              ffi.Pointer<ffi.Void>, ffi.Size)>>('realloc');
  late final _realloc = _reallocPtr
      .asFunction<ffi.Pointer<ffi.Void> Function(ffi.Pointer<ffi.Void>, int)>();

  ffi.Pointer<ffi.Void> valloc(
    int arg0,
  ) {
    return _valloc(
      arg0,
    );
  }

  late final _vallocPtr =
      _lookup<ffi.NativeFunction<ffi.Pointer<ffi.Void> Function(ffi.Size)>>(
          'valloc');
  late final _valloc =
      _vallocPtr.asFunction<ffi.Pointer<ffi.Void> Function(int)>();

  ffi.Pointer<ffi.Void> aligned_alloc(
    int __alignment,
    int __size,
  ) {
    return _aligned_alloc(
      __alignment,
      __size,
    );
  }

  late final _aligned_allocPtr = _lookup<
      ffi.NativeFunction<
          ffi.Pointer<ffi.Void> Function(ffi.Size, ffi.Size)>>('aligned_alloc');
  late final _aligned_alloc =
      _aligned_allocPtr.asFunction<ffi.Pointer<ffi.Void> Function(int, int)>();

  int posix_memalign(
    ffi.Pointer<ffi.Pointer<ffi.Void>> __memptr,
    int __alignment,
    int __size,
  ) {
    return _posix_memalign(
      __memptr,
      __alignment,
      __size,
    );
  }

  late final _posix_memalignPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int Function(ffi.Pointer<ffi.Pointer<ffi.Void>>, ffi.Size,
              ffi.Size)>>('posix_memalign');
  late final _posix_memalign = _posix_memalignPtr
      .asFunction<int Function(ffi.Pointer<ffi.Pointer<ffi.Void>>, int, int)>();

  void abort() {
    return _abort();
  }

  late final _abortPtr =
      _lookup<ffi.NativeFunction<ffi.Void Function()>>('abort');
  late final _abort = _abortPtr.asFunction<void Function()>();

  int abs(
    int arg0,
  ) {
    return _abs(
      arg0,
    );
  }

  late final _absPtr =
      _lookup<ffi.NativeFunction<ffi.Int Function(ffi.Int)>>('abs');
  late final _abs = _absPtr.asFunction<int Function(int)>();

  int atexit(
    ffi.Pointer<ffi.NativeFunction<ffi.Void Function()>> arg0,
  ) {
    return _atexit(
      arg0,
    );
  }

  late final _atexitPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int Function(
              ffi.Pointer<ffi.NativeFunction<ffi.Void Function()>>)>>('atexit');
  late final _atexit = _atexitPtr.asFunction<
      int Function(ffi.Pointer<ffi.NativeFunction<ffi.Void Function()>>)>();

  double atof(
    ffi.Pointer<ffi.Char> arg0,
  ) {
    return _atof(
      arg0,
    );
  }

  late final _atofPtr =
      _lookup<ffi.NativeFunction<ffi.Double Function(ffi.Pointer<ffi.Char>)>>(
          'atof');
  late final _atof =
      _atofPtr.asFunction<double Function(ffi.Pointer<ffi.Char>)>();

  int atoi(
    ffi.Pointer<ffi.Char> arg0,
  ) {
    return _atoi(
      arg0,
    );
  }

  late final _atoiPtr =
      _lookup<ffi.NativeFunction<ffi.Int Function(ffi.Pointer<ffi.Char>)>>(
          'atoi');
  late final _atoi = _atoiPtr.asFunction<int Function(ffi.Pointer<ffi.Char>)>();

  int atol(
    ffi.Pointer<ffi.Char> arg0,
  ) {
    return _atol(
      arg0,
    );
  }

  late final _atolPtr =
      _lookup<ffi.NativeFunction<ffi.Long Function(ffi.Pointer<ffi.Char>)>>(
          'atol');
  late final _atol = _atolPtr.asFunction<int Function(ffi.Pointer<ffi.Char>)>();

  int atoll(
    ffi.Pointer<ffi.Char> arg0,
  ) {
    return _atoll(
      arg0,
    );
  }

  late final _atollPtr =
      _lookup<ffi.NativeFunction<ffi.LongLong Function(ffi.Pointer<ffi.Char>)>>(
          'atoll');
  late final _atoll =
      _atollPtr.asFunction<int Function(ffi.Pointer<ffi.Char>)>();

  ffi.Pointer<ffi.Void> bsearch(
    ffi.Pointer<ffi.Void> __key,
    ffi.Pointer<ffi.Void> __base,
    int __nel,
    int __width,
    ffi.Pointer<
            ffi.NativeFunction<
                ffi.Int Function(ffi.Pointer<ffi.Void>, ffi.Pointer<ffi.Void>)>>
        __compar,
  ) {
    return _bsearch(
      __key,
      __base,
      __nel,
      __width,
      __compar,
    );
  }

  late final _bsearchPtr = _lookup<
      ffi.NativeFunction<
          ffi.Pointer<ffi.Void> Function(
              ffi.Pointer<ffi.Void>,
              ffi.Pointer<ffi.Void>,
              ffi.Size,
              ffi.Size,
              ffi.Pointer<
                  ffi.NativeFunction<
                      ffi.Int Function(ffi.Pointer<ffi.Void>,
                          ffi.Pointer<ffi.Void>)>>)>>('bsearch');
  late final _bsearch = _bsearchPtr.asFunction<
      ffi.Pointer<ffi.Void> Function(
          ffi.Pointer<ffi.Void>,
          ffi.Pointer<ffi.Void>,
          int,
          int,
          ffi.Pointer<
              ffi.NativeFunction<
                  ffi.Int Function(
                      ffi.Pointer<ffi.Void>, ffi.Pointer<ffi.Void>)>>)>();

  div_t div(
    int arg0,
    int arg1,
  ) {
    return _div(
      arg0,
      arg1,
    );
  }

  late final _divPtr =
      _lookup<ffi.NativeFunction<div_t Function(ffi.Int, ffi.Int)>>('div');
  late final _div = _divPtr.asFunction<div_t Function(int, int)>();

  void exit(
    int arg0,
  ) {
    return _exit(
      arg0,
    );
  }

  late final _exitPtr =
      _lookup<ffi.NativeFunction<ffi.Void Function(ffi.Int)>>('exit');
  late final _exit = _exitPtr.asFunction<void Function(int)>();

  ffi.Pointer<ffi.Char> getenv(
    ffi.Pointer<ffi.Char> arg0,
  ) {
    return _getenv(
      arg0,
    );
  }

  late final _getenvPtr = _lookup<
      ffi.NativeFunction<
          ffi.Pointer<ffi.Char> Function(ffi.Pointer<ffi.Char>)>>('getenv');
  late final _getenv = _getenvPtr
      .asFunction<ffi.Pointer<ffi.Char> Function(ffi.Pointer<ffi.Char>)>();

  int labs(
    int arg0,
  ) {
    return _labs(
      arg0,
    );
  }

  late final _labsPtr =
      _lookup<ffi.NativeFunction<ffi.Long Function(ffi.Long)>>('labs');
  late final _labs = _labsPtr.asFunction<int Function(int)>();

  ldiv_t ldiv(
    int arg0,
    int arg1,
  ) {
    return _ldiv(
      arg0,
      arg1,
    );
  }

  late final _ldivPtr =
      _lookup<ffi.NativeFunction<ldiv_t Function(ffi.Long, ffi.Long)>>('ldiv');
  late final _ldiv = _ldivPtr.asFunction<ldiv_t Function(int, int)>();

  int llabs(
    int arg0,
  ) {
    return _llabs(
      arg0,
    );
  }

  late final _llabsPtr =
      _lookup<ffi.NativeFunction<ffi.LongLong Function(ffi.LongLong)>>('llabs');
  late final _llabs = _llabsPtr.asFunction<int Function(int)>();

  lldiv_t lldiv(
    int arg0,
    int arg1,
  ) {
    return _lldiv(
      arg0,
      arg1,
    );
  }

  late final _lldivPtr =
      _lookup<ffi.NativeFunction<lldiv_t Function(ffi.LongLong, ffi.LongLong)>>(
          'lldiv');
  late final _lldiv = _lldivPtr.asFunction<lldiv_t Function(int, int)>();

  int mblen(
    ffi.Pointer<ffi.Char> __s,
    int __n,
  ) {
    return _mblen(
      __s,
      __n,
    );
  }

  late final _mblenPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int Function(ffi.Pointer<ffi.Char>, ffi.Size)>>('mblen');
  late final _mblen =
      _mblenPtr.asFunction<int Function(ffi.Pointer<ffi.Char>, int)>();

  int mbstowcs(
    ffi.Pointer<ffi.WChar> arg0,
    ffi.Pointer<ffi.Char> arg1,
    int arg2,
  ) {
    return _mbstowcs(
      arg0,
      arg1,
      arg2,
    );
  }

  late final _mbstowcsPtr = _lookup<
      ffi.NativeFunction<
          ffi.Size Function(ffi.Pointer<ffi.WChar>, ffi.Pointer<ffi.Char>,
              ffi.Size)>>('mbstowcs');
  late final _mbstowcs = _mbstowcsPtr.asFunction<
      int Function(ffi.Pointer<ffi.WChar>, ffi.Pointer<ffi.Char>, int)>();

  int mbtowc(
    ffi.Pointer<ffi.WChar> arg0,
    ffi.Pointer<ffi.Char> arg1,
    int arg2,
  ) {
    return _mbtowc(
      arg0,
      arg1,
      arg2,
    );
  }

  late final _mbtowcPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int Function(ffi.Pointer<ffi.WChar>, ffi.Pointer<ffi.Char>,
              ffi.Size)>>('mbtowc');
  late final _mbtowc = _mbtowcPtr.asFunction<
      int Function(ffi.Pointer<ffi.WChar>, ffi.Pointer<ffi.Char>, int)>();

  void qsort(
    ffi.Pointer<ffi.Void> __base,
    int __nel,
    int __width,
    ffi.Pointer<
            ffi.NativeFunction<
                ffi.Int Function(ffi.Pointer<ffi.Void>, ffi.Pointer<ffi.Void>)>>
        __compar,
  ) {
    return _qsort(
      __base,
      __nel,
      __width,
      __compar,
    );
  }

  late final _qsortPtr = _lookup<
      ffi.NativeFunction<
          ffi.Void Function(
              ffi.Pointer<ffi.Void>,
              ffi.Size,
              ffi.Size,
              ffi.Pointer<
                  ffi.NativeFunction<
                      ffi.Int Function(ffi.Pointer<ffi.Void>,
                          ffi.Pointer<ffi.Void>)>>)>>('qsort');
  late final _qsort = _qsortPtr.asFunction<
      void Function(
          ffi.Pointer<ffi.Void>,
          int,
          int,
          ffi.Pointer<
              ffi.NativeFunction<
                  ffi.Int Function(
                      ffi.Pointer<ffi.Void>, ffi.Pointer<ffi.Void>)>>)>();

  int rand() {
    return _rand();
  }

  late final _randPtr = _lookup<ffi.NativeFunction<ffi.Int Function()>>('rand');
  late final _rand = _randPtr.asFunction<int Function()>();

  void srand(
    int arg0,
  ) {
    return _srand(
      arg0,
    );
  }

  late final _srandPtr =
      _lookup<ffi.NativeFunction<ffi.Void Function(ffi.UnsignedInt)>>('srand');
  late final _srand = _srandPtr.asFunction<void Function(int)>();

  double strtod(
    ffi.Pointer<ffi.Char> arg0,
    ffi.Pointer<ffi.Pointer<ffi.Char>> arg1,
  ) {
    return _strtod(
      arg0,
      arg1,
    );
  }

  late final _strtodPtr = _lookup<
      ffi.NativeFunction<
          ffi.Double Function(ffi.Pointer<ffi.Char>,
              ffi.Pointer<ffi.Pointer<ffi.Char>>)>>('strtod');
  late final _strtod = _strtodPtr.asFunction<
      double Function(
          ffi.Pointer<ffi.Char>, ffi.Pointer<ffi.Pointer<ffi.Char>>)>();

  double strtof(
    ffi.Pointer<ffi.Char> arg0,
    ffi.Pointer<ffi.Pointer<ffi.Char>> arg1,
  ) {
    return _strtof(
      arg0,
      arg1,
    );
  }

  late final _strtofPtr = _lookup<
      ffi.NativeFunction<
          ffi.Float Function(ffi.Pointer<ffi.Char>,
              ffi.Pointer<ffi.Pointer<ffi.Char>>)>>('strtof');
  late final _strtof = _strtofPtr.asFunction<
      double Function(
          ffi.Pointer<ffi.Char>, ffi.Pointer<ffi.Pointer<ffi.Char>>)>();

  int strtol(
    ffi.Pointer<ffi.Char> __str,
    ffi.Pointer<ffi.Pointer<ffi.Char>> __endptr,
    int __base,
  ) {
    return _strtol(
      __str,
      __endptr,
      __base,
    );
  }

  late final _strtolPtr = _lookup<
      ffi.NativeFunction<
          ffi.Long Function(ffi.Pointer<ffi.Char>,
              ffi.Pointer<ffi.Pointer<ffi.Char>>, ffi.Int)>>('strtol');
  late final _strtol = _strtolPtr.asFunction<
      int Function(
          ffi.Pointer<ffi.Char>, ffi.Pointer<ffi.Pointer<ffi.Char>>, int)>();

  int strtoll(
    ffi.Pointer<ffi.Char> __str,
    ffi.Pointer<ffi.Pointer<ffi.Char>> __endptr,
    int __base,
  ) {
    return _strtoll(
      __str,
      __endptr,
      __base,
    );
  }

  late final _strtollPtr = _lookup<
      ffi.NativeFunction<
          ffi.LongLong Function(ffi.Pointer<ffi.Char>,
              ffi.Pointer<ffi.Pointer<ffi.Char>>, ffi.Int)>>('strtoll');
  late final _strtoll = _strtollPtr.asFunction<
      int Function(
          ffi.Pointer<ffi.Char>, ffi.Pointer<ffi.Pointer<ffi.Char>>, int)>();

  int strtoul(
    ffi.Pointer<ffi.Char> __str,
    ffi.Pointer<ffi.Pointer<ffi.Char>> __endptr,
    int __base,
  ) {
    return _strtoul(
      __str,
      __endptr,
      __base,
    );
  }

  late final _strtoulPtr = _lookup<
      ffi.NativeFunction<
          ffi.UnsignedLong Function(ffi.Pointer<ffi.Char>,
              ffi.Pointer<ffi.Pointer<ffi.Char>>, ffi.Int)>>('strtoul');
  late final _strtoul = _strtoulPtr.asFunction<
      int Function(
          ffi.Pointer<ffi.Char>, ffi.Pointer<ffi.Pointer<ffi.Char>>, int)>();

  int strtoull(
    ffi.Pointer<ffi.Char> __str,
    ffi.Pointer<ffi.Pointer<ffi.Char>> __endptr,
    int __base,
  ) {
    return _strtoull(
      __str,
      __endptr,
      __base,
    );
  }

  late final _strtoullPtr = _lookup<
      ffi.NativeFunction<
          ffi.UnsignedLongLong Function(ffi.Pointer<ffi.Char>,
              ffi.Pointer<ffi.Pointer<ffi.Char>>, ffi.Int)>>('strtoull');
  late final _strtoull = _strtoullPtr.asFunction<
      int Function(
          ffi.Pointer<ffi.Char>, ffi.Pointer<ffi.Pointer<ffi.Char>>, int)>();

  int system(
    ffi.Pointer<ffi.Char> arg0,
  ) {
    return _system(
      arg0,
    );
  }

  late final _systemPtr =
      _lookup<ffi.NativeFunction<ffi.Int Function(ffi.Pointer<ffi.Char>)>>(
          'system');
  late final _system =
      _systemPtr.asFunction<int Function(ffi.Pointer<ffi.Char>)>();

  int wcstombs(
    ffi.Pointer<ffi.Char> arg0,
    ffi.Pointer<ffi.WChar> arg1,
    int arg2,
  ) {
    return _wcstombs(
      arg0,
      arg1,
      arg2,
    );
  }

  late final _wcstombsPtr = _lookup<
      ffi.NativeFunction<
          ffi.Size Function(ffi.Pointer<ffi.Char>, ffi.Pointer<ffi.WChar>,
              ffi.Size)>>('wcstombs');
  late final _wcstombs = _wcstombsPtr.asFunction<
      int Function(ffi.Pointer<ffi.Char>, ffi.Pointer<ffi.WChar>, int)>();

  int wctomb(
    ffi.Pointer<ffi.Char> arg0,
    int arg1,
  ) {
    return _wctomb(
      arg0,
      arg1,
    );
  }

  late final _wctombPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int Function(ffi.Pointer<ffi.Char>, ffi.WChar)>>('wctomb');
  late final _wctomb =
      _wctombPtr.asFunction<int Function(ffi.Pointer<ffi.Char>, int)>();

  void _Exit(
    int arg0,
  ) {
    return __Exit(
      arg0,
    );
  }

  late final __ExitPtr =
      _lookup<ffi.NativeFunction<ffi.Void Function(ffi.Int)>>('_Exit');
  late final __Exit = __ExitPtr.asFunction<void Function(int)>();

  int a64l(
    ffi.Pointer<ffi.Char> arg0,
  ) {
    return _a64l(
      arg0,
    );
  }

  late final _a64lPtr =
      _lookup<ffi.NativeFunction<ffi.Long Function(ffi.Pointer<ffi.Char>)>>(
          'a64l');
  late final _a64l = _a64lPtr.asFunction<int Function(ffi.Pointer<ffi.Char>)>();

  double drand48() {
    return _drand48();
  }

  late final _drand48Ptr =
      _lookup<ffi.NativeFunction<ffi.Double Function()>>('drand48');
  late final _drand48 = _drand48Ptr.asFunction<double Function()>();

  ffi.Pointer<ffi.Char> ecvt(
    double arg0,
    int arg1,
    ffi.Pointer<ffi.Int> arg2,
    ffi.Pointer<ffi.Int> arg3,
  ) {
    return _ecvt(
      arg0,
      arg1,
      arg2,
      arg3,
    );
  }

  late final _ecvtPtr = _lookup<
      ffi.NativeFunction<
          ffi.Pointer<ffi.Char> Function(ffi.Double, ffi.Int,
              ffi.Pointer<ffi.Int>, ffi.Pointer<ffi.Int>)>>('ecvt');
  late final _ecvt = _ecvtPtr.asFunction<
      ffi.Pointer<ffi.Char> Function(
          double, int, ffi.Pointer<ffi.Int>, ffi.Pointer<ffi.Int>)>();

  double erand48(
    ffi.Pointer<ffi.UnsignedShort> arg0,
  ) {
    return _erand48(
      arg0,
    );
  }

  late final _erand48Ptr = _lookup<
      ffi.NativeFunction<
          ffi.Double Function(ffi.Pointer<ffi.UnsignedShort>)>>('erand48');
  late final _erand48 =
      _erand48Ptr.asFunction<double Function(ffi.Pointer<ffi.UnsignedShort>)>();

  ffi.Pointer<ffi.Char> fcvt(
    double arg0,
    int arg1,
    ffi.Pointer<ffi.Int> arg2,
    ffi.Pointer<ffi.Int> arg3,
  ) {
    return _fcvt(
      arg0,
      arg1,
      arg2,
      arg3,
    );
  }

  late final _fcvtPtr = _lookup<
      ffi.NativeFunction<
          ffi.Pointer<ffi.Char> Function(ffi.Double, ffi.Int,
              ffi.Pointer<ffi.Int>, ffi.Pointer<ffi.Int>)>>('fcvt');
  late final _fcvt = _fcvtPtr.asFunction<
      ffi.Pointer<ffi.Char> Function(
          double, int, ffi.Pointer<ffi.Int>, ffi.Pointer<ffi.Int>)>();

  ffi.Pointer<ffi.Char> gcvt(
    double arg0,
    int arg1,
    ffi.Pointer<ffi.Char> arg2,
  ) {
    return _gcvt(
      arg0,
      arg1,
      arg2,
    );
  }

  late final _gcvtPtr = _lookup<
      ffi.NativeFunction<
          ffi.Pointer<ffi.Char> Function(
              ffi.Double, ffi.Int, ffi.Pointer<ffi.Char>)>>('gcvt');
  late final _gcvt = _gcvtPtr.asFunction<
      ffi.Pointer<ffi.Char> Function(double, int, ffi.Pointer<ffi.Char>)>();

  int getsubopt(
    ffi.Pointer<ffi.Pointer<ffi.Char>> arg0,
    ffi.Pointer<ffi.Pointer<ffi.Char>> arg1,
    ffi.Pointer<ffi.Pointer<ffi.Char>> arg2,
  ) {
    return _getsubopt(
      arg0,
      arg1,
      arg2,
    );
  }

  late final _getsuboptPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int Function(
              ffi.Pointer<ffi.Pointer<ffi.Char>>,
              ffi.Pointer<ffi.Pointer<ffi.Char>>,
              ffi.Pointer<ffi.Pointer<ffi.Char>>)>>('getsubopt');
  late final _getsubopt = _getsuboptPtr.asFunction<
      int Function(
          ffi.Pointer<ffi.Pointer<ffi.Char>>,
          ffi.Pointer<ffi.Pointer<ffi.Char>>,
          ffi.Pointer<ffi.Pointer<ffi.Char>>)>();

  int grantpt(
    int arg0,
  ) {
    return _grantpt(
      arg0,
    );
  }

  late final _grantptPtr =
      _lookup<ffi.NativeFunction<ffi.Int Function(ffi.Int)>>('grantpt');
  late final _grantpt = _grantptPtr.asFunction<int Function(int)>();

  ffi.Pointer<ffi.Char> initstate(
    int arg0,
    ffi.Pointer<ffi.Char> arg1,
    int arg2,
  ) {
    return _initstate(
      arg0,
      arg1,
      arg2,
    );
  }

  late final _initstatePtr = _lookup<
      ffi.NativeFunction<
          ffi.Pointer<ffi.Char> Function(
              ffi.UnsignedInt, ffi.Pointer<ffi.Char>, ffi.Size)>>('initstate');
  late final _initstate = _initstatePtr.asFunction<
      ffi.Pointer<ffi.Char> Function(int, ffi.Pointer<ffi.Char>, int)>();

  int jrand48(
    ffi.Pointer<ffi.UnsignedShort> arg0,
  ) {
    return _jrand48(
      arg0,
    );
  }

  late final _jrand48Ptr = _lookup<
      ffi.NativeFunction<
          ffi.Long Function(ffi.Pointer<ffi.UnsignedShort>)>>('jrand48');
  late final _jrand48 =
      _jrand48Ptr.asFunction<int Function(ffi.Pointer<ffi.UnsignedShort>)>();

  ffi.Pointer<ffi.Char> l64a(
    int arg0,
  ) {
    return _l64a(
      arg0,
    );
  }

  late final _l64aPtr =
      _lookup<ffi.NativeFunction<ffi.Pointer<ffi.Char> Function(ffi.Long)>>(
          'l64a');
  late final _l64a = _l64aPtr.asFunction<ffi.Pointer<ffi.Char> Function(int)>();

  void lcong48(
    ffi.Pointer<ffi.UnsignedShort> arg0,
  ) {
    return _lcong48(
      arg0,
    );
  }

  late final _lcong48Ptr = _lookup<
      ffi.NativeFunction<
          ffi.Void Function(ffi.Pointer<ffi.UnsignedShort>)>>('lcong48');
  late final _lcong48 =
      _lcong48Ptr.asFunction<void Function(ffi.Pointer<ffi.UnsignedShort>)>();

  int lrand48() {
    return _lrand48();
  }

  late final _lrand48Ptr =
      _lookup<ffi.NativeFunction<ffi.Long Function()>>('lrand48');
  late final _lrand48 = _lrand48Ptr.asFunction<int Function()>();

  ffi.Pointer<ffi.Char> mktemp(
    ffi.Pointer<ffi.Char> arg0,
  ) {
    return _mktemp(
      arg0,
    );
  }

  late final _mktempPtr = _lookup<
      ffi.NativeFunction<
          ffi.Pointer<ffi.Char> Function(ffi.Pointer<ffi.Char>)>>('mktemp');
  late final _mktemp = _mktempPtr
      .asFunction<ffi.Pointer<ffi.Char> Function(ffi.Pointer<ffi.Char>)>();

  int mkstemp(
    ffi.Pointer<ffi.Char> arg0,
  ) {
    return _mkstemp(
      arg0,
    );
  }

  late final _mkstempPtr =
      _lookup<ffi.NativeFunction<ffi.Int Function(ffi.Pointer<ffi.Char>)>>(
          'mkstemp');
  late final _mkstemp =
      _mkstempPtr.asFunction<int Function(ffi.Pointer<ffi.Char>)>();

  int mrand48() {
    return _mrand48();
  }

  late final _mrand48Ptr =
      _lookup<ffi.NativeFunction<ffi.Long Function()>>('mrand48');
  late final _mrand48 = _mrand48Ptr.asFunction<int Function()>();

  int nrand48(
    ffi.Pointer<ffi.UnsignedShort> arg0,
  ) {
    return _nrand48(
      arg0,
    );
  }

  late final _nrand48Ptr = _lookup<
      ffi.NativeFunction<
          ffi.Long Function(ffi.Pointer<ffi.UnsignedShort>)>>('nrand48');
  late final _nrand48 =
      _nrand48Ptr.asFunction<int Function(ffi.Pointer<ffi.UnsignedShort>)>();

  int posix_openpt(
    int arg0,
  ) {
    return _posix_openpt(
      arg0,
    );
  }

  late final _posix_openptPtr =
      _lookup<ffi.NativeFunction<ffi.Int Function(ffi.Int)>>('posix_openpt');
  late final _posix_openpt = _posix_openptPtr.asFunction<int Function(int)>();

  ffi.Pointer<ffi.Char> ptsname(
    int arg0,
  ) {
    return _ptsname(
      arg0,
    );
  }

  late final _ptsnamePtr =
      _lookup<ffi.NativeFunction<ffi.Pointer<ffi.Char> Function(ffi.Int)>>(
          'ptsname');
  late final _ptsname =
      _ptsnamePtr.asFunction<ffi.Pointer<ffi.Char> Function(int)>();

  int ptsname_r(
    int fildes,
    ffi.Pointer<ffi.Char> buffer,
    int buflen,
  ) {
    return _ptsname_r(
      fildes,
      buffer,
      buflen,
    );
  }

  late final _ptsname_rPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int Function(
              ffi.Int, ffi.Pointer<ffi.Char>, ffi.Size)>>('ptsname_r');
  late final _ptsname_r =
      _ptsname_rPtr.asFunction<int Function(int, ffi.Pointer<ffi.Char>, int)>();

  int putenv(
    ffi.Pointer<ffi.Char> arg0,
  ) {
    return _putenv(
      arg0,
    );
  }

  late final _putenvPtr =
      _lookup<ffi.NativeFunction<ffi.Int Function(ffi.Pointer<ffi.Char>)>>(
          'putenv');
  late final _putenv =
      _putenvPtr.asFunction<int Function(ffi.Pointer<ffi.Char>)>();

  int random() {
    return _random();
  }

  late final _randomPtr =
      _lookup<ffi.NativeFunction<ffi.Long Function()>>('random');
  late final _random = _randomPtr.asFunction<int Function()>();

  int rand_r(
    ffi.Pointer<ffi.UnsignedInt> arg0,
  ) {
    return _rand_r(
      arg0,
    );
  }

  late final _rand_rPtr = _lookup<
          ffi.NativeFunction<ffi.Int Function(ffi.Pointer<ffi.UnsignedInt>)>>(
      'rand_r');
  late final _rand_r =
      _rand_rPtr.asFunction<int Function(ffi.Pointer<ffi.UnsignedInt>)>();

  ffi.Pointer<ffi.Char> realpath(
    ffi.Pointer<ffi.Char> arg0,
    ffi.Pointer<ffi.Char> arg1,
  ) {
    return _realpath(
      arg0,
      arg1,
    );
  }

  late final _realpathPtr = _lookup<
      ffi.NativeFunction<
          ffi.Pointer<ffi.Char> Function(
              ffi.Pointer<ffi.Char>, ffi.Pointer<ffi.Char>)>>('realpath');
  late final _realpath = _realpathPtr.asFunction<
      ffi.Pointer<ffi.Char> Function(
          ffi.Pointer<ffi.Char>, ffi.Pointer<ffi.Char>)>();

  ffi.Pointer<ffi.UnsignedShort> seed48(
    ffi.Pointer<ffi.UnsignedShort> arg0,
  ) {
    return _seed48(
      arg0,
    );
  }

  late final _seed48Ptr = _lookup<
      ffi.NativeFunction<
          ffi.Pointer<ffi.UnsignedShort> Function(
              ffi.Pointer<ffi.UnsignedShort>)>>('seed48');
  late final _seed48 = _seed48Ptr.asFunction<
      ffi.Pointer<ffi.UnsignedShort> Function(
          ffi.Pointer<ffi.UnsignedShort>)>();

  int setenv(
    ffi.Pointer<ffi.Char> __name,
    ffi.Pointer<ffi.Char> __value,
    int __overwrite,
  ) {
    return _setenv(
      __name,
      __value,
      __overwrite,
    );
  }

  late final _setenvPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int Function(ffi.Pointer<ffi.Char>, ffi.Pointer<ffi.Char>,
              ffi.Int)>>('setenv');
  late final _setenv = _setenvPtr.asFunction<
      int Function(ffi.Pointer<ffi.Char>, ffi.Pointer<ffi.Char>, int)>();

  void setkey(
    ffi.Pointer<ffi.Char> arg0,
  ) {
    return _setkey(
      arg0,
    );
  }

  late final _setkeyPtr =
      _lookup<ffi.NativeFunction<ffi.Void Function(ffi.Pointer<ffi.Char>)>>(
          'setkey');
  late final _setkey =
      _setkeyPtr.asFunction<void Function(ffi.Pointer<ffi.Char>)>();

  ffi.Pointer<ffi.Char> setstate(
    ffi.Pointer<ffi.Char> arg0,
  ) {
    return _setstate(
      arg0,
    );
  }

  late final _setstatePtr = _lookup<
      ffi.NativeFunction<
          ffi.Pointer<ffi.Char> Function(ffi.Pointer<ffi.Char>)>>('setstate');
  late final _setstate = _setstatePtr
      .asFunction<ffi.Pointer<ffi.Char> Function(ffi.Pointer<ffi.Char>)>();

  void srand48(
    int arg0,
  ) {
    return _srand48(
      arg0,
    );
  }

  late final _srand48Ptr =
      _lookup<ffi.NativeFunction<ffi.Void Function(ffi.Long)>>('srand48');
  late final _srand48 = _srand48Ptr.asFunction<void Function(int)>();

  void srandom(
    int arg0,
  ) {
    return _srandom(
      arg0,
    );
  }

  late final _srandomPtr =
      _lookup<ffi.NativeFunction<ffi.Void Function(ffi.UnsignedInt)>>(
          'srandom');
  late final _srandom = _srandomPtr.asFunction<void Function(int)>();

  int unlockpt(
    int arg0,
  ) {
    return _unlockpt(
      arg0,
    );
  }

  late final _unlockptPtr =
      _lookup<ffi.NativeFunction<ffi.Int Function(ffi.Int)>>('unlockpt');
  late final _unlockpt = _unlockptPtr.asFunction<int Function(int)>();

  int unsetenv(
    ffi.Pointer<ffi.Char> arg0,
  ) {
    return _unsetenv(
      arg0,
    );
  }

  late final _unsetenvPtr =
      _lookup<ffi.NativeFunction<ffi.Int Function(ffi.Pointer<ffi.Char>)>>(
          'unsetenv');
  late final _unsetenv =
      _unsetenvPtr.asFunction<int Function(ffi.Pointer<ffi.Char>)>();

  int arc4random() {
    return _arc4random();
  }

  late final _arc4randomPtr =
      _lookup<ffi.NativeFunction<ffi.Uint32 Function()>>('arc4random');
  late final _arc4random = _arc4randomPtr.asFunction<int Function()>();

  void arc4random_addrandom(
    ffi.Pointer<ffi.UnsignedChar> arg0,
    int arg1,
  ) {
    return _arc4random_addrandom(
      arg0,
      arg1,
    );
  }

  late final _arc4random_addrandomPtr = _lookup<
      ffi.NativeFunction<
          ffi.Void Function(
              ffi.Pointer<ffi.UnsignedChar>, ffi.Int)>>('arc4random_addrandom');
  late final _arc4random_addrandom = _arc4random_addrandomPtr
      .asFunction<void Function(ffi.Pointer<ffi.UnsignedChar>, int)>();

  void arc4random_buf(
    ffi.Pointer<ffi.Void> __buf,
    int __nbytes,
  ) {
    return _arc4random_buf(
      __buf,
      __nbytes,
    );
  }

  late final _arc4random_bufPtr = _lookup<
          ffi
          .NativeFunction<ffi.Void Function(ffi.Pointer<ffi.Void>, ffi.Size)>>(
      'arc4random_buf');
  late final _arc4random_buf = _arc4random_bufPtr
      .asFunction<void Function(ffi.Pointer<ffi.Void>, int)>();

  void arc4random_stir() {
    return _arc4random_stir();
  }

  late final _arc4random_stirPtr =
      _lookup<ffi.NativeFunction<ffi.Void Function()>>('arc4random_stir');
  late final _arc4random_stir =
      _arc4random_stirPtr.asFunction<void Function()>();

  int arc4random_uniform(
    int __upper_bound,
  ) {
    return _arc4random_uniform(
      __upper_bound,
    );
  }

  late final _arc4random_uniformPtr =
      _lookup<ffi.NativeFunction<ffi.Uint32 Function(ffi.Uint32)>>(
          'arc4random_uniform');
  late final _arc4random_uniform =
      _arc4random_uniformPtr.asFunction<int Function(int)>();

  ffi.Pointer<ffi.Char> cgetcap(
    ffi.Pointer<ffi.Char> arg0,
    ffi.Pointer<ffi.Char> arg1,
    int arg2,
  ) {
    return _cgetcap(
      arg0,
      arg1,
      arg2,
    );
  }

  late final _cgetcapPtr = _lookup<
      ffi.NativeFunction<
          ffi.Pointer<ffi.Char> Function(ffi.Pointer<ffi.Char>,
              ffi.Pointer<ffi.Char>, ffi.Int)>>('cgetcap');
  late final _cgetcap = _cgetcapPtr.asFunction<
      ffi.Pointer<ffi.Char> Function(
          ffi.Pointer<ffi.Char>, ffi.Pointer<ffi.Char>, int)>();

  int cgetclose() {
    return _cgetclose();
  }

  late final _cgetclosePtr =
      _lookup<ffi.NativeFunction<ffi.Int Function()>>('cgetclose');
  late final _cgetclose = _cgetclosePtr.asFunction<int Function()>();

  int cgetent(
    ffi.Pointer<ffi.Pointer<ffi.Char>> arg0,
    ffi.Pointer<ffi.Pointer<ffi.Char>> arg1,
    ffi.Pointer<ffi.Char> arg2,
  ) {
    return _cgetent(
      arg0,
      arg1,
      arg2,
    );
  }

  late final _cgetentPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int Function(
              ffi.Pointer<ffi.Pointer<ffi.Char>>,
              ffi.Pointer<ffi.Pointer<ffi.Char>>,
              ffi.Pointer<ffi.Char>)>>('cgetent');
  late final _cgetent = _cgetentPtr.asFunction<
      int Function(ffi.Pointer<ffi.Pointer<ffi.Char>>,
          ffi.Pointer<ffi.Pointer<ffi.Char>>, ffi.Pointer<ffi.Char>)>();

  int cgetfirst(
    ffi.Pointer<ffi.Pointer<ffi.Char>> arg0,
    ffi.Pointer<ffi.Pointer<ffi.Char>> arg1,
  ) {
    return _cgetfirst(
      arg0,
      arg1,
    );
  }

  late final _cgetfirstPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int Function(ffi.Pointer<ffi.Pointer<ffi.Char>>,
              ffi.Pointer<ffi.Pointer<ffi.Char>>)>>('cgetfirst');
  late final _cgetfirst = _cgetfirstPtr.asFunction<
      int Function(ffi.Pointer<ffi.Pointer<ffi.Char>>,
          ffi.Pointer<ffi.Pointer<ffi.Char>>)>();

  int cgetmatch(
    ffi.Pointer<ffi.Char> arg0,
    ffi.Pointer<ffi.Char> arg1,
  ) {
    return _cgetmatch(
      arg0,
      arg1,
    );
  }

  late final _cgetmatchPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int Function(
              ffi.Pointer<ffi.Char>, ffi.Pointer<ffi.Char>)>>('cgetmatch');
  late final _cgetmatch = _cgetmatchPtr
      .asFunction<int Function(ffi.Pointer<ffi.Char>, ffi.Pointer<ffi.Char>)>();

  int cgetnext(
    ffi.Pointer<ffi.Pointer<ffi.Char>> arg0,
    ffi.Pointer<ffi.Pointer<ffi.Char>> arg1,
  ) {
    return _cgetnext(
      arg0,
      arg1,
    );
  }

  late final _cgetnextPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int Function(ffi.Pointer<ffi.Pointer<ffi.Char>>,
              ffi.Pointer<ffi.Pointer<ffi.Char>>)>>('cgetnext');
  late final _cgetnext = _cgetnextPtr.asFunction<
      int Function(ffi.Pointer<ffi.Pointer<ffi.Char>>,
          ffi.Pointer<ffi.Pointer<ffi.Char>>)>();

  int cgetnum(
    ffi.Pointer<ffi.Char> arg0,
    ffi.Pointer<ffi.Char> arg1,
    ffi.Pointer<ffi.Long> arg2,
  ) {
    return _cgetnum(
      arg0,
      arg1,
      arg2,
    );
  }

  late final _cgetnumPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int Function(ffi.Pointer<ffi.Char>, ffi.Pointer<ffi.Char>,
              ffi.Pointer<ffi.Long>)>>('cgetnum');
  late final _cgetnum = _cgetnumPtr.asFunction<
      int Function(ffi.Pointer<ffi.Char>, ffi.Pointer<ffi.Char>,
          ffi.Pointer<ffi.Long>)>();

  int cgetset(
    ffi.Pointer<ffi.Char> arg0,
  ) {
    return _cgetset(
      arg0,
    );
  }

  late final _cgetsetPtr =
      _lookup<ffi.NativeFunction<ffi.Int Function(ffi.Pointer<ffi.Char>)>>(
          'cgetset');
  late final _cgetset =
      _cgetsetPtr.asFunction<int Function(ffi.Pointer<ffi.Char>)>();

  int cgetstr(
    ffi.Pointer<ffi.Char> arg0,
    ffi.Pointer<ffi.Char> arg1,
    ffi.Pointer<ffi.Pointer<ffi.Char>> arg2,
  ) {
    return _cgetstr(
      arg0,
      arg1,
      arg2,
    );
  }

  late final _cgetstrPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int Function(ffi.Pointer<ffi.Char>, ffi.Pointer<ffi.Char>,
              ffi.Pointer<ffi.Pointer<ffi.Char>>)>>('cgetstr');
  late final _cgetstr = _cgetstrPtr.asFunction<
      int Function(ffi.Pointer<ffi.Char>, ffi.Pointer<ffi.Char>,
          ffi.Pointer<ffi.Pointer<ffi.Char>>)>();

  int cgetustr(
    ffi.Pointer<ffi.Char> arg0,
    ffi.Pointer<ffi.Char> arg1,
    ffi.Pointer<ffi.Pointer<ffi.Char>> arg2,
  ) {
    return _cgetustr(
      arg0,
      arg1,
      arg2,
    );
  }

  late final _cgetustrPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int Function(ffi.Pointer<ffi.Char>, ffi.Pointer<ffi.Char>,
              ffi.Pointer<ffi.Pointer<ffi.Char>>)>>('cgetustr');
  late final _cgetustr = _cgetustrPtr.asFunction<
      int Function(ffi.Pointer<ffi.Char>, ffi.Pointer<ffi.Char>,
          ffi.Pointer<ffi.Pointer<ffi.Char>>)>();

  int daemon(
    int arg0,
    int arg1,
  ) {
    return _daemon(
      arg0,
      arg1,
    );
  }

  late final _daemonPtr =
      _lookup<ffi.NativeFunction<ffi.Int Function(ffi.Int, ffi.Int)>>('daemon');
  late final _daemon = _daemonPtr.asFunction<int Function(int, int)>();

  ffi.Pointer<ffi.Char> devname(
    int arg0,
    int arg1,
  ) {
    return _devname(
      arg0,
      arg1,
    );
  }

  late final _devnamePtr = _lookup<
          ffi.NativeFunction<ffi.Pointer<ffi.Char> Function(dev_t, mode_t)>>(
      'devname');
  late final _devname =
      _devnamePtr.asFunction<ffi.Pointer<ffi.Char> Function(int, int)>();

  ffi.Pointer<ffi.Char> devname_r(
    int arg0,
    int arg1,
    ffi.Pointer<ffi.Char> buf,
    int len,
  ) {
    return _devname_r(
      arg0,
      arg1,
      buf,
      len,
    );
  }

  late final _devname_rPtr = _lookup<
      ffi.NativeFunction<
          ffi.Pointer<ffi.Char> Function(
              dev_t, mode_t, ffi.Pointer<ffi.Char>, ffi.Int)>>('devname_r');
  late final _devname_r = _devname_rPtr.asFunction<
      ffi.Pointer<ffi.Char> Function(int, int, ffi.Pointer<ffi.Char>, int)>();

  ffi.Pointer<ffi.Char> getbsize(
    ffi.Pointer<ffi.Int> arg0,
    ffi.Pointer<ffi.Long> arg1,
  ) {
    return _getbsize(
      arg0,
      arg1,
    );
  }

  late final _getbsizePtr = _lookup<
      ffi.NativeFunction<
          ffi.Pointer<ffi.Char> Function(
              ffi.Pointer<ffi.Int>, ffi.Pointer<ffi.Long>)>>('getbsize');
  late final _getbsize = _getbsizePtr.asFunction<
      ffi.Pointer<ffi.Char> Function(
          ffi.Pointer<ffi.Int>, ffi.Pointer<ffi.Long>)>();

  int getloadavg(
    ffi.Pointer<ffi.Double> arg0,
    int arg1,
  ) {
    return _getloadavg(
      arg0,
      arg1,
    );
  }

  late final _getloadavgPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int Function(ffi.Pointer<ffi.Double>, ffi.Int)>>('getloadavg');
  late final _getloadavg =
      _getloadavgPtr.asFunction<int Function(ffi.Pointer<ffi.Double>, int)>();

  ffi.Pointer<ffi.Char> getprogname() {
    return _getprogname();
  }

  late final _getprognamePtr =
      _lookup<ffi.NativeFunction<ffi.Pointer<ffi.Char> Function()>>(
          'getprogname');
  late final _getprogname =
      _getprognamePtr.asFunction<ffi.Pointer<ffi.Char> Function()>();

  void setprogname(
    ffi.Pointer<ffi.Char> arg0,
  ) {
    return _setprogname(
      arg0,
    );
  }

  late final _setprognamePtr =
      _lookup<ffi.NativeFunction<ffi.Void Function(ffi.Pointer<ffi.Char>)>>(
          'setprogname');
  late final _setprogname =
      _setprognamePtr.asFunction<void Function(ffi.Pointer<ffi.Char>)>();

  int heapsort(
    ffi.Pointer<ffi.Void> __base,
    int __nel,
    int __width,
    ffi.Pointer<
            ffi.NativeFunction<
                ffi.Int Function(ffi.Pointer<ffi.Void>, ffi.Pointer<ffi.Void>)>>
        __compar,
  ) {
    return _heapsort(
      __base,
      __nel,
      __width,
      __compar,
    );
  }

  late final _heapsortPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int Function(
              ffi.Pointer<ffi.Void>,
              ffi.Size,
              ffi.Size,
              ffi.Pointer<
                  ffi.NativeFunction<
                      ffi.Int Function(ffi.Pointer<ffi.Void>,
                          ffi.Pointer<ffi.Void>)>>)>>('heapsort');
  late final _heapsort = _heapsortPtr.asFunction<
      int Function(
          ffi.Pointer<ffi.Void>,
          int,
          int,
          ffi.Pointer<
              ffi.NativeFunction<
                  ffi.Int Function(
                      ffi.Pointer<ffi.Void>, ffi.Pointer<ffi.Void>)>>)>();

  int mergesort(
    ffi.Pointer<ffi.Void> __base,
    int __nel,
    int __width,
    ffi.Pointer<
            ffi.NativeFunction<
                ffi.Int Function(ffi.Pointer<ffi.Void>, ffi.Pointer<ffi.Void>)>>
        __compar,
  ) {
    return _mergesort(
      __base,
      __nel,
      __width,
      __compar,
    );
  }

  late final _mergesortPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int Function(
              ffi.Pointer<ffi.Void>,
              ffi.Size,
              ffi.Size,
              ffi.Pointer<
                  ffi.NativeFunction<
                      ffi.Int Function(ffi.Pointer<ffi.Void>,
                          ffi.Pointer<ffi.Void>)>>)>>('mergesort');
  late final _mergesort = _mergesortPtr.asFunction<
      int Function(
          ffi.Pointer<ffi.Void>,
          int,
          int,
          ffi.Pointer<
              ffi.NativeFunction<
                  ffi.Int Function(
                      ffi.Pointer<ffi.Void>, ffi.Pointer<ffi.Void>)>>)>();

  void psort(
    ffi.Pointer<ffi.Void> __base,
    int __nel,
    int __width,
    ffi.Pointer<
            ffi.NativeFunction<
                ffi.Int Function(ffi.Pointer<ffi.Void>, ffi.Pointer<ffi.Void>)>>
        __compar,
  ) {
    return _psort(
      __base,
      __nel,
      __width,
      __compar,
    );
  }

  late final _psortPtr = _lookup<
      ffi.NativeFunction<
          ffi.Void Function(
              ffi.Pointer<ffi.Void>,
              ffi.Size,
              ffi.Size,
              ffi.Pointer<
                  ffi.NativeFunction<
                      ffi.Int Function(ffi.Pointer<ffi.Void>,
                          ffi.Pointer<ffi.Void>)>>)>>('psort');
  late final _psort = _psortPtr.asFunction<
      void Function(
          ffi.Pointer<ffi.Void>,
          int,
          int,
          ffi.Pointer<
              ffi.NativeFunction<
                  ffi.Int Function(
                      ffi.Pointer<ffi.Void>, ffi.Pointer<ffi.Void>)>>)>();

  void psort_r(
    ffi.Pointer<ffi.Void> __base,
    int __nel,
    int __width,
    ffi.Pointer<ffi.Void> arg3,
    ffi.Pointer<
            ffi.NativeFunction<
                ffi.Int Function(ffi.Pointer<ffi.Void>, ffi.Pointer<ffi.Void>,
                    ffi.Pointer<ffi.Void>)>>
        __compar,
  ) {
    return _psort_r(
      __base,
      __nel,
      __width,
      arg3,
      __compar,
    );
  }

  late final _psort_rPtr = _lookup<
      ffi.NativeFunction<
          ffi.Void Function(
              ffi.Pointer<ffi.Void>,
              ffi.Size,
              ffi.Size,
              ffi.Pointer<ffi.Void>,
              ffi.Pointer<
                  ffi.NativeFunction<
                      ffi.Int Function(
                          ffi.Pointer<ffi.Void>,
                          ffi.Pointer<ffi.Void>,
                          ffi.Pointer<ffi.Void>)>>)>>('psort_r');
  late final _psort_r = _psort_rPtr.asFunction<
      void Function(
          ffi.Pointer<ffi.Void>,
          int,
          int,
          ffi.Pointer<ffi.Void>,
          ffi.Pointer<
              ffi.NativeFunction<
                  ffi.Int Function(ffi.Pointer<ffi.Void>, ffi.Pointer<ffi.Void>,
                      ffi.Pointer<ffi.Void>)>>)>();

  void qsort_r(
    ffi.Pointer<ffi.Void> __base,
    int __nel,
    int __width,
    ffi.Pointer<ffi.Void> arg3,
    ffi.Pointer<
            ffi.NativeFunction<
                ffi.Int Function(ffi.Pointer<ffi.Void>, ffi.Pointer<ffi.Void>,
                    ffi.Pointer<ffi.Void>)>>
        __compar,
  ) {
    return _qsort_r(
      __base,
      __nel,
      __width,
      arg3,
      __compar,
    );
  }

  late final _qsort_rPtr = _lookup<
      ffi.NativeFunction<
          ffi.Void Function(
              ffi.Pointer<ffi.Void>,
              ffi.Size,
              ffi.Size,
              ffi.Pointer<ffi.Void>,
              ffi.Pointer<
                  ffi.NativeFunction<
                      ffi.Int Function(
                          ffi.Pointer<ffi.Void>,
                          ffi.Pointer<ffi.Void>,
                          ffi.Pointer<ffi.Void>)>>)>>('qsort_r');
  late final _qsort_r = _qsort_rPtr.asFunction<
      void Function(
          ffi.Pointer<ffi.Void>,
          int,
          int,
          ffi.Pointer<ffi.Void>,
          ffi.Pointer<
              ffi.NativeFunction<
                  ffi.Int Function(ffi.Pointer<ffi.Void>, ffi.Pointer<ffi.Void>,
                      ffi.Pointer<ffi.Void>)>>)>();

  int radixsort(
    ffi.Pointer<ffi.Pointer<ffi.UnsignedChar>> __base,
    int __nel,
    ffi.Pointer<ffi.UnsignedChar> __table,
    int __endbyte,
  ) {
    return _radixsort(
      __base,
      __nel,
      __table,
      __endbyte,
    );
  }

  late final _radixsortPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int Function(ffi.Pointer<ffi.Pointer<ffi.UnsignedChar>>, ffi.Int,
              ffi.Pointer<ffi.UnsignedChar>, ffi.UnsignedInt)>>('radixsort');
  late final _radixsort = _radixsortPtr.asFunction<
      int Function(ffi.Pointer<ffi.Pointer<ffi.UnsignedChar>>, int,
          ffi.Pointer<ffi.UnsignedChar>, int)>();

  int rpmatch(
    ffi.Pointer<ffi.Char> arg0,
  ) {
    return _rpmatch(
      arg0,
    );
  }

  late final _rpmatchPtr =
      _lookup<ffi.NativeFunction<ffi.Int Function(ffi.Pointer<ffi.Char>)>>(
          'rpmatch');
  late final _rpmatch =
      _rpmatchPtr.asFunction<int Function(ffi.Pointer<ffi.Char>)>();

  int sradixsort(
    ffi.Pointer<ffi.Pointer<ffi.UnsignedChar>> __base,
    int __nel,
    ffi.Pointer<ffi.UnsignedChar> __table,
    int __endbyte,
  ) {
    return _sradixsort(
      __base,
      __nel,
      __table,
      __endbyte,
    );
  }

  late final _sradixsortPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int Function(ffi.Pointer<ffi.Pointer<ffi.UnsignedChar>>, ffi.Int,
              ffi.Pointer<ffi.UnsignedChar>, ffi.UnsignedInt)>>('sradixsort');
  late final _sradixsort = _sradixsortPtr.asFunction<
      int Function(ffi.Pointer<ffi.Pointer<ffi.UnsignedChar>>, int,
          ffi.Pointer<ffi.UnsignedChar>, int)>();

  void sranddev() {
    return _sranddev();
  }

  late final _sranddevPtr =
      _lookup<ffi.NativeFunction<ffi.Void Function()>>('sranddev');
  late final _sranddev = _sranddevPtr.asFunction<void Function()>();

  void srandomdev() {
    return _srandomdev();
  }

  late final _srandomdevPtr =
      _lookup<ffi.NativeFunction<ffi.Void Function()>>('srandomdev');
  late final _srandomdev = _srandomdevPtr.asFunction<void Function()>();

  ffi.Pointer<ffi.Void> reallocf(
    ffi.Pointer<ffi.Void> __ptr,
    int __size,
  ) {
    return _reallocf(
      __ptr,
      __size,
    );
  }

  late final _reallocfPtr = _lookup<
      ffi.NativeFunction<
          ffi.Pointer<ffi.Void> Function(
              ffi.Pointer<ffi.Void>, ffi.Size)>>('reallocf');
  late final _reallocf = _reallocfPtr
      .asFunction<ffi.Pointer<ffi.Void> Function(ffi.Pointer<ffi.Void>, int)>();

  int strtonum(
    ffi.Pointer<ffi.Char> __numstr,
    int __minval,
    int __maxval,
    ffi.Pointer<ffi.Pointer<ffi.Char>> __errstrp,
  ) {
    return _strtonum(
      __numstr,
      __minval,
      __maxval,
      __errstrp,
    );
  }

  late final _strtonumPtr = _lookup<
      ffi.NativeFunction<
          ffi.LongLong Function(ffi.Pointer<ffi.Char>, ffi.LongLong,
              ffi.LongLong, ffi.Pointer<ffi.Pointer<ffi.Char>>)>>('strtonum');
  late final _strtonum = _strtonumPtr.asFunction<
      int Function(ffi.Pointer<ffi.Char>, int, int,
          ffi.Pointer<ffi.Pointer<ffi.Char>>)>();

  int strtoq(
    ffi.Pointer<ffi.Char> __str,
    ffi.Pointer<ffi.Pointer<ffi.Char>> __endptr,
    int __base,
  ) {
    return _strtoq(
      __str,
      __endptr,
      __base,
    );
  }

  late final _strtoqPtr = _lookup<
      ffi.NativeFunction<
          ffi.LongLong Function(ffi.Pointer<ffi.Char>,
              ffi.Pointer<ffi.Pointer<ffi.Char>>, ffi.Int)>>('strtoq');
  late final _strtoq = _strtoqPtr.asFunction<
      int Function(
          ffi.Pointer<ffi.Char>, ffi.Pointer<ffi.Pointer<ffi.Char>>, int)>();

  int strtouq(
    ffi.Pointer<ffi.Char> __str,
    ffi.Pointer<ffi.Pointer<ffi.Char>> __endptr,
    int __base,
  ) {
    return _strtouq(
      __str,
      __endptr,
      __base,
    );
  }

  late final _strtouqPtr = _lookup<
      ffi.NativeFunction<
          ffi.UnsignedLongLong Function(ffi.Pointer<ffi.Char>,
              ffi.Pointer<ffi.Pointer<ffi.Char>>, ffi.Int)>>('strtouq');
  late final _strtouq = _strtouqPtr.asFunction<
      int Function(
          ffi.Pointer<ffi.Char>, ffi.Pointer<ffi.Pointer<ffi.Char>>, int)>();

  late final ffi.Pointer<ffi.Pointer<ffi.Char>> _suboptarg =
      _lookup<ffi.Pointer<ffi.Char>>('suboptarg');

  ffi.Pointer<ffi.Char> get suboptarg => _suboptarg.value;

  set suboptarg(ffi.Pointer<ffi.Char> value) => _suboptarg.value = value;

  /// # Safety
  ffi.Pointer<ffi.Char> FFI_AuthV1_GetToken(
    ffi.Pointer<ffi.Char> auth_url,
    ffi.Pointer<ffi.Char> device_info,
    ffi.Pointer<ffi.Uint8> private_key,
    ffi.Pointer<ffi.Char> domain,
  ) {
    return _FFI_AuthV1_GetToken(
      auth_url,
      device_info,
      private_key,
      domain,
    );
  }

  late final _FFI_AuthV1_GetTokenPtr = _lookup<
      ffi.NativeFunction<
          ffi.Pointer<ffi.Char> Function(
              ffi.Pointer<ffi.Char>,
              ffi.Pointer<ffi.Char>,
              ffi.Pointer<ffi.Uint8>,
              ffi.Pointer<ffi.Char>)>>('FFI_AuthV1_GetToken');
  late final _FFI_AuthV1_GetToken = _FFI_AuthV1_GetTokenPtr.asFunction<
      ffi.Pointer<ffi.Char> Function(
          ffi.Pointer<ffi.Char>,
          ffi.Pointer<ffi.Char>,
          ffi.Pointer<ffi.Uint8>,
          ffi.Pointer<ffi.Char>)>();

  /// # Safety
  ChatIOAuthPtr FFI_AuthV2_Create(
    ffi.Pointer<ffi.Char> proxy,
    ffi.Pointer<ffi.Char> auth_url,
    ffi.Pointer<ffi.Uint8> tmp_private_key,
    ffi.Pointer<ffi.Char> domain,
    ffi.Pointer<ffi.Char> phone,
    ffi.Pointer<ffi.Char> device_info,
  ) {
    return _FFI_AuthV2_Create(
      proxy,
      auth_url,
      tmp_private_key,
      domain,
      phone,
      device_info,
    );
  }

  late final _FFI_AuthV2_CreatePtr = _lookup<
      ffi.NativeFunction<
          ChatIOAuthPtr Function(
              ffi.Pointer<ffi.Char>,
              ffi.Pointer<ffi.Char>,
              ffi.Pointer<ffi.Uint8>,
              ffi.Pointer<ffi.Char>,
              ffi.Pointer<ffi.Char>,
              ffi.Pointer<ffi.Char>)>>('FFI_AuthV2_Create');
  late final _FFI_AuthV2_Create = _FFI_AuthV2_CreatePtr.asFunction<
      ChatIOAuthPtr Function(
          ffi.Pointer<ffi.Char>,
          ffi.Pointer<ffi.Char>,
          ffi.Pointer<ffi.Uint8>,
          ffi.Pointer<ffi.Char>,
          ffi.Pointer<ffi.Char>,
          ffi.Pointer<ffi.Char>)>();

  /// # Safety
  ffi.Pointer<ffi.Char> FFI_AuthV2_GetToken(
    ChatIOAuthPtr auth_ref,
    ffi.Pointer<ffi.Char> msg_code,
  ) {
    return _FFI_AuthV2_GetToken(
      auth_ref,
      msg_code,
    );
  }

  late final _FFI_AuthV2_GetTokenPtr = _lookup<
      ffi.NativeFunction<
          ffi.Pointer<ffi.Char> Function(
              ChatIOAuthPtr, ffi.Pointer<ffi.Char>)>>('FFI_AuthV2_GetToken');
  late final _FFI_AuthV2_GetToken = _FFI_AuthV2_GetTokenPtr.asFunction<
      ffi.Pointer<ffi.Char> Function(ChatIOAuthPtr, ffi.Pointer<ffi.Char>)>();

  /// # Safety
  int FFI_AuthV2_PrivateKey(
    ChatIOAuthPtr auth_ref,
    ffi.Pointer<ffi.Pointer<ffi.Uint8>> res_private_key,
  ) {
    return _FFI_AuthV2_PrivateKey(
      auth_ref,
      res_private_key,
    );
  }

  late final _FFI_AuthV2_PrivateKeyPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(ChatIOAuthPtr,
              ffi.Pointer<ffi.Pointer<ffi.Uint8>>)>>('FFI_AuthV2_PrivateKey');
  late final _FFI_AuthV2_PrivateKey = _FFI_AuthV2_PrivateKeyPtr.asFunction<
      int Function(ChatIOAuthPtr, ffi.Pointer<ffi.Pointer<ffi.Uint8>>)>();

  /// # Safety
  void FFI_AuthV2_Delete(
    ChatIOAuthPtr auth_ref,
  ) {
    return _FFI_AuthV2_Delete(
      auth_ref,
    );
  }

  late final _FFI_AuthV2_DeletePtr =
      _lookup<ffi.NativeFunction<ffi.Void Function(ChatIOAuthPtr)>>(
          'FFI_AuthV2_Delete');
  late final _FFI_AuthV2_Delete =
      _FFI_AuthV2_DeletePtr.asFunction<void Function(ChatIOAuthPtr)>();

  /// # Safety
  ffi.Pointer<ffi.Char> FFI_AuthV2_QRCodeInfo(
    ffi.Pointer<ffi.Char> auth_url,
    ffi.Pointer<ffi.Char> uuid,
    ffi.Pointer<ffi.Uint8> private_key,
  ) {
    return _FFI_AuthV2_QRCodeInfo(
      auth_url,
      uuid,
      private_key,
    );
  }

  late final _FFI_AuthV2_QRCodeInfoPtr = _lookup<
      ffi.NativeFunction<
          ffi.Pointer<ffi.Char> Function(
              ffi.Pointer<ffi.Char>,
              ffi.Pointer<ffi.Char>,
              ffi.Pointer<ffi.Uint8>)>>('FFI_AuthV2_QRCodeInfo');
  late final _FFI_AuthV2_QRCodeInfo = _FFI_AuthV2_QRCodeInfoPtr.asFunction<
      ffi.Pointer<ffi.Char> Function(ffi.Pointer<ffi.Char>,
          ffi.Pointer<ffi.Char>, ffi.Pointer<ffi.Uint8>)>();

  /// # Safety
  int FFI_AuthV2_QRCodeScan(
    ffi.Pointer<ffi.Char> auth_url,
    ffi.Pointer<ffi.Char> uuid,
    ffi.Pointer<ffi.Char> qrcode_public_key_bs64,
    ffi.Pointer<ffi.Uint8> private_key,
    ffi.Pointer<ffi.Uint8> data_ptr,
    int data_len,
  ) {
    return _FFI_AuthV2_QRCodeScan(
      auth_url,
      uuid,
      qrcode_public_key_bs64,
      private_key,
      data_ptr,
      data_len,
    );
  }

  late final _FFI_AuthV2_QRCodeScanPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(
              ffi.Pointer<ffi.Char>,
              ffi.Pointer<ffi.Char>,
              ffi.Pointer<ffi.Char>,
              ffi.Pointer<ffi.Uint8>,
              ffi.Pointer<ffi.Uint8>,
              ffi.Uint32)>>('FFI_AuthV2_QRCodeScan');
  late final _FFI_AuthV2_QRCodeScan = _FFI_AuthV2_QRCodeScanPtr.asFunction<
      int Function(
          ffi.Pointer<ffi.Char>,
          ffi.Pointer<ffi.Char>,
          ffi.Pointer<ffi.Char>,
          ffi.Pointer<ffi.Uint8>,
          ffi.Pointer<ffi.Uint8>,
          int)>();

  /// # Safety
  ///
  /// v3 auth 基于加密的协商认证
  ffi.Pointer<ffi.Char> FFI_AuthV3_GetToken(
    ffi.Pointer<ffi.Char> auth_url,
    ffi.Pointer<ffi.Char> extend_data,
    ffi.Pointer<ffi.Char> device_code,
    ffi.Pointer<ffi.Uint8> private_key,
    int action,
  ) {
    return _FFI_AuthV3_GetToken(
      auth_url,
      extend_data,
      device_code,
      private_key,
      action,
    );
  }

  late final _FFI_AuthV3_GetTokenPtr = _lookup<
      ffi.NativeFunction<
          ffi.Pointer<ffi.Char> Function(
              ffi.Pointer<ffi.Char>,
              ffi.Pointer<ffi.Char>,
              ffi.Pointer<ffi.Char>,
              ffi.Pointer<ffi.Uint8>,
              ffi.Uint8)>>('FFI_AuthV3_GetToken');
  late final _FFI_AuthV3_GetToken = _FFI_AuthV3_GetTokenPtr.asFunction<
      ffi.Pointer<ffi.Char> Function(
          ffi.Pointer<ffi.Char>,
          ffi.Pointer<ffi.Char>,
          ffi.Pointer<ffi.Char>,
          ffi.Pointer<ffi.Uint8>,
          int)>();

  /// # Safety
  ///
  /// 设置代理
  void FFI_Chatio_Proxy(
    ffi.Pointer<ffi.Char> proxy,
  ) {
    return _FFI_Chatio_Proxy(
      proxy,
    );
  }

  late final _FFI_Chatio_ProxyPtr =
      _lookup<ffi.NativeFunction<ffi.Void Function(ffi.Pointer<ffi.Char>)>>(
          'FFI_Chatio_Proxy');
  late final _FFI_Chatio_Proxy =
      _FFI_Chatio_ProxyPtr.asFunction<void Function(ffi.Pointer<ffi.Char>)>();

  /// # Safety
  ///
  /// 创建加密通道
  ChatIOFfiChatioPtr FFI_Chatio_New(
    ffi.Pointer<ffi.Char> data_path,
    ffi.Pointer<ffi.Char> server_url,
    ffi.Pointer<ffi.Uint8> private_key,
    ffi.Pointer<ffi.Char> domain,
    ffi.Pointer<ffi.Char> token,
    ChatIOMessageCallBack msg_cb,
    ChatIOMessageErrorCallBack msg_err_cb,
    ChatIOUnauthorizedCallBack unauth_cb,
  ) {
    return _FFI_Chatio_New(
      data_path,
      server_url,
      private_key,
      domain,
      token,
      msg_cb,
      msg_err_cb,
      unauth_cb,
    );
  }

  late final _FFI_Chatio_NewPtr = _lookup<
      ffi.NativeFunction<
          ChatIOFfiChatioPtr Function(
              ffi.Pointer<ffi.Char>,
              ffi.Pointer<ffi.Char>,
              ffi.Pointer<ffi.Uint8>,
              ffi.Pointer<ffi.Char>,
              ffi.Pointer<ffi.Char>,
              ChatIOMessageCallBack,
              ChatIOMessageErrorCallBack,
              ChatIOUnauthorizedCallBack)>>('FFI_Chatio_New');
  late final _FFI_Chatio_New = _FFI_Chatio_NewPtr.asFunction<
      ChatIOFfiChatioPtr Function(
          ffi.Pointer<ffi.Char>,
          ffi.Pointer<ffi.Char>,
          ffi.Pointer<ffi.Uint8>,
          ffi.Pointer<ffi.Char>,
          ffi.Pointer<ffi.Char>,
          ChatIOMessageCallBack,
          ChatIOMessageErrorCallBack,
          ChatIOUnauthorizedCallBack)>();

  /// # Safety
  ///
  /// 获取加密通道的设备名字
  ffi.Pointer<ffi.Char> FFI_Chatio_GetDeviceName(
    ChatIOFfiChatioPtr ch_ref,
  ) {
    return _FFI_Chatio_GetDeviceName(
      ch_ref,
    );
  }

  late final _FFI_Chatio_GetDeviceNamePtr = _lookup<
          ffi
          .NativeFunction<ffi.Pointer<ffi.Char> Function(ChatIOFfiChatioPtr)>>(
      'FFI_Chatio_GetDeviceName');
  late final _FFI_Chatio_GetDeviceName = _FFI_Chatio_GetDeviceNamePtr
      .asFunction<ffi.Pointer<ffi.Char> Function(ChatIOFfiChatioPtr)>();

  /// # Safety
  ///
  /// 连接加密通道
  int FFI_Chatio_Connect(
    ChatIOFfiChatioPtr ch_ref,
  ) {
    return _FFI_Chatio_Connect(
      ch_ref,
    );
  }

  late final _FFI_Chatio_ConnectPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ChatIOFfiChatioPtr)>>(
          'FFI_Chatio_Connect');
  late final _FFI_Chatio_Connect =
      _FFI_Chatio_ConnectPtr.asFunction<int Function(ChatIOFfiChatioPtr)>();

  /// # Safety
  ///
  /// 清除加密通道的会话
  int FFI_Chatio_CleanSession(
    ChatIOFfiChatioPtr ch_ref,
    ffi.Pointer<ffi.Char> name,
    int device_id,
  ) {
    return _FFI_Chatio_CleanSession(
      ch_ref,
      name,
      device_id,
    );
  }

  late final _FFI_Chatio_CleanSessionPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(ChatIOFfiChatioPtr, ffi.Pointer<ffi.Char>,
              ffi.Uint32)>>('FFI_Chatio_CleanSession');
  late final _FFI_Chatio_CleanSession = _FFI_Chatio_CleanSessionPtr.asFunction<
      int Function(ChatIOFfiChatioPtr, ffi.Pointer<ffi.Char>, int)>();

  /// # Safety
  ///
  /// 使用加密通道发送消息
  ffi.Pointer<ffi.Char> FFI_Chatio_SendMessage(
    ChatIOFfiChatioPtr ch_ref,
    int push_type,
    ffi.Pointer<ffi.Pointer<ffi.Char>> to_list,
    int len,
    ffi.Pointer<ffi.Uint8> message_ptr,
    int message_len,
  ) {
    return _FFI_Chatio_SendMessage(
      ch_ref,
      push_type,
      to_list,
      len,
      message_ptr,
      message_len,
    );
  }

  late final _FFI_Chatio_SendMessagePtr = _lookup<
      ffi.NativeFunction<
          ffi.Pointer<ffi.Char> Function(
              ChatIOFfiChatioPtr,
              ffi.Uint16,
              ffi.Pointer<ffi.Pointer<ffi.Char>>,
              ffi.Uint32,
              ffi.Pointer<ffi.Uint8>,
              ffi.Uint32)>>('FFI_Chatio_SendMessage');
  late final _FFI_Chatio_SendMessage = _FFI_Chatio_SendMessagePtr.asFunction<
      ffi.Pointer<ffi.Char> Function(
          ChatIOFfiChatioPtr,
          int,
          ffi.Pointer<ffi.Pointer<ffi.Char>>,
          int,
          ffi.Pointer<ffi.Uint8>,
          int)>();

  /// # Safety
  ///
  /// 接收加密通道的消息
  int FFI_Chatio_RecvMessage(
    ChatIOFfiChatioPtr ch_ref,
  ) {
    return _FFI_Chatio_RecvMessage(
      ch_ref,
    );
  }

  late final _FFI_Chatio_RecvMessagePtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ChatIOFfiChatioPtr)>>(
          'FFI_Chatio_RecvMessage');
  late final _FFI_Chatio_RecvMessage =
      _FFI_Chatio_RecvMessagePtr.asFunction<int Function(ChatIOFfiChatioPtr)>();

  /// # Safety
  ///
  /// 释放加密通道
  void FFI_Chatio_Delete(
    ChatIOFfiChatioPtr ch_ref,
  ) {
    return _FFI_Chatio_Delete(
      ch_ref,
    );
  }

  late final _FFI_Chatio_DeletePtr =
      _lookup<ffi.NativeFunction<ffi.Void Function(ChatIOFfiChatioPtr)>>(
          'FFI_Chatio_Delete');
  late final _FFI_Chatio_Delete =
      _FFI_Chatio_DeletePtr.asFunction<void Function(ChatIOFfiChatioPtr)>();

  /// # Safety
  ///
  /// This function
  int FFI_Group_Create(
    ffi.Pointer<ffi.Char> in_file,
    ffi.Pointer<ffi.Char> out_file,
    ffi.Pointer<ffi.Uint8> body_ptr,
    int body_len,
    ffi.Pointer<ffi.Uint8> private_key_ptr,
  ) {
    return _FFI_Group_Create(
      in_file,
      out_file,
      body_ptr,
      body_len,
      private_key_ptr,
    );
  }

  late final _FFI_Group_CreatePtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(
              ffi.Pointer<ffi.Char>,
              ffi.Pointer<ffi.Char>,
              ffi.Pointer<ffi.Uint8>,
              ffi.Uint32,
              ffi.Pointer<ffi.Uint8>)>>('FFI_Group_Create');
  late final _FFI_Group_Create = _FFI_Group_CreatePtr.asFunction<
      int Function(ffi.Pointer<ffi.Char>, ffi.Pointer<ffi.Char>,
          ffi.Pointer<ffi.Uint8>, int, ffi.Pointer<ffi.Uint8>)>();

  /// # Safety
  ///
  /// This function
  int FFI_Group_Commit(
    ffi.Pointer<ffi.Char> in_file,
    ffi.Pointer<ffi.Char> out_file,
    ffi.Pointer<ffi.Uint8> body,
    int body_len,
    ffi.Pointer<ffi.Uint8> private_key,
  ) {
    return _FFI_Group_Commit(
      in_file,
      out_file,
      body,
      body_len,
      private_key,
    );
  }

  late final _FFI_Group_CommitPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(
              ffi.Pointer<ffi.Char>,
              ffi.Pointer<ffi.Char>,
              ffi.Pointer<ffi.Uint8>,
              ffi.Uint32,
              ffi.Pointer<ffi.Uint8>)>>('FFI_Group_Commit');
  late final _FFI_Group_Commit = _FFI_Group_CommitPtr.asFunction<
      int Function(ffi.Pointer<ffi.Char>, ffi.Pointer<ffi.Char>,
          ffi.Pointer<ffi.Uint8>, int, ffi.Pointer<ffi.Uint8>)>();

  /// # Safety
  ///
  /// This function
  int FFI_Group_Merge(
    ffi.Pointer<ffi.Char> in_file,
    ffi.Pointer<ffi.Char> merge_file,
    ffi.Pointer<ffi.Char> out_file,
  ) {
    return _FFI_Group_Merge(
      in_file,
      merge_file,
      out_file,
    );
  }

  late final _FFI_Group_MergePtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(ffi.Pointer<ffi.Char>, ffi.Pointer<ffi.Char>,
              ffi.Pointer<ffi.Char>)>>('FFI_Group_Merge');
  late final _FFI_Group_Merge = _FFI_Group_MergePtr.asFunction<
      int Function(ffi.Pointer<ffi.Char>, ffi.Pointer<ffi.Char>,
          ffi.Pointer<ffi.Char>)>();

  /// # Safety
  ///
  /// This function
  ChatIOGroupPtr FFI_Group_Parse(
    ffi.Pointer<ffi.Char> in_file,
  ) {
    return _FFI_Group_Parse(
      in_file,
    );
  }

  late final _FFI_Group_ParsePtr = _lookup<
          ffi.NativeFunction<ChatIOGroupPtr Function(ffi.Pointer<ffi.Char>)>>(
      'FFI_Group_Parse');
  late final _FFI_Group_Parse = _FFI_Group_ParsePtr.asFunction<
      ChatIOGroupPtr Function(ffi.Pointer<ffi.Char>)>();

  /// # Safety
  ///
  /// This function
  void FFI_Group_Free(
    ChatIOGroupPtr group,
  ) {
    return _FFI_Group_Free(
      group,
    );
  }

  late final _FFI_Group_FreePtr =
      _lookup<ffi.NativeFunction<ffi.Void Function(ChatIOGroupPtr)>>(
          'FFI_Group_Free');
  late final _FFI_Group_Free =
      _FFI_Group_FreePtr.asFunction<void Function(ChatIOGroupPtr)>();

  void FFI_Util_Logger(
    int max_level,
  ) {
    return _FFI_Util_Logger(
      max_level,
    );
  }

  late final _FFI_Util_LoggerPtr =
      _lookup<ffi.NativeFunction<ffi.Void Function(ffi.Int32)>>(
          'FFI_Util_Logger');
  late final _FFI_Util_Logger =
      _FFI_Util_LoggerPtr.asFunction<void Function(int)>();

  /// # Safety
  ///
  /// 释放底层申请的const char的内存
  void FFI_Util_Free(
    ffi.Pointer<ffi.Char> ptr,
  ) {
    return _FFI_Util_Free(
      ptr,
    );
  }

  late final _FFI_Util_FreePtr =
      _lookup<ffi.NativeFunction<ffi.Void Function(ffi.Pointer<ffi.Char>)>>(
          'FFI_Util_Free');
  late final _FFI_Util_Free =
      _FFI_Util_FreePtr.asFunction<void Function(ffi.Pointer<ffi.Char>)>();

  /// # Safety
  ///
  /// 获取显示名字
  ffi.Pointer<ffi.Char> FFI_Util_DisplayName(
    ffi.Pointer<ffi.Uint8> private_key_ptr,
  ) {
    return _FFI_Util_DisplayName(
      private_key_ptr,
    );
  }

  late final _FFI_Util_DisplayNamePtr = _lookup<
      ffi.NativeFunction<
          ffi.Pointer<ffi.Char> Function(
              ffi.Pointer<ffi.Uint8>)>>('FFI_Util_DisplayName');
  late final _FFI_Util_DisplayName = _FFI_Util_DisplayNamePtr.asFunction<
      ffi.Pointer<ffi.Char> Function(ffi.Pointer<ffi.Uint8>)>();

  /// # Safety
  ///
  /// 释放 Bytes 自定义字符串对象
  void FFI_Util_FreeBytes(
    ChatIOBytesPtr bytes,
  ) {
    return _FFI_Util_FreeBytes(
      bytes,
    );
  }

  late final _FFI_Util_FreeBytesPtr =
      _lookup<ffi.NativeFunction<ffi.Void Function(ChatIOBytesPtr)>>(
          'FFI_Util_FreeBytes');
  late final _FFI_Util_FreeBytes =
      _FFI_Util_FreeBytesPtr.asFunction<void Function(ChatIOBytesPtr)>();

  /// # Safety
  ///
  /// 指纹确认的显示内容
  ffi.Pointer<ffi.Char> FFI_Util_DisplayableFingeprint(
    ffi.Pointer<ffi.Char> remote_name,
    ffi.Pointer<ffi.Uint8> private_key,
  ) {
    return _FFI_Util_DisplayableFingeprint(
      remote_name,
      private_key,
    );
  }

  late final _FFI_Util_DisplayableFingeprintPtr = _lookup<
      ffi.NativeFunction<
          ffi.Pointer<ffi.Char> Function(ffi.Pointer<ffi.Char>,
              ffi.Pointer<ffi.Uint8>)>>('FFI_Util_DisplayableFingeprint');
  late final _FFI_Util_DisplayableFingeprint =
      _FFI_Util_DisplayableFingeprintPtr.asFunction<
          ffi.Pointer<ffi.Char> Function(
              ffi.Pointer<ffi.Char>, ffi.Pointer<ffi.Uint8>)>();

  /// # Safety
  ///
  /// 指纹确认的二维码内容
  ffi.Pointer<ffi.Char> FFI_Util_ScannableFingeprint(
    ffi.Pointer<ffi.Char> remote_name,
    ffi.Pointer<ffi.Uint8> private_key,
  ) {
    return _FFI_Util_ScannableFingeprint(
      remote_name,
      private_key,
    );
  }

  late final _FFI_Util_ScannableFingeprintPtr = _lookup<
      ffi.NativeFunction<
          ffi.Pointer<ffi.Char> Function(ffi.Pointer<ffi.Char>,
              ffi.Pointer<ffi.Uint8>)>>('FFI_Util_ScannableFingeprint');
  late final _FFI_Util_ScannableFingeprint =
      _FFI_Util_ScannableFingeprintPtr.asFunction<
          ffi.Pointer<ffi.Char> Function(
              ffi.Pointer<ffi.Char>, ffi.Pointer<ffi.Uint8>)>();

  /// # Safety
  ///
  /// 扫描结果验证
  int FFI_Util_ScannableFingeprintCompare(
    ffi.Pointer<ffi.Char> remote_name,
    ffi.Pointer<ffi.Char> combined,
    ffi.Pointer<ffi.Uint8> private_key,
  ) {
    return _FFI_Util_ScannableFingeprintCompare(
      remote_name,
      combined,
      private_key,
    );
  }

  late final _FFI_Util_ScannableFingeprintComparePtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(ffi.Pointer<ffi.Char>, ffi.Pointer<ffi.Char>,
              ffi.Pointer<ffi.Uint8>)>>('FFI_Util_ScannableFingeprintCompare');
  late final _FFI_Util_ScannableFingeprintCompare =
      _FFI_Util_ScannableFingeprintComparePtr.asFunction<
          int Function(ffi.Pointer<ffi.Char>, ffi.Pointer<ffi.Char>,
              ffi.Pointer<ffi.Uint8>)>();

  /// # Safety
  ///
  /// 解密得到私钥
  int FFI_Util_Private(
    ffi.Pointer<ffi.Char> private_bs64,
    ffi.Pointer<ffi.Char> password,
    ffi.Pointer<ffi.Pointer<ffi.Uint8>> res_private_key,
  ) {
    return _FFI_Util_Private(
      private_bs64,
      password,
      res_private_key,
    );
  }

  late final _FFI_Util_PrivatePtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(ffi.Pointer<ffi.Char>, ffi.Pointer<ffi.Char>,
              ffi.Pointer<ffi.Pointer<ffi.Uint8>>)>>('FFI_Util_Private');
  late final _FFI_Util_Private = _FFI_Util_PrivatePtr.asFunction<
      int Function(ffi.Pointer<ffi.Char>, ffi.Pointer<ffi.Char>,
          ffi.Pointer<ffi.Pointer<ffi.Uint8>>)>();

  /// # Safety
  ///
  /// 生成私钥
  int FFI_Util_CreatePrivateKey(
    ffi.Pointer<ffi.Pointer<ffi.Uint8>> res_private_key,
  ) {
    return _FFI_Util_CreatePrivateKey(
      res_private_key,
    );
  }

  late final _FFI_Util_CreatePrivateKeyPtr = _lookup<
          ffi.NativeFunction<
              ffi.Int32 Function(ffi.Pointer<ffi.Pointer<ffi.Uint8>>)>>(
      'FFI_Util_CreatePrivateKey');
  late final _FFI_Util_CreatePrivateKey = _FFI_Util_CreatePrivateKeyPtr
      .asFunction<int Function(ffi.Pointer<ffi.Pointer<ffi.Uint8>>)>();

  /// # Safety
  ///
  /// 生成密钥对
  int FFI_Util_CreateKeyPair(
    ffi.Pointer<ffi.Pointer<ffi.Uint8>> res_private_key,
    ffi.Pointer<ffi.Pointer<ffi.Uint8>> res_public_key,
  ) {
    return _FFI_Util_CreateKeyPair(
      res_private_key,
      res_public_key,
    );
  }

  late final _FFI_Util_CreateKeyPairPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(ffi.Pointer<ffi.Pointer<ffi.Uint8>>,
              ffi.Pointer<ffi.Pointer<ffi.Uint8>>)>>('FFI_Util_CreateKeyPair');
  late final _FFI_Util_CreateKeyPair = _FFI_Util_CreateKeyPairPtr.asFunction<
      int Function(ffi.Pointer<ffi.Pointer<ffi.Uint8>>,
          ffi.Pointer<ffi.Pointer<ffi.Uint8>>)>();

  /// # Safety
  ///
  /// 文件加密
  ffi.Pointer<ffi.Char> FFI_Util_FileEncrypt(
    ffi.Pointer<ffi.Char> src,
    ffi.Pointer<ffi.Char> dst,
  ) {
    return _FFI_Util_FileEncrypt(
      src,
      dst,
    );
  }

  late final _FFI_Util_FileEncryptPtr = _lookup<
      ffi.NativeFunction<
          ffi.Pointer<ffi.Char> Function(ffi.Pointer<ffi.Char>,
              ffi.Pointer<ffi.Char>)>>('FFI_Util_FileEncrypt');
  late final _FFI_Util_FileEncrypt = _FFI_Util_FileEncryptPtr.asFunction<
      ffi.Pointer<ffi.Char> Function(
          ffi.Pointer<ffi.Char>, ffi.Pointer<ffi.Char>)>();

  /// # Safety
  ///
  /// 文件解密
  int FFI_Util_FileDecrypt(
    ffi.Pointer<ffi.Char> src,
    ffi.Pointer<ffi.Char> dst,
    ffi.Pointer<ffi.Char> fragment,
  ) {
    return _FFI_Util_FileDecrypt(
      src,
      dst,
      fragment,
    );
  }

  late final _FFI_Util_FileDecryptPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(ffi.Pointer<ffi.Char>, ffi.Pointer<ffi.Char>,
              ffi.Pointer<ffi.Char>)>>('FFI_Util_FileDecrypt');
  late final _FFI_Util_FileDecrypt = _FFI_Util_FileDecryptPtr.asFunction<
      int Function(ffi.Pointer<ffi.Char>, ffi.Pointer<ffi.Char>,
          ffi.Pointer<ffi.Char>)>();

  /// # Safety
  ///
  /// 文件解密到内存
  ChatIOBytesPtr FFI_Util_FileDecryptToMemory(
    ffi.Pointer<ffi.Char> src,
    ffi.Pointer<ffi.Char> fragment,
  ) {
    return _FFI_Util_FileDecryptToMemory(
      src,
      fragment,
    );
  }

  late final _FFI_Util_FileDecryptToMemoryPtr = _lookup<
      ffi.NativeFunction<
          ChatIOBytesPtr Function(ffi.Pointer<ffi.Char>,
              ffi.Pointer<ffi.Char>)>>('FFI_Util_FileDecryptToMemory');
  late final _FFI_Util_FileDecryptToMemory =
      _FFI_Util_FileDecryptToMemoryPtr.asFunction<
          ChatIOBytesPtr Function(
              ffi.Pointer<ffi.Char>, ffi.Pointer<ffi.Char>)>();
}

final class __mbstate_t extends ffi.Union {
  @ffi.Array.multi([128])
  external ffi.Array<ffi.Char> __mbstate8;

  @ffi.LongLong()
  external int _mbstateL;
}

final class __darwin_pthread_handler_rec extends ffi.Struct {
  external ffi
      .Pointer<ffi.NativeFunction<ffi.Void Function(ffi.Pointer<ffi.Void>)>>
      __routine;

  external ffi.Pointer<ffi.Void> __arg;

  external ffi.Pointer<__darwin_pthread_handler_rec> __next;
}

final class _opaque_pthread_attr_t extends ffi.Struct {
  @ffi.Long()
  external int __sig;

  @ffi.Array.multi([56])
  external ffi.Array<ffi.Char> __opaque;
}

final class _opaque_pthread_cond_t extends ffi.Struct {
  @ffi.Long()
  external int __sig;

  @ffi.Array.multi([40])
  external ffi.Array<ffi.Char> __opaque;
}

final class _opaque_pthread_condattr_t extends ffi.Struct {
  @ffi.Long()
  external int __sig;

  @ffi.Array.multi([8])
  external ffi.Array<ffi.Char> __opaque;
}

final class _opaque_pthread_mutex_t extends ffi.Struct {
  @ffi.Long()
  external int __sig;

  @ffi.Array.multi([56])
  external ffi.Array<ffi.Char> __opaque;
}

final class _opaque_pthread_mutexattr_t extends ffi.Struct {
  @ffi.Long()
  external int __sig;

  @ffi.Array.multi([8])
  external ffi.Array<ffi.Char> __opaque;
}

final class _opaque_pthread_once_t extends ffi.Struct {
  @ffi.Long()
  external int __sig;

  @ffi.Array.multi([8])
  external ffi.Array<ffi.Char> __opaque;
}

final class _opaque_pthread_rwlock_t extends ffi.Struct {
  @ffi.Long()
  external int __sig;

  @ffi.Array.multi([192])
  external ffi.Array<ffi.Char> __opaque;
}

final class _opaque_pthread_rwlockattr_t extends ffi.Struct {
  @ffi.Long()
  external int __sig;

  @ffi.Array.multi([16])
  external ffi.Array<ffi.Char> __opaque;
}

final class _opaque_pthread_t extends ffi.Struct {
  @ffi.Long()
  external int __sig;

  external ffi.Pointer<__darwin_pthread_handler_rec> __cleanup_stack;

  @ffi.Array.multi([8176])
  external ffi.Array<ffi.Char> __opaque;
}

abstract class idtype_t {
  static const int P_ALL = 0;
  static const int P_PID = 1;
  static const int P_PGID = 2;
}

final class __darwin_i386_thread_state extends ffi.Struct {
  @ffi.UnsignedInt()
  external int __eax;

  @ffi.UnsignedInt()
  external int __ebx;

  @ffi.UnsignedInt()
  external int __ecx;

  @ffi.UnsignedInt()
  external int __edx;

  @ffi.UnsignedInt()
  external int __edi;

  @ffi.UnsignedInt()
  external int __esi;

  @ffi.UnsignedInt()
  external int __ebp;

  @ffi.UnsignedInt()
  external int __esp;

  @ffi.UnsignedInt()
  external int __ss;

  @ffi.UnsignedInt()
  external int __eflags;

  @ffi.UnsignedInt()
  external int __eip;

  @ffi.UnsignedInt()
  external int __cs;

  @ffi.UnsignedInt()
  external int __ds;

  @ffi.UnsignedInt()
  external int __es;

  @ffi.UnsignedInt()
  external int __fs;

  @ffi.UnsignedInt()
  external int __gs;
}

final class __darwin_fp_control extends ffi.Opaque {}

final class __darwin_fp_status extends ffi.Opaque {}

final class __darwin_mmst_reg extends ffi.Struct {
  @ffi.Array.multi([10])
  external ffi.Array<ffi.Char> __mmst_reg;

  @ffi.Array.multi([6])
  external ffi.Array<ffi.Char> __mmst_rsrv;
}

final class __darwin_xmm_reg extends ffi.Struct {
  @ffi.Array.multi([16])
  external ffi.Array<ffi.Char> __xmm_reg;
}

final class __darwin_ymm_reg extends ffi.Struct {
  @ffi.Array.multi([32])
  external ffi.Array<ffi.Char> __ymm_reg;
}

final class __darwin_zmm_reg extends ffi.Struct {
  @ffi.Array.multi([64])
  external ffi.Array<ffi.Char> __zmm_reg;
}

final class __darwin_opmask_reg extends ffi.Struct {
  @ffi.Array.multi([8])
  external ffi.Array<ffi.Char> __opmask_reg;
}

final class __darwin_i386_float_state extends ffi.Opaque {}

final class __darwin_i386_avx_state extends ffi.Opaque {}

final class __darwin_i386_avx512_state extends ffi.Opaque {}

final class __darwin_i386_exception_state extends ffi.Struct {
  @__uint16_t()
  external int __trapno;

  @__uint16_t()
  external int __cpu;

  @__uint32_t()
  external int __err;

  @__uint32_t()
  external int __faultvaddr;
}

typedef __uint16_t = ffi.UnsignedShort;
typedef __uint32_t = ffi.UnsignedInt;

final class __darwin_x86_debug_state32 extends ffi.Struct {
  @ffi.UnsignedInt()
  external int __dr0;

  @ffi.UnsignedInt()
  external int __dr1;

  @ffi.UnsignedInt()
  external int __dr2;

  @ffi.UnsignedInt()
  external int __dr3;

  @ffi.UnsignedInt()
  external int __dr4;

  @ffi.UnsignedInt()
  external int __dr5;

  @ffi.UnsignedInt()
  external int __dr6;

  @ffi.UnsignedInt()
  external int __dr7;
}

final class __x86_instruction_state extends ffi.Struct {
  @ffi.Int()
  external int __insn_stream_valid_bytes;

  @ffi.Int()
  external int __insn_offset;

  @ffi.Int()
  external int __out_of_synch;

  @ffi.Array.multi([2380])
  external ffi.Array<__uint8_t> __insn_bytes;

  @ffi.Array.multi([64])
  external ffi.Array<__uint8_t> __insn_cacheline;
}

typedef __uint8_t = ffi.UnsignedChar;

final class __last_branch_record extends ffi.Opaque {}

final class __last_branch_state extends ffi.Opaque {}

final class __x86_pagein_state extends ffi.Struct {
  @ffi.Int()
  external int __pagein_error;
}

final class __darwin_x86_thread_state64 extends ffi.Struct {
  @__uint64_t()
  external int __rax;

  @__uint64_t()
  external int __rbx;

  @__uint64_t()
  external int __rcx;

  @__uint64_t()
  external int __rdx;

  @__uint64_t()
  external int __rdi;

  @__uint64_t()
  external int __rsi;

  @__uint64_t()
  external int __rbp;

  @__uint64_t()
  external int __rsp;

  @__uint64_t()
  external int __r8;

  @__uint64_t()
  external int __r9;

  @__uint64_t()
  external int __r10;

  @__uint64_t()
  external int __r11;

  @__uint64_t()
  external int __r12;

  @__uint64_t()
  external int __r13;

  @__uint64_t()
  external int __r14;

  @__uint64_t()
  external int __r15;

  @__uint64_t()
  external int __rip;

  @__uint64_t()
  external int __rflags;

  @__uint64_t()
  external int __cs;

  @__uint64_t()
  external int __fs;

  @__uint64_t()
  external int __gs;
}

typedef __uint64_t = ffi.UnsignedLongLong;

final class __darwin_x86_thread_full_state64 extends ffi.Struct {
  external __darwin_x86_thread_state64 __ss64;

  @__uint64_t()
  external int __ds;

  @__uint64_t()
  external int __es;

  @__uint64_t()
  external int __ss;

  @__uint64_t()
  external int __gsbase;
}

final class __darwin_x86_float_state64 extends ffi.Opaque {}

final class __darwin_x86_avx_state64 extends ffi.Opaque {}

final class __darwin_x86_avx512_state64 extends ffi.Opaque {}

final class __darwin_x86_exception_state64 extends ffi.Struct {
  @__uint16_t()
  external int __trapno;

  @__uint16_t()
  external int __cpu;

  @__uint32_t()
  external int __err;

  @__uint64_t()
  external int __faultvaddr;
}

final class __darwin_x86_debug_state64 extends ffi.Struct {
  @__uint64_t()
  external int __dr0;

  @__uint64_t()
  external int __dr1;

  @__uint64_t()
  external int __dr2;

  @__uint64_t()
  external int __dr3;

  @__uint64_t()
  external int __dr4;

  @__uint64_t()
  external int __dr5;

  @__uint64_t()
  external int __dr6;

  @__uint64_t()
  external int __dr7;
}

final class __darwin_x86_cpmu_state64 extends ffi.Struct {
  @ffi.Array.multi([16])
  external ffi.Array<__uint64_t> __ctrs;
}

final class __darwin_mcontext32 extends ffi.Opaque {}

final class __darwin_mcontext_avx32 extends ffi.Opaque {}

final class __darwin_mcontext_avx512_32 extends ffi.Opaque {}

final class __darwin_mcontext64 extends ffi.Opaque {}

final class __darwin_mcontext64_full extends ffi.Opaque {}

final class __darwin_mcontext_avx64 extends ffi.Opaque {}

final class __darwin_mcontext_avx64_full extends ffi.Opaque {}

final class __darwin_mcontext_avx512_64 extends ffi.Opaque {}

final class __darwin_mcontext_avx512_64_full extends ffi.Opaque {}

final class __darwin_sigaltstack extends ffi.Struct {
  external ffi.Pointer<ffi.Void> ss_sp;

  @__darwin_size_t()
  external int ss_size;

  @ffi.Int()
  external int ss_flags;
}

typedef __darwin_size_t = ffi.UnsignedLong;

final class __darwin_ucontext extends ffi.Struct {
  @ffi.Int()
  external int uc_onstack;

  @__darwin_sigset_t()
  external int uc_sigmask;

  external __darwin_sigaltstack uc_stack;

  external ffi.Pointer<__darwin_ucontext> uc_link;

  @__darwin_size_t()
  external int uc_mcsize;

  external ffi.Pointer<__darwin_mcontext64> uc_mcontext;
}

typedef __darwin_sigset_t = __uint32_t;

final class sigval extends ffi.Union {
  @ffi.Int()
  external int sival_int;

  external ffi.Pointer<ffi.Void> sival_ptr;
}

final class sigevent extends ffi.Struct {
  @ffi.Int()
  external int sigev_notify;

  @ffi.Int()
  external int sigev_signo;

  external sigval sigev_value;

  external ffi.Pointer<ffi.NativeFunction<ffi.Void Function(sigval)>>
      sigev_notify_function;

  external ffi.Pointer<pthread_attr_t> sigev_notify_attributes;
}

typedef pthread_attr_t = __darwin_pthread_attr_t;
typedef __darwin_pthread_attr_t = _opaque_pthread_attr_t;

final class __siginfo extends ffi.Struct {
  @ffi.Int()
  external int si_signo;

  @ffi.Int()
  external int si_errno;

  @ffi.Int()
  external int si_code;

  @pid_t()
  external int si_pid;

  @uid_t()
  external int si_uid;

  @ffi.Int()
  external int si_status;

  external ffi.Pointer<ffi.Void> si_addr;

  external sigval si_value;

  @ffi.Long()
  external int si_band;

  @ffi.Array.multi([7])
  external ffi.Array<ffi.UnsignedLong> __pad;
}

typedef pid_t = __darwin_pid_t;
typedef __darwin_pid_t = __int32_t;
typedef __int32_t = ffi.Int;
typedef uid_t = __darwin_uid_t;
typedef __darwin_uid_t = __uint32_t;

final class __sigaction_u extends ffi.Union {
  external ffi.Pointer<ffi.NativeFunction<ffi.Void Function(ffi.Int)>>
      __sa_handler;

  external ffi.Pointer<
          ffi.NativeFunction<
              ffi.Void Function(
                  ffi.Int, ffi.Pointer<__siginfo>, ffi.Pointer<ffi.Void>)>>
      __sa_sigaction;
}

final class __sigaction extends ffi.Struct {
  external __sigaction_u __sigaction_u1;

  external ffi.Pointer<
      ffi.NativeFunction<
          ffi.Void Function(ffi.Pointer<ffi.Void>, ffi.Int, ffi.Int,
              ffi.Pointer<siginfo_t>, ffi.Pointer<ffi.Void>)>> sa_tramp;

  @sigset_t()
  external int sa_mask;

  @ffi.Int()
  external int sa_flags;
}

typedef siginfo_t = __siginfo;
typedef sigset_t = __darwin_sigset_t;

final class sigaction extends ffi.Struct {
  external __sigaction_u __sigaction_u1;

  @sigset_t()
  external int sa_mask;

  @ffi.Int()
  external int sa_flags;
}

final class sigvec extends ffi.Struct {
  external ffi.Pointer<ffi.NativeFunction<ffi.Void Function(ffi.Int)>>
      sv_handler;

  @ffi.Int()
  external int sv_mask;

  @ffi.Int()
  external int sv_flags;
}

final class sigstack extends ffi.Struct {
  external ffi.Pointer<ffi.Char> ss_sp;

  @ffi.Int()
  external int ss_onstack;
}

final class timeval extends ffi.Struct {
  @__darwin_time_t()
  external int tv_sec;

  @__darwin_suseconds_t()
  external int tv_usec;
}

typedef __darwin_time_t = ffi.Long;
typedef __darwin_suseconds_t = __int32_t;

final class rusage extends ffi.Struct {
  external timeval ru_utime;

  external timeval ru_stime;

  @ffi.Long()
  external int ru_maxrss;

  @ffi.Long()
  external int ru_ixrss;

  @ffi.Long()
  external int ru_idrss;

  @ffi.Long()
  external int ru_isrss;

  @ffi.Long()
  external int ru_minflt;

  @ffi.Long()
  external int ru_majflt;

  @ffi.Long()
  external int ru_nswap;

  @ffi.Long()
  external int ru_inblock;

  @ffi.Long()
  external int ru_oublock;

  @ffi.Long()
  external int ru_msgsnd;

  @ffi.Long()
  external int ru_msgrcv;

  @ffi.Long()
  external int ru_nsignals;

  @ffi.Long()
  external int ru_nvcsw;

  @ffi.Long()
  external int ru_nivcsw;
}

final class rusage_info_v0 extends ffi.Struct {
  @ffi.Array.multi([16])
  external ffi.Array<ffi.Uint8> ri_uuid;

  @ffi.Uint64()
  external int ri_user_time;

  @ffi.Uint64()
  external int ri_system_time;

  @ffi.Uint64()
  external int ri_pkg_idle_wkups;

  @ffi.Uint64()
  external int ri_interrupt_wkups;

  @ffi.Uint64()
  external int ri_pageins;

  @ffi.Uint64()
  external int ri_wired_size;

  @ffi.Uint64()
  external int ri_resident_size;

  @ffi.Uint64()
  external int ri_phys_footprint;

  @ffi.Uint64()
  external int ri_proc_start_abstime;

  @ffi.Uint64()
  external int ri_proc_exit_abstime;
}

final class rusage_info_v1 extends ffi.Struct {
  @ffi.Array.multi([16])
  external ffi.Array<ffi.Uint8> ri_uuid;

  @ffi.Uint64()
  external int ri_user_time;

  @ffi.Uint64()
  external int ri_system_time;

  @ffi.Uint64()
  external int ri_pkg_idle_wkups;

  @ffi.Uint64()
  external int ri_interrupt_wkups;

  @ffi.Uint64()
  external int ri_pageins;

  @ffi.Uint64()
  external int ri_wired_size;

  @ffi.Uint64()
  external int ri_resident_size;

  @ffi.Uint64()
  external int ri_phys_footprint;

  @ffi.Uint64()
  external int ri_proc_start_abstime;

  @ffi.Uint64()
  external int ri_proc_exit_abstime;

  @ffi.Uint64()
  external int ri_child_user_time;

  @ffi.Uint64()
  external int ri_child_system_time;

  @ffi.Uint64()
  external int ri_child_pkg_idle_wkups;

  @ffi.Uint64()
  external int ri_child_interrupt_wkups;

  @ffi.Uint64()
  external int ri_child_pageins;

  @ffi.Uint64()
  external int ri_child_elapsed_abstime;
}

final class rusage_info_v2 extends ffi.Struct {
  @ffi.Array.multi([16])
  external ffi.Array<ffi.Uint8> ri_uuid;

  @ffi.Uint64()
  external int ri_user_time;

  @ffi.Uint64()
  external int ri_system_time;

  @ffi.Uint64()
  external int ri_pkg_idle_wkups;

  @ffi.Uint64()
  external int ri_interrupt_wkups;

  @ffi.Uint64()
  external int ri_pageins;

  @ffi.Uint64()
  external int ri_wired_size;

  @ffi.Uint64()
  external int ri_resident_size;

  @ffi.Uint64()
  external int ri_phys_footprint;

  @ffi.Uint64()
  external int ri_proc_start_abstime;

  @ffi.Uint64()
  external int ri_proc_exit_abstime;

  @ffi.Uint64()
  external int ri_child_user_time;

  @ffi.Uint64()
  external int ri_child_system_time;

  @ffi.Uint64()
  external int ri_child_pkg_idle_wkups;

  @ffi.Uint64()
  external int ri_child_interrupt_wkups;

  @ffi.Uint64()
  external int ri_child_pageins;

  @ffi.Uint64()
  external int ri_child_elapsed_abstime;

  @ffi.Uint64()
  external int ri_diskio_bytesread;

  @ffi.Uint64()
  external int ri_diskio_byteswritten;
}

final class rusage_info_v3 extends ffi.Struct {
  @ffi.Array.multi([16])
  external ffi.Array<ffi.Uint8> ri_uuid;

  @ffi.Uint64()
  external int ri_user_time;

  @ffi.Uint64()
  external int ri_system_time;

  @ffi.Uint64()
  external int ri_pkg_idle_wkups;

  @ffi.Uint64()
  external int ri_interrupt_wkups;

  @ffi.Uint64()
  external int ri_pageins;

  @ffi.Uint64()
  external int ri_wired_size;

  @ffi.Uint64()
  external int ri_resident_size;

  @ffi.Uint64()
  external int ri_phys_footprint;

  @ffi.Uint64()
  external int ri_proc_start_abstime;

  @ffi.Uint64()
  external int ri_proc_exit_abstime;

  @ffi.Uint64()
  external int ri_child_user_time;

  @ffi.Uint64()
  external int ri_child_system_time;

  @ffi.Uint64()
  external int ri_child_pkg_idle_wkups;

  @ffi.Uint64()
  external int ri_child_interrupt_wkups;

  @ffi.Uint64()
  external int ri_child_pageins;

  @ffi.Uint64()
  external int ri_child_elapsed_abstime;

  @ffi.Uint64()
  external int ri_diskio_bytesread;

  @ffi.Uint64()
  external int ri_diskio_byteswritten;

  @ffi.Uint64()
  external int ri_cpu_time_qos_default;

  @ffi.Uint64()
  external int ri_cpu_time_qos_maintenance;

  @ffi.Uint64()
  external int ri_cpu_time_qos_background;

  @ffi.Uint64()
  external int ri_cpu_time_qos_utility;

  @ffi.Uint64()
  external int ri_cpu_time_qos_legacy;

  @ffi.Uint64()
  external int ri_cpu_time_qos_user_initiated;

  @ffi.Uint64()
  external int ri_cpu_time_qos_user_interactive;

  @ffi.Uint64()
  external int ri_billed_system_time;

  @ffi.Uint64()
  external int ri_serviced_system_time;
}

final class rusage_info_v4 extends ffi.Struct {
  @ffi.Array.multi([16])
  external ffi.Array<ffi.Uint8> ri_uuid;

  @ffi.Uint64()
  external int ri_user_time;

  @ffi.Uint64()
  external int ri_system_time;

  @ffi.Uint64()
  external int ri_pkg_idle_wkups;

  @ffi.Uint64()
  external int ri_interrupt_wkups;

  @ffi.Uint64()
  external int ri_pageins;

  @ffi.Uint64()
  external int ri_wired_size;

  @ffi.Uint64()
  external int ri_resident_size;

  @ffi.Uint64()
  external int ri_phys_footprint;

  @ffi.Uint64()
  external int ri_proc_start_abstime;

  @ffi.Uint64()
  external int ri_proc_exit_abstime;

  @ffi.Uint64()
  external int ri_child_user_time;

  @ffi.Uint64()
  external int ri_child_system_time;

  @ffi.Uint64()
  external int ri_child_pkg_idle_wkups;

  @ffi.Uint64()
  external int ri_child_interrupt_wkups;

  @ffi.Uint64()
  external int ri_child_pageins;

  @ffi.Uint64()
  external int ri_child_elapsed_abstime;

  @ffi.Uint64()
  external int ri_diskio_bytesread;

  @ffi.Uint64()
  external int ri_diskio_byteswritten;

  @ffi.Uint64()
  external int ri_cpu_time_qos_default;

  @ffi.Uint64()
  external int ri_cpu_time_qos_maintenance;

  @ffi.Uint64()
  external int ri_cpu_time_qos_background;

  @ffi.Uint64()
  external int ri_cpu_time_qos_utility;

  @ffi.Uint64()
  external int ri_cpu_time_qos_legacy;

  @ffi.Uint64()
  external int ri_cpu_time_qos_user_initiated;

  @ffi.Uint64()
  external int ri_cpu_time_qos_user_interactive;

  @ffi.Uint64()
  external int ri_billed_system_time;

  @ffi.Uint64()
  external int ri_serviced_system_time;

  @ffi.Uint64()
  external int ri_logical_writes;

  @ffi.Uint64()
  external int ri_lifetime_max_phys_footprint;

  @ffi.Uint64()
  external int ri_instructions;

  @ffi.Uint64()
  external int ri_cycles;

  @ffi.Uint64()
  external int ri_billed_energy;

  @ffi.Uint64()
  external int ri_serviced_energy;

  @ffi.Uint64()
  external int ri_interval_max_phys_footprint;

  @ffi.Uint64()
  external int ri_runnable_time;
}

final class rusage_info_v5 extends ffi.Struct {
  @ffi.Array.multi([16])
  external ffi.Array<ffi.Uint8> ri_uuid;

  @ffi.Uint64()
  external int ri_user_time;

  @ffi.Uint64()
  external int ri_system_time;

  @ffi.Uint64()
  external int ri_pkg_idle_wkups;

  @ffi.Uint64()
  external int ri_interrupt_wkups;

  @ffi.Uint64()
  external int ri_pageins;

  @ffi.Uint64()
  external int ri_wired_size;

  @ffi.Uint64()
  external int ri_resident_size;

  @ffi.Uint64()
  external int ri_phys_footprint;

  @ffi.Uint64()
  external int ri_proc_start_abstime;

  @ffi.Uint64()
  external int ri_proc_exit_abstime;

  @ffi.Uint64()
  external int ri_child_user_time;

  @ffi.Uint64()
  external int ri_child_system_time;

  @ffi.Uint64()
  external int ri_child_pkg_idle_wkups;

  @ffi.Uint64()
  external int ri_child_interrupt_wkups;

  @ffi.Uint64()
  external int ri_child_pageins;

  @ffi.Uint64()
  external int ri_child_elapsed_abstime;

  @ffi.Uint64()
  external int ri_diskio_bytesread;

  @ffi.Uint64()
  external int ri_diskio_byteswritten;

  @ffi.Uint64()
  external int ri_cpu_time_qos_default;

  @ffi.Uint64()
  external int ri_cpu_time_qos_maintenance;

  @ffi.Uint64()
  external int ri_cpu_time_qos_background;

  @ffi.Uint64()
  external int ri_cpu_time_qos_utility;

  @ffi.Uint64()
  external int ri_cpu_time_qos_legacy;

  @ffi.Uint64()
  external int ri_cpu_time_qos_user_initiated;

  @ffi.Uint64()
  external int ri_cpu_time_qos_user_interactive;

  @ffi.Uint64()
  external int ri_billed_system_time;

  @ffi.Uint64()
  external int ri_serviced_system_time;

  @ffi.Uint64()
  external int ri_logical_writes;

  @ffi.Uint64()
  external int ri_lifetime_max_phys_footprint;

  @ffi.Uint64()
  external int ri_instructions;

  @ffi.Uint64()
  external int ri_cycles;

  @ffi.Uint64()
  external int ri_billed_energy;

  @ffi.Uint64()
  external int ri_serviced_energy;

  @ffi.Uint64()
  external int ri_interval_max_phys_footprint;

  @ffi.Uint64()
  external int ri_runnable_time;

  @ffi.Uint64()
  external int ri_flags;
}

final class rusage_info_v6 extends ffi.Struct {
  @ffi.Array.multi([16])
  external ffi.Array<ffi.Uint8> ri_uuid;

  @ffi.Uint64()
  external int ri_user_time;

  @ffi.Uint64()
  external int ri_system_time;

  @ffi.Uint64()
  external int ri_pkg_idle_wkups;

  @ffi.Uint64()
  external int ri_interrupt_wkups;

  @ffi.Uint64()
  external int ri_pageins;

  @ffi.Uint64()
  external int ri_wired_size;

  @ffi.Uint64()
  external int ri_resident_size;

  @ffi.Uint64()
  external int ri_phys_footprint;

  @ffi.Uint64()
  external int ri_proc_start_abstime;

  @ffi.Uint64()
  external int ri_proc_exit_abstime;

  @ffi.Uint64()
  external int ri_child_user_time;

  @ffi.Uint64()
  external int ri_child_system_time;

  @ffi.Uint64()
  external int ri_child_pkg_idle_wkups;

  @ffi.Uint64()
  external int ri_child_interrupt_wkups;

  @ffi.Uint64()
  external int ri_child_pageins;

  @ffi.Uint64()
  external int ri_child_elapsed_abstime;

  @ffi.Uint64()
  external int ri_diskio_bytesread;

  @ffi.Uint64()
  external int ri_diskio_byteswritten;

  @ffi.Uint64()
  external int ri_cpu_time_qos_default;

  @ffi.Uint64()
  external int ri_cpu_time_qos_maintenance;

  @ffi.Uint64()
  external int ri_cpu_time_qos_background;

  @ffi.Uint64()
  external int ri_cpu_time_qos_utility;

  @ffi.Uint64()
  external int ri_cpu_time_qos_legacy;

  @ffi.Uint64()
  external int ri_cpu_time_qos_user_initiated;

  @ffi.Uint64()
  external int ri_cpu_time_qos_user_interactive;

  @ffi.Uint64()
  external int ri_billed_system_time;

  @ffi.Uint64()
  external int ri_serviced_system_time;

  @ffi.Uint64()
  external int ri_logical_writes;

  @ffi.Uint64()
  external int ri_lifetime_max_phys_footprint;

  @ffi.Uint64()
  external int ri_instructions;

  @ffi.Uint64()
  external int ri_cycles;

  @ffi.Uint64()
  external int ri_billed_energy;

  @ffi.Uint64()
  external int ri_serviced_energy;

  @ffi.Uint64()
  external int ri_interval_max_phys_footprint;

  @ffi.Uint64()
  external int ri_runnable_time;

  @ffi.Uint64()
  external int ri_flags;

  @ffi.Uint64()
  external int ri_user_ptime;

  @ffi.Uint64()
  external int ri_system_ptime;

  @ffi.Uint64()
  external int ri_pinstructions;

  @ffi.Uint64()
  external int ri_pcycles;

  @ffi.Uint64()
  external int ri_energy_nj;

  @ffi.Uint64()
  external int ri_penergy_nj;

  @ffi.Array.multi([14])
  external ffi.Array<ffi.Uint64> ri_reserved;
}

final class rlimit extends ffi.Struct {
  @rlim_t()
  external int rlim_cur;

  @rlim_t()
  external int rlim_max;
}

typedef rlim_t = __uint64_t;

final class proc_rlimit_control_wakeupmon extends ffi.Struct {
  @ffi.Uint32()
  external int wm_flags;

  @ffi.Int32()
  external int wm_rate;
}

typedef id_t = __darwin_id_t;
typedef __darwin_id_t = __uint32_t;

final class wait extends ffi.Opaque {}

final class div_t extends ffi.Struct {
  @ffi.Int()
  external int quot;

  @ffi.Int()
  external int rem;
}

final class ldiv_t extends ffi.Struct {
  @ffi.Long()
  external int quot;

  @ffi.Long()
  external int rem;
}

final class lldiv_t extends ffi.Struct {
  @ffi.LongLong()
  external int quot;

  @ffi.LongLong()
  external int rem;
}

typedef malloc_type_id_t = ffi.UnsignedLongLong;

final class _malloc_zone_t extends ffi.Opaque {}

typedef malloc_zone_t = _malloc_zone_t;
typedef dev_t = __darwin_dev_t;
typedef __darwin_dev_t = __int32_t;
typedef mode_t = __darwin_mode_t;
typedef __darwin_mode_t = __uint16_t;

final class ChatIOAuth extends ffi.Opaque {}

final class ChatIOFfiChatio extends ffi.Opaque {}

final class ChatIOGroupItem extends ffi.Struct {
  external ffi.Pointer<ffi.UnsignedChar> body_ptr;

  @ffi.Uint32()
  external int body_len;

  external ffi.Pointer<ffi.Char> owner;
}

final class ChatIOGroup extends ffi.Struct {
  external ChatIOGroupItem master;

  external ffi.Pointer<ChatIOGroupItem> commits;

  @ffi.Uint32()
  external int commits_len;
}

final class ChatIOBytes extends ffi.Struct {
  external ffi.Pointer<ffi.UnsignedChar> ptr;

  @ffi.Uint32()
  external int len;
}

typedef ChatIOAuthPtr = ffi.Pointer<ChatIOAuth>;
typedef ChatIOFfiChatioPtr = ffi.Pointer<ChatIOFfiChatio>;
typedef ChatIOMessageCallBack = ffi.Pointer<
    ffi.NativeFunction<
        ffi.Bool Function(ffi.Pointer<ffi.Uint8>, ffi.Int32,
            ffi.Pointer<ffi.Char>, ffi.Pointer<ffi.Char>)>>;
typedef ChatIOMessageErrorCallBack = ffi.Pointer<
    ffi.NativeFunction<
        ffi.Bool Function(
            ffi.Pointer<ffi.Char>, ffi.Uint32, ffi.Pointer<ffi.Char>)>>;
typedef ChatIOUnauthorizedCallBack
    = ffi.Pointer<ffi.NativeFunction<ffi.Bool Function()>>;
typedef ChatIOGroupPtr = ffi.Pointer<ChatIOGroup>;
typedef ChatIOBytesPtr = ffi.Pointer<ChatIOBytes>;

const int __bool_true_false_are_defined = 1;

const int true1 = 1;

const int false1 = 0;

const int __WORDSIZE = 64;

const int __DARWIN_ONLY_64_BIT_INO_T = 0;

const int __DARWIN_ONLY_UNIX_CONFORMANCE = 1;

const int __DARWIN_ONLY_VERS_1050 = 0;

const int __DARWIN_UNIX03 = 1;

const int __DARWIN_64_BIT_INO_T = 1;

const int __DARWIN_VERS_1050 = 1;

const int __DARWIN_NON_CANCELABLE = 0;

const String __DARWIN_SUF_64_BIT_INO_T = '\$INODE64';

const String __DARWIN_SUF_1050 = '\$1050';

const String __DARWIN_SUF_EXTSN = '\$DARWIN_EXTSN';

const int __DARWIN_C_ANSI = 4096;

const int __DARWIN_C_FULL = 900000;

const int __DARWIN_C_LEVEL = 900000;

const int __STDC_WANT_LIB_EXT1__ = 1;

const int __DARWIN_NO_LONG_LONG = 0;

const int _DARWIN_FEATURE_64_BIT_INODE = 1;

const int _DARWIN_FEATURE_ONLY_UNIX_CONFORMANCE = 1;

const int _DARWIN_FEATURE_UNIX_CONFORMANCE = 3;

const int __has_ptrcheck = 0;

const int __DARWIN_NULL = 0;

const int __PTHREAD_SIZE__ = 8176;

const int __PTHREAD_ATTR_SIZE__ = 56;

const int __PTHREAD_MUTEXATTR_SIZE__ = 8;

const int __PTHREAD_MUTEX_SIZE__ = 56;

const int __PTHREAD_CONDATTR_SIZE__ = 8;

const int __PTHREAD_COND_SIZE__ = 40;

const int __PTHREAD_ONCE_SIZE__ = 8;

const int __PTHREAD_RWLOCK_SIZE__ = 192;

const int __PTHREAD_RWLOCKATTR_SIZE__ = 16;

const int USER_ADDR_NULL = 0;

const int INT8_MAX = 127;

const int INT16_MAX = 32767;

const int INT32_MAX = 2147483647;

const int INT64_MAX = 9223372036854775807;

const int INT8_MIN = -128;

const int INT16_MIN = -32768;

const int INT32_MIN = -2147483648;

const int INT64_MIN = -9223372036854775808;

const int UINT8_MAX = 255;

const int UINT16_MAX = 65535;

const int UINT32_MAX = 4294967295;

const int UINT64_MAX = -1;

const int INT_LEAST8_MIN = -128;

const int INT_LEAST16_MIN = -32768;

const int INT_LEAST32_MIN = -2147483648;

const int INT_LEAST64_MIN = -9223372036854775808;

const int INT_LEAST8_MAX = 127;

const int INT_LEAST16_MAX = 32767;

const int INT_LEAST32_MAX = 2147483647;

const int INT_LEAST64_MAX = 9223372036854775807;

const int UINT_LEAST8_MAX = 255;

const int UINT_LEAST16_MAX = 65535;

const int UINT_LEAST32_MAX = 4294967295;

const int UINT_LEAST64_MAX = -1;

const int INT_FAST8_MIN = -128;

const int INT_FAST16_MIN = -32768;

const int INT_FAST32_MIN = -2147483648;

const int INT_FAST64_MIN = -9223372036854775808;

const int INT_FAST8_MAX = 127;

const int INT_FAST16_MAX = 32767;

const int INT_FAST32_MAX = 2147483647;

const int INT_FAST64_MAX = 9223372036854775807;

const int UINT_FAST8_MAX = 255;

const int UINT_FAST16_MAX = 65535;

const int UINT_FAST32_MAX = 4294967295;

const int UINT_FAST64_MAX = -1;

const int INTPTR_MAX = 9223372036854775807;

const int INTPTR_MIN = -9223372036854775808;

const int UINTPTR_MAX = -1;

const int INTMAX_MAX = 9223372036854775807;

const int UINTMAX_MAX = -1;

const int INTMAX_MIN = -9223372036854775808;

const int PTRDIFF_MIN = -9223372036854775808;

const int PTRDIFF_MAX = 9223372036854775807;

const int SIZE_MAX = -1;

const int RSIZE_MAX = 9223372036854775807;

const int WCHAR_MAX = 2147483647;

const int WCHAR_MIN = -2147483648;

const int WINT_MIN = -2147483648;

const int WINT_MAX = 2147483647;

const int SIG_ATOMIC_MIN = -2147483648;

const int SIG_ATOMIC_MAX = 2147483647;

const int __API_TO_BE_DEPRECATED = 100000;

const int __API_TO_BE_DEPRECATED_MACOS = 100000;

const int __API_TO_BE_DEPRECATED_IOS = 100000;

const int __API_TO_BE_DEPRECATED_MACCATALYST = 100000;

const int __API_TO_BE_DEPRECATED_WATCHOS = 100000;

const int __API_TO_BE_DEPRECATED_TVOS = 100000;

const int __API_TO_BE_DEPRECATED_DRIVERKIT = 100000;

const int __API_TO_BE_DEPRECATED_XROS = 100000;

const int __MAC_10_0 = 1000;

const int __MAC_10_1 = 1010;

const int __MAC_10_2 = 1020;

const int __MAC_10_3 = 1030;

const int __MAC_10_4 = 1040;

const int __MAC_10_5 = 1050;

const int __MAC_10_6 = 1060;

const int __MAC_10_7 = 1070;

const int __MAC_10_8 = 1080;

const int __MAC_10_9 = 1090;

const int __MAC_10_10 = 101000;

const int __MAC_10_10_2 = 101002;

const int __MAC_10_10_3 = 101003;

const int __MAC_10_11 = 101100;

const int __MAC_10_11_2 = 101102;

const int __MAC_10_11_3 = 101103;

const int __MAC_10_11_4 = 101104;

const int __MAC_10_12 = 101200;

const int __MAC_10_12_1 = 101201;

const int __MAC_10_12_2 = 101202;

const int __MAC_10_12_4 = 101204;

const int __MAC_10_13 = 101300;

const int __MAC_10_13_1 = 101301;

const int __MAC_10_13_2 = 101302;

const int __MAC_10_13_4 = 101304;

const int __MAC_10_14 = 101400;

const int __MAC_10_14_1 = 101401;

const int __MAC_10_14_4 = 101404;

const int __MAC_10_14_5 = 101405;

const int __MAC_10_14_6 = 101406;

const int __MAC_10_15 = 101500;

const int __MAC_10_15_1 = 101501;

const int __MAC_10_15_4 = 101504;

const int __MAC_10_16 = 101600;

const int __MAC_11_0 = 110000;

const int __MAC_11_1 = 110100;

const int __MAC_11_3 = 110300;

const int __MAC_11_4 = 110400;

const int __MAC_11_5 = 110500;

const int __MAC_11_6 = 110600;

const int __MAC_12_0 = 120000;

const int __MAC_12_1 = 120100;

const int __MAC_12_2 = 120200;

const int __MAC_12_3 = 120300;

const int __MAC_12_4 = 120400;

const int __MAC_12_5 = 120500;

const int __MAC_13_0 = 130000;

const int __MAC_13_1 = 130100;

const int __MAC_13_2 = 130200;

const int __MAC_13_3 = 130300;

const int __MAC_13_4 = 130400;

const int __MAC_14_0 = 140000;

const int __IPHONE_2_0 = 20000;

const int __IPHONE_2_1 = 20100;

const int __IPHONE_2_2 = 20200;

const int __IPHONE_3_0 = 30000;

const int __IPHONE_3_1 = 30100;

const int __IPHONE_3_2 = 30200;

const int __IPHONE_4_0 = 40000;

const int __IPHONE_4_1 = 40100;

const int __IPHONE_4_2 = 40200;

const int __IPHONE_4_3 = 40300;

const int __IPHONE_5_0 = 50000;

const int __IPHONE_5_1 = 50100;

const int __IPHONE_6_0 = 60000;

const int __IPHONE_6_1 = 60100;

const int __IPHONE_7_0 = 70000;

const int __IPHONE_7_1 = 70100;

const int __IPHONE_8_0 = 80000;

const int __IPHONE_8_1 = 80100;

const int __IPHONE_8_2 = 80200;

const int __IPHONE_8_3 = 80300;

const int __IPHONE_8_4 = 80400;

const int __IPHONE_9_0 = 90000;

const int __IPHONE_9_1 = 90100;

const int __IPHONE_9_2 = 90200;

const int __IPHONE_9_3 = 90300;

const int __IPHONE_10_0 = 100000;

const int __IPHONE_10_1 = 100100;

const int __IPHONE_10_2 = 100200;

const int __IPHONE_10_3 = 100300;

const int __IPHONE_11_0 = 110000;

const int __IPHONE_11_1 = 110100;

const int __IPHONE_11_2 = 110200;

const int __IPHONE_11_3 = 110300;

const int __IPHONE_11_4 = 110400;

const int __IPHONE_12_0 = 120000;

const int __IPHONE_12_1 = 120100;

const int __IPHONE_12_2 = 120200;

const int __IPHONE_12_3 = 120300;

const int __IPHONE_12_4 = 120400;

const int __IPHONE_13_0 = 130000;

const int __IPHONE_13_1 = 130100;

const int __IPHONE_13_2 = 130200;

const int __IPHONE_13_3 = 130300;

const int __IPHONE_13_4 = 130400;

const int __IPHONE_13_5 = 130500;

const int __IPHONE_13_6 = 130600;

const int __IPHONE_13_7 = 130700;

const int __IPHONE_14_0 = 140000;

const int __IPHONE_14_1 = 140100;

const int __IPHONE_14_2 = 140200;

const int __IPHONE_14_3 = 140300;

const int __IPHONE_14_5 = 140500;

const int __IPHONE_14_4 = 140400;

const int __IPHONE_14_6 = 140600;

const int __IPHONE_14_7 = 140700;

const int __IPHONE_14_8 = 140800;

const int __IPHONE_15_0 = 150000;

const int __IPHONE_15_1 = 150100;

const int __IPHONE_15_2 = 150200;

const int __IPHONE_15_3 = 150300;

const int __IPHONE_15_4 = 150400;

const int __IPHONE_15_5 = 150500;

const int __IPHONE_15_6 = 150600;

const int __IPHONE_16_0 = 160000;

const int __IPHONE_16_1 = 160100;

const int __IPHONE_16_2 = 160200;

const int __IPHONE_16_3 = 160300;

const int __IPHONE_16_4 = 160400;

const int __IPHONE_16_5 = 160500;

const int __IPHONE_17_0 = 170000;

const int __WATCHOS_1_0 = 10000;

const int __WATCHOS_2_0 = 20000;

const int __WATCHOS_2_1 = 20100;

const int __WATCHOS_2_2 = 20200;

const int __WATCHOS_3_0 = 30000;

const int __WATCHOS_3_1 = 30100;

const int __WATCHOS_3_1_1 = 30101;

const int __WATCHOS_3_2 = 30200;

const int __WATCHOS_4_0 = 40000;

const int __WATCHOS_4_1 = 40100;

const int __WATCHOS_4_2 = 40200;

const int __WATCHOS_4_3 = 40300;

const int __WATCHOS_5_0 = 50000;

const int __WATCHOS_5_1 = 50100;

const int __WATCHOS_5_2 = 50200;

const int __WATCHOS_5_3 = 50300;

const int __WATCHOS_6_0 = 60000;

const int __WATCHOS_6_1 = 60100;

const int __WATCHOS_6_2 = 60200;

const int __WATCHOS_7_0 = 70000;

const int __WATCHOS_7_1 = 70100;

const int __WATCHOS_7_2 = 70200;

const int __WATCHOS_7_3 = 70300;

const int __WATCHOS_7_4 = 70400;

const int __WATCHOS_7_5 = 70500;

const int __WATCHOS_7_6 = 70600;

const int __WATCHOS_8_0 = 80000;

const int __WATCHOS_8_1 = 80100;

const int __WATCHOS_8_3 = 80300;

const int __WATCHOS_8_4 = 80400;

const int __WATCHOS_8_5 = 80500;

const int __WATCHOS_8_6 = 80600;

const int __WATCHOS_8_7 = 80700;

const int __WATCHOS_9_0 = 90000;

const int __WATCHOS_9_1 = 90100;

const int __WATCHOS_9_2 = 90200;

const int __WATCHOS_9_3 = 90300;

const int __WATCHOS_9_4 = 90400;

const int __WATCHOS_9_5 = 90500;

const int __WATCHOS_10_0 = 100000;

const int __TVOS_9_0 = 90000;

const int __TVOS_9_1 = 90100;

const int __TVOS_9_2 = 90200;

const int __TVOS_10_0 = 100000;

const int __TVOS_10_0_1 = 100001;

const int __TVOS_10_1 = 100100;

const int __TVOS_10_2 = 100200;

const int __TVOS_11_0 = 110000;

const int __TVOS_11_1 = 110100;

const int __TVOS_11_2 = 110200;

const int __TVOS_11_3 = 110300;

const int __TVOS_11_4 = 110400;

const int __TVOS_12_0 = 120000;

const int __TVOS_12_1 = 120100;

const int __TVOS_12_2 = 120200;

const int __TVOS_12_3 = 120300;

const int __TVOS_12_4 = 120400;

const int __TVOS_13_0 = 130000;

const int __TVOS_13_2 = 130200;

const int __TVOS_13_3 = 130300;

const int __TVOS_13_4 = 130400;

const int __TVOS_14_0 = 140000;

const int __TVOS_14_1 = 140100;

const int __TVOS_14_2 = 140200;

const int __TVOS_14_3 = 140300;

const int __TVOS_14_5 = 140500;

const int __TVOS_14_6 = 140600;

const int __TVOS_14_7 = 140700;

const int __TVOS_15_0 = 150000;

const int __TVOS_15_1 = 150100;

const int __TVOS_15_2 = 150200;

const int __TVOS_15_3 = 150300;

const int __TVOS_15_4 = 150400;

const int __TVOS_15_5 = 150500;

const int __TVOS_15_6 = 150600;

const int __TVOS_16_0 = 160000;

const int __TVOS_16_1 = 160100;

const int __TVOS_16_2 = 160200;

const int __TVOS_16_3 = 160300;

const int __TVOS_16_4 = 160400;

const int __TVOS_16_5 = 160500;

const int __TVOS_17_0 = 170000;

const int __BRIDGEOS_2_0 = 20000;

const int __BRIDGEOS_3_0 = 30000;

const int __BRIDGEOS_3_1 = 30100;

const int __BRIDGEOS_3_4 = 30400;

const int __BRIDGEOS_4_0 = 40000;

const int __BRIDGEOS_4_1 = 40100;

const int __BRIDGEOS_5_0 = 50000;

const int __BRIDGEOS_5_1 = 50100;

const int __BRIDGEOS_5_3 = 50300;

const int __BRIDGEOS_6_0 = 60000;

const int __BRIDGEOS_6_2 = 60200;

const int __BRIDGEOS_6_4 = 60400;

const int __BRIDGEOS_6_5 = 60500;

const int __BRIDGEOS_6_6 = 60600;

const int __BRIDGEOS_7_0 = 70000;

const int __BRIDGEOS_7_1 = 70100;

const int __BRIDGEOS_7_2 = 70200;

const int __BRIDGEOS_7_3 = 70300;

const int __BRIDGEOS_7_4 = 70400;

const int __BRIDGEOS_8_0 = 80000;

const int __DRIVERKIT_19_0 = 190000;

const int __DRIVERKIT_20_0 = 200000;

const int __DRIVERKIT_21_0 = 210000;

const int __DRIVERKIT_22_0 = 220000;

const int __DRIVERKIT_22_4 = 220400;

const int __DRIVERKIT_22_5 = 220500;

const int __DRIVERKIT_23_0 = 230000;

const int __XROS_1_0 = 10000;

const int MAC_OS_X_VERSION_10_0 = 1000;

const int MAC_OS_X_VERSION_10_1 = 1010;

const int MAC_OS_X_VERSION_10_2 = 1020;

const int MAC_OS_X_VERSION_10_3 = 1030;

const int MAC_OS_X_VERSION_10_4 = 1040;

const int MAC_OS_X_VERSION_10_5 = 1050;

const int MAC_OS_X_VERSION_10_6 = 1060;

const int MAC_OS_X_VERSION_10_7 = 1070;

const int MAC_OS_X_VERSION_10_8 = 1080;

const int MAC_OS_X_VERSION_10_9 = 1090;

const int MAC_OS_X_VERSION_10_10 = 101000;

const int MAC_OS_X_VERSION_10_10_2 = 101002;

const int MAC_OS_X_VERSION_10_10_3 = 101003;

const int MAC_OS_X_VERSION_10_11 = 101100;

const int MAC_OS_X_VERSION_10_11_2 = 101102;

const int MAC_OS_X_VERSION_10_11_3 = 101103;

const int MAC_OS_X_VERSION_10_11_4 = 101104;

const int MAC_OS_X_VERSION_10_12 = 101200;

const int MAC_OS_X_VERSION_10_12_1 = 101201;

const int MAC_OS_X_VERSION_10_12_2 = 101202;

const int MAC_OS_X_VERSION_10_12_4 = 101204;

const int MAC_OS_X_VERSION_10_13 = 101300;

const int MAC_OS_X_VERSION_10_13_1 = 101301;

const int MAC_OS_X_VERSION_10_13_2 = 101302;

const int MAC_OS_X_VERSION_10_13_4 = 101304;

const int MAC_OS_X_VERSION_10_14 = 101400;

const int MAC_OS_X_VERSION_10_14_1 = 101401;

const int MAC_OS_X_VERSION_10_14_4 = 101404;

const int MAC_OS_X_VERSION_10_14_5 = 101405;

const int MAC_OS_X_VERSION_10_14_6 = 101406;

const int MAC_OS_X_VERSION_10_15 = 101500;

const int MAC_OS_X_VERSION_10_15_1 = 101501;

const int MAC_OS_X_VERSION_10_15_4 = 101504;

const int MAC_OS_X_VERSION_10_16 = 101600;

const int MAC_OS_VERSION_11_0 = 110000;

const int MAC_OS_VERSION_11_1 = 110100;

const int MAC_OS_VERSION_11_3 = 110300;

const int MAC_OS_VERSION_11_4 = 110400;

const int MAC_OS_VERSION_11_5 = 110500;

const int MAC_OS_VERSION_11_6 = 110600;

const int MAC_OS_VERSION_12_0 = 120000;

const int MAC_OS_VERSION_12_1 = 120100;

const int MAC_OS_VERSION_12_2 = 120200;

const int MAC_OS_VERSION_12_3 = 120300;

const int MAC_OS_VERSION_12_4 = 120400;

const int MAC_OS_VERSION_12_5 = 120500;

const int MAC_OS_VERSION_13_0 = 130000;

const int MAC_OS_VERSION_13_1 = 130100;

const int MAC_OS_VERSION_13_2 = 130200;

const int MAC_OS_VERSION_13_3 = 130300;

const int MAC_OS_VERSION_13_4 = 130400;

const int MAC_OS_VERSION_14_0 = 140000;

const int __MAC_OS_X_VERSION_MIN_REQUIRED = 140000;

const int __MAC_OS_X_VERSION_MAX_ALLOWED = 140000;

const int __ENABLE_LEGACY_MAC_AVAILABILITY = 1;

const int __DARWIN_WCHAR_MAX = 2147483647;

const int __DARWIN_WCHAR_MIN = -2147483648;

const int __DARWIN_WEOF = -1;

const int _FORTIFY_SOURCE = 2;

const int __DARWIN_NSIG = 32;

const int NSIG = 32;

const int _I386_SIGNAL_H_ = 1;

const int SIGHUP = 1;

const int SIGINT = 2;

const int SIGQUIT = 3;

const int SIGILL = 4;

const int SIGTRAP = 5;

const int SIGABRT = 6;

const int SIGIOT = 6;

const int SIGEMT = 7;

const int SIGFPE = 8;

const int SIGKILL = 9;

const int SIGBUS = 10;

const int SIGSEGV = 11;

const int SIGSYS = 12;

const int SIGPIPE = 13;

const int SIGALRM = 14;

const int SIGTERM = 15;

const int SIGURG = 16;

const int SIGSTOP = 17;

const int SIGTSTP = 18;

const int SIGCONT = 19;

const int SIGCHLD = 20;

const int SIGTTIN = 21;

const int SIGTTOU = 22;

const int SIGIO = 23;

const int SIGXCPU = 24;

const int SIGXFSZ = 25;

const int SIGVTALRM = 26;

const int SIGPROF = 27;

const int SIGWINCH = 28;

const int SIGINFO = 29;

const int SIGUSR1 = 30;

const int SIGUSR2 = 31;

const int FP_PREC_24B = 0;

const int FP_PREC_53B = 2;

const int FP_PREC_64B = 3;

const int FP_RND_NEAR = 0;

const int FP_RND_DOWN = 1;

const int FP_RND_UP = 2;

const int FP_CHOP = 3;

const int FP_STATE_BYTES = 512;

const int _X86_INSTRUCTION_STATE_MAX_INSN_BYTES = 2380;

const int _X86_INSTRUCTION_STATE_CACHELINE_SIZE = 64;

const int __LASTBRANCH_MAX = 32;

const int SIGEV_NONE = 0;

const int SIGEV_SIGNAL = 1;

const int SIGEV_THREAD = 3;

const int ILL_NOOP = 0;

const int ILL_ILLOPC = 1;

const int ILL_ILLTRP = 2;

const int ILL_PRVOPC = 3;

const int ILL_ILLOPN = 4;

const int ILL_ILLADR = 5;

const int ILL_PRVREG = 6;

const int ILL_COPROC = 7;

const int ILL_BADSTK = 8;

const int FPE_NOOP = 0;

const int FPE_FLTDIV = 1;

const int FPE_FLTOVF = 2;

const int FPE_FLTUND = 3;

const int FPE_FLTRES = 4;

const int FPE_FLTINV = 5;

const int FPE_FLTSUB = 6;

const int FPE_INTDIV = 7;

const int FPE_INTOVF = 8;

const int SEGV_NOOP = 0;

const int SEGV_MAPERR = 1;

const int SEGV_ACCERR = 2;

const int BUS_NOOP = 0;

const int BUS_ADRALN = 1;

const int BUS_ADRERR = 2;

const int BUS_OBJERR = 3;

const int TRAP_BRKPT = 1;

const int TRAP_TRACE = 2;

const int CLD_NOOP = 0;

const int CLD_EXITED = 1;

const int CLD_KILLED = 2;

const int CLD_DUMPED = 3;

const int CLD_TRAPPED = 4;

const int CLD_STOPPED = 5;

const int CLD_CONTINUED = 6;

const int POLL_IN = 1;

const int POLL_OUT = 2;

const int POLL_MSG = 3;

const int POLL_ERR = 4;

const int POLL_PRI = 5;

const int POLL_HUP = 6;

const int SA_ONSTACK = 1;

const int SA_RESTART = 2;

const int SA_RESETHAND = 4;

const int SA_NOCLDSTOP = 8;

const int SA_NODEFER = 16;

const int SA_NOCLDWAIT = 32;

const int SA_SIGINFO = 64;

const int SA_USERTRAMP = 256;

const int SA_64REGSET = 512;

const int SA_USERSPACE_MASK = 127;

const int SIG_BLOCK = 1;

const int SIG_UNBLOCK = 2;

const int SIG_SETMASK = 3;

const int SI_USER = 65537;

const int SI_QUEUE = 65538;

const int SI_TIMER = 65539;

const int SI_ASYNCIO = 65540;

const int SI_MESGQ = 65541;

const int SS_ONSTACK = 1;

const int SS_DISABLE = 4;

const int MINSIGSTKSZ = 32768;

const int SIGSTKSZ = 131072;

const int SV_ONSTACK = 1;

const int SV_INTERRUPT = 2;

const int SV_RESETHAND = 4;

const int SV_NODEFER = 16;

const int SV_NOCLDSTOP = 8;

const int SV_SIGINFO = 64;

const int PRIO_PROCESS = 0;

const int PRIO_PGRP = 1;

const int PRIO_USER = 2;

const int PRIO_DARWIN_THREAD = 3;

const int PRIO_DARWIN_PROCESS = 4;

const int PRIO_MIN = -20;

const int PRIO_MAX = 20;

const int PRIO_DARWIN_BG = 4096;

const int PRIO_DARWIN_NONUI = 4097;

const int RUSAGE_SELF = 0;

const int RUSAGE_CHILDREN = -1;

const int RUSAGE_INFO_V0 = 0;

const int RUSAGE_INFO_V1 = 1;

const int RUSAGE_INFO_V2 = 2;

const int RUSAGE_INFO_V3 = 3;

const int RUSAGE_INFO_V4 = 4;

const int RUSAGE_INFO_V5 = 5;

const int RUSAGE_INFO_V6 = 6;

const int RUSAGE_INFO_CURRENT = 6;

const int RU_PROC_RUNS_RESLIDE = 1;

const int RLIM_INFINITY = 9223372036854775807;

const int RLIM_SAVED_MAX = 9223372036854775807;

const int RLIM_SAVED_CUR = 9223372036854775807;

const int RLIMIT_CPU = 0;

const int RLIMIT_FSIZE = 1;

const int RLIMIT_DATA = 2;

const int RLIMIT_STACK = 3;

const int RLIMIT_CORE = 4;

const int RLIMIT_AS = 5;

const int RLIMIT_RSS = 5;

const int RLIMIT_MEMLOCK = 6;

const int RLIMIT_NPROC = 7;

const int RLIMIT_NOFILE = 8;

const int RLIM_NLIMITS = 9;

const int _RLIMIT_POSIX_FLAG = 4096;

const int RLIMIT_WAKEUPS_MONITOR = 1;

const int RLIMIT_CPU_USAGE_MONITOR = 2;

const int RLIMIT_THREAD_CPULIMITS = 3;

const int RLIMIT_FOOTPRINT_INTERVAL = 4;

const int WAKEMON_ENABLE = 1;

const int WAKEMON_DISABLE = 2;

const int WAKEMON_GET_PARAMS = 4;

const int WAKEMON_SET_DEFAULTS = 8;

const int WAKEMON_MAKE_FATAL = 16;

const int CPUMON_MAKE_FATAL = 4096;

const int FOOTPRINT_INTERVAL_RESET = 1;

const int IOPOL_TYPE_DISK = 0;

const int IOPOL_TYPE_VFS_ATIME_UPDATES = 2;

const int IOPOL_TYPE_VFS_MATERIALIZE_DATALESS_FILES = 3;

const int IOPOL_TYPE_VFS_STATFS_NO_DATA_VOLUME = 4;

const int IOPOL_TYPE_VFS_TRIGGER_RESOLVE = 5;

const int IOPOL_TYPE_VFS_IGNORE_CONTENT_PROTECTION = 6;

const int IOPOL_TYPE_VFS_IGNORE_PERMISSIONS = 7;

const int IOPOL_TYPE_VFS_SKIP_MTIME_UPDATE = 8;

const int IOPOL_TYPE_VFS_ALLOW_LOW_SPACE_WRITES = 9;

const int IOPOL_TYPE_VFS_DISALLOW_RW_FOR_O_EVTONLY = 10;

const int IOPOL_SCOPE_PROCESS = 0;

const int IOPOL_SCOPE_THREAD = 1;

const int IOPOL_SCOPE_DARWIN_BG = 2;

const int IOPOL_DEFAULT = 0;

const int IOPOL_IMPORTANT = 1;

const int IOPOL_PASSIVE = 2;

const int IOPOL_THROTTLE = 3;

const int IOPOL_UTILITY = 4;

const int IOPOL_STANDARD = 5;

const int IOPOL_APPLICATION = 5;

const int IOPOL_NORMAL = 1;

const int IOPOL_ATIME_UPDATES_DEFAULT = 0;

const int IOPOL_ATIME_UPDATES_OFF = 1;

const int IOPOL_MATERIALIZE_DATALESS_FILES_DEFAULT = 0;

const int IOPOL_MATERIALIZE_DATALESS_FILES_OFF = 1;

const int IOPOL_MATERIALIZE_DATALESS_FILES_ON = 2;

const int IOPOL_VFS_STATFS_NO_DATA_VOLUME_DEFAULT = 0;

const int IOPOL_VFS_STATFS_FORCE_NO_DATA_VOLUME = 1;

const int IOPOL_VFS_TRIGGER_RESOLVE_DEFAULT = 0;

const int IOPOL_VFS_TRIGGER_RESOLVE_OFF = 1;

const int IOPOL_VFS_CONTENT_PROTECTION_DEFAULT = 0;

const int IOPOL_VFS_CONTENT_PROTECTION_IGNORE = 1;

const int IOPOL_VFS_IGNORE_PERMISSIONS_OFF = 0;

const int IOPOL_VFS_IGNORE_PERMISSIONS_ON = 1;

const int IOPOL_VFS_SKIP_MTIME_UPDATE_OFF = 0;

const int IOPOL_VFS_SKIP_MTIME_UPDATE_ON = 1;

const int IOPOL_VFS_ALLOW_LOW_SPACE_WRITES_OFF = 0;

const int IOPOL_VFS_ALLOW_LOW_SPACE_WRITES_ON = 1;

const int IOPOL_VFS_DISALLOW_RW_FOR_O_EVTONLY_DEFAULT = 0;

const int IOPOL_VFS_DISALLOW_RW_FOR_O_EVTONLY_ON = 1;

const int IOPOL_VFS_NOCACHE_WRITE_FS_BLKSIZE_DEFAULT = 0;

const int IOPOL_VFS_NOCACHE_WRITE_FS_BLKSIZE_ON = 1;

const int WNOHANG = 1;

const int WUNTRACED = 2;

const int WCOREFLAG = 128;

const int _WSTOPPED = 127;

const int WEXITED = 4;

const int WSTOPPED = 8;

const int WCONTINUED = 16;

const int WNOWAIT = 32;

const int WAIT_ANY = -1;

const int WAIT_MYPGRP = 0;

const int _QUAD_HIGHWORD = 1;

const int _QUAD_LOWWORD = 0;

const int __DARWIN_LITTLE_ENDIAN = 1234;

const int __DARWIN_BIG_ENDIAN = 4321;

const int __DARWIN_PDP_ENDIAN = 3412;

const int __DARWIN_BYTE_ORDER = 1234;

const int LITTLE_ENDIAN = 1234;

const int BIG_ENDIAN = 4321;

const int PDP_ENDIAN = 3412;

const int BYTE_ORDER = 1234;

const int NULL = 0;

const int EXIT_FAILURE = 1;

const int EXIT_SUCCESS = 0;

const int RAND_MAX = 2147483647;
