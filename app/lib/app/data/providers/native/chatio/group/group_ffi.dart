/*
 * @Author: your name
 * @Date: 2022-04-22 11:36:18
 * @LastEditTime : 2022-05-09 15:05:27
 * @LastEditors  : Daen
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath     : /flutter_metatel/lib/app/data/providers/native/chatio/group/group_ffi.dart
 */

import '../../isolate_data_channel.dart';
import 'isolate.dart';

class GroupFFI {
  GroupFFI() {
    _init();
  }
  final IsolateDataChannel _channel = IsolateDataChannel('group_channel');

  void _init() {
    _channel.createNewIsolate(createIsolate, _channel.rootSendPort);
  }

  Future<List<Map<String, String>>> groupParse(String inFile) async {
    dynamic result = await _channel.sendData(IsolateEvent.groupParse, inFile);
    await destroyIsolate();
    return result as List<Map<String, String>>;
  }

  Future<void> destroy() async {
    await _channel.sendData(IsolateEvent.destroy, '');
  }

  Future<void> destroyIsolate() async {
    await _channel.sendData(IsolateEvent.destroy, '');
    _channel.destroyNewIsolate();
  }
}
