/*
 * @Author: your name
 * @Date: 2022-04-22 11:13:35
 * @LastEditTime : 2022-05-09 15:05:36
 * @LastEditors  : Daen
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath     : /flutter_metatel/lib/app/data/providers/native/chatio/group/isolate.dart
 */

import 'dart:isolate';

import '../../isolate_data_channel.dart';
import 'group.dart';

late final Group _gGroup;
late final SendPort _gRootSendPort;

class IsolateEvent {
  static String groupParse = 'groupParse';
  static String destroy = 'destroy';
}

/// 创建Endpoint隔离区
void createIsolate(dynamic isolateData) {
  ReceivePort newIsolateReceivePort = ReceivePort();
  newIsolateReceivePort.listen(messageParse);

  _gRootSendPort = isolateData as SendPort;
  _gGroup = Group();

  // 把当前的sendPort发送到主Isolate中
  IsolateMessageEvent data =
      IsolateMessageEvent(sendPortResponse, '', newIsolateReceivePort.sendPort);
  _gRootSendPort.send(data);
}

/// 消息解析
void messageParse(dynamic message) async {
  var event = message as IsolateMessageEvent;

  Object? resultData;
  if (event.action == IsolateEvent.groupParse) {
    resultData = _gGroup.groupParse(event.data);
  } //
  else if (event.action == IsolateEvent.destroy) {
    _gGroup.destroy();
  } //

  IsolateMessageEvent sendData =
      IsolateMessageEvent(event.action, event.uuid, resultData);
  _gRootSendPort.send(sendData);
}
