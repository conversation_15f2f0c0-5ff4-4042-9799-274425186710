/*
 * @Author: your name
 * @Date: 2022-04-22 10:44:09
 * @LastEditTime : 2022-05-06 18:04:57
 * @LastEditors  : Daen
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath     : /flutter_metatel/lib/app/data/providers/chatio/group/group.dart
 */
import 'dart:convert';
import 'dart:ffi';

import 'package:ffi/ffi.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';

import '../../native_library.dart';
import '../chatio_generated_bindings.dart';

class Group {
  Group() {
    _init();
  }

  late final ChatIO _chatio;
  ChatIOGroupPtr _handle = nullptr;

  void _init() {
    _chatio = ChatIO(chatioNativeLibrary());
    _chatio.FFI_Util_Logger(chatioLogLevel);
  }

  /// 解析群信息文件
  List<Map<String, String>> groupParse(String inFile) {
    Pointer<Utf8> ptrInFile = inFile.toNativeUtf8();
    List<Map<String, String>> listResultData = [];

    try {
      do {
        _handle = _chatio.FFI_Group_Parse(ptrInFile.cast());
        if (_handle == nullptr) {
          break;
        }

        var master = _handle.ref.master;
        var commits = _handle.ref.commits;

        String ownerID = master.owner.cast<Utf8>().toDartString();
        String body = utf8
            .decode(master.body_ptr.cast<Uint8>().asTypedList(master.body_len));

        // 群主信息放在列表首位
        Map<String, String> mapMasterData = {};
        mapMasterData['ownerID'] = ownerID;
        mapMasterData['body'] = body;
        listResultData.add(mapMasterData);

        // 遍历附加信息
        for (int i = 0; i < _handle.ref.commits_len; i++) {
          var commitData = commits[i];
          String ownerID = commitData.owner.cast<Utf8>().toDartString();
          String body = utf8.decode(commitData.body_ptr
              .cast<Uint8>()
              .asTypedList(commitData.body_len));

          // 附加信息依次放入列表
          Map<String, String> mapCommitData = {};
          mapCommitData['ownerID'] = ownerID;
          mapCommitData['body'] = body;
          listResultData.add(mapCommitData);
        }
      } while (false);
    } catch (e, s) {
      AppLogger.d('$e');
      AppLogger.d('$s');
    }
    _freePtr(ptrInFile);

    return listResultData;
  }

  /// 销毁句柄
  void destroy() {
    if (_handle == nullptr) {
      return;
    }
    _chatio.FFI_Group_Free(_handle);
    _handle = nullptr;
  }

  /// 指针释放
  void _freePtr(Pointer ptr) {
    if (ptr != nullptr) {
      calloc.free(ptr);
    }
  }
}
