/*
 * <AUTHOR> <PERSON><PERSON>
 * @Date         : 2022-04-22 14:17:41
 * @Description  : 获取动态库的目录
 * @LastEditors  : <PERSON><PERSON>
 * @LastEditTime : 2022-06-28 14:46:06
 * @FilePath     : /flutter_metatel/lib/app/data/providers/native/native_library.dart
 */

import 'dart:ffi';
import 'dart:io';

import 'package:flutter/foundation.dart';

const int _chatioLogLevel = 0;
 int  get chatioLogLevel => kDebugMode?4:0;

DynamicLibrary chatioNativeLibrary() {
  return Platform.isMacOS || Platform.isIOS
      ? DynamicLibrary.process() // macos and ios
      : (DynamicLibrary.open(Platform.isWindows // windows
          ? 'libchatio_ffi.dll'
          : 'libchatio_ffi.so')); // android and linux
}
