/*
 * @Author: your name
 * @Date: 2022-02-18 19:12:56
 * @LastEditTime : 2022-05-09 16:06:49
 * @LastEditors  : Daen
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath     : /flutter_metatel/lib/app/data/providers/native/isolate_data_channel.dart
 */

import 'dart:async';
import 'dart:isolate';
import 'package:flutter/foundation.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:flutter_metatel/core/utils/util.dart';

const String sendPortResponse = 'sendPortResponse';
typedef NewIsolateMessageCallback = void Function(dynamic message);

class IsolateMessageEvent {
  IsolateMessageEvent(this.action, this.uuid, this.data);
  IsolateMessageEvent.fromSendPortResponse(this.data,
      {this.action = sendPortResponse, this.uuid = ''});

  final String action;
  final String uuid;
  final dynamic data;
}

class IsolateDataChannel {
  IsolateDataChannel(this._isolateLabel) {
    _rootIsolateReceivePort.listen(_receiveData);

    // 定时任务
    Timer.periodic(const Duration(milliseconds: 500), (timer) {
      if (_newIsolateSendPort != null && _listWaitSendData.isEmpty) {
        timer.cancel();
      }

      if (_newIsolateSendPort != null && _listWaitSendData.isNotEmpty) {
        _newIsolateSendPort!.send(_listWaitSendData.removeAt(0));
      }
    });
  }

  /// 标签
  final String _isolateLabel;

  /// 存储数据返回对象
  final Map<String, Completer<dynamic>> _mapDataResult = {};

  /// isolate通信通道
  final ReceivePort _rootIsolateReceivePort = ReceivePort();

  /// 新isolate，发送数据对象
  SendPort? _newIsolateSendPort;

  /// 接收到新isolate消息回调
  NewIsolateMessageCallback? _messageCallback;

  /// 等待发送的数据
  final List<IsolateMessageEvent> _listWaitSendData = [];

  /// 新隔离区句柄
  Isolate? _newIsolate;

  /// 获取主Isolate发送端口
  SendPort get rootSendPort => _rootIsolateReceivePort.sendPort;

  /// 设置消息回调
  void setNewIsolateMessageCallback(NewIsolateMessageCallback call) {
    _messageCallback = call;
  }

  /// 创建新Isolate
  void createNewIsolate(
      void Function(dynamic data) create, dynamic data) async {
    _newIsolate = await Isolate.spawn(create, data);
  }

  void createSendPortIsolate(void Function(dynamic data) create) async {
    _newIsolate = await Isolate.spawn(create, rootSendPort);
  }

  /// 销毁新Isolate
  void destroyNewIsolate() {
    _newIsolate?.kill(priority: Isolate.immediate);
    _newIsolate = null;
    _rootIsolateReceivePort.close();
  }

  /// 向新的Isolate发送数据
  Future<dynamic> sendData(String action, dynamic data) {
    // 构建事件
    IsolateMessageEvent event = IsolateMessageEvent(action, uuid(), data);

    // 异步数据结果返回对象
    Completer<dynamic> result = Completer<dynamic>();
    _mapDataResult[event.uuid] = result;

    // AppLogger.d(
    //     '$_isolateLabel - action:${event.action} send new isolate data:${event.data}');

    if (_newIsolateSendPort != null) {
      _newIsolateSendPort!.send(event);
    } else {
      _listWaitSendData.add(event);
    }

    return result.future;
  }

  /// 接收新的Isolate数据
  void _receiveData(dynamic message) {
    var event = message as IsolateMessageEvent;
    if (event.action == sendPortResponse) {
      _newIsolateSendPort ??= event.data as SendPort;
    } else {
      String uuid = event.uuid;
      if (_mapDataResult.containsKey(uuid)) {
        var completer = _mapDataResult[uuid]!;
        if (!completer.isCompleted) {
          completer.complete(event.data);
        }
        _mapDataResult.remove(uuid);
      } else {
        _messageCallback?.call(message);
      }
    }

    // AppLogger.d(
    //     '$_isolateLabel - action:${event.action} receive new isolate data:${event.data}');
  }
}
