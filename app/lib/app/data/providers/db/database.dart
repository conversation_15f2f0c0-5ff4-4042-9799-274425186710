/*
 * <AUTHOR> <PERSON><PERSON>
 * @Date         : 2022-04-21 11:28:07
 * @Description  : 数据库相关
 * @LastEditors: luo<PERSON> <EMAIL>
 * @LastEditTime: 2022-05-07 21:30:28
 * @FilePath     : /flutter_metatel/lib/app/data/providers/db/database.dart
 */
import 'dart:ffi';
import 'dart:io';

import 'package:drift/drift.dart';
import 'package:drift/native.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_metatel/app/data/models/backup_model.dart';
import 'package:flutter_metatel/core/task/time_task.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:sqlite3/sqlite3.dart';
part 'database.g.dart';

@DriftDatabase(
  // .drift文件相关的 import , drift 也支持 `package:` 的 import
  include: {'tables.drift'},
)
class AppDatabase extends _$AppDatabase {
  AppDatabase(String dbPath, {String? dbKey})
      : super(_openConnection(dbPath, dbKey: dbKey));
  @override
  MigrationStrategy get migration {
    AppLogger.d('MigrationStrategy ....');

    return MigrationStrategy(
      onCreate: (Migrator m) async {
        await m.createAll();
      },
      onUpgrade: (Migrator m, int from, int to) async {
        AppLogger.d('MigrationStrategy from$from ...to=$to');
        if (from < 2) {
          // we added the dueDate property in the change from version 1 to
          // version 2
          await addColumn(m, channelInfo, channelInfo.minMsgUuid);
          await addColumn(m, channelInfo, channelInfo.maxMsgUuid);
          await addColumn(m, channelInfo, channelInfo.maxMsgTime);
        }
        if (from < 3) {
          // we added the dueDate property in the change from version 1 to
          // version 2

          await addColumn(m, message, message.at);
        }
        if (from < 4) {
          await addColumn(m, session, session.at);
        }
        if (from < 5) {
          await addColumn(m, channelInfo, channelInfo.backgroundPath);
          await addColumn(m, channelInfo, channelInfo.backgroundUrl);
          await addColumn(m, channelInfo, channelInfo.allMute);
          await addColumn(m, channelInfo, channelInfo.joinVerify);
          await addColumn(m, channelInfo, channelInfo.inviteLimit);
          await addColumn(m, groupMember, groupMember.role);
          await addColumn(m, groupMember, groupMember.startMuteTime);
          await addColumn(m, groupMember, groupMember.muteDuration);
        }
        if (from < 6) {
          await m.deleteTable('group_member');
          await m.createTable(groupMember);
        }
        if (from < 7) {
          await addColumn(m, groupMember, groupMember.muteExpire); //增加禁言到期时间字段
        }
        if (from < 8) {
          await updateSessionTopNull();
          await m.alterTable(TableMigration(session)); // top字段，添加 DEFAULT约束
        }
        if (from < 9) {
          await addColumn(
              m, contact, contact.chatBackgroundUrl); //增加聊天背景文件url地址字段
          await addColumn(
              m, contact, contact.chatBackgroundPath); //增加聊天背景文件路径字段
        }
        if (from < 10) {
          await addColumn(
              m, groupInfo, groupInfo.backgroundPath); //增加聊天背景文件url地址字段
          await addColumn(m, groupInfo, groupInfo.backgroundUrl); //增加聊天背景文件路径字段
          await addColumn(m, groupInfo, groupInfo.invalid); //增加群是否无效
        }
        if (from < 13) {
          await addColumn(m, groupInfo, groupInfo.propertyFragment);
          await addColumn(
              m, contact, contact.chatBackgroundUrl); //增加聊天背景文件url地址字段
          await addColumn(
              m, contact, contact.chatBackgroundPath); //增加聊天背景文件路径字段
        }
        if (from < 14) {
          await addColumn(m, message, message.resourceUuid); //增加项资源uuid字段
        }
        if (from < 15) {
          await addColumn(m, message, message.hasShown); //增加是已经在消息列表显示过
        }
        if (from < 16) {
          await addColumn(m, message, message.messageHasRead); //增加是已经在消息列表显示过
        }
        if (from < 17) {
          await addColumn(m, message, message.fileDuration); //添加音视频文件时长字段
        }
        if (from < 18) {
          await addColumn(m, message, message.undoEdit); //撤回消息是否可再次编辑
          await addColumn(
              m, message, message.userNameFileHelper); //消息 添加userNameFileHelper
          await addColumn(m, contact, contact.type); //联系人添加类型
        }
        if (from < 19) {
          await m.createTable(proxyInfo); // 添加前置点表
        }
        if (from < 20) {
          await addColumn(m, groupInfo, groupInfo.announcement); // 添加群公告
          await addColumn(m, channelInfo, channelInfo.announcement); // 添加群公告
        }
        if (from < 21) {
          await addColumn(m, channelInfo, channelInfo.attribute); // 添加频道属性
        }
        if (from < 22) {
          await addColumn(m, contact, contact.isBlack); // 添加黑名单
        }
        if (from < 23) {
          await addColumn(m, channelInfo, channelInfo.options); // 添加options 字段，json字符串
        }
        if (from < 24) {
          await addColumn(m, message, message.expand); // 添加扩展 字段，json字符串
        }
        if (from < 25) {
          await addColumn(m, message, message.thumbnailFileState);
        }
        if (from < 26) {
          await addColumn(m, message, message.noises);
        }
        if (from < 27) {
          await m.createTable(messageTop); /// 添加置顶消息表
        }
        if (from < 28) {
          await m.createTable(contactWallet); /// 添加联系人钱包表
        }
        if (from < 29) {
          await addColumn(m, session, session.silence);
        }
        if (from < 30) {
          await addColumn(m, proxyInfo, proxyInfo.user);
          await addColumn(m, proxyInfo, proxyInfo.pwd);
        }
        if (from < 31) {
          await m.createTable(adInfo); /// 添加广告表
        }
        if (from < 32) {
          await m.createTable(messageTopAdmin); /// 添加群管理或群主置顶消息表
        }
        if (from < 33) {
          await addColumn(m, channelInfo, channelInfo.limit); /// 群信息表添加人员限制字段
        }
        if (from < 34) {
          await addColumn(m, message, message.hasIdentify); /// 群信息表添加人员限制字段
        }
        if (from < 35) {
          await addColumn(m, contact, contact.isTid); /// 添加是否有tid标识
          await addColumn(m, session, session.isTid); /// 添加是否有tid标识
        }
        if (from < 36) {
          await addColumn(m, channelInfo, channelInfo.memberCount); ///
          await addColumn(m, groupInfo, groupInfo.memberCount); ///
        }
        if (from < 37) {
          await addColumn(m, proxyInfo, proxyInfo.isHttp); ///
        }
        if (from < 38) {
          await addColumn(m, message, message.displayName); ///
          await addColumn(m, messageTop, messageTop.displayName); ///
        }
        if (from < 39) {
          await addColumn(m, message, message.translateMsg); ///
        }
        if (from < 40) {
          /// 添加dao相关属性
          await addColumn(m, channelInfo, channelInfo.chain); /// 添加链id
          await addColumn(m, channelInfo, channelInfo.tokenAddress); /// 添加代币地址
          await addColumn(m, channelInfo, channelInfo.minNumToken); /// 添加持币最低门槛
          await addColumn(m, channelInfo, channelInfo.tags); /// 频道信息，添加dao的标签
          await addColumn(m, groupMember, groupMember.tags); /// 成员信息，添加dao的标签
        }
      },
    );
  }

  Future<void> addColumn(
      Migrator m, TableInfo table, GeneratedColumn column) async {
    return m.addColumn(table, column).catchError((e) {
      if (kDebugMode) {
        print('addColumn error ${e.toString()}');
      }
    });
  }

  @override
  int get schemaVersion => 40;

  Future<int> insertContactData(ContactCompanion data) async {
    // if (data.username.value.isEmpty) return -1;

    // return into(contact).insert(data);
    return insertOrUpdateContactData(data);
  }

  Future<int> updateContactData(ContactCompanion data) async {
    // if (data.username.value.isEmpty) return -1;

    // return (update(contact)
    //       ..where((t) => t.username.equals(data.username.value)))
    //     .write(data);
    return insertOrUpdateContactData(data);
  }

  Selectable<SessionData> allSessionByChatType(int chatType) {
    return customSelect(
        'SELECT * FROM session WHERE chat_type =?1 ORDER BY top DESC, time DESC',
        variables: [Variable<int>(chatType)],
        readsFrom: {
          session,
        }).asyncMap(session.mapFromRow);
  }

  Selectable<GroupInfoData> groupInfoIsInvalid(List<String> groupIds) {
    var $arrayStartIndex = 1;
    final expandedvar2 = $expandVar($arrayStartIndex, groupIds.length);
    $arrayStartIndex += groupIds.length;
    return customSelect('SELECT * FROM group_info WHERE invalid = false and group_id in ($expandedvar2)',
        variables: [
          for (var $ in groupIds) Variable<String>($)
        ],
        readsFrom: {
          groupInfo,
        }).asyncMap(groupInfo.mapFromRow);
  }

  Selectable<ContactData> contactInfoByName(List<String> names) {
    var sql = '';
    bool isFirset = true;
    for (var n in names) {
      sql = '$sql ${isFirset ? '' : 'or'} username like \'%$n%\'';
      isFirset = false;
    }
    var totalSql = 'SELECT * FROM contact WHERE $sql';
    return customSelect(totalSql, variables: [], readsFrom: {
      contact,
    }).asyncMap(contact.mapFromRow);
  }

  Future<int> insertOrUpdateContactData(ContactCompanion data) async {
    if (data.username.value.isEmpty) return -1;

    return into(contact).insert(
      data,
      onConflict: DoUpdate(
            (_) => data.copyWith(createTime: const Value.absent()),
        target: [contact.username],
      ),
    );
  }

  Future<void> insertOrUpdateContactDatas(List<ContactCompanion> rows) async {
    if (rows.isEmpty) return;

    await batch((batch) {
      for (final row in rows) {
        batch.insert(
          contact,
          row,
          onConflict: DoUpdate(
                (_) => row.copyWith(createTime: const Value.absent()),
            target: [contact.username],
          ),
        );
      }
    });
  }

  Future<int> insertOrUpdateSessionData(SessionCompanion data) async {
    if (data.username.value.isEmpty) return -1;

    SessionCompanion newData = data;
    if (data.time.value == null) {
      newData = data.copyWith(
          time: ofNullable(TimeTask.instance.getNowTime()));
    }
    // if (data.isTid.value == null) {
    //   newData = data.copyWith(
    //       isTid: ofNullable(true));
    // }
    AppLogger.d('newData ');
    return into(session).insert(
      newData,
      onConflict: DoUpdate(
            (_) => data.copyWith(createTime: const Value.absent()),
        target: [session.username],
      ),
    );
  }

  Future<int> insertMessageData(MessageCompanion data) async {
    AppLogger.d('insertMessageData = $data');
    if (data.msgId.value.isEmpty) return -1;
    // Future<int> value;
    // value = into(message).insert(data).catchError((e) {
    //   AppLogger.e('insertMessageData  error= ' + e.toString());
    //   value = updateMessageData(data);
    // });

    return into(message).insert(
      data,
      onConflict: DoUpdate(
            (_) => data,
        target: [message.msgId],
      ),
    );
    //return value;
  }

  Future<int> updateMessageData(MessageCompanion data) async {
    if (data.msgId.value.isEmpty) return -1;
    // return (update(message)..where((t) => t.msgId.equals(data.msgId.value)))
    //     .write(data);
    return insertMessageData(data);
  }

  Future<void> insertOrUpdateMessageData(List<MessageCompanion> rows) async {
    if (rows.isEmpty) return;

    await batch((batch) {
      for (final row in rows) {
        batch.insert(
          message,
          row,
          onConflict: DoUpdate(
                (_) => row.copyWith(createTime: const Value.absent()),
            target: [message.msgId],
          ),
        );
      }
    });
  }
  Future<void> insertOrUpdateAdInfoData(List<AdInfoCompanion> rows) async {
    if (rows.isEmpty) return;
    await batch((batch) {
      for (final row in rows) {
        batch.insert(
          adInfo,
          row,
        );
      }
    });
  }

  Selectable<ContactData> searchContact(String key) {
    return customSelect(
        'SELECT * FROM contact WHERE (localname like \'%$key%\'  or displayname like \'%$key%\') and state=0',
        variables: [],
        readsFrom: {
          contact,
        }).asyncMap(contact.mapFromRow);
  }
  Selectable<ContactData> searchContactByUserName(String key) {
    return customSelect(
        'SELECT * FROM contact WHERE username like \'%$key%\'  ',
        variables: [],
        readsFrom: {
          contact,
        }).asyncMap(contact.mapFromRow);
  }

  Selectable<MessageData> searchMessage(String key) {
    return customSelect(
        'SELECT * FROM message WHERE body like \'%$key%\' and type=0 order by time desc',
        variables: [],
        readsFrom: {
          message,
        }).asyncMap(message.mapFromRow);
  }

  Selectable<ChannelInfoData> searchChannel(String key) {
    return customSelect(
        'SELECT * FROM channel_info WHERE title like \'%$key%\' order by update_time desc',
        variables: [],
        readsFrom: {
          channelInfo,
        }).asyncMap(channelInfo.mapFromRow);
  }

  Selectable<GroupInfoData> searchGroup(String key) {
    return customSelect(
        'SELECT * FROM group_info WHERE title like \'%$key%\' order by update_time desc',
        variables: [],
        readsFrom: {
          groupInfo,
        }).asyncMap(groupInfo.mapFromRow);
  }
  Future<int> updateMessageTranslate(String? translateMsg, String msgId) {
    return customUpdate(
      'UPDATE message SET translate_msg = ?1 WHERE msg_id = ?2 ',
      variables: [Variable<String>(translateMsg),Variable<String>(msgId)],
      updates: {message},
      updateKind: UpdateKind.update,
    );
  }
  Future<int> updateMessageStateForError() {
    return customUpdate(
      'UPDATE  message SET state=-1 WHERE direction =1 AND (state=1 or state=0)',
      variables: [],
      updates: {message},
      updateKind: UpdateKind.update,
    );
  }

  Future<int> updateSessionSetAvatarEmpty(List<String> var1) {
    var $arrayStartIndex = 1;
    final expandedvar1 = $expandVar($arrayStartIndex, var1.length);
    $arrayStartIndex += var1.length;
    return customUpdate(
      'UPDATE session SET avatar_path = null WHERE username IN ($expandedvar1)',
      variables: [for (var $ in var1) Variable<String>($)],
      updates: {session},
      updateKind: UpdateKind.update,
    );
  }

  Future<int> updateChannelInfoSetAvatarEmpty(List<String> var1) {
    var $arrayStartIndex = 1;
    final expandedvar1 = $expandVar($arrayStartIndex, var1.length);
    $arrayStartIndex += var1.length;
    return customUpdate(
      'UPDATE channel_info SET avatar_path = null WHERE channel_id IN ($expandedvar1)',
      variables: [for (var $ in var1) Variable<String>($)],
      updates: {channelInfo},
      updateKind: UpdateKind.update,
    );
  }

  Future<int> updateContactSetAvatarEmpty(List<String> var1) {
    var $arrayStartIndex = 1;
    final expandedvar1 = $expandVar($arrayStartIndex, var1.length);
    $arrayStartIndex += var1.length;
    return customUpdate(
      'UPDATE contact SET avatar_path = null WHERE username IN ($expandedvar1)',
      variables: [for (var $ in var1) Variable<String>($)],
      updates: {contact},
      updateKind: UpdateKind.update,
    );
  }

  Future<int> updateMessageOneEmojiForHasShown(String userName) {
    return customUpdate(
      'UPDATE  message SET has_shown = true WHERE owner == \'$userName\'',
      variables: [],
      updates: {message},
      updateKind: UpdateKind.update,
    );
  }



  Selectable<ContactData> allContactWithoutUserName(String username) {
    return customSelect(
        'SELECT * FROM contact WHERE state = 0 and username  != \'$username\'',
        variables: [],
        readsFrom: {
          contact,
        }).asyncMap(contact.mapFromRow);
  }
  Future<int> cleanMsgByTime(int? time) {
    return customUpdate(
      'DELETE FROM message WHERE  chat_type = 2 and time < ?1',
      variables: [Variable<int>(time)],
      updates: {message},
      updateKind: UpdateKind.delete,
    );
  }

  Future<int> updateAudioFileDuration(String duration,String msgId) {
    return customUpdate(
      'UPDATE message SET file_duration = ?1 WHERE msg_id = ?2 ',
      variables: [Variable<String>(duration),Variable<String>(msgId)],
      updates: {message},
      updateKind: UpdateKind.update,
    );
  }
  Future<void> insertOrUpdateChannelInfoData(
      List<ChannelInfoCompanion> rows) async {
    if (rows.isEmpty) return;

    await batch((batch) {
      for (final row in rows) {
        batch.insert(
          channelInfo,
          row,
          onConflict: DoUpdate(
                (_) => row.copyWith(createTime: const Value.absent()),
            target: [channelInfo.channelId],
          ),
        );
      }
    });
  }

  Future<void> insertOrUpdateGroupInfoData(
      List<GroupInfoCompanion> rows) async {
    if (rows.isEmpty) return;

    await batch((batch) {
      for (final row in rows) {
        batch.insert(
          groupInfo,
          row,
          onConflict: DoUpdate(
                (_) => row.copyWith(createTime: const Value.absent()),
            target: [groupInfo.groupId],
          ),
        );
      }
    });
  }

  Future<void> insertOrUpdateMemberData(List<GroupMemberCompanion> rows) async {
    if (rows.isEmpty) return;

    await batch((batch) {
      for (final row in rows) {
        batch.insert(
          groupMember,
          row,
          onConflict: DoUpdate(
                (_) => row.copyWith(createTime: const Value.absent()),
            target: [groupMember.memberUuid],
          ),
        );
      }
    });
  }

  Selectable<ContactData> allContactByChatBackgroundPath(String path) {
    return customSelect(
        'SELECT * FROM contact WHERE state = 0 and chat_background_path == \'$path\'',
        variables: [],
        readsFrom: {
          contact,
        }).asyncMap(contact.mapFromRow);
  }

  Selectable<ContactData> allContactByAvatarPathPath(String path) {
    return customSelect(
        'SELECT * FROM contact WHERE state = 0 and avatarPath == \'$path\'',
        variables: [],
        readsFrom: {
          contact,
        }).asyncMap(contact.mapFromRow);
  }

  Selectable<ChannelInfoData> allChannelInfoByChatBackgroundPathOrAvatarPath(
      String path) {
    return customSelect(
        'SELECT * FROM channel_info  WHERE background_path == \'$path\' or  avatar_path == \'$path\'',
        variables: [],
        readsFrom: {
          channelInfo,
        }).asyncMap(channelInfo.mapFromRow);
  }

  Selectable<ChannelInfoData>
  allChannelInfoByChatBackgroundPathOrAvatarPathOwner(
      String path, String owner) {
    return customSelect(
        'SELECT * FROM channel_info  WHERE background_path == \'$path\' or  avatar_path == \'$path\' and owner== \'$path\'',
        variables: [],
        readsFrom: {
          channelInfo,
        }).asyncMap(channelInfo.mapFromRow);
  }


  Future<int> insertOrUpdateProxyInfoData(ProxyInfoCompanion data) async {
    if (data.uuid.value.isEmpty) return -1;

    return into(proxyInfo).insert(
      data,
      onConflict: DoUpdate(
            (_) => data.copyWith(createTime: const Value.absent()),
        target: [proxyInfo.uuid],
      ),
    );
  }

  /// 数据迁移函数
  Future<void> insterContactDatas(List<ContactData> datas) async {
    return batch((batch) {
      batch.insertAll(contact, datas, mode: InsertMode.insertOrIgnore);
    });
  }

  Future<void> insterGroupInfoDatas(List<GroupInfoData> datas) async {
    return batch((batch) {
      batch.insertAll(groupInfo, datas, mode: InsertMode.insertOrIgnore);
    });
  }

  Future<void> insterGroupMemberDatas(List<GroupMemberData> datas) async {
    return batch((batch) {
      batch.insertAll(groupMember, datas, mode: InsertMode.insertOrIgnore);
    });
  }

  Future<void> insterLogDatas(List<LogData> datas) async {
    return batch((batch) {
      batch.insertAll(log, datas, mode: InsertMode.insertOrIgnore);
    });
  }

  Future<void> insterMessageDatas(List<MessageData> datas) async {
    return batch((batch) {
      batch.insertAll(message, datas, mode: InsertMode.insertOrIgnore);
    });
  }

  Future<void> insterSessionDatas(List<SessionData> datas) async {
    return batch((batch) {
      batch.insertAll(session, datas, mode: InsertMode.insertOrIgnore);
    });
  }

  Future<void> insterChannelInfoDatas(List<ChannelInfoData> datas) async {
    return batch((batch) {
      batch.insertAll(channelInfo, datas, mode: InsertMode.insertOrIgnore);
    });
  }

  Future<void> insterProxyInfoDatas(List<ProxyInfoData> datas) async {
    return batch((batch) {
      batch.insertAll(proxyInfo, datas, mode: InsertMode.insertOrIgnore);
    });
  }

  Future<void> vacuum() async {
    await customStatement('VACUUM');
  }

  Future<void> createIndex() async {
    await customStatement(
        'DROP INDEX MsgIndex')
        .catchError((e) {
      AppLogger.e("createIndex e:${e.toString()}");

    });
    return await customStatement(
        'CREATE INDEX MsgIndex ON message (owner,state,type,time) ')
        .catchError((e) {});
  }

  clearAllChannelMsgDataMoreThen1000() async {
    var listChannel =await allChannelInfoDatas().get();
    for (var element in listChannel) {
      var ret = await channelChatMsgDelMoreThen1000(element.channelId);
      AppLogger.d("channelChatMsgDelMoreThen1000 ret:$ret channelId:${element.channelId} title:${element.title}");
    }
  }
  Future insertOrUpdateTopMsg(
      List<MessageTopCompanion> rows) async {
    if (rows.isEmpty) return;

    await batch((batch) {
      for (final row in rows) {
        batch.insert(
          messageTop,
          row,
          onConflict: DoUpdate(
                (_) => row.copyWith(createTime: const Value.absent()),
            target: [messageTop.msgId],
          ),
        );
      }
    });
  }
  Future insertOrUpdateTopMsgAdmin(
      List<MessageTopAdminCompanion> rows) async {
    if (rows.isEmpty) return;

    await batch((batch) {
      for (final row in rows) {
        batch.insert(
          messageTopAdmin,
          row,
          onConflict: DoUpdate(
                (_) => row.copyWith(createTime: const Value.absent()),
            target: [messageTopAdmin.msgId],
          ),
        );
      }
    });
  }
  Future insertOrUpdateContactWallet(
      List<ContactWalletCompanion> rows) async {
    if (rows.isEmpty) return;

    await batch((batch) {
      for (final row in rows) {
        batch.insert(
          contactWallet,
          row,
          onConflict: DoUpdate(
                (_) => row.copyWith(createTime: const Value.absent()),
            target: [contactWallet.address],
          ),
        );
      }
    });
  }

  Future<int> deleteChannelTopMessageAdmin(String? owner, List<String> var2) {
    var $arrayStartIndex = 2;
    final expandedvar2 = $expandVar($arrayStartIndex, var2.length);
    $arrayStartIndex += var2.length;
    return customUpdate(
      'DELETE FROM message_top_admin WHERE owner = ?1 AND uuid IN ($expandedvar2)',
      variables: [
        Variable<String>(owner),
        for (var $ in var2) Variable<String>($)
      ],
      updates: {messageTopAdmin},
      updateKind: UpdateKind.delete,
    );
  }
  Future<int> deleteChannelTopMessageAdminByMsgId(String msgId) {
    return customUpdate(
      'DELETE FROM message_top_admin WHERE  msg_id = ?1',
      variables: [
        Variable<String>(msgId),
      ],
      updates: {messageTopAdmin},
      updateKind: UpdateKind.delete,
    );
  }


  Selectable<String> userNameByWalletAddress(String? address) {
    return customSelect(
        'SELECT user_name FROM contact_wallet WHERE address = ?1 ',
        variables: [
          Variable<String>(address)
        ],
        readsFrom: {
          messageTopAdmin,
        }).map((QueryRow row) => row.read<String>('user_name'));
  }

  Selectable<SessionBackInfo> allSessionIsSilence() {
    return customSelect(
        'SELECT username, top FROM session WHERE silence = TRUE',
        variables: [],
        readsFrom: {
          session,
        }).map((QueryRow row) {
      return SessionBackInfo(
        username: row.read<String>('username'),
      );
    });
  }

  Selectable<MessageData> allFileMessage() {
    return customSelect(
        'SELECT * FROM message WHERE (file_path IS NOT NULL OR thumbnail_path IS NOT NULL)',
        variables: [],
        readsFrom: {
          message,
        }).asyncMap(message.mapFromRow);
  }

  Selectable<MessageData> selectMessageByFilePath(String path) {
    return customSelect(
        'SELECT * FROM message WHERE (file_path  = ?1  OR thumbnail_path = ?1 )',
        variables: [Variable<String>(path)],
        readsFrom: {
          message,
        }).asyncMap(message.mapFromRow);
  }

}
ss(Database db) {
  // 查询版本号
  var result = db.select('pragma cipher_version');
  if (result.isEmpty) {
    throw UnsupportedError('此数据库需要使用 SQLCipher 运行，但该库不可用！');
  }
  // 设置密码
  db.execute("pragma key = '${db.pwd}'");
  // 测试数据库是否打开
  try {
    result = db.select('select count(*) from sqlite_master');
    AppLogger.d('数据库打开成功');
  } catch (e) {
    throw UnsupportedError('数据库打开失败。pwd= ${db.pwd} error:$e');
  }
}
LazyDatabase _openConnection(String dbPath, {String? dbKey}) {
  // LazyDatabase 工具类可以让我们找一个合适的位置来存放异步文件。
  return LazyDatabase(() async {
    return NativeDatabase.createInBackground(
        File(dbPath),
        setup: dbKey == null
            ? null
            :ss,
        pwd: dbKey
    );
  });
}

Value<T?> ofNullable<T>(T? data) {
  if (data != null) {
    return Value(data);
  } else {
    return const Value.absent();
  }
}

Value<T> ofNotNull<T>(T data) {
  return Value(data);
}
