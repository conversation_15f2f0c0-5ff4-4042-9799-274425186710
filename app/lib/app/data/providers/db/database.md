

# 本地数据库设计

- 联系人

- 群信息

- 群成员

- 消息

- 会话

- 日志

## 联系人表

### 创建语句

```sqlite
CREATE TABLE IF NOT EXISTS contact (
  "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
  "username" TEXT,
  "displayname" TEXT,
  "localname" TEXT,
  "avatar_path" TEXT,
  "avatar_url" TEXT,
  "fragment" TEXT,
  "edit" BOOLEAN,
  "read" BOOLEAN,
  "state" INTEGER,
  "fistname" TEXT,
  "lastname" TEXT,
  "mobile" TEXT,
  "create_time" NUMERIC,
  "update_time" NUMERIC
);
```

### 字段说明

| 字段                   | 类型      | 描述                  |
|:---------------------|---------|---------------------|
| id                   | INTEGER | 数据库ID               |
| username             | TEXT    | 用户名（系统中使用）          |
| displayname          | TEXT    | 显示名（用户自己设置的昵称）      |
| localname            | TEXT    | 本地名（本地用户设置的备注）      |
| avatar_path          | TEXT    | 头像资源文件路径            |
| avatar_url           | TEXT    | 头像资源文件网络地址          |
| chat_background_path | TEXT    | 聊天背景文件路径            |
| chat_background_url  | TEXT    | 聊天背景文件网络地址          |
| fragment             | TEXT    | 文件解密指纹              |
| edit                 | BOOLEAN | 是否编辑过               |
| read                 | BOOLEAN | 是否阅读过               |
| state                | INTEGER | 状态 ：0 显示\           |1不显示 |
| fistname             | TEXT    | 姓氏                  |
| lastname             | TEXT    | 名字                  |
| mobile               | TEXT    | 电话                  |
| create_time          | NUMERIC | 创建时间                |
| update_time          | NUMERIC | 更新时间                |
| type                 | INTEGER | 联系人类型 0 普通 1 文件传输助手 |

## 群信息表

### 创建语句

```sqlite
CREATE TABLE IF NOT EXISTS group_info (
  "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
  "group_id" TEXT,
  "title" TEXT,
  "permission" INTEGER,
  "owner" TEXT,
  "avatar_url" TEXT,
  "avatar_path" TEXT,
  "avatar_fragment" TEXT,
  "property_url" TEXT,
  "property_path" TEXT,
  "describe" TEXT,
  "create_time" NUMERIC,
  "update_time" NUMERIC
);
```

### 字段说明

| 字段              | 类型      | 描述          |
|-----------------|---------|-------------|
| id              | INTEGER | 数据库ID       |
| group_id        | TEXT    | 群的唯一ID      |
| title           | TEXT    | 标题          |
| permission      | INTEGER | 权限：0x01 修改\ |0x02 邀请 |
| owner           | TEXT    | 群主          |
| avatar_path     | TEXT    | 头像资源文件路径    |
| avatar_url      | TEXT    | 头像资源文件网络地址  |
| avatar_fragment | TEXT    | 头像资源文件解密指纹  |
| property_url    | TEXT    | 属性文件路径      |
| property_path   | TEXT    | 属性文件网络地址    |
| describe        | TEXT    | 描述          |
| create_time     | NUMERIC | 创建时间        |
| update_time     | NUMERIC | 更新时间        |

## 群成员表

### 创建语句

```sqlite
CREATE TABLE IF NOT EXISTS group_member (
  "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
  "group_id" INTEGER,
  "username" TEXT,
  "displayname" TEXT,
  "uuid" INTEGER,
  "create_time" NUMERIC,
  "update_time" NUMERIC
);
```

### 字段说明

| 字段          | 类型      | 描述                      |
|-------------|---------|-------------------------|
| id          | INTEGER | 数据库ID                   |
| group_id    | TEXT    | 群的唯一ID                  |
| username    | TEXT    | 用户名（系统中使用）              |
| displayname | TEXT    | 显示名（用户自己设置的昵称）          |
| uuid        | TEXT    | 群成员唯一标识（群id+成员username） |
| create_time | NUMERIC | 创建时间                    |
| update_time | NUMERIC | 更新时间                    |



## 消息表

### 创建语句

```sqlite
CREATE TABLE IF NOT EXISTS message (
  "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
  "owner" TEXT,
  "body" TEXT,
  "file_path" TEXT,
  "file_url" TEXT,
  "file_fragment" TEXT,
  "file_size" INTEGER,
  "thumbnail_uri" TEXT,
  "thumbnail_url" TEXT,
  "thumbnail_fragment" TEXT,
  "self_destruct" BOOLEAN,
  "time" INTEGER,
  "type" INTEGER,
  "chat_type" INTEGER,
  "call_state" INTEGER,
  "state" INTEGER,
  "direction" INTEGER,
  "file_state" INTEGER,
  "msg_id" TEXT,
  "uuid" TEXT,
  "file_name" TEXT,
  "ext1" TEXT,
  "ack_number" INTEGER,
  "read" BOOLEAN,
  "replay_msg" TEXT,
  "marge_msg" TEXT,
  "contact_msg" TEXT,
  "share_msg" TEXT,
  "create_time" NUMERIC,
  "update_time" NUMERIC
  "resource_uuid" TEXT,
  "is_first" BOOLEAN,
);
```

### 字段说明

| 字段                    | 类型      | 描述                                                  |
|-----------------------|---------|-----------------------------------------------------|
| id                    | INTEGER | 数据库ID                                               |
| owner                 | TEXT    | 所属人的Username                                        |
| body                  | TEXT    | 消息内容                                                |
| file_path             | TEXT    | 消息资源文件本地路径                                          |
| file_url              | TEXT    | 消息资源文件地址                                            |
| file_fragment         | TEXT    | 消息资源文件指纹                                            |
| self_destruct         | BOOLEAN | 是否阅后即焚                                              |
| time                  | INTEGER | 消息的时间戳                                              |
| type                  | INTEGER | 消息类型                                                |
| chat_type             | INTEGER | 聊天类型：0单聊、1群聊                                        |
| call_state            | INTEGER | 通话状态                                                |
| state                 | INTEGER | 消息状态 ： -1 发送失败，0 默认状态，1、发送成功，2，发送失败，3对方收到           |
| direction             | INTEGER | 方向：0接收、1发送                                          |
| file_state            | INTEGER | 文件状态 0：文件下载成功 1：正在下载 2：下载、解密失败 3：合并消息文件解压成功，-1文件已删除 |
| msg_id                | TEXT    | 消息ID                                                |
| uuid                  | TEXT    | 底层消息ID                                              |
| file_name             | TEXT    | 消息名称                                                |
| ext1                  | TEXT    | 备用字段                                                |
| ack_number            | INTEGER | 已收到确认消息的数量                                          |
| read                  | BOOLEAN | 已读                                                  |
| replay_msg            | TEXT    | 回复消息体                                               |
| marge_msg             | TEXT    | 合并消息体                                               |
| contact_msg           | TEXT    | 联系人名片                                               |
| share_msg             | TEXT    | 分享消息                                                |
| create_time           | NUMERIC | 创建时间                                                |
| update_time           | NUMERIC | 更新时间                                                |
| resource_uuid         | TEXT    | 合并消息用的资源id                                          |
| has_shown             | BOOLEAN | 该消息是否已经在消息列表显示过                                     |
| message_has_read      | BOOLEAN | 该消息是否已读（语音消息需要点击听了才为true）                           | 
| file_duration         | INTEGER | 音视频时长                                               | 
| user_name_file_helper | TEXT    | 文件传输助手userName                                      | 



## 会话表



### 创建语句

```sqlite
CREATE TABLE IF NOT EXISTS session (
  "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
  "username" TEXT,
  "displayname" TEXT,
  "body" TEXT,
  "time" INTEGER,
  "top" BOOLEAN,
  "avatar_path" TEXT,
  "unread_count" INTEGER,
  "edit" BOOLEAN,
  "read" BOOLEAN,
  "type" INTEGER,
  "chat_type" INTEGER,
  "direction" INTEGER,
  "self_destruct" BOOLEAN,
  "ext1" TEXT,
  "state" INTEGER,
  "msg_id" TEXT,
  "uuid" TEXT,
  "explain" TEXT,
  "at" BOOLEAN,
  "create_time" NUMERIC,
  "update_time" NUMERIC
);
```

### 字段说明

| 字段            | 类型      | 描述（可以参考消息表）       |
|---------------|---------|-------------------|
| id            | INTEGER | 数据库ID             |
| username      | TEXT    | 用户名（系统中使用）        |
| body          | TEXT    | 消息内容              |
| displayname   | TEXT    | 显示名（用户自己设置的昵称）    |
| time          | INTEGER | 消息的时间戳            |
| top           | BOOLEAN | 是否置顶              |
| avatar_path   | TEXT    | 头像资源文件路径          |
| unread_count  | INTEGER | 未读数量              |
| edit          | BOOLEAN | 是否编辑              |
| read          | BOOLEAN | 是否阅读过             |
| type          | INTEGER | 消息类型              |
| chat_type     | INTEGER | 聊天类型              |
| direction     | TEXT    | 描述                |
| self_destruct | BOOLEAN | 是否阅后即焚            |
| ext1          | TEXT    | 扩展字段              |
| state         | INTEGER | 状态                |
| msg_id        | TEXT    | 消息ID              |
| uuid          | TEXT    | 底层消息ID            |
| explain       | TEXT    | 特殊消息 比如需要展示未接来电等  |
| at            | BOOLEAN | true:有人@我 false:无 |
| create_time   | NUMERIC | 创建时间              |
| update_time   | NUMERIC | 更新时间              |

## 

## 日志表

### 创建语句

```sqlite
CREATE TABLE IF NOT EXISTS log (
  "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
  "type" TEXT,
  "body" TEXT,
  "create_time" NUMERIC
);
```

### 字段说明

| 字段          | 类型      | 描述                 |
|-------------|---------|--------------------|
| id          | INTEGER | 数据库ID              |
| type        | TEXT    | 数据类型（eg :“errmsg”） |
| body        | TEXT    | 数据内容               |
| create_time | NUMERIC |                    |

## 
