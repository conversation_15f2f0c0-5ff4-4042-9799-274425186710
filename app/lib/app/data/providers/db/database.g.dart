// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'database.dart';

// ignore_for_file: type=lint
class Contact extends Table with TableInfo<Contact, ContactData> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  Contact(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  late final GeneratedColumn<int> id = GeneratedColumn<int>(
      'id', aliasedName, false,
      hasAutoIncrement: true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      $customConstraints: 'NOT NULL PRIMARY KEY AUTOINCREMENT');
  static const VerificationMeta _usernameMeta =
      const VerificationMeta('username');
  late final GeneratedColumn<String> username = GeneratedColumn<String>(
      'username', aliasedName, false,
      type: DriftSqlType.string,
      requiredDuringInsert: true,
      $customConstraints: 'NOT NULL UNIQUE');
  static const VerificationMeta _displaynameMeta =
      const VerificationMeta('displayname');
  late final GeneratedColumn<String> displayname = GeneratedColumn<String>(
      'displayname', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _localnameMeta =
      const VerificationMeta('localname');
  late final GeneratedColumn<String> localname = GeneratedColumn<String>(
      'localname', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _avatarPathMeta =
      const VerificationMeta('avatarPath');
  late final GeneratedColumn<String> avatarPath = GeneratedColumn<String>(
      'avatar_path', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _avatarUrlMeta =
      const VerificationMeta('avatarUrl');
  late final GeneratedColumn<String> avatarUrl = GeneratedColumn<String>(
      'avatar_url', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _chatBackgroundPathMeta =
      const VerificationMeta('chatBackgroundPath');
  late final GeneratedColumn<String> chatBackgroundPath =
      GeneratedColumn<String>('chat_background_path', aliasedName, true,
          type: DriftSqlType.string,
          requiredDuringInsert: false,
          $customConstraints: '');
  static const VerificationMeta _chatBackgroundUrlMeta =
      const VerificationMeta('chatBackgroundUrl');
  late final GeneratedColumn<String> chatBackgroundUrl =
      GeneratedColumn<String>('chat_background_url', aliasedName, true,
          type: DriftSqlType.string,
          requiredDuringInsert: false,
          $customConstraints: '');
  static const VerificationMeta _fragmentMeta =
      const VerificationMeta('fragment');
  late final GeneratedColumn<String> fragment = GeneratedColumn<String>(
      'fragment', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _editMeta = const VerificationMeta('edit');
  late final GeneratedColumn<bool> edit = GeneratedColumn<bool>(
      'edit', aliasedName, true,
      type: DriftSqlType.bool,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _readMeta = const VerificationMeta('read');
  late final GeneratedColumn<bool> read = GeneratedColumn<bool>(
      'read', aliasedName, true,
      type: DriftSqlType.bool,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _stateMeta = const VerificationMeta('state');
  late final GeneratedColumn<int> state = GeneratedColumn<int>(
      'state', aliasedName, true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _fistnameMeta =
      const VerificationMeta('fistname');
  late final GeneratedColumn<String> fistname = GeneratedColumn<String>(
      'fistname', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _lastnameMeta =
      const VerificationMeta('lastname');
  late final GeneratedColumn<String> lastname = GeneratedColumn<String>(
      'lastname', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _mobileMeta = const VerificationMeta('mobile');
  late final GeneratedColumn<String> mobile = GeneratedColumn<String>(
      'mobile', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _createTimeMeta =
      const VerificationMeta('createTime');
  late final GeneratedColumn<double> createTime = GeneratedColumn<double>(
      'create_time', aliasedName, true,
      type: DriftSqlType.double,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _updateTimeMeta =
      const VerificationMeta('updateTime');
  late final GeneratedColumn<double> updateTime = GeneratedColumn<double>(
      'update_time', aliasedName, true,
      type: DriftSqlType.double,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _isBlackMeta =
      const VerificationMeta('isBlack');
  late final GeneratedColumn<bool> isBlack = GeneratedColumn<bool>(
      'isBlack', aliasedName, true,
      type: DriftSqlType.bool,
      requiredDuringInsert: false,
      $customConstraints: 'DEFAULT FALSE',
      defaultValue: const CustomExpression('FALSE'));
  static const VerificationMeta _isTidMeta = const VerificationMeta('isTid');
  late final GeneratedColumn<bool> isTid = GeneratedColumn<bool>(
      'isTid', aliasedName, true,
      type: DriftSqlType.bool,
      requiredDuringInsert: false,
      $customConstraints: 'DEFAULT FALSE',
      defaultValue: const CustomExpression('FALSE'));
  static const VerificationMeta _typeMeta = const VerificationMeta('type');
  late final GeneratedColumn<int> type = GeneratedColumn<int>(
      'type', aliasedName, true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      $customConstraints: '');
  @override
  List<GeneratedColumn> get $columns => [
        id,
        username,
        displayname,
        localname,
        avatarPath,
        avatarUrl,
        chatBackgroundPath,
        chatBackgroundUrl,
        fragment,
        edit,
        read,
        state,
        fistname,
        lastname,
        mobile,
        createTime,
        updateTime,
        isBlack,
        isTid,
        type
      ];
  @override
  String get aliasedName => _alias ?? 'contact';
  @override
  String get actualTableName => 'contact';
  @override
  VerificationContext validateIntegrity(Insertable<ContactData> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    }
    if (data.containsKey('username')) {
      context.handle(_usernameMeta,
          username.isAcceptableOrUnknown(data['username']!, _usernameMeta));
    } else if (isInserting) {
      context.missing(_usernameMeta);
    }
    if (data.containsKey('displayname')) {
      context.handle(
          _displaynameMeta,
          displayname.isAcceptableOrUnknown(
              data['displayname']!, _displaynameMeta));
    }
    if (data.containsKey('localname')) {
      context.handle(_localnameMeta,
          localname.isAcceptableOrUnknown(data['localname']!, _localnameMeta));
    }
    if (data.containsKey('avatar_path')) {
      context.handle(
          _avatarPathMeta,
          avatarPath.isAcceptableOrUnknown(
              data['avatar_path']!, _avatarPathMeta));
    }
    if (data.containsKey('avatar_url')) {
      context.handle(_avatarUrlMeta,
          avatarUrl.isAcceptableOrUnknown(data['avatar_url']!, _avatarUrlMeta));
    }
    if (data.containsKey('chat_background_path')) {
      context.handle(
          _chatBackgroundPathMeta,
          chatBackgroundPath.isAcceptableOrUnknown(
              data['chat_background_path']!, _chatBackgroundPathMeta));
    }
    if (data.containsKey('chat_background_url')) {
      context.handle(
          _chatBackgroundUrlMeta,
          chatBackgroundUrl.isAcceptableOrUnknown(
              data['chat_background_url']!, _chatBackgroundUrlMeta));
    }
    if (data.containsKey('fragment')) {
      context.handle(_fragmentMeta,
          fragment.isAcceptableOrUnknown(data['fragment']!, _fragmentMeta));
    }
    if (data.containsKey('edit')) {
      context.handle(
          _editMeta, edit.isAcceptableOrUnknown(data['edit']!, _editMeta));
    }
    if (data.containsKey('read')) {
      context.handle(
          _readMeta, read.isAcceptableOrUnknown(data['read']!, _readMeta));
    }
    if (data.containsKey('state')) {
      context.handle(
          _stateMeta, state.isAcceptableOrUnknown(data['state']!, _stateMeta));
    }
    if (data.containsKey('fistname')) {
      context.handle(_fistnameMeta,
          fistname.isAcceptableOrUnknown(data['fistname']!, _fistnameMeta));
    }
    if (data.containsKey('lastname')) {
      context.handle(_lastnameMeta,
          lastname.isAcceptableOrUnknown(data['lastname']!, _lastnameMeta));
    }
    if (data.containsKey('mobile')) {
      context.handle(_mobileMeta,
          mobile.isAcceptableOrUnknown(data['mobile']!, _mobileMeta));
    }
    if (data.containsKey('create_time')) {
      context.handle(
          _createTimeMeta,
          createTime.isAcceptableOrUnknown(
              data['create_time']!, _createTimeMeta));
    }
    if (data.containsKey('update_time')) {
      context.handle(
          _updateTimeMeta,
          updateTime.isAcceptableOrUnknown(
              data['update_time']!, _updateTimeMeta));
    }
    if (data.containsKey('isBlack')) {
      context.handle(_isBlackMeta,
          isBlack.isAcceptableOrUnknown(data['isBlack']!, _isBlackMeta));
    }
    if (data.containsKey('isTid')) {
      context.handle(
          _isTidMeta, isTid.isAcceptableOrUnknown(data['isTid']!, _isTidMeta));
    }
    if (data.containsKey('type')) {
      context.handle(
          _typeMeta, type.isAcceptableOrUnknown(data['type']!, _typeMeta));
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  ContactData map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return ContactData(
      id: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}id'])!,
      username: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}username'])!,
      displayname: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}displayname']),
      localname: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}localname']),
      avatarPath: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}avatar_path']),
      avatarUrl: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}avatar_url']),
      chatBackgroundPath: attachedDatabase.typeMapping.read(
          DriftSqlType.string, data['${effectivePrefix}chat_background_path']),
      chatBackgroundUrl: attachedDatabase.typeMapping.read(
          DriftSqlType.string, data['${effectivePrefix}chat_background_url']),
      fragment: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}fragment']),
      edit: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}edit']),
      read: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}read']),
      state: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}state']),
      fistname: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}fistname']),
      lastname: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}lastname']),
      mobile: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}mobile']),
      createTime: attachedDatabase.typeMapping
          .read(DriftSqlType.double, data['${effectivePrefix}create_time']),
      updateTime: attachedDatabase.typeMapping
          .read(DriftSqlType.double, data['${effectivePrefix}update_time']),
      isBlack: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}isBlack']),
      isTid: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}isTid']),
      type: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}type']),
    );
  }

  @override
  Contact createAlias(String alias) {
    return Contact(attachedDatabase, alias);
  }

  @override
  bool get dontWriteConstraints => true;
}

class ContactData extends DataClass implements Insertable<ContactData> {
  final int id;
  final String username;
  final String? displayname;
  final String? localname;
  final String? avatarPath;
  final String? avatarUrl;
  final String? chatBackgroundPath;
  final String? chatBackgroundUrl;
  final String? fragment;
  final bool? edit;
  final bool? read;
  final int? state;
  final String? fistname;
  final String? lastname;
  final String? mobile;
  final double? createTime;
  final double? updateTime;
  final bool? isBlack;
  final bool? isTid;
  final int? type;
  const ContactData(
      {required this.id,
      required this.username,
      this.displayname,
      this.localname,
      this.avatarPath,
      this.avatarUrl,
      this.chatBackgroundPath,
      this.chatBackgroundUrl,
      this.fragment,
      this.edit,
      this.read,
      this.state,
      this.fistname,
      this.lastname,
      this.mobile,
      this.createTime,
      this.updateTime,
      this.isBlack,
      this.isTid,
      this.type});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<int>(id);
    map['username'] = Variable<String>(username);
    if (!nullToAbsent || displayname != null) {
      map['displayname'] = Variable<String>(displayname);
    }
    if (!nullToAbsent || localname != null) {
      map['localname'] = Variable<String>(localname);
    }
    if (!nullToAbsent || avatarPath != null) {
      map['avatar_path'] = Variable<String>(avatarPath);
    }
    if (!nullToAbsent || avatarUrl != null) {
      map['avatar_url'] = Variable<String>(avatarUrl);
    }
    if (!nullToAbsent || chatBackgroundPath != null) {
      map['chat_background_path'] = Variable<String>(chatBackgroundPath);
    }
    if (!nullToAbsent || chatBackgroundUrl != null) {
      map['chat_background_url'] = Variable<String>(chatBackgroundUrl);
    }
    if (!nullToAbsent || fragment != null) {
      map['fragment'] = Variable<String>(fragment);
    }
    if (!nullToAbsent || edit != null) {
      map['edit'] = Variable<bool>(edit);
    }
    if (!nullToAbsent || read != null) {
      map['read'] = Variable<bool>(read);
    }
    if (!nullToAbsent || state != null) {
      map['state'] = Variable<int>(state);
    }
    if (!nullToAbsent || fistname != null) {
      map['fistname'] = Variable<String>(fistname);
    }
    if (!nullToAbsent || lastname != null) {
      map['lastname'] = Variable<String>(lastname);
    }
    if (!nullToAbsent || mobile != null) {
      map['mobile'] = Variable<String>(mobile);
    }
    if (!nullToAbsent || createTime != null) {
      map['create_time'] = Variable<double>(createTime);
    }
    if (!nullToAbsent || updateTime != null) {
      map['update_time'] = Variable<double>(updateTime);
    }
    if (!nullToAbsent || isBlack != null) {
      map['isBlack'] = Variable<bool>(isBlack);
    }
    if (!nullToAbsent || isTid != null) {
      map['isTid'] = Variable<bool>(isTid);
    }
    if (!nullToAbsent || type != null) {
      map['type'] = Variable<int>(type);
    }
    return map;
  }

  ContactCompanion toCompanion(bool nullToAbsent) {
    return ContactCompanion(
      id: Value(id),
      username: Value(username),
      displayname: displayname == null && nullToAbsent
          ? const Value.absent()
          : Value(displayname),
      localname: localname == null && nullToAbsent
          ? const Value.absent()
          : Value(localname),
      avatarPath: avatarPath == null && nullToAbsent
          ? const Value.absent()
          : Value(avatarPath),
      avatarUrl: avatarUrl == null && nullToAbsent
          ? const Value.absent()
          : Value(avatarUrl),
      chatBackgroundPath: chatBackgroundPath == null && nullToAbsent
          ? const Value.absent()
          : Value(chatBackgroundPath),
      chatBackgroundUrl: chatBackgroundUrl == null && nullToAbsent
          ? const Value.absent()
          : Value(chatBackgroundUrl),
      fragment: fragment == null && nullToAbsent
          ? const Value.absent()
          : Value(fragment),
      edit: edit == null && nullToAbsent ? const Value.absent() : Value(edit),
      read: read == null && nullToAbsent ? const Value.absent() : Value(read),
      state:
          state == null && nullToAbsent ? const Value.absent() : Value(state),
      fistname: fistname == null && nullToAbsent
          ? const Value.absent()
          : Value(fistname),
      lastname: lastname == null && nullToAbsent
          ? const Value.absent()
          : Value(lastname),
      mobile:
          mobile == null && nullToAbsent ? const Value.absent() : Value(mobile),
      createTime: createTime == null && nullToAbsent
          ? const Value.absent()
          : Value(createTime),
      updateTime: updateTime == null && nullToAbsent
          ? const Value.absent()
          : Value(updateTime),
      isBlack: isBlack == null && nullToAbsent
          ? const Value.absent()
          : Value(isBlack),
      isTid:
          isTid == null && nullToAbsent ? const Value.absent() : Value(isTid),
      type: type == null && nullToAbsent ? const Value.absent() : Value(type),
    );
  }

  factory ContactData.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return ContactData(
      id: serializer.fromJson<int>(json['id']),
      username: serializer.fromJson<String>(json['username']),
      displayname: serializer.fromJson<String?>(json['displayname']),
      localname: serializer.fromJson<String?>(json['localname']),
      avatarPath: serializer.fromJson<String?>(json['avatar_path']),
      avatarUrl: serializer.fromJson<String?>(json['avatar_url']),
      chatBackgroundPath:
          serializer.fromJson<String?>(json['chat_background_path']),
      chatBackgroundUrl:
          serializer.fromJson<String?>(json['chat_background_url']),
      fragment: serializer.fromJson<String?>(json['fragment']),
      edit: serializer.fromJson<bool?>(json['edit']),
      read: serializer.fromJson<bool?>(json['read']),
      state: serializer.fromJson<int?>(json['state']),
      fistname: serializer.fromJson<String?>(json['fistname']),
      lastname: serializer.fromJson<String?>(json['lastname']),
      mobile: serializer.fromJson<String?>(json['mobile']),
      createTime: serializer.fromJson<double?>(json['create_time']),
      updateTime: serializer.fromJson<double?>(json['update_time']),
      isBlack: serializer.fromJson<bool?>(json['isBlack']),
      isTid: serializer.fromJson<bool?>(json['isTid']),
      type: serializer.fromJson<int?>(json['type']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<int>(id),
      'username': serializer.toJson<String>(username),
      'displayname': serializer.toJson<String?>(displayname),
      'localname': serializer.toJson<String?>(localname),
      'avatar_path': serializer.toJson<String?>(avatarPath),
      'avatar_url': serializer.toJson<String?>(avatarUrl),
      'chat_background_path': serializer.toJson<String?>(chatBackgroundPath),
      'chat_background_url': serializer.toJson<String?>(chatBackgroundUrl),
      'fragment': serializer.toJson<String?>(fragment),
      'edit': serializer.toJson<bool?>(edit),
      'read': serializer.toJson<bool?>(read),
      'state': serializer.toJson<int?>(state),
      'fistname': serializer.toJson<String?>(fistname),
      'lastname': serializer.toJson<String?>(lastname),
      'mobile': serializer.toJson<String?>(mobile),
      'create_time': serializer.toJson<double?>(createTime),
      'update_time': serializer.toJson<double?>(updateTime),
      'isBlack': serializer.toJson<bool?>(isBlack),
      'isTid': serializer.toJson<bool?>(isTid),
      'type': serializer.toJson<int?>(type),
    };
  }

  ContactData copyWith(
          {int? id,
          String? username,
          Value<String?> displayname = const Value.absent(),
          Value<String?> localname = const Value.absent(),
          Value<String?> avatarPath = const Value.absent(),
          Value<String?> avatarUrl = const Value.absent(),
          Value<String?> chatBackgroundPath = const Value.absent(),
          Value<String?> chatBackgroundUrl = const Value.absent(),
          Value<String?> fragment = const Value.absent(),
          Value<bool?> edit = const Value.absent(),
          Value<bool?> read = const Value.absent(),
          Value<int?> state = const Value.absent(),
          Value<String?> fistname = const Value.absent(),
          Value<String?> lastname = const Value.absent(),
          Value<String?> mobile = const Value.absent(),
          Value<double?> createTime = const Value.absent(),
          Value<double?> updateTime = const Value.absent(),
          Value<bool?> isBlack = const Value.absent(),
          Value<bool?> isTid = const Value.absent(),
          Value<int?> type = const Value.absent()}) =>
      ContactData(
        id: id ?? this.id,
        username: username ?? this.username,
        displayname: displayname.present ? displayname.value : this.displayname,
        localname: localname.present ? localname.value : this.localname,
        avatarPath: avatarPath.present ? avatarPath.value : this.avatarPath,
        avatarUrl: avatarUrl.present ? avatarUrl.value : this.avatarUrl,
        chatBackgroundPath: chatBackgroundPath.present
            ? chatBackgroundPath.value
            : this.chatBackgroundPath,
        chatBackgroundUrl: chatBackgroundUrl.present
            ? chatBackgroundUrl.value
            : this.chatBackgroundUrl,
        fragment: fragment.present ? fragment.value : this.fragment,
        edit: edit.present ? edit.value : this.edit,
        read: read.present ? read.value : this.read,
        state: state.present ? state.value : this.state,
        fistname: fistname.present ? fistname.value : this.fistname,
        lastname: lastname.present ? lastname.value : this.lastname,
        mobile: mobile.present ? mobile.value : this.mobile,
        createTime: createTime.present ? createTime.value : this.createTime,
        updateTime: updateTime.present ? updateTime.value : this.updateTime,
        isBlack: isBlack.present ? isBlack.value : this.isBlack,
        isTid: isTid.present ? isTid.value : this.isTid,
        type: type.present ? type.value : this.type,
      );
  @override
  String toString() {
    return (StringBuffer('ContactData(')
          ..write('id: $id, ')
          ..write('username: $username, ')
          ..write('displayname: $displayname, ')
          ..write('localname: $localname, ')
          ..write('avatarPath: $avatarPath, ')
          ..write('avatarUrl: $avatarUrl, ')
          ..write('chatBackgroundPath: $chatBackgroundPath, ')
          ..write('chatBackgroundUrl: $chatBackgroundUrl, ')
          ..write('fragment: $fragment, ')
          ..write('edit: $edit, ')
          ..write('read: $read, ')
          ..write('state: $state, ')
          ..write('fistname: $fistname, ')
          ..write('lastname: $lastname, ')
          ..write('mobile: $mobile, ')
          ..write('createTime: $createTime, ')
          ..write('updateTime: $updateTime, ')
          ..write('isBlack: $isBlack, ')
          ..write('isTid: $isTid, ')
          ..write('type: $type')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(
      id,
      username,
      displayname,
      localname,
      avatarPath,
      avatarUrl,
      chatBackgroundPath,
      chatBackgroundUrl,
      fragment,
      edit,
      read,
      state,
      fistname,
      lastname,
      mobile,
      createTime,
      updateTime,
      isBlack,
      isTid,
      type);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is ContactData &&
          other.id == this.id &&
          other.username == this.username &&
          other.displayname == this.displayname &&
          other.localname == this.localname &&
          other.avatarPath == this.avatarPath &&
          other.avatarUrl == this.avatarUrl &&
          other.chatBackgroundPath == this.chatBackgroundPath &&
          other.chatBackgroundUrl == this.chatBackgroundUrl &&
          other.fragment == this.fragment &&
          other.edit == this.edit &&
          other.read == this.read &&
          other.state == this.state &&
          other.fistname == this.fistname &&
          other.lastname == this.lastname &&
          other.mobile == this.mobile &&
          other.createTime == this.createTime &&
          other.updateTime == this.updateTime &&
          other.isBlack == this.isBlack &&
          other.isTid == this.isTid &&
          other.type == this.type);
}

class ContactCompanion extends UpdateCompanion<ContactData> {
  final Value<int> id;
  final Value<String> username;
  final Value<String?> displayname;
  final Value<String?> localname;
  final Value<String?> avatarPath;
  final Value<String?> avatarUrl;
  final Value<String?> chatBackgroundPath;
  final Value<String?> chatBackgroundUrl;
  final Value<String?> fragment;
  final Value<bool?> edit;
  final Value<bool?> read;
  final Value<int?> state;
  final Value<String?> fistname;
  final Value<String?> lastname;
  final Value<String?> mobile;
  final Value<double?> createTime;
  final Value<double?> updateTime;
  final Value<bool?> isBlack;
  final Value<bool?> isTid;
  final Value<int?> type;
  const ContactCompanion({
    this.id = const Value.absent(),
    this.username = const Value.absent(),
    this.displayname = const Value.absent(),
    this.localname = const Value.absent(),
    this.avatarPath = const Value.absent(),
    this.avatarUrl = const Value.absent(),
    this.chatBackgroundPath = const Value.absent(),
    this.chatBackgroundUrl = const Value.absent(),
    this.fragment = const Value.absent(),
    this.edit = const Value.absent(),
    this.read = const Value.absent(),
    this.state = const Value.absent(),
    this.fistname = const Value.absent(),
    this.lastname = const Value.absent(),
    this.mobile = const Value.absent(),
    this.createTime = const Value.absent(),
    this.updateTime = const Value.absent(),
    this.isBlack = const Value.absent(),
    this.isTid = const Value.absent(),
    this.type = const Value.absent(),
  });
  ContactCompanion.insert({
    this.id = const Value.absent(),
    required String username,
    this.displayname = const Value.absent(),
    this.localname = const Value.absent(),
    this.avatarPath = const Value.absent(),
    this.avatarUrl = const Value.absent(),
    this.chatBackgroundPath = const Value.absent(),
    this.chatBackgroundUrl = const Value.absent(),
    this.fragment = const Value.absent(),
    this.edit = const Value.absent(),
    this.read = const Value.absent(),
    this.state = const Value.absent(),
    this.fistname = const Value.absent(),
    this.lastname = const Value.absent(),
    this.mobile = const Value.absent(),
    this.createTime = const Value.absent(),
    this.updateTime = const Value.absent(),
    this.isBlack = const Value.absent(),
    this.isTid = const Value.absent(),
    this.type = const Value.absent(),
  }) : username = Value(username);
  static Insertable<ContactData> custom({
    Expression<int>? id,
    Expression<String>? username,
    Expression<String>? displayname,
    Expression<String>? localname,
    Expression<String>? avatarPath,
    Expression<String>? avatarUrl,
    Expression<String>? chatBackgroundPath,
    Expression<String>? chatBackgroundUrl,
    Expression<String>? fragment,
    Expression<bool>? edit,
    Expression<bool>? read,
    Expression<int>? state,
    Expression<String>? fistname,
    Expression<String>? lastname,
    Expression<String>? mobile,
    Expression<double>? createTime,
    Expression<double>? updateTime,
    Expression<bool>? isBlack,
    Expression<bool>? isTid,
    Expression<int>? type,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (username != null) 'username': username,
      if (displayname != null) 'displayname': displayname,
      if (localname != null) 'localname': localname,
      if (avatarPath != null) 'avatar_path': avatarPath,
      if (avatarUrl != null) 'avatar_url': avatarUrl,
      if (chatBackgroundPath != null)
        'chat_background_path': chatBackgroundPath,
      if (chatBackgroundUrl != null) 'chat_background_url': chatBackgroundUrl,
      if (fragment != null) 'fragment': fragment,
      if (edit != null) 'edit': edit,
      if (read != null) 'read': read,
      if (state != null) 'state': state,
      if (fistname != null) 'fistname': fistname,
      if (lastname != null) 'lastname': lastname,
      if (mobile != null) 'mobile': mobile,
      if (createTime != null) 'create_time': createTime,
      if (updateTime != null) 'update_time': updateTime,
      if (isBlack != null) 'isBlack': isBlack,
      if (isTid != null) 'isTid': isTid,
      if (type != null) 'type': type,
    });
  }

  ContactCompanion copyWith(
      {Value<int>? id,
      Value<String>? username,
      Value<String?>? displayname,
      Value<String?>? localname,
      Value<String?>? avatarPath,
      Value<String?>? avatarUrl,
      Value<String?>? chatBackgroundPath,
      Value<String?>? chatBackgroundUrl,
      Value<String?>? fragment,
      Value<bool?>? edit,
      Value<bool?>? read,
      Value<int?>? state,
      Value<String?>? fistname,
      Value<String?>? lastname,
      Value<String?>? mobile,
      Value<double?>? createTime,
      Value<double?>? updateTime,
      Value<bool?>? isBlack,
      Value<bool?>? isTid,
      Value<int?>? type}) {
    return ContactCompanion(
      id: id ?? this.id,
      username: username ?? this.username,
      displayname: displayname ?? this.displayname,
      localname: localname ?? this.localname,
      avatarPath: avatarPath ?? this.avatarPath,
      avatarUrl: avatarUrl ?? this.avatarUrl,
      chatBackgroundPath: chatBackgroundPath ?? this.chatBackgroundPath,
      chatBackgroundUrl: chatBackgroundUrl ?? this.chatBackgroundUrl,
      fragment: fragment ?? this.fragment,
      edit: edit ?? this.edit,
      read: read ?? this.read,
      state: state ?? this.state,
      fistname: fistname ?? this.fistname,
      lastname: lastname ?? this.lastname,
      mobile: mobile ?? this.mobile,
      createTime: createTime ?? this.createTime,
      updateTime: updateTime ?? this.updateTime,
      isBlack: isBlack ?? this.isBlack,
      isTid: isTid ?? this.isTid,
      type: type ?? this.type,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<int>(id.value);
    }
    if (username.present) {
      map['username'] = Variable<String>(username.value);
    }
    if (displayname.present) {
      map['displayname'] = Variable<String>(displayname.value);
    }
    if (localname.present) {
      map['localname'] = Variable<String>(localname.value);
    }
    if (avatarPath.present) {
      map['avatar_path'] = Variable<String>(avatarPath.value);
    }
    if (avatarUrl.present) {
      map['avatar_url'] = Variable<String>(avatarUrl.value);
    }
    if (chatBackgroundPath.present) {
      map['chat_background_path'] = Variable<String>(chatBackgroundPath.value);
    }
    if (chatBackgroundUrl.present) {
      map['chat_background_url'] = Variable<String>(chatBackgroundUrl.value);
    }
    if (fragment.present) {
      map['fragment'] = Variable<String>(fragment.value);
    }
    if (edit.present) {
      map['edit'] = Variable<bool>(edit.value);
    }
    if (read.present) {
      map['read'] = Variable<bool>(read.value);
    }
    if (state.present) {
      map['state'] = Variable<int>(state.value);
    }
    if (fistname.present) {
      map['fistname'] = Variable<String>(fistname.value);
    }
    if (lastname.present) {
      map['lastname'] = Variable<String>(lastname.value);
    }
    if (mobile.present) {
      map['mobile'] = Variable<String>(mobile.value);
    }
    if (createTime.present) {
      map['create_time'] = Variable<double>(createTime.value);
    }
    if (updateTime.present) {
      map['update_time'] = Variable<double>(updateTime.value);
    }
    if (isBlack.present) {
      map['isBlack'] = Variable<bool>(isBlack.value);
    }
    if (isTid.present) {
      map['isTid'] = Variable<bool>(isTid.value);
    }
    if (type.present) {
      map['type'] = Variable<int>(type.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('ContactCompanion(')
          ..write('id: $id, ')
          ..write('username: $username, ')
          ..write('displayname: $displayname, ')
          ..write('localname: $localname, ')
          ..write('avatarPath: $avatarPath, ')
          ..write('avatarUrl: $avatarUrl, ')
          ..write('chatBackgroundPath: $chatBackgroundPath, ')
          ..write('chatBackgroundUrl: $chatBackgroundUrl, ')
          ..write('fragment: $fragment, ')
          ..write('edit: $edit, ')
          ..write('read: $read, ')
          ..write('state: $state, ')
          ..write('fistname: $fistname, ')
          ..write('lastname: $lastname, ')
          ..write('mobile: $mobile, ')
          ..write('createTime: $createTime, ')
          ..write('updateTime: $updateTime, ')
          ..write('isBlack: $isBlack, ')
          ..write('isTid: $isTid, ')
          ..write('type: $type')
          ..write(')'))
        .toString();
  }
}

class ContactWallet extends Table
    with TableInfo<ContactWallet, ContactWalletData> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  ContactWallet(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  late final GeneratedColumn<int> id = GeneratedColumn<int>(
      'id', aliasedName, false,
      hasAutoIncrement: true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      $customConstraints: 'NOT NULL PRIMARY KEY AUTOINCREMENT');
  static const VerificationMeta _addressMeta =
      const VerificationMeta('address');
  late final GeneratedColumn<String> address = GeneratedColumn<String>(
      'address', aliasedName, false,
      type: DriftSqlType.string,
      requiredDuringInsert: true,
      $customConstraints: 'NOT NULL UNIQUE');
  static const VerificationMeta _userNameMeta =
      const VerificationMeta('userName');
  late final GeneratedColumn<String> userName = GeneratedColumn<String>(
      'user_name', aliasedName, false,
      type: DriftSqlType.string,
      requiredDuringInsert: true,
      $customConstraints: 'NOT NULL');
  static const VerificationMeta _chainTypeMeta =
      const VerificationMeta('chainType');
  late final GeneratedColumn<int> chainType = GeneratedColumn<int>(
      'chain_type', aliasedName, true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _chainIdMeta =
      const VerificationMeta('chainId');
  late final GeneratedColumn<int> chainId = GeneratedColumn<int>(
      'chain_id', aliasedName, true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _createTimeMeta =
      const VerificationMeta('createTime');
  late final GeneratedColumn<double> createTime = GeneratedColumn<double>(
      'create_time', aliasedName, true,
      type: DriftSqlType.double,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _updateTimeMeta =
      const VerificationMeta('updateTime');
  late final GeneratedColumn<double> updateTime = GeneratedColumn<double>(
      'update_time', aliasedName, true,
      type: DriftSqlType.double,
      requiredDuringInsert: false,
      $customConstraints: '');
  @override
  List<GeneratedColumn> get $columns =>
      [id, address, userName, chainType, chainId, createTime, updateTime];
  @override
  String get aliasedName => _alias ?? 'contact_wallet';
  @override
  String get actualTableName => 'contact_wallet';
  @override
  VerificationContext validateIntegrity(Insertable<ContactWalletData> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    }
    if (data.containsKey('address')) {
      context.handle(_addressMeta,
          address.isAcceptableOrUnknown(data['address']!, _addressMeta));
    } else if (isInserting) {
      context.missing(_addressMeta);
    }
    if (data.containsKey('user_name')) {
      context.handle(_userNameMeta,
          userName.isAcceptableOrUnknown(data['user_name']!, _userNameMeta));
    } else if (isInserting) {
      context.missing(_userNameMeta);
    }
    if (data.containsKey('chain_type')) {
      context.handle(_chainTypeMeta,
          chainType.isAcceptableOrUnknown(data['chain_type']!, _chainTypeMeta));
    }
    if (data.containsKey('chain_id')) {
      context.handle(_chainIdMeta,
          chainId.isAcceptableOrUnknown(data['chain_id']!, _chainIdMeta));
    }
    if (data.containsKey('create_time')) {
      context.handle(
          _createTimeMeta,
          createTime.isAcceptableOrUnknown(
              data['create_time']!, _createTimeMeta));
    }
    if (data.containsKey('update_time')) {
      context.handle(
          _updateTimeMeta,
          updateTime.isAcceptableOrUnknown(
              data['update_time']!, _updateTimeMeta));
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  ContactWalletData map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return ContactWalletData(
      id: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}id'])!,
      address: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}address'])!,
      userName: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}user_name'])!,
      chainType: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}chain_type']),
      chainId: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}chain_id']),
      createTime: attachedDatabase.typeMapping
          .read(DriftSqlType.double, data['${effectivePrefix}create_time']),
      updateTime: attachedDatabase.typeMapping
          .read(DriftSqlType.double, data['${effectivePrefix}update_time']),
    );
  }

  @override
  ContactWallet createAlias(String alias) {
    return ContactWallet(attachedDatabase, alias);
  }

  @override
  bool get dontWriteConstraints => true;
}

class ContactWalletData extends DataClass
    implements Insertable<ContactWalletData> {
  final int id;
  final String address;
  final String userName;
  final int? chainType;
  final int? chainId;
  final double? createTime;
  final double? updateTime;
  const ContactWalletData(
      {required this.id,
      required this.address,
      required this.userName,
      this.chainType,
      this.chainId,
      this.createTime,
      this.updateTime});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<int>(id);
    map['address'] = Variable<String>(address);
    map['user_name'] = Variable<String>(userName);
    if (!nullToAbsent || chainType != null) {
      map['chain_type'] = Variable<int>(chainType);
    }
    if (!nullToAbsent || chainId != null) {
      map['chain_id'] = Variable<int>(chainId);
    }
    if (!nullToAbsent || createTime != null) {
      map['create_time'] = Variable<double>(createTime);
    }
    if (!nullToAbsent || updateTime != null) {
      map['update_time'] = Variable<double>(updateTime);
    }
    return map;
  }

  ContactWalletCompanion toCompanion(bool nullToAbsent) {
    return ContactWalletCompanion(
      id: Value(id),
      address: Value(address),
      userName: Value(userName),
      chainType: chainType == null && nullToAbsent
          ? const Value.absent()
          : Value(chainType),
      chainId: chainId == null && nullToAbsent
          ? const Value.absent()
          : Value(chainId),
      createTime: createTime == null && nullToAbsent
          ? const Value.absent()
          : Value(createTime),
      updateTime: updateTime == null && nullToAbsent
          ? const Value.absent()
          : Value(updateTime),
    );
  }

  factory ContactWalletData.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return ContactWalletData(
      id: serializer.fromJson<int>(json['id']),
      address: serializer.fromJson<String>(json['address']),
      userName: serializer.fromJson<String>(json['user_name']),
      chainType: serializer.fromJson<int?>(json['chain_type']),
      chainId: serializer.fromJson<int?>(json['chain_id']),
      createTime: serializer.fromJson<double?>(json['create_time']),
      updateTime: serializer.fromJson<double?>(json['update_time']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<int>(id),
      'address': serializer.toJson<String>(address),
      'user_name': serializer.toJson<String>(userName),
      'chain_type': serializer.toJson<int?>(chainType),
      'chain_id': serializer.toJson<int?>(chainId),
      'create_time': serializer.toJson<double?>(createTime),
      'update_time': serializer.toJson<double?>(updateTime),
    };
  }

  ContactWalletData copyWith(
          {int? id,
          String? address,
          String? userName,
          Value<int?> chainType = const Value.absent(),
          Value<int?> chainId = const Value.absent(),
          Value<double?> createTime = const Value.absent(),
          Value<double?> updateTime = const Value.absent()}) =>
      ContactWalletData(
        id: id ?? this.id,
        address: address ?? this.address,
        userName: userName ?? this.userName,
        chainType: chainType.present ? chainType.value : this.chainType,
        chainId: chainId.present ? chainId.value : this.chainId,
        createTime: createTime.present ? createTime.value : this.createTime,
        updateTime: updateTime.present ? updateTime.value : this.updateTime,
      );
  @override
  String toString() {
    return (StringBuffer('ContactWalletData(')
          ..write('id: $id, ')
          ..write('address: $address, ')
          ..write('userName: $userName, ')
          ..write('chainType: $chainType, ')
          ..write('chainId: $chainId, ')
          ..write('createTime: $createTime, ')
          ..write('updateTime: $updateTime')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(
      id, address, userName, chainType, chainId, createTime, updateTime);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is ContactWalletData &&
          other.id == this.id &&
          other.address == this.address &&
          other.userName == this.userName &&
          other.chainType == this.chainType &&
          other.chainId == this.chainId &&
          other.createTime == this.createTime &&
          other.updateTime == this.updateTime);
}

class ContactWalletCompanion extends UpdateCompanion<ContactWalletData> {
  final Value<int> id;
  final Value<String> address;
  final Value<String> userName;
  final Value<int?> chainType;
  final Value<int?> chainId;
  final Value<double?> createTime;
  final Value<double?> updateTime;
  const ContactWalletCompanion({
    this.id = const Value.absent(),
    this.address = const Value.absent(),
    this.userName = const Value.absent(),
    this.chainType = const Value.absent(),
    this.chainId = const Value.absent(),
    this.createTime = const Value.absent(),
    this.updateTime = const Value.absent(),
  });
  ContactWalletCompanion.insert({
    this.id = const Value.absent(),
    required String address,
    required String userName,
    this.chainType = const Value.absent(),
    this.chainId = const Value.absent(),
    this.createTime = const Value.absent(),
    this.updateTime = const Value.absent(),
  })  : address = Value(address),
        userName = Value(userName);
  static Insertable<ContactWalletData> custom({
    Expression<int>? id,
    Expression<String>? address,
    Expression<String>? userName,
    Expression<int>? chainType,
    Expression<int>? chainId,
    Expression<double>? createTime,
    Expression<double>? updateTime,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (address != null) 'address': address,
      if (userName != null) 'user_name': userName,
      if (chainType != null) 'chain_type': chainType,
      if (chainId != null) 'chain_id': chainId,
      if (createTime != null) 'create_time': createTime,
      if (updateTime != null) 'update_time': updateTime,
    });
  }

  ContactWalletCompanion copyWith(
      {Value<int>? id,
      Value<String>? address,
      Value<String>? userName,
      Value<int?>? chainType,
      Value<int?>? chainId,
      Value<double?>? createTime,
      Value<double?>? updateTime}) {
    return ContactWalletCompanion(
      id: id ?? this.id,
      address: address ?? this.address,
      userName: userName ?? this.userName,
      chainType: chainType ?? this.chainType,
      chainId: chainId ?? this.chainId,
      createTime: createTime ?? this.createTime,
      updateTime: updateTime ?? this.updateTime,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<int>(id.value);
    }
    if (address.present) {
      map['address'] = Variable<String>(address.value);
    }
    if (userName.present) {
      map['user_name'] = Variable<String>(userName.value);
    }
    if (chainType.present) {
      map['chain_type'] = Variable<int>(chainType.value);
    }
    if (chainId.present) {
      map['chain_id'] = Variable<int>(chainId.value);
    }
    if (createTime.present) {
      map['create_time'] = Variable<double>(createTime.value);
    }
    if (updateTime.present) {
      map['update_time'] = Variable<double>(updateTime.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('ContactWalletCompanion(')
          ..write('id: $id, ')
          ..write('address: $address, ')
          ..write('userName: $userName, ')
          ..write('chainType: $chainType, ')
          ..write('chainId: $chainId, ')
          ..write('createTime: $createTime, ')
          ..write('updateTime: $updateTime')
          ..write(')'))
        .toString();
  }
}

class GroupInfo extends Table with TableInfo<GroupInfo, GroupInfoData> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  GroupInfo(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  late final GeneratedColumn<int> id = GeneratedColumn<int>(
      'id', aliasedName, false,
      hasAutoIncrement: true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      $customConstraints: 'NOT NULL PRIMARY KEY AUTOINCREMENT');
  static const VerificationMeta _groupIdMeta =
      const VerificationMeta('groupId');
  late final GeneratedColumn<String> groupId = GeneratedColumn<String>(
      'group_id', aliasedName, false,
      type: DriftSqlType.string,
      requiredDuringInsert: true,
      $customConstraints: 'NOT NULL UNIQUE');
  static const VerificationMeta _titleMeta = const VerificationMeta('title');
  late final GeneratedColumn<String> title = GeneratedColumn<String>(
      'title', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _permissionMeta =
      const VerificationMeta('permission');
  late final GeneratedColumn<int> permission = GeneratedColumn<int>(
      'permission', aliasedName, true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _ownerMeta = const VerificationMeta('owner');
  late final GeneratedColumn<String> owner = GeneratedColumn<String>(
      'owner', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _avatarUrlMeta =
      const VerificationMeta('avatarUrl');
  late final GeneratedColumn<String> avatarUrl = GeneratedColumn<String>(
      'avatar_url', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _avatarPathMeta =
      const VerificationMeta('avatarPath');
  late final GeneratedColumn<String> avatarPath = GeneratedColumn<String>(
      'avatar_path', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _avatarFragmentMeta =
      const VerificationMeta('avatarFragment');
  late final GeneratedColumn<String> avatarFragment = GeneratedColumn<String>(
      'avatar_fragment', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _propertyUrlMeta =
      const VerificationMeta('propertyUrl');
  late final GeneratedColumn<String> propertyUrl = GeneratedColumn<String>(
      'property_url', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _propertyPathMeta =
      const VerificationMeta('propertyPath');
  late final GeneratedColumn<String> propertyPath = GeneratedColumn<String>(
      'property_path', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _propertyFragmentMeta =
      const VerificationMeta('propertyFragment');
  late final GeneratedColumn<String> propertyFragment = GeneratedColumn<String>(
      'property_fragment', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _describeMeta =
      const VerificationMeta('describe');
  late final GeneratedColumn<String> describe = GeneratedColumn<String>(
      'describe', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _backgroundPathMeta =
      const VerificationMeta('backgroundPath');
  late final GeneratedColumn<String> backgroundPath = GeneratedColumn<String>(
      'background_path', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _backgroundUrlMeta =
      const VerificationMeta('backgroundUrl');
  late final GeneratedColumn<String> backgroundUrl = GeneratedColumn<String>(
      'background_url', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _invalidMeta =
      const VerificationMeta('invalid');
  late final GeneratedColumn<bool> invalid = GeneratedColumn<bool>(
      'invalid', aliasedName, true,
      type: DriftSqlType.bool,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _createTimeMeta =
      const VerificationMeta('createTime');
  late final GeneratedColumn<double> createTime = GeneratedColumn<double>(
      'create_time', aliasedName, true,
      type: DriftSqlType.double,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _updateTimeMeta =
      const VerificationMeta('updateTime');
  late final GeneratedColumn<double> updateTime = GeneratedColumn<double>(
      'update_time', aliasedName, true,
      type: DriftSqlType.double,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _memberCountMeta =
      const VerificationMeta('memberCount');
  late final GeneratedColumn<int> memberCount = GeneratedColumn<int>(
      'member_count', aliasedName, true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _announcementMeta =
      const VerificationMeta('announcement');
  late final GeneratedColumn<String> announcement = GeneratedColumn<String>(
      'announcement', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  @override
  List<GeneratedColumn> get $columns => [
        id,
        groupId,
        title,
        permission,
        owner,
        avatarUrl,
        avatarPath,
        avatarFragment,
        propertyUrl,
        propertyPath,
        propertyFragment,
        describe,
        backgroundPath,
        backgroundUrl,
        invalid,
        createTime,
        updateTime,
        memberCount,
        announcement
      ];
  @override
  String get aliasedName => _alias ?? 'group_info';
  @override
  String get actualTableName => 'group_info';
  @override
  VerificationContext validateIntegrity(Insertable<GroupInfoData> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    }
    if (data.containsKey('group_id')) {
      context.handle(_groupIdMeta,
          groupId.isAcceptableOrUnknown(data['group_id']!, _groupIdMeta));
    } else if (isInserting) {
      context.missing(_groupIdMeta);
    }
    if (data.containsKey('title')) {
      context.handle(
          _titleMeta, title.isAcceptableOrUnknown(data['title']!, _titleMeta));
    }
    if (data.containsKey('permission')) {
      context.handle(
          _permissionMeta,
          permission.isAcceptableOrUnknown(
              data['permission']!, _permissionMeta));
    }
    if (data.containsKey('owner')) {
      context.handle(
          _ownerMeta, owner.isAcceptableOrUnknown(data['owner']!, _ownerMeta));
    }
    if (data.containsKey('avatar_url')) {
      context.handle(_avatarUrlMeta,
          avatarUrl.isAcceptableOrUnknown(data['avatar_url']!, _avatarUrlMeta));
    }
    if (data.containsKey('avatar_path')) {
      context.handle(
          _avatarPathMeta,
          avatarPath.isAcceptableOrUnknown(
              data['avatar_path']!, _avatarPathMeta));
    }
    if (data.containsKey('avatar_fragment')) {
      context.handle(
          _avatarFragmentMeta,
          avatarFragment.isAcceptableOrUnknown(
              data['avatar_fragment']!, _avatarFragmentMeta));
    }
    if (data.containsKey('property_url')) {
      context.handle(
          _propertyUrlMeta,
          propertyUrl.isAcceptableOrUnknown(
              data['property_url']!, _propertyUrlMeta));
    }
    if (data.containsKey('property_path')) {
      context.handle(
          _propertyPathMeta,
          propertyPath.isAcceptableOrUnknown(
              data['property_path']!, _propertyPathMeta));
    }
    if (data.containsKey('property_fragment')) {
      context.handle(
          _propertyFragmentMeta,
          propertyFragment.isAcceptableOrUnknown(
              data['property_fragment']!, _propertyFragmentMeta));
    }
    if (data.containsKey('describe')) {
      context.handle(_describeMeta,
          describe.isAcceptableOrUnknown(data['describe']!, _describeMeta));
    }
    if (data.containsKey('background_path')) {
      context.handle(
          _backgroundPathMeta,
          backgroundPath.isAcceptableOrUnknown(
              data['background_path']!, _backgroundPathMeta));
    }
    if (data.containsKey('background_url')) {
      context.handle(
          _backgroundUrlMeta,
          backgroundUrl.isAcceptableOrUnknown(
              data['background_url']!, _backgroundUrlMeta));
    }
    if (data.containsKey('invalid')) {
      context.handle(_invalidMeta,
          invalid.isAcceptableOrUnknown(data['invalid']!, _invalidMeta));
    }
    if (data.containsKey('create_time')) {
      context.handle(
          _createTimeMeta,
          createTime.isAcceptableOrUnknown(
              data['create_time']!, _createTimeMeta));
    }
    if (data.containsKey('update_time')) {
      context.handle(
          _updateTimeMeta,
          updateTime.isAcceptableOrUnknown(
              data['update_time']!, _updateTimeMeta));
    }
    if (data.containsKey('member_count')) {
      context.handle(
          _memberCountMeta,
          memberCount.isAcceptableOrUnknown(
              data['member_count']!, _memberCountMeta));
    }
    if (data.containsKey('announcement')) {
      context.handle(
          _announcementMeta,
          announcement.isAcceptableOrUnknown(
              data['announcement']!, _announcementMeta));
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  GroupInfoData map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return GroupInfoData(
      id: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}id'])!,
      groupId: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}group_id'])!,
      title: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}title']),
      permission: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}permission']),
      owner: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}owner']),
      avatarUrl: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}avatar_url']),
      avatarPath: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}avatar_path']),
      avatarFragment: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}avatar_fragment']),
      propertyUrl: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}property_url']),
      propertyPath: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}property_path']),
      propertyFragment: attachedDatabase.typeMapping.read(
          DriftSqlType.string, data['${effectivePrefix}property_fragment']),
      describe: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}describe']),
      backgroundPath: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}background_path']),
      backgroundUrl: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}background_url']),
      invalid: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}invalid']),
      createTime: attachedDatabase.typeMapping
          .read(DriftSqlType.double, data['${effectivePrefix}create_time']),
      updateTime: attachedDatabase.typeMapping
          .read(DriftSqlType.double, data['${effectivePrefix}update_time']),
      memberCount: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}member_count']),
      announcement: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}announcement']),
    );
  }

  @override
  GroupInfo createAlias(String alias) {
    return GroupInfo(attachedDatabase, alias);
  }

  @override
  bool get dontWriteConstraints => true;
}

class GroupInfoData extends DataClass implements Insertable<GroupInfoData> {
  final int id;
  final String groupId;
  final String? title;
  final int? permission;
  final String? owner;
  final String? avatarUrl;
  final String? avatarPath;
  final String? avatarFragment;
  final String? propertyUrl;
  final String? propertyPath;
  final String? propertyFragment;
  final String? describe;
  final String? backgroundPath;
  final String? backgroundUrl;
  final bool? invalid;
  final double? createTime;
  final double? updateTime;
  final int? memberCount;
  final String? announcement;
  const GroupInfoData(
      {required this.id,
      required this.groupId,
      this.title,
      this.permission,
      this.owner,
      this.avatarUrl,
      this.avatarPath,
      this.avatarFragment,
      this.propertyUrl,
      this.propertyPath,
      this.propertyFragment,
      this.describe,
      this.backgroundPath,
      this.backgroundUrl,
      this.invalid,
      this.createTime,
      this.updateTime,
      this.memberCount,
      this.announcement});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<int>(id);
    map['group_id'] = Variable<String>(groupId);
    if (!nullToAbsent || title != null) {
      map['title'] = Variable<String>(title);
    }
    if (!nullToAbsent || permission != null) {
      map['permission'] = Variable<int>(permission);
    }
    if (!nullToAbsent || owner != null) {
      map['owner'] = Variable<String>(owner);
    }
    if (!nullToAbsent || avatarUrl != null) {
      map['avatar_url'] = Variable<String>(avatarUrl);
    }
    if (!nullToAbsent || avatarPath != null) {
      map['avatar_path'] = Variable<String>(avatarPath);
    }
    if (!nullToAbsent || avatarFragment != null) {
      map['avatar_fragment'] = Variable<String>(avatarFragment);
    }
    if (!nullToAbsent || propertyUrl != null) {
      map['property_url'] = Variable<String>(propertyUrl);
    }
    if (!nullToAbsent || propertyPath != null) {
      map['property_path'] = Variable<String>(propertyPath);
    }
    if (!nullToAbsent || propertyFragment != null) {
      map['property_fragment'] = Variable<String>(propertyFragment);
    }
    if (!nullToAbsent || describe != null) {
      map['describe'] = Variable<String>(describe);
    }
    if (!nullToAbsent || backgroundPath != null) {
      map['background_path'] = Variable<String>(backgroundPath);
    }
    if (!nullToAbsent || backgroundUrl != null) {
      map['background_url'] = Variable<String>(backgroundUrl);
    }
    if (!nullToAbsent || invalid != null) {
      map['invalid'] = Variable<bool>(invalid);
    }
    if (!nullToAbsent || createTime != null) {
      map['create_time'] = Variable<double>(createTime);
    }
    if (!nullToAbsent || updateTime != null) {
      map['update_time'] = Variable<double>(updateTime);
    }
    if (!nullToAbsent || memberCount != null) {
      map['member_count'] = Variable<int>(memberCount);
    }
    if (!nullToAbsent || announcement != null) {
      map['announcement'] = Variable<String>(announcement);
    }
    return map;
  }

  GroupInfoCompanion toCompanion(bool nullToAbsent) {
    return GroupInfoCompanion(
      id: Value(id),
      groupId: Value(groupId),
      title:
          title == null && nullToAbsent ? const Value.absent() : Value(title),
      permission: permission == null && nullToAbsent
          ? const Value.absent()
          : Value(permission),
      owner:
          owner == null && nullToAbsent ? const Value.absent() : Value(owner),
      avatarUrl: avatarUrl == null && nullToAbsent
          ? const Value.absent()
          : Value(avatarUrl),
      avatarPath: avatarPath == null && nullToAbsent
          ? const Value.absent()
          : Value(avatarPath),
      avatarFragment: avatarFragment == null && nullToAbsent
          ? const Value.absent()
          : Value(avatarFragment),
      propertyUrl: propertyUrl == null && nullToAbsent
          ? const Value.absent()
          : Value(propertyUrl),
      propertyPath: propertyPath == null && nullToAbsent
          ? const Value.absent()
          : Value(propertyPath),
      propertyFragment: propertyFragment == null && nullToAbsent
          ? const Value.absent()
          : Value(propertyFragment),
      describe: describe == null && nullToAbsent
          ? const Value.absent()
          : Value(describe),
      backgroundPath: backgroundPath == null && nullToAbsent
          ? const Value.absent()
          : Value(backgroundPath),
      backgroundUrl: backgroundUrl == null && nullToAbsent
          ? const Value.absent()
          : Value(backgroundUrl),
      invalid: invalid == null && nullToAbsent
          ? const Value.absent()
          : Value(invalid),
      createTime: createTime == null && nullToAbsent
          ? const Value.absent()
          : Value(createTime),
      updateTime: updateTime == null && nullToAbsent
          ? const Value.absent()
          : Value(updateTime),
      memberCount: memberCount == null && nullToAbsent
          ? const Value.absent()
          : Value(memberCount),
      announcement: announcement == null && nullToAbsent
          ? const Value.absent()
          : Value(announcement),
    );
  }

  factory GroupInfoData.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return GroupInfoData(
      id: serializer.fromJson<int>(json['id']),
      groupId: serializer.fromJson<String>(json['group_id']),
      title: serializer.fromJson<String?>(json['title']),
      permission: serializer.fromJson<int?>(json['permission']),
      owner: serializer.fromJson<String?>(json['owner']),
      avatarUrl: serializer.fromJson<String?>(json['avatar_url']),
      avatarPath: serializer.fromJson<String?>(json['avatar_path']),
      avatarFragment: serializer.fromJson<String?>(json['avatar_fragment']),
      propertyUrl: serializer.fromJson<String?>(json['property_url']),
      propertyPath: serializer.fromJson<String?>(json['property_path']),
      propertyFragment: serializer.fromJson<String?>(json['property_fragment']),
      describe: serializer.fromJson<String?>(json['describe']),
      backgroundPath: serializer.fromJson<String?>(json['background_path']),
      backgroundUrl: serializer.fromJson<String?>(json['background_url']),
      invalid: serializer.fromJson<bool?>(json['invalid']),
      createTime: serializer.fromJson<double?>(json['create_time']),
      updateTime: serializer.fromJson<double?>(json['update_time']),
      memberCount: serializer.fromJson<int?>(json['member_count']),
      announcement: serializer.fromJson<String?>(json['announcement']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<int>(id),
      'group_id': serializer.toJson<String>(groupId),
      'title': serializer.toJson<String?>(title),
      'permission': serializer.toJson<int?>(permission),
      'owner': serializer.toJson<String?>(owner),
      'avatar_url': serializer.toJson<String?>(avatarUrl),
      'avatar_path': serializer.toJson<String?>(avatarPath),
      'avatar_fragment': serializer.toJson<String?>(avatarFragment),
      'property_url': serializer.toJson<String?>(propertyUrl),
      'property_path': serializer.toJson<String?>(propertyPath),
      'property_fragment': serializer.toJson<String?>(propertyFragment),
      'describe': serializer.toJson<String?>(describe),
      'background_path': serializer.toJson<String?>(backgroundPath),
      'background_url': serializer.toJson<String?>(backgroundUrl),
      'invalid': serializer.toJson<bool?>(invalid),
      'create_time': serializer.toJson<double?>(createTime),
      'update_time': serializer.toJson<double?>(updateTime),
      'member_count': serializer.toJson<int?>(memberCount),
      'announcement': serializer.toJson<String?>(announcement),
    };
  }

  GroupInfoData copyWith(
          {int? id,
          String? groupId,
          Value<String?> title = const Value.absent(),
          Value<int?> permission = const Value.absent(),
          Value<String?> owner = const Value.absent(),
          Value<String?> avatarUrl = const Value.absent(),
          Value<String?> avatarPath = const Value.absent(),
          Value<String?> avatarFragment = const Value.absent(),
          Value<String?> propertyUrl = const Value.absent(),
          Value<String?> propertyPath = const Value.absent(),
          Value<String?> propertyFragment = const Value.absent(),
          Value<String?> describe = const Value.absent(),
          Value<String?> backgroundPath = const Value.absent(),
          Value<String?> backgroundUrl = const Value.absent(),
          Value<bool?> invalid = const Value.absent(),
          Value<double?> createTime = const Value.absent(),
          Value<double?> updateTime = const Value.absent(),
          Value<int?> memberCount = const Value.absent(),
          Value<String?> announcement = const Value.absent()}) =>
      GroupInfoData(
        id: id ?? this.id,
        groupId: groupId ?? this.groupId,
        title: title.present ? title.value : this.title,
        permission: permission.present ? permission.value : this.permission,
        owner: owner.present ? owner.value : this.owner,
        avatarUrl: avatarUrl.present ? avatarUrl.value : this.avatarUrl,
        avatarPath: avatarPath.present ? avatarPath.value : this.avatarPath,
        avatarFragment:
            avatarFragment.present ? avatarFragment.value : this.avatarFragment,
        propertyUrl: propertyUrl.present ? propertyUrl.value : this.propertyUrl,
        propertyPath:
            propertyPath.present ? propertyPath.value : this.propertyPath,
        propertyFragment: propertyFragment.present
            ? propertyFragment.value
            : this.propertyFragment,
        describe: describe.present ? describe.value : this.describe,
        backgroundPath:
            backgroundPath.present ? backgroundPath.value : this.backgroundPath,
        backgroundUrl:
            backgroundUrl.present ? backgroundUrl.value : this.backgroundUrl,
        invalid: invalid.present ? invalid.value : this.invalid,
        createTime: createTime.present ? createTime.value : this.createTime,
        updateTime: updateTime.present ? updateTime.value : this.updateTime,
        memberCount: memberCount.present ? memberCount.value : this.memberCount,
        announcement:
            announcement.present ? announcement.value : this.announcement,
      );
  @override
  String toString() {
    return (StringBuffer('GroupInfoData(')
          ..write('id: $id, ')
          ..write('groupId: $groupId, ')
          ..write('title: $title, ')
          ..write('permission: $permission, ')
          ..write('owner: $owner, ')
          ..write('avatarUrl: $avatarUrl, ')
          ..write('avatarPath: $avatarPath, ')
          ..write('avatarFragment: $avatarFragment, ')
          ..write('propertyUrl: $propertyUrl, ')
          ..write('propertyPath: $propertyPath, ')
          ..write('propertyFragment: $propertyFragment, ')
          ..write('describe: $describe, ')
          ..write('backgroundPath: $backgroundPath, ')
          ..write('backgroundUrl: $backgroundUrl, ')
          ..write('invalid: $invalid, ')
          ..write('createTime: $createTime, ')
          ..write('updateTime: $updateTime, ')
          ..write('memberCount: $memberCount, ')
          ..write('announcement: $announcement')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(
      id,
      groupId,
      title,
      permission,
      owner,
      avatarUrl,
      avatarPath,
      avatarFragment,
      propertyUrl,
      propertyPath,
      propertyFragment,
      describe,
      backgroundPath,
      backgroundUrl,
      invalid,
      createTime,
      updateTime,
      memberCount,
      announcement);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is GroupInfoData &&
          other.id == this.id &&
          other.groupId == this.groupId &&
          other.title == this.title &&
          other.permission == this.permission &&
          other.owner == this.owner &&
          other.avatarUrl == this.avatarUrl &&
          other.avatarPath == this.avatarPath &&
          other.avatarFragment == this.avatarFragment &&
          other.propertyUrl == this.propertyUrl &&
          other.propertyPath == this.propertyPath &&
          other.propertyFragment == this.propertyFragment &&
          other.describe == this.describe &&
          other.backgroundPath == this.backgroundPath &&
          other.backgroundUrl == this.backgroundUrl &&
          other.invalid == this.invalid &&
          other.createTime == this.createTime &&
          other.updateTime == this.updateTime &&
          other.memberCount == this.memberCount &&
          other.announcement == this.announcement);
}

class GroupInfoCompanion extends UpdateCompanion<GroupInfoData> {
  final Value<int> id;
  final Value<String> groupId;
  final Value<String?> title;
  final Value<int?> permission;
  final Value<String?> owner;
  final Value<String?> avatarUrl;
  final Value<String?> avatarPath;
  final Value<String?> avatarFragment;
  final Value<String?> propertyUrl;
  final Value<String?> propertyPath;
  final Value<String?> propertyFragment;
  final Value<String?> describe;
  final Value<String?> backgroundPath;
  final Value<String?> backgroundUrl;
  final Value<bool?> invalid;
  final Value<double?> createTime;
  final Value<double?> updateTime;
  final Value<int?> memberCount;
  final Value<String?> announcement;
  const GroupInfoCompanion({
    this.id = const Value.absent(),
    this.groupId = const Value.absent(),
    this.title = const Value.absent(),
    this.permission = const Value.absent(),
    this.owner = const Value.absent(),
    this.avatarUrl = const Value.absent(),
    this.avatarPath = const Value.absent(),
    this.avatarFragment = const Value.absent(),
    this.propertyUrl = const Value.absent(),
    this.propertyPath = const Value.absent(),
    this.propertyFragment = const Value.absent(),
    this.describe = const Value.absent(),
    this.backgroundPath = const Value.absent(),
    this.backgroundUrl = const Value.absent(),
    this.invalid = const Value.absent(),
    this.createTime = const Value.absent(),
    this.updateTime = const Value.absent(),
    this.memberCount = const Value.absent(),
    this.announcement = const Value.absent(),
  });
  GroupInfoCompanion.insert({
    this.id = const Value.absent(),
    required String groupId,
    this.title = const Value.absent(),
    this.permission = const Value.absent(),
    this.owner = const Value.absent(),
    this.avatarUrl = const Value.absent(),
    this.avatarPath = const Value.absent(),
    this.avatarFragment = const Value.absent(),
    this.propertyUrl = const Value.absent(),
    this.propertyPath = const Value.absent(),
    this.propertyFragment = const Value.absent(),
    this.describe = const Value.absent(),
    this.backgroundPath = const Value.absent(),
    this.backgroundUrl = const Value.absent(),
    this.invalid = const Value.absent(),
    this.createTime = const Value.absent(),
    this.updateTime = const Value.absent(),
    this.memberCount = const Value.absent(),
    this.announcement = const Value.absent(),
  }) : groupId = Value(groupId);
  static Insertable<GroupInfoData> custom({
    Expression<int>? id,
    Expression<String>? groupId,
    Expression<String>? title,
    Expression<int>? permission,
    Expression<String>? owner,
    Expression<String>? avatarUrl,
    Expression<String>? avatarPath,
    Expression<String>? avatarFragment,
    Expression<String>? propertyUrl,
    Expression<String>? propertyPath,
    Expression<String>? propertyFragment,
    Expression<String>? describe,
    Expression<String>? backgroundPath,
    Expression<String>? backgroundUrl,
    Expression<bool>? invalid,
    Expression<double>? createTime,
    Expression<double>? updateTime,
    Expression<int>? memberCount,
    Expression<String>? announcement,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (groupId != null) 'group_id': groupId,
      if (title != null) 'title': title,
      if (permission != null) 'permission': permission,
      if (owner != null) 'owner': owner,
      if (avatarUrl != null) 'avatar_url': avatarUrl,
      if (avatarPath != null) 'avatar_path': avatarPath,
      if (avatarFragment != null) 'avatar_fragment': avatarFragment,
      if (propertyUrl != null) 'property_url': propertyUrl,
      if (propertyPath != null) 'property_path': propertyPath,
      if (propertyFragment != null) 'property_fragment': propertyFragment,
      if (describe != null) 'describe': describe,
      if (backgroundPath != null) 'background_path': backgroundPath,
      if (backgroundUrl != null) 'background_url': backgroundUrl,
      if (invalid != null) 'invalid': invalid,
      if (createTime != null) 'create_time': createTime,
      if (updateTime != null) 'update_time': updateTime,
      if (memberCount != null) 'member_count': memberCount,
      if (announcement != null) 'announcement': announcement,
    });
  }

  GroupInfoCompanion copyWith(
      {Value<int>? id,
      Value<String>? groupId,
      Value<String?>? title,
      Value<int?>? permission,
      Value<String?>? owner,
      Value<String?>? avatarUrl,
      Value<String?>? avatarPath,
      Value<String?>? avatarFragment,
      Value<String?>? propertyUrl,
      Value<String?>? propertyPath,
      Value<String?>? propertyFragment,
      Value<String?>? describe,
      Value<String?>? backgroundPath,
      Value<String?>? backgroundUrl,
      Value<bool?>? invalid,
      Value<double?>? createTime,
      Value<double?>? updateTime,
      Value<int?>? memberCount,
      Value<String?>? announcement}) {
    return GroupInfoCompanion(
      id: id ?? this.id,
      groupId: groupId ?? this.groupId,
      title: title ?? this.title,
      permission: permission ?? this.permission,
      owner: owner ?? this.owner,
      avatarUrl: avatarUrl ?? this.avatarUrl,
      avatarPath: avatarPath ?? this.avatarPath,
      avatarFragment: avatarFragment ?? this.avatarFragment,
      propertyUrl: propertyUrl ?? this.propertyUrl,
      propertyPath: propertyPath ?? this.propertyPath,
      propertyFragment: propertyFragment ?? this.propertyFragment,
      describe: describe ?? this.describe,
      backgroundPath: backgroundPath ?? this.backgroundPath,
      backgroundUrl: backgroundUrl ?? this.backgroundUrl,
      invalid: invalid ?? this.invalid,
      createTime: createTime ?? this.createTime,
      updateTime: updateTime ?? this.updateTime,
      memberCount: memberCount ?? this.memberCount,
      announcement: announcement ?? this.announcement,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<int>(id.value);
    }
    if (groupId.present) {
      map['group_id'] = Variable<String>(groupId.value);
    }
    if (title.present) {
      map['title'] = Variable<String>(title.value);
    }
    if (permission.present) {
      map['permission'] = Variable<int>(permission.value);
    }
    if (owner.present) {
      map['owner'] = Variable<String>(owner.value);
    }
    if (avatarUrl.present) {
      map['avatar_url'] = Variable<String>(avatarUrl.value);
    }
    if (avatarPath.present) {
      map['avatar_path'] = Variable<String>(avatarPath.value);
    }
    if (avatarFragment.present) {
      map['avatar_fragment'] = Variable<String>(avatarFragment.value);
    }
    if (propertyUrl.present) {
      map['property_url'] = Variable<String>(propertyUrl.value);
    }
    if (propertyPath.present) {
      map['property_path'] = Variable<String>(propertyPath.value);
    }
    if (propertyFragment.present) {
      map['property_fragment'] = Variable<String>(propertyFragment.value);
    }
    if (describe.present) {
      map['describe'] = Variable<String>(describe.value);
    }
    if (backgroundPath.present) {
      map['background_path'] = Variable<String>(backgroundPath.value);
    }
    if (backgroundUrl.present) {
      map['background_url'] = Variable<String>(backgroundUrl.value);
    }
    if (invalid.present) {
      map['invalid'] = Variable<bool>(invalid.value);
    }
    if (createTime.present) {
      map['create_time'] = Variable<double>(createTime.value);
    }
    if (updateTime.present) {
      map['update_time'] = Variable<double>(updateTime.value);
    }
    if (memberCount.present) {
      map['member_count'] = Variable<int>(memberCount.value);
    }
    if (announcement.present) {
      map['announcement'] = Variable<String>(announcement.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('GroupInfoCompanion(')
          ..write('id: $id, ')
          ..write('groupId: $groupId, ')
          ..write('title: $title, ')
          ..write('permission: $permission, ')
          ..write('owner: $owner, ')
          ..write('avatarUrl: $avatarUrl, ')
          ..write('avatarPath: $avatarPath, ')
          ..write('avatarFragment: $avatarFragment, ')
          ..write('propertyUrl: $propertyUrl, ')
          ..write('propertyPath: $propertyPath, ')
          ..write('propertyFragment: $propertyFragment, ')
          ..write('describe: $describe, ')
          ..write('backgroundPath: $backgroundPath, ')
          ..write('backgroundUrl: $backgroundUrl, ')
          ..write('invalid: $invalid, ')
          ..write('createTime: $createTime, ')
          ..write('updateTime: $updateTime, ')
          ..write('memberCount: $memberCount, ')
          ..write('announcement: $announcement')
          ..write(')'))
        .toString();
  }
}

class GroupMember extends Table with TableInfo<GroupMember, GroupMemberData> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  GroupMember(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  late final GeneratedColumn<int> id = GeneratedColumn<int>(
      'id', aliasedName, false,
      hasAutoIncrement: true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      $customConstraints: 'NOT NULL PRIMARY KEY AUTOINCREMENT');
  static const VerificationMeta _groupIdMeta =
      const VerificationMeta('groupId');
  late final GeneratedColumn<String> groupId = GeneratedColumn<String>(
      'group_id', aliasedName, false,
      type: DriftSqlType.string,
      requiredDuringInsert: true,
      $customConstraints: 'NOT NULL');
  static const VerificationMeta _usernameMeta =
      const VerificationMeta('username');
  late final GeneratedColumn<String> username = GeneratedColumn<String>(
      'username', aliasedName, false,
      type: DriftSqlType.string,
      requiredDuringInsert: true,
      $customConstraints: 'NOT NULL');
  static const VerificationMeta _displaynameMeta =
      const VerificationMeta('displayname');
  late final GeneratedColumn<String> displayname = GeneratedColumn<String>(
      'displayname', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _memberUuidMeta =
      const VerificationMeta('memberUuid');
  late final GeneratedColumn<String> memberUuid = GeneratedColumn<String>(
      'member_uuid', aliasedName, false,
      type: DriftSqlType.string,
      requiredDuringInsert: true,
      $customConstraints: 'NOT NULL UNIQUE');
  static const VerificationMeta _roleMeta = const VerificationMeta('role');
  late final GeneratedColumn<int> role = GeneratedColumn<int>(
      'role', aliasedName, true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _startMuteTimeMeta =
      const VerificationMeta('startMuteTime');
  late final GeneratedColumn<int> startMuteTime = GeneratedColumn<int>(
      'start_mute_time', aliasedName, true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _muteDurationMeta =
      const VerificationMeta('muteDuration');
  late final GeneratedColumn<int> muteDuration = GeneratedColumn<int>(
      'mute_duration', aliasedName, true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _muteExpireMeta =
      const VerificationMeta('muteExpire');
  late final GeneratedColumn<int> muteExpire = GeneratedColumn<int>(
      'mute_expire', aliasedName, true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _tagsMeta = const VerificationMeta('tags');
  late final GeneratedColumn<String> tags = GeneratedColumn<String>(
      'tags', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _createTimeMeta =
      const VerificationMeta('createTime');
  late final GeneratedColumn<double> createTime = GeneratedColumn<double>(
      'create_time', aliasedName, true,
      type: DriftSqlType.double,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _updateTimeMeta =
      const VerificationMeta('updateTime');
  late final GeneratedColumn<double> updateTime = GeneratedColumn<double>(
      'update_time', aliasedName, true,
      type: DriftSqlType.double,
      requiredDuringInsert: false,
      $customConstraints: '');
  @override
  List<GeneratedColumn> get $columns => [
        id,
        groupId,
        username,
        displayname,
        memberUuid,
        role,
        startMuteTime,
        muteDuration,
        muteExpire,
        tags,
        createTime,
        updateTime
      ];
  @override
  String get aliasedName => _alias ?? 'group_member';
  @override
  String get actualTableName => 'group_member';
  @override
  VerificationContext validateIntegrity(Insertable<GroupMemberData> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    }
    if (data.containsKey('group_id')) {
      context.handle(_groupIdMeta,
          groupId.isAcceptableOrUnknown(data['group_id']!, _groupIdMeta));
    } else if (isInserting) {
      context.missing(_groupIdMeta);
    }
    if (data.containsKey('username')) {
      context.handle(_usernameMeta,
          username.isAcceptableOrUnknown(data['username']!, _usernameMeta));
    } else if (isInserting) {
      context.missing(_usernameMeta);
    }
    if (data.containsKey('displayname')) {
      context.handle(
          _displaynameMeta,
          displayname.isAcceptableOrUnknown(
              data['displayname']!, _displaynameMeta));
    }
    if (data.containsKey('member_uuid')) {
      context.handle(
          _memberUuidMeta,
          memberUuid.isAcceptableOrUnknown(
              data['member_uuid']!, _memberUuidMeta));
    } else if (isInserting) {
      context.missing(_memberUuidMeta);
    }
    if (data.containsKey('role')) {
      context.handle(
          _roleMeta, role.isAcceptableOrUnknown(data['role']!, _roleMeta));
    }
    if (data.containsKey('start_mute_time')) {
      context.handle(
          _startMuteTimeMeta,
          startMuteTime.isAcceptableOrUnknown(
              data['start_mute_time']!, _startMuteTimeMeta));
    }
    if (data.containsKey('mute_duration')) {
      context.handle(
          _muteDurationMeta,
          muteDuration.isAcceptableOrUnknown(
              data['mute_duration']!, _muteDurationMeta));
    }
    if (data.containsKey('mute_expire')) {
      context.handle(
          _muteExpireMeta,
          muteExpire.isAcceptableOrUnknown(
              data['mute_expire']!, _muteExpireMeta));
    }
    if (data.containsKey('tags')) {
      context.handle(
          _tagsMeta, tags.isAcceptableOrUnknown(data['tags']!, _tagsMeta));
    }
    if (data.containsKey('create_time')) {
      context.handle(
          _createTimeMeta,
          createTime.isAcceptableOrUnknown(
              data['create_time']!, _createTimeMeta));
    }
    if (data.containsKey('update_time')) {
      context.handle(
          _updateTimeMeta,
          updateTime.isAcceptableOrUnknown(
              data['update_time']!, _updateTimeMeta));
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  GroupMemberData map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return GroupMemberData(
      id: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}id'])!,
      groupId: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}group_id'])!,
      username: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}username'])!,
      displayname: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}displayname']),
      memberUuid: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}member_uuid'])!,
      role: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}role']),
      startMuteTime: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}start_mute_time']),
      muteDuration: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}mute_duration']),
      muteExpire: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}mute_expire']),
      tags: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}tags']),
      createTime: attachedDatabase.typeMapping
          .read(DriftSqlType.double, data['${effectivePrefix}create_time']),
      updateTime: attachedDatabase.typeMapping
          .read(DriftSqlType.double, data['${effectivePrefix}update_time']),
    );
  }

  @override
  GroupMember createAlias(String alias) {
    return GroupMember(attachedDatabase, alias);
  }

  @override
  bool get dontWriteConstraints => true;
}

class GroupMemberData extends DataClass implements Insertable<GroupMemberData> {
  final int id;
  final String groupId;
  final String username;
  final String? displayname;
  final String memberUuid;
  final int? role;
  final int? startMuteTime;
  final int? muteDuration;
  final int? muteExpire;
  final String? tags;
  final double? createTime;
  final double? updateTime;
  const GroupMemberData(
      {required this.id,
      required this.groupId,
      required this.username,
      this.displayname,
      required this.memberUuid,
      this.role,
      this.startMuteTime,
      this.muteDuration,
      this.muteExpire,
      this.tags,
      this.createTime,
      this.updateTime});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<int>(id);
    map['group_id'] = Variable<String>(groupId);
    map['username'] = Variable<String>(username);
    if (!nullToAbsent || displayname != null) {
      map['displayname'] = Variable<String>(displayname);
    }
    map['member_uuid'] = Variable<String>(memberUuid);
    if (!nullToAbsent || role != null) {
      map['role'] = Variable<int>(role);
    }
    if (!nullToAbsent || startMuteTime != null) {
      map['start_mute_time'] = Variable<int>(startMuteTime);
    }
    if (!nullToAbsent || muteDuration != null) {
      map['mute_duration'] = Variable<int>(muteDuration);
    }
    if (!nullToAbsent || muteExpire != null) {
      map['mute_expire'] = Variable<int>(muteExpire);
    }
    if (!nullToAbsent || tags != null) {
      map['tags'] = Variable<String>(tags);
    }
    if (!nullToAbsent || createTime != null) {
      map['create_time'] = Variable<double>(createTime);
    }
    if (!nullToAbsent || updateTime != null) {
      map['update_time'] = Variable<double>(updateTime);
    }
    return map;
  }

  GroupMemberCompanion toCompanion(bool nullToAbsent) {
    return GroupMemberCompanion(
      id: Value(id),
      groupId: Value(groupId),
      username: Value(username),
      displayname: displayname == null && nullToAbsent
          ? const Value.absent()
          : Value(displayname),
      memberUuid: Value(memberUuid),
      role: role == null && nullToAbsent ? const Value.absent() : Value(role),
      startMuteTime: startMuteTime == null && nullToAbsent
          ? const Value.absent()
          : Value(startMuteTime),
      muteDuration: muteDuration == null && nullToAbsent
          ? const Value.absent()
          : Value(muteDuration),
      muteExpire: muteExpire == null && nullToAbsent
          ? const Value.absent()
          : Value(muteExpire),
      tags: tags == null && nullToAbsent ? const Value.absent() : Value(tags),
      createTime: createTime == null && nullToAbsent
          ? const Value.absent()
          : Value(createTime),
      updateTime: updateTime == null && nullToAbsent
          ? const Value.absent()
          : Value(updateTime),
    );
  }

  factory GroupMemberData.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return GroupMemberData(
      id: serializer.fromJson<int>(json['id']),
      groupId: serializer.fromJson<String>(json['group_id']),
      username: serializer.fromJson<String>(json['username']),
      displayname: serializer.fromJson<String?>(json['displayname']),
      memberUuid: serializer.fromJson<String>(json['member_uuid']),
      role: serializer.fromJson<int?>(json['role']),
      startMuteTime: serializer.fromJson<int?>(json['start_mute_time']),
      muteDuration: serializer.fromJson<int?>(json['mute_duration']),
      muteExpire: serializer.fromJson<int?>(json['mute_expire']),
      tags: serializer.fromJson<String?>(json['tags']),
      createTime: serializer.fromJson<double?>(json['create_time']),
      updateTime: serializer.fromJson<double?>(json['update_time']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<int>(id),
      'group_id': serializer.toJson<String>(groupId),
      'username': serializer.toJson<String>(username),
      'displayname': serializer.toJson<String?>(displayname),
      'member_uuid': serializer.toJson<String>(memberUuid),
      'role': serializer.toJson<int?>(role),
      'start_mute_time': serializer.toJson<int?>(startMuteTime),
      'mute_duration': serializer.toJson<int?>(muteDuration),
      'mute_expire': serializer.toJson<int?>(muteExpire),
      'tags': serializer.toJson<String?>(tags),
      'create_time': serializer.toJson<double?>(createTime),
      'update_time': serializer.toJson<double?>(updateTime),
    };
  }

  GroupMemberData copyWith(
          {int? id,
          String? groupId,
          String? username,
          Value<String?> displayname = const Value.absent(),
          String? memberUuid,
          Value<int?> role = const Value.absent(),
          Value<int?> startMuteTime = const Value.absent(),
          Value<int?> muteDuration = const Value.absent(),
          Value<int?> muteExpire = const Value.absent(),
          Value<String?> tags = const Value.absent(),
          Value<double?> createTime = const Value.absent(),
          Value<double?> updateTime = const Value.absent()}) =>
      GroupMemberData(
        id: id ?? this.id,
        groupId: groupId ?? this.groupId,
        username: username ?? this.username,
        displayname: displayname.present ? displayname.value : this.displayname,
        memberUuid: memberUuid ?? this.memberUuid,
        role: role.present ? role.value : this.role,
        startMuteTime:
            startMuteTime.present ? startMuteTime.value : this.startMuteTime,
        muteDuration:
            muteDuration.present ? muteDuration.value : this.muteDuration,
        muteExpire: muteExpire.present ? muteExpire.value : this.muteExpire,
        tags: tags.present ? tags.value : this.tags,
        createTime: createTime.present ? createTime.value : this.createTime,
        updateTime: updateTime.present ? updateTime.value : this.updateTime,
      );
  @override
  String toString() {
    return (StringBuffer('GroupMemberData(')
          ..write('id: $id, ')
          ..write('groupId: $groupId, ')
          ..write('username: $username, ')
          ..write('displayname: $displayname, ')
          ..write('memberUuid: $memberUuid, ')
          ..write('role: $role, ')
          ..write('startMuteTime: $startMuteTime, ')
          ..write('muteDuration: $muteDuration, ')
          ..write('muteExpire: $muteExpire, ')
          ..write('tags: $tags, ')
          ..write('createTime: $createTime, ')
          ..write('updateTime: $updateTime')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(
      id,
      groupId,
      username,
      displayname,
      memberUuid,
      role,
      startMuteTime,
      muteDuration,
      muteExpire,
      tags,
      createTime,
      updateTime);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is GroupMemberData &&
          other.id == this.id &&
          other.groupId == this.groupId &&
          other.username == this.username &&
          other.displayname == this.displayname &&
          other.memberUuid == this.memberUuid &&
          other.role == this.role &&
          other.startMuteTime == this.startMuteTime &&
          other.muteDuration == this.muteDuration &&
          other.muteExpire == this.muteExpire &&
          other.tags == this.tags &&
          other.createTime == this.createTime &&
          other.updateTime == this.updateTime);
}

class GroupMemberCompanion extends UpdateCompanion<GroupMemberData> {
  final Value<int> id;
  final Value<String> groupId;
  final Value<String> username;
  final Value<String?> displayname;
  final Value<String> memberUuid;
  final Value<int?> role;
  final Value<int?> startMuteTime;
  final Value<int?> muteDuration;
  final Value<int?> muteExpire;
  final Value<String?> tags;
  final Value<double?> createTime;
  final Value<double?> updateTime;
  const GroupMemberCompanion({
    this.id = const Value.absent(),
    this.groupId = const Value.absent(),
    this.username = const Value.absent(),
    this.displayname = const Value.absent(),
    this.memberUuid = const Value.absent(),
    this.role = const Value.absent(),
    this.startMuteTime = const Value.absent(),
    this.muteDuration = const Value.absent(),
    this.muteExpire = const Value.absent(),
    this.tags = const Value.absent(),
    this.createTime = const Value.absent(),
    this.updateTime = const Value.absent(),
  });
  GroupMemberCompanion.insert({
    this.id = const Value.absent(),
    required String groupId,
    required String username,
    this.displayname = const Value.absent(),
    required String memberUuid,
    this.role = const Value.absent(),
    this.startMuteTime = const Value.absent(),
    this.muteDuration = const Value.absent(),
    this.muteExpire = const Value.absent(),
    this.tags = const Value.absent(),
    this.createTime = const Value.absent(),
    this.updateTime = const Value.absent(),
  })  : groupId = Value(groupId),
        username = Value(username),
        memberUuid = Value(memberUuid);
  static Insertable<GroupMemberData> custom({
    Expression<int>? id,
    Expression<String>? groupId,
    Expression<String>? username,
    Expression<String>? displayname,
    Expression<String>? memberUuid,
    Expression<int>? role,
    Expression<int>? startMuteTime,
    Expression<int>? muteDuration,
    Expression<int>? muteExpire,
    Expression<String>? tags,
    Expression<double>? createTime,
    Expression<double>? updateTime,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (groupId != null) 'group_id': groupId,
      if (username != null) 'username': username,
      if (displayname != null) 'displayname': displayname,
      if (memberUuid != null) 'member_uuid': memberUuid,
      if (role != null) 'role': role,
      if (startMuteTime != null) 'start_mute_time': startMuteTime,
      if (muteDuration != null) 'mute_duration': muteDuration,
      if (muteExpire != null) 'mute_expire': muteExpire,
      if (tags != null) 'tags': tags,
      if (createTime != null) 'create_time': createTime,
      if (updateTime != null) 'update_time': updateTime,
    });
  }

  GroupMemberCompanion copyWith(
      {Value<int>? id,
      Value<String>? groupId,
      Value<String>? username,
      Value<String?>? displayname,
      Value<String>? memberUuid,
      Value<int?>? role,
      Value<int?>? startMuteTime,
      Value<int?>? muteDuration,
      Value<int?>? muteExpire,
      Value<String?>? tags,
      Value<double?>? createTime,
      Value<double?>? updateTime}) {
    return GroupMemberCompanion(
      id: id ?? this.id,
      groupId: groupId ?? this.groupId,
      username: username ?? this.username,
      displayname: displayname ?? this.displayname,
      memberUuid: memberUuid ?? this.memberUuid,
      role: role ?? this.role,
      startMuteTime: startMuteTime ?? this.startMuteTime,
      muteDuration: muteDuration ?? this.muteDuration,
      muteExpire: muteExpire ?? this.muteExpire,
      tags: tags ?? this.tags,
      createTime: createTime ?? this.createTime,
      updateTime: updateTime ?? this.updateTime,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<int>(id.value);
    }
    if (groupId.present) {
      map['group_id'] = Variable<String>(groupId.value);
    }
    if (username.present) {
      map['username'] = Variable<String>(username.value);
    }
    if (displayname.present) {
      map['displayname'] = Variable<String>(displayname.value);
    }
    if (memberUuid.present) {
      map['member_uuid'] = Variable<String>(memberUuid.value);
    }
    if (role.present) {
      map['role'] = Variable<int>(role.value);
    }
    if (startMuteTime.present) {
      map['start_mute_time'] = Variable<int>(startMuteTime.value);
    }
    if (muteDuration.present) {
      map['mute_duration'] = Variable<int>(muteDuration.value);
    }
    if (muteExpire.present) {
      map['mute_expire'] = Variable<int>(muteExpire.value);
    }
    if (tags.present) {
      map['tags'] = Variable<String>(tags.value);
    }
    if (createTime.present) {
      map['create_time'] = Variable<double>(createTime.value);
    }
    if (updateTime.present) {
      map['update_time'] = Variable<double>(updateTime.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('GroupMemberCompanion(')
          ..write('id: $id, ')
          ..write('groupId: $groupId, ')
          ..write('username: $username, ')
          ..write('displayname: $displayname, ')
          ..write('memberUuid: $memberUuid, ')
          ..write('role: $role, ')
          ..write('startMuteTime: $startMuteTime, ')
          ..write('muteDuration: $muteDuration, ')
          ..write('muteExpire: $muteExpire, ')
          ..write('tags: $tags, ')
          ..write('createTime: $createTime, ')
          ..write('updateTime: $updateTime')
          ..write(')'))
        .toString();
  }
}

class Log extends Table with TableInfo<Log, LogData> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  Log(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  late final GeneratedColumn<int> id = GeneratedColumn<int>(
      'id', aliasedName, false,
      hasAutoIncrement: true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      $customConstraints: 'NOT NULL PRIMARY KEY AUTOINCREMENT');
  static const VerificationMeta _typeMeta = const VerificationMeta('type');
  late final GeneratedColumn<int> type = GeneratedColumn<int>(
      'type', aliasedName, true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _bodyMeta = const VerificationMeta('body');
  late final GeneratedColumn<String> body = GeneratedColumn<String>(
      'body', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _createTimeMeta =
      const VerificationMeta('createTime');
  late final GeneratedColumn<double> createTime = GeneratedColumn<double>(
      'create_time', aliasedName, true,
      type: DriftSqlType.double,
      requiredDuringInsert: false,
      $customConstraints: '');
  @override
  List<GeneratedColumn> get $columns => [id, type, body, createTime];
  @override
  String get aliasedName => _alias ?? 'log';
  @override
  String get actualTableName => 'log';
  @override
  VerificationContext validateIntegrity(Insertable<LogData> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    }
    if (data.containsKey('type')) {
      context.handle(
          _typeMeta, type.isAcceptableOrUnknown(data['type']!, _typeMeta));
    }
    if (data.containsKey('body')) {
      context.handle(
          _bodyMeta, body.isAcceptableOrUnknown(data['body']!, _bodyMeta));
    }
    if (data.containsKey('create_time')) {
      context.handle(
          _createTimeMeta,
          createTime.isAcceptableOrUnknown(
              data['create_time']!, _createTimeMeta));
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  LogData map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return LogData(
      id: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}id'])!,
      type: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}type']),
      body: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}body']),
      createTime: attachedDatabase.typeMapping
          .read(DriftSqlType.double, data['${effectivePrefix}create_time']),
    );
  }

  @override
  Log createAlias(String alias) {
    return Log(attachedDatabase, alias);
  }

  @override
  bool get dontWriteConstraints => true;
}

class LogData extends DataClass implements Insertable<LogData> {
  final int id;
  final int? type;
  final String? body;
  final double? createTime;
  const LogData({required this.id, this.type, this.body, this.createTime});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<int>(id);
    if (!nullToAbsent || type != null) {
      map['type'] = Variable<int>(type);
    }
    if (!nullToAbsent || body != null) {
      map['body'] = Variable<String>(body);
    }
    if (!nullToAbsent || createTime != null) {
      map['create_time'] = Variable<double>(createTime);
    }
    return map;
  }

  LogCompanion toCompanion(bool nullToAbsent) {
    return LogCompanion(
      id: Value(id),
      type: type == null && nullToAbsent ? const Value.absent() : Value(type),
      body: body == null && nullToAbsent ? const Value.absent() : Value(body),
      createTime: createTime == null && nullToAbsent
          ? const Value.absent()
          : Value(createTime),
    );
  }

  factory LogData.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return LogData(
      id: serializer.fromJson<int>(json['id']),
      type: serializer.fromJson<int?>(json['type']),
      body: serializer.fromJson<String?>(json['body']),
      createTime: serializer.fromJson<double?>(json['create_time']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<int>(id),
      'type': serializer.toJson<int?>(type),
      'body': serializer.toJson<String?>(body),
      'create_time': serializer.toJson<double?>(createTime),
    };
  }

  LogData copyWith(
          {int? id,
          Value<int?> type = const Value.absent(),
          Value<String?> body = const Value.absent(),
          Value<double?> createTime = const Value.absent()}) =>
      LogData(
        id: id ?? this.id,
        type: type.present ? type.value : this.type,
        body: body.present ? body.value : this.body,
        createTime: createTime.present ? createTime.value : this.createTime,
      );
  @override
  String toString() {
    return (StringBuffer('LogData(')
          ..write('id: $id, ')
          ..write('type: $type, ')
          ..write('body: $body, ')
          ..write('createTime: $createTime')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(id, type, body, createTime);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is LogData &&
          other.id == this.id &&
          other.type == this.type &&
          other.body == this.body &&
          other.createTime == this.createTime);
}

class LogCompanion extends UpdateCompanion<LogData> {
  final Value<int> id;
  final Value<int?> type;
  final Value<String?> body;
  final Value<double?> createTime;
  const LogCompanion({
    this.id = const Value.absent(),
    this.type = const Value.absent(),
    this.body = const Value.absent(),
    this.createTime = const Value.absent(),
  });
  LogCompanion.insert({
    this.id = const Value.absent(),
    this.type = const Value.absent(),
    this.body = const Value.absent(),
    this.createTime = const Value.absent(),
  });
  static Insertable<LogData> custom({
    Expression<int>? id,
    Expression<int>? type,
    Expression<String>? body,
    Expression<double>? createTime,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (type != null) 'type': type,
      if (body != null) 'body': body,
      if (createTime != null) 'create_time': createTime,
    });
  }

  LogCompanion copyWith(
      {Value<int>? id,
      Value<int?>? type,
      Value<String?>? body,
      Value<double?>? createTime}) {
    return LogCompanion(
      id: id ?? this.id,
      type: type ?? this.type,
      body: body ?? this.body,
      createTime: createTime ?? this.createTime,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<int>(id.value);
    }
    if (type.present) {
      map['type'] = Variable<int>(type.value);
    }
    if (body.present) {
      map['body'] = Variable<String>(body.value);
    }
    if (createTime.present) {
      map['create_time'] = Variable<double>(createTime.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('LogCompanion(')
          ..write('id: $id, ')
          ..write('type: $type, ')
          ..write('body: $body, ')
          ..write('createTime: $createTime')
          ..write(')'))
        .toString();
  }
}

class Message extends Table with TableInfo<Message, MessageData> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  Message(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  late final GeneratedColumn<int> id = GeneratedColumn<int>(
      'id', aliasedName, false,
      hasAutoIncrement: true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      $customConstraints: 'NOT NULL PRIMARY KEY AUTOINCREMENT');
  static const VerificationMeta _ownerMeta = const VerificationMeta('owner');
  late final GeneratedColumn<String> owner = GeneratedColumn<String>(
      'owner', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _fromMeta = const VerificationMeta('from');
  late final GeneratedColumn<String> from = GeneratedColumn<String>(
      'from', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _bodyMeta = const VerificationMeta('body');
  late final GeneratedColumn<String> body = GeneratedColumn<String>(
      'body', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _filePathMeta =
      const VerificationMeta('filePath');
  late final GeneratedColumn<String> filePath = GeneratedColumn<String>(
      'file_path', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _fileUrlMeta =
      const VerificationMeta('fileUrl');
  late final GeneratedColumn<String> fileUrl = GeneratedColumn<String>(
      'file_url', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _fileFragmentMeta =
      const VerificationMeta('fileFragment');
  late final GeneratedColumn<String> fileFragment = GeneratedColumn<String>(
      'file_fragment', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _thumbnailPathMeta =
      const VerificationMeta('thumbnailPath');
  late final GeneratedColumn<String> thumbnailPath = GeneratedColumn<String>(
      'thumbnail_path', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _thumbnailUrlMeta =
      const VerificationMeta('thumbnailUrl');
  late final GeneratedColumn<String> thumbnailUrl = GeneratedColumn<String>(
      'thumbnail_url', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _thumbnailFragmentMeta =
      const VerificationMeta('thumbnailFragment');
  late final GeneratedColumn<String> thumbnailFragment =
      GeneratedColumn<String>('thumbnail_fragment', aliasedName, true,
          type: DriftSqlType.string,
          requiredDuringInsert: false,
          $customConstraints: '');
  static const VerificationMeta _selfDestructMeta =
      const VerificationMeta('selfDestruct');
  late final GeneratedColumn<bool> selfDestruct = GeneratedColumn<bool>(
      'self_destruct', aliasedName, true,
      type: DriftSqlType.bool,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _undoMeta = const VerificationMeta('undo');
  late final GeneratedColumn<bool> undo = GeneratedColumn<bool>(
      'undo', aliasedName, true,
      type: DriftSqlType.bool,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _undoEditMeta =
      const VerificationMeta('undoEdit');
  late final GeneratedColumn<bool> undoEdit = GeneratedColumn<bool>(
      'undo_edit', aliasedName, true,
      type: DriftSqlType.bool,
      requiredDuringInsert: false,
      $customConstraints: 'DEFAULT FALSE',
      defaultValue: const CustomExpression('FALSE'));
  static const VerificationMeta _timeMeta = const VerificationMeta('time');
  late final GeneratedColumn<int> time = GeneratedColumn<int>(
      'time', aliasedName, true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _typeMeta = const VerificationMeta('type');
  late final GeneratedColumn<int> type = GeneratedColumn<int>(
      'type', aliasedName, true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _chatTypeMeta =
      const VerificationMeta('chatType');
  late final GeneratedColumn<int> chatType = GeneratedColumn<int>(
      'chat_type', aliasedName, true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _callStateMeta =
      const VerificationMeta('callState');
  late final GeneratedColumn<int> callState = GeneratedColumn<int>(
      'call_state', aliasedName, true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _stateMeta = const VerificationMeta('state');
  late final GeneratedColumn<int> state = GeneratedColumn<int>(
      'state', aliasedName, true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _directionMeta =
      const VerificationMeta('direction');
  late final GeneratedColumn<int> direction = GeneratedColumn<int>(
      'direction', aliasedName, true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _fileStateMeta =
      const VerificationMeta('fileState');
  late final GeneratedColumn<int> fileState = GeneratedColumn<int>(
      'file_state', aliasedName, true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _thumbnailFileStateMeta =
      const VerificationMeta('thumbnailFileState');
  late final GeneratedColumn<int> thumbnailFileState = GeneratedColumn<int>(
      'thumbnail_file_state', aliasedName, true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _msgIdMeta = const VerificationMeta('msgId');
  late final GeneratedColumn<String> msgId = GeneratedColumn<String>(
      'msg_id', aliasedName, false,
      type: DriftSqlType.string,
      requiredDuringInsert: true,
      $customConstraints: 'NOT NULL UNIQUE');
  static const VerificationMeta _uuidMeta = const VerificationMeta('uuid');
  late final GeneratedColumn<String> uuid = GeneratedColumn<String>(
      'uuid', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _fileNameMeta =
      const VerificationMeta('fileName');
  late final GeneratedColumn<String> fileName = GeneratedColumn<String>(
      'file_name', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _fileSizeMeta =
      const VerificationMeta('fileSize');
  late final GeneratedColumn<int> fileSize = GeneratedColumn<int>(
      'fileSize', aliasedName, true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _ext1Meta = const VerificationMeta('ext1');
  late final GeneratedColumn<String> ext1 = GeneratedColumn<String>(
      'ext1', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _ackNumberMeta =
      const VerificationMeta('ackNumber');
  late final GeneratedColumn<int> ackNumber = GeneratedColumn<int>(
      'ack_number', aliasedName, true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _readMeta = const VerificationMeta('read');
  late final GeneratedColumn<bool> read = GeneratedColumn<bool>(
      'read', aliasedName, true,
      type: DriftSqlType.bool,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _replayMsgMeta =
      const VerificationMeta('replayMsg');
  late final GeneratedColumn<String> replayMsg = GeneratedColumn<String>(
      'replay_msg', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _margeMsgMeta =
      const VerificationMeta('margeMsg');
  late final GeneratedColumn<String> margeMsg = GeneratedColumn<String>(
      'marge_msg', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _contactMsgMeta =
      const VerificationMeta('contactMsg');
  late final GeneratedColumn<String> contactMsg = GeneratedColumn<String>(
      'contact_msg', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _shareMsgMeta =
      const VerificationMeta('shareMsg');
  late final GeneratedColumn<String> shareMsg = GeneratedColumn<String>(
      'share_msg', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _createTimeMeta =
      const VerificationMeta('createTime');
  late final GeneratedColumn<double> createTime = GeneratedColumn<double>(
      'create_time', aliasedName, true,
      type: DriftSqlType.double,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _atMeta = const VerificationMeta('at');
  late final GeneratedColumn<String> at = GeneratedColumn<String>(
      'at', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _updateTimeMeta =
      const VerificationMeta('updateTime');
  late final GeneratedColumn<double> updateTime = GeneratedColumn<double>(
      'update_time', aliasedName, true,
      type: DriftSqlType.double,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _resourceUuidMeta =
      const VerificationMeta('resourceUuid');
  late final GeneratedColumn<String> resourceUuid = GeneratedColumn<String>(
      'resource_uuid', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _hasShownMeta =
      const VerificationMeta('hasShown');
  late final GeneratedColumn<bool> hasShown = GeneratedColumn<bool>(
      'has_shown', aliasedName, true,
      type: DriftSqlType.bool,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _hasIdentifyMeta =
      const VerificationMeta('hasIdentify');
  late final GeneratedColumn<bool> hasIdentify = GeneratedColumn<bool>(
      'has_identify', aliasedName, true,
      type: DriftSqlType.bool,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _messageHasReadMeta =
      const VerificationMeta('messageHasRead');
  late final GeneratedColumn<bool> messageHasRead = GeneratedColumn<bool>(
      'message_has_read', aliasedName, true,
      type: DriftSqlType.bool,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _fileDurationMeta =
      const VerificationMeta('fileDuration');
  late final GeneratedColumn<int> fileDuration = GeneratedColumn<int>(
      'file_duration', aliasedName, true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _userNameFileHelperMeta =
      const VerificationMeta('userNameFileHelper');
  late final GeneratedColumn<String> userNameFileHelper =
      GeneratedColumn<String>('user_name_file_helper', aliasedName, true,
          type: DriftSqlType.string,
          requiredDuringInsert: false,
          $customConstraints: '');
  static const VerificationMeta _noisesMeta = const VerificationMeta('noises');
  late final GeneratedColumn<String> noises = GeneratedColumn<String>(
      'noises', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _displayNameMeta =
      const VerificationMeta('displayName');
  late final GeneratedColumn<String> displayName = GeneratedColumn<String>(
      'display_name', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _translateMsgMeta =
      const VerificationMeta('translateMsg');
  late final GeneratedColumn<String> translateMsg = GeneratedColumn<String>(
      'translate_msg', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _expandMeta = const VerificationMeta('expand');
  late final GeneratedColumn<String> expand = GeneratedColumn<String>(
      'expand', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  @override
  List<GeneratedColumn> get $columns => [
        id,
        owner,
        from,
        body,
        filePath,
        fileUrl,
        fileFragment,
        thumbnailPath,
        thumbnailUrl,
        thumbnailFragment,
        selfDestruct,
        undo,
        undoEdit,
        time,
        type,
        chatType,
        callState,
        state,
        direction,
        fileState,
        thumbnailFileState,
        msgId,
        uuid,
        fileName,
        fileSize,
        ext1,
        ackNumber,
        read,
        replayMsg,
        margeMsg,
        contactMsg,
        shareMsg,
        createTime,
        at,
        updateTime,
        resourceUuid,
        hasShown,
        hasIdentify,
        messageHasRead,
        fileDuration,
        userNameFileHelper,
        noises,
        displayName,
        translateMsg,
        expand
      ];
  @override
  String get aliasedName => _alias ?? 'message';
  @override
  String get actualTableName => 'message';
  @override
  VerificationContext validateIntegrity(Insertable<MessageData> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    }
    if (data.containsKey('owner')) {
      context.handle(
          _ownerMeta, owner.isAcceptableOrUnknown(data['owner']!, _ownerMeta));
    }
    if (data.containsKey('from')) {
      context.handle(
          _fromMeta, from.isAcceptableOrUnknown(data['from']!, _fromMeta));
    }
    if (data.containsKey('body')) {
      context.handle(
          _bodyMeta, body.isAcceptableOrUnknown(data['body']!, _bodyMeta));
    }
    if (data.containsKey('file_path')) {
      context.handle(_filePathMeta,
          filePath.isAcceptableOrUnknown(data['file_path']!, _filePathMeta));
    }
    if (data.containsKey('file_url')) {
      context.handle(_fileUrlMeta,
          fileUrl.isAcceptableOrUnknown(data['file_url']!, _fileUrlMeta));
    }
    if (data.containsKey('file_fragment')) {
      context.handle(
          _fileFragmentMeta,
          fileFragment.isAcceptableOrUnknown(
              data['file_fragment']!, _fileFragmentMeta));
    }
    if (data.containsKey('thumbnail_path')) {
      context.handle(
          _thumbnailPathMeta,
          thumbnailPath.isAcceptableOrUnknown(
              data['thumbnail_path']!, _thumbnailPathMeta));
    }
    if (data.containsKey('thumbnail_url')) {
      context.handle(
          _thumbnailUrlMeta,
          thumbnailUrl.isAcceptableOrUnknown(
              data['thumbnail_url']!, _thumbnailUrlMeta));
    }
    if (data.containsKey('thumbnail_fragment')) {
      context.handle(
          _thumbnailFragmentMeta,
          thumbnailFragment.isAcceptableOrUnknown(
              data['thumbnail_fragment']!, _thumbnailFragmentMeta));
    }
    if (data.containsKey('self_destruct')) {
      context.handle(
          _selfDestructMeta,
          selfDestruct.isAcceptableOrUnknown(
              data['self_destruct']!, _selfDestructMeta));
    }
    if (data.containsKey('undo')) {
      context.handle(
          _undoMeta, undo.isAcceptableOrUnknown(data['undo']!, _undoMeta));
    }
    if (data.containsKey('undo_edit')) {
      context.handle(_undoEditMeta,
          undoEdit.isAcceptableOrUnknown(data['undo_edit']!, _undoEditMeta));
    }
    if (data.containsKey('time')) {
      context.handle(
          _timeMeta, time.isAcceptableOrUnknown(data['time']!, _timeMeta));
    }
    if (data.containsKey('type')) {
      context.handle(
          _typeMeta, type.isAcceptableOrUnknown(data['type']!, _typeMeta));
    }
    if (data.containsKey('chat_type')) {
      context.handle(_chatTypeMeta,
          chatType.isAcceptableOrUnknown(data['chat_type']!, _chatTypeMeta));
    }
    if (data.containsKey('call_state')) {
      context.handle(_callStateMeta,
          callState.isAcceptableOrUnknown(data['call_state']!, _callStateMeta));
    }
    if (data.containsKey('state')) {
      context.handle(
          _stateMeta, state.isAcceptableOrUnknown(data['state']!, _stateMeta));
    }
    if (data.containsKey('direction')) {
      context.handle(_directionMeta,
          direction.isAcceptableOrUnknown(data['direction']!, _directionMeta));
    }
    if (data.containsKey('file_state')) {
      context.handle(_fileStateMeta,
          fileState.isAcceptableOrUnknown(data['file_state']!, _fileStateMeta));
    }
    if (data.containsKey('thumbnail_file_state')) {
      context.handle(
          _thumbnailFileStateMeta,
          thumbnailFileState.isAcceptableOrUnknown(
              data['thumbnail_file_state']!, _thumbnailFileStateMeta));
    }
    if (data.containsKey('msg_id')) {
      context.handle(
          _msgIdMeta, msgId.isAcceptableOrUnknown(data['msg_id']!, _msgIdMeta));
    } else if (isInserting) {
      context.missing(_msgIdMeta);
    }
    if (data.containsKey('uuid')) {
      context.handle(
          _uuidMeta, uuid.isAcceptableOrUnknown(data['uuid']!, _uuidMeta));
    }
    if (data.containsKey('file_name')) {
      context.handle(_fileNameMeta,
          fileName.isAcceptableOrUnknown(data['file_name']!, _fileNameMeta));
    }
    if (data.containsKey('fileSize')) {
      context.handle(_fileSizeMeta,
          fileSize.isAcceptableOrUnknown(data['fileSize']!, _fileSizeMeta));
    }
    if (data.containsKey('ext1')) {
      context.handle(
          _ext1Meta, ext1.isAcceptableOrUnknown(data['ext1']!, _ext1Meta));
    }
    if (data.containsKey('ack_number')) {
      context.handle(_ackNumberMeta,
          ackNumber.isAcceptableOrUnknown(data['ack_number']!, _ackNumberMeta));
    }
    if (data.containsKey('read')) {
      context.handle(
          _readMeta, read.isAcceptableOrUnknown(data['read']!, _readMeta));
    }
    if (data.containsKey('replay_msg')) {
      context.handle(_replayMsgMeta,
          replayMsg.isAcceptableOrUnknown(data['replay_msg']!, _replayMsgMeta));
    }
    if (data.containsKey('marge_msg')) {
      context.handle(_margeMsgMeta,
          margeMsg.isAcceptableOrUnknown(data['marge_msg']!, _margeMsgMeta));
    }
    if (data.containsKey('contact_msg')) {
      context.handle(
          _contactMsgMeta,
          contactMsg.isAcceptableOrUnknown(
              data['contact_msg']!, _contactMsgMeta));
    }
    if (data.containsKey('share_msg')) {
      context.handle(_shareMsgMeta,
          shareMsg.isAcceptableOrUnknown(data['share_msg']!, _shareMsgMeta));
    }
    if (data.containsKey('create_time')) {
      context.handle(
          _createTimeMeta,
          createTime.isAcceptableOrUnknown(
              data['create_time']!, _createTimeMeta));
    }
    if (data.containsKey('at')) {
      context.handle(_atMeta, at.isAcceptableOrUnknown(data['at']!, _atMeta));
    }
    if (data.containsKey('update_time')) {
      context.handle(
          _updateTimeMeta,
          updateTime.isAcceptableOrUnknown(
              data['update_time']!, _updateTimeMeta));
    }
    if (data.containsKey('resource_uuid')) {
      context.handle(
          _resourceUuidMeta,
          resourceUuid.isAcceptableOrUnknown(
              data['resource_uuid']!, _resourceUuidMeta));
    }
    if (data.containsKey('has_shown')) {
      context.handle(_hasShownMeta,
          hasShown.isAcceptableOrUnknown(data['has_shown']!, _hasShownMeta));
    }
    if (data.containsKey('has_identify')) {
      context.handle(
          _hasIdentifyMeta,
          hasIdentify.isAcceptableOrUnknown(
              data['has_identify']!, _hasIdentifyMeta));
    }
    if (data.containsKey('message_has_read')) {
      context.handle(
          _messageHasReadMeta,
          messageHasRead.isAcceptableOrUnknown(
              data['message_has_read']!, _messageHasReadMeta));
    }
    if (data.containsKey('file_duration')) {
      context.handle(
          _fileDurationMeta,
          fileDuration.isAcceptableOrUnknown(
              data['file_duration']!, _fileDurationMeta));
    }
    if (data.containsKey('user_name_file_helper')) {
      context.handle(
          _userNameFileHelperMeta,
          userNameFileHelper.isAcceptableOrUnknown(
              data['user_name_file_helper']!, _userNameFileHelperMeta));
    }
    if (data.containsKey('noises')) {
      context.handle(_noisesMeta,
          noises.isAcceptableOrUnknown(data['noises']!, _noisesMeta));
    }
    if (data.containsKey('display_name')) {
      context.handle(
          _displayNameMeta,
          displayName.isAcceptableOrUnknown(
              data['display_name']!, _displayNameMeta));
    }
    if (data.containsKey('translate_msg')) {
      context.handle(
          _translateMsgMeta,
          translateMsg.isAcceptableOrUnknown(
              data['translate_msg']!, _translateMsgMeta));
    }
    if (data.containsKey('expand')) {
      context.handle(_expandMeta,
          expand.isAcceptableOrUnknown(data['expand']!, _expandMeta));
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  MessageData map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return MessageData(
      id: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}id'])!,
      owner: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}owner']),
      from: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}from']),
      body: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}body']),
      filePath: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}file_path']),
      fileUrl: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}file_url']),
      fileFragment: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}file_fragment']),
      thumbnailPath: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}thumbnail_path']),
      thumbnailUrl: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}thumbnail_url']),
      thumbnailFragment: attachedDatabase.typeMapping.read(
          DriftSqlType.string, data['${effectivePrefix}thumbnail_fragment']),
      selfDestruct: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}self_destruct']),
      undo: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}undo']),
      undoEdit: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}undo_edit']),
      time: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}time']),
      type: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}type']),
      chatType: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}chat_type']),
      callState: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}call_state']),
      state: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}state']),
      direction: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}direction']),
      fileState: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}file_state']),
      thumbnailFileState: attachedDatabase.typeMapping.read(
          DriftSqlType.int, data['${effectivePrefix}thumbnail_file_state']),
      msgId: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}msg_id'])!,
      uuid: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}uuid']),
      fileName: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}file_name']),
      fileSize: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}fileSize']),
      ext1: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}ext1']),
      ackNumber: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}ack_number']),
      read: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}read']),
      replayMsg: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}replay_msg']),
      margeMsg: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}marge_msg']),
      contactMsg: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}contact_msg']),
      shareMsg: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}share_msg']),
      createTime: attachedDatabase.typeMapping
          .read(DriftSqlType.double, data['${effectivePrefix}create_time']),
      at: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}at']),
      updateTime: attachedDatabase.typeMapping
          .read(DriftSqlType.double, data['${effectivePrefix}update_time']),
      resourceUuid: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}resource_uuid']),
      hasShown: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}has_shown']),
      hasIdentify: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}has_identify']),
      messageHasRead: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}message_has_read']),
      fileDuration: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}file_duration']),
      userNameFileHelper: attachedDatabase.typeMapping.read(
          DriftSqlType.string, data['${effectivePrefix}user_name_file_helper']),
      noises: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}noises']),
      displayName: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}display_name']),
      translateMsg: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}translate_msg']),
      expand: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}expand']),
    );
  }

  @override
  Message createAlias(String alias) {
    return Message(attachedDatabase, alias);
  }

  @override
  bool get dontWriteConstraints => true;
}

class MessageData extends DataClass implements Insertable<MessageData> {
  final int id;
  final String? owner;
  final String? from;
  final String? body;
  final String? filePath;
  final String? fileUrl;
  final String? fileFragment;
  final String? thumbnailPath;
  final String? thumbnailUrl;
  final String? thumbnailFragment;
  final bool? selfDestruct;
  final bool? undo;
  final bool? undoEdit;
  final int? time;
  final int? type;
  final int? chatType;
  final int? callState;
  final int? state;
  final int? direction;
  final int? fileState;
  final int? thumbnailFileState;
  final String msgId;
  final String? uuid;
  final String? fileName;
  final int? fileSize;
  final String? ext1;
  final int? ackNumber;
  final bool? read;
  final String? replayMsg;
  final String? margeMsg;
  final String? contactMsg;
  final String? shareMsg;
  final double? createTime;
  final String? at;
  final double? updateTime;
  final String? resourceUuid;
  final bool? hasShown;
  final bool? hasIdentify;
  final bool? messageHasRead;
  final int? fileDuration;
  final String? userNameFileHelper;
  final String? noises;
  final String? displayName;
  final String? translateMsg;
  final String? expand;
  const MessageData(
      {required this.id,
      this.owner,
      this.from,
      this.body,
      this.filePath,
      this.fileUrl,
      this.fileFragment,
      this.thumbnailPath,
      this.thumbnailUrl,
      this.thumbnailFragment,
      this.selfDestruct,
      this.undo,
      this.undoEdit,
      this.time,
      this.type,
      this.chatType,
      this.callState,
      this.state,
      this.direction,
      this.fileState,
      this.thumbnailFileState,
      required this.msgId,
      this.uuid,
      this.fileName,
      this.fileSize,
      this.ext1,
      this.ackNumber,
      this.read,
      this.replayMsg,
      this.margeMsg,
      this.contactMsg,
      this.shareMsg,
      this.createTime,
      this.at,
      this.updateTime,
      this.resourceUuid,
      this.hasShown,
      this.hasIdentify,
      this.messageHasRead,
      this.fileDuration,
      this.userNameFileHelper,
      this.noises,
      this.displayName,
      this.translateMsg,
      this.expand});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<int>(id);
    if (!nullToAbsent || owner != null) {
      map['owner'] = Variable<String>(owner);
    }
    if (!nullToAbsent || from != null) {
      map['from'] = Variable<String>(from);
    }
    if (!nullToAbsent || body != null) {
      map['body'] = Variable<String>(body);
    }
    if (!nullToAbsent || filePath != null) {
      map['file_path'] = Variable<String>(filePath);
    }
    if (!nullToAbsent || fileUrl != null) {
      map['file_url'] = Variable<String>(fileUrl);
    }
    if (!nullToAbsent || fileFragment != null) {
      map['file_fragment'] = Variable<String>(fileFragment);
    }
    if (!nullToAbsent || thumbnailPath != null) {
      map['thumbnail_path'] = Variable<String>(thumbnailPath);
    }
    if (!nullToAbsent || thumbnailUrl != null) {
      map['thumbnail_url'] = Variable<String>(thumbnailUrl);
    }
    if (!nullToAbsent || thumbnailFragment != null) {
      map['thumbnail_fragment'] = Variable<String>(thumbnailFragment);
    }
    if (!nullToAbsent || selfDestruct != null) {
      map['self_destruct'] = Variable<bool>(selfDestruct);
    }
    if (!nullToAbsent || undo != null) {
      map['undo'] = Variable<bool>(undo);
    }
    if (!nullToAbsent || undoEdit != null) {
      map['undo_edit'] = Variable<bool>(undoEdit);
    }
    if (!nullToAbsent || time != null) {
      map['time'] = Variable<int>(time);
    }
    if (!nullToAbsent || type != null) {
      map['type'] = Variable<int>(type);
    }
    if (!nullToAbsent || chatType != null) {
      map['chat_type'] = Variable<int>(chatType);
    }
    if (!nullToAbsent || callState != null) {
      map['call_state'] = Variable<int>(callState);
    }
    if (!nullToAbsent || state != null) {
      map['state'] = Variable<int>(state);
    }
    if (!nullToAbsent || direction != null) {
      map['direction'] = Variable<int>(direction);
    }
    if (!nullToAbsent || fileState != null) {
      map['file_state'] = Variable<int>(fileState);
    }
    if (!nullToAbsent || thumbnailFileState != null) {
      map['thumbnail_file_state'] = Variable<int>(thumbnailFileState);
    }
    map['msg_id'] = Variable<String>(msgId);
    if (!nullToAbsent || uuid != null) {
      map['uuid'] = Variable<String>(uuid);
    }
    if (!nullToAbsent || fileName != null) {
      map['file_name'] = Variable<String>(fileName);
    }
    if (!nullToAbsent || fileSize != null) {
      map['fileSize'] = Variable<int>(fileSize);
    }
    if (!nullToAbsent || ext1 != null) {
      map['ext1'] = Variable<String>(ext1);
    }
    if (!nullToAbsent || ackNumber != null) {
      map['ack_number'] = Variable<int>(ackNumber);
    }
    if (!nullToAbsent || read != null) {
      map['read'] = Variable<bool>(read);
    }
    if (!nullToAbsent || replayMsg != null) {
      map['replay_msg'] = Variable<String>(replayMsg);
    }
    if (!nullToAbsent || margeMsg != null) {
      map['marge_msg'] = Variable<String>(margeMsg);
    }
    if (!nullToAbsent || contactMsg != null) {
      map['contact_msg'] = Variable<String>(contactMsg);
    }
    if (!nullToAbsent || shareMsg != null) {
      map['share_msg'] = Variable<String>(shareMsg);
    }
    if (!nullToAbsent || createTime != null) {
      map['create_time'] = Variable<double>(createTime);
    }
    if (!nullToAbsent || at != null) {
      map['at'] = Variable<String>(at);
    }
    if (!nullToAbsent || updateTime != null) {
      map['update_time'] = Variable<double>(updateTime);
    }
    if (!nullToAbsent || resourceUuid != null) {
      map['resource_uuid'] = Variable<String>(resourceUuid);
    }
    if (!nullToAbsent || hasShown != null) {
      map['has_shown'] = Variable<bool>(hasShown);
    }
    if (!nullToAbsent || hasIdentify != null) {
      map['has_identify'] = Variable<bool>(hasIdentify);
    }
    if (!nullToAbsent || messageHasRead != null) {
      map['message_has_read'] = Variable<bool>(messageHasRead);
    }
    if (!nullToAbsent || fileDuration != null) {
      map['file_duration'] = Variable<int>(fileDuration);
    }
    if (!nullToAbsent || userNameFileHelper != null) {
      map['user_name_file_helper'] = Variable<String>(userNameFileHelper);
    }
    if (!nullToAbsent || noises != null) {
      map['noises'] = Variable<String>(noises);
    }
    if (!nullToAbsent || displayName != null) {
      map['display_name'] = Variable<String>(displayName);
    }
    if (!nullToAbsent || translateMsg != null) {
      map['translate_msg'] = Variable<String>(translateMsg);
    }
    if (!nullToAbsent || expand != null) {
      map['expand'] = Variable<String>(expand);
    }
    return map;
  }

  MessageCompanion toCompanion(bool nullToAbsent) {
    return MessageCompanion(
      id: Value(id),
      owner:
          owner == null && nullToAbsent ? const Value.absent() : Value(owner),
      from: from == null && nullToAbsent ? const Value.absent() : Value(from),
      body: body == null && nullToAbsent ? const Value.absent() : Value(body),
      filePath: filePath == null && nullToAbsent
          ? const Value.absent()
          : Value(filePath),
      fileUrl: fileUrl == null && nullToAbsent
          ? const Value.absent()
          : Value(fileUrl),
      fileFragment: fileFragment == null && nullToAbsent
          ? const Value.absent()
          : Value(fileFragment),
      thumbnailPath: thumbnailPath == null && nullToAbsent
          ? const Value.absent()
          : Value(thumbnailPath),
      thumbnailUrl: thumbnailUrl == null && nullToAbsent
          ? const Value.absent()
          : Value(thumbnailUrl),
      thumbnailFragment: thumbnailFragment == null && nullToAbsent
          ? const Value.absent()
          : Value(thumbnailFragment),
      selfDestruct: selfDestruct == null && nullToAbsent
          ? const Value.absent()
          : Value(selfDestruct),
      undo: undo == null && nullToAbsent ? const Value.absent() : Value(undo),
      undoEdit: undoEdit == null && nullToAbsent
          ? const Value.absent()
          : Value(undoEdit),
      time: time == null && nullToAbsent ? const Value.absent() : Value(time),
      type: type == null && nullToAbsent ? const Value.absent() : Value(type),
      chatType: chatType == null && nullToAbsent
          ? const Value.absent()
          : Value(chatType),
      callState: callState == null && nullToAbsent
          ? const Value.absent()
          : Value(callState),
      state:
          state == null && nullToAbsent ? const Value.absent() : Value(state),
      direction: direction == null && nullToAbsent
          ? const Value.absent()
          : Value(direction),
      fileState: fileState == null && nullToAbsent
          ? const Value.absent()
          : Value(fileState),
      thumbnailFileState: thumbnailFileState == null && nullToAbsent
          ? const Value.absent()
          : Value(thumbnailFileState),
      msgId: Value(msgId),
      uuid: uuid == null && nullToAbsent ? const Value.absent() : Value(uuid),
      fileName: fileName == null && nullToAbsent
          ? const Value.absent()
          : Value(fileName),
      fileSize: fileSize == null && nullToAbsent
          ? const Value.absent()
          : Value(fileSize),
      ext1: ext1 == null && nullToAbsent ? const Value.absent() : Value(ext1),
      ackNumber: ackNumber == null && nullToAbsent
          ? const Value.absent()
          : Value(ackNumber),
      read: read == null && nullToAbsent ? const Value.absent() : Value(read),
      replayMsg: replayMsg == null && nullToAbsent
          ? const Value.absent()
          : Value(replayMsg),
      margeMsg: margeMsg == null && nullToAbsent
          ? const Value.absent()
          : Value(margeMsg),
      contactMsg: contactMsg == null && nullToAbsent
          ? const Value.absent()
          : Value(contactMsg),
      shareMsg: shareMsg == null && nullToAbsent
          ? const Value.absent()
          : Value(shareMsg),
      createTime: createTime == null && nullToAbsent
          ? const Value.absent()
          : Value(createTime),
      at: at == null && nullToAbsent ? const Value.absent() : Value(at),
      updateTime: updateTime == null && nullToAbsent
          ? const Value.absent()
          : Value(updateTime),
      resourceUuid: resourceUuid == null && nullToAbsent
          ? const Value.absent()
          : Value(resourceUuid),
      hasShown: hasShown == null && nullToAbsent
          ? const Value.absent()
          : Value(hasShown),
      hasIdentify: hasIdentify == null && nullToAbsent
          ? const Value.absent()
          : Value(hasIdentify),
      messageHasRead: messageHasRead == null && nullToAbsent
          ? const Value.absent()
          : Value(messageHasRead),
      fileDuration: fileDuration == null && nullToAbsent
          ? const Value.absent()
          : Value(fileDuration),
      userNameFileHelper: userNameFileHelper == null && nullToAbsent
          ? const Value.absent()
          : Value(userNameFileHelper),
      noises:
          noises == null && nullToAbsent ? const Value.absent() : Value(noises),
      displayName: displayName == null && nullToAbsent
          ? const Value.absent()
          : Value(displayName),
      translateMsg: translateMsg == null && nullToAbsent
          ? const Value.absent()
          : Value(translateMsg),
      expand:
          expand == null && nullToAbsent ? const Value.absent() : Value(expand),
    );
  }

  factory MessageData.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return MessageData(
      id: serializer.fromJson<int>(json['id']),
      owner: serializer.fromJson<String?>(json['owner']),
      from: serializer.fromJson<String?>(json['from']),
      body: serializer.fromJson<String?>(json['body']),
      filePath: serializer.fromJson<String?>(json['file_path']),
      fileUrl: serializer.fromJson<String?>(json['file_url']),
      fileFragment: serializer.fromJson<String?>(json['file_fragment']),
      thumbnailPath: serializer.fromJson<String?>(json['thumbnail_path']),
      thumbnailUrl: serializer.fromJson<String?>(json['thumbnail_url']),
      thumbnailFragment:
          serializer.fromJson<String?>(json['thumbnail_fragment']),
      selfDestruct: serializer.fromJson<bool?>(json['self_destruct']),
      undo: serializer.fromJson<bool?>(json['undo']),
      undoEdit: serializer.fromJson<bool?>(json['undo_edit']),
      time: serializer.fromJson<int?>(json['time']),
      type: serializer.fromJson<int?>(json['type']),
      chatType: serializer.fromJson<int?>(json['chat_type']),
      callState: serializer.fromJson<int?>(json['call_state']),
      state: serializer.fromJson<int?>(json['state']),
      direction: serializer.fromJson<int?>(json['direction']),
      fileState: serializer.fromJson<int?>(json['file_state']),
      thumbnailFileState:
          serializer.fromJson<int?>(json['thumbnail_file_state']),
      msgId: serializer.fromJson<String>(json['msg_id']),
      uuid: serializer.fromJson<String?>(json['uuid']),
      fileName: serializer.fromJson<String?>(json['file_name']),
      fileSize: serializer.fromJson<int?>(json['fileSize']),
      ext1: serializer.fromJson<String?>(json['ext1']),
      ackNumber: serializer.fromJson<int?>(json['ack_number']),
      read: serializer.fromJson<bool?>(json['read']),
      replayMsg: serializer.fromJson<String?>(json['replay_msg']),
      margeMsg: serializer.fromJson<String?>(json['marge_msg']),
      contactMsg: serializer.fromJson<String?>(json['contact_msg']),
      shareMsg: serializer.fromJson<String?>(json['share_msg']),
      createTime: serializer.fromJson<double?>(json['create_time']),
      at: serializer.fromJson<String?>(json['at']),
      updateTime: serializer.fromJson<double?>(json['update_time']),
      resourceUuid: serializer.fromJson<String?>(json['resource_uuid']),
      hasShown: serializer.fromJson<bool?>(json['has_shown']),
      hasIdentify: serializer.fromJson<bool?>(json['has_identify']),
      messageHasRead: serializer.fromJson<bool?>(json['message_has_read']),
      fileDuration: serializer.fromJson<int?>(json['file_duration']),
      userNameFileHelper:
          serializer.fromJson<String?>(json['user_name_file_helper']),
      noises: serializer.fromJson<String?>(json['noises']),
      displayName: serializer.fromJson<String?>(json['display_name']),
      translateMsg: serializer.fromJson<String?>(json['translate_msg']),
      expand: serializer.fromJson<String?>(json['expand']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<int>(id),
      'owner': serializer.toJson<String?>(owner),
      'from': serializer.toJson<String?>(from),
      'body': serializer.toJson<String?>(body),
      'file_path': serializer.toJson<String?>(filePath),
      'file_url': serializer.toJson<String?>(fileUrl),
      'file_fragment': serializer.toJson<String?>(fileFragment),
      'thumbnail_path': serializer.toJson<String?>(thumbnailPath),
      'thumbnail_url': serializer.toJson<String?>(thumbnailUrl),
      'thumbnail_fragment': serializer.toJson<String?>(thumbnailFragment),
      'self_destruct': serializer.toJson<bool?>(selfDestruct),
      'undo': serializer.toJson<bool?>(undo),
      'undo_edit': serializer.toJson<bool?>(undoEdit),
      'time': serializer.toJson<int?>(time),
      'type': serializer.toJson<int?>(type),
      'chat_type': serializer.toJson<int?>(chatType),
      'call_state': serializer.toJson<int?>(callState),
      'state': serializer.toJson<int?>(state),
      'direction': serializer.toJson<int?>(direction),
      'file_state': serializer.toJson<int?>(fileState),
      'thumbnail_file_state': serializer.toJson<int?>(thumbnailFileState),
      'msg_id': serializer.toJson<String>(msgId),
      'uuid': serializer.toJson<String?>(uuid),
      'file_name': serializer.toJson<String?>(fileName),
      'fileSize': serializer.toJson<int?>(fileSize),
      'ext1': serializer.toJson<String?>(ext1),
      'ack_number': serializer.toJson<int?>(ackNumber),
      'read': serializer.toJson<bool?>(read),
      'replay_msg': serializer.toJson<String?>(replayMsg),
      'marge_msg': serializer.toJson<String?>(margeMsg),
      'contact_msg': serializer.toJson<String?>(contactMsg),
      'share_msg': serializer.toJson<String?>(shareMsg),
      'create_time': serializer.toJson<double?>(createTime),
      'at': serializer.toJson<String?>(at),
      'update_time': serializer.toJson<double?>(updateTime),
      'resource_uuid': serializer.toJson<String?>(resourceUuid),
      'has_shown': serializer.toJson<bool?>(hasShown),
      'has_identify': serializer.toJson<bool?>(hasIdentify),
      'message_has_read': serializer.toJson<bool?>(messageHasRead),
      'file_duration': serializer.toJson<int?>(fileDuration),
      'user_name_file_helper': serializer.toJson<String?>(userNameFileHelper),
      'noises': serializer.toJson<String?>(noises),
      'display_name': serializer.toJson<String?>(displayName),
      'translate_msg': serializer.toJson<String?>(translateMsg),
      'expand': serializer.toJson<String?>(expand),
    };
  }

  MessageData copyWith(
          {int? id,
          Value<String?> owner = const Value.absent(),
          Value<String?> from = const Value.absent(),
          Value<String?> body = const Value.absent(),
          Value<String?> filePath = const Value.absent(),
          Value<String?> fileUrl = const Value.absent(),
          Value<String?> fileFragment = const Value.absent(),
          Value<String?> thumbnailPath = const Value.absent(),
          Value<String?> thumbnailUrl = const Value.absent(),
          Value<String?> thumbnailFragment = const Value.absent(),
          Value<bool?> selfDestruct = const Value.absent(),
          Value<bool?> undo = const Value.absent(),
          Value<bool?> undoEdit = const Value.absent(),
          Value<int?> time = const Value.absent(),
          Value<int?> type = const Value.absent(),
          Value<int?> chatType = const Value.absent(),
          Value<int?> callState = const Value.absent(),
          Value<int?> state = const Value.absent(),
          Value<int?> direction = const Value.absent(),
          Value<int?> fileState = const Value.absent(),
          Value<int?> thumbnailFileState = const Value.absent(),
          String? msgId,
          Value<String?> uuid = const Value.absent(),
          Value<String?> fileName = const Value.absent(),
          Value<int?> fileSize = const Value.absent(),
          Value<String?> ext1 = const Value.absent(),
          Value<int?> ackNumber = const Value.absent(),
          Value<bool?> read = const Value.absent(),
          Value<String?> replayMsg = const Value.absent(),
          Value<String?> margeMsg = const Value.absent(),
          Value<String?> contactMsg = const Value.absent(),
          Value<String?> shareMsg = const Value.absent(),
          Value<double?> createTime = const Value.absent(),
          Value<String?> at = const Value.absent(),
          Value<double?> updateTime = const Value.absent(),
          Value<String?> resourceUuid = const Value.absent(),
          Value<bool?> hasShown = const Value.absent(),
          Value<bool?> hasIdentify = const Value.absent(),
          Value<bool?> messageHasRead = const Value.absent(),
          Value<int?> fileDuration = const Value.absent(),
          Value<String?> userNameFileHelper = const Value.absent(),
          Value<String?> noises = const Value.absent(),
          Value<String?> displayName = const Value.absent(),
          Value<String?> translateMsg = const Value.absent(),
          Value<String?> expand = const Value.absent()}) =>
      MessageData(
        id: id ?? this.id,
        owner: owner.present ? owner.value : this.owner,
        from: from.present ? from.value : this.from,
        body: body.present ? body.value : this.body,
        filePath: filePath.present ? filePath.value : this.filePath,
        fileUrl: fileUrl.present ? fileUrl.value : this.fileUrl,
        fileFragment:
            fileFragment.present ? fileFragment.value : this.fileFragment,
        thumbnailPath:
            thumbnailPath.present ? thumbnailPath.value : this.thumbnailPath,
        thumbnailUrl:
            thumbnailUrl.present ? thumbnailUrl.value : this.thumbnailUrl,
        thumbnailFragment: thumbnailFragment.present
            ? thumbnailFragment.value
            : this.thumbnailFragment,
        selfDestruct:
            selfDestruct.present ? selfDestruct.value : this.selfDestruct,
        undo: undo.present ? undo.value : this.undo,
        undoEdit: undoEdit.present ? undoEdit.value : this.undoEdit,
        time: time.present ? time.value : this.time,
        type: type.present ? type.value : this.type,
        chatType: chatType.present ? chatType.value : this.chatType,
        callState: callState.present ? callState.value : this.callState,
        state: state.present ? state.value : this.state,
        direction: direction.present ? direction.value : this.direction,
        fileState: fileState.present ? fileState.value : this.fileState,
        thumbnailFileState: thumbnailFileState.present
            ? thumbnailFileState.value
            : this.thumbnailFileState,
        msgId: msgId ?? this.msgId,
        uuid: uuid.present ? uuid.value : this.uuid,
        fileName: fileName.present ? fileName.value : this.fileName,
        fileSize: fileSize.present ? fileSize.value : this.fileSize,
        ext1: ext1.present ? ext1.value : this.ext1,
        ackNumber: ackNumber.present ? ackNumber.value : this.ackNumber,
        read: read.present ? read.value : this.read,
        replayMsg: replayMsg.present ? replayMsg.value : this.replayMsg,
        margeMsg: margeMsg.present ? margeMsg.value : this.margeMsg,
        contactMsg: contactMsg.present ? contactMsg.value : this.contactMsg,
        shareMsg: shareMsg.present ? shareMsg.value : this.shareMsg,
        createTime: createTime.present ? createTime.value : this.createTime,
        at: at.present ? at.value : this.at,
        updateTime: updateTime.present ? updateTime.value : this.updateTime,
        resourceUuid:
            resourceUuid.present ? resourceUuid.value : this.resourceUuid,
        hasShown: hasShown.present ? hasShown.value : this.hasShown,
        hasIdentify: hasIdentify.present ? hasIdentify.value : this.hasIdentify,
        messageHasRead:
            messageHasRead.present ? messageHasRead.value : this.messageHasRead,
        fileDuration:
            fileDuration.present ? fileDuration.value : this.fileDuration,
        userNameFileHelper: userNameFileHelper.present
            ? userNameFileHelper.value
            : this.userNameFileHelper,
        noises: noises.present ? noises.value : this.noises,
        displayName: displayName.present ? displayName.value : this.displayName,
        translateMsg:
            translateMsg.present ? translateMsg.value : this.translateMsg,
        expand: expand.present ? expand.value : this.expand,
      );
  @override
  String toString() {
    return (StringBuffer('MessageData(')
          ..write('id: $id, ')
          ..write('owner: $owner, ')
          ..write('from: $from, ')
          ..write('body: $body, ')
          ..write('filePath: $filePath, ')
          ..write('fileUrl: $fileUrl, ')
          ..write('fileFragment: $fileFragment, ')
          ..write('thumbnailPath: $thumbnailPath, ')
          ..write('thumbnailUrl: $thumbnailUrl, ')
          ..write('thumbnailFragment: $thumbnailFragment, ')
          ..write('selfDestruct: $selfDestruct, ')
          ..write('undo: $undo, ')
          ..write('undoEdit: $undoEdit, ')
          ..write('time: $time, ')
          ..write('type: $type, ')
          ..write('chatType: $chatType, ')
          ..write('callState: $callState, ')
          ..write('state: $state, ')
          ..write('direction: $direction, ')
          ..write('fileState: $fileState, ')
          ..write('thumbnailFileState: $thumbnailFileState, ')
          ..write('msgId: $msgId, ')
          ..write('uuid: $uuid, ')
          ..write('fileName: $fileName, ')
          ..write('fileSize: $fileSize, ')
          ..write('ext1: $ext1, ')
          ..write('ackNumber: $ackNumber, ')
          ..write('read: $read, ')
          ..write('replayMsg: $replayMsg, ')
          ..write('margeMsg: $margeMsg, ')
          ..write('contactMsg: $contactMsg, ')
          ..write('shareMsg: $shareMsg, ')
          ..write('createTime: $createTime, ')
          ..write('at: $at, ')
          ..write('updateTime: $updateTime, ')
          ..write('resourceUuid: $resourceUuid, ')
          ..write('hasShown: $hasShown, ')
          ..write('hasIdentify: $hasIdentify, ')
          ..write('messageHasRead: $messageHasRead, ')
          ..write('fileDuration: $fileDuration, ')
          ..write('userNameFileHelper: $userNameFileHelper, ')
          ..write('noises: $noises, ')
          ..write('displayName: $displayName, ')
          ..write('translateMsg: $translateMsg, ')
          ..write('expand: $expand')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hashAll([
        id,
        owner,
        from,
        body,
        filePath,
        fileUrl,
        fileFragment,
        thumbnailPath,
        thumbnailUrl,
        thumbnailFragment,
        selfDestruct,
        undo,
        undoEdit,
        time,
        type,
        chatType,
        callState,
        state,
        direction,
        fileState,
        thumbnailFileState,
        msgId,
        uuid,
        fileName,
        fileSize,
        ext1,
        ackNumber,
        read,
        replayMsg,
        margeMsg,
        contactMsg,
        shareMsg,
        createTime,
        at,
        updateTime,
        resourceUuid,
        hasShown,
        hasIdentify,
        messageHasRead,
        fileDuration,
        userNameFileHelper,
        noises,
        displayName,
        translateMsg,
        expand
      ]);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is MessageData &&
          other.id == this.id &&
          other.owner == this.owner &&
          other.from == this.from &&
          other.body == this.body &&
          other.filePath == this.filePath &&
          other.fileUrl == this.fileUrl &&
          other.fileFragment == this.fileFragment &&
          other.thumbnailPath == this.thumbnailPath &&
          other.thumbnailUrl == this.thumbnailUrl &&
          other.thumbnailFragment == this.thumbnailFragment &&
          other.selfDestruct == this.selfDestruct &&
          other.undo == this.undo &&
          other.undoEdit == this.undoEdit &&
          other.time == this.time &&
          other.type == this.type &&
          other.chatType == this.chatType &&
          other.callState == this.callState &&
          other.state == this.state &&
          other.direction == this.direction &&
          other.fileState == this.fileState &&
          other.thumbnailFileState == this.thumbnailFileState &&
          other.msgId == this.msgId &&
          other.uuid == this.uuid &&
          other.fileName == this.fileName &&
          other.fileSize == this.fileSize &&
          other.ext1 == this.ext1 &&
          other.ackNumber == this.ackNumber &&
          other.read == this.read &&
          other.replayMsg == this.replayMsg &&
          other.margeMsg == this.margeMsg &&
          other.contactMsg == this.contactMsg &&
          other.shareMsg == this.shareMsg &&
          other.createTime == this.createTime &&
          other.at == this.at &&
          other.updateTime == this.updateTime &&
          other.resourceUuid == this.resourceUuid &&
          other.hasShown == this.hasShown &&
          other.hasIdentify == this.hasIdentify &&
          other.messageHasRead == this.messageHasRead &&
          other.fileDuration == this.fileDuration &&
          other.userNameFileHelper == this.userNameFileHelper &&
          other.noises == this.noises &&
          other.displayName == this.displayName &&
          other.translateMsg == this.translateMsg &&
          other.expand == this.expand);
}

class MessageCompanion extends UpdateCompanion<MessageData> {
  final Value<int> id;
  final Value<String?> owner;
  final Value<String?> from;
  final Value<String?> body;
  final Value<String?> filePath;
  final Value<String?> fileUrl;
  final Value<String?> fileFragment;
  final Value<String?> thumbnailPath;
  final Value<String?> thumbnailUrl;
  final Value<String?> thumbnailFragment;
  final Value<bool?> selfDestruct;
  final Value<bool?> undo;
  final Value<bool?> undoEdit;
  final Value<int?> time;
  final Value<int?> type;
  final Value<int?> chatType;
  final Value<int?> callState;
  final Value<int?> state;
  final Value<int?> direction;
  final Value<int?> fileState;
  final Value<int?> thumbnailFileState;
  final Value<String> msgId;
  final Value<String?> uuid;
  final Value<String?> fileName;
  final Value<int?> fileSize;
  final Value<String?> ext1;
  final Value<int?> ackNumber;
  final Value<bool?> read;
  final Value<String?> replayMsg;
  final Value<String?> margeMsg;
  final Value<String?> contactMsg;
  final Value<String?> shareMsg;
  final Value<double?> createTime;
  final Value<String?> at;
  final Value<double?> updateTime;
  final Value<String?> resourceUuid;
  final Value<bool?> hasShown;
  final Value<bool?> hasIdentify;
  final Value<bool?> messageHasRead;
  final Value<int?> fileDuration;
  final Value<String?> userNameFileHelper;
  final Value<String?> noises;
  final Value<String?> displayName;
  final Value<String?> translateMsg;
  final Value<String?> expand;
  const MessageCompanion({
    this.id = const Value.absent(),
    this.owner = const Value.absent(),
    this.from = const Value.absent(),
    this.body = const Value.absent(),
    this.filePath = const Value.absent(),
    this.fileUrl = const Value.absent(),
    this.fileFragment = const Value.absent(),
    this.thumbnailPath = const Value.absent(),
    this.thumbnailUrl = const Value.absent(),
    this.thumbnailFragment = const Value.absent(),
    this.selfDestruct = const Value.absent(),
    this.undo = const Value.absent(),
    this.undoEdit = const Value.absent(),
    this.time = const Value.absent(),
    this.type = const Value.absent(),
    this.chatType = const Value.absent(),
    this.callState = const Value.absent(),
    this.state = const Value.absent(),
    this.direction = const Value.absent(),
    this.fileState = const Value.absent(),
    this.thumbnailFileState = const Value.absent(),
    this.msgId = const Value.absent(),
    this.uuid = const Value.absent(),
    this.fileName = const Value.absent(),
    this.fileSize = const Value.absent(),
    this.ext1 = const Value.absent(),
    this.ackNumber = const Value.absent(),
    this.read = const Value.absent(),
    this.replayMsg = const Value.absent(),
    this.margeMsg = const Value.absent(),
    this.contactMsg = const Value.absent(),
    this.shareMsg = const Value.absent(),
    this.createTime = const Value.absent(),
    this.at = const Value.absent(),
    this.updateTime = const Value.absent(),
    this.resourceUuid = const Value.absent(),
    this.hasShown = const Value.absent(),
    this.hasIdentify = const Value.absent(),
    this.messageHasRead = const Value.absent(),
    this.fileDuration = const Value.absent(),
    this.userNameFileHelper = const Value.absent(),
    this.noises = const Value.absent(),
    this.displayName = const Value.absent(),
    this.translateMsg = const Value.absent(),
    this.expand = const Value.absent(),
  });
  MessageCompanion.insert({
    this.id = const Value.absent(),
    this.owner = const Value.absent(),
    this.from = const Value.absent(),
    this.body = const Value.absent(),
    this.filePath = const Value.absent(),
    this.fileUrl = const Value.absent(),
    this.fileFragment = const Value.absent(),
    this.thumbnailPath = const Value.absent(),
    this.thumbnailUrl = const Value.absent(),
    this.thumbnailFragment = const Value.absent(),
    this.selfDestruct = const Value.absent(),
    this.undo = const Value.absent(),
    this.undoEdit = const Value.absent(),
    this.time = const Value.absent(),
    this.type = const Value.absent(),
    this.chatType = const Value.absent(),
    this.callState = const Value.absent(),
    this.state = const Value.absent(),
    this.direction = const Value.absent(),
    this.fileState = const Value.absent(),
    this.thumbnailFileState = const Value.absent(),
    required String msgId,
    this.uuid = const Value.absent(),
    this.fileName = const Value.absent(),
    this.fileSize = const Value.absent(),
    this.ext1 = const Value.absent(),
    this.ackNumber = const Value.absent(),
    this.read = const Value.absent(),
    this.replayMsg = const Value.absent(),
    this.margeMsg = const Value.absent(),
    this.contactMsg = const Value.absent(),
    this.shareMsg = const Value.absent(),
    this.createTime = const Value.absent(),
    this.at = const Value.absent(),
    this.updateTime = const Value.absent(),
    this.resourceUuid = const Value.absent(),
    this.hasShown = const Value.absent(),
    this.hasIdentify = const Value.absent(),
    this.messageHasRead = const Value.absent(),
    this.fileDuration = const Value.absent(),
    this.userNameFileHelper = const Value.absent(),
    this.noises = const Value.absent(),
    this.displayName = const Value.absent(),
    this.translateMsg = const Value.absent(),
    this.expand = const Value.absent(),
  }) : msgId = Value(msgId);
  static Insertable<MessageData> custom({
    Expression<int>? id,
    Expression<String>? owner,
    Expression<String>? from,
    Expression<String>? body,
    Expression<String>? filePath,
    Expression<String>? fileUrl,
    Expression<String>? fileFragment,
    Expression<String>? thumbnailPath,
    Expression<String>? thumbnailUrl,
    Expression<String>? thumbnailFragment,
    Expression<bool>? selfDestruct,
    Expression<bool>? undo,
    Expression<bool>? undoEdit,
    Expression<int>? time,
    Expression<int>? type,
    Expression<int>? chatType,
    Expression<int>? callState,
    Expression<int>? state,
    Expression<int>? direction,
    Expression<int>? fileState,
    Expression<int>? thumbnailFileState,
    Expression<String>? msgId,
    Expression<String>? uuid,
    Expression<String>? fileName,
    Expression<int>? fileSize,
    Expression<String>? ext1,
    Expression<int>? ackNumber,
    Expression<bool>? read,
    Expression<String>? replayMsg,
    Expression<String>? margeMsg,
    Expression<String>? contactMsg,
    Expression<String>? shareMsg,
    Expression<double>? createTime,
    Expression<String>? at,
    Expression<double>? updateTime,
    Expression<String>? resourceUuid,
    Expression<bool>? hasShown,
    Expression<bool>? hasIdentify,
    Expression<bool>? messageHasRead,
    Expression<int>? fileDuration,
    Expression<String>? userNameFileHelper,
    Expression<String>? noises,
    Expression<String>? displayName,
    Expression<String>? translateMsg,
    Expression<String>? expand,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (owner != null) 'owner': owner,
      if (from != null) 'from': from,
      if (body != null) 'body': body,
      if (filePath != null) 'file_path': filePath,
      if (fileUrl != null) 'file_url': fileUrl,
      if (fileFragment != null) 'file_fragment': fileFragment,
      if (thumbnailPath != null) 'thumbnail_path': thumbnailPath,
      if (thumbnailUrl != null) 'thumbnail_url': thumbnailUrl,
      if (thumbnailFragment != null) 'thumbnail_fragment': thumbnailFragment,
      if (selfDestruct != null) 'self_destruct': selfDestruct,
      if (undo != null) 'undo': undo,
      if (undoEdit != null) 'undo_edit': undoEdit,
      if (time != null) 'time': time,
      if (type != null) 'type': type,
      if (chatType != null) 'chat_type': chatType,
      if (callState != null) 'call_state': callState,
      if (state != null) 'state': state,
      if (direction != null) 'direction': direction,
      if (fileState != null) 'file_state': fileState,
      if (thumbnailFileState != null)
        'thumbnail_file_state': thumbnailFileState,
      if (msgId != null) 'msg_id': msgId,
      if (uuid != null) 'uuid': uuid,
      if (fileName != null) 'file_name': fileName,
      if (fileSize != null) 'fileSize': fileSize,
      if (ext1 != null) 'ext1': ext1,
      if (ackNumber != null) 'ack_number': ackNumber,
      if (read != null) 'read': read,
      if (replayMsg != null) 'replay_msg': replayMsg,
      if (margeMsg != null) 'marge_msg': margeMsg,
      if (contactMsg != null) 'contact_msg': contactMsg,
      if (shareMsg != null) 'share_msg': shareMsg,
      if (createTime != null) 'create_time': createTime,
      if (at != null) 'at': at,
      if (updateTime != null) 'update_time': updateTime,
      if (resourceUuid != null) 'resource_uuid': resourceUuid,
      if (hasShown != null) 'has_shown': hasShown,
      if (hasIdentify != null) 'has_identify': hasIdentify,
      if (messageHasRead != null) 'message_has_read': messageHasRead,
      if (fileDuration != null) 'file_duration': fileDuration,
      if (userNameFileHelper != null)
        'user_name_file_helper': userNameFileHelper,
      if (noises != null) 'noises': noises,
      if (displayName != null) 'display_name': displayName,
      if (translateMsg != null) 'translate_msg': translateMsg,
      if (expand != null) 'expand': expand,
    });
  }

  MessageCompanion copyWith(
      {Value<int>? id,
      Value<String?>? owner,
      Value<String?>? from,
      Value<String?>? body,
      Value<String?>? filePath,
      Value<String?>? fileUrl,
      Value<String?>? fileFragment,
      Value<String?>? thumbnailPath,
      Value<String?>? thumbnailUrl,
      Value<String?>? thumbnailFragment,
      Value<bool?>? selfDestruct,
      Value<bool?>? undo,
      Value<bool?>? undoEdit,
      Value<int?>? time,
      Value<int?>? type,
      Value<int?>? chatType,
      Value<int?>? callState,
      Value<int?>? state,
      Value<int?>? direction,
      Value<int?>? fileState,
      Value<int?>? thumbnailFileState,
      Value<String>? msgId,
      Value<String?>? uuid,
      Value<String?>? fileName,
      Value<int?>? fileSize,
      Value<String?>? ext1,
      Value<int?>? ackNumber,
      Value<bool?>? read,
      Value<String?>? replayMsg,
      Value<String?>? margeMsg,
      Value<String?>? contactMsg,
      Value<String?>? shareMsg,
      Value<double?>? createTime,
      Value<String?>? at,
      Value<double?>? updateTime,
      Value<String?>? resourceUuid,
      Value<bool?>? hasShown,
      Value<bool?>? hasIdentify,
      Value<bool?>? messageHasRead,
      Value<int?>? fileDuration,
      Value<String?>? userNameFileHelper,
      Value<String?>? noises,
      Value<String?>? displayName,
      Value<String?>? translateMsg,
      Value<String?>? expand}) {
    return MessageCompanion(
      id: id ?? this.id,
      owner: owner ?? this.owner,
      from: from ?? this.from,
      body: body ?? this.body,
      filePath: filePath ?? this.filePath,
      fileUrl: fileUrl ?? this.fileUrl,
      fileFragment: fileFragment ?? this.fileFragment,
      thumbnailPath: thumbnailPath ?? this.thumbnailPath,
      thumbnailUrl: thumbnailUrl ?? this.thumbnailUrl,
      thumbnailFragment: thumbnailFragment ?? this.thumbnailFragment,
      selfDestruct: selfDestruct ?? this.selfDestruct,
      undo: undo ?? this.undo,
      undoEdit: undoEdit ?? this.undoEdit,
      time: time ?? this.time,
      type: type ?? this.type,
      chatType: chatType ?? this.chatType,
      callState: callState ?? this.callState,
      state: state ?? this.state,
      direction: direction ?? this.direction,
      fileState: fileState ?? this.fileState,
      thumbnailFileState: thumbnailFileState ?? this.thumbnailFileState,
      msgId: msgId ?? this.msgId,
      uuid: uuid ?? this.uuid,
      fileName: fileName ?? this.fileName,
      fileSize: fileSize ?? this.fileSize,
      ext1: ext1 ?? this.ext1,
      ackNumber: ackNumber ?? this.ackNumber,
      read: read ?? this.read,
      replayMsg: replayMsg ?? this.replayMsg,
      margeMsg: margeMsg ?? this.margeMsg,
      contactMsg: contactMsg ?? this.contactMsg,
      shareMsg: shareMsg ?? this.shareMsg,
      createTime: createTime ?? this.createTime,
      at: at ?? this.at,
      updateTime: updateTime ?? this.updateTime,
      resourceUuid: resourceUuid ?? this.resourceUuid,
      hasShown: hasShown ?? this.hasShown,
      hasIdentify: hasIdentify ?? this.hasIdentify,
      messageHasRead: messageHasRead ?? this.messageHasRead,
      fileDuration: fileDuration ?? this.fileDuration,
      userNameFileHelper: userNameFileHelper ?? this.userNameFileHelper,
      noises: noises ?? this.noises,
      displayName: displayName ?? this.displayName,
      translateMsg: translateMsg ?? this.translateMsg,
      expand: expand ?? this.expand,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<int>(id.value);
    }
    if (owner.present) {
      map['owner'] = Variable<String>(owner.value);
    }
    if (from.present) {
      map['from'] = Variable<String>(from.value);
    }
    if (body.present) {
      map['body'] = Variable<String>(body.value);
    }
    if (filePath.present) {
      map['file_path'] = Variable<String>(filePath.value);
    }
    if (fileUrl.present) {
      map['file_url'] = Variable<String>(fileUrl.value);
    }
    if (fileFragment.present) {
      map['file_fragment'] = Variable<String>(fileFragment.value);
    }
    if (thumbnailPath.present) {
      map['thumbnail_path'] = Variable<String>(thumbnailPath.value);
    }
    if (thumbnailUrl.present) {
      map['thumbnail_url'] = Variable<String>(thumbnailUrl.value);
    }
    if (thumbnailFragment.present) {
      map['thumbnail_fragment'] = Variable<String>(thumbnailFragment.value);
    }
    if (selfDestruct.present) {
      map['self_destruct'] = Variable<bool>(selfDestruct.value);
    }
    if (undo.present) {
      map['undo'] = Variable<bool>(undo.value);
    }
    if (undoEdit.present) {
      map['undo_edit'] = Variable<bool>(undoEdit.value);
    }
    if (time.present) {
      map['time'] = Variable<int>(time.value);
    }
    if (type.present) {
      map['type'] = Variable<int>(type.value);
    }
    if (chatType.present) {
      map['chat_type'] = Variable<int>(chatType.value);
    }
    if (callState.present) {
      map['call_state'] = Variable<int>(callState.value);
    }
    if (state.present) {
      map['state'] = Variable<int>(state.value);
    }
    if (direction.present) {
      map['direction'] = Variable<int>(direction.value);
    }
    if (fileState.present) {
      map['file_state'] = Variable<int>(fileState.value);
    }
    if (thumbnailFileState.present) {
      map['thumbnail_file_state'] = Variable<int>(thumbnailFileState.value);
    }
    if (msgId.present) {
      map['msg_id'] = Variable<String>(msgId.value);
    }
    if (uuid.present) {
      map['uuid'] = Variable<String>(uuid.value);
    }
    if (fileName.present) {
      map['file_name'] = Variable<String>(fileName.value);
    }
    if (fileSize.present) {
      map['fileSize'] = Variable<int>(fileSize.value);
    }
    if (ext1.present) {
      map['ext1'] = Variable<String>(ext1.value);
    }
    if (ackNumber.present) {
      map['ack_number'] = Variable<int>(ackNumber.value);
    }
    if (read.present) {
      map['read'] = Variable<bool>(read.value);
    }
    if (replayMsg.present) {
      map['replay_msg'] = Variable<String>(replayMsg.value);
    }
    if (margeMsg.present) {
      map['marge_msg'] = Variable<String>(margeMsg.value);
    }
    if (contactMsg.present) {
      map['contact_msg'] = Variable<String>(contactMsg.value);
    }
    if (shareMsg.present) {
      map['share_msg'] = Variable<String>(shareMsg.value);
    }
    if (createTime.present) {
      map['create_time'] = Variable<double>(createTime.value);
    }
    if (at.present) {
      map['at'] = Variable<String>(at.value);
    }
    if (updateTime.present) {
      map['update_time'] = Variable<double>(updateTime.value);
    }
    if (resourceUuid.present) {
      map['resource_uuid'] = Variable<String>(resourceUuid.value);
    }
    if (hasShown.present) {
      map['has_shown'] = Variable<bool>(hasShown.value);
    }
    if (hasIdentify.present) {
      map['has_identify'] = Variable<bool>(hasIdentify.value);
    }
    if (messageHasRead.present) {
      map['message_has_read'] = Variable<bool>(messageHasRead.value);
    }
    if (fileDuration.present) {
      map['file_duration'] = Variable<int>(fileDuration.value);
    }
    if (userNameFileHelper.present) {
      map['user_name_file_helper'] = Variable<String>(userNameFileHelper.value);
    }
    if (noises.present) {
      map['noises'] = Variable<String>(noises.value);
    }
    if (displayName.present) {
      map['display_name'] = Variable<String>(displayName.value);
    }
    if (translateMsg.present) {
      map['translate_msg'] = Variable<String>(translateMsg.value);
    }
    if (expand.present) {
      map['expand'] = Variable<String>(expand.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('MessageCompanion(')
          ..write('id: $id, ')
          ..write('owner: $owner, ')
          ..write('from: $from, ')
          ..write('body: $body, ')
          ..write('filePath: $filePath, ')
          ..write('fileUrl: $fileUrl, ')
          ..write('fileFragment: $fileFragment, ')
          ..write('thumbnailPath: $thumbnailPath, ')
          ..write('thumbnailUrl: $thumbnailUrl, ')
          ..write('thumbnailFragment: $thumbnailFragment, ')
          ..write('selfDestruct: $selfDestruct, ')
          ..write('undo: $undo, ')
          ..write('undoEdit: $undoEdit, ')
          ..write('time: $time, ')
          ..write('type: $type, ')
          ..write('chatType: $chatType, ')
          ..write('callState: $callState, ')
          ..write('state: $state, ')
          ..write('direction: $direction, ')
          ..write('fileState: $fileState, ')
          ..write('thumbnailFileState: $thumbnailFileState, ')
          ..write('msgId: $msgId, ')
          ..write('uuid: $uuid, ')
          ..write('fileName: $fileName, ')
          ..write('fileSize: $fileSize, ')
          ..write('ext1: $ext1, ')
          ..write('ackNumber: $ackNumber, ')
          ..write('read: $read, ')
          ..write('replayMsg: $replayMsg, ')
          ..write('margeMsg: $margeMsg, ')
          ..write('contactMsg: $contactMsg, ')
          ..write('shareMsg: $shareMsg, ')
          ..write('createTime: $createTime, ')
          ..write('at: $at, ')
          ..write('updateTime: $updateTime, ')
          ..write('resourceUuid: $resourceUuid, ')
          ..write('hasShown: $hasShown, ')
          ..write('hasIdentify: $hasIdentify, ')
          ..write('messageHasRead: $messageHasRead, ')
          ..write('fileDuration: $fileDuration, ')
          ..write('userNameFileHelper: $userNameFileHelper, ')
          ..write('noises: $noises, ')
          ..write('displayName: $displayName, ')
          ..write('translateMsg: $translateMsg, ')
          ..write('expand: $expand')
          ..write(')'))
        .toString();
  }
}

class MessageTop extends Table with TableInfo<MessageTop, MessageTopData> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  MessageTop(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  late final GeneratedColumn<int> id = GeneratedColumn<int>(
      'id', aliasedName, false,
      hasAutoIncrement: true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      $customConstraints: 'NOT NULL PRIMARY KEY AUTOINCREMENT');
  static const VerificationMeta _ownerMeta = const VerificationMeta('owner');
  late final GeneratedColumn<String> owner = GeneratedColumn<String>(
      'owner', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _fromMeta = const VerificationMeta('from');
  late final GeneratedColumn<String> from = GeneratedColumn<String>(
      'from', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _bodyMeta = const VerificationMeta('body');
  late final GeneratedColumn<String> body = GeneratedColumn<String>(
      'body', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _filePathMeta =
      const VerificationMeta('filePath');
  late final GeneratedColumn<String> filePath = GeneratedColumn<String>(
      'file_path', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _fileUrlMeta =
      const VerificationMeta('fileUrl');
  late final GeneratedColumn<String> fileUrl = GeneratedColumn<String>(
      'file_url', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _fileFragmentMeta =
      const VerificationMeta('fileFragment');
  late final GeneratedColumn<String> fileFragment = GeneratedColumn<String>(
      'file_fragment', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _thumbnailPathMeta =
      const VerificationMeta('thumbnailPath');
  late final GeneratedColumn<String> thumbnailPath = GeneratedColumn<String>(
      'thumbnail_path', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _thumbnailUrlMeta =
      const VerificationMeta('thumbnailUrl');
  late final GeneratedColumn<String> thumbnailUrl = GeneratedColumn<String>(
      'thumbnail_url', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _thumbnailFragmentMeta =
      const VerificationMeta('thumbnailFragment');
  late final GeneratedColumn<String> thumbnailFragment =
      GeneratedColumn<String>('thumbnail_fragment', aliasedName, true,
          type: DriftSqlType.string,
          requiredDuringInsert: false,
          $customConstraints: '');
  static const VerificationMeta _selfDestructMeta =
      const VerificationMeta('selfDestruct');
  late final GeneratedColumn<bool> selfDestruct = GeneratedColumn<bool>(
      'self_destruct', aliasedName, true,
      type: DriftSqlType.bool,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _undoMeta = const VerificationMeta('undo');
  late final GeneratedColumn<bool> undo = GeneratedColumn<bool>(
      'undo', aliasedName, true,
      type: DriftSqlType.bool,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _undoEditMeta =
      const VerificationMeta('undoEdit');
  late final GeneratedColumn<bool> undoEdit = GeneratedColumn<bool>(
      'undo_edit', aliasedName, true,
      type: DriftSqlType.bool,
      requiredDuringInsert: false,
      $customConstraints: 'DEFAULT FALSE',
      defaultValue: const CustomExpression('FALSE'));
  static const VerificationMeta _timeMeta = const VerificationMeta('time');
  late final GeneratedColumn<int> time = GeneratedColumn<int>(
      'time', aliasedName, true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _typeMeta = const VerificationMeta('type');
  late final GeneratedColumn<int> type = GeneratedColumn<int>(
      'type', aliasedName, true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _chatTypeMeta =
      const VerificationMeta('chatType');
  late final GeneratedColumn<int> chatType = GeneratedColumn<int>(
      'chat_type', aliasedName, true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _callStateMeta =
      const VerificationMeta('callState');
  late final GeneratedColumn<int> callState = GeneratedColumn<int>(
      'call_state', aliasedName, true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _stateMeta = const VerificationMeta('state');
  late final GeneratedColumn<int> state = GeneratedColumn<int>(
      'state', aliasedName, true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _directionMeta =
      const VerificationMeta('direction');
  late final GeneratedColumn<int> direction = GeneratedColumn<int>(
      'direction', aliasedName, true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _fileStateMeta =
      const VerificationMeta('fileState');
  late final GeneratedColumn<int> fileState = GeneratedColumn<int>(
      'file_state', aliasedName, true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _thumbnailFileStateMeta =
      const VerificationMeta('thumbnailFileState');
  late final GeneratedColumn<int> thumbnailFileState = GeneratedColumn<int>(
      'thumbnail_file_state', aliasedName, true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _msgIdMeta = const VerificationMeta('msgId');
  late final GeneratedColumn<String> msgId = GeneratedColumn<String>(
      'msg_id', aliasedName, false,
      type: DriftSqlType.string,
      requiredDuringInsert: true,
      $customConstraints: 'NOT NULL UNIQUE');
  static const VerificationMeta _uuidMeta = const VerificationMeta('uuid');
  late final GeneratedColumn<String> uuid = GeneratedColumn<String>(
      'uuid', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _fileNameMeta =
      const VerificationMeta('fileName');
  late final GeneratedColumn<String> fileName = GeneratedColumn<String>(
      'file_name', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _fileSizeMeta =
      const VerificationMeta('fileSize');
  late final GeneratedColumn<int> fileSize = GeneratedColumn<int>(
      'fileSize', aliasedName, true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _ext1Meta = const VerificationMeta('ext1');
  late final GeneratedColumn<String> ext1 = GeneratedColumn<String>(
      'ext1', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _ackNumberMeta =
      const VerificationMeta('ackNumber');
  late final GeneratedColumn<int> ackNumber = GeneratedColumn<int>(
      'ack_number', aliasedName, true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _readMeta = const VerificationMeta('read');
  late final GeneratedColumn<bool> read = GeneratedColumn<bool>(
      'read', aliasedName, true,
      type: DriftSqlType.bool,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _replayMsgMeta =
      const VerificationMeta('replayMsg');
  late final GeneratedColumn<String> replayMsg = GeneratedColumn<String>(
      'replay_msg', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _margeMsgMeta =
      const VerificationMeta('margeMsg');
  late final GeneratedColumn<String> margeMsg = GeneratedColumn<String>(
      'marge_msg', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _contactMsgMeta =
      const VerificationMeta('contactMsg');
  late final GeneratedColumn<String> contactMsg = GeneratedColumn<String>(
      'contact_msg', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _shareMsgMeta =
      const VerificationMeta('shareMsg');
  late final GeneratedColumn<String> shareMsg = GeneratedColumn<String>(
      'share_msg', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _createTimeMeta =
      const VerificationMeta('createTime');
  late final GeneratedColumn<double> createTime = GeneratedColumn<double>(
      'create_time', aliasedName, true,
      type: DriftSqlType.double,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _atMeta = const VerificationMeta('at');
  late final GeneratedColumn<String> at = GeneratedColumn<String>(
      'at', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _updateTimeMeta =
      const VerificationMeta('updateTime');
  late final GeneratedColumn<double> updateTime = GeneratedColumn<double>(
      'update_time', aliasedName, true,
      type: DriftSqlType.double,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _resourceUuidMeta =
      const VerificationMeta('resourceUuid');
  late final GeneratedColumn<String> resourceUuid = GeneratedColumn<String>(
      'resource_uuid', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _hasShownMeta =
      const VerificationMeta('hasShown');
  late final GeneratedColumn<bool> hasShown = GeneratedColumn<bool>(
      'has_shown', aliasedName, true,
      type: DriftSqlType.bool,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _messageHasReadMeta =
      const VerificationMeta('messageHasRead');
  late final GeneratedColumn<bool> messageHasRead = GeneratedColumn<bool>(
      'message_has_read', aliasedName, true,
      type: DriftSqlType.bool,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _fileDurationMeta =
      const VerificationMeta('fileDuration');
  late final GeneratedColumn<int> fileDuration = GeneratedColumn<int>(
      'file_duration', aliasedName, true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _userNameFileHelperMeta =
      const VerificationMeta('userNameFileHelper');
  late final GeneratedColumn<String> userNameFileHelper =
      GeneratedColumn<String>('user_name_file_helper', aliasedName, true,
          type: DriftSqlType.string,
          requiredDuringInsert: false,
          $customConstraints: '');
  static const VerificationMeta _noisesMeta = const VerificationMeta('noises');
  late final GeneratedColumn<String> noises = GeneratedColumn<String>(
      'noises', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _displayNameMeta =
      const VerificationMeta('displayName');
  late final GeneratedColumn<String> displayName = GeneratedColumn<String>(
      'display_name', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _expandMeta = const VerificationMeta('expand');
  late final GeneratedColumn<String> expand = GeneratedColumn<String>(
      'expand', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  @override
  List<GeneratedColumn> get $columns => [
        id,
        owner,
        from,
        body,
        filePath,
        fileUrl,
        fileFragment,
        thumbnailPath,
        thumbnailUrl,
        thumbnailFragment,
        selfDestruct,
        undo,
        undoEdit,
        time,
        type,
        chatType,
        callState,
        state,
        direction,
        fileState,
        thumbnailFileState,
        msgId,
        uuid,
        fileName,
        fileSize,
        ext1,
        ackNumber,
        read,
        replayMsg,
        margeMsg,
        contactMsg,
        shareMsg,
        createTime,
        at,
        updateTime,
        resourceUuid,
        hasShown,
        messageHasRead,
        fileDuration,
        userNameFileHelper,
        noises,
        displayName,
        expand
      ];
  @override
  String get aliasedName => _alias ?? 'message_top';
  @override
  String get actualTableName => 'message_top';
  @override
  VerificationContext validateIntegrity(Insertable<MessageTopData> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    }
    if (data.containsKey('owner')) {
      context.handle(
          _ownerMeta, owner.isAcceptableOrUnknown(data['owner']!, _ownerMeta));
    }
    if (data.containsKey('from')) {
      context.handle(
          _fromMeta, from.isAcceptableOrUnknown(data['from']!, _fromMeta));
    }
    if (data.containsKey('body')) {
      context.handle(
          _bodyMeta, body.isAcceptableOrUnknown(data['body']!, _bodyMeta));
    }
    if (data.containsKey('file_path')) {
      context.handle(_filePathMeta,
          filePath.isAcceptableOrUnknown(data['file_path']!, _filePathMeta));
    }
    if (data.containsKey('file_url')) {
      context.handle(_fileUrlMeta,
          fileUrl.isAcceptableOrUnknown(data['file_url']!, _fileUrlMeta));
    }
    if (data.containsKey('file_fragment')) {
      context.handle(
          _fileFragmentMeta,
          fileFragment.isAcceptableOrUnknown(
              data['file_fragment']!, _fileFragmentMeta));
    }
    if (data.containsKey('thumbnail_path')) {
      context.handle(
          _thumbnailPathMeta,
          thumbnailPath.isAcceptableOrUnknown(
              data['thumbnail_path']!, _thumbnailPathMeta));
    }
    if (data.containsKey('thumbnail_url')) {
      context.handle(
          _thumbnailUrlMeta,
          thumbnailUrl.isAcceptableOrUnknown(
              data['thumbnail_url']!, _thumbnailUrlMeta));
    }
    if (data.containsKey('thumbnail_fragment')) {
      context.handle(
          _thumbnailFragmentMeta,
          thumbnailFragment.isAcceptableOrUnknown(
              data['thumbnail_fragment']!, _thumbnailFragmentMeta));
    }
    if (data.containsKey('self_destruct')) {
      context.handle(
          _selfDestructMeta,
          selfDestruct.isAcceptableOrUnknown(
              data['self_destruct']!, _selfDestructMeta));
    }
    if (data.containsKey('undo')) {
      context.handle(
          _undoMeta, undo.isAcceptableOrUnknown(data['undo']!, _undoMeta));
    }
    if (data.containsKey('undo_edit')) {
      context.handle(_undoEditMeta,
          undoEdit.isAcceptableOrUnknown(data['undo_edit']!, _undoEditMeta));
    }
    if (data.containsKey('time')) {
      context.handle(
          _timeMeta, time.isAcceptableOrUnknown(data['time']!, _timeMeta));
    }
    if (data.containsKey('type')) {
      context.handle(
          _typeMeta, type.isAcceptableOrUnknown(data['type']!, _typeMeta));
    }
    if (data.containsKey('chat_type')) {
      context.handle(_chatTypeMeta,
          chatType.isAcceptableOrUnknown(data['chat_type']!, _chatTypeMeta));
    }
    if (data.containsKey('call_state')) {
      context.handle(_callStateMeta,
          callState.isAcceptableOrUnknown(data['call_state']!, _callStateMeta));
    }
    if (data.containsKey('state')) {
      context.handle(
          _stateMeta, state.isAcceptableOrUnknown(data['state']!, _stateMeta));
    }
    if (data.containsKey('direction')) {
      context.handle(_directionMeta,
          direction.isAcceptableOrUnknown(data['direction']!, _directionMeta));
    }
    if (data.containsKey('file_state')) {
      context.handle(_fileStateMeta,
          fileState.isAcceptableOrUnknown(data['file_state']!, _fileStateMeta));
    }
    if (data.containsKey('thumbnail_file_state')) {
      context.handle(
          _thumbnailFileStateMeta,
          thumbnailFileState.isAcceptableOrUnknown(
              data['thumbnail_file_state']!, _thumbnailFileStateMeta));
    }
    if (data.containsKey('msg_id')) {
      context.handle(
          _msgIdMeta, msgId.isAcceptableOrUnknown(data['msg_id']!, _msgIdMeta));
    } else if (isInserting) {
      context.missing(_msgIdMeta);
    }
    if (data.containsKey('uuid')) {
      context.handle(
          _uuidMeta, uuid.isAcceptableOrUnknown(data['uuid']!, _uuidMeta));
    }
    if (data.containsKey('file_name')) {
      context.handle(_fileNameMeta,
          fileName.isAcceptableOrUnknown(data['file_name']!, _fileNameMeta));
    }
    if (data.containsKey('fileSize')) {
      context.handle(_fileSizeMeta,
          fileSize.isAcceptableOrUnknown(data['fileSize']!, _fileSizeMeta));
    }
    if (data.containsKey('ext1')) {
      context.handle(
          _ext1Meta, ext1.isAcceptableOrUnknown(data['ext1']!, _ext1Meta));
    }
    if (data.containsKey('ack_number')) {
      context.handle(_ackNumberMeta,
          ackNumber.isAcceptableOrUnknown(data['ack_number']!, _ackNumberMeta));
    }
    if (data.containsKey('read')) {
      context.handle(
          _readMeta, read.isAcceptableOrUnknown(data['read']!, _readMeta));
    }
    if (data.containsKey('replay_msg')) {
      context.handle(_replayMsgMeta,
          replayMsg.isAcceptableOrUnknown(data['replay_msg']!, _replayMsgMeta));
    }
    if (data.containsKey('marge_msg')) {
      context.handle(_margeMsgMeta,
          margeMsg.isAcceptableOrUnknown(data['marge_msg']!, _margeMsgMeta));
    }
    if (data.containsKey('contact_msg')) {
      context.handle(
          _contactMsgMeta,
          contactMsg.isAcceptableOrUnknown(
              data['contact_msg']!, _contactMsgMeta));
    }
    if (data.containsKey('share_msg')) {
      context.handle(_shareMsgMeta,
          shareMsg.isAcceptableOrUnknown(data['share_msg']!, _shareMsgMeta));
    }
    if (data.containsKey('create_time')) {
      context.handle(
          _createTimeMeta,
          createTime.isAcceptableOrUnknown(
              data['create_time']!, _createTimeMeta));
    }
    if (data.containsKey('at')) {
      context.handle(_atMeta, at.isAcceptableOrUnknown(data['at']!, _atMeta));
    }
    if (data.containsKey('update_time')) {
      context.handle(
          _updateTimeMeta,
          updateTime.isAcceptableOrUnknown(
              data['update_time']!, _updateTimeMeta));
    }
    if (data.containsKey('resource_uuid')) {
      context.handle(
          _resourceUuidMeta,
          resourceUuid.isAcceptableOrUnknown(
              data['resource_uuid']!, _resourceUuidMeta));
    }
    if (data.containsKey('has_shown')) {
      context.handle(_hasShownMeta,
          hasShown.isAcceptableOrUnknown(data['has_shown']!, _hasShownMeta));
    }
    if (data.containsKey('message_has_read')) {
      context.handle(
          _messageHasReadMeta,
          messageHasRead.isAcceptableOrUnknown(
              data['message_has_read']!, _messageHasReadMeta));
    }
    if (data.containsKey('file_duration')) {
      context.handle(
          _fileDurationMeta,
          fileDuration.isAcceptableOrUnknown(
              data['file_duration']!, _fileDurationMeta));
    }
    if (data.containsKey('user_name_file_helper')) {
      context.handle(
          _userNameFileHelperMeta,
          userNameFileHelper.isAcceptableOrUnknown(
              data['user_name_file_helper']!, _userNameFileHelperMeta));
    }
    if (data.containsKey('noises')) {
      context.handle(_noisesMeta,
          noises.isAcceptableOrUnknown(data['noises']!, _noisesMeta));
    }
    if (data.containsKey('display_name')) {
      context.handle(
          _displayNameMeta,
          displayName.isAcceptableOrUnknown(
              data['display_name']!, _displayNameMeta));
    }
    if (data.containsKey('expand')) {
      context.handle(_expandMeta,
          expand.isAcceptableOrUnknown(data['expand']!, _expandMeta));
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  MessageTopData map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return MessageTopData(
      id: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}id'])!,
      owner: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}owner']),
      from: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}from']),
      body: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}body']),
      filePath: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}file_path']),
      fileUrl: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}file_url']),
      fileFragment: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}file_fragment']),
      thumbnailPath: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}thumbnail_path']),
      thumbnailUrl: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}thumbnail_url']),
      thumbnailFragment: attachedDatabase.typeMapping.read(
          DriftSqlType.string, data['${effectivePrefix}thumbnail_fragment']),
      selfDestruct: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}self_destruct']),
      undo: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}undo']),
      undoEdit: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}undo_edit']),
      time: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}time']),
      type: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}type']),
      chatType: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}chat_type']),
      callState: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}call_state']),
      state: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}state']),
      direction: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}direction']),
      fileState: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}file_state']),
      thumbnailFileState: attachedDatabase.typeMapping.read(
          DriftSqlType.int, data['${effectivePrefix}thumbnail_file_state']),
      msgId: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}msg_id'])!,
      uuid: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}uuid']),
      fileName: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}file_name']),
      fileSize: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}fileSize']),
      ext1: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}ext1']),
      ackNumber: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}ack_number']),
      read: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}read']),
      replayMsg: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}replay_msg']),
      margeMsg: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}marge_msg']),
      contactMsg: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}contact_msg']),
      shareMsg: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}share_msg']),
      createTime: attachedDatabase.typeMapping
          .read(DriftSqlType.double, data['${effectivePrefix}create_time']),
      at: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}at']),
      updateTime: attachedDatabase.typeMapping
          .read(DriftSqlType.double, data['${effectivePrefix}update_time']),
      resourceUuid: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}resource_uuid']),
      hasShown: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}has_shown']),
      messageHasRead: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}message_has_read']),
      fileDuration: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}file_duration']),
      userNameFileHelper: attachedDatabase.typeMapping.read(
          DriftSqlType.string, data['${effectivePrefix}user_name_file_helper']),
      noises: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}noises']),
      displayName: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}display_name']),
      expand: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}expand']),
    );
  }

  @override
  MessageTop createAlias(String alias) {
    return MessageTop(attachedDatabase, alias);
  }

  @override
  bool get dontWriteConstraints => true;
}

class MessageTopData extends DataClass implements Insertable<MessageTopData> {
  final int id;
  final String? owner;
  final String? from;
  final String? body;
  final String? filePath;
  final String? fileUrl;
  final String? fileFragment;
  final String? thumbnailPath;
  final String? thumbnailUrl;
  final String? thumbnailFragment;
  final bool? selfDestruct;
  final bool? undo;
  final bool? undoEdit;
  final int? time;
  final int? type;
  final int? chatType;
  final int? callState;
  final int? state;
  final int? direction;
  final int? fileState;
  final int? thumbnailFileState;
  final String msgId;
  final String? uuid;
  final String? fileName;
  final int? fileSize;
  final String? ext1;
  final int? ackNumber;
  final bool? read;
  final String? replayMsg;
  final String? margeMsg;
  final String? contactMsg;
  final String? shareMsg;
  final double? createTime;
  final String? at;
  final double? updateTime;
  final String? resourceUuid;
  final bool? hasShown;
  final bool? messageHasRead;
  final int? fileDuration;
  final String? userNameFileHelper;
  final String? noises;
  final String? displayName;
  final String? expand;
  const MessageTopData(
      {required this.id,
      this.owner,
      this.from,
      this.body,
      this.filePath,
      this.fileUrl,
      this.fileFragment,
      this.thumbnailPath,
      this.thumbnailUrl,
      this.thumbnailFragment,
      this.selfDestruct,
      this.undo,
      this.undoEdit,
      this.time,
      this.type,
      this.chatType,
      this.callState,
      this.state,
      this.direction,
      this.fileState,
      this.thumbnailFileState,
      required this.msgId,
      this.uuid,
      this.fileName,
      this.fileSize,
      this.ext1,
      this.ackNumber,
      this.read,
      this.replayMsg,
      this.margeMsg,
      this.contactMsg,
      this.shareMsg,
      this.createTime,
      this.at,
      this.updateTime,
      this.resourceUuid,
      this.hasShown,
      this.messageHasRead,
      this.fileDuration,
      this.userNameFileHelper,
      this.noises,
      this.displayName,
      this.expand});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<int>(id);
    if (!nullToAbsent || owner != null) {
      map['owner'] = Variable<String>(owner);
    }
    if (!nullToAbsent || from != null) {
      map['from'] = Variable<String>(from);
    }
    if (!nullToAbsent || body != null) {
      map['body'] = Variable<String>(body);
    }
    if (!nullToAbsent || filePath != null) {
      map['file_path'] = Variable<String>(filePath);
    }
    if (!nullToAbsent || fileUrl != null) {
      map['file_url'] = Variable<String>(fileUrl);
    }
    if (!nullToAbsent || fileFragment != null) {
      map['file_fragment'] = Variable<String>(fileFragment);
    }
    if (!nullToAbsent || thumbnailPath != null) {
      map['thumbnail_path'] = Variable<String>(thumbnailPath);
    }
    if (!nullToAbsent || thumbnailUrl != null) {
      map['thumbnail_url'] = Variable<String>(thumbnailUrl);
    }
    if (!nullToAbsent || thumbnailFragment != null) {
      map['thumbnail_fragment'] = Variable<String>(thumbnailFragment);
    }
    if (!nullToAbsent || selfDestruct != null) {
      map['self_destruct'] = Variable<bool>(selfDestruct);
    }
    if (!nullToAbsent || undo != null) {
      map['undo'] = Variable<bool>(undo);
    }
    if (!nullToAbsent || undoEdit != null) {
      map['undo_edit'] = Variable<bool>(undoEdit);
    }
    if (!nullToAbsent || time != null) {
      map['time'] = Variable<int>(time);
    }
    if (!nullToAbsent || type != null) {
      map['type'] = Variable<int>(type);
    }
    if (!nullToAbsent || chatType != null) {
      map['chat_type'] = Variable<int>(chatType);
    }
    if (!nullToAbsent || callState != null) {
      map['call_state'] = Variable<int>(callState);
    }
    if (!nullToAbsent || state != null) {
      map['state'] = Variable<int>(state);
    }
    if (!nullToAbsent || direction != null) {
      map['direction'] = Variable<int>(direction);
    }
    if (!nullToAbsent || fileState != null) {
      map['file_state'] = Variable<int>(fileState);
    }
    if (!nullToAbsent || thumbnailFileState != null) {
      map['thumbnail_file_state'] = Variable<int>(thumbnailFileState);
    }
    map['msg_id'] = Variable<String>(msgId);
    if (!nullToAbsent || uuid != null) {
      map['uuid'] = Variable<String>(uuid);
    }
    if (!nullToAbsent || fileName != null) {
      map['file_name'] = Variable<String>(fileName);
    }
    if (!nullToAbsent || fileSize != null) {
      map['fileSize'] = Variable<int>(fileSize);
    }
    if (!nullToAbsent || ext1 != null) {
      map['ext1'] = Variable<String>(ext1);
    }
    if (!nullToAbsent || ackNumber != null) {
      map['ack_number'] = Variable<int>(ackNumber);
    }
    if (!nullToAbsent || read != null) {
      map['read'] = Variable<bool>(read);
    }
    if (!nullToAbsent || replayMsg != null) {
      map['replay_msg'] = Variable<String>(replayMsg);
    }
    if (!nullToAbsent || margeMsg != null) {
      map['marge_msg'] = Variable<String>(margeMsg);
    }
    if (!nullToAbsent || contactMsg != null) {
      map['contact_msg'] = Variable<String>(contactMsg);
    }
    if (!nullToAbsent || shareMsg != null) {
      map['share_msg'] = Variable<String>(shareMsg);
    }
    if (!nullToAbsent || createTime != null) {
      map['create_time'] = Variable<double>(createTime);
    }
    if (!nullToAbsent || at != null) {
      map['at'] = Variable<String>(at);
    }
    if (!nullToAbsent || updateTime != null) {
      map['update_time'] = Variable<double>(updateTime);
    }
    if (!nullToAbsent || resourceUuid != null) {
      map['resource_uuid'] = Variable<String>(resourceUuid);
    }
    if (!nullToAbsent || hasShown != null) {
      map['has_shown'] = Variable<bool>(hasShown);
    }
    if (!nullToAbsent || messageHasRead != null) {
      map['message_has_read'] = Variable<bool>(messageHasRead);
    }
    if (!nullToAbsent || fileDuration != null) {
      map['file_duration'] = Variable<int>(fileDuration);
    }
    if (!nullToAbsent || userNameFileHelper != null) {
      map['user_name_file_helper'] = Variable<String>(userNameFileHelper);
    }
    if (!nullToAbsent || noises != null) {
      map['noises'] = Variable<String>(noises);
    }
    if (!nullToAbsent || displayName != null) {
      map['display_name'] = Variable<String>(displayName);
    }
    if (!nullToAbsent || expand != null) {
      map['expand'] = Variable<String>(expand);
    }
    return map;
  }

  MessageTopCompanion toCompanion(bool nullToAbsent) {
    return MessageTopCompanion(
      id: Value(id),
      owner:
          owner == null && nullToAbsent ? const Value.absent() : Value(owner),
      from: from == null && nullToAbsent ? const Value.absent() : Value(from),
      body: body == null && nullToAbsent ? const Value.absent() : Value(body),
      filePath: filePath == null && nullToAbsent
          ? const Value.absent()
          : Value(filePath),
      fileUrl: fileUrl == null && nullToAbsent
          ? const Value.absent()
          : Value(fileUrl),
      fileFragment: fileFragment == null && nullToAbsent
          ? const Value.absent()
          : Value(fileFragment),
      thumbnailPath: thumbnailPath == null && nullToAbsent
          ? const Value.absent()
          : Value(thumbnailPath),
      thumbnailUrl: thumbnailUrl == null && nullToAbsent
          ? const Value.absent()
          : Value(thumbnailUrl),
      thumbnailFragment: thumbnailFragment == null && nullToAbsent
          ? const Value.absent()
          : Value(thumbnailFragment),
      selfDestruct: selfDestruct == null && nullToAbsent
          ? const Value.absent()
          : Value(selfDestruct),
      undo: undo == null && nullToAbsent ? const Value.absent() : Value(undo),
      undoEdit: undoEdit == null && nullToAbsent
          ? const Value.absent()
          : Value(undoEdit),
      time: time == null && nullToAbsent ? const Value.absent() : Value(time),
      type: type == null && nullToAbsent ? const Value.absent() : Value(type),
      chatType: chatType == null && nullToAbsent
          ? const Value.absent()
          : Value(chatType),
      callState: callState == null && nullToAbsent
          ? const Value.absent()
          : Value(callState),
      state:
          state == null && nullToAbsent ? const Value.absent() : Value(state),
      direction: direction == null && nullToAbsent
          ? const Value.absent()
          : Value(direction),
      fileState: fileState == null && nullToAbsent
          ? const Value.absent()
          : Value(fileState),
      thumbnailFileState: thumbnailFileState == null && nullToAbsent
          ? const Value.absent()
          : Value(thumbnailFileState),
      msgId: Value(msgId),
      uuid: uuid == null && nullToAbsent ? const Value.absent() : Value(uuid),
      fileName: fileName == null && nullToAbsent
          ? const Value.absent()
          : Value(fileName),
      fileSize: fileSize == null && nullToAbsent
          ? const Value.absent()
          : Value(fileSize),
      ext1: ext1 == null && nullToAbsent ? const Value.absent() : Value(ext1),
      ackNumber: ackNumber == null && nullToAbsent
          ? const Value.absent()
          : Value(ackNumber),
      read: read == null && nullToAbsent ? const Value.absent() : Value(read),
      replayMsg: replayMsg == null && nullToAbsent
          ? const Value.absent()
          : Value(replayMsg),
      margeMsg: margeMsg == null && nullToAbsent
          ? const Value.absent()
          : Value(margeMsg),
      contactMsg: contactMsg == null && nullToAbsent
          ? const Value.absent()
          : Value(contactMsg),
      shareMsg: shareMsg == null && nullToAbsent
          ? const Value.absent()
          : Value(shareMsg),
      createTime: createTime == null && nullToAbsent
          ? const Value.absent()
          : Value(createTime),
      at: at == null && nullToAbsent ? const Value.absent() : Value(at),
      updateTime: updateTime == null && nullToAbsent
          ? const Value.absent()
          : Value(updateTime),
      resourceUuid: resourceUuid == null && nullToAbsent
          ? const Value.absent()
          : Value(resourceUuid),
      hasShown: hasShown == null && nullToAbsent
          ? const Value.absent()
          : Value(hasShown),
      messageHasRead: messageHasRead == null && nullToAbsent
          ? const Value.absent()
          : Value(messageHasRead),
      fileDuration: fileDuration == null && nullToAbsent
          ? const Value.absent()
          : Value(fileDuration),
      userNameFileHelper: userNameFileHelper == null && nullToAbsent
          ? const Value.absent()
          : Value(userNameFileHelper),
      noises:
          noises == null && nullToAbsent ? const Value.absent() : Value(noises),
      displayName: displayName == null && nullToAbsent
          ? const Value.absent()
          : Value(displayName),
      expand:
          expand == null && nullToAbsent ? const Value.absent() : Value(expand),
    );
  }

  factory MessageTopData.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return MessageTopData(
      id: serializer.fromJson<int>(json['id']),
      owner: serializer.fromJson<String?>(json['owner']),
      from: serializer.fromJson<String?>(json['from']),
      body: serializer.fromJson<String?>(json['body']),
      filePath: serializer.fromJson<String?>(json['file_path']),
      fileUrl: serializer.fromJson<String?>(json['file_url']),
      fileFragment: serializer.fromJson<String?>(json['file_fragment']),
      thumbnailPath: serializer.fromJson<String?>(json['thumbnail_path']),
      thumbnailUrl: serializer.fromJson<String?>(json['thumbnail_url']),
      thumbnailFragment:
          serializer.fromJson<String?>(json['thumbnail_fragment']),
      selfDestruct: serializer.fromJson<bool?>(json['self_destruct']),
      undo: serializer.fromJson<bool?>(json['undo']),
      undoEdit: serializer.fromJson<bool?>(json['undo_edit']),
      time: serializer.fromJson<int?>(json['time']),
      type: serializer.fromJson<int?>(json['type']),
      chatType: serializer.fromJson<int?>(json['chat_type']),
      callState: serializer.fromJson<int?>(json['call_state']),
      state: serializer.fromJson<int?>(json['state']),
      direction: serializer.fromJson<int?>(json['direction']),
      fileState: serializer.fromJson<int?>(json['file_state']),
      thumbnailFileState:
          serializer.fromJson<int?>(json['thumbnail_file_state']),
      msgId: serializer.fromJson<String>(json['msg_id']),
      uuid: serializer.fromJson<String?>(json['uuid']),
      fileName: serializer.fromJson<String?>(json['file_name']),
      fileSize: serializer.fromJson<int?>(json['fileSize']),
      ext1: serializer.fromJson<String?>(json['ext1']),
      ackNumber: serializer.fromJson<int?>(json['ack_number']),
      read: serializer.fromJson<bool?>(json['read']),
      replayMsg: serializer.fromJson<String?>(json['replay_msg']),
      margeMsg: serializer.fromJson<String?>(json['marge_msg']),
      contactMsg: serializer.fromJson<String?>(json['contact_msg']),
      shareMsg: serializer.fromJson<String?>(json['share_msg']),
      createTime: serializer.fromJson<double?>(json['create_time']),
      at: serializer.fromJson<String?>(json['at']),
      updateTime: serializer.fromJson<double?>(json['update_time']),
      resourceUuid: serializer.fromJson<String?>(json['resource_uuid']),
      hasShown: serializer.fromJson<bool?>(json['has_shown']),
      messageHasRead: serializer.fromJson<bool?>(json['message_has_read']),
      fileDuration: serializer.fromJson<int?>(json['file_duration']),
      userNameFileHelper:
          serializer.fromJson<String?>(json['user_name_file_helper']),
      noises: serializer.fromJson<String?>(json['noises']),
      displayName: serializer.fromJson<String?>(json['display_name']),
      expand: serializer.fromJson<String?>(json['expand']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<int>(id),
      'owner': serializer.toJson<String?>(owner),
      'from': serializer.toJson<String?>(from),
      'body': serializer.toJson<String?>(body),
      'file_path': serializer.toJson<String?>(filePath),
      'file_url': serializer.toJson<String?>(fileUrl),
      'file_fragment': serializer.toJson<String?>(fileFragment),
      'thumbnail_path': serializer.toJson<String?>(thumbnailPath),
      'thumbnail_url': serializer.toJson<String?>(thumbnailUrl),
      'thumbnail_fragment': serializer.toJson<String?>(thumbnailFragment),
      'self_destruct': serializer.toJson<bool?>(selfDestruct),
      'undo': serializer.toJson<bool?>(undo),
      'undo_edit': serializer.toJson<bool?>(undoEdit),
      'time': serializer.toJson<int?>(time),
      'type': serializer.toJson<int?>(type),
      'chat_type': serializer.toJson<int?>(chatType),
      'call_state': serializer.toJson<int?>(callState),
      'state': serializer.toJson<int?>(state),
      'direction': serializer.toJson<int?>(direction),
      'file_state': serializer.toJson<int?>(fileState),
      'thumbnail_file_state': serializer.toJson<int?>(thumbnailFileState),
      'msg_id': serializer.toJson<String>(msgId),
      'uuid': serializer.toJson<String?>(uuid),
      'file_name': serializer.toJson<String?>(fileName),
      'fileSize': serializer.toJson<int?>(fileSize),
      'ext1': serializer.toJson<String?>(ext1),
      'ack_number': serializer.toJson<int?>(ackNumber),
      'read': serializer.toJson<bool?>(read),
      'replay_msg': serializer.toJson<String?>(replayMsg),
      'marge_msg': serializer.toJson<String?>(margeMsg),
      'contact_msg': serializer.toJson<String?>(contactMsg),
      'share_msg': serializer.toJson<String?>(shareMsg),
      'create_time': serializer.toJson<double?>(createTime),
      'at': serializer.toJson<String?>(at),
      'update_time': serializer.toJson<double?>(updateTime),
      'resource_uuid': serializer.toJson<String?>(resourceUuid),
      'has_shown': serializer.toJson<bool?>(hasShown),
      'message_has_read': serializer.toJson<bool?>(messageHasRead),
      'file_duration': serializer.toJson<int?>(fileDuration),
      'user_name_file_helper': serializer.toJson<String?>(userNameFileHelper),
      'noises': serializer.toJson<String?>(noises),
      'display_name': serializer.toJson<String?>(displayName),
      'expand': serializer.toJson<String?>(expand),
    };
  }

  MessageTopData copyWith(
          {int? id,
          Value<String?> owner = const Value.absent(),
          Value<String?> from = const Value.absent(),
          Value<String?> body = const Value.absent(),
          Value<String?> filePath = const Value.absent(),
          Value<String?> fileUrl = const Value.absent(),
          Value<String?> fileFragment = const Value.absent(),
          Value<String?> thumbnailPath = const Value.absent(),
          Value<String?> thumbnailUrl = const Value.absent(),
          Value<String?> thumbnailFragment = const Value.absent(),
          Value<bool?> selfDestruct = const Value.absent(),
          Value<bool?> undo = const Value.absent(),
          Value<bool?> undoEdit = const Value.absent(),
          Value<int?> time = const Value.absent(),
          Value<int?> type = const Value.absent(),
          Value<int?> chatType = const Value.absent(),
          Value<int?> callState = const Value.absent(),
          Value<int?> state = const Value.absent(),
          Value<int?> direction = const Value.absent(),
          Value<int?> fileState = const Value.absent(),
          Value<int?> thumbnailFileState = const Value.absent(),
          String? msgId,
          Value<String?> uuid = const Value.absent(),
          Value<String?> fileName = const Value.absent(),
          Value<int?> fileSize = const Value.absent(),
          Value<String?> ext1 = const Value.absent(),
          Value<int?> ackNumber = const Value.absent(),
          Value<bool?> read = const Value.absent(),
          Value<String?> replayMsg = const Value.absent(),
          Value<String?> margeMsg = const Value.absent(),
          Value<String?> contactMsg = const Value.absent(),
          Value<String?> shareMsg = const Value.absent(),
          Value<double?> createTime = const Value.absent(),
          Value<String?> at = const Value.absent(),
          Value<double?> updateTime = const Value.absent(),
          Value<String?> resourceUuid = const Value.absent(),
          Value<bool?> hasShown = const Value.absent(),
          Value<bool?> messageHasRead = const Value.absent(),
          Value<int?> fileDuration = const Value.absent(),
          Value<String?> userNameFileHelper = const Value.absent(),
          Value<String?> noises = const Value.absent(),
          Value<String?> displayName = const Value.absent(),
          Value<String?> expand = const Value.absent()}) =>
      MessageTopData(
        id: id ?? this.id,
        owner: owner.present ? owner.value : this.owner,
        from: from.present ? from.value : this.from,
        body: body.present ? body.value : this.body,
        filePath: filePath.present ? filePath.value : this.filePath,
        fileUrl: fileUrl.present ? fileUrl.value : this.fileUrl,
        fileFragment:
            fileFragment.present ? fileFragment.value : this.fileFragment,
        thumbnailPath:
            thumbnailPath.present ? thumbnailPath.value : this.thumbnailPath,
        thumbnailUrl:
            thumbnailUrl.present ? thumbnailUrl.value : this.thumbnailUrl,
        thumbnailFragment: thumbnailFragment.present
            ? thumbnailFragment.value
            : this.thumbnailFragment,
        selfDestruct:
            selfDestruct.present ? selfDestruct.value : this.selfDestruct,
        undo: undo.present ? undo.value : this.undo,
        undoEdit: undoEdit.present ? undoEdit.value : this.undoEdit,
        time: time.present ? time.value : this.time,
        type: type.present ? type.value : this.type,
        chatType: chatType.present ? chatType.value : this.chatType,
        callState: callState.present ? callState.value : this.callState,
        state: state.present ? state.value : this.state,
        direction: direction.present ? direction.value : this.direction,
        fileState: fileState.present ? fileState.value : this.fileState,
        thumbnailFileState: thumbnailFileState.present
            ? thumbnailFileState.value
            : this.thumbnailFileState,
        msgId: msgId ?? this.msgId,
        uuid: uuid.present ? uuid.value : this.uuid,
        fileName: fileName.present ? fileName.value : this.fileName,
        fileSize: fileSize.present ? fileSize.value : this.fileSize,
        ext1: ext1.present ? ext1.value : this.ext1,
        ackNumber: ackNumber.present ? ackNumber.value : this.ackNumber,
        read: read.present ? read.value : this.read,
        replayMsg: replayMsg.present ? replayMsg.value : this.replayMsg,
        margeMsg: margeMsg.present ? margeMsg.value : this.margeMsg,
        contactMsg: contactMsg.present ? contactMsg.value : this.contactMsg,
        shareMsg: shareMsg.present ? shareMsg.value : this.shareMsg,
        createTime: createTime.present ? createTime.value : this.createTime,
        at: at.present ? at.value : this.at,
        updateTime: updateTime.present ? updateTime.value : this.updateTime,
        resourceUuid:
            resourceUuid.present ? resourceUuid.value : this.resourceUuid,
        hasShown: hasShown.present ? hasShown.value : this.hasShown,
        messageHasRead:
            messageHasRead.present ? messageHasRead.value : this.messageHasRead,
        fileDuration:
            fileDuration.present ? fileDuration.value : this.fileDuration,
        userNameFileHelper: userNameFileHelper.present
            ? userNameFileHelper.value
            : this.userNameFileHelper,
        noises: noises.present ? noises.value : this.noises,
        displayName: displayName.present ? displayName.value : this.displayName,
        expand: expand.present ? expand.value : this.expand,
      );
  @override
  String toString() {
    return (StringBuffer('MessageTopData(')
          ..write('id: $id, ')
          ..write('owner: $owner, ')
          ..write('from: $from, ')
          ..write('body: $body, ')
          ..write('filePath: $filePath, ')
          ..write('fileUrl: $fileUrl, ')
          ..write('fileFragment: $fileFragment, ')
          ..write('thumbnailPath: $thumbnailPath, ')
          ..write('thumbnailUrl: $thumbnailUrl, ')
          ..write('thumbnailFragment: $thumbnailFragment, ')
          ..write('selfDestruct: $selfDestruct, ')
          ..write('undo: $undo, ')
          ..write('undoEdit: $undoEdit, ')
          ..write('time: $time, ')
          ..write('type: $type, ')
          ..write('chatType: $chatType, ')
          ..write('callState: $callState, ')
          ..write('state: $state, ')
          ..write('direction: $direction, ')
          ..write('fileState: $fileState, ')
          ..write('thumbnailFileState: $thumbnailFileState, ')
          ..write('msgId: $msgId, ')
          ..write('uuid: $uuid, ')
          ..write('fileName: $fileName, ')
          ..write('fileSize: $fileSize, ')
          ..write('ext1: $ext1, ')
          ..write('ackNumber: $ackNumber, ')
          ..write('read: $read, ')
          ..write('replayMsg: $replayMsg, ')
          ..write('margeMsg: $margeMsg, ')
          ..write('contactMsg: $contactMsg, ')
          ..write('shareMsg: $shareMsg, ')
          ..write('createTime: $createTime, ')
          ..write('at: $at, ')
          ..write('updateTime: $updateTime, ')
          ..write('resourceUuid: $resourceUuid, ')
          ..write('hasShown: $hasShown, ')
          ..write('messageHasRead: $messageHasRead, ')
          ..write('fileDuration: $fileDuration, ')
          ..write('userNameFileHelper: $userNameFileHelper, ')
          ..write('noises: $noises, ')
          ..write('displayName: $displayName, ')
          ..write('expand: $expand')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hashAll([
        id,
        owner,
        from,
        body,
        filePath,
        fileUrl,
        fileFragment,
        thumbnailPath,
        thumbnailUrl,
        thumbnailFragment,
        selfDestruct,
        undo,
        undoEdit,
        time,
        type,
        chatType,
        callState,
        state,
        direction,
        fileState,
        thumbnailFileState,
        msgId,
        uuid,
        fileName,
        fileSize,
        ext1,
        ackNumber,
        read,
        replayMsg,
        margeMsg,
        contactMsg,
        shareMsg,
        createTime,
        at,
        updateTime,
        resourceUuid,
        hasShown,
        messageHasRead,
        fileDuration,
        userNameFileHelper,
        noises,
        displayName,
        expand
      ]);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is MessageTopData &&
          other.id == this.id &&
          other.owner == this.owner &&
          other.from == this.from &&
          other.body == this.body &&
          other.filePath == this.filePath &&
          other.fileUrl == this.fileUrl &&
          other.fileFragment == this.fileFragment &&
          other.thumbnailPath == this.thumbnailPath &&
          other.thumbnailUrl == this.thumbnailUrl &&
          other.thumbnailFragment == this.thumbnailFragment &&
          other.selfDestruct == this.selfDestruct &&
          other.undo == this.undo &&
          other.undoEdit == this.undoEdit &&
          other.time == this.time &&
          other.type == this.type &&
          other.chatType == this.chatType &&
          other.callState == this.callState &&
          other.state == this.state &&
          other.direction == this.direction &&
          other.fileState == this.fileState &&
          other.thumbnailFileState == this.thumbnailFileState &&
          other.msgId == this.msgId &&
          other.uuid == this.uuid &&
          other.fileName == this.fileName &&
          other.fileSize == this.fileSize &&
          other.ext1 == this.ext1 &&
          other.ackNumber == this.ackNumber &&
          other.read == this.read &&
          other.replayMsg == this.replayMsg &&
          other.margeMsg == this.margeMsg &&
          other.contactMsg == this.contactMsg &&
          other.shareMsg == this.shareMsg &&
          other.createTime == this.createTime &&
          other.at == this.at &&
          other.updateTime == this.updateTime &&
          other.resourceUuid == this.resourceUuid &&
          other.hasShown == this.hasShown &&
          other.messageHasRead == this.messageHasRead &&
          other.fileDuration == this.fileDuration &&
          other.userNameFileHelper == this.userNameFileHelper &&
          other.noises == this.noises &&
          other.displayName == this.displayName &&
          other.expand == this.expand);
}

class MessageTopCompanion extends UpdateCompanion<MessageTopData> {
  final Value<int> id;
  final Value<String?> owner;
  final Value<String?> from;
  final Value<String?> body;
  final Value<String?> filePath;
  final Value<String?> fileUrl;
  final Value<String?> fileFragment;
  final Value<String?> thumbnailPath;
  final Value<String?> thumbnailUrl;
  final Value<String?> thumbnailFragment;
  final Value<bool?> selfDestruct;
  final Value<bool?> undo;
  final Value<bool?> undoEdit;
  final Value<int?> time;
  final Value<int?> type;
  final Value<int?> chatType;
  final Value<int?> callState;
  final Value<int?> state;
  final Value<int?> direction;
  final Value<int?> fileState;
  final Value<int?> thumbnailFileState;
  final Value<String> msgId;
  final Value<String?> uuid;
  final Value<String?> fileName;
  final Value<int?> fileSize;
  final Value<String?> ext1;
  final Value<int?> ackNumber;
  final Value<bool?> read;
  final Value<String?> replayMsg;
  final Value<String?> margeMsg;
  final Value<String?> contactMsg;
  final Value<String?> shareMsg;
  final Value<double?> createTime;
  final Value<String?> at;
  final Value<double?> updateTime;
  final Value<String?> resourceUuid;
  final Value<bool?> hasShown;
  final Value<bool?> messageHasRead;
  final Value<int?> fileDuration;
  final Value<String?> userNameFileHelper;
  final Value<String?> noises;
  final Value<String?> displayName;
  final Value<String?> expand;
  const MessageTopCompanion({
    this.id = const Value.absent(),
    this.owner = const Value.absent(),
    this.from = const Value.absent(),
    this.body = const Value.absent(),
    this.filePath = const Value.absent(),
    this.fileUrl = const Value.absent(),
    this.fileFragment = const Value.absent(),
    this.thumbnailPath = const Value.absent(),
    this.thumbnailUrl = const Value.absent(),
    this.thumbnailFragment = const Value.absent(),
    this.selfDestruct = const Value.absent(),
    this.undo = const Value.absent(),
    this.undoEdit = const Value.absent(),
    this.time = const Value.absent(),
    this.type = const Value.absent(),
    this.chatType = const Value.absent(),
    this.callState = const Value.absent(),
    this.state = const Value.absent(),
    this.direction = const Value.absent(),
    this.fileState = const Value.absent(),
    this.thumbnailFileState = const Value.absent(),
    this.msgId = const Value.absent(),
    this.uuid = const Value.absent(),
    this.fileName = const Value.absent(),
    this.fileSize = const Value.absent(),
    this.ext1 = const Value.absent(),
    this.ackNumber = const Value.absent(),
    this.read = const Value.absent(),
    this.replayMsg = const Value.absent(),
    this.margeMsg = const Value.absent(),
    this.contactMsg = const Value.absent(),
    this.shareMsg = const Value.absent(),
    this.createTime = const Value.absent(),
    this.at = const Value.absent(),
    this.updateTime = const Value.absent(),
    this.resourceUuid = const Value.absent(),
    this.hasShown = const Value.absent(),
    this.messageHasRead = const Value.absent(),
    this.fileDuration = const Value.absent(),
    this.userNameFileHelper = const Value.absent(),
    this.noises = const Value.absent(),
    this.displayName = const Value.absent(),
    this.expand = const Value.absent(),
  });
  MessageTopCompanion.insert({
    this.id = const Value.absent(),
    this.owner = const Value.absent(),
    this.from = const Value.absent(),
    this.body = const Value.absent(),
    this.filePath = const Value.absent(),
    this.fileUrl = const Value.absent(),
    this.fileFragment = const Value.absent(),
    this.thumbnailPath = const Value.absent(),
    this.thumbnailUrl = const Value.absent(),
    this.thumbnailFragment = const Value.absent(),
    this.selfDestruct = const Value.absent(),
    this.undo = const Value.absent(),
    this.undoEdit = const Value.absent(),
    this.time = const Value.absent(),
    this.type = const Value.absent(),
    this.chatType = const Value.absent(),
    this.callState = const Value.absent(),
    this.state = const Value.absent(),
    this.direction = const Value.absent(),
    this.fileState = const Value.absent(),
    this.thumbnailFileState = const Value.absent(),
    required String msgId,
    this.uuid = const Value.absent(),
    this.fileName = const Value.absent(),
    this.fileSize = const Value.absent(),
    this.ext1 = const Value.absent(),
    this.ackNumber = const Value.absent(),
    this.read = const Value.absent(),
    this.replayMsg = const Value.absent(),
    this.margeMsg = const Value.absent(),
    this.contactMsg = const Value.absent(),
    this.shareMsg = const Value.absent(),
    this.createTime = const Value.absent(),
    this.at = const Value.absent(),
    this.updateTime = const Value.absent(),
    this.resourceUuid = const Value.absent(),
    this.hasShown = const Value.absent(),
    this.messageHasRead = const Value.absent(),
    this.fileDuration = const Value.absent(),
    this.userNameFileHelper = const Value.absent(),
    this.noises = const Value.absent(),
    this.displayName = const Value.absent(),
    this.expand = const Value.absent(),
  }) : msgId = Value(msgId);
  static Insertable<MessageTopData> custom({
    Expression<int>? id,
    Expression<String>? owner,
    Expression<String>? from,
    Expression<String>? body,
    Expression<String>? filePath,
    Expression<String>? fileUrl,
    Expression<String>? fileFragment,
    Expression<String>? thumbnailPath,
    Expression<String>? thumbnailUrl,
    Expression<String>? thumbnailFragment,
    Expression<bool>? selfDestruct,
    Expression<bool>? undo,
    Expression<bool>? undoEdit,
    Expression<int>? time,
    Expression<int>? type,
    Expression<int>? chatType,
    Expression<int>? callState,
    Expression<int>? state,
    Expression<int>? direction,
    Expression<int>? fileState,
    Expression<int>? thumbnailFileState,
    Expression<String>? msgId,
    Expression<String>? uuid,
    Expression<String>? fileName,
    Expression<int>? fileSize,
    Expression<String>? ext1,
    Expression<int>? ackNumber,
    Expression<bool>? read,
    Expression<String>? replayMsg,
    Expression<String>? margeMsg,
    Expression<String>? contactMsg,
    Expression<String>? shareMsg,
    Expression<double>? createTime,
    Expression<String>? at,
    Expression<double>? updateTime,
    Expression<String>? resourceUuid,
    Expression<bool>? hasShown,
    Expression<bool>? messageHasRead,
    Expression<int>? fileDuration,
    Expression<String>? userNameFileHelper,
    Expression<String>? noises,
    Expression<String>? displayName,
    Expression<String>? expand,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (owner != null) 'owner': owner,
      if (from != null) 'from': from,
      if (body != null) 'body': body,
      if (filePath != null) 'file_path': filePath,
      if (fileUrl != null) 'file_url': fileUrl,
      if (fileFragment != null) 'file_fragment': fileFragment,
      if (thumbnailPath != null) 'thumbnail_path': thumbnailPath,
      if (thumbnailUrl != null) 'thumbnail_url': thumbnailUrl,
      if (thumbnailFragment != null) 'thumbnail_fragment': thumbnailFragment,
      if (selfDestruct != null) 'self_destruct': selfDestruct,
      if (undo != null) 'undo': undo,
      if (undoEdit != null) 'undo_edit': undoEdit,
      if (time != null) 'time': time,
      if (type != null) 'type': type,
      if (chatType != null) 'chat_type': chatType,
      if (callState != null) 'call_state': callState,
      if (state != null) 'state': state,
      if (direction != null) 'direction': direction,
      if (fileState != null) 'file_state': fileState,
      if (thumbnailFileState != null)
        'thumbnail_file_state': thumbnailFileState,
      if (msgId != null) 'msg_id': msgId,
      if (uuid != null) 'uuid': uuid,
      if (fileName != null) 'file_name': fileName,
      if (fileSize != null) 'fileSize': fileSize,
      if (ext1 != null) 'ext1': ext1,
      if (ackNumber != null) 'ack_number': ackNumber,
      if (read != null) 'read': read,
      if (replayMsg != null) 'replay_msg': replayMsg,
      if (margeMsg != null) 'marge_msg': margeMsg,
      if (contactMsg != null) 'contact_msg': contactMsg,
      if (shareMsg != null) 'share_msg': shareMsg,
      if (createTime != null) 'create_time': createTime,
      if (at != null) 'at': at,
      if (updateTime != null) 'update_time': updateTime,
      if (resourceUuid != null) 'resource_uuid': resourceUuid,
      if (hasShown != null) 'has_shown': hasShown,
      if (messageHasRead != null) 'message_has_read': messageHasRead,
      if (fileDuration != null) 'file_duration': fileDuration,
      if (userNameFileHelper != null)
        'user_name_file_helper': userNameFileHelper,
      if (noises != null) 'noises': noises,
      if (displayName != null) 'display_name': displayName,
      if (expand != null) 'expand': expand,
    });
  }

  MessageTopCompanion copyWith(
      {Value<int>? id,
      Value<String?>? owner,
      Value<String?>? from,
      Value<String?>? body,
      Value<String?>? filePath,
      Value<String?>? fileUrl,
      Value<String?>? fileFragment,
      Value<String?>? thumbnailPath,
      Value<String?>? thumbnailUrl,
      Value<String?>? thumbnailFragment,
      Value<bool?>? selfDestruct,
      Value<bool?>? undo,
      Value<bool?>? undoEdit,
      Value<int?>? time,
      Value<int?>? type,
      Value<int?>? chatType,
      Value<int?>? callState,
      Value<int?>? state,
      Value<int?>? direction,
      Value<int?>? fileState,
      Value<int?>? thumbnailFileState,
      Value<String>? msgId,
      Value<String?>? uuid,
      Value<String?>? fileName,
      Value<int?>? fileSize,
      Value<String?>? ext1,
      Value<int?>? ackNumber,
      Value<bool?>? read,
      Value<String?>? replayMsg,
      Value<String?>? margeMsg,
      Value<String?>? contactMsg,
      Value<String?>? shareMsg,
      Value<double?>? createTime,
      Value<String?>? at,
      Value<double?>? updateTime,
      Value<String?>? resourceUuid,
      Value<bool?>? hasShown,
      Value<bool?>? messageHasRead,
      Value<int?>? fileDuration,
      Value<String?>? userNameFileHelper,
      Value<String?>? noises,
      Value<String?>? displayName,
      Value<String?>? expand}) {
    return MessageTopCompanion(
      id: id ?? this.id,
      owner: owner ?? this.owner,
      from: from ?? this.from,
      body: body ?? this.body,
      filePath: filePath ?? this.filePath,
      fileUrl: fileUrl ?? this.fileUrl,
      fileFragment: fileFragment ?? this.fileFragment,
      thumbnailPath: thumbnailPath ?? this.thumbnailPath,
      thumbnailUrl: thumbnailUrl ?? this.thumbnailUrl,
      thumbnailFragment: thumbnailFragment ?? this.thumbnailFragment,
      selfDestruct: selfDestruct ?? this.selfDestruct,
      undo: undo ?? this.undo,
      undoEdit: undoEdit ?? this.undoEdit,
      time: time ?? this.time,
      type: type ?? this.type,
      chatType: chatType ?? this.chatType,
      callState: callState ?? this.callState,
      state: state ?? this.state,
      direction: direction ?? this.direction,
      fileState: fileState ?? this.fileState,
      thumbnailFileState: thumbnailFileState ?? this.thumbnailFileState,
      msgId: msgId ?? this.msgId,
      uuid: uuid ?? this.uuid,
      fileName: fileName ?? this.fileName,
      fileSize: fileSize ?? this.fileSize,
      ext1: ext1 ?? this.ext1,
      ackNumber: ackNumber ?? this.ackNumber,
      read: read ?? this.read,
      replayMsg: replayMsg ?? this.replayMsg,
      margeMsg: margeMsg ?? this.margeMsg,
      contactMsg: contactMsg ?? this.contactMsg,
      shareMsg: shareMsg ?? this.shareMsg,
      createTime: createTime ?? this.createTime,
      at: at ?? this.at,
      updateTime: updateTime ?? this.updateTime,
      resourceUuid: resourceUuid ?? this.resourceUuid,
      hasShown: hasShown ?? this.hasShown,
      messageHasRead: messageHasRead ?? this.messageHasRead,
      fileDuration: fileDuration ?? this.fileDuration,
      userNameFileHelper: userNameFileHelper ?? this.userNameFileHelper,
      noises: noises ?? this.noises,
      displayName: displayName ?? this.displayName,
      expand: expand ?? this.expand,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<int>(id.value);
    }
    if (owner.present) {
      map['owner'] = Variable<String>(owner.value);
    }
    if (from.present) {
      map['from'] = Variable<String>(from.value);
    }
    if (body.present) {
      map['body'] = Variable<String>(body.value);
    }
    if (filePath.present) {
      map['file_path'] = Variable<String>(filePath.value);
    }
    if (fileUrl.present) {
      map['file_url'] = Variable<String>(fileUrl.value);
    }
    if (fileFragment.present) {
      map['file_fragment'] = Variable<String>(fileFragment.value);
    }
    if (thumbnailPath.present) {
      map['thumbnail_path'] = Variable<String>(thumbnailPath.value);
    }
    if (thumbnailUrl.present) {
      map['thumbnail_url'] = Variable<String>(thumbnailUrl.value);
    }
    if (thumbnailFragment.present) {
      map['thumbnail_fragment'] = Variable<String>(thumbnailFragment.value);
    }
    if (selfDestruct.present) {
      map['self_destruct'] = Variable<bool>(selfDestruct.value);
    }
    if (undo.present) {
      map['undo'] = Variable<bool>(undo.value);
    }
    if (undoEdit.present) {
      map['undo_edit'] = Variable<bool>(undoEdit.value);
    }
    if (time.present) {
      map['time'] = Variable<int>(time.value);
    }
    if (type.present) {
      map['type'] = Variable<int>(type.value);
    }
    if (chatType.present) {
      map['chat_type'] = Variable<int>(chatType.value);
    }
    if (callState.present) {
      map['call_state'] = Variable<int>(callState.value);
    }
    if (state.present) {
      map['state'] = Variable<int>(state.value);
    }
    if (direction.present) {
      map['direction'] = Variable<int>(direction.value);
    }
    if (fileState.present) {
      map['file_state'] = Variable<int>(fileState.value);
    }
    if (thumbnailFileState.present) {
      map['thumbnail_file_state'] = Variable<int>(thumbnailFileState.value);
    }
    if (msgId.present) {
      map['msg_id'] = Variable<String>(msgId.value);
    }
    if (uuid.present) {
      map['uuid'] = Variable<String>(uuid.value);
    }
    if (fileName.present) {
      map['file_name'] = Variable<String>(fileName.value);
    }
    if (fileSize.present) {
      map['fileSize'] = Variable<int>(fileSize.value);
    }
    if (ext1.present) {
      map['ext1'] = Variable<String>(ext1.value);
    }
    if (ackNumber.present) {
      map['ack_number'] = Variable<int>(ackNumber.value);
    }
    if (read.present) {
      map['read'] = Variable<bool>(read.value);
    }
    if (replayMsg.present) {
      map['replay_msg'] = Variable<String>(replayMsg.value);
    }
    if (margeMsg.present) {
      map['marge_msg'] = Variable<String>(margeMsg.value);
    }
    if (contactMsg.present) {
      map['contact_msg'] = Variable<String>(contactMsg.value);
    }
    if (shareMsg.present) {
      map['share_msg'] = Variable<String>(shareMsg.value);
    }
    if (createTime.present) {
      map['create_time'] = Variable<double>(createTime.value);
    }
    if (at.present) {
      map['at'] = Variable<String>(at.value);
    }
    if (updateTime.present) {
      map['update_time'] = Variable<double>(updateTime.value);
    }
    if (resourceUuid.present) {
      map['resource_uuid'] = Variable<String>(resourceUuid.value);
    }
    if (hasShown.present) {
      map['has_shown'] = Variable<bool>(hasShown.value);
    }
    if (messageHasRead.present) {
      map['message_has_read'] = Variable<bool>(messageHasRead.value);
    }
    if (fileDuration.present) {
      map['file_duration'] = Variable<int>(fileDuration.value);
    }
    if (userNameFileHelper.present) {
      map['user_name_file_helper'] = Variable<String>(userNameFileHelper.value);
    }
    if (noises.present) {
      map['noises'] = Variable<String>(noises.value);
    }
    if (displayName.present) {
      map['display_name'] = Variable<String>(displayName.value);
    }
    if (expand.present) {
      map['expand'] = Variable<String>(expand.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('MessageTopCompanion(')
          ..write('id: $id, ')
          ..write('owner: $owner, ')
          ..write('from: $from, ')
          ..write('body: $body, ')
          ..write('filePath: $filePath, ')
          ..write('fileUrl: $fileUrl, ')
          ..write('fileFragment: $fileFragment, ')
          ..write('thumbnailPath: $thumbnailPath, ')
          ..write('thumbnailUrl: $thumbnailUrl, ')
          ..write('thumbnailFragment: $thumbnailFragment, ')
          ..write('selfDestruct: $selfDestruct, ')
          ..write('undo: $undo, ')
          ..write('undoEdit: $undoEdit, ')
          ..write('time: $time, ')
          ..write('type: $type, ')
          ..write('chatType: $chatType, ')
          ..write('callState: $callState, ')
          ..write('state: $state, ')
          ..write('direction: $direction, ')
          ..write('fileState: $fileState, ')
          ..write('thumbnailFileState: $thumbnailFileState, ')
          ..write('msgId: $msgId, ')
          ..write('uuid: $uuid, ')
          ..write('fileName: $fileName, ')
          ..write('fileSize: $fileSize, ')
          ..write('ext1: $ext1, ')
          ..write('ackNumber: $ackNumber, ')
          ..write('read: $read, ')
          ..write('replayMsg: $replayMsg, ')
          ..write('margeMsg: $margeMsg, ')
          ..write('contactMsg: $contactMsg, ')
          ..write('shareMsg: $shareMsg, ')
          ..write('createTime: $createTime, ')
          ..write('at: $at, ')
          ..write('updateTime: $updateTime, ')
          ..write('resourceUuid: $resourceUuid, ')
          ..write('hasShown: $hasShown, ')
          ..write('messageHasRead: $messageHasRead, ')
          ..write('fileDuration: $fileDuration, ')
          ..write('userNameFileHelper: $userNameFileHelper, ')
          ..write('noises: $noises, ')
          ..write('displayName: $displayName, ')
          ..write('expand: $expand')
          ..write(')'))
        .toString();
  }
}

class MessageTopAdmin extends Table
    with TableInfo<MessageTopAdmin, MessageTopAdminData> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  MessageTopAdmin(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  late final GeneratedColumn<int> id = GeneratedColumn<int>(
      'id', aliasedName, false,
      hasAutoIncrement: true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      $customConstraints: 'NOT NULL PRIMARY KEY AUTOINCREMENT');
  static const VerificationMeta _ownerMeta = const VerificationMeta('owner');
  late final GeneratedColumn<String> owner = GeneratedColumn<String>(
      'owner', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _fromMeta = const VerificationMeta('from');
  late final GeneratedColumn<String> from = GeneratedColumn<String>(
      'from', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _bodyMeta = const VerificationMeta('body');
  late final GeneratedColumn<String> body = GeneratedColumn<String>(
      'body', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _filePathMeta =
      const VerificationMeta('filePath');
  late final GeneratedColumn<String> filePath = GeneratedColumn<String>(
      'file_path', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _fileUrlMeta =
      const VerificationMeta('fileUrl');
  late final GeneratedColumn<String> fileUrl = GeneratedColumn<String>(
      'file_url', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _fileFragmentMeta =
      const VerificationMeta('fileFragment');
  late final GeneratedColumn<String> fileFragment = GeneratedColumn<String>(
      'file_fragment', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _thumbnailPathMeta =
      const VerificationMeta('thumbnailPath');
  late final GeneratedColumn<String> thumbnailPath = GeneratedColumn<String>(
      'thumbnail_path', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _thumbnailUrlMeta =
      const VerificationMeta('thumbnailUrl');
  late final GeneratedColumn<String> thumbnailUrl = GeneratedColumn<String>(
      'thumbnail_url', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _thumbnailFragmentMeta =
      const VerificationMeta('thumbnailFragment');
  late final GeneratedColumn<String> thumbnailFragment =
      GeneratedColumn<String>('thumbnail_fragment', aliasedName, true,
          type: DriftSqlType.string,
          requiredDuringInsert: false,
          $customConstraints: '');
  static const VerificationMeta _selfDestructMeta =
      const VerificationMeta('selfDestruct');
  late final GeneratedColumn<bool> selfDestruct = GeneratedColumn<bool>(
      'self_destruct', aliasedName, true,
      type: DriftSqlType.bool,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _undoMeta = const VerificationMeta('undo');
  late final GeneratedColumn<bool> undo = GeneratedColumn<bool>(
      'undo', aliasedName, true,
      type: DriftSqlType.bool,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _undoEditMeta =
      const VerificationMeta('undoEdit');
  late final GeneratedColumn<bool> undoEdit = GeneratedColumn<bool>(
      'undo_edit', aliasedName, true,
      type: DriftSqlType.bool,
      requiredDuringInsert: false,
      $customConstraints: 'DEFAULT FALSE',
      defaultValue: const CustomExpression('FALSE'));
  static const VerificationMeta _timeMeta = const VerificationMeta('time');
  late final GeneratedColumn<int> time = GeneratedColumn<int>(
      'time', aliasedName, true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _typeMeta = const VerificationMeta('type');
  late final GeneratedColumn<int> type = GeneratedColumn<int>(
      'type', aliasedName, true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _chatTypeMeta =
      const VerificationMeta('chatType');
  late final GeneratedColumn<int> chatType = GeneratedColumn<int>(
      'chat_type', aliasedName, true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _callStateMeta =
      const VerificationMeta('callState');
  late final GeneratedColumn<int> callState = GeneratedColumn<int>(
      'call_state', aliasedName, true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _stateMeta = const VerificationMeta('state');
  late final GeneratedColumn<int> state = GeneratedColumn<int>(
      'state', aliasedName, true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _directionMeta =
      const VerificationMeta('direction');
  late final GeneratedColumn<int> direction = GeneratedColumn<int>(
      'direction', aliasedName, true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _fileStateMeta =
      const VerificationMeta('fileState');
  late final GeneratedColumn<int> fileState = GeneratedColumn<int>(
      'file_state', aliasedName, true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _thumbnailFileStateMeta =
      const VerificationMeta('thumbnailFileState');
  late final GeneratedColumn<int> thumbnailFileState = GeneratedColumn<int>(
      'thumbnail_file_state', aliasedName, true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _msgIdMeta = const VerificationMeta('msgId');
  late final GeneratedColumn<String> msgId = GeneratedColumn<String>(
      'msg_id', aliasedName, false,
      type: DriftSqlType.string,
      requiredDuringInsert: true,
      $customConstraints: 'NOT NULL UNIQUE');
  static const VerificationMeta _uuidMeta = const VerificationMeta('uuid');
  late final GeneratedColumn<String> uuid = GeneratedColumn<String>(
      'uuid', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _fileNameMeta =
      const VerificationMeta('fileName');
  late final GeneratedColumn<String> fileName = GeneratedColumn<String>(
      'file_name', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _fileSizeMeta =
      const VerificationMeta('fileSize');
  late final GeneratedColumn<int> fileSize = GeneratedColumn<int>(
      'fileSize', aliasedName, true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _ext1Meta = const VerificationMeta('ext1');
  late final GeneratedColumn<String> ext1 = GeneratedColumn<String>(
      'ext1', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _ackNumberMeta =
      const VerificationMeta('ackNumber');
  late final GeneratedColumn<int> ackNumber = GeneratedColumn<int>(
      'ack_number', aliasedName, true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _readMeta = const VerificationMeta('read');
  late final GeneratedColumn<bool> read = GeneratedColumn<bool>(
      'read', aliasedName, true,
      type: DriftSqlType.bool,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _replayMsgMeta =
      const VerificationMeta('replayMsg');
  late final GeneratedColumn<String> replayMsg = GeneratedColumn<String>(
      'replay_msg', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _margeMsgMeta =
      const VerificationMeta('margeMsg');
  late final GeneratedColumn<String> margeMsg = GeneratedColumn<String>(
      'marge_msg', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _contactMsgMeta =
      const VerificationMeta('contactMsg');
  late final GeneratedColumn<String> contactMsg = GeneratedColumn<String>(
      'contact_msg', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _shareMsgMeta =
      const VerificationMeta('shareMsg');
  late final GeneratedColumn<String> shareMsg = GeneratedColumn<String>(
      'share_msg', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _createTimeMeta =
      const VerificationMeta('createTime');
  late final GeneratedColumn<double> createTime = GeneratedColumn<double>(
      'create_time', aliasedName, true,
      type: DriftSqlType.double,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _atMeta = const VerificationMeta('at');
  late final GeneratedColumn<String> at = GeneratedColumn<String>(
      'at', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _updateTimeMeta =
      const VerificationMeta('updateTime');
  late final GeneratedColumn<double> updateTime = GeneratedColumn<double>(
      'update_time', aliasedName, true,
      type: DriftSqlType.double,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _resourceUuidMeta =
      const VerificationMeta('resourceUuid');
  late final GeneratedColumn<String> resourceUuid = GeneratedColumn<String>(
      'resource_uuid', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _hasShownMeta =
      const VerificationMeta('hasShown');
  late final GeneratedColumn<bool> hasShown = GeneratedColumn<bool>(
      'has_shown', aliasedName, true,
      type: DriftSqlType.bool,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _messageHasReadMeta =
      const VerificationMeta('messageHasRead');
  late final GeneratedColumn<bool> messageHasRead = GeneratedColumn<bool>(
      'message_has_read', aliasedName, true,
      type: DriftSqlType.bool,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _fileDurationMeta =
      const VerificationMeta('fileDuration');
  late final GeneratedColumn<int> fileDuration = GeneratedColumn<int>(
      'file_duration', aliasedName, true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _userNameFileHelperMeta =
      const VerificationMeta('userNameFileHelper');
  late final GeneratedColumn<String> userNameFileHelper =
      GeneratedColumn<String>('user_name_file_helper', aliasedName, true,
          type: DriftSqlType.string,
          requiredDuringInsert: false,
          $customConstraints: '');
  static const VerificationMeta _noisesMeta = const VerificationMeta('noises');
  late final GeneratedColumn<String> noises = GeneratedColumn<String>(
      'noises', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _displayNameMeta =
      const VerificationMeta('displayName');
  late final GeneratedColumn<String> displayName = GeneratedColumn<String>(
      'display_name', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _expandMeta = const VerificationMeta('expand');
  late final GeneratedColumn<String> expand = GeneratedColumn<String>(
      'expand', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  @override
  List<GeneratedColumn> get $columns => [
        id,
        owner,
        from,
        body,
        filePath,
        fileUrl,
        fileFragment,
        thumbnailPath,
        thumbnailUrl,
        thumbnailFragment,
        selfDestruct,
        undo,
        undoEdit,
        time,
        type,
        chatType,
        callState,
        state,
        direction,
        fileState,
        thumbnailFileState,
        msgId,
        uuid,
        fileName,
        fileSize,
        ext1,
        ackNumber,
        read,
        replayMsg,
        margeMsg,
        contactMsg,
        shareMsg,
        createTime,
        at,
        updateTime,
        resourceUuid,
        hasShown,
        messageHasRead,
        fileDuration,
        userNameFileHelper,
        noises,
        displayName,
        expand
      ];
  @override
  String get aliasedName => _alias ?? 'message_top_admin';
  @override
  String get actualTableName => 'message_top_admin';
  @override
  VerificationContext validateIntegrity(
      Insertable<MessageTopAdminData> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    }
    if (data.containsKey('owner')) {
      context.handle(
          _ownerMeta, owner.isAcceptableOrUnknown(data['owner']!, _ownerMeta));
    }
    if (data.containsKey('from')) {
      context.handle(
          _fromMeta, from.isAcceptableOrUnknown(data['from']!, _fromMeta));
    }
    if (data.containsKey('body')) {
      context.handle(
          _bodyMeta, body.isAcceptableOrUnknown(data['body']!, _bodyMeta));
    }
    if (data.containsKey('file_path')) {
      context.handle(_filePathMeta,
          filePath.isAcceptableOrUnknown(data['file_path']!, _filePathMeta));
    }
    if (data.containsKey('file_url')) {
      context.handle(_fileUrlMeta,
          fileUrl.isAcceptableOrUnknown(data['file_url']!, _fileUrlMeta));
    }
    if (data.containsKey('file_fragment')) {
      context.handle(
          _fileFragmentMeta,
          fileFragment.isAcceptableOrUnknown(
              data['file_fragment']!, _fileFragmentMeta));
    }
    if (data.containsKey('thumbnail_path')) {
      context.handle(
          _thumbnailPathMeta,
          thumbnailPath.isAcceptableOrUnknown(
              data['thumbnail_path']!, _thumbnailPathMeta));
    }
    if (data.containsKey('thumbnail_url')) {
      context.handle(
          _thumbnailUrlMeta,
          thumbnailUrl.isAcceptableOrUnknown(
              data['thumbnail_url']!, _thumbnailUrlMeta));
    }
    if (data.containsKey('thumbnail_fragment')) {
      context.handle(
          _thumbnailFragmentMeta,
          thumbnailFragment.isAcceptableOrUnknown(
              data['thumbnail_fragment']!, _thumbnailFragmentMeta));
    }
    if (data.containsKey('self_destruct')) {
      context.handle(
          _selfDestructMeta,
          selfDestruct.isAcceptableOrUnknown(
              data['self_destruct']!, _selfDestructMeta));
    }
    if (data.containsKey('undo')) {
      context.handle(
          _undoMeta, undo.isAcceptableOrUnknown(data['undo']!, _undoMeta));
    }
    if (data.containsKey('undo_edit')) {
      context.handle(_undoEditMeta,
          undoEdit.isAcceptableOrUnknown(data['undo_edit']!, _undoEditMeta));
    }
    if (data.containsKey('time')) {
      context.handle(
          _timeMeta, time.isAcceptableOrUnknown(data['time']!, _timeMeta));
    }
    if (data.containsKey('type')) {
      context.handle(
          _typeMeta, type.isAcceptableOrUnknown(data['type']!, _typeMeta));
    }
    if (data.containsKey('chat_type')) {
      context.handle(_chatTypeMeta,
          chatType.isAcceptableOrUnknown(data['chat_type']!, _chatTypeMeta));
    }
    if (data.containsKey('call_state')) {
      context.handle(_callStateMeta,
          callState.isAcceptableOrUnknown(data['call_state']!, _callStateMeta));
    }
    if (data.containsKey('state')) {
      context.handle(
          _stateMeta, state.isAcceptableOrUnknown(data['state']!, _stateMeta));
    }
    if (data.containsKey('direction')) {
      context.handle(_directionMeta,
          direction.isAcceptableOrUnknown(data['direction']!, _directionMeta));
    }
    if (data.containsKey('file_state')) {
      context.handle(_fileStateMeta,
          fileState.isAcceptableOrUnknown(data['file_state']!, _fileStateMeta));
    }
    if (data.containsKey('thumbnail_file_state')) {
      context.handle(
          _thumbnailFileStateMeta,
          thumbnailFileState.isAcceptableOrUnknown(
              data['thumbnail_file_state']!, _thumbnailFileStateMeta));
    }
    if (data.containsKey('msg_id')) {
      context.handle(
          _msgIdMeta, msgId.isAcceptableOrUnknown(data['msg_id']!, _msgIdMeta));
    } else if (isInserting) {
      context.missing(_msgIdMeta);
    }
    if (data.containsKey('uuid')) {
      context.handle(
          _uuidMeta, uuid.isAcceptableOrUnknown(data['uuid']!, _uuidMeta));
    }
    if (data.containsKey('file_name')) {
      context.handle(_fileNameMeta,
          fileName.isAcceptableOrUnknown(data['file_name']!, _fileNameMeta));
    }
    if (data.containsKey('fileSize')) {
      context.handle(_fileSizeMeta,
          fileSize.isAcceptableOrUnknown(data['fileSize']!, _fileSizeMeta));
    }
    if (data.containsKey('ext1')) {
      context.handle(
          _ext1Meta, ext1.isAcceptableOrUnknown(data['ext1']!, _ext1Meta));
    }
    if (data.containsKey('ack_number')) {
      context.handle(_ackNumberMeta,
          ackNumber.isAcceptableOrUnknown(data['ack_number']!, _ackNumberMeta));
    }
    if (data.containsKey('read')) {
      context.handle(
          _readMeta, read.isAcceptableOrUnknown(data['read']!, _readMeta));
    }
    if (data.containsKey('replay_msg')) {
      context.handle(_replayMsgMeta,
          replayMsg.isAcceptableOrUnknown(data['replay_msg']!, _replayMsgMeta));
    }
    if (data.containsKey('marge_msg')) {
      context.handle(_margeMsgMeta,
          margeMsg.isAcceptableOrUnknown(data['marge_msg']!, _margeMsgMeta));
    }
    if (data.containsKey('contact_msg')) {
      context.handle(
          _contactMsgMeta,
          contactMsg.isAcceptableOrUnknown(
              data['contact_msg']!, _contactMsgMeta));
    }
    if (data.containsKey('share_msg')) {
      context.handle(_shareMsgMeta,
          shareMsg.isAcceptableOrUnknown(data['share_msg']!, _shareMsgMeta));
    }
    if (data.containsKey('create_time')) {
      context.handle(
          _createTimeMeta,
          createTime.isAcceptableOrUnknown(
              data['create_time']!, _createTimeMeta));
    }
    if (data.containsKey('at')) {
      context.handle(_atMeta, at.isAcceptableOrUnknown(data['at']!, _atMeta));
    }
    if (data.containsKey('update_time')) {
      context.handle(
          _updateTimeMeta,
          updateTime.isAcceptableOrUnknown(
              data['update_time']!, _updateTimeMeta));
    }
    if (data.containsKey('resource_uuid')) {
      context.handle(
          _resourceUuidMeta,
          resourceUuid.isAcceptableOrUnknown(
              data['resource_uuid']!, _resourceUuidMeta));
    }
    if (data.containsKey('has_shown')) {
      context.handle(_hasShownMeta,
          hasShown.isAcceptableOrUnknown(data['has_shown']!, _hasShownMeta));
    }
    if (data.containsKey('message_has_read')) {
      context.handle(
          _messageHasReadMeta,
          messageHasRead.isAcceptableOrUnknown(
              data['message_has_read']!, _messageHasReadMeta));
    }
    if (data.containsKey('file_duration')) {
      context.handle(
          _fileDurationMeta,
          fileDuration.isAcceptableOrUnknown(
              data['file_duration']!, _fileDurationMeta));
    }
    if (data.containsKey('user_name_file_helper')) {
      context.handle(
          _userNameFileHelperMeta,
          userNameFileHelper.isAcceptableOrUnknown(
              data['user_name_file_helper']!, _userNameFileHelperMeta));
    }
    if (data.containsKey('noises')) {
      context.handle(_noisesMeta,
          noises.isAcceptableOrUnknown(data['noises']!, _noisesMeta));
    }
    if (data.containsKey('display_name')) {
      context.handle(
          _displayNameMeta,
          displayName.isAcceptableOrUnknown(
              data['display_name']!, _displayNameMeta));
    }
    if (data.containsKey('expand')) {
      context.handle(_expandMeta,
          expand.isAcceptableOrUnknown(data['expand']!, _expandMeta));
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  MessageTopAdminData map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return MessageTopAdminData(
      id: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}id'])!,
      owner: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}owner']),
      from: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}from']),
      body: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}body']),
      filePath: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}file_path']),
      fileUrl: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}file_url']),
      fileFragment: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}file_fragment']),
      thumbnailPath: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}thumbnail_path']),
      thumbnailUrl: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}thumbnail_url']),
      thumbnailFragment: attachedDatabase.typeMapping.read(
          DriftSqlType.string, data['${effectivePrefix}thumbnail_fragment']),
      selfDestruct: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}self_destruct']),
      undo: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}undo']),
      undoEdit: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}undo_edit']),
      time: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}time']),
      type: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}type']),
      chatType: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}chat_type']),
      callState: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}call_state']),
      state: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}state']),
      direction: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}direction']),
      fileState: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}file_state']),
      thumbnailFileState: attachedDatabase.typeMapping.read(
          DriftSqlType.int, data['${effectivePrefix}thumbnail_file_state']),
      msgId: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}msg_id'])!,
      uuid: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}uuid']),
      fileName: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}file_name']),
      fileSize: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}fileSize']),
      ext1: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}ext1']),
      ackNumber: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}ack_number']),
      read: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}read']),
      replayMsg: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}replay_msg']),
      margeMsg: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}marge_msg']),
      contactMsg: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}contact_msg']),
      shareMsg: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}share_msg']),
      createTime: attachedDatabase.typeMapping
          .read(DriftSqlType.double, data['${effectivePrefix}create_time']),
      at: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}at']),
      updateTime: attachedDatabase.typeMapping
          .read(DriftSqlType.double, data['${effectivePrefix}update_time']),
      resourceUuid: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}resource_uuid']),
      hasShown: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}has_shown']),
      messageHasRead: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}message_has_read']),
      fileDuration: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}file_duration']),
      userNameFileHelper: attachedDatabase.typeMapping.read(
          DriftSqlType.string, data['${effectivePrefix}user_name_file_helper']),
      noises: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}noises']),
      displayName: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}display_name']),
      expand: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}expand']),
    );
  }

  @override
  MessageTopAdmin createAlias(String alias) {
    return MessageTopAdmin(attachedDatabase, alias);
  }

  @override
  bool get dontWriteConstraints => true;
}

class MessageTopAdminData extends DataClass
    implements Insertable<MessageTopAdminData> {
  final int id;
  final String? owner;
  final String? from;
  final String? body;
  final String? filePath;
  final String? fileUrl;
  final String? fileFragment;
  final String? thumbnailPath;
  final String? thumbnailUrl;
  final String? thumbnailFragment;
  final bool? selfDestruct;
  final bool? undo;
  final bool? undoEdit;
  final int? time;
  final int? type;
  final int? chatType;
  final int? callState;
  final int? state;
  final int? direction;
  final int? fileState;
  final int? thumbnailFileState;
  final String msgId;
  final String? uuid;
  final String? fileName;
  final int? fileSize;
  final String? ext1;
  final int? ackNumber;
  final bool? read;
  final String? replayMsg;
  final String? margeMsg;
  final String? contactMsg;
  final String? shareMsg;
  final double? createTime;
  final String? at;
  final double? updateTime;
  final String? resourceUuid;
  final bool? hasShown;
  final bool? messageHasRead;
  final int? fileDuration;
  final String? userNameFileHelper;
  final String? noises;
  final String? displayName;
  final String? expand;
  const MessageTopAdminData(
      {required this.id,
      this.owner,
      this.from,
      this.body,
      this.filePath,
      this.fileUrl,
      this.fileFragment,
      this.thumbnailPath,
      this.thumbnailUrl,
      this.thumbnailFragment,
      this.selfDestruct,
      this.undo,
      this.undoEdit,
      this.time,
      this.type,
      this.chatType,
      this.callState,
      this.state,
      this.direction,
      this.fileState,
      this.thumbnailFileState,
      required this.msgId,
      this.uuid,
      this.fileName,
      this.fileSize,
      this.ext1,
      this.ackNumber,
      this.read,
      this.replayMsg,
      this.margeMsg,
      this.contactMsg,
      this.shareMsg,
      this.createTime,
      this.at,
      this.updateTime,
      this.resourceUuid,
      this.hasShown,
      this.messageHasRead,
      this.fileDuration,
      this.userNameFileHelper,
      this.noises,
      this.displayName,
      this.expand});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<int>(id);
    if (!nullToAbsent || owner != null) {
      map['owner'] = Variable<String>(owner);
    }
    if (!nullToAbsent || from != null) {
      map['from'] = Variable<String>(from);
    }
    if (!nullToAbsent || body != null) {
      map['body'] = Variable<String>(body);
    }
    if (!nullToAbsent || filePath != null) {
      map['file_path'] = Variable<String>(filePath);
    }
    if (!nullToAbsent || fileUrl != null) {
      map['file_url'] = Variable<String>(fileUrl);
    }
    if (!nullToAbsent || fileFragment != null) {
      map['file_fragment'] = Variable<String>(fileFragment);
    }
    if (!nullToAbsent || thumbnailPath != null) {
      map['thumbnail_path'] = Variable<String>(thumbnailPath);
    }
    if (!nullToAbsent || thumbnailUrl != null) {
      map['thumbnail_url'] = Variable<String>(thumbnailUrl);
    }
    if (!nullToAbsent || thumbnailFragment != null) {
      map['thumbnail_fragment'] = Variable<String>(thumbnailFragment);
    }
    if (!nullToAbsent || selfDestruct != null) {
      map['self_destruct'] = Variable<bool>(selfDestruct);
    }
    if (!nullToAbsent || undo != null) {
      map['undo'] = Variable<bool>(undo);
    }
    if (!nullToAbsent || undoEdit != null) {
      map['undo_edit'] = Variable<bool>(undoEdit);
    }
    if (!nullToAbsent || time != null) {
      map['time'] = Variable<int>(time);
    }
    if (!nullToAbsent || type != null) {
      map['type'] = Variable<int>(type);
    }
    if (!nullToAbsent || chatType != null) {
      map['chat_type'] = Variable<int>(chatType);
    }
    if (!nullToAbsent || callState != null) {
      map['call_state'] = Variable<int>(callState);
    }
    if (!nullToAbsent || state != null) {
      map['state'] = Variable<int>(state);
    }
    if (!nullToAbsent || direction != null) {
      map['direction'] = Variable<int>(direction);
    }
    if (!nullToAbsent || fileState != null) {
      map['file_state'] = Variable<int>(fileState);
    }
    if (!nullToAbsent || thumbnailFileState != null) {
      map['thumbnail_file_state'] = Variable<int>(thumbnailFileState);
    }
    map['msg_id'] = Variable<String>(msgId);
    if (!nullToAbsent || uuid != null) {
      map['uuid'] = Variable<String>(uuid);
    }
    if (!nullToAbsent || fileName != null) {
      map['file_name'] = Variable<String>(fileName);
    }
    if (!nullToAbsent || fileSize != null) {
      map['fileSize'] = Variable<int>(fileSize);
    }
    if (!nullToAbsent || ext1 != null) {
      map['ext1'] = Variable<String>(ext1);
    }
    if (!nullToAbsent || ackNumber != null) {
      map['ack_number'] = Variable<int>(ackNumber);
    }
    if (!nullToAbsent || read != null) {
      map['read'] = Variable<bool>(read);
    }
    if (!nullToAbsent || replayMsg != null) {
      map['replay_msg'] = Variable<String>(replayMsg);
    }
    if (!nullToAbsent || margeMsg != null) {
      map['marge_msg'] = Variable<String>(margeMsg);
    }
    if (!nullToAbsent || contactMsg != null) {
      map['contact_msg'] = Variable<String>(contactMsg);
    }
    if (!nullToAbsent || shareMsg != null) {
      map['share_msg'] = Variable<String>(shareMsg);
    }
    if (!nullToAbsent || createTime != null) {
      map['create_time'] = Variable<double>(createTime);
    }
    if (!nullToAbsent || at != null) {
      map['at'] = Variable<String>(at);
    }
    if (!nullToAbsent || updateTime != null) {
      map['update_time'] = Variable<double>(updateTime);
    }
    if (!nullToAbsent || resourceUuid != null) {
      map['resource_uuid'] = Variable<String>(resourceUuid);
    }
    if (!nullToAbsent || hasShown != null) {
      map['has_shown'] = Variable<bool>(hasShown);
    }
    if (!nullToAbsent || messageHasRead != null) {
      map['message_has_read'] = Variable<bool>(messageHasRead);
    }
    if (!nullToAbsent || fileDuration != null) {
      map['file_duration'] = Variable<int>(fileDuration);
    }
    if (!nullToAbsent || userNameFileHelper != null) {
      map['user_name_file_helper'] = Variable<String>(userNameFileHelper);
    }
    if (!nullToAbsent || noises != null) {
      map['noises'] = Variable<String>(noises);
    }
    if (!nullToAbsent || displayName != null) {
      map['display_name'] = Variable<String>(displayName);
    }
    if (!nullToAbsent || expand != null) {
      map['expand'] = Variable<String>(expand);
    }
    return map;
  }

  MessageTopAdminCompanion toCompanion(bool nullToAbsent) {
    return MessageTopAdminCompanion(
      id: Value(id),
      owner:
          owner == null && nullToAbsent ? const Value.absent() : Value(owner),
      from: from == null && nullToAbsent ? const Value.absent() : Value(from),
      body: body == null && nullToAbsent ? const Value.absent() : Value(body),
      filePath: filePath == null && nullToAbsent
          ? const Value.absent()
          : Value(filePath),
      fileUrl: fileUrl == null && nullToAbsent
          ? const Value.absent()
          : Value(fileUrl),
      fileFragment: fileFragment == null && nullToAbsent
          ? const Value.absent()
          : Value(fileFragment),
      thumbnailPath: thumbnailPath == null && nullToAbsent
          ? const Value.absent()
          : Value(thumbnailPath),
      thumbnailUrl: thumbnailUrl == null && nullToAbsent
          ? const Value.absent()
          : Value(thumbnailUrl),
      thumbnailFragment: thumbnailFragment == null && nullToAbsent
          ? const Value.absent()
          : Value(thumbnailFragment),
      selfDestruct: selfDestruct == null && nullToAbsent
          ? const Value.absent()
          : Value(selfDestruct),
      undo: undo == null && nullToAbsent ? const Value.absent() : Value(undo),
      undoEdit: undoEdit == null && nullToAbsent
          ? const Value.absent()
          : Value(undoEdit),
      time: time == null && nullToAbsent ? const Value.absent() : Value(time),
      type: type == null && nullToAbsent ? const Value.absent() : Value(type),
      chatType: chatType == null && nullToAbsent
          ? const Value.absent()
          : Value(chatType),
      callState: callState == null && nullToAbsent
          ? const Value.absent()
          : Value(callState),
      state:
          state == null && nullToAbsent ? const Value.absent() : Value(state),
      direction: direction == null && nullToAbsent
          ? const Value.absent()
          : Value(direction),
      fileState: fileState == null && nullToAbsent
          ? const Value.absent()
          : Value(fileState),
      thumbnailFileState: thumbnailFileState == null && nullToAbsent
          ? const Value.absent()
          : Value(thumbnailFileState),
      msgId: Value(msgId),
      uuid: uuid == null && nullToAbsent ? const Value.absent() : Value(uuid),
      fileName: fileName == null && nullToAbsent
          ? const Value.absent()
          : Value(fileName),
      fileSize: fileSize == null && nullToAbsent
          ? const Value.absent()
          : Value(fileSize),
      ext1: ext1 == null && nullToAbsent ? const Value.absent() : Value(ext1),
      ackNumber: ackNumber == null && nullToAbsent
          ? const Value.absent()
          : Value(ackNumber),
      read: read == null && nullToAbsent ? const Value.absent() : Value(read),
      replayMsg: replayMsg == null && nullToAbsent
          ? const Value.absent()
          : Value(replayMsg),
      margeMsg: margeMsg == null && nullToAbsent
          ? const Value.absent()
          : Value(margeMsg),
      contactMsg: contactMsg == null && nullToAbsent
          ? const Value.absent()
          : Value(contactMsg),
      shareMsg: shareMsg == null && nullToAbsent
          ? const Value.absent()
          : Value(shareMsg),
      createTime: createTime == null && nullToAbsent
          ? const Value.absent()
          : Value(createTime),
      at: at == null && nullToAbsent ? const Value.absent() : Value(at),
      updateTime: updateTime == null && nullToAbsent
          ? const Value.absent()
          : Value(updateTime),
      resourceUuid: resourceUuid == null && nullToAbsent
          ? const Value.absent()
          : Value(resourceUuid),
      hasShown: hasShown == null && nullToAbsent
          ? const Value.absent()
          : Value(hasShown),
      messageHasRead: messageHasRead == null && nullToAbsent
          ? const Value.absent()
          : Value(messageHasRead),
      fileDuration: fileDuration == null && nullToAbsent
          ? const Value.absent()
          : Value(fileDuration),
      userNameFileHelper: userNameFileHelper == null && nullToAbsent
          ? const Value.absent()
          : Value(userNameFileHelper),
      noises:
          noises == null && nullToAbsent ? const Value.absent() : Value(noises),
      displayName: displayName == null && nullToAbsent
          ? const Value.absent()
          : Value(displayName),
      expand:
          expand == null && nullToAbsent ? const Value.absent() : Value(expand),
    );
  }

  factory MessageTopAdminData.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return MessageTopAdminData(
      id: serializer.fromJson<int>(json['id']),
      owner: serializer.fromJson<String?>(json['owner']),
      from: serializer.fromJson<String?>(json['from']),
      body: serializer.fromJson<String?>(json['body']),
      filePath: serializer.fromJson<String?>(json['file_path']),
      fileUrl: serializer.fromJson<String?>(json['file_url']),
      fileFragment: serializer.fromJson<String?>(json['file_fragment']),
      thumbnailPath: serializer.fromJson<String?>(json['thumbnail_path']),
      thumbnailUrl: serializer.fromJson<String?>(json['thumbnail_url']),
      thumbnailFragment:
          serializer.fromJson<String?>(json['thumbnail_fragment']),
      selfDestruct: serializer.fromJson<bool?>(json['self_destruct']),
      undo: serializer.fromJson<bool?>(json['undo']),
      undoEdit: serializer.fromJson<bool?>(json['undo_edit']),
      time: serializer.fromJson<int?>(json['time']),
      type: serializer.fromJson<int?>(json['type']),
      chatType: serializer.fromJson<int?>(json['chat_type']),
      callState: serializer.fromJson<int?>(json['call_state']),
      state: serializer.fromJson<int?>(json['state']),
      direction: serializer.fromJson<int?>(json['direction']),
      fileState: serializer.fromJson<int?>(json['file_state']),
      thumbnailFileState:
          serializer.fromJson<int?>(json['thumbnail_file_state']),
      msgId: serializer.fromJson<String>(json['msg_id']),
      uuid: serializer.fromJson<String?>(json['uuid']),
      fileName: serializer.fromJson<String?>(json['file_name']),
      fileSize: serializer.fromJson<int?>(json['fileSize']),
      ext1: serializer.fromJson<String?>(json['ext1']),
      ackNumber: serializer.fromJson<int?>(json['ack_number']),
      read: serializer.fromJson<bool?>(json['read']),
      replayMsg: serializer.fromJson<String?>(json['replay_msg']),
      margeMsg: serializer.fromJson<String?>(json['marge_msg']),
      contactMsg: serializer.fromJson<String?>(json['contact_msg']),
      shareMsg: serializer.fromJson<String?>(json['share_msg']),
      createTime: serializer.fromJson<double?>(json['create_time']),
      at: serializer.fromJson<String?>(json['at']),
      updateTime: serializer.fromJson<double?>(json['update_time']),
      resourceUuid: serializer.fromJson<String?>(json['resource_uuid']),
      hasShown: serializer.fromJson<bool?>(json['has_shown']),
      messageHasRead: serializer.fromJson<bool?>(json['message_has_read']),
      fileDuration: serializer.fromJson<int?>(json['file_duration']),
      userNameFileHelper:
          serializer.fromJson<String?>(json['user_name_file_helper']),
      noises: serializer.fromJson<String?>(json['noises']),
      displayName: serializer.fromJson<String?>(json['display_name']),
      expand: serializer.fromJson<String?>(json['expand']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<int>(id),
      'owner': serializer.toJson<String?>(owner),
      'from': serializer.toJson<String?>(from),
      'body': serializer.toJson<String?>(body),
      'file_path': serializer.toJson<String?>(filePath),
      'file_url': serializer.toJson<String?>(fileUrl),
      'file_fragment': serializer.toJson<String?>(fileFragment),
      'thumbnail_path': serializer.toJson<String?>(thumbnailPath),
      'thumbnail_url': serializer.toJson<String?>(thumbnailUrl),
      'thumbnail_fragment': serializer.toJson<String?>(thumbnailFragment),
      'self_destruct': serializer.toJson<bool?>(selfDestruct),
      'undo': serializer.toJson<bool?>(undo),
      'undo_edit': serializer.toJson<bool?>(undoEdit),
      'time': serializer.toJson<int?>(time),
      'type': serializer.toJson<int?>(type),
      'chat_type': serializer.toJson<int?>(chatType),
      'call_state': serializer.toJson<int?>(callState),
      'state': serializer.toJson<int?>(state),
      'direction': serializer.toJson<int?>(direction),
      'file_state': serializer.toJson<int?>(fileState),
      'thumbnail_file_state': serializer.toJson<int?>(thumbnailFileState),
      'msg_id': serializer.toJson<String>(msgId),
      'uuid': serializer.toJson<String?>(uuid),
      'file_name': serializer.toJson<String?>(fileName),
      'fileSize': serializer.toJson<int?>(fileSize),
      'ext1': serializer.toJson<String?>(ext1),
      'ack_number': serializer.toJson<int?>(ackNumber),
      'read': serializer.toJson<bool?>(read),
      'replay_msg': serializer.toJson<String?>(replayMsg),
      'marge_msg': serializer.toJson<String?>(margeMsg),
      'contact_msg': serializer.toJson<String?>(contactMsg),
      'share_msg': serializer.toJson<String?>(shareMsg),
      'create_time': serializer.toJson<double?>(createTime),
      'at': serializer.toJson<String?>(at),
      'update_time': serializer.toJson<double?>(updateTime),
      'resource_uuid': serializer.toJson<String?>(resourceUuid),
      'has_shown': serializer.toJson<bool?>(hasShown),
      'message_has_read': serializer.toJson<bool?>(messageHasRead),
      'file_duration': serializer.toJson<int?>(fileDuration),
      'user_name_file_helper': serializer.toJson<String?>(userNameFileHelper),
      'noises': serializer.toJson<String?>(noises),
      'display_name': serializer.toJson<String?>(displayName),
      'expand': serializer.toJson<String?>(expand),
    };
  }

  MessageTopAdminData copyWith(
          {int? id,
          Value<String?> owner = const Value.absent(),
          Value<String?> from = const Value.absent(),
          Value<String?> body = const Value.absent(),
          Value<String?> filePath = const Value.absent(),
          Value<String?> fileUrl = const Value.absent(),
          Value<String?> fileFragment = const Value.absent(),
          Value<String?> thumbnailPath = const Value.absent(),
          Value<String?> thumbnailUrl = const Value.absent(),
          Value<String?> thumbnailFragment = const Value.absent(),
          Value<bool?> selfDestruct = const Value.absent(),
          Value<bool?> undo = const Value.absent(),
          Value<bool?> undoEdit = const Value.absent(),
          Value<int?> time = const Value.absent(),
          Value<int?> type = const Value.absent(),
          Value<int?> chatType = const Value.absent(),
          Value<int?> callState = const Value.absent(),
          Value<int?> state = const Value.absent(),
          Value<int?> direction = const Value.absent(),
          Value<int?> fileState = const Value.absent(),
          Value<int?> thumbnailFileState = const Value.absent(),
          String? msgId,
          Value<String?> uuid = const Value.absent(),
          Value<String?> fileName = const Value.absent(),
          Value<int?> fileSize = const Value.absent(),
          Value<String?> ext1 = const Value.absent(),
          Value<int?> ackNumber = const Value.absent(),
          Value<bool?> read = const Value.absent(),
          Value<String?> replayMsg = const Value.absent(),
          Value<String?> margeMsg = const Value.absent(),
          Value<String?> contactMsg = const Value.absent(),
          Value<String?> shareMsg = const Value.absent(),
          Value<double?> createTime = const Value.absent(),
          Value<String?> at = const Value.absent(),
          Value<double?> updateTime = const Value.absent(),
          Value<String?> resourceUuid = const Value.absent(),
          Value<bool?> hasShown = const Value.absent(),
          Value<bool?> messageHasRead = const Value.absent(),
          Value<int?> fileDuration = const Value.absent(),
          Value<String?> userNameFileHelper = const Value.absent(),
          Value<String?> noises = const Value.absent(),
          Value<String?> displayName = const Value.absent(),
          Value<String?> expand = const Value.absent()}) =>
      MessageTopAdminData(
        id: id ?? this.id,
        owner: owner.present ? owner.value : this.owner,
        from: from.present ? from.value : this.from,
        body: body.present ? body.value : this.body,
        filePath: filePath.present ? filePath.value : this.filePath,
        fileUrl: fileUrl.present ? fileUrl.value : this.fileUrl,
        fileFragment:
            fileFragment.present ? fileFragment.value : this.fileFragment,
        thumbnailPath:
            thumbnailPath.present ? thumbnailPath.value : this.thumbnailPath,
        thumbnailUrl:
            thumbnailUrl.present ? thumbnailUrl.value : this.thumbnailUrl,
        thumbnailFragment: thumbnailFragment.present
            ? thumbnailFragment.value
            : this.thumbnailFragment,
        selfDestruct:
            selfDestruct.present ? selfDestruct.value : this.selfDestruct,
        undo: undo.present ? undo.value : this.undo,
        undoEdit: undoEdit.present ? undoEdit.value : this.undoEdit,
        time: time.present ? time.value : this.time,
        type: type.present ? type.value : this.type,
        chatType: chatType.present ? chatType.value : this.chatType,
        callState: callState.present ? callState.value : this.callState,
        state: state.present ? state.value : this.state,
        direction: direction.present ? direction.value : this.direction,
        fileState: fileState.present ? fileState.value : this.fileState,
        thumbnailFileState: thumbnailFileState.present
            ? thumbnailFileState.value
            : this.thumbnailFileState,
        msgId: msgId ?? this.msgId,
        uuid: uuid.present ? uuid.value : this.uuid,
        fileName: fileName.present ? fileName.value : this.fileName,
        fileSize: fileSize.present ? fileSize.value : this.fileSize,
        ext1: ext1.present ? ext1.value : this.ext1,
        ackNumber: ackNumber.present ? ackNumber.value : this.ackNumber,
        read: read.present ? read.value : this.read,
        replayMsg: replayMsg.present ? replayMsg.value : this.replayMsg,
        margeMsg: margeMsg.present ? margeMsg.value : this.margeMsg,
        contactMsg: contactMsg.present ? contactMsg.value : this.contactMsg,
        shareMsg: shareMsg.present ? shareMsg.value : this.shareMsg,
        createTime: createTime.present ? createTime.value : this.createTime,
        at: at.present ? at.value : this.at,
        updateTime: updateTime.present ? updateTime.value : this.updateTime,
        resourceUuid:
            resourceUuid.present ? resourceUuid.value : this.resourceUuid,
        hasShown: hasShown.present ? hasShown.value : this.hasShown,
        messageHasRead:
            messageHasRead.present ? messageHasRead.value : this.messageHasRead,
        fileDuration:
            fileDuration.present ? fileDuration.value : this.fileDuration,
        userNameFileHelper: userNameFileHelper.present
            ? userNameFileHelper.value
            : this.userNameFileHelper,
        noises: noises.present ? noises.value : this.noises,
        displayName: displayName.present ? displayName.value : this.displayName,
        expand: expand.present ? expand.value : this.expand,
      );
  @override
  String toString() {
    return (StringBuffer('MessageTopAdminData(')
          ..write('id: $id, ')
          ..write('owner: $owner, ')
          ..write('from: $from, ')
          ..write('body: $body, ')
          ..write('filePath: $filePath, ')
          ..write('fileUrl: $fileUrl, ')
          ..write('fileFragment: $fileFragment, ')
          ..write('thumbnailPath: $thumbnailPath, ')
          ..write('thumbnailUrl: $thumbnailUrl, ')
          ..write('thumbnailFragment: $thumbnailFragment, ')
          ..write('selfDestruct: $selfDestruct, ')
          ..write('undo: $undo, ')
          ..write('undoEdit: $undoEdit, ')
          ..write('time: $time, ')
          ..write('type: $type, ')
          ..write('chatType: $chatType, ')
          ..write('callState: $callState, ')
          ..write('state: $state, ')
          ..write('direction: $direction, ')
          ..write('fileState: $fileState, ')
          ..write('thumbnailFileState: $thumbnailFileState, ')
          ..write('msgId: $msgId, ')
          ..write('uuid: $uuid, ')
          ..write('fileName: $fileName, ')
          ..write('fileSize: $fileSize, ')
          ..write('ext1: $ext1, ')
          ..write('ackNumber: $ackNumber, ')
          ..write('read: $read, ')
          ..write('replayMsg: $replayMsg, ')
          ..write('margeMsg: $margeMsg, ')
          ..write('contactMsg: $contactMsg, ')
          ..write('shareMsg: $shareMsg, ')
          ..write('createTime: $createTime, ')
          ..write('at: $at, ')
          ..write('updateTime: $updateTime, ')
          ..write('resourceUuid: $resourceUuid, ')
          ..write('hasShown: $hasShown, ')
          ..write('messageHasRead: $messageHasRead, ')
          ..write('fileDuration: $fileDuration, ')
          ..write('userNameFileHelper: $userNameFileHelper, ')
          ..write('noises: $noises, ')
          ..write('displayName: $displayName, ')
          ..write('expand: $expand')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hashAll([
        id,
        owner,
        from,
        body,
        filePath,
        fileUrl,
        fileFragment,
        thumbnailPath,
        thumbnailUrl,
        thumbnailFragment,
        selfDestruct,
        undo,
        undoEdit,
        time,
        type,
        chatType,
        callState,
        state,
        direction,
        fileState,
        thumbnailFileState,
        msgId,
        uuid,
        fileName,
        fileSize,
        ext1,
        ackNumber,
        read,
        replayMsg,
        margeMsg,
        contactMsg,
        shareMsg,
        createTime,
        at,
        updateTime,
        resourceUuid,
        hasShown,
        messageHasRead,
        fileDuration,
        userNameFileHelper,
        noises,
        displayName,
        expand
      ]);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is MessageTopAdminData &&
          other.id == this.id &&
          other.owner == this.owner &&
          other.from == this.from &&
          other.body == this.body &&
          other.filePath == this.filePath &&
          other.fileUrl == this.fileUrl &&
          other.fileFragment == this.fileFragment &&
          other.thumbnailPath == this.thumbnailPath &&
          other.thumbnailUrl == this.thumbnailUrl &&
          other.thumbnailFragment == this.thumbnailFragment &&
          other.selfDestruct == this.selfDestruct &&
          other.undo == this.undo &&
          other.undoEdit == this.undoEdit &&
          other.time == this.time &&
          other.type == this.type &&
          other.chatType == this.chatType &&
          other.callState == this.callState &&
          other.state == this.state &&
          other.direction == this.direction &&
          other.fileState == this.fileState &&
          other.thumbnailFileState == this.thumbnailFileState &&
          other.msgId == this.msgId &&
          other.uuid == this.uuid &&
          other.fileName == this.fileName &&
          other.fileSize == this.fileSize &&
          other.ext1 == this.ext1 &&
          other.ackNumber == this.ackNumber &&
          other.read == this.read &&
          other.replayMsg == this.replayMsg &&
          other.margeMsg == this.margeMsg &&
          other.contactMsg == this.contactMsg &&
          other.shareMsg == this.shareMsg &&
          other.createTime == this.createTime &&
          other.at == this.at &&
          other.updateTime == this.updateTime &&
          other.resourceUuid == this.resourceUuid &&
          other.hasShown == this.hasShown &&
          other.messageHasRead == this.messageHasRead &&
          other.fileDuration == this.fileDuration &&
          other.userNameFileHelper == this.userNameFileHelper &&
          other.noises == this.noises &&
          other.displayName == this.displayName &&
          other.expand == this.expand);
}

class MessageTopAdminCompanion extends UpdateCompanion<MessageTopAdminData> {
  final Value<int> id;
  final Value<String?> owner;
  final Value<String?> from;
  final Value<String?> body;
  final Value<String?> filePath;
  final Value<String?> fileUrl;
  final Value<String?> fileFragment;
  final Value<String?> thumbnailPath;
  final Value<String?> thumbnailUrl;
  final Value<String?> thumbnailFragment;
  final Value<bool?> selfDestruct;
  final Value<bool?> undo;
  final Value<bool?> undoEdit;
  final Value<int?> time;
  final Value<int?> type;
  final Value<int?> chatType;
  final Value<int?> callState;
  final Value<int?> state;
  final Value<int?> direction;
  final Value<int?> fileState;
  final Value<int?> thumbnailFileState;
  final Value<String> msgId;
  final Value<String?> uuid;
  final Value<String?> fileName;
  final Value<int?> fileSize;
  final Value<String?> ext1;
  final Value<int?> ackNumber;
  final Value<bool?> read;
  final Value<String?> replayMsg;
  final Value<String?> margeMsg;
  final Value<String?> contactMsg;
  final Value<String?> shareMsg;
  final Value<double?> createTime;
  final Value<String?> at;
  final Value<double?> updateTime;
  final Value<String?> resourceUuid;
  final Value<bool?> hasShown;
  final Value<bool?> messageHasRead;
  final Value<int?> fileDuration;
  final Value<String?> userNameFileHelper;
  final Value<String?> noises;
  final Value<String?> displayName;
  final Value<String?> expand;
  const MessageTopAdminCompanion({
    this.id = const Value.absent(),
    this.owner = const Value.absent(),
    this.from = const Value.absent(),
    this.body = const Value.absent(),
    this.filePath = const Value.absent(),
    this.fileUrl = const Value.absent(),
    this.fileFragment = const Value.absent(),
    this.thumbnailPath = const Value.absent(),
    this.thumbnailUrl = const Value.absent(),
    this.thumbnailFragment = const Value.absent(),
    this.selfDestruct = const Value.absent(),
    this.undo = const Value.absent(),
    this.undoEdit = const Value.absent(),
    this.time = const Value.absent(),
    this.type = const Value.absent(),
    this.chatType = const Value.absent(),
    this.callState = const Value.absent(),
    this.state = const Value.absent(),
    this.direction = const Value.absent(),
    this.fileState = const Value.absent(),
    this.thumbnailFileState = const Value.absent(),
    this.msgId = const Value.absent(),
    this.uuid = const Value.absent(),
    this.fileName = const Value.absent(),
    this.fileSize = const Value.absent(),
    this.ext1 = const Value.absent(),
    this.ackNumber = const Value.absent(),
    this.read = const Value.absent(),
    this.replayMsg = const Value.absent(),
    this.margeMsg = const Value.absent(),
    this.contactMsg = const Value.absent(),
    this.shareMsg = const Value.absent(),
    this.createTime = const Value.absent(),
    this.at = const Value.absent(),
    this.updateTime = const Value.absent(),
    this.resourceUuid = const Value.absent(),
    this.hasShown = const Value.absent(),
    this.messageHasRead = const Value.absent(),
    this.fileDuration = const Value.absent(),
    this.userNameFileHelper = const Value.absent(),
    this.noises = const Value.absent(),
    this.displayName = const Value.absent(),
    this.expand = const Value.absent(),
  });
  MessageTopAdminCompanion.insert({
    this.id = const Value.absent(),
    this.owner = const Value.absent(),
    this.from = const Value.absent(),
    this.body = const Value.absent(),
    this.filePath = const Value.absent(),
    this.fileUrl = const Value.absent(),
    this.fileFragment = const Value.absent(),
    this.thumbnailPath = const Value.absent(),
    this.thumbnailUrl = const Value.absent(),
    this.thumbnailFragment = const Value.absent(),
    this.selfDestruct = const Value.absent(),
    this.undo = const Value.absent(),
    this.undoEdit = const Value.absent(),
    this.time = const Value.absent(),
    this.type = const Value.absent(),
    this.chatType = const Value.absent(),
    this.callState = const Value.absent(),
    this.state = const Value.absent(),
    this.direction = const Value.absent(),
    this.fileState = const Value.absent(),
    this.thumbnailFileState = const Value.absent(),
    required String msgId,
    this.uuid = const Value.absent(),
    this.fileName = const Value.absent(),
    this.fileSize = const Value.absent(),
    this.ext1 = const Value.absent(),
    this.ackNumber = const Value.absent(),
    this.read = const Value.absent(),
    this.replayMsg = const Value.absent(),
    this.margeMsg = const Value.absent(),
    this.contactMsg = const Value.absent(),
    this.shareMsg = const Value.absent(),
    this.createTime = const Value.absent(),
    this.at = const Value.absent(),
    this.updateTime = const Value.absent(),
    this.resourceUuid = const Value.absent(),
    this.hasShown = const Value.absent(),
    this.messageHasRead = const Value.absent(),
    this.fileDuration = const Value.absent(),
    this.userNameFileHelper = const Value.absent(),
    this.noises = const Value.absent(),
    this.displayName = const Value.absent(),
    this.expand = const Value.absent(),
  }) : msgId = Value(msgId);
  static Insertable<MessageTopAdminData> custom({
    Expression<int>? id,
    Expression<String>? owner,
    Expression<String>? from,
    Expression<String>? body,
    Expression<String>? filePath,
    Expression<String>? fileUrl,
    Expression<String>? fileFragment,
    Expression<String>? thumbnailPath,
    Expression<String>? thumbnailUrl,
    Expression<String>? thumbnailFragment,
    Expression<bool>? selfDestruct,
    Expression<bool>? undo,
    Expression<bool>? undoEdit,
    Expression<int>? time,
    Expression<int>? type,
    Expression<int>? chatType,
    Expression<int>? callState,
    Expression<int>? state,
    Expression<int>? direction,
    Expression<int>? fileState,
    Expression<int>? thumbnailFileState,
    Expression<String>? msgId,
    Expression<String>? uuid,
    Expression<String>? fileName,
    Expression<int>? fileSize,
    Expression<String>? ext1,
    Expression<int>? ackNumber,
    Expression<bool>? read,
    Expression<String>? replayMsg,
    Expression<String>? margeMsg,
    Expression<String>? contactMsg,
    Expression<String>? shareMsg,
    Expression<double>? createTime,
    Expression<String>? at,
    Expression<double>? updateTime,
    Expression<String>? resourceUuid,
    Expression<bool>? hasShown,
    Expression<bool>? messageHasRead,
    Expression<int>? fileDuration,
    Expression<String>? userNameFileHelper,
    Expression<String>? noises,
    Expression<String>? displayName,
    Expression<String>? expand,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (owner != null) 'owner': owner,
      if (from != null) 'from': from,
      if (body != null) 'body': body,
      if (filePath != null) 'file_path': filePath,
      if (fileUrl != null) 'file_url': fileUrl,
      if (fileFragment != null) 'file_fragment': fileFragment,
      if (thumbnailPath != null) 'thumbnail_path': thumbnailPath,
      if (thumbnailUrl != null) 'thumbnail_url': thumbnailUrl,
      if (thumbnailFragment != null) 'thumbnail_fragment': thumbnailFragment,
      if (selfDestruct != null) 'self_destruct': selfDestruct,
      if (undo != null) 'undo': undo,
      if (undoEdit != null) 'undo_edit': undoEdit,
      if (time != null) 'time': time,
      if (type != null) 'type': type,
      if (chatType != null) 'chat_type': chatType,
      if (callState != null) 'call_state': callState,
      if (state != null) 'state': state,
      if (direction != null) 'direction': direction,
      if (fileState != null) 'file_state': fileState,
      if (thumbnailFileState != null)
        'thumbnail_file_state': thumbnailFileState,
      if (msgId != null) 'msg_id': msgId,
      if (uuid != null) 'uuid': uuid,
      if (fileName != null) 'file_name': fileName,
      if (fileSize != null) 'fileSize': fileSize,
      if (ext1 != null) 'ext1': ext1,
      if (ackNumber != null) 'ack_number': ackNumber,
      if (read != null) 'read': read,
      if (replayMsg != null) 'replay_msg': replayMsg,
      if (margeMsg != null) 'marge_msg': margeMsg,
      if (contactMsg != null) 'contact_msg': contactMsg,
      if (shareMsg != null) 'share_msg': shareMsg,
      if (createTime != null) 'create_time': createTime,
      if (at != null) 'at': at,
      if (updateTime != null) 'update_time': updateTime,
      if (resourceUuid != null) 'resource_uuid': resourceUuid,
      if (hasShown != null) 'has_shown': hasShown,
      if (messageHasRead != null) 'message_has_read': messageHasRead,
      if (fileDuration != null) 'file_duration': fileDuration,
      if (userNameFileHelper != null)
        'user_name_file_helper': userNameFileHelper,
      if (noises != null) 'noises': noises,
      if (displayName != null) 'display_name': displayName,
      if (expand != null) 'expand': expand,
    });
  }

  MessageTopAdminCompanion copyWith(
      {Value<int>? id,
      Value<String?>? owner,
      Value<String?>? from,
      Value<String?>? body,
      Value<String?>? filePath,
      Value<String?>? fileUrl,
      Value<String?>? fileFragment,
      Value<String?>? thumbnailPath,
      Value<String?>? thumbnailUrl,
      Value<String?>? thumbnailFragment,
      Value<bool?>? selfDestruct,
      Value<bool?>? undo,
      Value<bool?>? undoEdit,
      Value<int?>? time,
      Value<int?>? type,
      Value<int?>? chatType,
      Value<int?>? callState,
      Value<int?>? state,
      Value<int?>? direction,
      Value<int?>? fileState,
      Value<int?>? thumbnailFileState,
      Value<String>? msgId,
      Value<String?>? uuid,
      Value<String?>? fileName,
      Value<int?>? fileSize,
      Value<String?>? ext1,
      Value<int?>? ackNumber,
      Value<bool?>? read,
      Value<String?>? replayMsg,
      Value<String?>? margeMsg,
      Value<String?>? contactMsg,
      Value<String?>? shareMsg,
      Value<double?>? createTime,
      Value<String?>? at,
      Value<double?>? updateTime,
      Value<String?>? resourceUuid,
      Value<bool?>? hasShown,
      Value<bool?>? messageHasRead,
      Value<int?>? fileDuration,
      Value<String?>? userNameFileHelper,
      Value<String?>? noises,
      Value<String?>? displayName,
      Value<String?>? expand}) {
    return MessageTopAdminCompanion(
      id: id ?? this.id,
      owner: owner ?? this.owner,
      from: from ?? this.from,
      body: body ?? this.body,
      filePath: filePath ?? this.filePath,
      fileUrl: fileUrl ?? this.fileUrl,
      fileFragment: fileFragment ?? this.fileFragment,
      thumbnailPath: thumbnailPath ?? this.thumbnailPath,
      thumbnailUrl: thumbnailUrl ?? this.thumbnailUrl,
      thumbnailFragment: thumbnailFragment ?? this.thumbnailFragment,
      selfDestruct: selfDestruct ?? this.selfDestruct,
      undo: undo ?? this.undo,
      undoEdit: undoEdit ?? this.undoEdit,
      time: time ?? this.time,
      type: type ?? this.type,
      chatType: chatType ?? this.chatType,
      callState: callState ?? this.callState,
      state: state ?? this.state,
      direction: direction ?? this.direction,
      fileState: fileState ?? this.fileState,
      thumbnailFileState: thumbnailFileState ?? this.thumbnailFileState,
      msgId: msgId ?? this.msgId,
      uuid: uuid ?? this.uuid,
      fileName: fileName ?? this.fileName,
      fileSize: fileSize ?? this.fileSize,
      ext1: ext1 ?? this.ext1,
      ackNumber: ackNumber ?? this.ackNumber,
      read: read ?? this.read,
      replayMsg: replayMsg ?? this.replayMsg,
      margeMsg: margeMsg ?? this.margeMsg,
      contactMsg: contactMsg ?? this.contactMsg,
      shareMsg: shareMsg ?? this.shareMsg,
      createTime: createTime ?? this.createTime,
      at: at ?? this.at,
      updateTime: updateTime ?? this.updateTime,
      resourceUuid: resourceUuid ?? this.resourceUuid,
      hasShown: hasShown ?? this.hasShown,
      messageHasRead: messageHasRead ?? this.messageHasRead,
      fileDuration: fileDuration ?? this.fileDuration,
      userNameFileHelper: userNameFileHelper ?? this.userNameFileHelper,
      noises: noises ?? this.noises,
      displayName: displayName ?? this.displayName,
      expand: expand ?? this.expand,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<int>(id.value);
    }
    if (owner.present) {
      map['owner'] = Variable<String>(owner.value);
    }
    if (from.present) {
      map['from'] = Variable<String>(from.value);
    }
    if (body.present) {
      map['body'] = Variable<String>(body.value);
    }
    if (filePath.present) {
      map['file_path'] = Variable<String>(filePath.value);
    }
    if (fileUrl.present) {
      map['file_url'] = Variable<String>(fileUrl.value);
    }
    if (fileFragment.present) {
      map['file_fragment'] = Variable<String>(fileFragment.value);
    }
    if (thumbnailPath.present) {
      map['thumbnail_path'] = Variable<String>(thumbnailPath.value);
    }
    if (thumbnailUrl.present) {
      map['thumbnail_url'] = Variable<String>(thumbnailUrl.value);
    }
    if (thumbnailFragment.present) {
      map['thumbnail_fragment'] = Variable<String>(thumbnailFragment.value);
    }
    if (selfDestruct.present) {
      map['self_destruct'] = Variable<bool>(selfDestruct.value);
    }
    if (undo.present) {
      map['undo'] = Variable<bool>(undo.value);
    }
    if (undoEdit.present) {
      map['undo_edit'] = Variable<bool>(undoEdit.value);
    }
    if (time.present) {
      map['time'] = Variable<int>(time.value);
    }
    if (type.present) {
      map['type'] = Variable<int>(type.value);
    }
    if (chatType.present) {
      map['chat_type'] = Variable<int>(chatType.value);
    }
    if (callState.present) {
      map['call_state'] = Variable<int>(callState.value);
    }
    if (state.present) {
      map['state'] = Variable<int>(state.value);
    }
    if (direction.present) {
      map['direction'] = Variable<int>(direction.value);
    }
    if (fileState.present) {
      map['file_state'] = Variable<int>(fileState.value);
    }
    if (thumbnailFileState.present) {
      map['thumbnail_file_state'] = Variable<int>(thumbnailFileState.value);
    }
    if (msgId.present) {
      map['msg_id'] = Variable<String>(msgId.value);
    }
    if (uuid.present) {
      map['uuid'] = Variable<String>(uuid.value);
    }
    if (fileName.present) {
      map['file_name'] = Variable<String>(fileName.value);
    }
    if (fileSize.present) {
      map['fileSize'] = Variable<int>(fileSize.value);
    }
    if (ext1.present) {
      map['ext1'] = Variable<String>(ext1.value);
    }
    if (ackNumber.present) {
      map['ack_number'] = Variable<int>(ackNumber.value);
    }
    if (read.present) {
      map['read'] = Variable<bool>(read.value);
    }
    if (replayMsg.present) {
      map['replay_msg'] = Variable<String>(replayMsg.value);
    }
    if (margeMsg.present) {
      map['marge_msg'] = Variable<String>(margeMsg.value);
    }
    if (contactMsg.present) {
      map['contact_msg'] = Variable<String>(contactMsg.value);
    }
    if (shareMsg.present) {
      map['share_msg'] = Variable<String>(shareMsg.value);
    }
    if (createTime.present) {
      map['create_time'] = Variable<double>(createTime.value);
    }
    if (at.present) {
      map['at'] = Variable<String>(at.value);
    }
    if (updateTime.present) {
      map['update_time'] = Variable<double>(updateTime.value);
    }
    if (resourceUuid.present) {
      map['resource_uuid'] = Variable<String>(resourceUuid.value);
    }
    if (hasShown.present) {
      map['has_shown'] = Variable<bool>(hasShown.value);
    }
    if (messageHasRead.present) {
      map['message_has_read'] = Variable<bool>(messageHasRead.value);
    }
    if (fileDuration.present) {
      map['file_duration'] = Variable<int>(fileDuration.value);
    }
    if (userNameFileHelper.present) {
      map['user_name_file_helper'] = Variable<String>(userNameFileHelper.value);
    }
    if (noises.present) {
      map['noises'] = Variable<String>(noises.value);
    }
    if (displayName.present) {
      map['display_name'] = Variable<String>(displayName.value);
    }
    if (expand.present) {
      map['expand'] = Variable<String>(expand.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('MessageTopAdminCompanion(')
          ..write('id: $id, ')
          ..write('owner: $owner, ')
          ..write('from: $from, ')
          ..write('body: $body, ')
          ..write('filePath: $filePath, ')
          ..write('fileUrl: $fileUrl, ')
          ..write('fileFragment: $fileFragment, ')
          ..write('thumbnailPath: $thumbnailPath, ')
          ..write('thumbnailUrl: $thumbnailUrl, ')
          ..write('thumbnailFragment: $thumbnailFragment, ')
          ..write('selfDestruct: $selfDestruct, ')
          ..write('undo: $undo, ')
          ..write('undoEdit: $undoEdit, ')
          ..write('time: $time, ')
          ..write('type: $type, ')
          ..write('chatType: $chatType, ')
          ..write('callState: $callState, ')
          ..write('state: $state, ')
          ..write('direction: $direction, ')
          ..write('fileState: $fileState, ')
          ..write('thumbnailFileState: $thumbnailFileState, ')
          ..write('msgId: $msgId, ')
          ..write('uuid: $uuid, ')
          ..write('fileName: $fileName, ')
          ..write('fileSize: $fileSize, ')
          ..write('ext1: $ext1, ')
          ..write('ackNumber: $ackNumber, ')
          ..write('read: $read, ')
          ..write('replayMsg: $replayMsg, ')
          ..write('margeMsg: $margeMsg, ')
          ..write('contactMsg: $contactMsg, ')
          ..write('shareMsg: $shareMsg, ')
          ..write('createTime: $createTime, ')
          ..write('at: $at, ')
          ..write('updateTime: $updateTime, ')
          ..write('resourceUuid: $resourceUuid, ')
          ..write('hasShown: $hasShown, ')
          ..write('messageHasRead: $messageHasRead, ')
          ..write('fileDuration: $fileDuration, ')
          ..write('userNameFileHelper: $userNameFileHelper, ')
          ..write('noises: $noises, ')
          ..write('displayName: $displayName, ')
          ..write('expand: $expand')
          ..write(')'))
        .toString();
  }
}

class Session extends Table with TableInfo<Session, SessionData> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  Session(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  late final GeneratedColumn<int> id = GeneratedColumn<int>(
      'id', aliasedName, false,
      hasAutoIncrement: true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      $customConstraints: 'NOT NULL PRIMARY KEY AUTOINCREMENT');
  static const VerificationMeta _usernameMeta =
      const VerificationMeta('username');
  late final GeneratedColumn<String> username = GeneratedColumn<String>(
      'username', aliasedName, false,
      type: DriftSqlType.string,
      requiredDuringInsert: true,
      $customConstraints: 'NOT NULL UNIQUE');
  static const VerificationMeta _displaynameMeta =
      const VerificationMeta('displayname');
  late final GeneratedColumn<String> displayname = GeneratedColumn<String>(
      'displayname', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _bodyMeta = const VerificationMeta('body');
  late final GeneratedColumn<String> body = GeneratedColumn<String>(
      'body', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _timeMeta = const VerificationMeta('time');
  late final GeneratedColumn<int> time = GeneratedColumn<int>(
      'time', aliasedName, true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _topMeta = const VerificationMeta('top');
  late final GeneratedColumn<bool> top = GeneratedColumn<bool>(
      'top', aliasedName, true,
      type: DriftSqlType.bool,
      requiredDuringInsert: false,
      $customConstraints: 'DEFAULT FALSE',
      defaultValue: const CustomExpression('FALSE'));
  static const VerificationMeta _avatarPathMeta =
      const VerificationMeta('avatarPath');
  late final GeneratedColumn<String> avatarPath = GeneratedColumn<String>(
      'avatar_path', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _unreadCountMeta =
      const VerificationMeta('unreadCount');
  late final GeneratedColumn<int> unreadCount = GeneratedColumn<int>(
      'unread_count', aliasedName, true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _editMeta = const VerificationMeta('edit');
  late final GeneratedColumn<bool> edit = GeneratedColumn<bool>(
      'edit', aliasedName, true,
      type: DriftSqlType.bool,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _readMeta = const VerificationMeta('read');
  late final GeneratedColumn<bool> read = GeneratedColumn<bool>(
      'read', aliasedName, true,
      type: DriftSqlType.bool,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _typeMeta = const VerificationMeta('type');
  late final GeneratedColumn<int> type = GeneratedColumn<int>(
      'type', aliasedName, true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _chatTypeMeta =
      const VerificationMeta('chatType');
  late final GeneratedColumn<int> chatType = GeneratedColumn<int>(
      'chat_type', aliasedName, true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _directionMeta =
      const VerificationMeta('direction');
  late final GeneratedColumn<int> direction = GeneratedColumn<int>(
      'direction', aliasedName, true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _selfDestructMeta =
      const VerificationMeta('selfDestruct');
  late final GeneratedColumn<bool> selfDestruct = GeneratedColumn<bool>(
      'self_destruct', aliasedName, true,
      type: DriftSqlType.bool,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _ext1Meta = const VerificationMeta('ext1');
  late final GeneratedColumn<String> ext1 = GeneratedColumn<String>(
      'ext1', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _stateMeta = const VerificationMeta('state');
  late final GeneratedColumn<int> state = GeneratedColumn<int>(
      'state', aliasedName, true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _msgIdMeta = const VerificationMeta('msgId');
  late final GeneratedColumn<String> msgId = GeneratedColumn<String>(
      'msg_id', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _uuidMeta = const VerificationMeta('uuid');
  late final GeneratedColumn<String> uuid = GeneratedColumn<String>(
      'uuid', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _explainsMeta =
      const VerificationMeta('explains');
  late final GeneratedColumn<String> explains = GeneratedColumn<String>(
      'explains', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _atMeta = const VerificationMeta('at');
  late final GeneratedColumn<bool> at = GeneratedColumn<bool>(
      'at', aliasedName, true,
      type: DriftSqlType.bool,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _createTimeMeta =
      const VerificationMeta('createTime');
  late final GeneratedColumn<double> createTime = GeneratedColumn<double>(
      'create_time', aliasedName, true,
      type: DriftSqlType.double,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _silenceMeta =
      const VerificationMeta('silence');
  late final GeneratedColumn<bool> silence = GeneratedColumn<bool>(
      'silence', aliasedName, true,
      type: DriftSqlType.bool,
      requiredDuringInsert: false,
      $customConstraints: 'DEFAULT FALSE',
      defaultValue: const CustomExpression('FALSE'));
  static const VerificationMeta _isTidMeta = const VerificationMeta('isTid');
  late final GeneratedColumn<bool> isTid = GeneratedColumn<bool>(
      'isTid', aliasedName, true,
      type: DriftSqlType.bool,
      requiredDuringInsert: false,
      $customConstraints: 'DEFAULT FALSE',
      defaultValue: const CustomExpression('FALSE'));
  static const VerificationMeta _updateTimeMeta =
      const VerificationMeta('updateTime');
  late final GeneratedColumn<double> updateTime = GeneratedColumn<double>(
      'update_time', aliasedName, true,
      type: DriftSqlType.double,
      requiredDuringInsert: false,
      $customConstraints: '');
  @override
  List<GeneratedColumn> get $columns => [
        id,
        username,
        displayname,
        body,
        time,
        top,
        avatarPath,
        unreadCount,
        edit,
        read,
        type,
        chatType,
        direction,
        selfDestruct,
        ext1,
        state,
        msgId,
        uuid,
        explains,
        at,
        createTime,
        silence,
        isTid,
        updateTime
      ];
  @override
  String get aliasedName => _alias ?? 'session';
  @override
  String get actualTableName => 'session';
  @override
  VerificationContext validateIntegrity(Insertable<SessionData> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    }
    if (data.containsKey('username')) {
      context.handle(_usernameMeta,
          username.isAcceptableOrUnknown(data['username']!, _usernameMeta));
    } else if (isInserting) {
      context.missing(_usernameMeta);
    }
    if (data.containsKey('displayname')) {
      context.handle(
          _displaynameMeta,
          displayname.isAcceptableOrUnknown(
              data['displayname']!, _displaynameMeta));
    }
    if (data.containsKey('body')) {
      context.handle(
          _bodyMeta, body.isAcceptableOrUnknown(data['body']!, _bodyMeta));
    }
    if (data.containsKey('time')) {
      context.handle(
          _timeMeta, time.isAcceptableOrUnknown(data['time']!, _timeMeta));
    }
    if (data.containsKey('top')) {
      context.handle(
          _topMeta, top.isAcceptableOrUnknown(data['top']!, _topMeta));
    }
    if (data.containsKey('avatar_path')) {
      context.handle(
          _avatarPathMeta,
          avatarPath.isAcceptableOrUnknown(
              data['avatar_path']!, _avatarPathMeta));
    }
    if (data.containsKey('unread_count')) {
      context.handle(
          _unreadCountMeta,
          unreadCount.isAcceptableOrUnknown(
              data['unread_count']!, _unreadCountMeta));
    }
    if (data.containsKey('edit')) {
      context.handle(
          _editMeta, edit.isAcceptableOrUnknown(data['edit']!, _editMeta));
    }
    if (data.containsKey('read')) {
      context.handle(
          _readMeta, read.isAcceptableOrUnknown(data['read']!, _readMeta));
    }
    if (data.containsKey('type')) {
      context.handle(
          _typeMeta, type.isAcceptableOrUnknown(data['type']!, _typeMeta));
    }
    if (data.containsKey('chat_type')) {
      context.handle(_chatTypeMeta,
          chatType.isAcceptableOrUnknown(data['chat_type']!, _chatTypeMeta));
    }
    if (data.containsKey('direction')) {
      context.handle(_directionMeta,
          direction.isAcceptableOrUnknown(data['direction']!, _directionMeta));
    }
    if (data.containsKey('self_destruct')) {
      context.handle(
          _selfDestructMeta,
          selfDestruct.isAcceptableOrUnknown(
              data['self_destruct']!, _selfDestructMeta));
    }
    if (data.containsKey('ext1')) {
      context.handle(
          _ext1Meta, ext1.isAcceptableOrUnknown(data['ext1']!, _ext1Meta));
    }
    if (data.containsKey('state')) {
      context.handle(
          _stateMeta, state.isAcceptableOrUnknown(data['state']!, _stateMeta));
    }
    if (data.containsKey('msg_id')) {
      context.handle(
          _msgIdMeta, msgId.isAcceptableOrUnknown(data['msg_id']!, _msgIdMeta));
    }
    if (data.containsKey('uuid')) {
      context.handle(
          _uuidMeta, uuid.isAcceptableOrUnknown(data['uuid']!, _uuidMeta));
    }
    if (data.containsKey('explains')) {
      context.handle(_explainsMeta,
          explains.isAcceptableOrUnknown(data['explains']!, _explainsMeta));
    }
    if (data.containsKey('at')) {
      context.handle(_atMeta, at.isAcceptableOrUnknown(data['at']!, _atMeta));
    }
    if (data.containsKey('create_time')) {
      context.handle(
          _createTimeMeta,
          createTime.isAcceptableOrUnknown(
              data['create_time']!, _createTimeMeta));
    }
    if (data.containsKey('silence')) {
      context.handle(_silenceMeta,
          silence.isAcceptableOrUnknown(data['silence']!, _silenceMeta));
    }
    if (data.containsKey('isTid')) {
      context.handle(
          _isTidMeta, isTid.isAcceptableOrUnknown(data['isTid']!, _isTidMeta));
    }
    if (data.containsKey('update_time')) {
      context.handle(
          _updateTimeMeta,
          updateTime.isAcceptableOrUnknown(
              data['update_time']!, _updateTimeMeta));
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  SessionData map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return SessionData(
      id: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}id'])!,
      username: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}username'])!,
      displayname: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}displayname']),
      body: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}body']),
      time: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}time']),
      top: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}top']),
      avatarPath: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}avatar_path']),
      unreadCount: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}unread_count']),
      edit: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}edit']),
      read: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}read']),
      type: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}type']),
      chatType: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}chat_type']),
      direction: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}direction']),
      selfDestruct: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}self_destruct']),
      ext1: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}ext1']),
      state: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}state']),
      msgId: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}msg_id']),
      uuid: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}uuid']),
      explains: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}explains']),
      at: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}at']),
      createTime: attachedDatabase.typeMapping
          .read(DriftSqlType.double, data['${effectivePrefix}create_time']),
      silence: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}silence']),
      isTid: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}isTid']),
      updateTime: attachedDatabase.typeMapping
          .read(DriftSqlType.double, data['${effectivePrefix}update_time']),
    );
  }

  @override
  Session createAlias(String alias) {
    return Session(attachedDatabase, alias);
  }

  @override
  bool get dontWriteConstraints => true;
}

class SessionData extends DataClass implements Insertable<SessionData> {
  final int id;
  final String username;
  final String? displayname;
  final String? body;
  final int? time;
  final bool? top;
  final String? avatarPath;
  final int? unreadCount;
  final bool? edit;
  final bool? read;
  final int? type;
  final int? chatType;
  final int? direction;
  final bool? selfDestruct;
  final String? ext1;
  final int? state;
  final String? msgId;
  final String? uuid;
  final String? explains;
  final bool? at;
  final double? createTime;
  final bool? silence;
  final bool? isTid;
  final double? updateTime;
  const SessionData(
      {required this.id,
      required this.username,
      this.displayname,
      this.body,
      this.time,
      this.top,
      this.avatarPath,
      this.unreadCount,
      this.edit,
      this.read,
      this.type,
      this.chatType,
      this.direction,
      this.selfDestruct,
      this.ext1,
      this.state,
      this.msgId,
      this.uuid,
      this.explains,
      this.at,
      this.createTime,
      this.silence,
      this.isTid,
      this.updateTime});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<int>(id);
    map['username'] = Variable<String>(username);
    if (!nullToAbsent || displayname != null) {
      map['displayname'] = Variable<String>(displayname);
    }
    if (!nullToAbsent || body != null) {
      map['body'] = Variable<String>(body);
    }
    if (!nullToAbsent || time != null) {
      map['time'] = Variable<int>(time);
    }
    if (!nullToAbsent || top != null) {
      map['top'] = Variable<bool>(top);
    }
    if (!nullToAbsent || avatarPath != null) {
      map['avatar_path'] = Variable<String>(avatarPath);
    }
    if (!nullToAbsent || unreadCount != null) {
      map['unread_count'] = Variable<int>(unreadCount);
    }
    if (!nullToAbsent || edit != null) {
      map['edit'] = Variable<bool>(edit);
    }
    if (!nullToAbsent || read != null) {
      map['read'] = Variable<bool>(read);
    }
    if (!nullToAbsent || type != null) {
      map['type'] = Variable<int>(type);
    }
    if (!nullToAbsent || chatType != null) {
      map['chat_type'] = Variable<int>(chatType);
    }
    if (!nullToAbsent || direction != null) {
      map['direction'] = Variable<int>(direction);
    }
    if (!nullToAbsent || selfDestruct != null) {
      map['self_destruct'] = Variable<bool>(selfDestruct);
    }
    if (!nullToAbsent || ext1 != null) {
      map['ext1'] = Variable<String>(ext1);
    }
    if (!nullToAbsent || state != null) {
      map['state'] = Variable<int>(state);
    }
    if (!nullToAbsent || msgId != null) {
      map['msg_id'] = Variable<String>(msgId);
    }
    if (!nullToAbsent || uuid != null) {
      map['uuid'] = Variable<String>(uuid);
    }
    if (!nullToAbsent || explains != null) {
      map['explains'] = Variable<String>(explains);
    }
    if (!nullToAbsent || at != null) {
      map['at'] = Variable<bool>(at);
    }
    if (!nullToAbsent || createTime != null) {
      map['create_time'] = Variable<double>(createTime);
    }
    if (!nullToAbsent || silence != null) {
      map['silence'] = Variable<bool>(silence);
    }
    if (!nullToAbsent || isTid != null) {
      map['isTid'] = Variable<bool>(isTid);
    }
    if (!nullToAbsent || updateTime != null) {
      map['update_time'] = Variable<double>(updateTime);
    }
    return map;
  }

  SessionCompanion toCompanion(bool nullToAbsent) {
    return SessionCompanion(
      id: Value(id),
      username: Value(username),
      displayname: displayname == null && nullToAbsent
          ? const Value.absent()
          : Value(displayname),
      body: body == null && nullToAbsent ? const Value.absent() : Value(body),
      time: time == null && nullToAbsent ? const Value.absent() : Value(time),
      top: top == null && nullToAbsent ? const Value.absent() : Value(top),
      avatarPath: avatarPath == null && nullToAbsent
          ? const Value.absent()
          : Value(avatarPath),
      unreadCount: unreadCount == null && nullToAbsent
          ? const Value.absent()
          : Value(unreadCount),
      edit: edit == null && nullToAbsent ? const Value.absent() : Value(edit),
      read: read == null && nullToAbsent ? const Value.absent() : Value(read),
      type: type == null && nullToAbsent ? const Value.absent() : Value(type),
      chatType: chatType == null && nullToAbsent
          ? const Value.absent()
          : Value(chatType),
      direction: direction == null && nullToAbsent
          ? const Value.absent()
          : Value(direction),
      selfDestruct: selfDestruct == null && nullToAbsent
          ? const Value.absent()
          : Value(selfDestruct),
      ext1: ext1 == null && nullToAbsent ? const Value.absent() : Value(ext1),
      state:
          state == null && nullToAbsent ? const Value.absent() : Value(state),
      msgId:
          msgId == null && nullToAbsent ? const Value.absent() : Value(msgId),
      uuid: uuid == null && nullToAbsent ? const Value.absent() : Value(uuid),
      explains: explains == null && nullToAbsent
          ? const Value.absent()
          : Value(explains),
      at: at == null && nullToAbsent ? const Value.absent() : Value(at),
      createTime: createTime == null && nullToAbsent
          ? const Value.absent()
          : Value(createTime),
      silence: silence == null && nullToAbsent
          ? const Value.absent()
          : Value(silence),
      isTid:
          isTid == null && nullToAbsent ? const Value.absent() : Value(isTid),
      updateTime: updateTime == null && nullToAbsent
          ? const Value.absent()
          : Value(updateTime),
    );
  }

  factory SessionData.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return SessionData(
      id: serializer.fromJson<int>(json['id']),
      username: serializer.fromJson<String>(json['username']),
      displayname: serializer.fromJson<String?>(json['displayname']),
      body: serializer.fromJson<String?>(json['body']),
      time: serializer.fromJson<int?>(json['time']),
      top: serializer.fromJson<bool?>(json['top']),
      avatarPath: serializer.fromJson<String?>(json['avatar_path']),
      unreadCount: serializer.fromJson<int?>(json['unread_count']),
      edit: serializer.fromJson<bool?>(json['edit']),
      read: serializer.fromJson<bool?>(json['read']),
      type: serializer.fromJson<int?>(json['type']),
      chatType: serializer.fromJson<int?>(json['chat_type']),
      direction: serializer.fromJson<int?>(json['direction']),
      selfDestruct: serializer.fromJson<bool?>(json['self_destruct']),
      ext1: serializer.fromJson<String?>(json['ext1']),
      state: serializer.fromJson<int?>(json['state']),
      msgId: serializer.fromJson<String?>(json['msg_id']),
      uuid: serializer.fromJson<String?>(json['uuid']),
      explains: serializer.fromJson<String?>(json['explains']),
      at: serializer.fromJson<bool?>(json['at']),
      createTime: serializer.fromJson<double?>(json['create_time']),
      silence: serializer.fromJson<bool?>(json['silence']),
      isTid: serializer.fromJson<bool?>(json['isTid']),
      updateTime: serializer.fromJson<double?>(json['update_time']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<int>(id),
      'username': serializer.toJson<String>(username),
      'displayname': serializer.toJson<String?>(displayname),
      'body': serializer.toJson<String?>(body),
      'time': serializer.toJson<int?>(time),
      'top': serializer.toJson<bool?>(top),
      'avatar_path': serializer.toJson<String?>(avatarPath),
      'unread_count': serializer.toJson<int?>(unreadCount),
      'edit': serializer.toJson<bool?>(edit),
      'read': serializer.toJson<bool?>(read),
      'type': serializer.toJson<int?>(type),
      'chat_type': serializer.toJson<int?>(chatType),
      'direction': serializer.toJson<int?>(direction),
      'self_destruct': serializer.toJson<bool?>(selfDestruct),
      'ext1': serializer.toJson<String?>(ext1),
      'state': serializer.toJson<int?>(state),
      'msg_id': serializer.toJson<String?>(msgId),
      'uuid': serializer.toJson<String?>(uuid),
      'explains': serializer.toJson<String?>(explains),
      'at': serializer.toJson<bool?>(at),
      'create_time': serializer.toJson<double?>(createTime),
      'silence': serializer.toJson<bool?>(silence),
      'isTid': serializer.toJson<bool?>(isTid),
      'update_time': serializer.toJson<double?>(updateTime),
    };
  }

  SessionData copyWith(
          {int? id,
          String? username,
          Value<String?> displayname = const Value.absent(),
          Value<String?> body = const Value.absent(),
          Value<int?> time = const Value.absent(),
          Value<bool?> top = const Value.absent(),
          Value<String?> avatarPath = const Value.absent(),
          Value<int?> unreadCount = const Value.absent(),
          Value<bool?> edit = const Value.absent(),
          Value<bool?> read = const Value.absent(),
          Value<int?> type = const Value.absent(),
          Value<int?> chatType = const Value.absent(),
          Value<int?> direction = const Value.absent(),
          Value<bool?> selfDestruct = const Value.absent(),
          Value<String?> ext1 = const Value.absent(),
          Value<int?> state = const Value.absent(),
          Value<String?> msgId = const Value.absent(),
          Value<String?> uuid = const Value.absent(),
          Value<String?> explains = const Value.absent(),
          Value<bool?> at = const Value.absent(),
          Value<double?> createTime = const Value.absent(),
          Value<bool?> silence = const Value.absent(),
          Value<bool?> isTid = const Value.absent(),
          Value<double?> updateTime = const Value.absent()}) =>
      SessionData(
        id: id ?? this.id,
        username: username ?? this.username,
        displayname: displayname.present ? displayname.value : this.displayname,
        body: body.present ? body.value : this.body,
        time: time.present ? time.value : this.time,
        top: top.present ? top.value : this.top,
        avatarPath: avatarPath.present ? avatarPath.value : this.avatarPath,
        unreadCount: unreadCount.present ? unreadCount.value : this.unreadCount,
        edit: edit.present ? edit.value : this.edit,
        read: read.present ? read.value : this.read,
        type: type.present ? type.value : this.type,
        chatType: chatType.present ? chatType.value : this.chatType,
        direction: direction.present ? direction.value : this.direction,
        selfDestruct:
            selfDestruct.present ? selfDestruct.value : this.selfDestruct,
        ext1: ext1.present ? ext1.value : this.ext1,
        state: state.present ? state.value : this.state,
        msgId: msgId.present ? msgId.value : this.msgId,
        uuid: uuid.present ? uuid.value : this.uuid,
        explains: explains.present ? explains.value : this.explains,
        at: at.present ? at.value : this.at,
        createTime: createTime.present ? createTime.value : this.createTime,
        silence: silence.present ? silence.value : this.silence,
        isTid: isTid.present ? isTid.value : this.isTid,
        updateTime: updateTime.present ? updateTime.value : this.updateTime,
      );
  @override
  String toString() {
    return (StringBuffer('SessionData(')
          ..write('id: $id, ')
          ..write('username: $username, ')
          ..write('displayname: $displayname, ')
          ..write('body: $body, ')
          ..write('time: $time, ')
          ..write('top: $top, ')
          ..write('avatarPath: $avatarPath, ')
          ..write('unreadCount: $unreadCount, ')
          ..write('edit: $edit, ')
          ..write('read: $read, ')
          ..write('type: $type, ')
          ..write('chatType: $chatType, ')
          ..write('direction: $direction, ')
          ..write('selfDestruct: $selfDestruct, ')
          ..write('ext1: $ext1, ')
          ..write('state: $state, ')
          ..write('msgId: $msgId, ')
          ..write('uuid: $uuid, ')
          ..write('explains: $explains, ')
          ..write('at: $at, ')
          ..write('createTime: $createTime, ')
          ..write('silence: $silence, ')
          ..write('isTid: $isTid, ')
          ..write('updateTime: $updateTime')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hashAll([
        id,
        username,
        displayname,
        body,
        time,
        top,
        avatarPath,
        unreadCount,
        edit,
        read,
        type,
        chatType,
        direction,
        selfDestruct,
        ext1,
        state,
        msgId,
        uuid,
        explains,
        at,
        createTime,
        silence,
        isTid,
        updateTime
      ]);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is SessionData &&
          other.id == this.id &&
          other.username == this.username &&
          other.displayname == this.displayname &&
          other.body == this.body &&
          other.time == this.time &&
          other.top == this.top &&
          other.avatarPath == this.avatarPath &&
          other.unreadCount == this.unreadCount &&
          other.edit == this.edit &&
          other.read == this.read &&
          other.type == this.type &&
          other.chatType == this.chatType &&
          other.direction == this.direction &&
          other.selfDestruct == this.selfDestruct &&
          other.ext1 == this.ext1 &&
          other.state == this.state &&
          other.msgId == this.msgId &&
          other.uuid == this.uuid &&
          other.explains == this.explains &&
          other.at == this.at &&
          other.createTime == this.createTime &&
          other.silence == this.silence &&
          other.isTid == this.isTid &&
          other.updateTime == this.updateTime);
}

class SessionCompanion extends UpdateCompanion<SessionData> {
  final Value<int> id;
  final Value<String> username;
  final Value<String?> displayname;
  final Value<String?> body;
  final Value<int?> time;
  final Value<bool?> top;
  final Value<String?> avatarPath;
  final Value<int?> unreadCount;
  final Value<bool?> edit;
  final Value<bool?> read;
  final Value<int?> type;
  final Value<int?> chatType;
  final Value<int?> direction;
  final Value<bool?> selfDestruct;
  final Value<String?> ext1;
  final Value<int?> state;
  final Value<String?> msgId;
  final Value<String?> uuid;
  final Value<String?> explains;
  final Value<bool?> at;
  final Value<double?> createTime;
  final Value<bool?> silence;
  final Value<bool?> isTid;
  final Value<double?> updateTime;
  const SessionCompanion({
    this.id = const Value.absent(),
    this.username = const Value.absent(),
    this.displayname = const Value.absent(),
    this.body = const Value.absent(),
    this.time = const Value.absent(),
    this.top = const Value.absent(),
    this.avatarPath = const Value.absent(),
    this.unreadCount = const Value.absent(),
    this.edit = const Value.absent(),
    this.read = const Value.absent(),
    this.type = const Value.absent(),
    this.chatType = const Value.absent(),
    this.direction = const Value.absent(),
    this.selfDestruct = const Value.absent(),
    this.ext1 = const Value.absent(),
    this.state = const Value.absent(),
    this.msgId = const Value.absent(),
    this.uuid = const Value.absent(),
    this.explains = const Value.absent(),
    this.at = const Value.absent(),
    this.createTime = const Value.absent(),
    this.silence = const Value.absent(),
    this.isTid = const Value.absent(),
    this.updateTime = const Value.absent(),
  });
  SessionCompanion.insert({
    this.id = const Value.absent(),
    required String username,
    this.displayname = const Value.absent(),
    this.body = const Value.absent(),
    this.time = const Value.absent(),
    this.top = const Value.absent(),
    this.avatarPath = const Value.absent(),
    this.unreadCount = const Value.absent(),
    this.edit = const Value.absent(),
    this.read = const Value.absent(),
    this.type = const Value.absent(),
    this.chatType = const Value.absent(),
    this.direction = const Value.absent(),
    this.selfDestruct = const Value.absent(),
    this.ext1 = const Value.absent(),
    this.state = const Value.absent(),
    this.msgId = const Value.absent(),
    this.uuid = const Value.absent(),
    this.explains = const Value.absent(),
    this.at = const Value.absent(),
    this.createTime = const Value.absent(),
    this.silence = const Value.absent(),
    this.isTid = const Value.absent(),
    this.updateTime = const Value.absent(),
  }) : username = Value(username);
  static Insertable<SessionData> custom({
    Expression<int>? id,
    Expression<String>? username,
    Expression<String>? displayname,
    Expression<String>? body,
    Expression<int>? time,
    Expression<bool>? top,
    Expression<String>? avatarPath,
    Expression<int>? unreadCount,
    Expression<bool>? edit,
    Expression<bool>? read,
    Expression<int>? type,
    Expression<int>? chatType,
    Expression<int>? direction,
    Expression<bool>? selfDestruct,
    Expression<String>? ext1,
    Expression<int>? state,
    Expression<String>? msgId,
    Expression<String>? uuid,
    Expression<String>? explains,
    Expression<bool>? at,
    Expression<double>? createTime,
    Expression<bool>? silence,
    Expression<bool>? isTid,
    Expression<double>? updateTime,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (username != null) 'username': username,
      if (displayname != null) 'displayname': displayname,
      if (body != null) 'body': body,
      if (time != null) 'time': time,
      if (top != null) 'top': top,
      if (avatarPath != null) 'avatar_path': avatarPath,
      if (unreadCount != null) 'unread_count': unreadCount,
      if (edit != null) 'edit': edit,
      if (read != null) 'read': read,
      if (type != null) 'type': type,
      if (chatType != null) 'chat_type': chatType,
      if (direction != null) 'direction': direction,
      if (selfDestruct != null) 'self_destruct': selfDestruct,
      if (ext1 != null) 'ext1': ext1,
      if (state != null) 'state': state,
      if (msgId != null) 'msg_id': msgId,
      if (uuid != null) 'uuid': uuid,
      if (explains != null) 'explains': explains,
      if (at != null) 'at': at,
      if (createTime != null) 'create_time': createTime,
      if (silence != null) 'silence': silence,
      if (isTid != null) 'isTid': isTid,
      if (updateTime != null) 'update_time': updateTime,
    });
  }

  SessionCompanion copyWith(
      {Value<int>? id,
      Value<String>? username,
      Value<String?>? displayname,
      Value<String?>? body,
      Value<int?>? time,
      Value<bool?>? top,
      Value<String?>? avatarPath,
      Value<int?>? unreadCount,
      Value<bool?>? edit,
      Value<bool?>? read,
      Value<int?>? type,
      Value<int?>? chatType,
      Value<int?>? direction,
      Value<bool?>? selfDestruct,
      Value<String?>? ext1,
      Value<int?>? state,
      Value<String?>? msgId,
      Value<String?>? uuid,
      Value<String?>? explains,
      Value<bool?>? at,
      Value<double?>? createTime,
      Value<bool?>? silence,
      Value<bool?>? isTid,
      Value<double?>? updateTime}) {
    return SessionCompanion(
      id: id ?? this.id,
      username: username ?? this.username,
      displayname: displayname ?? this.displayname,
      body: body ?? this.body,
      time: time ?? this.time,
      top: top ?? this.top,
      avatarPath: avatarPath ?? this.avatarPath,
      unreadCount: unreadCount ?? this.unreadCount,
      edit: edit ?? this.edit,
      read: read ?? this.read,
      type: type ?? this.type,
      chatType: chatType ?? this.chatType,
      direction: direction ?? this.direction,
      selfDestruct: selfDestruct ?? this.selfDestruct,
      ext1: ext1 ?? this.ext1,
      state: state ?? this.state,
      msgId: msgId ?? this.msgId,
      uuid: uuid ?? this.uuid,
      explains: explains ?? this.explains,
      at: at ?? this.at,
      createTime: createTime ?? this.createTime,
      silence: silence ?? this.silence,
      isTid: isTid ?? this.isTid,
      updateTime: updateTime ?? this.updateTime,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<int>(id.value);
    }
    if (username.present) {
      map['username'] = Variable<String>(username.value);
    }
    if (displayname.present) {
      map['displayname'] = Variable<String>(displayname.value);
    }
    if (body.present) {
      map['body'] = Variable<String>(body.value);
    }
    if (time.present) {
      map['time'] = Variable<int>(time.value);
    }
    if (top.present) {
      map['top'] = Variable<bool>(top.value);
    }
    if (avatarPath.present) {
      map['avatar_path'] = Variable<String>(avatarPath.value);
    }
    if (unreadCount.present) {
      map['unread_count'] = Variable<int>(unreadCount.value);
    }
    if (edit.present) {
      map['edit'] = Variable<bool>(edit.value);
    }
    if (read.present) {
      map['read'] = Variable<bool>(read.value);
    }
    if (type.present) {
      map['type'] = Variable<int>(type.value);
    }
    if (chatType.present) {
      map['chat_type'] = Variable<int>(chatType.value);
    }
    if (direction.present) {
      map['direction'] = Variable<int>(direction.value);
    }
    if (selfDestruct.present) {
      map['self_destruct'] = Variable<bool>(selfDestruct.value);
    }
    if (ext1.present) {
      map['ext1'] = Variable<String>(ext1.value);
    }
    if (state.present) {
      map['state'] = Variable<int>(state.value);
    }
    if (msgId.present) {
      map['msg_id'] = Variable<String>(msgId.value);
    }
    if (uuid.present) {
      map['uuid'] = Variable<String>(uuid.value);
    }
    if (explains.present) {
      map['explains'] = Variable<String>(explains.value);
    }
    if (at.present) {
      map['at'] = Variable<bool>(at.value);
    }
    if (createTime.present) {
      map['create_time'] = Variable<double>(createTime.value);
    }
    if (silence.present) {
      map['silence'] = Variable<bool>(silence.value);
    }
    if (isTid.present) {
      map['isTid'] = Variable<bool>(isTid.value);
    }
    if (updateTime.present) {
      map['update_time'] = Variable<double>(updateTime.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('SessionCompanion(')
          ..write('id: $id, ')
          ..write('username: $username, ')
          ..write('displayname: $displayname, ')
          ..write('body: $body, ')
          ..write('time: $time, ')
          ..write('top: $top, ')
          ..write('avatarPath: $avatarPath, ')
          ..write('unreadCount: $unreadCount, ')
          ..write('edit: $edit, ')
          ..write('read: $read, ')
          ..write('type: $type, ')
          ..write('chatType: $chatType, ')
          ..write('direction: $direction, ')
          ..write('selfDestruct: $selfDestruct, ')
          ..write('ext1: $ext1, ')
          ..write('state: $state, ')
          ..write('msgId: $msgId, ')
          ..write('uuid: $uuid, ')
          ..write('explains: $explains, ')
          ..write('at: $at, ')
          ..write('createTime: $createTime, ')
          ..write('silence: $silence, ')
          ..write('isTid: $isTid, ')
          ..write('updateTime: $updateTime')
          ..write(')'))
        .toString();
  }
}

class ChannelInfo extends Table with TableInfo<ChannelInfo, ChannelInfoData> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  ChannelInfo(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  late final GeneratedColumn<int> id = GeneratedColumn<int>(
      'id', aliasedName, false,
      hasAutoIncrement: true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      $customConstraints: 'NOT NULL PRIMARY KEY AUTOINCREMENT');
  static const VerificationMeta _channelIdMeta =
      const VerificationMeta('channelId');
  late final GeneratedColumn<String> channelId = GeneratedColumn<String>(
      'channel_id', aliasedName, false,
      type: DriftSqlType.string,
      requiredDuringInsert: true,
      $customConstraints: 'NOT NULL UNIQUE');
  static const VerificationMeta _titleMeta = const VerificationMeta('title');
  late final GeneratedColumn<String> title = GeneratedColumn<String>(
      'title', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _stateMeta = const VerificationMeta('state');
  late final GeneratedColumn<int> state = GeneratedColumn<int>(
      'state', aliasedName, true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _ownerMeta = const VerificationMeta('owner');
  late final GeneratedColumn<String> owner = GeneratedColumn<String>(
      'owner', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _avatarUrlMeta =
      const VerificationMeta('avatarUrl');
  late final GeneratedColumn<String> avatarUrl = GeneratedColumn<String>(
      'avatar_url', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _avatarPathMeta =
      const VerificationMeta('avatarPath');
  late final GeneratedColumn<String> avatarPath = GeneratedColumn<String>(
      'avatar_path', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _describeMeta =
      const VerificationMeta('describe');
  late final GeneratedColumn<String> describe = GeneratedColumn<String>(
      'describe', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _minMsgUuidMeta =
      const VerificationMeta('minMsgUuid');
  late final GeneratedColumn<String> minMsgUuid = GeneratedColumn<String>(
      'min_msg_uuid', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _maxMsgUuidMeta =
      const VerificationMeta('maxMsgUuid');
  late final GeneratedColumn<String> maxMsgUuid = GeneratedColumn<String>(
      'max_msg_uuid', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _maxMsgTimeMeta =
      const VerificationMeta('maxMsgTime');
  late final GeneratedColumn<int> maxMsgTime = GeneratedColumn<int>(
      'max_msg_time', aliasedName, true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _backgroundPathMeta =
      const VerificationMeta('backgroundPath');
  late final GeneratedColumn<String> backgroundPath = GeneratedColumn<String>(
      'background_path', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _backgroundUrlMeta =
      const VerificationMeta('backgroundUrl');
  late final GeneratedColumn<String> backgroundUrl = GeneratedColumn<String>(
      'background_url', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _allMuteMeta =
      const VerificationMeta('allMute');
  late final GeneratedColumn<bool> allMute = GeneratedColumn<bool>(
      'all_mute', aliasedName, true,
      type: DriftSqlType.bool,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _joinVerifyMeta =
      const VerificationMeta('joinVerify');
  late final GeneratedColumn<bool> joinVerify = GeneratedColumn<bool>(
      'join_verify', aliasedName, true,
      type: DriftSqlType.bool,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _inviteLimitMeta =
      const VerificationMeta('inviteLimit');
  late final GeneratedColumn<bool> inviteLimit = GeneratedColumn<bool>(
      'invite_limit', aliasedName, true,
      type: DriftSqlType.bool,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _createTimeMeta =
      const VerificationMeta('createTime');
  late final GeneratedColumn<double> createTime = GeneratedColumn<double>(
      'create_time', aliasedName, true,
      type: DriftSqlType.double,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _updateTimeMeta =
      const VerificationMeta('updateTime');
  late final GeneratedColumn<double> updateTime = GeneratedColumn<double>(
      'update_time', aliasedName, true,
      type: DriftSqlType.double,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _announcementMeta =
      const VerificationMeta('announcement');
  late final GeneratedColumn<String> announcement = GeneratedColumn<String>(
      'announcement', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _attributeMeta =
      const VerificationMeta('attribute');
  late final GeneratedColumn<int> attribute = GeneratedColumn<int>(
      'attribute', aliasedName, true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      $customConstraints: 'DEFAULT 0',
      defaultValue: const CustomExpression('0'));
  static const VerificationMeta _optionsMeta =
      const VerificationMeta('options');
  late final GeneratedColumn<String> options = GeneratedColumn<String>(
      'options', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _memberCountMeta =
      const VerificationMeta('memberCount');
  late final GeneratedColumn<int> memberCount = GeneratedColumn<int>(
      'member_count', aliasedName, true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _limitMeta = const VerificationMeta('limit');
  late final GeneratedColumn<int> limit = GeneratedColumn<int>(
      'limit', aliasedName, true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _chainMeta = const VerificationMeta('chain');
  late final GeneratedColumn<String> chain = GeneratedColumn<String>(
      'chain', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _tokenAddressMeta =
      const VerificationMeta('tokenAddress');
  late final GeneratedColumn<String> tokenAddress = GeneratedColumn<String>(
      'token_address', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _minNumTokenMeta =
      const VerificationMeta('minNumToken');
  late final GeneratedColumn<double> minNumToken = GeneratedColumn<double>(
      'min_num_token', aliasedName, true,
      type: DriftSqlType.double,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _tagsMeta = const VerificationMeta('tags');
  late final GeneratedColumn<String> tags = GeneratedColumn<String>(
      'tags', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  @override
  List<GeneratedColumn> get $columns => [
        id,
        channelId,
        title,
        state,
        owner,
        avatarUrl,
        avatarPath,
        describe,
        minMsgUuid,
        maxMsgUuid,
        maxMsgTime,
        backgroundPath,
        backgroundUrl,
        allMute,
        joinVerify,
        inviteLimit,
        createTime,
        updateTime,
        announcement,
        attribute,
        options,
        memberCount,
        limit,
        chain,
        tokenAddress,
        minNumToken,
        tags
      ];
  @override
  String get aliasedName => _alias ?? 'channel_info';
  @override
  String get actualTableName => 'channel_info';
  @override
  VerificationContext validateIntegrity(Insertable<ChannelInfoData> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    }
    if (data.containsKey('channel_id')) {
      context.handle(_channelIdMeta,
          channelId.isAcceptableOrUnknown(data['channel_id']!, _channelIdMeta));
    } else if (isInserting) {
      context.missing(_channelIdMeta);
    }
    if (data.containsKey('title')) {
      context.handle(
          _titleMeta, title.isAcceptableOrUnknown(data['title']!, _titleMeta));
    }
    if (data.containsKey('state')) {
      context.handle(
          _stateMeta, state.isAcceptableOrUnknown(data['state']!, _stateMeta));
    }
    if (data.containsKey('owner')) {
      context.handle(
          _ownerMeta, owner.isAcceptableOrUnknown(data['owner']!, _ownerMeta));
    }
    if (data.containsKey('avatar_url')) {
      context.handle(_avatarUrlMeta,
          avatarUrl.isAcceptableOrUnknown(data['avatar_url']!, _avatarUrlMeta));
    }
    if (data.containsKey('avatar_path')) {
      context.handle(
          _avatarPathMeta,
          avatarPath.isAcceptableOrUnknown(
              data['avatar_path']!, _avatarPathMeta));
    }
    if (data.containsKey('describe')) {
      context.handle(_describeMeta,
          describe.isAcceptableOrUnknown(data['describe']!, _describeMeta));
    }
    if (data.containsKey('min_msg_uuid')) {
      context.handle(
          _minMsgUuidMeta,
          minMsgUuid.isAcceptableOrUnknown(
              data['min_msg_uuid']!, _minMsgUuidMeta));
    }
    if (data.containsKey('max_msg_uuid')) {
      context.handle(
          _maxMsgUuidMeta,
          maxMsgUuid.isAcceptableOrUnknown(
              data['max_msg_uuid']!, _maxMsgUuidMeta));
    }
    if (data.containsKey('max_msg_time')) {
      context.handle(
          _maxMsgTimeMeta,
          maxMsgTime.isAcceptableOrUnknown(
              data['max_msg_time']!, _maxMsgTimeMeta));
    }
    if (data.containsKey('background_path')) {
      context.handle(
          _backgroundPathMeta,
          backgroundPath.isAcceptableOrUnknown(
              data['background_path']!, _backgroundPathMeta));
    }
    if (data.containsKey('background_url')) {
      context.handle(
          _backgroundUrlMeta,
          backgroundUrl.isAcceptableOrUnknown(
              data['background_url']!, _backgroundUrlMeta));
    }
    if (data.containsKey('all_mute')) {
      context.handle(_allMuteMeta,
          allMute.isAcceptableOrUnknown(data['all_mute']!, _allMuteMeta));
    }
    if (data.containsKey('join_verify')) {
      context.handle(
          _joinVerifyMeta,
          joinVerify.isAcceptableOrUnknown(
              data['join_verify']!, _joinVerifyMeta));
    }
    if (data.containsKey('invite_limit')) {
      context.handle(
          _inviteLimitMeta,
          inviteLimit.isAcceptableOrUnknown(
              data['invite_limit']!, _inviteLimitMeta));
    }
    if (data.containsKey('create_time')) {
      context.handle(
          _createTimeMeta,
          createTime.isAcceptableOrUnknown(
              data['create_time']!, _createTimeMeta));
    }
    if (data.containsKey('update_time')) {
      context.handle(
          _updateTimeMeta,
          updateTime.isAcceptableOrUnknown(
              data['update_time']!, _updateTimeMeta));
    }
    if (data.containsKey('announcement')) {
      context.handle(
          _announcementMeta,
          announcement.isAcceptableOrUnknown(
              data['announcement']!, _announcementMeta));
    }
    if (data.containsKey('attribute')) {
      context.handle(_attributeMeta,
          attribute.isAcceptableOrUnknown(data['attribute']!, _attributeMeta));
    }
    if (data.containsKey('options')) {
      context.handle(_optionsMeta,
          options.isAcceptableOrUnknown(data['options']!, _optionsMeta));
    }
    if (data.containsKey('member_count')) {
      context.handle(
          _memberCountMeta,
          memberCount.isAcceptableOrUnknown(
              data['member_count']!, _memberCountMeta));
    }
    if (data.containsKey('limit')) {
      context.handle(
          _limitMeta, limit.isAcceptableOrUnknown(data['limit']!, _limitMeta));
    }
    if (data.containsKey('chain')) {
      context.handle(
          _chainMeta, chain.isAcceptableOrUnknown(data['chain']!, _chainMeta));
    }
    if (data.containsKey('token_address')) {
      context.handle(
          _tokenAddressMeta,
          tokenAddress.isAcceptableOrUnknown(
              data['token_address']!, _tokenAddressMeta));
    }
    if (data.containsKey('min_num_token')) {
      context.handle(
          _minNumTokenMeta,
          minNumToken.isAcceptableOrUnknown(
              data['min_num_token']!, _minNumTokenMeta));
    }
    if (data.containsKey('tags')) {
      context.handle(
          _tagsMeta, tags.isAcceptableOrUnknown(data['tags']!, _tagsMeta));
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  ChannelInfoData map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return ChannelInfoData(
      id: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}id'])!,
      channelId: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}channel_id'])!,
      title: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}title']),
      state: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}state']),
      owner: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}owner']),
      avatarUrl: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}avatar_url']),
      avatarPath: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}avatar_path']),
      describe: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}describe']),
      minMsgUuid: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}min_msg_uuid']),
      maxMsgUuid: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}max_msg_uuid']),
      maxMsgTime: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}max_msg_time']),
      backgroundPath: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}background_path']),
      backgroundUrl: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}background_url']),
      allMute: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}all_mute']),
      joinVerify: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}join_verify']),
      inviteLimit: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}invite_limit']),
      createTime: attachedDatabase.typeMapping
          .read(DriftSqlType.double, data['${effectivePrefix}create_time']),
      updateTime: attachedDatabase.typeMapping
          .read(DriftSqlType.double, data['${effectivePrefix}update_time']),
      announcement: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}announcement']),
      attribute: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}attribute']),
      options: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}options']),
      memberCount: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}member_count']),
      limit: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}limit']),
      chain: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}chain']),
      tokenAddress: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}token_address']),
      minNumToken: attachedDatabase.typeMapping
          .read(DriftSqlType.double, data['${effectivePrefix}min_num_token']),
      tags: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}tags']),
    );
  }

  @override
  ChannelInfo createAlias(String alias) {
    return ChannelInfo(attachedDatabase, alias);
  }

  @override
  bool get dontWriteConstraints => true;
}

class ChannelInfoData extends DataClass implements Insertable<ChannelInfoData> {
  final int id;
  final String channelId;
  final String? title;
  final int? state;
  final String? owner;
  final String? avatarUrl;
  final String? avatarPath;
  final String? describe;
  final String? minMsgUuid;
  final String? maxMsgUuid;
  final int? maxMsgTime;
  final String? backgroundPath;
  final String? backgroundUrl;
  final bool? allMute;
  final bool? joinVerify;
  final bool? inviteLimit;
  final double? createTime;
  final double? updateTime;
  final String? announcement;
  final int? attribute;
  final String? options;
  final int? memberCount;
  final int? limit;
  final String? chain;
  final String? tokenAddress;
  final double? minNumToken;
  final String? tags;
  const ChannelInfoData(
      {required this.id,
      required this.channelId,
      this.title,
      this.state,
      this.owner,
      this.avatarUrl,
      this.avatarPath,
      this.describe,
      this.minMsgUuid,
      this.maxMsgUuid,
      this.maxMsgTime,
      this.backgroundPath,
      this.backgroundUrl,
      this.allMute,
      this.joinVerify,
      this.inviteLimit,
      this.createTime,
      this.updateTime,
      this.announcement,
      this.attribute,
      this.options,
      this.memberCount,
      this.limit,
      this.chain,
      this.tokenAddress,
      this.minNumToken,
      this.tags});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<int>(id);
    map['channel_id'] = Variable<String>(channelId);
    if (!nullToAbsent || title != null) {
      map['title'] = Variable<String>(title);
    }
    if (!nullToAbsent || state != null) {
      map['state'] = Variable<int>(state);
    }
    if (!nullToAbsent || owner != null) {
      map['owner'] = Variable<String>(owner);
    }
    if (!nullToAbsent || avatarUrl != null) {
      map['avatar_url'] = Variable<String>(avatarUrl);
    }
    if (!nullToAbsent || avatarPath != null) {
      map['avatar_path'] = Variable<String>(avatarPath);
    }
    if (!nullToAbsent || describe != null) {
      map['describe'] = Variable<String>(describe);
    }
    if (!nullToAbsent || minMsgUuid != null) {
      map['min_msg_uuid'] = Variable<String>(minMsgUuid);
    }
    if (!nullToAbsent || maxMsgUuid != null) {
      map['max_msg_uuid'] = Variable<String>(maxMsgUuid);
    }
    if (!nullToAbsent || maxMsgTime != null) {
      map['max_msg_time'] = Variable<int>(maxMsgTime);
    }
    if (!nullToAbsent || backgroundPath != null) {
      map['background_path'] = Variable<String>(backgroundPath);
    }
    if (!nullToAbsent || backgroundUrl != null) {
      map['background_url'] = Variable<String>(backgroundUrl);
    }
    if (!nullToAbsent || allMute != null) {
      map['all_mute'] = Variable<bool>(allMute);
    }
    if (!nullToAbsent || joinVerify != null) {
      map['join_verify'] = Variable<bool>(joinVerify);
    }
    if (!nullToAbsent || inviteLimit != null) {
      map['invite_limit'] = Variable<bool>(inviteLimit);
    }
    if (!nullToAbsent || createTime != null) {
      map['create_time'] = Variable<double>(createTime);
    }
    if (!nullToAbsent || updateTime != null) {
      map['update_time'] = Variable<double>(updateTime);
    }
    if (!nullToAbsent || announcement != null) {
      map['announcement'] = Variable<String>(announcement);
    }
    if (!nullToAbsent || attribute != null) {
      map['attribute'] = Variable<int>(attribute);
    }
    if (!nullToAbsent || options != null) {
      map['options'] = Variable<String>(options);
    }
    if (!nullToAbsent || memberCount != null) {
      map['member_count'] = Variable<int>(memberCount);
    }
    if (!nullToAbsent || limit != null) {
      map['limit'] = Variable<int>(limit);
    }
    if (!nullToAbsent || chain != null) {
      map['chain'] = Variable<String>(chain);
    }
    if (!nullToAbsent || tokenAddress != null) {
      map['token_address'] = Variable<String>(tokenAddress);
    }
    if (!nullToAbsent || minNumToken != null) {
      map['min_num_token'] = Variable<double>(minNumToken);
    }
    if (!nullToAbsent || tags != null) {
      map['tags'] = Variable<String>(tags);
    }
    return map;
  }

  ChannelInfoCompanion toCompanion(bool nullToAbsent) {
    return ChannelInfoCompanion(
      id: Value(id),
      channelId: Value(channelId),
      title:
          title == null && nullToAbsent ? const Value.absent() : Value(title),
      state:
          state == null && nullToAbsent ? const Value.absent() : Value(state),
      owner:
          owner == null && nullToAbsent ? const Value.absent() : Value(owner),
      avatarUrl: avatarUrl == null && nullToAbsent
          ? const Value.absent()
          : Value(avatarUrl),
      avatarPath: avatarPath == null && nullToAbsent
          ? const Value.absent()
          : Value(avatarPath),
      describe: describe == null && nullToAbsent
          ? const Value.absent()
          : Value(describe),
      minMsgUuid: minMsgUuid == null && nullToAbsent
          ? const Value.absent()
          : Value(minMsgUuid),
      maxMsgUuid: maxMsgUuid == null && nullToAbsent
          ? const Value.absent()
          : Value(maxMsgUuid),
      maxMsgTime: maxMsgTime == null && nullToAbsent
          ? const Value.absent()
          : Value(maxMsgTime),
      backgroundPath: backgroundPath == null && nullToAbsent
          ? const Value.absent()
          : Value(backgroundPath),
      backgroundUrl: backgroundUrl == null && nullToAbsent
          ? const Value.absent()
          : Value(backgroundUrl),
      allMute: allMute == null && nullToAbsent
          ? const Value.absent()
          : Value(allMute),
      joinVerify: joinVerify == null && nullToAbsent
          ? const Value.absent()
          : Value(joinVerify),
      inviteLimit: inviteLimit == null && nullToAbsent
          ? const Value.absent()
          : Value(inviteLimit),
      createTime: createTime == null && nullToAbsent
          ? const Value.absent()
          : Value(createTime),
      updateTime: updateTime == null && nullToAbsent
          ? const Value.absent()
          : Value(updateTime),
      announcement: announcement == null && nullToAbsent
          ? const Value.absent()
          : Value(announcement),
      attribute: attribute == null && nullToAbsent
          ? const Value.absent()
          : Value(attribute),
      options: options == null && nullToAbsent
          ? const Value.absent()
          : Value(options),
      memberCount: memberCount == null && nullToAbsent
          ? const Value.absent()
          : Value(memberCount),
      limit:
          limit == null && nullToAbsent ? const Value.absent() : Value(limit),
      chain:
          chain == null && nullToAbsent ? const Value.absent() : Value(chain),
      tokenAddress: tokenAddress == null && nullToAbsent
          ? const Value.absent()
          : Value(tokenAddress),
      minNumToken: minNumToken == null && nullToAbsent
          ? const Value.absent()
          : Value(minNumToken),
      tags: tags == null && nullToAbsent ? const Value.absent() : Value(tags),
    );
  }

  factory ChannelInfoData.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return ChannelInfoData(
      id: serializer.fromJson<int>(json['id']),
      channelId: serializer.fromJson<String>(json['channel_id']),
      title: serializer.fromJson<String?>(json['title']),
      state: serializer.fromJson<int?>(json['state']),
      owner: serializer.fromJson<String?>(json['owner']),
      avatarUrl: serializer.fromJson<String?>(json['avatar_url']),
      avatarPath: serializer.fromJson<String?>(json['avatar_path']),
      describe: serializer.fromJson<String?>(json['describe']),
      minMsgUuid: serializer.fromJson<String?>(json['min_msg_uuid']),
      maxMsgUuid: serializer.fromJson<String?>(json['max_msg_uuid']),
      maxMsgTime: serializer.fromJson<int?>(json['max_msg_time']),
      backgroundPath: serializer.fromJson<String?>(json['background_path']),
      backgroundUrl: serializer.fromJson<String?>(json['background_url']),
      allMute: serializer.fromJson<bool?>(json['all_mute']),
      joinVerify: serializer.fromJson<bool?>(json['join_verify']),
      inviteLimit: serializer.fromJson<bool?>(json['invite_limit']),
      createTime: serializer.fromJson<double?>(json['create_time']),
      updateTime: serializer.fromJson<double?>(json['update_time']),
      announcement: serializer.fromJson<String?>(json['announcement']),
      attribute: serializer.fromJson<int?>(json['attribute']),
      options: serializer.fromJson<String?>(json['options']),
      memberCount: serializer.fromJson<int?>(json['member_count']),
      limit: serializer.fromJson<int?>(json['limit']),
      chain: serializer.fromJson<String?>(json['chain']),
      tokenAddress: serializer.fromJson<String?>(json['token_address']),
      minNumToken: serializer.fromJson<double?>(json['min_num_token']),
      tags: serializer.fromJson<String?>(json['tags']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<int>(id),
      'channel_id': serializer.toJson<String>(channelId),
      'title': serializer.toJson<String?>(title),
      'state': serializer.toJson<int?>(state),
      'owner': serializer.toJson<String?>(owner),
      'avatar_url': serializer.toJson<String?>(avatarUrl),
      'avatar_path': serializer.toJson<String?>(avatarPath),
      'describe': serializer.toJson<String?>(describe),
      'min_msg_uuid': serializer.toJson<String?>(minMsgUuid),
      'max_msg_uuid': serializer.toJson<String?>(maxMsgUuid),
      'max_msg_time': serializer.toJson<int?>(maxMsgTime),
      'background_path': serializer.toJson<String?>(backgroundPath),
      'background_url': serializer.toJson<String?>(backgroundUrl),
      'all_mute': serializer.toJson<bool?>(allMute),
      'join_verify': serializer.toJson<bool?>(joinVerify),
      'invite_limit': serializer.toJson<bool?>(inviteLimit),
      'create_time': serializer.toJson<double?>(createTime),
      'update_time': serializer.toJson<double?>(updateTime),
      'announcement': serializer.toJson<String?>(announcement),
      'attribute': serializer.toJson<int?>(attribute),
      'options': serializer.toJson<String?>(options),
      'member_count': serializer.toJson<int?>(memberCount),
      'limit': serializer.toJson<int?>(limit),
      'chain': serializer.toJson<String?>(chain),
      'token_address': serializer.toJson<String?>(tokenAddress),
      'min_num_token': serializer.toJson<double?>(minNumToken),
      'tags': serializer.toJson<String?>(tags),
    };
  }

  ChannelInfoData copyWith(
          {int? id,
          String? channelId,
          Value<String?> title = const Value.absent(),
          Value<int?> state = const Value.absent(),
          Value<String?> owner = const Value.absent(),
          Value<String?> avatarUrl = const Value.absent(),
          Value<String?> avatarPath = const Value.absent(),
          Value<String?> describe = const Value.absent(),
          Value<String?> minMsgUuid = const Value.absent(),
          Value<String?> maxMsgUuid = const Value.absent(),
          Value<int?> maxMsgTime = const Value.absent(),
          Value<String?> backgroundPath = const Value.absent(),
          Value<String?> backgroundUrl = const Value.absent(),
          Value<bool?> allMute = const Value.absent(),
          Value<bool?> joinVerify = const Value.absent(),
          Value<bool?> inviteLimit = const Value.absent(),
          Value<double?> createTime = const Value.absent(),
          Value<double?> updateTime = const Value.absent(),
          Value<String?> announcement = const Value.absent(),
          Value<int?> attribute = const Value.absent(),
          Value<String?> options = const Value.absent(),
          Value<int?> memberCount = const Value.absent(),
          Value<int?> limit = const Value.absent(),
          Value<String?> chain = const Value.absent(),
          Value<String?> tokenAddress = const Value.absent(),
          Value<double?> minNumToken = const Value.absent(),
          Value<String?> tags = const Value.absent()}) =>
      ChannelInfoData(
        id: id ?? this.id,
        channelId: channelId ?? this.channelId,
        title: title.present ? title.value : this.title,
        state: state.present ? state.value : this.state,
        owner: owner.present ? owner.value : this.owner,
        avatarUrl: avatarUrl.present ? avatarUrl.value : this.avatarUrl,
        avatarPath: avatarPath.present ? avatarPath.value : this.avatarPath,
        describe: describe.present ? describe.value : this.describe,
        minMsgUuid: minMsgUuid.present ? minMsgUuid.value : this.minMsgUuid,
        maxMsgUuid: maxMsgUuid.present ? maxMsgUuid.value : this.maxMsgUuid,
        maxMsgTime: maxMsgTime.present ? maxMsgTime.value : this.maxMsgTime,
        backgroundPath:
            backgroundPath.present ? backgroundPath.value : this.backgroundPath,
        backgroundUrl:
            backgroundUrl.present ? backgroundUrl.value : this.backgroundUrl,
        allMute: allMute.present ? allMute.value : this.allMute,
        joinVerify: joinVerify.present ? joinVerify.value : this.joinVerify,
        inviteLimit: inviteLimit.present ? inviteLimit.value : this.inviteLimit,
        createTime: createTime.present ? createTime.value : this.createTime,
        updateTime: updateTime.present ? updateTime.value : this.updateTime,
        announcement:
            announcement.present ? announcement.value : this.announcement,
        attribute: attribute.present ? attribute.value : this.attribute,
        options: options.present ? options.value : this.options,
        memberCount: memberCount.present ? memberCount.value : this.memberCount,
        limit: limit.present ? limit.value : this.limit,
        chain: chain.present ? chain.value : this.chain,
        tokenAddress:
            tokenAddress.present ? tokenAddress.value : this.tokenAddress,
        minNumToken: minNumToken.present ? minNumToken.value : this.minNumToken,
        tags: tags.present ? tags.value : this.tags,
      );
  @override
  String toString() {
    return (StringBuffer('ChannelInfoData(')
          ..write('id: $id, ')
          ..write('channelId: $channelId, ')
          ..write('title: $title, ')
          ..write('state: $state, ')
          ..write('owner: $owner, ')
          ..write('avatarUrl: $avatarUrl, ')
          ..write('avatarPath: $avatarPath, ')
          ..write('describe: $describe, ')
          ..write('minMsgUuid: $minMsgUuid, ')
          ..write('maxMsgUuid: $maxMsgUuid, ')
          ..write('maxMsgTime: $maxMsgTime, ')
          ..write('backgroundPath: $backgroundPath, ')
          ..write('backgroundUrl: $backgroundUrl, ')
          ..write('allMute: $allMute, ')
          ..write('joinVerify: $joinVerify, ')
          ..write('inviteLimit: $inviteLimit, ')
          ..write('createTime: $createTime, ')
          ..write('updateTime: $updateTime, ')
          ..write('announcement: $announcement, ')
          ..write('attribute: $attribute, ')
          ..write('options: $options, ')
          ..write('memberCount: $memberCount, ')
          ..write('limit: $limit, ')
          ..write('chain: $chain, ')
          ..write('tokenAddress: $tokenAddress, ')
          ..write('minNumToken: $minNumToken, ')
          ..write('tags: $tags')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hashAll([
        id,
        channelId,
        title,
        state,
        owner,
        avatarUrl,
        avatarPath,
        describe,
        minMsgUuid,
        maxMsgUuid,
        maxMsgTime,
        backgroundPath,
        backgroundUrl,
        allMute,
        joinVerify,
        inviteLimit,
        createTime,
        updateTime,
        announcement,
        attribute,
        options,
        memberCount,
        limit,
        chain,
        tokenAddress,
        minNumToken,
        tags
      ]);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is ChannelInfoData &&
          other.id == this.id &&
          other.channelId == this.channelId &&
          other.title == this.title &&
          other.state == this.state &&
          other.owner == this.owner &&
          other.avatarUrl == this.avatarUrl &&
          other.avatarPath == this.avatarPath &&
          other.describe == this.describe &&
          other.minMsgUuid == this.minMsgUuid &&
          other.maxMsgUuid == this.maxMsgUuid &&
          other.maxMsgTime == this.maxMsgTime &&
          other.backgroundPath == this.backgroundPath &&
          other.backgroundUrl == this.backgroundUrl &&
          other.allMute == this.allMute &&
          other.joinVerify == this.joinVerify &&
          other.inviteLimit == this.inviteLimit &&
          other.createTime == this.createTime &&
          other.updateTime == this.updateTime &&
          other.announcement == this.announcement &&
          other.attribute == this.attribute &&
          other.options == this.options &&
          other.memberCount == this.memberCount &&
          other.limit == this.limit &&
          other.chain == this.chain &&
          other.tokenAddress == this.tokenAddress &&
          other.minNumToken == this.minNumToken &&
          other.tags == this.tags);
}

class ChannelInfoCompanion extends UpdateCompanion<ChannelInfoData> {
  final Value<int> id;
  final Value<String> channelId;
  final Value<String?> title;
  final Value<int?> state;
  final Value<String?> owner;
  final Value<String?> avatarUrl;
  final Value<String?> avatarPath;
  final Value<String?> describe;
  final Value<String?> minMsgUuid;
  final Value<String?> maxMsgUuid;
  final Value<int?> maxMsgTime;
  final Value<String?> backgroundPath;
  final Value<String?> backgroundUrl;
  final Value<bool?> allMute;
  final Value<bool?> joinVerify;
  final Value<bool?> inviteLimit;
  final Value<double?> createTime;
  final Value<double?> updateTime;
  final Value<String?> announcement;
  final Value<int?> attribute;
  final Value<String?> options;
  final Value<int?> memberCount;
  final Value<int?> limit;
  final Value<String?> chain;
  final Value<String?> tokenAddress;
  final Value<double?> minNumToken;
  final Value<String?> tags;
  const ChannelInfoCompanion({
    this.id = const Value.absent(),
    this.channelId = const Value.absent(),
    this.title = const Value.absent(),
    this.state = const Value.absent(),
    this.owner = const Value.absent(),
    this.avatarUrl = const Value.absent(),
    this.avatarPath = const Value.absent(),
    this.describe = const Value.absent(),
    this.minMsgUuid = const Value.absent(),
    this.maxMsgUuid = const Value.absent(),
    this.maxMsgTime = const Value.absent(),
    this.backgroundPath = const Value.absent(),
    this.backgroundUrl = const Value.absent(),
    this.allMute = const Value.absent(),
    this.joinVerify = const Value.absent(),
    this.inviteLimit = const Value.absent(),
    this.createTime = const Value.absent(),
    this.updateTime = const Value.absent(),
    this.announcement = const Value.absent(),
    this.attribute = const Value.absent(),
    this.options = const Value.absent(),
    this.memberCount = const Value.absent(),
    this.limit = const Value.absent(),
    this.chain = const Value.absent(),
    this.tokenAddress = const Value.absent(),
    this.minNumToken = const Value.absent(),
    this.tags = const Value.absent(),
  });
  ChannelInfoCompanion.insert({
    this.id = const Value.absent(),
    required String channelId,
    this.title = const Value.absent(),
    this.state = const Value.absent(),
    this.owner = const Value.absent(),
    this.avatarUrl = const Value.absent(),
    this.avatarPath = const Value.absent(),
    this.describe = const Value.absent(),
    this.minMsgUuid = const Value.absent(),
    this.maxMsgUuid = const Value.absent(),
    this.maxMsgTime = const Value.absent(),
    this.backgroundPath = const Value.absent(),
    this.backgroundUrl = const Value.absent(),
    this.allMute = const Value.absent(),
    this.joinVerify = const Value.absent(),
    this.inviteLimit = const Value.absent(),
    this.createTime = const Value.absent(),
    this.updateTime = const Value.absent(),
    this.announcement = const Value.absent(),
    this.attribute = const Value.absent(),
    this.options = const Value.absent(),
    this.memberCount = const Value.absent(),
    this.limit = const Value.absent(),
    this.chain = const Value.absent(),
    this.tokenAddress = const Value.absent(),
    this.minNumToken = const Value.absent(),
    this.tags = const Value.absent(),
  }) : channelId = Value(channelId);
  static Insertable<ChannelInfoData> custom({
    Expression<int>? id,
    Expression<String>? channelId,
    Expression<String>? title,
    Expression<int>? state,
    Expression<String>? owner,
    Expression<String>? avatarUrl,
    Expression<String>? avatarPath,
    Expression<String>? describe,
    Expression<String>? minMsgUuid,
    Expression<String>? maxMsgUuid,
    Expression<int>? maxMsgTime,
    Expression<String>? backgroundPath,
    Expression<String>? backgroundUrl,
    Expression<bool>? allMute,
    Expression<bool>? joinVerify,
    Expression<bool>? inviteLimit,
    Expression<double>? createTime,
    Expression<double>? updateTime,
    Expression<String>? announcement,
    Expression<int>? attribute,
    Expression<String>? options,
    Expression<int>? memberCount,
    Expression<int>? limit,
    Expression<String>? chain,
    Expression<String>? tokenAddress,
    Expression<double>? minNumToken,
    Expression<String>? tags,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (channelId != null) 'channel_id': channelId,
      if (title != null) 'title': title,
      if (state != null) 'state': state,
      if (owner != null) 'owner': owner,
      if (avatarUrl != null) 'avatar_url': avatarUrl,
      if (avatarPath != null) 'avatar_path': avatarPath,
      if (describe != null) 'describe': describe,
      if (minMsgUuid != null) 'min_msg_uuid': minMsgUuid,
      if (maxMsgUuid != null) 'max_msg_uuid': maxMsgUuid,
      if (maxMsgTime != null) 'max_msg_time': maxMsgTime,
      if (backgroundPath != null) 'background_path': backgroundPath,
      if (backgroundUrl != null) 'background_url': backgroundUrl,
      if (allMute != null) 'all_mute': allMute,
      if (joinVerify != null) 'join_verify': joinVerify,
      if (inviteLimit != null) 'invite_limit': inviteLimit,
      if (createTime != null) 'create_time': createTime,
      if (updateTime != null) 'update_time': updateTime,
      if (announcement != null) 'announcement': announcement,
      if (attribute != null) 'attribute': attribute,
      if (options != null) 'options': options,
      if (memberCount != null) 'member_count': memberCount,
      if (limit != null) 'limit': limit,
      if (chain != null) 'chain': chain,
      if (tokenAddress != null) 'token_address': tokenAddress,
      if (minNumToken != null) 'min_num_token': minNumToken,
      if (tags != null) 'tags': tags,
    });
  }

  ChannelInfoCompanion copyWith(
      {Value<int>? id,
      Value<String>? channelId,
      Value<String?>? title,
      Value<int?>? state,
      Value<String?>? owner,
      Value<String?>? avatarUrl,
      Value<String?>? avatarPath,
      Value<String?>? describe,
      Value<String?>? minMsgUuid,
      Value<String?>? maxMsgUuid,
      Value<int?>? maxMsgTime,
      Value<String?>? backgroundPath,
      Value<String?>? backgroundUrl,
      Value<bool?>? allMute,
      Value<bool?>? joinVerify,
      Value<bool?>? inviteLimit,
      Value<double?>? createTime,
      Value<double?>? updateTime,
      Value<String?>? announcement,
      Value<int?>? attribute,
      Value<String?>? options,
      Value<int?>? memberCount,
      Value<int?>? limit,
      Value<String?>? chain,
      Value<String?>? tokenAddress,
      Value<double?>? minNumToken,
      Value<String?>? tags}) {
    return ChannelInfoCompanion(
      id: id ?? this.id,
      channelId: channelId ?? this.channelId,
      title: title ?? this.title,
      state: state ?? this.state,
      owner: owner ?? this.owner,
      avatarUrl: avatarUrl ?? this.avatarUrl,
      avatarPath: avatarPath ?? this.avatarPath,
      describe: describe ?? this.describe,
      minMsgUuid: minMsgUuid ?? this.minMsgUuid,
      maxMsgUuid: maxMsgUuid ?? this.maxMsgUuid,
      maxMsgTime: maxMsgTime ?? this.maxMsgTime,
      backgroundPath: backgroundPath ?? this.backgroundPath,
      backgroundUrl: backgroundUrl ?? this.backgroundUrl,
      allMute: allMute ?? this.allMute,
      joinVerify: joinVerify ?? this.joinVerify,
      inviteLimit: inviteLimit ?? this.inviteLimit,
      createTime: createTime ?? this.createTime,
      updateTime: updateTime ?? this.updateTime,
      announcement: announcement ?? this.announcement,
      attribute: attribute ?? this.attribute,
      options: options ?? this.options,
      memberCount: memberCount ?? this.memberCount,
      limit: limit ?? this.limit,
      chain: chain ?? this.chain,
      tokenAddress: tokenAddress ?? this.tokenAddress,
      minNumToken: minNumToken ?? this.minNumToken,
      tags: tags ?? this.tags,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<int>(id.value);
    }
    if (channelId.present) {
      map['channel_id'] = Variable<String>(channelId.value);
    }
    if (title.present) {
      map['title'] = Variable<String>(title.value);
    }
    if (state.present) {
      map['state'] = Variable<int>(state.value);
    }
    if (owner.present) {
      map['owner'] = Variable<String>(owner.value);
    }
    if (avatarUrl.present) {
      map['avatar_url'] = Variable<String>(avatarUrl.value);
    }
    if (avatarPath.present) {
      map['avatar_path'] = Variable<String>(avatarPath.value);
    }
    if (describe.present) {
      map['describe'] = Variable<String>(describe.value);
    }
    if (minMsgUuid.present) {
      map['min_msg_uuid'] = Variable<String>(minMsgUuid.value);
    }
    if (maxMsgUuid.present) {
      map['max_msg_uuid'] = Variable<String>(maxMsgUuid.value);
    }
    if (maxMsgTime.present) {
      map['max_msg_time'] = Variable<int>(maxMsgTime.value);
    }
    if (backgroundPath.present) {
      map['background_path'] = Variable<String>(backgroundPath.value);
    }
    if (backgroundUrl.present) {
      map['background_url'] = Variable<String>(backgroundUrl.value);
    }
    if (allMute.present) {
      map['all_mute'] = Variable<bool>(allMute.value);
    }
    if (joinVerify.present) {
      map['join_verify'] = Variable<bool>(joinVerify.value);
    }
    if (inviteLimit.present) {
      map['invite_limit'] = Variable<bool>(inviteLimit.value);
    }
    if (createTime.present) {
      map['create_time'] = Variable<double>(createTime.value);
    }
    if (updateTime.present) {
      map['update_time'] = Variable<double>(updateTime.value);
    }
    if (announcement.present) {
      map['announcement'] = Variable<String>(announcement.value);
    }
    if (attribute.present) {
      map['attribute'] = Variable<int>(attribute.value);
    }
    if (options.present) {
      map['options'] = Variable<String>(options.value);
    }
    if (memberCount.present) {
      map['member_count'] = Variable<int>(memberCount.value);
    }
    if (limit.present) {
      map['limit'] = Variable<int>(limit.value);
    }
    if (chain.present) {
      map['chain'] = Variable<String>(chain.value);
    }
    if (tokenAddress.present) {
      map['token_address'] = Variable<String>(tokenAddress.value);
    }
    if (minNumToken.present) {
      map['min_num_token'] = Variable<double>(minNumToken.value);
    }
    if (tags.present) {
      map['tags'] = Variable<String>(tags.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('ChannelInfoCompanion(')
          ..write('id: $id, ')
          ..write('channelId: $channelId, ')
          ..write('title: $title, ')
          ..write('state: $state, ')
          ..write('owner: $owner, ')
          ..write('avatarUrl: $avatarUrl, ')
          ..write('avatarPath: $avatarPath, ')
          ..write('describe: $describe, ')
          ..write('minMsgUuid: $minMsgUuid, ')
          ..write('maxMsgUuid: $maxMsgUuid, ')
          ..write('maxMsgTime: $maxMsgTime, ')
          ..write('backgroundPath: $backgroundPath, ')
          ..write('backgroundUrl: $backgroundUrl, ')
          ..write('allMute: $allMute, ')
          ..write('joinVerify: $joinVerify, ')
          ..write('inviteLimit: $inviteLimit, ')
          ..write('createTime: $createTime, ')
          ..write('updateTime: $updateTime, ')
          ..write('announcement: $announcement, ')
          ..write('attribute: $attribute, ')
          ..write('options: $options, ')
          ..write('memberCount: $memberCount, ')
          ..write('limit: $limit, ')
          ..write('chain: $chain, ')
          ..write('tokenAddress: $tokenAddress, ')
          ..write('minNumToken: $minNumToken, ')
          ..write('tags: $tags')
          ..write(')'))
        .toString();
  }
}

class ProxyInfo extends Table with TableInfo<ProxyInfo, ProxyInfoData> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  ProxyInfo(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  late final GeneratedColumn<int> id = GeneratedColumn<int>(
      'id', aliasedName, false,
      hasAutoIncrement: true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      $customConstraints: 'NOT NULL PRIMARY KEY AUTOINCREMENT');
  static const VerificationMeta _uuidMeta = const VerificationMeta('uuid');
  late final GeneratedColumn<String> uuid = GeneratedColumn<String>(
      'uuid', aliasedName, false,
      type: DriftSqlType.string,
      requiredDuringInsert: true,
      $customConstraints: 'NOT NULL UNIQUE');
  static const VerificationMeta _nameMeta = const VerificationMeta('name');
  late final GeneratedColumn<String> name = GeneratedColumn<String>(
      'name', aliasedName, false,
      type: DriftSqlType.string,
      requiredDuringInsert: true,
      $customConstraints: 'NOT NULL');
  static const VerificationMeta _hostMeta = const VerificationMeta('host');
  late final GeneratedColumn<String> host = GeneratedColumn<String>(
      'host', aliasedName, false,
      type: DriftSqlType.string,
      requiredDuringInsert: true,
      $customConstraints: 'NOT NULL');
  static const VerificationMeta _portMeta = const VerificationMeta('port');
  late final GeneratedColumn<String> port = GeneratedColumn<String>(
      'port', aliasedName, false,
      type: DriftSqlType.string,
      requiredDuringInsert: true,
      $customConstraints: 'NOT NULL');
  static const VerificationMeta _userMeta = const VerificationMeta('user');
  late final GeneratedColumn<String> user = GeneratedColumn<String>(
      'user', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _pwdMeta = const VerificationMeta('pwd');
  late final GeneratedColumn<String> pwd = GeneratedColumn<String>(
      'pwd', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _enableMeta = const VerificationMeta('enable');
  late final GeneratedColumn<bool> enable = GeneratedColumn<bool>(
      'enable', aliasedName, true,
      type: DriftSqlType.bool,
      requiredDuringInsert: false,
      $customConstraints: 'DEFAULT FALSE',
      defaultValue: const CustomExpression('FALSE'));
  static const VerificationMeta _isHttpMeta = const VerificationMeta('isHttp');
  late final GeneratedColumn<bool> isHttp = GeneratedColumn<bool>(
      'is_http', aliasedName, true,
      type: DriftSqlType.bool,
      requiredDuringInsert: false,
      $customConstraints: 'DEFAULT TRUE',
      defaultValue: const CustomExpression('TRUE'));
  static const VerificationMeta _createTimeMeta =
      const VerificationMeta('createTime');
  late final GeneratedColumn<double> createTime = GeneratedColumn<double>(
      'create_time', aliasedName, true,
      type: DriftSqlType.double,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _updateTimeMeta =
      const VerificationMeta('updateTime');
  late final GeneratedColumn<double> updateTime = GeneratedColumn<double>(
      'update_time', aliasedName, true,
      type: DriftSqlType.double,
      requiredDuringInsert: false,
      $customConstraints: '');
  @override
  List<GeneratedColumn> get $columns => [
        id,
        uuid,
        name,
        host,
        port,
        user,
        pwd,
        enable,
        isHttp,
        createTime,
        updateTime
      ];
  @override
  String get aliasedName => _alias ?? 'proxy_info';
  @override
  String get actualTableName => 'proxy_info';
  @override
  VerificationContext validateIntegrity(Insertable<ProxyInfoData> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    }
    if (data.containsKey('uuid')) {
      context.handle(
          _uuidMeta, uuid.isAcceptableOrUnknown(data['uuid']!, _uuidMeta));
    } else if (isInserting) {
      context.missing(_uuidMeta);
    }
    if (data.containsKey('name')) {
      context.handle(
          _nameMeta, name.isAcceptableOrUnknown(data['name']!, _nameMeta));
    } else if (isInserting) {
      context.missing(_nameMeta);
    }
    if (data.containsKey('host')) {
      context.handle(
          _hostMeta, host.isAcceptableOrUnknown(data['host']!, _hostMeta));
    } else if (isInserting) {
      context.missing(_hostMeta);
    }
    if (data.containsKey('port')) {
      context.handle(
          _portMeta, port.isAcceptableOrUnknown(data['port']!, _portMeta));
    } else if (isInserting) {
      context.missing(_portMeta);
    }
    if (data.containsKey('user')) {
      context.handle(
          _userMeta, user.isAcceptableOrUnknown(data['user']!, _userMeta));
    }
    if (data.containsKey('pwd')) {
      context.handle(
          _pwdMeta, pwd.isAcceptableOrUnknown(data['pwd']!, _pwdMeta));
    }
    if (data.containsKey('enable')) {
      context.handle(_enableMeta,
          enable.isAcceptableOrUnknown(data['enable']!, _enableMeta));
    }
    if (data.containsKey('is_http')) {
      context.handle(_isHttpMeta,
          isHttp.isAcceptableOrUnknown(data['is_http']!, _isHttpMeta));
    }
    if (data.containsKey('create_time')) {
      context.handle(
          _createTimeMeta,
          createTime.isAcceptableOrUnknown(
              data['create_time']!, _createTimeMeta));
    }
    if (data.containsKey('update_time')) {
      context.handle(
          _updateTimeMeta,
          updateTime.isAcceptableOrUnknown(
              data['update_time']!, _updateTimeMeta));
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  ProxyInfoData map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return ProxyInfoData(
      id: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}id'])!,
      uuid: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}uuid'])!,
      name: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}name'])!,
      host: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}host'])!,
      port: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}port'])!,
      user: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}user']),
      pwd: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}pwd']),
      enable: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}enable']),
      isHttp: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}is_http']),
      createTime: attachedDatabase.typeMapping
          .read(DriftSqlType.double, data['${effectivePrefix}create_time']),
      updateTime: attachedDatabase.typeMapping
          .read(DriftSqlType.double, data['${effectivePrefix}update_time']),
    );
  }

  @override
  ProxyInfo createAlias(String alias) {
    return ProxyInfo(attachedDatabase, alias);
  }

  @override
  bool get dontWriteConstraints => true;
}

class ProxyInfoData extends DataClass implements Insertable<ProxyInfoData> {
  final int id;
  final String uuid;
  final String name;
  final String host;
  final String port;
  final String? user;
  final String? pwd;
  final bool? enable;
  final bool? isHttp;
  final double? createTime;
  final double? updateTime;
  const ProxyInfoData(
      {required this.id,
      required this.uuid,
      required this.name,
      required this.host,
      required this.port,
      this.user,
      this.pwd,
      this.enable,
      this.isHttp,
      this.createTime,
      this.updateTime});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<int>(id);
    map['uuid'] = Variable<String>(uuid);
    map['name'] = Variable<String>(name);
    map['host'] = Variable<String>(host);
    map['port'] = Variable<String>(port);
    if (!nullToAbsent || user != null) {
      map['user'] = Variable<String>(user);
    }
    if (!nullToAbsent || pwd != null) {
      map['pwd'] = Variable<String>(pwd);
    }
    if (!nullToAbsent || enable != null) {
      map['enable'] = Variable<bool>(enable);
    }
    if (!nullToAbsent || isHttp != null) {
      map['is_http'] = Variable<bool>(isHttp);
    }
    if (!nullToAbsent || createTime != null) {
      map['create_time'] = Variable<double>(createTime);
    }
    if (!nullToAbsent || updateTime != null) {
      map['update_time'] = Variable<double>(updateTime);
    }
    return map;
  }

  ProxyInfoCompanion toCompanion(bool nullToAbsent) {
    return ProxyInfoCompanion(
      id: Value(id),
      uuid: Value(uuid),
      name: Value(name),
      host: Value(host),
      port: Value(port),
      user: user == null && nullToAbsent ? const Value.absent() : Value(user),
      pwd: pwd == null && nullToAbsent ? const Value.absent() : Value(pwd),
      enable:
          enable == null && nullToAbsent ? const Value.absent() : Value(enable),
      isHttp:
          isHttp == null && nullToAbsent ? const Value.absent() : Value(isHttp),
      createTime: createTime == null && nullToAbsent
          ? const Value.absent()
          : Value(createTime),
      updateTime: updateTime == null && nullToAbsent
          ? const Value.absent()
          : Value(updateTime),
    );
  }

  factory ProxyInfoData.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return ProxyInfoData(
      id: serializer.fromJson<int>(json['id']),
      uuid: serializer.fromJson<String>(json['uuid']),
      name: serializer.fromJson<String>(json['name']),
      host: serializer.fromJson<String>(json['host']),
      port: serializer.fromJson<String>(json['port']),
      user: serializer.fromJson<String?>(json['user']),
      pwd: serializer.fromJson<String?>(json['pwd']),
      enable: serializer.fromJson<bool?>(json['enable']),
      isHttp: serializer.fromJson<bool?>(json['is_http']),
      createTime: serializer.fromJson<double?>(json['create_time']),
      updateTime: serializer.fromJson<double?>(json['update_time']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<int>(id),
      'uuid': serializer.toJson<String>(uuid),
      'name': serializer.toJson<String>(name),
      'host': serializer.toJson<String>(host),
      'port': serializer.toJson<String>(port),
      'user': serializer.toJson<String?>(user),
      'pwd': serializer.toJson<String?>(pwd),
      'enable': serializer.toJson<bool?>(enable),
      'is_http': serializer.toJson<bool?>(isHttp),
      'create_time': serializer.toJson<double?>(createTime),
      'update_time': serializer.toJson<double?>(updateTime),
    };
  }

  ProxyInfoData copyWith(
          {int? id,
          String? uuid,
          String? name,
          String? host,
          String? port,
          Value<String?> user = const Value.absent(),
          Value<String?> pwd = const Value.absent(),
          Value<bool?> enable = const Value.absent(),
          Value<bool?> isHttp = const Value.absent(),
          Value<double?> createTime = const Value.absent(),
          Value<double?> updateTime = const Value.absent()}) =>
      ProxyInfoData(
        id: id ?? this.id,
        uuid: uuid ?? this.uuid,
        name: name ?? this.name,
        host: host ?? this.host,
        port: port ?? this.port,
        user: user.present ? user.value : this.user,
        pwd: pwd.present ? pwd.value : this.pwd,
        enable: enable.present ? enable.value : this.enable,
        isHttp: isHttp.present ? isHttp.value : this.isHttp,
        createTime: createTime.present ? createTime.value : this.createTime,
        updateTime: updateTime.present ? updateTime.value : this.updateTime,
      );
  @override
  String toString() {
    return (StringBuffer('ProxyInfoData(')
          ..write('id: $id, ')
          ..write('uuid: $uuid, ')
          ..write('name: $name, ')
          ..write('host: $host, ')
          ..write('port: $port, ')
          ..write('user: $user, ')
          ..write('pwd: $pwd, ')
          ..write('enable: $enable, ')
          ..write('isHttp: $isHttp, ')
          ..write('createTime: $createTime, ')
          ..write('updateTime: $updateTime')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(id, uuid, name, host, port, user, pwd, enable,
      isHttp, createTime, updateTime);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is ProxyInfoData &&
          other.id == this.id &&
          other.uuid == this.uuid &&
          other.name == this.name &&
          other.host == this.host &&
          other.port == this.port &&
          other.user == this.user &&
          other.pwd == this.pwd &&
          other.enable == this.enable &&
          other.isHttp == this.isHttp &&
          other.createTime == this.createTime &&
          other.updateTime == this.updateTime);
}

class ProxyInfoCompanion extends UpdateCompanion<ProxyInfoData> {
  final Value<int> id;
  final Value<String> uuid;
  final Value<String> name;
  final Value<String> host;
  final Value<String> port;
  final Value<String?> user;
  final Value<String?> pwd;
  final Value<bool?> enable;
  final Value<bool?> isHttp;
  final Value<double?> createTime;
  final Value<double?> updateTime;
  const ProxyInfoCompanion({
    this.id = const Value.absent(),
    this.uuid = const Value.absent(),
    this.name = const Value.absent(),
    this.host = const Value.absent(),
    this.port = const Value.absent(),
    this.user = const Value.absent(),
    this.pwd = const Value.absent(),
    this.enable = const Value.absent(),
    this.isHttp = const Value.absent(),
    this.createTime = const Value.absent(),
    this.updateTime = const Value.absent(),
  });
  ProxyInfoCompanion.insert({
    this.id = const Value.absent(),
    required String uuid,
    required String name,
    required String host,
    required String port,
    this.user = const Value.absent(),
    this.pwd = const Value.absent(),
    this.enable = const Value.absent(),
    this.isHttp = const Value.absent(),
    this.createTime = const Value.absent(),
    this.updateTime = const Value.absent(),
  })  : uuid = Value(uuid),
        name = Value(name),
        host = Value(host),
        port = Value(port);
  static Insertable<ProxyInfoData> custom({
    Expression<int>? id,
    Expression<String>? uuid,
    Expression<String>? name,
    Expression<String>? host,
    Expression<String>? port,
    Expression<String>? user,
    Expression<String>? pwd,
    Expression<bool>? enable,
    Expression<bool>? isHttp,
    Expression<double>? createTime,
    Expression<double>? updateTime,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (uuid != null) 'uuid': uuid,
      if (name != null) 'name': name,
      if (host != null) 'host': host,
      if (port != null) 'port': port,
      if (user != null) 'user': user,
      if (pwd != null) 'pwd': pwd,
      if (enable != null) 'enable': enable,
      if (isHttp != null) 'is_http': isHttp,
      if (createTime != null) 'create_time': createTime,
      if (updateTime != null) 'update_time': updateTime,
    });
  }

  ProxyInfoCompanion copyWith(
      {Value<int>? id,
      Value<String>? uuid,
      Value<String>? name,
      Value<String>? host,
      Value<String>? port,
      Value<String?>? user,
      Value<String?>? pwd,
      Value<bool?>? enable,
      Value<bool?>? isHttp,
      Value<double?>? createTime,
      Value<double?>? updateTime}) {
    return ProxyInfoCompanion(
      id: id ?? this.id,
      uuid: uuid ?? this.uuid,
      name: name ?? this.name,
      host: host ?? this.host,
      port: port ?? this.port,
      user: user ?? this.user,
      pwd: pwd ?? this.pwd,
      enable: enable ?? this.enable,
      isHttp: isHttp ?? this.isHttp,
      createTime: createTime ?? this.createTime,
      updateTime: updateTime ?? this.updateTime,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<int>(id.value);
    }
    if (uuid.present) {
      map['uuid'] = Variable<String>(uuid.value);
    }
    if (name.present) {
      map['name'] = Variable<String>(name.value);
    }
    if (host.present) {
      map['host'] = Variable<String>(host.value);
    }
    if (port.present) {
      map['port'] = Variable<String>(port.value);
    }
    if (user.present) {
      map['user'] = Variable<String>(user.value);
    }
    if (pwd.present) {
      map['pwd'] = Variable<String>(pwd.value);
    }
    if (enable.present) {
      map['enable'] = Variable<bool>(enable.value);
    }
    if (isHttp.present) {
      map['is_http'] = Variable<bool>(isHttp.value);
    }
    if (createTime.present) {
      map['create_time'] = Variable<double>(createTime.value);
    }
    if (updateTime.present) {
      map['update_time'] = Variable<double>(updateTime.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('ProxyInfoCompanion(')
          ..write('id: $id, ')
          ..write('uuid: $uuid, ')
          ..write('name: $name, ')
          ..write('host: $host, ')
          ..write('port: $port, ')
          ..write('user: $user, ')
          ..write('pwd: $pwd, ')
          ..write('enable: $enable, ')
          ..write('isHttp: $isHttp, ')
          ..write('createTime: $createTime, ')
          ..write('updateTime: $updateTime')
          ..write(')'))
        .toString();
  }
}

class AdInfo extends Table with TableInfo<AdInfo, AdInfoData> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  AdInfo(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  late final GeneratedColumn<int> id = GeneratedColumn<int>(
      'id', aliasedName, false,
      hasAutoIncrement: true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      $customConstraints: 'NOT NULL PRIMARY KEY AUTOINCREMENT');
  static const VerificationMeta _uuidMeta = const VerificationMeta('uuid');
  late final GeneratedColumn<String> uuid = GeneratedColumn<String>(
      'uuid', aliasedName, false,
      type: DriftSqlType.string,
      requiredDuringInsert: true,
      $customConstraints: 'NOT NULL UNIQUE');
  static const VerificationMeta _topicMeta = const VerificationMeta('topic');
  late final GeneratedColumn<String> topic = GeneratedColumn<String>(
      'topic', aliasedName, false,
      type: DriftSqlType.string,
      requiredDuringInsert: true,
      $customConstraints: 'NOT NULL');
  static const VerificationMeta _timesMeta = const VerificationMeta('times');
  late final GeneratedColumn<double> times = GeneratedColumn<double>(
      'times', aliasedName, true,
      type: DriftSqlType.double,
      requiredDuringInsert: false,
      $customConstraints: '');
  static const VerificationMeta _updateTimeMeta =
      const VerificationMeta('updateTime');
  late final GeneratedColumn<double> updateTime = GeneratedColumn<double>(
      'update_time', aliasedName, true,
      type: DriftSqlType.double,
      requiredDuringInsert: false,
      $customConstraints: '');
  @override
  List<GeneratedColumn> get $columns => [id, uuid, topic, times, updateTime];
  @override
  String get aliasedName => _alias ?? 'ad_info';
  @override
  String get actualTableName => 'ad_info';
  @override
  VerificationContext validateIntegrity(Insertable<AdInfoData> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    }
    if (data.containsKey('uuid')) {
      context.handle(
          _uuidMeta, uuid.isAcceptableOrUnknown(data['uuid']!, _uuidMeta));
    } else if (isInserting) {
      context.missing(_uuidMeta);
    }
    if (data.containsKey('topic')) {
      context.handle(
          _topicMeta, topic.isAcceptableOrUnknown(data['topic']!, _topicMeta));
    } else if (isInserting) {
      context.missing(_topicMeta);
    }
    if (data.containsKey('times')) {
      context.handle(
          _timesMeta, times.isAcceptableOrUnknown(data['times']!, _timesMeta));
    }
    if (data.containsKey('update_time')) {
      context.handle(
          _updateTimeMeta,
          updateTime.isAcceptableOrUnknown(
              data['update_time']!, _updateTimeMeta));
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  AdInfoData map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return AdInfoData(
      id: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}id'])!,
      uuid: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}uuid'])!,
      topic: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}topic'])!,
      times: attachedDatabase.typeMapping
          .read(DriftSqlType.double, data['${effectivePrefix}times']),
      updateTime: attachedDatabase.typeMapping
          .read(DriftSqlType.double, data['${effectivePrefix}update_time']),
    );
  }

  @override
  AdInfo createAlias(String alias) {
    return AdInfo(attachedDatabase, alias);
  }

  @override
  bool get dontWriteConstraints => true;
}

class AdInfoData extends DataClass implements Insertable<AdInfoData> {
  final int id;
  final String uuid;
  final String topic;
  final double? times;
  final double? updateTime;
  const AdInfoData(
      {required this.id,
      required this.uuid,
      required this.topic,
      this.times,
      this.updateTime});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<int>(id);
    map['uuid'] = Variable<String>(uuid);
    map['topic'] = Variable<String>(topic);
    if (!nullToAbsent || times != null) {
      map['times'] = Variable<double>(times);
    }
    if (!nullToAbsent || updateTime != null) {
      map['update_time'] = Variable<double>(updateTime);
    }
    return map;
  }

  AdInfoCompanion toCompanion(bool nullToAbsent) {
    return AdInfoCompanion(
      id: Value(id),
      uuid: Value(uuid),
      topic: Value(topic),
      times:
          times == null && nullToAbsent ? const Value.absent() : Value(times),
      updateTime: updateTime == null && nullToAbsent
          ? const Value.absent()
          : Value(updateTime),
    );
  }

  factory AdInfoData.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return AdInfoData(
      id: serializer.fromJson<int>(json['id']),
      uuid: serializer.fromJson<String>(json['uuid']),
      topic: serializer.fromJson<String>(json['topic']),
      times: serializer.fromJson<double?>(json['times']),
      updateTime: serializer.fromJson<double?>(json['update_time']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<int>(id),
      'uuid': serializer.toJson<String>(uuid),
      'topic': serializer.toJson<String>(topic),
      'times': serializer.toJson<double?>(times),
      'update_time': serializer.toJson<double?>(updateTime),
    };
  }

  AdInfoData copyWith(
          {int? id,
          String? uuid,
          String? topic,
          Value<double?> times = const Value.absent(),
          Value<double?> updateTime = const Value.absent()}) =>
      AdInfoData(
        id: id ?? this.id,
        uuid: uuid ?? this.uuid,
        topic: topic ?? this.topic,
        times: times.present ? times.value : this.times,
        updateTime: updateTime.present ? updateTime.value : this.updateTime,
      );
  @override
  String toString() {
    return (StringBuffer('AdInfoData(')
          ..write('id: $id, ')
          ..write('uuid: $uuid, ')
          ..write('topic: $topic, ')
          ..write('times: $times, ')
          ..write('updateTime: $updateTime')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(id, uuid, topic, times, updateTime);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is AdInfoData &&
          other.id == this.id &&
          other.uuid == this.uuid &&
          other.topic == this.topic &&
          other.times == this.times &&
          other.updateTime == this.updateTime);
}

class AdInfoCompanion extends UpdateCompanion<AdInfoData> {
  final Value<int> id;
  final Value<String> uuid;
  final Value<String> topic;
  final Value<double?> times;
  final Value<double?> updateTime;
  const AdInfoCompanion({
    this.id = const Value.absent(),
    this.uuid = const Value.absent(),
    this.topic = const Value.absent(),
    this.times = const Value.absent(),
    this.updateTime = const Value.absent(),
  });
  AdInfoCompanion.insert({
    this.id = const Value.absent(),
    required String uuid,
    required String topic,
    this.times = const Value.absent(),
    this.updateTime = const Value.absent(),
  })  : uuid = Value(uuid),
        topic = Value(topic);
  static Insertable<AdInfoData> custom({
    Expression<int>? id,
    Expression<String>? uuid,
    Expression<String>? topic,
    Expression<double>? times,
    Expression<double>? updateTime,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (uuid != null) 'uuid': uuid,
      if (topic != null) 'topic': topic,
      if (times != null) 'times': times,
      if (updateTime != null) 'update_time': updateTime,
    });
  }

  AdInfoCompanion copyWith(
      {Value<int>? id,
      Value<String>? uuid,
      Value<String>? topic,
      Value<double?>? times,
      Value<double?>? updateTime}) {
    return AdInfoCompanion(
      id: id ?? this.id,
      uuid: uuid ?? this.uuid,
      topic: topic ?? this.topic,
      times: times ?? this.times,
      updateTime: updateTime ?? this.updateTime,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<int>(id.value);
    }
    if (uuid.present) {
      map['uuid'] = Variable<String>(uuid.value);
    }
    if (topic.present) {
      map['topic'] = Variable<String>(topic.value);
    }
    if (times.present) {
      map['times'] = Variable<double>(times.value);
    }
    if (updateTime.present) {
      map['update_time'] = Variable<double>(updateTime.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('AdInfoCompanion(')
          ..write('id: $id, ')
          ..write('uuid: $uuid, ')
          ..write('topic: $topic, ')
          ..write('times: $times, ')
          ..write('updateTime: $updateTime')
          ..write(')'))
        .toString();
  }
}

abstract class _$AppDatabase extends GeneratedDatabase {
  _$AppDatabase(QueryExecutor e) : super(e);
  late final Contact contact = Contact(this);
  late final ContactWallet contactWallet = ContactWallet(this);
  late final GroupInfo groupInfo = GroupInfo(this);
  late final GroupMember groupMember = GroupMember(this);
  late final Log log = Log(this);
  late final Message message = Message(this);
  late final MessageTop messageTop = MessageTop(this);
  late final MessageTopAdmin messageTopAdmin = MessageTopAdmin(this);
  late final Session session = Session(this);
  late final ChannelInfo channelInfo = ChannelInfo(this);
  late final ProxyInfo proxyInfo = ProxyInfo(this);
  late final AdInfo adInfo = AdInfo(this);
  Selectable<SessionData> allSession() {
    return customSelect(
        'SELECT * FROM session WHERE chat_type > -1 ORDER BY top DESC, time DESC',
        variables: [],
        readsFrom: {
          session,
        }).asyncMap(session.mapFromRow);
  }

  Selectable<SessionData> oneSession(String username) {
    return customSelect('SELECT * FROM session WHERE username = ?1',
        variables: [
          Variable<String>(username)
        ],
        readsFrom: {
          session,
        }).asyncMap(session.mapFromRow);
  }

  Future<int> deleteSession(String username) {
    return customUpdate(
      'DELETE FROM session WHERE username = ?1',
      variables: [Variable<String>(username)],
      updates: {session},
      updateKind: UpdateKind.delete,
    );
  }

  Selectable<ContactData> oneContact(String username) {
    return customSelect('SELECT * FROM contact WHERE username = ?1',
        variables: [
          Variable<String>(username)
        ],
        readsFrom: {
          contact,
        }).asyncMap(contact.mapFromRow);
  }

  Selectable<MessageData> oneMessage(String msgId) {
    return customSelect('SELECT * FROM message WHERE msg_id = ?1', variables: [
      Variable<String>(msgId)
    ], readsFrom: {
      message,
    }).asyncMap(message.mapFromRow);
  }

  Selectable<MessageData> oneMessageByUUID(String? uuid) {
    return customSelect('SELECT * FROM message WHERE uuid = ?1', variables: [
      Variable<String>(uuid)
    ], readsFrom: {
      message,
    }).asyncMap(message.mapFromRow);
  }

  Selectable<MessageData> messagesByUuid(List<String?> var1) {
    var $arrayStartIndex = 1;
    final expandedvar1 = $expandVar($arrayStartIndex, var1.length);
    $arrayStartIndex += var1.length;
    return customSelect(
        'SELECT * FROM message WHERE uuid IN ($expandedvar1) ORDER BY time DESC',
        variables: [
          for (var $ in var1) Variable<String>($)
        ],
        readsFrom: {
          message,
        }).asyncMap(message.mapFromRow);
  }

  Future<int> deleteMessageByUUID(String? uuid) {
    return customUpdate(
      'DELETE FROM message WHERE uuid = ?1',
      variables: [Variable<String>(uuid)],
      updates: {message},
      updateKind: UpdateKind.delete,
    );
  }

  Future<int> deleteOwnerMessage(String? owner, List<String> var2) {
    var $arrayStartIndex = 2;
    final expandedvar2 = $expandVar($arrayStartIndex, var2.length);
    $arrayStartIndex += var2.length;
    return customUpdate(
      'DELETE FROM message WHERE owner = ?1 AND msg_id NOT IN ($expandedvar2)',
      variables: [
        Variable<String>(owner),
        for (var $ in var2) Variable<String>($)
      ],
      updates: {message},
      updateKind: UpdateKind.delete,
    );
  }

  Future<int> updateMessageRead(double? updateTime, List<String> var2) {
    var $arrayStartIndex = 2;
    final expandedvar2 = $expandVar($arrayStartIndex, var2.length);
    $arrayStartIndex += var2.length;
    return customUpdate(
      'UPDATE message SET read = TRUE, update_time = ?1 WHERE msg_id IN ($expandedvar2)',
      variables: [
        Variable<double>(updateTime),
        for (var $ in var2) Variable<String>($)
      ],
      updates: {message},
      updateKind: UpdateKind.update,
    );
  }

  Selectable<MessageData> allUnreadMessage(String? owner) {
    return customSelect(
        'SELECT * FROM message WHERE owner = ?1 AND(read IS NULL OR read = FALSE)AND direction = 0',
        variables: [
          Variable<String>(owner)
        ],
        readsFrom: {
          message,
        }).asyncMap(message.mapFromRow);
  }

  Selectable<MessageData> oneRecentMessage(String? owner) {
    return customSelect(
        'SELECT * FROM message WHERE owner = ?1 ORDER BY time DESC LIMIT 1',
        variables: [
          Variable<String>(owner)
        ],
        readsFrom: {
          message,
        }).asyncMap(message.mapFromRow);
  }

  Selectable<MessageData> lastMessageByID(int? chatType) {
    return customSelect(
        'SELECT * FROM message WHERE chat_type = ?1 AND uuid IS NOT NULL ORDER BY id DESC LIMIT 1',
        variables: [
          Variable<int>(chatType)
        ],
        readsFrom: {
          message,
        }).asyncMap(message.mapFromRow);
  }

  Selectable<ContactData> allFriendContact() {
    return customSelect('SELECT * FROM contact WHERE state = 0',
        variables: [],
        readsFrom: {
          contact,
        }).asyncMap(contact.mapFromRow);
  }

  Future<int> deleteMessage(String msgId) {
    return customUpdate(
      'DELETE FROM message WHERE msg_id = ?1',
      variables: [Variable<String>(msgId)],
      updates: {message},
      updateKind: UpdateKind.delete,
    );
  }

  Selectable<MessageData> allFileMessageByType(String? owner, int? type) {
    return customSelect(
        'SELECT * FROM message WHERE owner = ?1 AND type = ?2 AND self_destruct != TRUE AND state >= 0 AND(undo IS NULL OR undo = FALSE)ORDER BY time DESC',
        variables: [
          Variable<String>(owner),
          Variable<int>(type)
        ],
        readsFrom: {
          message,
        }).asyncMap(message.mapFromRow);
  }

  Selectable<ChannelInfoData> oneChannelInfo(String channelId) {
    return customSelect('SELECT * FROM channel_info WHERE channel_id = ?1',
        variables: [
          Variable<String>(channelId)
        ],
        readsFrom: {
          channelInfo,
        }).asyncMap(channelInfo.mapFromRow);
  }

  Selectable<ChannelInfoData> allChannelByState(int? state) {
    return customSelect('SELECT * FROM channel_info WHERE state = ?1',
        variables: [
          Variable<int>(state)
        ],
        readsFrom: {
          channelInfo,
        }).asyncMap(channelInfo.mapFromRow);
  }

  Selectable<GroupMemberData> allGroupMember(String groupId) {
    return customSelect(
        'SELECT * FROM group_member WHERE group_id = ?1 ORDER BY role DESC',
        variables: [
          Variable<String>(groupId)
        ],
        readsFrom: {
          groupMember,
        }).asyncMap(groupMember.mapFromRow);
  }

  Selectable<GroupMemberData> oneGroupMember(String groupId, String username) {
    return customSelect(
        'SELECT * FROM group_member WHERE group_id = ?1 AND username = ?2',
        variables: [
          Variable<String>(groupId),
          Variable<String>(username)
        ],
        readsFrom: {
          groupMember,
        }).asyncMap(groupMember.mapFromRow);
  }

  Future<int> deleteAllGroupMember(String groupId) {
    return customUpdate(
      'DELETE FROM group_member WHERE group_id = ?1',
      variables: [Variable<String>(groupId)],
      updates: {groupMember},
      updateKind: UpdateKind.delete,
    );
  }

  Future<int> deleteGroupMember(String groupId, List<String> var2) {
    var $arrayStartIndex = 2;
    final expandedvar2 = $expandVar($arrayStartIndex, var2.length);
    $arrayStartIndex += var2.length;
    return customUpdate(
      'DELETE FROM group_member WHERE group_id = ?1 AND username IN ($expandedvar2)',
      variables: [
        Variable<String>(groupId),
        for (var $ in var2) Variable<String>($)
      ],
      updates: {groupMember},
      updateKind: UpdateKind.delete,
    );
  }

  Selectable<MemberContactDatasResult> memberContactDatas(
      String var1, List<String> var2) {
    var $arrayStartIndex = 2;
    final expandedvar2 = $expandVar($arrayStartIndex, var2.length);
    $arrayStartIndex += var2.length;
    return customSelect(
        'SELECT g.group_id, g.username, g.displayname AS nickname, c.displayname, c.localname, c.avatar_path, c.avatar_url, c.state FROM group_member AS g LEFT OUTER JOIN contact AS c ON g.username = c.username WHERE g.group_id = ?1 AND g.username IN ($expandedvar2)',
        variables: [
          Variable<String>(var1),
          for (var $ in var2) Variable<String>($)
        ],
        readsFrom: {
          groupMember,
          contact,
        }).map((QueryRow row) {
      return MemberContactDatasResult(
        groupId: row.read<String>('group_id'),
        username: row.read<String>('username'),
        nickname: row.readNullable<String>('nickname'),
        displayname: row.readNullable<String>('displayname'),
        localname: row.readNullable<String>('localname'),
        avatarPath: row.readNullable<String>('avatar_path'),
        avatarUrl: row.readNullable<String>('avatar_url'),
        state: row.readNullable<int>('state'),
      );
    });
  }

  Selectable<String> oneGroupMemberExcludeUser(
      String groupId, String username) {
    return customSelect(
        'SELECT username FROM group_member WHERE group_id = ?1 AND username != ?2',
        variables: [
          Variable<String>(groupId),
          Variable<String>(username)
        ],
        readsFrom: {
          groupMember,
        }).map((QueryRow row) => row.read<String>('username'));
  }

  Selectable<AllChannelInfoByMemberNumResult> allChannelInfoByMemberNum() {
    return customSelect(
        'SELECT c.channel_id, c.title, c.avatar_path, count(g.group_id) AS num FROM channel_info AS c,group_member AS g WHERE c.state = 1 AND c.channel_id = g.group_id GROUP BY g.group_id',
        variables: [],
        readsFrom: {
          channelInfo,
          groupMember,
        }).map((QueryRow row) {
      return AllChannelInfoByMemberNumResult(
        channelId: row.read<String>('channel_id'),
        title: row.readNullable<String>('title'),
        avatarPath: row.readNullable<String>('avatar_path'),
        num: row.read<int>('num'),
      );
    });
  }

  Selectable<int?> groupMemberRole(String groupId, String username) {
    return customSelect(
        'SELECT role FROM group_member WHERE group_id = ?1 AND username = ?2',
        variables: [
          Variable<String>(groupId),
          Variable<String>(username)
        ],
        readsFrom: {
          groupMember,
        }).map((QueryRow row) => row.readNullable<int>('role'));
  }

  Selectable<ContactData> contactByGroupMember(String groupId) {
    return customSelect(
        'SELECT * FROM contact WHERE username IN (SELECT username FROM group_member WHERE group_id = ?1 ORDER BY role DESC)',
        variables: [
          Variable<String>(groupId)
        ],
        readsFrom: {
          contact,
          groupMember,
        }).asyncMap(contact.mapFromRow);
  }

  Future<int> updateMessageRecall(
      String? var1, String? var2, String? var3, int? var4) {
    return customUpdate(
      'UPDATE message SET ext1 = ?1, undo = TRUE WHERE owner = ?2 AND "from" = ?3 AND time >= ?4 AND(undo IS NULL OR undo != TRUE)',
      variables: [
        Variable<String>(var1),
        Variable<String>(var2),
        Variable<String>(var3),
        Variable<int>(var4)
      ],
      updates: {message},
      updateKind: UpdateKind.update,
    );
  }

  Selectable<MessageData> recallMsgByUser(
      String? var1, String? var2, int? var3) {
    return customSelect(
        'SELECT * FROM message WHERE owner = ?1 AND "from" = ?2 AND time >= ?3',
        variables: [
          Variable<String>(var1),
          Variable<String>(var2),
          Variable<int>(var3)
        ],
        readsFrom: {
          message,
        }).asyncMap(message.mapFromRow);
  }

  Selectable<MessageData> recallAllMsgByUser(String? var1, String? var2) {
    return customSelect(
        'SELECT * FROM message WHERE owner = ?1 AND "from" = ?2',
        variables: [
          Variable<String>(var1),
          Variable<String>(var2)
        ],
        readsFrom: {
          message,
        }).asyncMap(message.mapFromRow);
  }

  Future<int> updateMessageAllRecall(String? var1, String? var2, String? var3) {
    return customUpdate(
      'UPDATE message SET ext1 = ?1, undo = TRUE WHERE owner = ?2 AND "from" = ?3 AND(undo IS NULL OR undo != TRUE)',
      variables: [
        Variable<String>(var1),
        Variable<String>(var2),
        Variable<String>(var3)
      ],
      updates: {message},
      updateKind: UpdateKind.update,
    );
  }

  Future<int> updateSessionTopNull() {
    return customUpdate(
      'UPDATE session SET top = FALSE WHERE top IS NULL',
      variables: [],
      updates: {session},
      updateKind: UpdateKind.update,
    );
  }

  Selectable<GroupInfoData> oneGroupInfo(String groupId) {
    return customSelect('SELECT * FROM group_info WHERE group_id = ?1',
        variables: [
          Variable<String>(groupId)
        ],
        readsFrom: {
          groupInfo,
        }).asyncMap(groupInfo.mapFromRow);
  }

  Future<int> deleteGroupInfo(List<String> var1) {
    var $arrayStartIndex = 1;
    final expandedvar1 = $expandVar($arrayStartIndex, var1.length);
    $arrayStartIndex += var1.length;
    return customUpdate(
      'DELETE FROM group_info WHERE group_id IN ($expandedvar1)',
      variables: [for (var $ in var1) Variable<String>($)],
      updates: {groupInfo},
      updateKind: UpdateKind.delete,
    );
  }

  Selectable<GroupInfoData> allGroupInfo() {
    return customSelect(
        'SELECT * FROM group_info WHERE invalid = TRUE ORDER BY update_time DESC',
        variables: [],
        readsFrom: {
          groupInfo,
        }).asyncMap(groupInfo.mapFromRow);
  }

  Future<int> updateMessageMessageHasRead(List<String> var1) {
    var $arrayStartIndex = 1;
    final expandedvar1 = $expandVar($arrayStartIndex, var1.length);
    $arrayStartIndex += var1.length;
    return customUpdate(
      'UPDATE message SET message_has_read = TRUE WHERE msg_id IN ($expandedvar1)',
      variables: [for (var $ in var1) Variable<String>($)],
      updates: {message},
      updateKind: UpdateKind.update,
    );
  }

  Future<int> deleteContact(String username) {
    return customUpdate(
      'DELETE FROM contact WHERE username = ?1',
      variables: [Variable<String>(username)],
      updates: {contact},
      updateKind: UpdateKind.delete,
    );
  }

  Future<int> deleteContactByType(int? type) {
    return customUpdate(
      'DELETE FROM contact WHERE type = ?1',
      variables: [Variable<int>(type)],
      updates: {contact},
      updateKind: UpdateKind.delete,
    );
  }

  Future<int> updateMessageByUndoEdit(String? owner) {
    return customUpdate(
      'UPDATE message SET undo_edit = FALSE WHERE owner = ?1 AND undo_edit = TRUE',
      variables: [Variable<String>(owner)],
      updates: {message},
      updateKind: UpdateKind.update,
    );
  }

  Selectable<ContactData> allContactByType(int? type) {
    return customSelect('SELECT * FROM contact WHERE type = ?1', variables: [
      Variable<int>(type)
    ], readsFrom: {
      contact,
    }).asyncMap(contact.mapFromRow);
  }

  Selectable<OneGroupMemberByUserResult> oneGroupMemberByUser(
      String groupId, String username) {
    return customSelect(
        'SELECT username, displayname FROM group_member WHERE group_id = ?1 AND username = ?2',
        variables: [
          Variable<String>(groupId),
          Variable<String>(username)
        ],
        readsFrom: {
          groupMember,
        }).map((QueryRow row) {
      return OneGroupMemberByUserResult(
        username: row.read<String>('username'),
        displayname: row.readNullable<String>('displayname'),
      );
    });
  }

  Selectable<MessageData> allFileMessageByOwner(String? owner) {
    return customSelect(
        'SELECT * FROM message WHERE owner = ?1 AND(file_path IS NOT NULL OR thumbnail_path IS NOT NULL)',
        variables: [
          Variable<String>(owner)
        ],
        readsFrom: {
          message,
        }).asyncMap(message.mapFromRow);
  }

  Selectable<ProxyInfoData> allProxyInfo() {
    return customSelect('SELECT * FROM proxy_info', variables: [], readsFrom: {
      proxyInfo,
    }).asyncMap(proxyInfo.mapFromRow);
  }

  Selectable<ProxyInfoData> proxyInfoByEnable(bool? enable) {
    return customSelect('SELECT * FROM proxy_info WHERE enable = ?1',
        variables: [
          Variable<bool>(enable)
        ],
        readsFrom: {
          proxyInfo,
        }).asyncMap(proxyInfo.mapFromRow);
  }

  Future<int> deleteProxyInfo(String uuid) {
    return customUpdate(
      'DELETE FROM proxy_info WHERE uuid = ?1',
      variables: [Variable<String>(uuid)],
      updates: {proxyInfo},
      updateKind: UpdateKind.delete,
    );
  }

  Future<int> updateProxyInfoByUuid(bool? enable, String uuid) {
    return customUpdate(
      'UPDATE proxy_info SET enable = ?1 WHERE uuid = ?2',
      variables: [Variable<bool>(enable), Variable<String>(uuid)],
      updates: {proxyInfo},
      updateKind: UpdateKind.update,
    );
  }

  Future<int> deleteSessionWhereChatTypeIsNull() {
    return customUpdate(
      'DELETE FROM session WHERE chat_type IS NULL',
      variables: [],
      updates: {session},
      updateKind: UpdateKind.delete,
    );
  }

  Selectable<ChannelInfoData> allChannelInfoByID(List<String> var1) {
    var $arrayStartIndex = 1;
    final expandedvar1 = $expandVar($arrayStartIndex, var1.length);
    $arrayStartIndex += var1.length;
    return customSelect(
        'SELECT * FROM channel_info WHERE channel_id IN ($expandedvar1)',
        variables: [
          for (var $ in var1) Variable<String>($)
        ],
        readsFrom: {
          channelInfo,
        }).asyncMap(channelInfo.mapFromRow);
  }

  Selectable<ContactData> allContactDatas() {
    return customSelect('SELECT * FROM contact', variables: [], readsFrom: {
      contact,
    }).asyncMap(contact.mapFromRow);
  }

  Selectable<GroupInfoData> allGroupInfoDatas() {
    return customSelect('SELECT * FROM group_info', variables: [], readsFrom: {
      groupInfo,
    }).asyncMap(groupInfo.mapFromRow);
  }

  Selectable<GroupMemberData> allGroupMemberDatas() {
    return customSelect('SELECT * FROM group_member',
        variables: [],
        readsFrom: {
          groupMember,
        }).asyncMap(groupMember.mapFromRow);
  }

  Selectable<LogData> allLogDatas() {
    return customSelect('SELECT * FROM log', variables: [], readsFrom: {
      log,
    }).asyncMap(log.mapFromRow);
  }

  Selectable<MessageData> allMessageDatas() {
    return customSelect('SELECT * FROM message', variables: [], readsFrom: {
      message,
    }).asyncMap(message.mapFromRow);
  }

  Selectable<SessionData> allSessionDatas() {
    return customSelect('SELECT * FROM session', variables: [], readsFrom: {
      session,
    }).asyncMap(session.mapFromRow);
  }

  Selectable<ChannelInfoData> allChannelInfoDatas() {
    return customSelect('SELECT * FROM channel_info',
        variables: [],
        readsFrom: {
          channelInfo,
        }).asyncMap(channelInfo.mapFromRow);
  }

  Selectable<ProxyInfoData> allProxyInfoDatas() {
    return customSelect('SELECT * FROM proxy_info', variables: [], readsFrom: {
      proxyInfo,
    }).asyncMap(proxyInfo.mapFromRow);
  }

  Selectable<int> dbConnect() {
    return customSelect('SELECT count(*) AS _c0 FROM sqlite_master',
        variables: [],
        readsFrom: {}).map((QueryRow row) => row.read<int>('_c0'));
  }

  Selectable<ContactData> allBlackedContact() {
    return customSelect('SELECT * FROM contact WHERE isBlack = TRUE',
        variables: [],
        readsFrom: {
          contact,
        }).asyncMap(contact.mapFromRow);
  }

  Future<int> updateBlackContact(bool? isBlack, String username) {
    return customUpdate(
      'UPDATE contact SET isBlack = ?1 WHERE username = ?2',
      variables: [Variable<bool>(isBlack), Variable<String>(username)],
      updates: {contact},
      updateKind: UpdateKind.update,
    );
  }

  Selectable<ContactData> oneContactIsBlacked(String username) {
    return customSelect(
        'SELECT * FROM contact WHERE isBlack = TRUE AND username = ?1',
        variables: [
          Variable<String>(username)
        ],
        readsFrom: {
          contact,
        }).asyncMap(contact.mapFromRow);
  }

  Selectable<MessageData> allProbeMessage(double? time, double? updateTime) {
    return customSelect(
        'SELECT * FROM message WHERE((direction = 1 AND state = 3)OR(direction = 0 AND read = TRUE))AND type >= 0 AND chat_type = 0 AND(update_time - time)< ?1 AND update_time > ?2 ORDER BY update_time DESC',
        variables: [
          Variable<double>(time),
          Variable<double>(updateTime)
        ],
        readsFrom: {
          message,
        }).asyncMap(message.mapFromRow);
  }

  Selectable<SessionData> allChannelSessionDatas(List<String> var1) {
    var $arrayStartIndex = 1;
    final expandedvar1 = $expandVar($arrayStartIndex, var1.length);
    $arrayStartIndex += var1.length;
    return customSelect(
        'SELECT * FROM session WHERE chat_type = 2 AND username IN ($expandedvar1)',
        variables: [
          for (var $ in var1) Variable<String>($)
        ],
        readsFrom: {
          session,
        }).asyncMap(session.mapFromRow);
  }

  Selectable<String?> allFileMessageByFilePath(List<String?> var1) {
    var $arrayStartIndex = 1;
    final expandedvar1 = $expandVar($arrayStartIndex, var1.length);
    $arrayStartIndex += var1.length;
    return customSelect(
        'SELECT file_path FROM message WHERE file_path IN ($expandedvar1)',
        variables: [
          for (var $ in var1) Variable<String>($)
        ],
        readsFrom: {
          message,
        }).map((QueryRow row) => row.readNullable<String>('file_path'));
  }

  Selectable<String?> allFileMessageByThumbnailPath(List<String?> var1) {
    var $arrayStartIndex = 1;
    final expandedvar1 = $expandVar($arrayStartIndex, var1.length);
    $arrayStartIndex += var1.length;
    return customSelect(
        'SELECT thumbnail_path FROM message WHERE thumbnail_path IN ($expandedvar1)',
        variables: [
          for (var $ in var1) Variable<String>($)
        ],
        readsFrom: {
          message,
        }).map((QueryRow row) => row.readNullable<String>('thumbnail_path'));
  }

  Future<int> updateMessageDelete(int? state, String? owner) {
    return customUpdate(
      'UPDATE message SET state = ?1 WHERE owner = ?2',
      variables: [Variable<int>(state), Variable<String>(owner)],
      updates: {message},
      updateKind: UpdateKind.update,
    );
  }

  Future<int> deleteMessageByTime(String? owner, int? time) {
    return customUpdate(
      'DELETE FROM message WHERE owner = ?1 AND time > ?2',
      variables: [Variable<String>(owner), Variable<int>(time)],
      updates: {message},
      updateKind: UpdateKind.delete,
    );
  }

  Future<int> updateOneMessageRecall(String? var1, String? var2, String var3) {
    return customUpdate(
      'UPDATE message SET ext1 = ?1, undo = TRUE WHERE owner = ?2 AND msg_id = ?3 AND(undo IS NULL OR undo != TRUE)',
      variables: [
        Variable<String>(var1),
        Variable<String>(var2),
        Variable<String>(var3)
      ],
      updates: {message},
      updateKind: UpdateKind.update,
    );
  }

  Selectable<MessageData> recallOneMsgByUser(String? var1, String var2) {
    return customSelect(
        'SELECT * FROM message WHERE owner = ?1 AND msg_id = ?2',
        variables: [
          Variable<String>(var1),
          Variable<String>(var2)
        ],
        readsFrom: {
          message,
        }).asyncMap(message.mapFromRow);
  }

  Selectable<int> countMsg() {
    return customSelect('SELECT count(*) AS _c0 FROM message',
        variables: [],
        readsFrom: {
          message,
        }).map((QueryRow row) => row.read<int>('_c0'));
  }

  Future<int> channelChatMsgDelMoreThen1000(String? channelId) {
    return customUpdate(
      'DELETE FROM message WHERE chat_type = 2 AND owner = ?1 AND time < (SELECT time FROM message WHERE owner = ?1 ORDER BY time DESC LIMIT 1 OFFSET 500)',
      variables: [Variable<String>(channelId)],
      updates: {message},
      updateKind: UpdateKind.delete,
    );
  }

  Future<int> deleteTopMsgById(String msgId) {
    return customUpdate(
      'DELETE FROM message_top WHERE msg_id = ?1',
      variables: [Variable<String>(msgId)],
      updates: {messageTop},
      updateKind: UpdateKind.delete,
    );
  }

  Selectable<MessageTopData> topMsgByUserName(String? owner) {
    return customSelect(
        'SELECT * FROM message_top WHERE owner = ?1 AND state IN (-1, 0, 1, 2, 3, 4, 5, 6, 7) AND type IN (0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29) ORDER BY update_time ASC LIMIT 1000',
        variables: [
          Variable<String>(owner)
        ],
        readsFrom: {
          messageTop,
        }).asyncMap(messageTop.mapFromRow);
  }

  Future<int> cleanTopMsgByUserName(String? owner) {
    return customUpdate(
      'DELETE FROM message_top WHERE owner = ?1',
      variables: [Variable<String>(owner)],
      updates: {messageTop},
      updateKind: UpdateKind.delete,
    );
  }

  Selectable<MessageTopData> topMsgByMsgId(String msgId) {
    return customSelect('SELECT * FROM message_top WHERE msg_id = ?1',
        variables: [
          Variable<String>(msgId)
        ],
        readsFrom: {
          messageTop,
        }).asyncMap(messageTop.mapFromRow);
  }

  Selectable<ContactWalletData> contactWalletByUserName(String userName) {
    return customSelect('SELECT * FROM contact_wallet WHERE user_name = ?1',
        variables: [
          Variable<String>(userName)
        ],
        readsFrom: {
          contactWallet,
        }).asyncMap(contactWallet.mapFromRow);
  }

  Selectable<ContactWalletData> contactWalletByChainType(
      String userName, int? chainType) {
    return customSelect(
        'SELECT * FROM contact_wallet WHERE user_name = ?1 AND chain_type = ?2',
        variables: [
          Variable<String>(userName),
          Variable<int>(chainType)
        ],
        readsFrom: {
          contactWallet,
        }).asyncMap(contactWallet.mapFromRow);
  }

  Selectable<MessageData> chatsByUserName(String? owner) {
    return customSelect(
        'SELECT * FROM message WHERE owner = ?1 AND state IN (-1, 0, 1, 2, 3, 4, 5, 6, 7) AND type IN (0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29) ORDER BY time DESC',
        variables: [
          Variable<String>(owner)
        ],
        readsFrom: {
          message,
        }).asyncMap(message.mapFromRow);
  }

  Selectable<int?> countNotReadMsg() {
    return customSelect(
        'SELECT sum(unread_count) AS _c0 FROM session WHERE silence != TRUE',
        variables: [],
        readsFrom: {
          session,
        }).map((QueryRow row) => row.readNullable<int>('_c0'));
  }

  Future<int> updateSessionSilence(bool? silence, String username) {
    return customUpdate(
      'UPDATE session SET silence = ?1 WHERE username = ?2',
      variables: [Variable<bool>(silence), Variable<String>(username)],
      updates: {session},
      updateKind: UpdateKind.update,
    );
  }

  Future<int> deleteAdInfos(List<String> var1) {
    var $arrayStartIndex = 1;
    final expandedvar1 = $expandVar($arrayStartIndex, var1.length);
    $arrayStartIndex += var1.length;
    return customUpdate(
      'DELETE FROM ad_info WHERE uuid IN ($expandedvar1)',
      variables: [for (var $ in var1) Variable<String>($)],
      updates: {adInfo},
      updateKind: UpdateKind.delete,
    );
  }

  Selectable<AdInfoData> allAdInfos() {
    return customSelect('SELECT * FROM ad_info', variables: [], readsFrom: {
      adInfo,
    }).asyncMap(adInfo.mapFromRow);
  }

  Selectable<MessageTopAdminData> topMsgAdminByUserName(String? owner) {
    return customSelect(
        'SELECT * FROM message_top_admin WHERE owner = ?1 AND state IN (-1, 0, 1, 2, 3, 4, 5, 6, 7) AND type IN (0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29) ORDER BY update_time ASC LIMIT 1000',
        variables: [
          Variable<String>(owner)
        ],
        readsFrom: {
          messageTopAdmin,
        }).asyncMap(messageTopAdmin.mapFromRow);
  }

  Selectable<MessageTopAdminData> topMsgByUserNameAdminByUuids(
      List<String?> var1) {
    var $arrayStartIndex = 1;
    final expandedvar1 = $expandVar($arrayStartIndex, var1.length);
    $arrayStartIndex += var1.length;
    return customSelect(
        'SELECT * FROM message_top_admin WHERE state IN (-1, 0, 1, 2, 3, 4, 5, 6, 7) AND uuid IN ($expandedvar1) ORDER BY time ASC LIMIT 1000',
        variables: [
          for (var $ in var1) Variable<String>($)
        ],
        readsFrom: {
          messageTopAdmin,
        }).asyncMap(messageTopAdmin.mapFromRow);
  }

  Future<int> updateBlackContacts(bool? isBlack, List<String> var2) {
    var $arrayStartIndex = 2;
    final expandedvar2 = $expandVar($arrayStartIndex, var2.length);
    $arrayStartIndex += var2.length;
    return customUpdate(
      'UPDATE contact SET isBlack = ?1 WHERE username IN ($expandedvar2)',
      variables: [
        Variable<bool>(isBlack),
        for (var $ in var2) Variable<String>($)
      ],
      updates: {contact},
      updateKind: UpdateKind.update,
    );
  }

  Selectable<String> allBlackedUserNames() {
    return customSelect('SELECT username FROM contact WHERE isBlack = TRUE',
        variables: [],
        readsFrom: {
          contact,
        }).map((QueryRow row) => row.read<String>('username'));
  }

  Future<int> updateSessionSetSilenceOrTop(bool var1, bool? var2, String var3) {
    return customUpdate(
      'UPDATE session SET silence = ?1 AND top = ?2 WHERE username = ?3',
      variables: [
        Variable<bool>(var1),
        Variable<bool>(var2),
        Variable<String>(var3)
      ],
      updates: {session},
      updateKind: UpdateKind.update,
    );
  }

  Future<int> updateMessageIdentifyState(bool var1, String? var2, String var3) {
    return customUpdate(
      'UPDATE message SET has_identify = ?1 AND ext1 = ?2 WHERE msg_id = ?3',
      variables: [
        Variable<bool>(var1),
        Variable<String>(var2),
        Variable<String>(var3)
      ],
      updates: {message},
      updateKind: UpdateKind.update,
    );
  }

  Future<int> updateOneMessageDelete(int? state, String msgId) {
    return customUpdate(
      'UPDATE message SET state = ?1 WHERE msg_id = ?2',
      variables: [Variable<int>(state), Variable<String>(msgId)],
      updates: {message},
      updateKind: UpdateKind.update,
    );
  }

  Selectable<ChannelInfoData> allChannelInfoDatasAvatarNotEmpty() {
    return customSelect(
        'SELECT * FROM channel_info WHERE avatar_path IS NOT NULL',
        variables: [],
        readsFrom: {
          channelInfo,
        }).asyncMap(channelInfo.mapFromRow);
  }

  Selectable<SessionData> allSessionAvatarNotEmpty() {
    return customSelect('SELECT * FROM session WHERE avatar_path IS NOT NULL',
        variables: [],
        readsFrom: {
          session,
        }).asyncMap(session.mapFromRow);
  }

  Selectable<ContactData> allContactAvatarNotEmpty() {
    return customSelect('SELECT * FROM contact WHERE avatar_path IS NOT NULL',
        variables: [],
        readsFrom: {
          contact,
        }).asyncMap(contact.mapFromRow);
  }

  Selectable<String> topMsgIdByUserNameAdmin(String? var1) {
    return customSelect(
        'SELECT msg_id FROM message_top_admin WHERE owner = ?1 AND state IN (-1, 0, 1, 2, 3, 4, 5, 6) AND type IN (0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29) ORDER BY update_time ASC LIMIT 1000',
        variables: [
          Variable<String>(var1)
        ],
        readsFrom: {
          messageTopAdmin,
        }).map((QueryRow row) => row.read<String>('msg_id'));
  }

  Selectable<MessageTopAdminData> topMsgByUserNameAdmin(String? var1) {
    return customSelect(
        'SELECT * FROM message_top_admin WHERE owner = ?1 AND state IN (-1, 0, 1, 2, 3, 4, 5, 6) AND type IN (0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29) ORDER BY update_time ASC LIMIT 1000',
        variables: [
          Variable<String>(var1)
        ],
        readsFrom: {
          messageTopAdmin,
        }).asyncMap(messageTopAdmin.mapFromRow);
  }

  Selectable<GroupMemberData> allGroupMemberWithTags(String groupId) {
    return customSelect(
        'SELECT * FROM group_member WHERE group_id = ?1 AND tags IS NOT NULL ORDER BY role DESC',
        variables: [
          Variable<String>(groupId)
        ],
        readsFrom: {
          groupMember,
        }).asyncMap(groupMember.mapFromRow);
  }

  @override
  Iterable<TableInfo<Table, Object?>> get allTables =>
      allSchemaEntities.whereType<TableInfo<Table, Object?>>();
  @override
  List<DatabaseSchemaEntity> get allSchemaEntities => [
        contact,
        contactWallet,
        groupInfo,
        groupMember,
        log,
        message,
        messageTop,
        messageTopAdmin,
        session,
        channelInfo,
        proxyInfo,
        adInfo
      ];
}

class MemberContactDatasResult {
  final String groupId;
  final String username;
  final String? nickname;
  final String? displayname;
  final String? localname;
  final String? avatarPath;
  final String? avatarUrl;
  final int? state;
  MemberContactDatasResult({
    required this.groupId,
    required this.username,
    this.nickname,
    this.displayname,
    this.localname,
    this.avatarPath,
    this.avatarUrl,
    this.state,
  });
}

class AllChannelInfoByMemberNumResult {
  final String channelId;
  final String? title;
  final String? avatarPath;
  final int num;
  AllChannelInfoByMemberNumResult({
    required this.channelId,
    this.title,
    this.avatarPath,
    required this.num,
  });
}

class OneGroupMemberByUserResult {
  final String username;
  final String? displayname;
  OneGroupMemberByUserResult({
    required this.username,
    this.displayname,
  });
}
