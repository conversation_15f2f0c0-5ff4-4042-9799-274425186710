-- tables.drift 文件
-- ----------------------------
-- Table structure for contact
-- ----------------------------
CREATE TABLE IF NOT EXISTS contact (
"id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
"username" TEXT NOT NULL UNIQUE,
"displayname" TEXT,
"localname" TEXT,
"avatar_path" TEXT,
"avatar_url" TEXT,
"chat_background_path" TEXT,
"chat_background_url" TEXT,
"fragment" TEXT,
"edit" BOOLEAN,
"read" BOOLEAN,
"state" INTEGER,
"fistname" TEXT,
"lastname" TEXT,
"mobile" TEXT,
"create_time" NUMERIC,
"update_time" NUMERIC,
"isBlack" BOOLEAN DEFAULT false,
"isTid" BOOLEAN DEFAULT false,
"type" INTEGER
)                                                ;
-- ----------------------------
-- Table structure for contact_wallet
-- ----------------------------
CREATE TABLE IF NOT EXISTS contact_wallet (
"id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
"address" TEXT NOT NULL UNIQUE,
"user_name" TEXT NOT NULL,
"chain_type" INTEGER,
"chain_id" INTEGER,
"create_time" NUMERIC,
"update_time" NUMERIC
)                                                ;
-- ----------------------------
-- Table structure for group
-- ----------------------------
CREATE TABLE IF NOT EXISTS group_info (
"id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
"group_id" TEXT NOT NULL UNIQUE,
"title" TEXT,
"permission" INTEGER,
"owner" TEXT,
"avatar_url" TEXT,
"avatar_path" TEXT,
"avatar_fragment" TEXT,
"property_url" TEXT,
"property_path" TEXT,
"property_fragment" TEXT,
"describe" TEXT,
"background_path" TEXT,
"background_url" TEXT,
"invalid" BOOLEAN,
"create_time" NUMERIC,
"update_time" NUMERIC,
"member_count" INTEGER,
"announcement" TEXT
)                                                ;

-- ----------------------------
-- Table structure for group_member
-- ----------------------------
CREATE TABLE IF NOT EXISTS group_member (
"id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
"group_id" TEXT NOT NULL,
"username" TEXT NOT NULL,
"displayname" TEXT,
"member_uuid" TEXT NOT NULL UNIQUE,
"role" INTEGER,
"start_mute_time" INTEGER,
"mute_duration" INTEGER,
"mute_expire" INTEGER,
"tags" TEXT,
"create_time" NUMERIC,
"update_time" NUMERIC
)                                                ;

-- ----------------------------
-- Table structure for log
-- ----------------------------
CREATE TABLE IF NOT EXISTS log (
"id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
"type" INTEGER,
"body" TEXT,
"create_time" NUMERIC
)                                                ;

-- ----------------------------
-- Table structure for message
-- ----------------------------
CREATE TABLE IF NOT EXISTS message (
"id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
"owner" TEXT,
"from" TEXT,
"body" TEXT,
"file_path" TEXT,
"file_url" TEXT,
"file_fragment" TEXT,
"thumbnail_path" TEXT,
"thumbnail_url" TEXT,
"thumbnail_fragment" TEXT,
"self_destruct" BOOLEAN,
"undo" BOOLEAN,
"undo_edit" BOOLEAN DEFAULT false,
"time" INTEGER,
"type" INTEGER,
"chat_type" INTEGER,
"call_state" INTEGER,
"state" INTEGER,
"direction" INTEGER,
"file_state" INTEGER,
"thumbnail_file_state" INTEGER,
"msg_id" TEXT NOT NULL UNIQUE,
"uuid" TEXT,
"file_name" TEXT,
"fileSize" INTEGER,
"ext1" TEXT,
"ack_number" INTEGER,
"read" BOOLEAN,
"replay_msg" TEXT,
"marge_msg" TEXT,
"contact_msg" TEXT,
"share_msg" TEXT,
"create_time" NUMERIC,
"at" TEXT,
"update_time" NUMERIC,
"resource_uuid" TEXT,
"has_shown" BOOLEAN,
"has_identify" BOOLEAN,
"message_has_read" BOOLEAN,
"file_duration" INTEGER,
"user_name_file_helper" TEXT,
"noises" TEXT,
"display_name" TEXT,
"translate_msg" TEXT,
"expand" TEXT
)                                                ;
-- ----------------------------
-- Table structure for message_top
-- ----------------------------
CREATE TABLE IF NOT EXISTS message_top (
"id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
"owner" TEXT,
"from" TEXT,
"body" TEXT,
"file_path" TEXT,
"file_url" TEXT,
"file_fragment" TEXT,
"thumbnail_path" TEXT,
"thumbnail_url" TEXT,
"thumbnail_fragment" TEXT,
"self_destruct" BOOLEAN,
"undo" BOOLEAN,
"undo_edit" BOOLEAN DEFAULT false,
"time" INTEGER,
"type" INTEGER,
"chat_type" INTEGER,
"call_state" INTEGER,
"state" INTEGER,
"direction" INTEGER,
"file_state" INTEGER,
"thumbnail_file_state" INTEGER,
"msg_id" TEXT NOT NULL UNIQUE,
"uuid" TEXT,
"file_name" TEXT,
"fileSize" INTEGER,
"ext1" TEXT,
"ack_number" INTEGER,
"read" BOOLEAN,
"replay_msg" TEXT,
"marge_msg" TEXT,
"contact_msg" TEXT,
"share_msg" TEXT,
"create_time" NUMERIC,
"at" TEXT,
"update_time" NUMERIC,
"resource_uuid" TEXT,
"has_shown" BOOLEAN,
"message_has_read" BOOLEAN,
"file_duration" INTEGER,
"user_name_file_helper" TEXT,
"noises" TEXT,
"display_name" TEXT,
"expand" TEXT
)   ;
-- ----------------------------
-- Table structure for message_top_admin
-- ----------------------------
CREATE TABLE IF NOT EXISTS message_top_admin (
"id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
"owner" TEXT,
"from" TEXT,
"body" TEXT,
"file_path" TEXT,
"file_url" TEXT,
"file_fragment" TEXT,
"thumbnail_path" TEXT,
"thumbnail_url" TEXT,
"thumbnail_fragment" TEXT,
"self_destruct" BOOLEAN,
"undo" BOOLEAN,
"undo_edit" BOOLEAN DEFAULT false,
"time" INTEGER,
"type" INTEGER,
"chat_type" INTEGER,
"call_state" INTEGER,
"state" INTEGER,
"direction" INTEGER,
"file_state" INTEGER,
"thumbnail_file_state" INTEGER,
"msg_id" TEXT NOT NULL UNIQUE,
"uuid" TEXT,
"file_name" TEXT,
"fileSize" INTEGER,
"ext1" TEXT,
"ack_number" INTEGER,
"read" BOOLEAN,
"replay_msg" TEXT,
"marge_msg" TEXT,
"contact_msg" TEXT,
"share_msg" TEXT,
"create_time" NUMERIC,
"at" TEXT,
"update_time" NUMERIC,
"resource_uuid" TEXT,
"has_shown" BOOLEAN,
"message_has_read" BOOLEAN,
"file_duration" INTEGER,
"user_name_file_helper" TEXT,
"noises" TEXT,
"display_name" TEXT,
"expand" TEXT
)   ;
-- ----------------------------
-- Table structure for session
-- ----------------------------
CREATE TABLE IF NOT EXISTS session (
"id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
"username" TEXT NOT NULL UNIQUE,
"displayname" TEXT,
"body" TEXT,
"time" INTEGER,
"top" BOOLEAN DEFAULT false,
"avatar_path" TEXT,
"unread_count" INTEGER,
"edit" BOOLEAN,
"read" BOOLEAN,
"type" INTEGER,
"chat_type" INTEGER,
"direction" INTEGER,
"self_destruct" BOOLEAN,
"ext1" TEXT,
"state" INTEGER,
"msg_id" TEXT,
"uuid" TEXT,
"explains" TEXT,
"at" BOOLEAN,
"create_time" NUMERIC,
"silence" BOOLEAN DEFAULT false,
"isTid" BOOLEAN DEFAULT false,
"update_time" NUMERIC
)                                                ;


-- ----------------------------
-- Table structure for channel_info
-- ----------------------------
CREATE TABLE IF NOT EXISTS channel_info (
"id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
"channel_id" TEXT NOT NULL UNIQUE,
"title" TEXT,
"state" INTEGER,
"owner" TEXT,
"avatar_url" TEXT,
"avatar_path" TEXT,
"describe" TEXT,
"min_msg_uuid" TEXT,
"max_msg_uuid" TEXT,
"max_msg_time" INTEGER,
"background_path" TEXT,
"background_url" TEXT,
"all_mute" BOOLEAN,
"join_verify" BOOLEAN,
"invite_limit" BOOLEAN,
"create_time" NUMERIC,
"update_time" NUMERIC,
"announcement" TEXT,
"attribute" INTEGER DEFAULT 0,
"options" TEXT,
"member_count" INTEGER,
"limit" int,
"chain" TEXT,
"token_address" TEXT,
"min_num_token" REAL,
"tags" TEXT
)                                                ;


-- ----------------------------
-- Table structure for proxy_info
-- ----------------------------
CREATE TABLE IF NOT EXISTS proxy_info (
"id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
"uuid" TEXT NOT NULL UNIQUE,
"name" TEXT NOT NULL,
"host" TEXT NOT NULL,
"port" TEXT NOT NULL,
"user" TEXT,
"pwd" TEXT,
"enable" BOOLEAN DEFAULT false,
"is_http" BOOLEAN DEFAULT true,
"create_time" NUMERIC,
"update_time" NUMERIC
);
-- ----------------------------
-- Table structure for ad_info
-- ----------------------------
CREATE TABLE IF NOT EXISTS ad_info (
"id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
"uuid" TEXT NOT NULL UNIQUE,
"topic" TEXT NOT NULL,
"times" NUMERIC,
"update_time" NUMERIC
);

allSession: SELECT * FROM session WHERE chat_type >-1 ORDER BY top DESC, time DESC                                  ;
oneSession: SELECT * FROM session WHERE username = :username                                    ;
deleteSession: DELETE FROM session WHERE username = :username                                   ;
oneContact: SELECT * FROM contact WHERE username = :username                                    ;
oneMessage: SELECT * FROM message WHERE msg_id = :msg_id                                        ;
oneMessageByUUID: SELECT * FROM message WHERE uuid = :uuid                                      ;
messagesByUuid: SELECT * FROM message WHERE uuid IN ?  ORDER BY  time DESC  ;
deleteMessageByUUID: DELETE FROM message WHERE uuid = :uuid                                     ;
deleteOwnerMessage: DELETE FROM message WHERE owner = :owner AND msg_id NOT IN ?                                   ;
updateMessageRead: UPDATE message SET read = true , update_time =:update_time WHERE msg_id IN ?                          ;
allUnreadMessage: SELECT * FROM message WHERE owner = :owner AND (read IS NULL OR read = false) AND direction = 0 ;
oneRecentMessage: SELECT * FROM message WHERE owner = :owner ORDER BY time DESC LIMIT 1         ;
lastMessageByID: SELECT * FROM message WHERE chat_type = :chat_type AND uuid IS NOT NULL ORDER BY id DESC LIMIT 1;
allFriendContact: SELECT * FROM contact WHERE state = 0                                         ;
deleteMessage: DELETE FROM message WHERE msg_id = :msg_id                                       ;
allFileMessageByType: SELECT * FROM message WHERE owner = :owner AND type = :type AND self_destruct != true AND state >=0 AND (undo IS NULL OR undo = false) ORDER BY time DESC  ;
oneChannelInfo: SELECT * FROM channel_info WHERE channel_id = :channel_id                       ;
allChannelByState: SELECT * FROM channel_info WHERE state = :state                                   ;
allGroupMember: SELECT * FROM group_member WHERE group_id = :group_id ORDER BY role DESC;
oneGroupMember: SELECT * FROM group_member WHERE group_id = :group_id AND username = :username  ;
deleteAllGroupMember: DELETE FROM group_member WHERE group_id = :group_id                       ;
deleteGroupMember: DELETE FROM group_member WHERE group_id = :group_id AND username IN ? ;
memberContactDatas: SELECT g.group_id, g.username, g.displayname as nickname, c.displayname, c.localname, c.avatar_path, c.avatar_url, c.state FROM group_member as g LEFT OUTER JOIN contact as c ON g.username = c.username WHERE g.group_id = ? AND g.username IN ?;
oneGroupMemberExcludeUser: SELECT username FROM group_member WHERE group_id = :group_id AND username != :username  ;
allChannelInfoByMemberNum: SELECT c.channel_id, c.title, c.avatar_path, count(g.group_id) num from channel_info c, group_member g WHERE c.state = 1 AND c.channel_id = g.group_id GROUP BY g.group_id;
groupMemberRole: SELECT role FROM group_member WHERE group_id = :group_id AND username = :username  ;
contactByGroupMember: SELECT * FROM contact WHERE username IN (SELECT username FROM group_member WHERE group_id = :group_id ORDER BY role DESC);
updateMessageRecall: UPDATE message   SET ext1 = ? , undo=true  WHERE owner = ? AND  [from] =?  AND  time >=?  AND (undo IS NULL OR undo != true);
recallMsgByUser: SELECT * FROM message  WHERE  owner = ? AND  [from] =?  AND  time >=?                   ;
recallAllMsgByUser: SELECT * FROM message  WHERE  owner = ? AND  [from] =?;
updateMessageAllRecall: UPDATE message   SET ext1 = ? , undo=true  WHERE owner = ? AND  [from] =? AND (undo IS NULL OR undo != true);
updateSessionTopNull: UPDATE session SET top = false WHERE top IS NULL;
oneGroupInfo: SELECT * FROM group_info WHERE group_id = :group_id                       ;
deleteGroupInfo: DELETE FROM group_info WHERE group_id IN ?                            ;
allGroupInfo: SELECT * FROM group_info WHERE invalid = true  ORDER BY update_time DESC        ;
updateMessageMessageHasRead: UPDATE message SET message_has_read = true WHERE msg_id IN ?;
deleteContact: DELETE  FROM contact WHERE username = :username                                    ;
deleteContactByType: DELETE  FROM contact WHERE type = :type                                    ;
updateMessageByUndoEdit: UPDATE message SET undo_edit = false WHERE owner = :owner AND undo_edit = true;
allContactByType: SELECT * FROM contact WHERE type = :type ;
oneGroupMemberByUser: SELECT username,displayname FROM group_member WHERE group_id = :group_id AND username = :username  ;
allFileMessageByOwner: SELECT * FROM message WHERE owner = :owner AND (file_path IS NOT NULL OR thumbnail_path IS NOT NULL);
allProxyInfo: SELECT * FROM proxy_info;
proxyInfoByEnable: SELECT * FROM proxy_info WHERE enable = :enable;
deleteProxyInfo: DELETE FROM proxy_info WHERE uuid = :uuid;
updateProxyInfoByUuid: UPDATE proxy_info SET enable = :enable WHERE uuid = :uuid;
deleteSessionWhereChatTypeIsNull: DELETE FROM session WHERE chat_type is null;
allChannelInfoByID: SELECT * FROM channel_info WHERE channel_id IN ?;

allContactDatas: SELECT * FROM contact ;
allGroupInfoDatas: SELECT * FROM group_info;
allGroupMemberDatas: SELECT * FROM group_member;
allLogDatas: SELECT * FROM log;
allMessageDatas: SELECT * FROM message;
allSessionDatas: SELECT * FROM session;
allChannelInfoDatas: SELECT * FROM channel_info;
allProxyInfoDatas: SELECT * FROM proxy_info;
dbConnect: select count(*) from sqlite_master;
allBlackedContact: SELECT * FROM contact WHERE isBlack = true;
updateBlackContact: UPDATE contact SET isBlack =:isBlack WHERE username = :username;
oneContactIsBlacked: SELECT * FROM contact WHERE isBlack = true AND  username = :username ;
allProbeMessage: SELECT * FROM message WHERE ((direction = 1 AND state = 3)OR(direction = 0 AND read = TRUE))  AND  type >=0 AND chat_type = 0 AND  (update_time - time)  <:time And update_time >:update_time ORDER BY update_time DESC  ;
allChannelSessionDatas: SELECT * FROM session where chat_type = 2 and username IN ? ;
allFileMessageByFilePath: SELECT file_path FROM message WHERE file_path IN ?  ;
allFileMessageByThumbnailPath: SELECT thumbnail_path FROM message WHERE thumbnail_path IN ? ;
updateMessageDelete: UPDATE message SET state =:state  WHERE owner = :owner ;
deleteMessageByTime:  DELETE FROM message WHERE owner = :owner AND time >:time   ;
updateOneMessageRecall: UPDATE message   SET ext1 = ? , undo=true   WHERE owner = ? AND  msg_id = ?    AND (undo IS NULL OR undo != true);
recallOneMsgByUser: SELECT * FROM message  WHERE  owner = ? AND  msg_id = ?  ;
countMsg: SELECT count(*) FROM message;
channelChatMsgDelMoreThen1000: DELETE FROM message WHERE chat_type = 2 And owner =:channelId and time < (SELECT time FROM message WHERE owner= :channelId  ORDER BY time desc LIMIT 500,1) ;
deleteTopMsgById: DELETE FROM message_top WHERE msg_id=:msgId;
topMsgByUserName: SELECT * FROM message_top WHERE owner = :owner AND state in(-1,0,1,2,3,4,5,6,7) AND type in(0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29)  ORDER BY update_time ASC LIMIT 1000;
cleanTopMsgByUserName: DELETE FROM message_top WHERE owner = :owner;
topMsgByMsgId: SELECT * FROM message_top WHERE msg_id=:msgId;
contactWalletByUserName: SELECT * FROM contact_wallet WHERE user_name=:userName ;
contactWalletByChainType: SELECT * FROM contact_wallet WHERE user_name=:userName and chain_type=:chainType;
chatsByUserName: SELECT * FROM message WHERE owner = :owner AND state in(-1,0,1,2,3,4,5,6,7) AND type in(0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29)  ORDER BY time DESC;
countNotReadMsg: select sum(unread_count) from session where silence != true;
updateSessionSilence: UPDATE  session  SET silence =:silence  WHERE username = :username ;
deleteAdInfos: DELETE FROM ad_info WHERE uuid IN  ?  ;
allAdInfos: SELECT * FROM ad_info ;
topMsgAdminByUserName: SELECT * FROM message_top_admin WHERE owner = :owner AND state in(-1,0,1,2,3,4,5,6,7) AND type in(0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29)  ORDER BY update_time ASC LIMIT 1000;
topMsgByUserNameAdminByUuids: SELECT * FROM message_top_admin WHERE  state IN (-1, 0, 1, 2, 3, 4, 5, 6,7) AND uuid IN ? ORDER BY time ASC LIMIT 1000;
updateBlackContacts: UPDATE contact SET isBlack =:isBlack WHERE username IN ? ;
allBlackedUserNames: SELECT username FROM contact WHERE isBlack = TRUE ;
updateSessionSetSilenceOrTop: UPDATE session SET silence = ? AND top =? WHERE username = ? ;
updateMessageIdentifyState: UPDATE message SET has_identify = ? AND ext1 =? WHERE msg_id = ?;
updateOneMessageDelete: UPDATE message SET state =:state  WHERE msg_id = :msg_id ;
allChannelInfoDatasAvatarNotEmpty: SELECT * FROM channel_info where  avatar_path is not null;
allSessionAvatarNotEmpty: SELECT * FROM session where avatar_path is not null;
allContactAvatarNotEmpty: SELECT * FROM contact where avatar_path is not null;
topMsgIdByUserNameAdmin: SELECT msg_id FROM message_top_admin WHERE owner = ?1 AND state IN (-1, 0, 1, 2, 3, 4, 5, 6) AND type IN (0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26,27,28,29) ORDER BY update_time ASC LIMIT 1000;
topMsgByUserNameAdmin: SELECT * FROM message_top_admin WHERE owner = ?1 AND state IN (-1, 0, 1, 2, 3, 4, 5, 6) AND type IN (0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24,25,26,27,28,29) ORDER BY update_time ASC LIMIT 1000;
allGroupMemberWithTags: SELECT * FROM group_member WHERE group_id = :group_id AND tags IS NOT NULL ORDER BY role DESC;