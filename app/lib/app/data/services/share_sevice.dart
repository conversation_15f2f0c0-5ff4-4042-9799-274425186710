//FileName ShareSevice
// <AUTHOR>
//@Date 2023/2/15 17:06
import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/data/events/events.dart';
import 'package:flutter_metatel/core/task/time_task.dart';
import 'package:get/get.dart';
import 'package:share_handler/share_handler.dart';

import '../../../core/utils/app_log.dart';
import '../../../core/utils/device_util.dart';
import '../../../core/utils/events_bus.dart';
import '../../../core/utils/util.dart';
import '../enums/enum.dart';
import '../enums/path.dart';
import 'chatio_service.dart';
import 'event_service.dart';

class ShareService extends GetxService {
  StreamSubscription? sream;
  static String tag = 'ShareService';
  List<SharedAttachment?>? shareattachList;
  String? content;

  Future<ShareService> init() async {
    AppLogger.d('$tag init ');
    ShareHandlerPlatform.instance;
    sream = ShareHandlerPlatform.instance.sharedMediaStream.listen((media) {
      if (media.attachments != null) {
        shareattachList = media.attachments;
      }
      AppLogger.d('$tag  ${media.encode()}');
      AppLogger.d('$tag  shareattachList ${shareattachList?.length}');
      content = media.content;
      AppLogger.d('$tag  content $content');

      if ((shareattachList?.isNotEmpty ?? false) ||
          (content?.isNotEmpty ?? false)) {
        Get.find<EventBus>().fire(ShareEvent());
      }
    });
    return this;
  }

  @override
  void onClose() {
    sream?.cancel();
    sream = null;
    super.onClose();
  }

  void getMedia(BuildContext context) async {
    AppLogger.d('$tag  getMedia  start');
    final handler = ShareHandlerPlatform.instance;
    SharedMedia? media = await handler.getInitialSharedMedia();
    if (media != null && media.attachments != null) {
      shareattachList = media.attachments;
    }
    content = media?.content;
    sendForward(context);
    AppLogger.d('$tag  getMedia  ${shareattachList?.length}');
  }

  sendForward(BuildContext context) async {
    if ((shareattachList?.isEmpty ?? false) && (content?.isEmpty ?? true)) {
      AppLogger.e('$tag  sendForward shareattachList is null ');
      return;
    }
    var mlist = List<MessageEvent>.empty(growable: true);
    if (shareattachList?.isNotEmpty ?? false) {
      for (var e in shareattachList!) {
        AppLogger.d('$tag   path=${e?.path}');
        try{
          e?.path = Uri.decodeComponent(e.path);
        }catch(e){

        }
        String? fileName = e?.path.split('/').last;
        AppLogger.d('$tag   path=${e?.path}');
        AppLogger.d('$tag   type=${e?.type}');
        
        AppLogger.d('$tag   fileName=$fileName');
        if (e == null) {
          return;
        }
        MessageType? type;
        var srcFileSize;
        switch (e.type) {
          case SharedAttachmentType.image:
            type = MessageType.image;
            break;
          case SharedAttachmentType.audio:
            type = MessageType.audio;
            break;
          case SharedAttachmentType.video:
            type = MessageType.video;
            break;
          case SharedAttachmentType.file:
            type = MessageType.file;
            var srcFile = File(e.path);
            if (!srcFile.existsSync()) {
              AppLogger.d('$tag   srcFile is not existsSync');
              return;
            }
            srcFileSize = srcFile.lengthSync();
            break;
        }

        String path = e.path;
        if (DeviceUtil.isIOS()) {
          var p = await mtCopyFile(File(path), appSupportDir).catchError((e) {
            return null;
          });
          if (p?.isNotEmpty ?? false) {
            path = p!;
          }
          AppLogger.d('$tag   p=$p');
        }

        var messageEvent = MessageEvent(uuid(),
            owner: '',
            type: type,
            chatType: ChatType.singleChat,
            // 转发时会根据选择对象修改，daen
            // filePath: filePath,
            // thumbnailPath: thumbnailPath,
            fileSize: srcFileSize,
            fileName: fileName,
            direction: 1,
            filePath: path,
            dateTime: TimeTask.instance.getNowDateTime());
        mlist.add(messageEvent);
      }
    }

    if (content?.isNotEmpty ?? false) {
      var messageEvent = MessageEvent(uuid(),
          owner: '',
          body: content ?? '',
          type: MessageType.text,
          chatType: ChatType.singleChat,
          // 转发时会根据选择对象修改，daen
          // filePath: filePath,
          // thumbnailPath: thumbnailPath,
          direction: 1,
          dateTime: TimeTask.instance.getNowDateTime());
      mlist.add(messageEvent);
    }
    if (mlist.isNotEmpty) {
      await shareForwardMessage(context, mlist);
    }
    shareattachList?.clear();
    content = null;
  }
}
