import 'package:flutter_metatel/app/modules/message/message_controller.dart';
import 'package:flutter_metatel/app/widgets/emoji/emoji.dart';
import 'package:flutter_metatel/app/widgets/emoji/emoji_remote.dart';
import 'package:flutter_metatel/core/languages/l.dart';
import 'package:get/get.dart';

const _fluentuiEmoji = "fluentui_emoji.zip";

class EmojiManageService extends GetxService {
  List<EmojiRemote> allEmoji = [];
  Future<EmojiManageService> init() async {
    return this;
  }

  void load() {
    allEmoji.add(EmojiRemote(
      _fluentuiEmoji,
      "MIT",
      EmojiSource.fluentuiEmoji,
      fronSourceTips: L.emoji_from_mit.tr,
    ));
  }

  void checkAllEmoji() {
    for (var element in allEmoji) {
      element.isOK();
    }
  }

  List<EmojiTabData> tabs(MessageController messageController) {
    List<EmojiTabData> datas = [];
    for (var element in allEmoji) {
      if (!element.isOK()) {
        continue;
      }

      datas.add(
          EmojiTabData(element.title(), element.widget(messageController)));
    }
    return datas;
  }

  EmojiFileData? find(String key) {
    if (EmojiUtil.emojiMapCus.containsKey(key)) {
      return EmojiFileData(
        EmojiUtil.emojiMapCus[key] ?? '',
        EmojiFileType.assets,
        EmojiSource.local,
      );
    }

    if (EmojiUtil.mtEmojiMap.containsKey(key)) {
      var file = EmojiUtil.mtEmojiMap[key] ?? '';

      var source = EmojiSource.local;
      if (file.contains(EmojiUtil.openmojiDir)) {
        source = EmojiSource.openEmoji;
      }

      return EmojiFileData(
        file,
        EmojiFileType.assets,
        source,
      );
    }

    for (var element in allEmoji) {
      var data = element.find(key);
      if (data != null) {
        return data;
      }
    }
    return null;
  }
}
