//FileName headset_service
// <AUTHOR>
//@Date 2022/7/18 10:37
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:get/get.dart';
import 'package:headset_connection_event/headset_event.dart';

import '../../../core/utils/events_bus.dart';
import '../events/events.dart';

class HeadSetService extends GetxService{
  final _headsetPlugin = HeadsetEvent();
  HeadsetState? _headsetState;
  Future<HeadSetService>init()async{
    _headsetPlugin.setListener((val) async{
      _headsetState = await _headsetPlugin.getCurrentState;
      AppLogger.d('_headsetState== $val');
      Get.find<EventBus>().fire(HeadSetChangeEvent(val==HeadsetState.CONNECT));
    });
    return this;
  }

  Future<bool>getHeadSetIsConnected ()async{
    _headsetState= await _headsetPlugin.getCurrentState;
    AppLogger.d('_headsetState== $_headsetState');
    return _headsetState==HeadsetState.CONNECT;
  }



}
