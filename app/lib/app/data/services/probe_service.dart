//FileName ProbeService
// <AUTHOR>
//@Date 2023/3/15 11:53
import 'package:async_task/async_task_extension.dart';
import 'package:flutter_metatel/app/data/models/avatar_model.dart';
import 'package:flutter_metatel/app/data/providers/api/21_plan.dart';
import 'package:flutter_metatel/app/data/providers/db/database.dart';
import 'package:flutter_metatel/app/data/services/config_service.dart';
import 'package:flutter_metatel/core/task/time_task.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:flutter_metatel/core/utils/comm_util.dart';
import 'package:flutter_metatel/core/utils/util.dart';
import 'package:get/get.dart';

import '../../../core/values/config.dart';
import '../providers/api/invite.dart';

class ProbeService extends GetxService {
  static String tag = 'ProbeService';
  int _nonce = 0;
  int intervalsTime = 8; //间隔8小时提交
  int commentTime = 60 * 60;

  Future<ProbeService> init() async {
    return this;
  }

  Future<void> sendTask(String type) async {
    // if(true){
    //   return;
    // }
    if (type == MiningTaskType.MINING_TASK_SEND_MESSAGE ||
        type == MiningTaskType.MINING_TASK_SEND_CHANNEL) {
      sendMessageTask(type == MiningTaskType.MINING_TASK_SEND_MESSAGE);
    }
    var isSended = Get.find<AppConfigService>().readMiningTaskTypeState(type);
    // AppLogger.d('$tag sendTask type=$type isSended=$isSended');
    if (isSended) {
      return;
    }
    //	"type": 0  // 类型，1-修改昵称，2-修改头像，3-第一个好友，4-第一个社区，5-第一条点对点消息，6-第一条群聊消息，
    //	7-第一个语言通话，8-第一个视频通话，9-第一个Dapp
    int? taskId;
    switch (type) {
      case MiningTaskType.MINING_TASK_UPDATE_NICKNAME:
        taskId = 1;
        break;
      case MiningTaskType.MINING_TASK_UPDATE_AVATAR:
        taskId = 2;
        break;
      case MiningTaskType.MINING_TASK_ADD_CONTACTOR:
        taskId = 3;
        break;
      case MiningTaskType.MINING_TASK_ADD_CHANNEL:
        taskId = 4;
        break;
      case MiningTaskType.MINING_TASK_SEND_MESSAGE:
        taskId = 5;
        break;
      case MiningTaskType.MINING_TASK_SEND_CHANNEL:
        taskId = 6;
        break;
      case MiningTaskType.MINING_TASK_CALL_AUDIO:
        taskId = 7;
        break;
      case MiningTaskType.MINING_TASK_CALL_VIDEO:
        taskId = 8;
        break;
      case MiningTaskType.MINING_TASK_LOGIN_DAPP:
        taskId = 9;
        break;
    }
    // var baseR = await Get.find<Plan21Api>().submit({});

    if (taskId != null) {
      var account = Get.find<AppConfigService>().getUserNameWithoutDomain();
      Map map = {'type': taskId, 'account': account};
      var baseRes = await Get.find<Plan21Api>().submitBaseOperate(map);
      if (baseRes.body?.code == 200) {
        await Get.find<AppConfigService>().saveMiningTaskType(type);
      }
      // AppLogger.d('$tag sendTask baseRes=${json.encode(baseRes.body)}');
    }
  }

  Future<void> sendMessageTask(bool isSingleMessage) async {
    var data = TimeTask.instance.getNowDateTime();
    if (canSubmitMessageInfo(isSingleMessage, data)) {
      var baseRes = await Get.find<Plan21Api>().submitClock(!isSingleMessage);
      if (baseRes.body?.code == 200) {
        var key = '${data.month}-${data.day}-${data.hour}';
        Get.find<AppConfigService>().saveMessageTask(key, isSingleMessage);
        AppLogger.d('sendMessageTask key=$key');
      }
    }
  }

  submitUserInfo() async {
    var conf = Get.find<AppConfigService>();

    var isSubmited = conf.readSubmitUserinfo();
    if (!isSubmited) {
      var baseRes = await Get.find<Plan21Api>().getUserInfo();
      if (baseRes.body?.code == 200) {
        var nickName = conf.getMySelfDisplayName();
        if (nickName.isNotEmpty) {
          await sendTask(MiningTaskType.MINING_TASK_UPDATE_NICKNAME);
        }
        AvatarModel avatar =
            AvatarModel.fromJson(conf.getMySelfAvatarInfo() ?? {});
        if (avatar.path?.isNotEmpty ?? false) {
          await sendTask(MiningTaskType.MINING_TASK_UPDATE_AVATAR);
        }
        await sendTask(MiningTaskType.MINING_TASK_LOGIN_DAPP);
        // conf.saveSubmitUserinfo(true);
      }
    }
  }

  ////8-12、13-17、18-23 时间段
  bool canSubmitMessageInfo(bool isSingleMessage, DateTime data) {
    var _currentRecord =
        Get.find<AppConfigService>().readMessageTask(isSingleMessage);
    AppLogger.d('canSubmitInfo _currentRecord=$_currentRecord');

    if (_currentRecord?.isEmpty ?? true) {
      return true;
    }
    var key = '${data.month}-${data.day}-${data.hour}';
    if (key != _currentRecord) {
      return true;
    }
    return false;

    var currentDataArray = _currentRecord?.split('-');
    if ((currentDataArray?.length != 3)) {
      return true;
    }

    var currentMonth = currentDataArray![0];
    var currentDay = currentDataArray[1];
    var currentHour = currentDataArray[2];

    int cMonth = int.parse(currentMonth);
    int cDay = int.parse(currentDay);
    int cHour = int.parse(currentHour);
    var currentMonthDay = '$cMonth-$cDay';
    AppLogger.d('canSubmitInfo currentHour=$currentHour');
    var hour = data.hour;
    var monthData = '${data.month}-${data.day}';
    if (hour >= 8 && hour <= 12) {
      //时间段1
      if (currentMonthDay != monthData) {
        return true;
      }
      if (cHour >= 8 && cHour <= 12) {
        return false;
      }
      return true;
    } else if (hour >= 13 && hour <= 17) {
      //时间段2
      if (currentMonthDay != monthData) {
        return true;
      }
      if (cHour >= 13 && cHour <= 17) {
        return false;
      }
      return true;
    } else if (hour >= 18 && hour <= 23) {
      //时间段3
      if (currentMonthDay != monthData) {
        return true;
      }
      if (cHour >= 18 && cHour <= 23) {
        return false;
      }
      return true;
    } else {
      //其他时间段不能打卡
      return false;
    }
  }
}
