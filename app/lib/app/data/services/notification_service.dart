import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:flutter_native_timezone/flutter_native_timezone.dart';
import 'package:get/get.dart';
import 'package:rxdart/rxdart.dart';
import 'package:timezone/data/latest_all.dart' as tz;
import 'package:timezone/timezone.dart' as tz;
import 'package:device_info_plus/device_info_plus.dart';

import '../../../core/languages/l.dart';
import '../../../core/notifycation/received_notification.dart';
import '../../../core/utils/device_util.dart';
import '../../../core/values/config.dart';
import '../../../routes/pages.dart';
import 'config_service.dart';

/// FileName NotificationService
///
/// <AUTHOR>
/// @Date 2022/5/12 14:31
///
class NotificationService extends GetxService {
  final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
      FlutterLocalNotificationsPlugin();
  AndroidNotificationDetails? androidPlatformChannelSpecifics;
   DarwinNotificationDetails? iOSPlatformChannelSpecifics ;
   DarwinNotificationDetails? iOSPlatformChannelSpecificsSound ;
  DarwinNotificationDetails? macOSPlatformChannelSpecifics ;
  /// Streams are created so that app can respond to notification-related events
  /// since the plugin is initialised in the `main` function
  final BehaviorSubject<ReceivedNotification>
      didReceiveLocalNotificationSubject =
      BehaviorSubject<ReceivedNotification>();

  static final  BehaviorSubject<String?> selectNotificationSubject =
      BehaviorSubject<String?>();
  String urlLaunchActionId = 'id_1';

  /// A notification action which triggers a App navigation event
  String navigationActionId = 'id_3';
  static const MethodChannel platform =
      MethodChannel('dexterx.dev/flutter_local_notifications_example');
  String? selectedNotificationPayload;


  /// Defines a iOS/MacOS notification category for text input actions.
  String darwinNotificationCategoryText = 'textCategory';

  /// Defines a iOS/MacOS notification category for plain actions.
  String darwinNotificationCategoryPlain = 'plainCategory';

  Future<NotificationService> init() async {
    WidgetsFlutterBinding.ensureInitialized();
    await _configureLocalTimeZone();
    final NotificationAppLaunchDetails? notificationAppLaunchDetails =
        !kIsWeb && Platform.isLinux
            ? null
            : await flutterLocalNotificationsPlugin
                .getNotificationAppLaunchDetails();
    if (notificationAppLaunchDetails?.didNotificationLaunchApp ?? false) {
      selectedNotificationPayload = notificationAppLaunchDetails?.notificationResponse?.payload;
    }
    AndroidInitializationSettings initializationSettingsAndroid =
        const AndroidInitializationSettings('ic_launcher');

    /// Note: permissions aren't requested here just to demonstrate that can be
    /// done later
    final List<DarwinNotificationCategory> darwinNotificationCategories =
    <DarwinNotificationCategory>[
      DarwinNotificationCategory(
        darwinNotificationCategoryText,
        actions: <DarwinNotificationAction>[
          DarwinNotificationAction.text(
            'text_1',
            'Action 1',
            buttonTitle: 'Send',
            placeholder: 'Placeholder',
          ),
        ],
      ),
      DarwinNotificationCategory(
        darwinNotificationCategoryPlain,
        actions: <DarwinNotificationAction>[
          DarwinNotificationAction.plain('id_1', 'Action 1'),
          DarwinNotificationAction.plain(
            'id_2',
            'Action 2 (destructive)',
            options: <DarwinNotificationActionOption>{
              DarwinNotificationActionOption.destructive,
            },
          ),
          DarwinNotificationAction.plain(
            navigationActionId,
            'Action 3 (foreground)',
            options: <DarwinNotificationActionOption>{
              DarwinNotificationActionOption.foreground,
            },
          ),
          DarwinNotificationAction.plain(
            'id_4',
            'Action 4 (auth required)',
            options: <DarwinNotificationActionOption>{
              DarwinNotificationActionOption.authenticationRequired,
            },
          ),
        ],
        options: <DarwinNotificationCategoryOption>{
          DarwinNotificationCategoryOption.hiddenPreviewShowTitle,
        },
      )
    ];
    final DarwinInitializationSettings initializationSettingsDarwin =
    DarwinInitializationSettings(
      requestAlertPermission: false,
      requestBadgePermission: false,
      requestSoundPermission: false,
      notificationCategories: darwinNotificationCategories,
    );


    final LinuxInitializationSettings initializationSettingsLinux =
        LinuxInitializationSettings(
      defaultActionName: 'Open notification',
      defaultIcon: AssetsLinuxIcon('icons/ic_logo.png'),
    );
    final InitializationSettings initializationSettings =
        InitializationSettings(
      android: initializationSettingsAndroid,
      iOS: initializationSettingsDarwin,
      macOS: initializationSettingsDarwin,
      linux: initializationSettingsLinux,
    );
    // await flutterLocalNotificationsPlugin.initialize(initializationSettings,
    //     onDidReceiveBackgroundNotificationResponse: (details) async {
    //   AppLogger.d('push click &payload');
    //   if (details.payload != null) {
    //     AppLogger.d('notification payload: ${details.payload}');
    //   }
    //   selectedNotificationPayload = details.payload;
    //   selectNotificationSubject.add(details.payload);
    // });
    await flutterLocalNotificationsPlugin.initialize(
      initializationSettings,
      onDidReceiveNotificationResponse:didReceiveNotificationResponse,
      onDidReceiveBackgroundNotificationResponse: notificationTapBackground,
    );
    _configureDidReceiveLocalNotificationSubject();
    _configureSelectNotificationSubject();
    return this;
  }

  static void didReceiveNotificationResponse(
      NotificationResponse notificationResponse) {
    if (notificationResponse.payload == 'call') {
      // 处理点击操作
      Get.toNamed(Routes.WEBRTC_CALL);
    }
    selectNotificationSubject.add(notificationResponse.payload);

    switch (notificationResponse.notificationResponseType) {
      case NotificationResponseType.selectedNotification:
        selectNotificationSubject.add(notificationResponse.payload);
        break;
      case NotificationResponseType.selectedNotificationAction:
        selectNotificationSubject.add(notificationResponse.payload);
        break;
    }
  }

  @pragma('vm:entry-point')
  static void notificationTapBackground(NotificationResponse notificationResponse) {
    // ignore: avoid_print
    print('notification(${notificationResponse.id}) action tapped: '
        '${notificationResponse.actionId} with'
        ' payload: ${notificationResponse.payload}');
    if (notificationResponse.input?.isNotEmpty ?? false) {
      // ignore: avoid_print
      print(
          'notification action tapped with input: ${notificationResponse.input}');
    }
    if (notificationResponse.payload == 'call') {
      // 处理后台点击通知逻辑
      Get.toNamed(Routes.WEBRTC_CALL);
      print(
          'Incoming call notification tapped with payload: ${notificationResponse.payload}');
    }
  }
  void initNotification() async{
    if (Platform.isAndroid) {
      androidPlatformChannelSpecifics =
          AndroidNotificationDetails(
          NotificationValue.message_notification_channel_id.tr,
          L.channel_name_of_message_notification.tr,
          channelDescription: '',
          playSound: true,
          enableVibration: true,
          importance: Importance.max,
          priority: Priority.high,
          fullScreenIntent: true,
          sound: const RawResourceAndroidNotificationSound('msg_tone'),
          ticker: 'ticker');
      // if(true){
      //   await flutterLocalNotificationsPlugin
      //       .resolvePlatformSpecificImplementation<
      //       AndroidFlutterLocalNotificationsPlugin>()?.deleteNotificationChannel(NotificationValue.application_id);
      // }
      // _startForegroundService();
      _createNotificationChannel();
    }else if(Platform.isIOS||Platform.isMacOS){
      iOSPlatformChannelSpecifics =
          DarwinNotificationDetails(subtitle: L.channel_name_of_message_notification.tr,presentSound: false);
      iOSPlatformChannelSpecificsSound =
          DarwinNotificationDetails(subtitle: L.channel_name_of_message_notification.tr);
      macOSPlatformChannelSpecifics =
          DarwinNotificationDetails(subtitle: L.channel_name_of_message_notification.tr);
    }
  }

  @override
  onClose() {
    didReceiveLocalNotificationSubject.close();
    selectNotificationSubject.close();
    super.onClose();
  }

  void _configureDidReceiveLocalNotificationSubject() {
    didReceiveLocalNotificationSubject.stream
        .listen((ReceivedNotification receivedNotification) async {
      AppLogger.d(
          'ReceivedNotification${receivedNotification.title}${receivedNotification.body}${receivedNotification.payload}');
    });
  }

  void _configureSelectNotificationSubject() {
    selectNotificationSubject.stream.listen((String? payload) async {
      //Fluttertoast.showToast(msg: '消息通知111');
        // 跳转到通话界面
        Get.toNamed(Routes.WEBRTC_CALL); // 示例：跳转到WebRTC通话界面
    });
  }

  Future<void> _configureLocalTimeZone() async {
    if (kIsWeb || Platform.isLinux) {
      return;
    }
    tz.initializeTimeZones();
    final String timeZoneName = await FlutterNativeTimezone.getLocalTimezone();
    tz.setLocalLocation(tz.getLocation(timeZoneName));
  }

  Future<void> _showAndroidNotification(
      String title, String? desc, String? type,int state) async {
    NotificationDetails platformChannelSpecifics =
        NotificationDetails(android: androidPlatformChannelSpecifics);
    await flutterLocalNotificationsPlugin
        .show(state, title, desc, platformChannelSpecifics, payload: type);
  }

  Future<void> _showIosNotification(
      String title, String? desc, String? type,int state,bool isSound) async {
    NotificationDetails platformChannelSpecifics = NotificationDetails(
        iOS: isSound?iOSPlatformChannelSpecificsSound:iOSPlatformChannelSpecifics, macOS: macOSPlatformChannelSpecifics);
    await flutterLocalNotificationsPlugin
        .show(state, title, desc, platformChannelSpecifics, payload: type);
  }

  void showMsgNotification(String title, {String? desc, String? type,bool isSound=true}) {
    if (Platform.isAndroid) {
      _showAndroidNotification(title, desc, type, 0);
    } else if (Platform.isMacOS || Platform.isIOS) {
      _showIosNotification(title, desc, type, 0,isSound);
    }
  }
  void showCallNotification(String title, {String? desc, String? type,bool isSound=true}) {
    if (Platform.isAndroid) {
      _showAndroidNotification(title, desc, type, 2);
    } else if (Platform.isMacOS || Platform.isIOS) {
      _showIosNotification(title, desc, type, 2,isSound);
    }
  }
  Future<void> _deleteNotificationChannel(String channelId) async {
    var list = await flutterLocalNotificationsPlugin
        .resolvePlatformSpecificImplementation<
            AndroidFlutterLocalNotificationsPlugin>()
        ?.getNotificationChannels();
    if (list != null) {
      for (var c in list) {
        if (c.id == channelId) {
          await flutterLocalNotificationsPlugin
              .resolvePlatformSpecificImplementation<
                  AndroidFlutterLocalNotificationsPlugin>()
              ?.deleteNotificationChannel(channelId);
          Get.find<AppConfigService>().saveChannelDeleted();
          break;
        }
      }
    }
  }

  Future<void> _startForegroundService() async {
    DeviceInfoPlugin info = DeviceInfoPlugin();
    var androidInfo = await info.androidInfo;
    int code = androidInfo.version.sdkInt;
    bool isUpend34 = code>=34;
    AppLogger.d('_startForegroundService code=$code');
    AndroidNotificationDetails androidPlatformChannelSpecifics =
    AndroidNotificationDetails(NotificationValue.application_id,
      L.metatel_system_warning.tr,
      channelDescription: '',
      importance: Importance.max,
      playSound: false,
      enableVibration: false,
      priority: Priority.high,
      fullScreenIntent: true,
      ticker: 'ticker',);

    await flutterLocalNotificationsPlugin
        .resolvePlatformSpecificImplementation<
            AndroidFlutterLocalNotificationsPlugin>()
        ?.startForegroundService(1, L.application_in_progress.tr, null,
            notificationDetails: androidPlatformChannelSpecifics,
            startType: AndroidServiceStartType.startNotSticky,
             foregroundServiceTypes:isUpend34 ? {AndroidServiceForegroundType.foregroundServiceTypeDataSync}:null,
            payload: '')
        .catchError((error) {
      AppLogger.e('_startForegroundService error ${error.toString()}');
    });
  }

  Future<void> stopForegroundService() async {
    await flutterLocalNotificationsPlugin
        .resolvePlatformSpecificImplementation<
            AndroidFlutterLocalNotificationsPlugin>()
        ?.stopForegroundService();
  }

  void requestPermissions() async{
    if (Platform.isIOS) {
      flutterLocalNotificationsPlugin
          .resolvePlatformSpecificImplementation<
              IOSFlutterLocalNotificationsPlugin>()
          ?.requestPermissions(
            alert: true,
            badge: true,
            sound: true,
          );
    } else if (Platform.isMacOS) {
      flutterLocalNotificationsPlugin
          .resolvePlatformSpecificImplementation<
              MacOSFlutterLocalNotificationsPlugin>()
          ?.requestPermissions(
            alert: true,
            badge: true,
            sound: true,
          );
    }else if (Platform.isAndroid){
      final AndroidFlutterLocalNotificationsPlugin? androidImplementation =
          flutterLocalNotificationsPlugin.resolvePlatformSpecificImplementation<
              AndroidFlutterLocalNotificationsPlugin>();
      final bool? granted = await androidImplementation?.requestNotificationsPermission();
      AppLogger.d('notification Android granted=$granted');
    }
  }

  Future<void> cancelNotification() async {
    await flutterLocalNotificationsPlugin.cancel(0);
  }
  Future<void> cancelCallNotification() async {
    await flutterLocalNotificationsPlugin.cancel(2);
  }

  Future<void> cancelNotificationWithTag() async {
    await flutterLocalNotificationsPlugin.cancel(0, tag: 'tag');
  }
  Future<void> cancelAllNotifications() async {
    cancelNotification();
    cancelCallNotification();
    await flutterLocalNotificationsPlugin.cancelAll();
  }

  Future<void> showNotificationCustomSound() async {
    const DarwinNotificationDetails iOSPlatformChannelSpecifics =
        DarwinNotificationDetails(sound: 'sound/');
    const DarwinNotificationDetails macOSPlatformChannelSpecifics =
        DarwinNotificationDetails(sound: '');
    final LinuxNotificationDetails linuxPlatformChannelSpecifics =
        LinuxNotificationDetails(
      sound: AssetsLinuxSound('sound/slow_spring_board.mp3'),
    );
    final NotificationDetails platformChannelSpecifics = NotificationDetails(
      android: androidPlatformChannelSpecifics,
      iOS: iOSPlatformChannelSpecifics,
      macOS: macOSPlatformChannelSpecifics,
      linux: linuxPlatformChannelSpecifics,
    );
    await flutterLocalNotificationsPlugin.show(
      0,
      L.app_name.tr,
      L.other_has_call.tr,
      platformChannelSpecifics,
    );
  }
  Future<void> _createNotificationChannel() async {
    var deleted= Get.find<AppConfigService>().readChannelDeleted();
    AppLogger.d('_createNotificationChannel deleted=$deleted');
    if(!deleted){
      await _deleteNotificationChannel(NotificationValue.call_notification_channel_id);
      await _deleteNotificationChannel(NotificationValue.msg_notification_channel_id);
    }
     if(!await DeviceUtil.isHuaweiPush()){
      AndroidNotificationChannel androidMsgChannel = AndroidNotificationChannel(
        NotificationValue.msg_notification_channel_id,
        L.channel_msg_of_notification.tr,
        sound: const RawResourceAndroidNotificationSound('msg_tone'),
        description: '',
         importance:Importance.max,
      );
      await flutterLocalNotificationsPlugin
          .resolvePlatformSpecificImplementation<
              AndroidFlutterLocalNotificationsPlugin>()
          ?.createNotificationChannel(androidMsgChannel);
    }
     AndroidNotificationChannel androidCallChannel =
     AndroidNotificationChannel(
      NotificationValue.call_notification_channel_id,
      L.channel_call_of_notification.tr,
      description: '',
      sound: const RawResourceAndroidNotificationSound('ring_new'),
       importance:Importance.max,
    );
    await flutterLocalNotificationsPlugin
        .resolvePlatformSpecificImplementation<
            AndroidFlutterLocalNotificationsPlugin>()
        ?.createNotificationChannel(androidCallChannel);

  }
}
