/*
 * <AUTHOR> <PERSON><PERSON>
 * @Date         : 2022-04-21 10:42:08
 * @Description  : 应用程序配置相关
 * 
 * @LastEditors  : <PERSON><PERSON>
 * @LastEditTime : 2022-09-16 23:31:19
 * @FilePath     : /flutter_metatel/lib/app/data/services/config_service.dart
 */

import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/data/enums/enum.dart';
import 'package:flutter_metatel/app/data/providers/db/database.dart';
import 'package:flutter_metatel/core/task/private_task.dart';
import 'package:flutter_metatel/core/utils/util.dart';
import 'package:flutter_metatel/core/values/config.dart';
import 'package:flutter_metatel/core/values/keys.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:worker_manager/worker_manager.dart';

import '../../../core/languages/l.dart';
import '../../../core/utils/app_log.dart';
import '../../../core/utils/events_bus.dart';
import '../enums/path.dart';
import '../events/events.dart';
import '../models/avatar_model.dart';
import '../models/invite_info_model.dart';
import '../models/res/node_conf.dart';
import '../models/turn_model.dart';

class AppConfigService extends GetxService {
  Future<AppConfigService> init() async {
    await initConfigPath();
    WidgetsFlutterBinding.ensureInitialized();
    box = GetStorage('GetStorage', appSupportDir!.path);
    await GetStorage.init();
    // box = GetStorage();
    // Get.changeTheme(box?.read(THEME) ? ThemeData.dark() : ThemeData.light());
    Config.getCerByte();
    AppLogger.d('AppConfigService=end');
    return this;
  }

  GetStorage? box;

  /// 当前会话ID
  String _currSessionID = '';


  set currSessionIDChange(String id) => _currSessionID = id;

  String get currSessionID => _currSessionID;

  final String _draftPrefix = 'draft_';
  final String _msgTopPrefix = 'msgTop_';
  final String _msgReplyPrefix = 'msgReply_';
  final String _channelInfoOptionsCountSuffix = '_channelInfoOptionsCount';

  bool getTheme() => box?.read(THEME) ?? false;

  bool getNotFirstLogin() {
    AppLogger.d('getNotFirstLogin box=$box');
    var b =  box?.read(NOT_FIRST_LOGIN) ?? false;
    AppLogger.d('getNotFirstLogin b=$b');
    return b;
  }

  bool mySelfInfoNeedUpload() => box?.read(MYSELF_INFO_NEED_UPLOAD) ?? false;

  bool firstMySelfInfoNeedUpload() => box?.read(MYSELF_INFO_NEED_UPLOAD_FIRST) ?? true;
  String getToken() => box?.read(TOKEN) ?? '';

  String getPhone() => box?.read(PHONE) ?? '';

  Future<String> getPrivateKey() async {
    await PrivateConfigTask().configPrivateKeyFromMnemonic();
    return Config.privateKeyBase64;
  }

  String getMySelfDisplayName() {
    String nickName = box?.read(MYSELF_DISPLAY_NAME)??"";
    nickName=displayNameProcess(nickName,getUserNameWithoutDomain());
    return nickName;
  }

  String getMySelfFirstName() => box?.read(MYSELF_FIRST_NAME) ?? '';

  String getMySelfLastName() => box?.read(MYSELF_LAST_NAME) ?? '';

  Map<String, dynamic>? getMySelfAvatarInfo() => box?.read(MYSELF_AVATAR_INFO);

  AvatarModel getMySelfAvatarInfoModel() =>
      AvatarModel.fromJson(getMySelfAvatarInfo() ?? {});

  Map<String, dynamic>? getTurnInfo() => box?.read(TURN_INFO);

  Map<String, dynamic>? getAwsInfo() => box?.read(AWS_INFO);

  Map<String, dynamic>? getOssInfo() => box?.read(OSS_INFO);

  Map<String, dynamic>? getStsInfo() => box?.read(STS_INFO);

  bool getUserMessageSilenceState(String userName) =>
      box?.read(CUSTOM_USER_MESSAGE_SILENCE_ + userName) ?? false;

  bool getMessageRingEnabled() => box?.read(ENABLE_MESSAGE_RING) ?? true;

  bool getMessageVibrateEnabled() => box?.read(ENABLE_MESSAGE_VIBRATE) ?? true;

  bool getShowBrowser() => box?.read(SHOW_BROWSER) ?? false;

  setShowBrowser(bool b) async {
    await box?.write(SHOW_BROWSER, b);
  }

  bool getLocalAgent() => box?.read(LOCAL_AGENT) ?? false;

  setLocalAgent(bool b) async {
    await box?.write(LOCAL_AGENT, b);
  }


  String getUserNameMex() => box?.read(USER_NAME_MEX) ?? "";

  String getUserMobileMex() => box?.read(USER_MOBILE_MEX) ?? "";

  double getKeyBoardHeight() => box?.read(KEY_BOARD_HEIGHT) ?? 0;

  String getDeviceCode() => box?.read(DEVICE_CODE) ?? "";

  String getIOIId() {
    String ioiId = L.nft_advance_number_no.tr;
    String? readIoiId = readIOIId();
    if (readIoiId != null && readIoiId.isNotEmpty) {
      ioiId = readIoiId;
    }
    return ioiId;
  }
  String getSbtId() {
    // String sbt = L.nft_advance_number_no.tr;
    return '';
  }
  String? readIOIId() {
    String? readIoiId = box?.read(IOI_ID);
    return readIoiId;
  }

  String? readSBTId() {
    String? readSbtId = box?.read(SBT_ID);
    return readSbtId;
  }

   saveSBTId(String tid) async{
     await box?.write(SBT_ID,tid);
  }

  String? getNode() => box?.read(NODE);
  String? getNodeNEW() => box?.read(NODE_NEW);

  String? getDomain() => box?.read(DOMAIN);
  String? getNodePort() => box?.read(NODE_PORT);
  String? getNodeNewPort() => box?.read(NODE_PORT_NEW);

  String? getNodeConfigJsonStr() => box?.read(NODE_CONFIG);

  bool? hasSetPin() => box?.read(HAS_SET_PIN);

  String? getFileHelpUserName() => box?.read(FILE_HELP_USER_NAME);

  bool? biometricsOpen() => box?.read(BIOMETRICS_OPEN);

  bool? hasActivateWallet() => box?.read(HAS_ACTIVATE_WALLET);
  bool? isWalletOpen() => box?.read(IS_WALLET_OPEN);

  changeNotFistLogin(bool isLogined) async {
    await box?.write(NOT_FIRST_LOGIN, isLogined);
  }

  changeTheme(bool b) async {
    await box?.write(THEME, b);
    // Get.changeTheme(b ? ThemeData.dark() : ThemeData.light());
  }

  saveToken(String? token) async {
    await box?.write(TOKEN, token);
  }

  savePhone(String phone) async {
    await box?.write(PHONE, phone);
  }

  saveTurn(dynamic turn) async {
    await box?.write(TURN_INFO, turn);
  }

  saveAwsInfo(dynamic turn) async {
    if (turn != null) {
      AppLogger.d(json.encode(turn));
    }
    await box?.write(AWS_INFO, turn);
  }

  saveOssInfo(dynamic turn) async {
    if (turn != null) {
      AppLogger.d(json.encode(turn));
    }
    await box?.write(OSS_INFO, turn);
  }

  saveStsInfo(dynamic turn) async {
    if (turn != null) {
      AppLogger.d(json.encode(turn));
    }
    await box?.write(STS_INFO, turn);
  }

  saveUserMessageSilenceState(String userName, bool isMute) async {
    await box?.write(CUSTOM_USER_MESSAGE_SILENCE_ + userName, isMute);
  }

  saveMessageRingEnabled(bool enable) async {
    await box?.write(ENABLE_MESSAGE_RING, enable);
  }

  saveMessageVibrateEnabled(bool enable) async {
    await box?.write(ENABLE_MESSAGE_VIBRATE, enable);
  }

  saveMySelfDisplayName(String myselfLocalName) async {
    await box?.write(MYSELF_DISPLAY_NAME, myselfLocalName);
  }

  saveMyselfFirstName(String myselfFirstName) async {
    await box?.write(MYSELF_FIRST_NAME, myselfFirstName);
  }

  saveMyselfLastName(String myselfLastName) async {
    await box?.write(MYSELF_LAST_NAME, myselfLastName);
  }

  /// [AvatarModel]
  saveMyselfAvatarInfo(dynamic avatarInfo) async {
    await box?.write(MYSELF_AVATAR_INFO, avatarInfo);
  }

  saveUpdateVersion(int? code) async {
    await box?.write(APP_VERSION, code);
  }

  saveComplaintTime(Map<String, int> into) async {
    await box?.write(COMPLAINT_TIME, into);
  }

  Map<String, int>? readComplaintTime() => box?.read(COMPLAINT_TIME);

  Future<int?> getUpdateVersion() async {
    var code = await box?.read(APP_VERSION);
    return code;
  }

  String? getUserName() {
    String? userName = userNameDomain(Config.userNameWithoutDomain);
    return userName;
  }

  String getUserNameWithoutDomain() {
    return Config.userNameWithoutDomain;
  }

  // Future<String?> getPublicKeyBase64() async {
  //   String privateKey = await getPrivateKey();
  //   if (privateKey.isEmpty) {
  //     return null;
  //   }
  //   var keyByte = base64.decode(privateKey);
  //   String? userName = await ChatioNative.utilName(keyByte, Config.node());
  //   var decodeBase58 = Base58().decode(userName ?? "");
  //   var publicKey = base64.encode(decodeBase58);
  //   return publicKey;
  // }

  Map<String, dynamic>? getIceServers() {
    var turnUrl = getTurnInfo(); //SpUtil.readString(ConstSp.TURN_URL, "");
    if (turnUrl == null || turnUrl.isEmpty) {
      return null;
    }
    TurnModel turnMode = TurnModel.fromJson(turnUrl);
    if ((turnMode.username?.isEmpty ?? true) ||
        (turnMode.urls?.isEmpty ?? true) ||
        (turnMode.credential?.isEmpty ?? true)) {
      return null;
    }
    Map<String, dynamic>? iceServers = {
      'iceServers': [
        {
          'urls': turnMode.urls,
          'username': turnMode.username,
          'credential': turnMode.credential
        },
      ]
    };
    return iceServers;
  }

  void saveUserNameMex(String userNameMex) async {
    await box?.write(USER_NAME_MEX, userNameMex);
  }

  void saveUserMobileMex(String userMobileMex) async {
    await box?.write(USER_MOBILE_MEX, userMobileMex);
  }

  void saveMySelfInfoNeedUpload(bool isNeed) async {
    await box?.write(MYSELF_INFO_NEED_UPLOAD, isNeed);
  }
  void saveOneMySelfInfoNeedUpload(bool isNeed) async {
    await box?.write(MYSELF_INFO_NEED_UPLOAD_FIRST, isNeed);
  }

  void saveKeyBoardHeight(double? height) async {
    // AppLogger.d("saveKeyBoardHeight==$height");
    await box?.write(KEY_BOARD_HEIGHT, height);
  }

  void saveDeviceCode(String? deviceCode) async {
    // AppLogger.d("saveDeviceCode==$deviceCode");
    await box?.write(DEVICE_CODE, deviceCode);
  }

  void saveIoiId(String? ioiId) async {
    // AppLogger.d("saveDeviceCode==$deviceCode");
    await box?.write(IOI_ID, ioiId);
  }

  /// 保存草稿
  void saveDraft(String? name, String? text) {
    if (name == null || name.isEmpty) {
      return;
    }

    if (text != null && text.trim().isEmpty) {
      text = null;
    }

    box?.write(_key(_draftPrefix, name), text);
    Get.find<EventBus>().fire(MsgDraftChangeEvent(name));
  }

  /// 获取草稿
  String? getDraft(String? name) {
    return box?.read(_key(_draftPrefix, name));
  }

  /// 清除草稿
  void removeDraft(String? name) {
    box?.remove(_key(_draftPrefix, name));
  }
  /// 保存回复msgID
  void saveReply(String? name, String? text) {
    if (name == null || name.isEmpty) {
      return;
    }

    if (text != null && text.trim().isEmpty) {
      text = null;
    }

    box?.write(_key(_msgReplyPrefix, name), text);
  }
  String _key(String prefix, String? name) {
    String key = prefix + (name ?? '');
    return key;
  }

  /// 获取回复msgID
  String? getReply(String? name) {
    return box?.read(_key(_msgReplyPrefix, name));
  }

  /// 清除回复msgID
  void removeReply(String? name) {
    box?.remove(_key(_msgReplyPrefix, name));
  }
  saveNode(String? node) async {
    Config.nodeDefault.value = node ?? "";
    await box?.write(NODE, node);
  }
  saveNodeNEW(String? node) async {
    Config.newNodeDefault = node ?? "";
    await box?.write(NODE_NEW, node);
  }

  saveDomain(String? domain) async {
    Config.domain = domain ?? "";
    await box?.write(DOMAIN, domain);
  }
  saveNodePort(String? nodePort) async {
    Config.nodeDefaultPort = nodePort ?? "";
    await box?.write(NODE_PORT, nodePort);
  }
  saveNodePortNew(String? nodePort) async {
    Config.newPortDefault = nodePort ?? "";
    await box?.write(NODE_PORT_NEW, nodePort);
  }
  void saveNodeConfig(String jsonNodeConfig) async {
    await box?.write(NODE_CONFIG, jsonNodeConfig);
  }

  saveHasSetPin(bool hasSetPin) async {
    await box?.write(HAS_SET_PIN, hasSetPin);
  }

  saveFileHelpUserName(String fileHelpUserName) async {
    await box?.write(FILE_HELP_USER_NAME, fileHelpUserName);
  }

  Future<void> saveDestructionPwd(String pwd) async {
    await box?.write(DESTRUCTION_PWD, pwd);
  }

  Future<String?> getDestructionPwd() async {
    var pwd = await box?.read(DESTRUCTION_PWD);
    return pwd;
  }

  Future<void> saveHasActivateWallet(bool hasActivateWallet) async {
    await box?.write(HAS_ACTIVATE_WALLET, hasActivateWallet);
  }

  Future<void> saveIsWalletOpen(bool isWalletOpen) async {
    await box?.write(IS_WALLET_OPEN, isWalletOpen);
  }

  /// 保存消息置顶
  void saveMsgTop(String? name, List<String>? msgId) {
    if ((name?.isEmpty??true) || (msgId?.isEmpty??true)) {
      return;
    }

    box?.write(_key(_msgTopPrefix, name), msgId);
  }

  List<String>? getMsgTopID(String? name) {
    var read = box?.read(_key(_msgTopPrefix, name));
    List<String>? ret;
    if (read.runtimeType == String) {
      ret = [read];
    } else if (read.runtimeType == List<dynamic>) {
      List<dynamic> l = read;
      ret = l.map((item) => item.toString()).toList();
    } else if (read.runtimeType == List<String>) {
      ret = read;
    }
    return (ret?.isEmpty??true)?null:ret;
  }

  void removeMsgTop(String? name,String? msgId) {
    Get.find<AppDatabase>().deleteTopMsgById(msgId??"");
    List<String>? topMsgList = getMsgTopID(name);
    topMsgList?.remove(msgId);
    saveMsgTop(name,topMsgList);
    // box?.remove(_key(_msgTopPrefix, name));
  }

  void cancelMsgTopTop(String? name, String? msgid,{bool clear=false}) {
    if(clear){
      Get.find<AppDatabase>().cleanTopMsgByUserName(name);
      box?.remove(_key(_msgTopPrefix, name));
      Get.find<EventBus>().fire(CancelMsgTopEvent(name ?? '', msgid ?? ''));
      return;
    }
    var value = getMsgTopID(name);
    if (msgid?.isNotEmpty == true && value!=null&&value.contains(msgid) ) {
      removeMsgTop(name,msgid);
      Get.find<EventBus>().fire(CancelMsgTopEvent(name ?? '', msgid ?? ''));
    }
  }

  List<dynamic>? getWalletNetwork() {
    return box?.read(WALLET_NETWORK);
  }

  Future<void> saveWalletNetwork(List<Map<String, dynamic>> datas) async {
    await box?.write(WALLET_NETWORK, datas);
  }

  int? getWalletSelectedNetwork() {
    return box?.read(WALLET_SELECTED_NETWORK_CHANID);
  }

  Future<void> saveWalletSelectedNetwork(int? chanID) async {
    await box?.write(WALLET_SELECTED_NETWORK_CHANID, chanID);
  }

  Map<String, dynamic>? getWalletTokenNetwork() {
    return box?.read(WALLET_TOKEN_NETWORK);
  }

  Future<void> saveWalletTokenNetwork(Map<String, List<String>> datas) async {
    await box?.write(WALLET_TOKEN_NETWORK, datas);
  }

  saveBiometricsOpen(bool isOpen) async {
    await box?.write(BIOMETRICS_OPEN, isOpen);
  }

  NodeConf? getNodeConfFromLocal() {
    String nodeConfigJsonStr = getNodeConfigJsonStr() ?? "";
    if (nodeConfigJsonStr.isNotEmpty) {
      var nodeConfJson = json.decode(
        nodeConfigJsonStr,
      );
      return NodeConf.fromJson(nodeConfJson);
    }
    return null;
  }

  saveUserAgreement(bool agree) async {
    await box?.write(USER_AGREEMENT, agree);
  }

  bool readUserAgreement() {
    return box?.read(USER_AGREEMENT) ?? false;
  }

  saveAnnouncementKey(String key, String announcement) async {
    await box?.write(key, announcement);
  }

  String readAnnouncementKey(
    String key,
  ) {
    return box?.read(key) ?? '';
  }

  saveEmojiMade(String data) async {
    await box?.write(EMOJI_TYPE, data);
  }

  readEmojiMade() => box?.read(EMOJI_TYPE);

  saveChannelDeleted() {
    box?.write(CHANNEL_DELETE, true);
  }

  bool readChannelDeleted() {
    return box?.read(CHANNEL_DELETE) ?? false;
  }

  Future<void> saveNeedPopInviteDialog(bool need) async {
    InviteInfoModel model = readInviteInfo() ?? InviteInfoModel();
    model.needPopInviteDialog = false;
    await changeInviteInfo(model);
    await box?.write(NEED_POP_INVITE_DIALOG, need);
  }

  bool readNeedPopInviteDialog() {
    var inviteInfo = readInviteInfo();
    var bool = inviteInfo?.needPopInviteDialog ??
        box?.read(NEED_POP_INVITE_DIALOG) ??
        true;
    return bool;
  }

  Future<void> saveIsBindRecommender() async {
    InviteInfoModel model = readInviteInfo() ?? InviteInfoModel();
    model.isBindRecommender = true;
    await changeInviteInfo(model);
    await box?.write(IS_BIND_RECOMMENDER, true);
  }

  bool isBindRecommender() {
    var inviteInfo = readInviteInfo();
    var bool = inviteInfo?.isBindRecommender ??
        box?.read(IS_BIND_RECOMMENDER) ??
        false;
    return bool;
  }

  InviteInfoModel? readInviteInfo() {
    var read = box?.read(INVITE_INFO);
    if (read != null) {
      InviteInfoModel inviteInfoModel = InviteInfoModel.fromJsonStr(read);
      return inviteInfoModel;
    }
    return null;
  }

  Future<void> saveInviteCode(String? inviteCode) async {
    var model = readInviteInfo() ?? InviteInfoModel();
    model.inviteCode = inviteCode;
    await changeInviteInfo(model);
  }

  String readInviteCode(){
    var model = readInviteInfo();
    return model?.inviteCode??"";
  }

  Future<void> changeInviteInfo(InviteInfoModel? inviteModel) async {
    String? jsonStr;
    jsonStr = inviteModel?.toJsonStr();
    return await box?.write(INVITE_INFO, jsonStr);
  }

  inviteInfoFromOldData() async {
    InviteInfoModel? infoModel = readInviteInfo();
    if (infoModel == null) {
      var model = InviteInfoModel(
        isBindRecommender: isBindRecommender(),
        needPopInviteDialog: readNeedPopInviteDialog(),
      );
      await changeInviteInfo(model);
    }
  }

  Future<void> saveInvitePosterPath(String? path) async {
    var model = readInviteInfo() ?? InviteInfoModel();
    model.invitePosterPath = path;
    await changeInviteInfo(model);
  }

  String readInvitePosterPath(){
    var model = readInviteInfo();
    return model?.invitePosterPath??"";
  }

  Future<void> savePushTokenTime(int? time) async {
    return await box?.write(PUSH_TOKEN_TIME, time);
  }

  int readPushTokenTime(){
    var time = box?.read(PUSH_TOKEN_TIME)??0;
    return time;
  }

  Future<void> saveProbeTime(int? time) async {
    return await box?.write(PROBE_TIME, time);
  }

  int? readProbeTime() {
    return box?.read(PROBE_TIME);
  }

  int? readMultiLanguage() {
    return box?.read(MULTI_LANGUAGE);
  }

  Future<void> saveMultiLanguage(int language) async {
    return await box?.write(MULTI_LANGUAGE, language);
  }

  Future<void> saveMiningTaskType(String key) async {
    return await box?.write('link_key_$key', true);
  }

  bool readMiningTaskTypeState(String key) {
    return box?.read('link_key_$key') ?? false;
  }

  String readMiningMiningInfo() {
    return box?.read(SUBMIT_MINING_INFO) ?? '';
  }

  Future<void> saveMiningMiningInfo(String address) async {
    return await box?.write(SUBMIT_MINING_INFO, address);
  }

  Map<String, dynamic> readBrowserClickCount() {
    return box?.read(SUBMIT_BROWSER_CLICK) ?? {};
  }

  Future<void> saveBrowserClickCount(Map address) async {
    return await box?.write(SUBMIT_BROWSER_CLICK, address);
  }
  String readBrowserClickTime() {
    return box?.read(SUBMIT_BROWSER_CLICK_TIME) ?? '';
  }

  Future<void> saveBrowserClickTime(String time) async {
    return await box?.write(SUBMIT_BROWSER_CLICK_TIME, time);
  }
  String readMiningMiningTime() {
    return box?.read(SUBMIT_MINING_TIME) ?? '';
  }

  Future<void> saveMiningMiningTime(String time) async {
    return await box?.write(SUBMIT_MINING_TIME, time);
  }

  String readIceTransportPolicy() {
    return box?.read(ICE_TRANSPORT_POLICY) ?? '';
  }

  Future<void> saveIceTransportPolicy(String? time) async {
    return await box?.write(ICE_TRANSPORT_POLICY, time);
  }

  Future<void> saveUserNameWithoutDomain(String userNameWithoutDomain) async {
    return await box?.write(USER_NAME_WITHOUT_DOMAIN, userNameWithoutDomain);
  }

  String? readUserNameWithoutDomain() {
    return box?.read(USER_NAME_WITHOUT_DOMAIN);
  }

  Future<void> saveOfficialMsgTimeRecent(double time) async {
    return await box?.write(OFFICIAL_MSG_TIME_RECENT, time);
  }

  double? readOfficialMsgTimeRecent() {
    return box?.read(OFFICIAL_MSG_TIME_RECENT);
  }

  bool readReUploadAvartar() {
    return box?.read(RE_UPLOAD_AVARTAR) ?? false;
  }
  Future<void> saveReUploadAvartar(bool uploaded) async {
    return await box?.write(RE_UPLOAD_AVARTAR, uploaded);
  }
  bool readReUploadChannelAvartar(String key) {
    return box?.read('avartar_$key') ?? false;
  }
  Future<void> saveReUploadChannelAvartar(String key,bool uploaded) async {
    return await box?.write('avartar_$key', uploaded);
  }

  String? readProxyHost() {
    return box?.read(PROXY_IP);
  }

  Future<void> saveProxyHost(String? ip) async {
    return await box?.write(PROXY_IP, ip);
  }

  String? readProxyPort() {
    return box?.read(PROXY_PORT);
  }

  Future<void> saveProxyPort(String? port) async {
    return await box?.write(PROXY_PORT, port);
  }

  PayIdentifyType readPayIdentifyType() {
    int read = box?.read(PAY_IDENTIFY_TYPE)??0;
    return PayIdentifyType.values[read];
  }

  savePayIdentifyType(PayIdentifyType type) async {
    return await box?.write(PAY_IDENTIFY_TYPE, type.index);
  }

  saveRecommenderUserNameWithDomain(String u) async {
    var model = readInviteInfo() ?? InviteInfoModel();
    model.recommenderUserNameWithDomain = u;
    await changeInviteInfo(model);
  }

  String readRecommenderUserNameWithDomain(){
    var model = readInviteInfo();
    return model?.recommenderUserNameWithDomain??"";
  }

  saveNeedSubmitSharePosterNode(bool need) async {
    return await box?.write(NEED_SUBMIT_SHARE_POSTER_NODE, need);
  }
  bool? readNeedSubmitSharePosterNode(){
    return box?.read(NEED_SUBMIT_SHARE_POSTER_NODE);
  }

  saveSqliteIndex(bool need) async {
    return await box?.write(SQLITE_INDEX, need);
  }

  bool readSqliteIndex(){
    return box?.read(SQLITE_INDEX)??false;
  }

  saveSqliteUpdateGroupInfo(bool need) async {
    return await box?.write(SQL_GROUP_INFO, need);
  }

  bool readSqliteUpdateGroupInfo(){
    return box?.read(SQL_GROUP_INFO)??false;
  }
  saveChannelInfoOptionsCount(int? count,String channelId) async {
    return await box?.write("$channelId$_channelInfoOptionsCountSuffix", count);
  }

  int? readChannelInfoOptionsCount(String channelId){
    return box?.read("$channelId$_channelInfoOptionsCountSuffix");
  }

  bool readEmojiUpdate() {
    return box?.read(EMOJI_UPDATE)??false;
  }

  saveEmojiUpdate(bool update) async {
    return await box?.write(EMOJI_UPDATE, update);
  }

  saveAdsInfo(String? ads) async {
    return await box?.write(ADS_INFO, ads);
  }

  String? readAdsInfo() {
    return box?.read(ADS_INFO);
  }
  saveAdsTime(String? ads) async {
    return await box?.write(ADS_INFO_TIME, ads);
  }

  String? readAdsTime() {
    return box?.read(ADS_INFO_TIME);
  }

  int? readLoginFirstTime() {
    return box?.read(LOGIN_FIRST_TIME);
  }

  saveLoginFirstTime(int time) async {
    return await box?.write(LOGIN_FIRST_TIME, time);
  }

  saveCurrentProxy(String address) async {
    return await box?.write(CURRENT_PROXY_ADDRESS, address);
  }

  String? readCurrentProxy() {
    return box?.read(CURRENT_PROXY_ADDRESS);
  }

  saveUnavailableProxy(Map<String,dynamic>? address) async {
    return await box?.write(UNVAILABLE_PROXY_ADDRESS, address);
  }

  Map<String,dynamic>? readUnavailableProxy() {
    return box?.read(UNVAILABLE_PROXY_ADDRESS);
  }

  saveChatMiningVerification(bool open) async {
    return await box?.write(CHAT_MINING_VERIFICATION, open);
  }

  bool? readChatMiningVerification() {
    return box?.read(CHAT_MINING_VERIFICATION);
  }
  saveMiningValidityPeriod(int? time) async {
    return await box?.write(MINING_VALIDITY_PERIOD, time);
  }

  int? readMiningValidityPeriod() {
    return box?.read(MINING_VALIDITY_PERIOD);
  }

  saveAuthenticationInterval(int? time) async {
    return await box?.write(AUTHENTICATION_INTERVAL, time);
  }

  int? readAuthenticationInterval() {
    return box?.read(AUTHENTICATION_INTERVAL);
  }

  saveAuthenticationUsedSate(bool? used) async {
    return await box?.write(AUTHENTICATION_IS_USED, used);
  }

  bool? readAuthenticationUsedSate() {
    return box?.read(AUTHENTICATION_IS_USED);
  }
  saveSwitchNodeInfo(Map<String,dynamic>? data) async {
    return await box?.write(SWITCH_NODE_INFO, data);
  }

  saveMessageTask(String value,bool isSingle) async {
    return await box?.write('${isSingle ? 'single':'channel'}_$MESSAGE_TASK', value);
  }

  String ? readMessageTask(bool isSingle) {
    return box?.read('${isSingle ? 'single':'channel'}_$MESSAGE_TASK');
  }

  Map<String,dynamic>? readSwitchNodeInfo() {
    return box?.read(SWITCH_NODE_INFO);
  }

  saveMainWallet(String address) async {
    return await box?.write(AMIN_WALLET, address);
  }

  String? readMainWallet() {
    return box?.read(AMIN_WALLET);
  }


  bool? readFirstWalletUpdate() {
    return box?.read(FIRST_UPDATE_WALLET);
  }

  saveFirstWalletUpdate(bool update) async {
    return await box?.write(FIRST_UPDATE_WALLET, update);
  }


  bool readAiSession() {
    return box?.read(AI_SESSION) ?? false;
  }

  saveAiSession(bool update) async {
    return await box?.write(AI_SESSION, update);
  }

  bool readSubmitUserinfo() {
    return box?.read(IS_SUBMIT_USER_INFO) ?? false;
  }

  saveSubmitUserinfo(bool update) async {
    return await box?.write(IS_SUBMIT_USER_INFO, update);
  }

}
