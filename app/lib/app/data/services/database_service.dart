/*
 * <AUTHOR> <PERSON><PERSON>
 * @Date         : 2022-04-21 14:01:16
 * @Description  : 数据库服务
 * @LastEditors  : <PERSON><PERSON>
 * @LastEditTime : 2022-04-25 15:21:42
 * @FilePath     : \flutter_metatel\lib\app\data\services\database_service.dart
 */

import 'dart:io';

import 'package:flutter_metatel/core/task/private_task.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:flutter_metatel/core/utils/util.dart';
import 'package:get/get.dart';
import 'package:path/path.dart' as path;

import '../../../core/languages/l.dart';
import '../../../core/task/quene_task.dart';
import '../../../core/values/config.dart';
import '../enums/path.dart';
import '../providers/db/database.dart';
import 'config_service.dart';

const String _dbDefaultPassword = 'default.metatel.com.cn';

class DatabaseService extends GetxService {
  bool dbIsConnected = false;
  bool get isDBConnect => dbIsConnected;

  Future<DatabaseService> init() async {
    // _loadSqlcipher();
    return this;
  }

  Future<String?> _getPrivateKey() async {
    AppLogger.d('database getPrivateKey loop');
    return await Get.find<AppConfigService>().getPrivateKey();
  }
  final TaskQueue _taskQueue = TaskQueue();

  Future<void> dbConnect() async {
    return await _taskQueue.submit(_dbConnected,null);
  }
  Future<void> _dbConnected(dynamic d) async {
    AppLogger.d('_dbConnected isDBConnect =$isDBConnect');
    if (isDBConnect) {
      return;
    }
    AppLogger.d('_dbConnected isDBConnect 2=$isDBConnect');
    var conf = Get.find<AppConfigService>();
    await PrivateConfigTask().configPrivateKeyFromMnemonic();
    /// 先获取username，再获取privateKey。因为在获取username时，privateKey为空则会赋值
    var username = conf.getUserNameWithoutDomain();
    var dbName = dataMD5(username);
    AppLogger.d('database getPrivateKey');
    var privateKey = await conf.getPrivateKey();
    if (privateKey.isEmpty) {
      int count=0;
      while (count <= 25 && privateKey.isEmpty) {
        await Future.delayed(const Duration(milliseconds: 500));
        privateKey = await _getPrivateKey() ?? '';
        count = count++;
        AppLogger.e('database pwd is null ...');

      }
    }

    var dbKey = dataMD5(privateKey);
    if (dbKey?.isNotEmpty != true) {
      dbKey = _dbDefaultPassword;
      AppLogger.e('database pwd id norm ...');
    }

    late AppDatabase database;
    if (Platform.isIOS) {
      database = await _tempInitDB();
    } else {
      database = await _initDB(dbName: dbName, dbKey: dbKey);
    }
    Get.put(database, permanent: true);
    // _dbConnect = true;
    if(dbIsConnected){
      AppLogger.d('database service start');
      var created = conf.readSqliteIndex();
      if(!created){
        database.createIndex().then((value) {
          conf.saveSqliteIndex(true);
          database.clearAllChannelMsgDataMoreThen1000();
          AppLogger.d('database service end');
        });
      }
      updateGroupInfoPath(conf,database);
      // await database.vacuum();
      updateAvatars(database);
      database.updateMessageStateForError();
      database.deleteSessionWhereChatTypeIsNull();

    }
  }

  //清洗下载下来的头像内容错误的文件
  void updateAvatars( AppDatabase database)async{
    var sessions = await database.allSessionAvatarNotEmpty().get();
    List<String> users = [];
    for (var s in sessions) {
      var path = appSupporAbsolutePath(s.avatarPath)??'';
      if(path.isNotEmpty && !path.startsWith('assets/images/')){
        AppLogger.d('updateAvatars s=$path');
        var size = File(path).lengthSync();
        AppLogger.d('updateAvatars s=$path size=${size}');
        if(size==54){
          users.add(s.username);
        }
      }
    }
    if(users.isNotEmpty){
      await database.updateSessionSetAvatarEmpty(users);
      users.clear();
    }
    var channelInfos = await database.allChannelInfoDatasAvatarNotEmpty().get();
    for (var s in channelInfos) {
      var path = appSupporAbsolutePath(s.avatarPath)??'';
      if(path.isNotEmpty && !path.startsWith('assets/images/')){
        AppLogger.d('updateAvatars channelInfos s=$path');
        var size = File(path).lengthSync();
        AppLogger.d('updateAvatars channelInfos s=$path size=${size}');
        if(size==54){
          users.add(s.channelId);
        }
      }
    }
    if(users.isNotEmpty){
      await database.updateChannelInfoSetAvatarEmpty(users);
      users.clear();
    }
    var contacts = await database.allContactAvatarNotEmpty().get();
    for (var s in contacts) {
      var path = appSupporAbsolutePath(s.avatarPath)??'';
      if(path.isNotEmpty && !path.startsWith('assets/images/')){
        AppLogger.d('updateAvatars contacts s=$path');
        var size = File(path).lengthSync();
        AppLogger.d('updateAvatars contacts s=$path size=${size}');
        if(size==54){
          users.add(s.username);
        }
        if(users.length>=50){
          await database.updateContactSetAvatarEmpty(users);
          users.clear();
        }
      }
    }
    if(users.isNotEmpty){
      await database.updateContactSetAvatarEmpty(users);
      users.clear();
    }
  }

  Future<AppDatabase> _initDB({String? dbName, String? dbKey}) async {
    if (appSupportDir == null) {
      await initConfigPath();
    }

    if (dbName?.isNotEmpty != true) {
      dbName = 'metatel';
    }

    var cipherDBPath = path.join(appSupportDir!.path, '$dbName.sqlite');

    /// 之前版本使用的未加密数据库路径
    var normalDBPath = path.join(appSupportDir!.path, 'db.sqlite');
    var normalDBFile = File(normalDBPath);

    late AppDatabase database;

    if (normalDBFile.existsSync()) {
      var normalDatabase = AppDatabase(normalDBFile.path);

      /// 数据转移到加密库中
      try {
        var cipherDatabase = AppDatabase(cipherDBPath, dbKey: dbKey);

        var oldContactDatas = await normalDatabase.allContactDatas().get();
        var oldGroupInfoDatas = await normalDatabase.allGroupInfoDatas().get();
        var oldGroupMemberDatas =
        await normalDatabase.allGroupMemberDatas().get();
        var oldLogDatas = await normalDatabase.allLogDatas().get();
        var oldMessageDatas = await normalDatabase.allMessageDatas().get();
        var oldSessionDatas = await normalDatabase.allSessionDatas().get();
        var oldChannelInfoDatas =
        await normalDatabase.allChannelInfoDatas().get();
        var oldProxyInfoDatas = await normalDatabase.allProxyInfoDatas().get();

        if (oldContactDatas.isNotEmpty) {
          await cipherDatabase.insterContactDatas(oldContactDatas);
        }

        if (oldGroupInfoDatas.isNotEmpty) {
          await cipherDatabase.insterGroupInfoDatas(oldGroupInfoDatas);
        }

        if (oldGroupMemberDatas.isNotEmpty) {
          await cipherDatabase.insterGroupMemberDatas(oldGroupMemberDatas);
        }

        if (oldLogDatas.isNotEmpty) {
          await cipherDatabase.insterLogDatas(oldLogDatas);
        }

        if (oldMessageDatas.isNotEmpty) {
          await cipherDatabase.insterMessageDatas(oldMessageDatas);
        }

        if (oldSessionDatas.isNotEmpty) {
          await cipherDatabase.insterSessionDatas(oldSessionDatas);
        }

        if (oldChannelInfoDatas.isNotEmpty) {
          await cipherDatabase.insterChannelInfoDatas(oldChannelInfoDatas);
        }

        if (oldProxyInfoDatas.isNotEmpty) {
          await cipherDatabase.insterProxyInfoDatas(oldProxyInfoDatas);
        }

        /// 删除未加密的数据库文件
        await normalDatabase.close();
        normalDBFile.deleteSync();

        database = cipherDatabase;
      } catch (e, s) {
        AppLogger.e('$e');
        AppLogger.e('$s');
        database = normalDatabase;
      }
    } else {
      database = AppDatabase(cipherDBPath, dbKey: dbKey);
    }

    /// 判断数据库是否连接成功
    try {
      await database.dbConnect().getSingleOrNull();
      dbIsConnected=true;
    } catch (e, s) {
      AppLogger.e('e1=$e');
      AppLogger.e('s1=$s');
      await Future.delayed(const Duration(milliseconds: 500), () async {
        try {
          await database.dbConnect().getSingleOrNull();
        } catch (e, s) {
          AppLogger.e('e2=$e');
          AppLogger.e('s2=$s');
          toast(L.db_open_fail.tr);
          dbIsConnected=false;
          await Future.delayed(const Duration(milliseconds: 1500), () {
            // exit(0);
          });
        }
      });
    }

    return database;
  }

  // void _loadSqlcipher() {
  //   open
  //     // ios、macOS无需额外设置，只需依赖sqlcipher_flutter_libs
  //     ..overrideFor(OperatingSystem.android, openCipherOnAndroid)
  //     // Windows、Linux需要手动包含dll
  //     ..overrideFor(
  //         OperatingSystem.linux, () => DynamicLibrary.open('libsqlcipher.so'))
  //     ..overrideFor(
  //         OperatingSystem.windows, () => DynamicLibrary.open('sqlcipher.dll'));
  // }

  Future<AppDatabase> _tempInitDB() async {
    if (appSupportDir == null) {
      await initConfigPath();
    }

    var normalDBPath = path.join(appSupportDir!.path, 'db.sqlite');
    AppDatabase database = AppDatabase(normalDBPath);

    /// 判断数据库是否连接成功
    try {
      await database.dbConnect().getSingleOrNull();
      dbIsConnected=true;
    } catch (e, s) {
      AppLogger.e('$e');
      AppLogger.e('$s');
      toast(L.db_open_fail.tr);
      dbIsConnected=false;
      await Future.delayed(const Duration(milliseconds: 1500), () {
        // exit(0);
      });
    }

    return database;
  }

  updateGroupInfoPath(AppConfigService conf,AppDatabase database) async{
    var updated = conf.readSqliteUpdateGroupInfo();
    if(!updated){
      var groupInfoDatas = await database.allGroupInfoDatas().get();
      if(groupInfoDatas.isNotEmpty){
        for(var g in groupInfoDatas){
          var join =  path.join(
              appSupportDir!.path, Config.RESOURCE_LOCAL_GROUP);
          AppLogger.d('updateGroupInfoPath join =$join');
          Directory(join).createSync(recursive: true);
          var old = path.join(
              appSupportDir!.path, g.groupId??'');
          var newPath = path.join(join, g.groupId??'');
          if(File(old).existsSync()&&!File(newPath).existsSync()){
            File(old).copySync(newPath);
            File(old).delete();
            AppLogger.d('updateGroupInfoPath old =$old');
          }
        }
      }
      conf.saveSqliteUpdateGroupInfo(true);
    }

  }
}
