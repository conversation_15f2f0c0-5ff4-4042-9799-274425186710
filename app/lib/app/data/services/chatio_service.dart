/*
 * <AUTHOR> <PERSON><PERSON>
 * @Date         : 2022-04-21 10:51:47
 * @Description  : 认证相关,需要有token才可以进入页面
 * @LastEditors  : Daen
 * @LastEditTime : 2022-08-22 12:18:40
 * @FilePath     : /flutter_metatel/lib/app/data/services/chatio_service.dart
 */
import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';
import 'package:dio/dio.dart' as d;
import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/data/events/events.dart';
import 'package:flutter_metatel/app/data/models/chatio_msg_model.dart';
import 'package:flutter_metatel/app/data/models/res/node_conf.dart';
import 'package:flutter_metatel/app/data/providers/api/21_plan.dart';
import 'package:flutter_metatel/app/data/providers/api/invite.dart';
import 'package:flutter_metatel/app/data/providers/api/operation_center_api.dart';
import 'package:flutter_metatel/app/data/providers/api/oss.dart';
import 'package:flutter_metatel/app/data/services/event_service.dart';
import 'package:flutter_metatel/app/data/services/secure_store_service.dart';
import 'package:flutter_metatel/app/data/services/sip_service.dart';
import 'package:flutter_metatel/app/modules/account/mnemonic/phone/phone_page.dart';
import 'package:flutter_metatel/app/modules/home/<USER>';
import 'package:flutter_metatel/app/modules/resentmessage/resent_message_page.dart';
import 'package:flutter_metatel/app/widgets/select_contact_page.dart';
import 'package:flutter_metatel/core/task/cmd_task.dart';
import 'package:flutter_metatel/core/task/contact_task.dart';
import 'package:flutter_metatel/core/task/private_task.dart';
import 'package:flutter_metatel/core/task/time_task.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:flutter_metatel/core/utils/comm_util.dart';
import 'package:flutter_metatel/core/utils/events_bus.dart';
import 'package:flutter_metatel/core/utils/proxy_util.dart';
import 'package:flutter_metatel/core/values/code.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:get/get.dart';
import 'package:video_thumbnail/video_thumbnail.dart';

import '../../../core/languages/l.dart';
import '../../../core/task/channel_option_task.dart';
import '../../../core/task/chat_task.dart';
import '../../../core/task/group_option_task.dart';
import '../../../core/task/message_task.dart';
import '../../../core/utils/crypto_util.dart';
import '../../../core/utils/util.dart';
import '../../../core/values/config.dart';
import '../../../webrtc/core/bean/call_model_model.dart';
import '../../modules/account/account_service.dart';
import '../../modules/dao/api/dao_api.dart';
import '../../modules/message/components/attachment_dialog.dart';
import '../../modules/realAuthenticate/real_name_authenticate_page.dart';
import '../../modules/virtualcard/api/api.dart';
import '../enums/enum.dart';
import '../enums/path.dart';
import '../models/base_model.dart';
import '../models/channel_card_model.dart';
import '../models/message_model.dart';
import '../models/mobile_verify_model.dart';
import '../models/real_name_verify_model.dart';
import '../models/res/auth_v3_get_token.dart';
import '../models/select_contact_model.dart';
import '../providers/api/api.dart';
import '../providers/api/channel.dart';
import '../providers/api/did.dart';
import '../providers/api/notice.dart';
import '../providers/api/own.dart';
import '../providers/api/staking_api.dart';
import '../providers/db/database.dart';
import '../providers/native/chatio/chatio/chatio_ffi.dart';
import '../providers/native/chatio/chatio_async.dart';
import 'config_service.dart';
import 'database_service.dart';
import 'network_connect_service.dart';
import 'push_service.dart';
import 'webrtc_service.dart';

class MsgDataSubjectEvent {
  final Object data;
  final String key;
  MsgDataSubjectEvent(this.data, this.key);
}

class ChatioService extends GetxService {
  static bool isLogined = false;
  late ChatioFFI ffi;
  bool isUnauthorizedCallBack = false;

  Future<ChatioService> init() async {
    ffi = Get.put(
      ChatioFFI(
        receiveMessageCallBack: receiveMessageCallBack,
        messageErrorCallBack: messageErrorCallBack,
        unauthorizedCallBack: unauthorizedCallBack,
        receiveMsgNumberCallBack: receiveMsgNumberCallBack,
      ),
      permanent: true,
    );

    // 定时获取未读消息
    Timer.periodic(const Duration(seconds: 60), (timer) {
      recvMessage();
    });

    var isRigeste = Get.find<AppConfigService>().getNotFirstLogin();
    AppLogger.d('initApiProvider isRigeste =$isRigeste');
    if (isRigeste) {
      AppLogger.d('initApiProvider1');
      await initApiProvider();
      AppLogger.d('initApiProvider2');

    }
    return this;
  }

  updateUnauthorized(bool state) {
    isUnauthorizedCallBack = state;
  }

  void unauthorizedCallBack() async {
    if (isUnauthorizedCallBack) {
      return;
    }
    updateUnauthorized(true);
    var authV3GetToken = await initToken(needUpdateNode: true);
    var token = authV3GetToken.token;
    if (token == null || token.isEmpty) {
      var error = authV3GetToken.error;
      AppLogger.e("unauthorizedCallBack! error =$error");
      if (error == "result==null") {
        Get.find<EventBus>().fire(UnauthorizedEvent(L.unauthorized.tr));
      }
    } else {
      await Future.delayed(const Duration(seconds: 2));
    }
    updateUnauthorized(false);
  }

  Future<void> initApiProvider() async{
    Config.userAgent = await getUserAgent();
    Get.put(ApiProvider(), permanent: true);
    Get.put(InviteApi(), permanent: true);
    Get.put(DaoApiProvider(), permanent: true);
    Get.put(StakingApi(), permanent: true);
    Get.put(VirtualCardApiProvider(), permanent: true);
    Get.put(DidApi(),permanent: true);
    Get.put(Plan21Api(),permanent: true);

    // ProxyUtil.instance.setProxy();
  }
  initConnection() async {
    if (Get.find<AppConfigService>().firstMySelfInfoNeedUpload()) {
      oneSubmitOwnInfo(isShowLoading: false);
    }
    await Future.delayed(const Duration(seconds: 1));
    await PrivateConfigTask().configPrivateKeyFromMnemonic();
    await updateDomain();
    bool login = await connection();
    AppLogger.d('initConnection =$login');
    if (!await Get.find<NetWorkConnectService>().networkConnected()) {
      //toast(L.network_unavailable.tr);
      return;
    }
    AppConfigService configService = Get.find();
    var token = configService.getToken();
    await initPush(token);
    //}
  }

  updateDomain() async{
    var domain = Config.getDomain();
    if(domain.isEmpty){
      if(Config.userNameWithoutDomain.isNotEmpty) {
        var myself = await geDomainInfo(Config.userNameWithoutDomain);
        AppLogger.d('updateDomain myself=$myself');
        if(myself?.name?.isNotEmpty??false){
          var arrys = myself?.name?.split('@');
          if(arrys?.length==2){
            await Get.find<AppConfigService>().saveDomain(arrys?.last);
          }
        }
      }
    }
  }

  saveDomain(String works,String domain) async{
    var userName= await PrivateConfigTask.fromMnemonicToUser(works);
    if(userName==null){
      return;
    }
    var myself = await geDomainInfo(userName);
    AppLogger.d('updateDomain myself=$myself');
    if(myself?.name?.isNotEmpty??false){
      var arrys = myself?.name?.split('@');
      if(arrys?.length==2){
        Get.find<AppConfigService>().saveDomain(arrys?.last);//优先使用服务器上的
        return;
      }
    }
    Get.find<AppConfigService>().saveDomain(domain);
  }

  void recvMessage() {
    if(isUnauthorizedCallBack){
      return;
    }
    ffi.recvMessage();
  }

  initPush(String token) async {
    await getServiceApi();
    String name = await ffi.getDeviceName();
    Get.find<SipService>().connect(name, token);
    Get.find<EventBus>().fire(SyncChannelsEvent());
  }

  Future<bool> connection() async {
    var dataPath = '${appSupportDir!.path}/MetaTel';
    var file = Directory(dataPath);
    bool exists = file.existsSync();
    if (!exists) {
      file.createSync(recursive: true);
    }
    AppConfigService configService = Get.find();
    AppLogger.d('connection getPrivateKey');
    var privateKey = await configService.getPrivateKey();
    var token = configService.getToken();
    await Get.find<DatabaseService>().dbConnect();
    isLogined = await Get.find<ChatioFFI>().connect(privateKey, token, dataPath, Config.getConnection(),Config.getDomain());
    if (isLogined) {
      Get.find<EventBus>().fire(UnauthorizedEvent(""));
    }
    AppLogger.d('connection isLogined=$isLogined');
    return isLogined;
  }

  Future<void> getServiceApi() async {
    Get.find<PushService>().pushToken();
    AppConfigService configS = Get.find();
    var provider = Get.find<ApiProvider>();

    provider.getOssToken().then((response) {
      if (response.statusCode==200) {
        configS.saveOssInfo(response.data?.data?.toJson());
      }
    });

    provider.getStsInfo().then((response) {
      if (response.data?.code == Code.code200) {
        configS.saveStsInfo(response.data?.stsData?.toJson());
      }
    });
  }
}

void receiveMessageCallBack(dynamic data, {bool isSync = false,bool saveAdminTop=false,double? updateTime,}) {
  var chatioMsgModel = data as ChatioMessageModel;
  var from = chatioMsgModel.from??'';
  var message = chatioMsgModel.message??'';
  var id = chatioMsgModel.id??'';
  if (from.isEmpty ||
      message.isEmpty ||
      id.isEmpty) {
    return;
  }

  Map<String, dynamic> mapData = json.decode(message);
  var msgModel = BaseModel.fromJson(mapData);

  if (msgModel.type != null) {
    AppLogger.d('getMessage type=${msgModel.type}');
    AppLogger.d('getMessage message=$message}');
    AppLogger.d('getMessage from=$from');
    var userName = Get.find<AppConfigService>().getUserName();
    var ccp = false;
    if(from==userName&&(msgModel.owner!=userName)){
      AppLogger.d('getMessage ccp from PC');
      from=msgModel.owner??'';
      ccp = true;
    }


    switch (MessageType.values[msgModel.type??-1]) {
      case MessageType.text:
      case MessageType.image:
      case MessageType.audio:
      case MessageType.video:
      case MessageType.file:
      case MessageType.tip:
      case MessageType.hongbao:
      case MessageType.sticker:
      case MessageType.stickerDefault:
      case MessageType.msgMergeForward:
      case MessageType.stickerDefaultRabbit:
      case MessageType.stickerDefaultEmoji:
      case MessageType.moneyExchange:
      case MessageType.walletTransaction:
      case MessageType.walletBill:
      case MessageType.meeting:
      // case MessageType.daoPoll:
        var msgTask = TextMessageTask();
        msgTask.injectParam(chatioMsgModel.from!, mapData, chatioMsgModel.id!,saveAdminTop,updateTime,ccp,
            isSync: isSync);
        break;
      case MessageType.walletAddress:
        var msgTask = WalletAddressMessageTask();
        msgTask.injectParam(
            from, mapData, chatioMsgModel.id!,saveAdminTop,updateTime,ccp);
        break;
      case MessageType.other:
        CmdTask.injectParam(chatioMsgModel.from!, chatioMsgModel.message!);
        break;
      case MessageType.userinfo:
        ContactTask.reqResMsg(chatioMsgModel.from!, chatioMsgModel.message!);
        break;
      case MessageType.contactCard:
        var tsk = ContactCardTask();
        tsk.injectParam( chatioMsgModel.from!, mapData, chatioMsgModel.id!,saveAdminTop, updateTime,ccp);
        break;
      case MessageType.group_operation:
        var tsk = GroupOptionTask();
        tsk.injectParam(chatioMsgModel.from!, mapData, chatioMsgModel.id!,saveAdminTop,updateTime ,ccp);
        break;
      case MessageType.call:
        CallModel callModel =
        CallModel.fromJson(json.decode(chatioMsgModel.message!));
        Get.find<WebRtcService>()
            .iWebRtcCall
            ?.onReceiverCmd(callModel, from);
        break;
      case MessageType.channelOpera:
        var msgTask = ChannelOptionTask();
        msgTask.injectParam(
            chatioMsgModel.from!, mapData, chatioMsgModel.id!,saveAdminTop,updateTime,ccp);
        break;
      case MessageType.channelCard:
        var tsk = ChannelCardTask();
        tsk.injectParam(
            chatioMsgModel.from!, mapData, chatioMsgModel.id!,saveAdminTop, updateTime,ccp);
        break;
      case MessageType.fileHelperOpera:

        break;
      case MessageType.pcOperate://pc交付消息
        var tsk = PcOperate();
        tsk.injectParam(
            chatioMsgModel.from!, mapData, chatioMsgModel.id!,saveAdminTop, updateTime, ccp);
        break;
      case MessageType.none:
        break;
      case MessageType.fail:
        break;
      case MessageType.date:
      case MessageType.other2:
        break;
    }
  }
}

void messageErrorCallBack(dynamic data) {
  var model = data as ChatioErrorMessageModel;

  AppDatabase db = Get.find();
  db.oneMessageByUUID(model.id).getSingleOrNull().then((message) {
    if (message == null) {
      MessageModel msg = MessageModel(
          msgId: uuid(), owner: model.fromName, type: MessageType.fail.index);
      ChatTask.instance.saveToDb(msg, model.fromName,null, uuid: model.id, saveAdminTop: false);
      CmdTask.sendResend(model.fromName, [model.id], ChatType.singleChat);

    }
  });
}




void receiveMsgNumberCallBack(int number) {
  if (number <= 0) {
    Get.find<EventBus>()
        .fire(SessionNoticeEvent(SessionNoticeType.chatioMsg, true));
  }
}

///

MessageModel _info2model(
    MessageEvent info, {
      String? fileUrl,
      String? thumbnailUrl,
    }) {
  MessageModel messageModel = MessageModel(
    msgId: info.msgId,
    type: info.type.index,
    body: info.body,
    time: info.dateTime.millisecondsSinceEpoch,
    chatType: info.chatType.index,
    owner: info.owner,
    fileUrl: fileUrl??info.fileUrl,
    fileFragment: info.fileFragment,
    thumbnailUrl: thumbnailUrl??info.thumbnailUrl,
    thumbnailFragment: info.thumbnailFragment,
    fileName: info.fileName,
    fileSize: info.fileSize,
    selfDestruct: info.selfDestruct,
    at: info.at,
    replayMsg: info.replayMsg,
    resourceId: info.resourceUuid,
    duration: info.fileDuration,
    extension: info.extension,
    expand: info.expand,
  );
  switch (info.type) {
    case MessageType.audio:
      try {
        // 兼容之前语音时长放在ext1里
        if (info.fileDuration == null) {
          messageModel.duration =
          info.ext1 == null ? null : int.parse(info.ext1 ?? '');
        }
      } catch (e) {
        AppLogger.w(e.toString());
      }
      break;
    default:
      break;
  }
  return messageModel;
}

MessageModel data2model(MessageData data) {
  List<String>? at;
  String? atStr = data.at;
  if (atStr != null && atStr.isNotEmpty) {
    at = json.decode(atStr);
  }
  MessageModel messageModel = MessageModel(
    msgId: data.msgId,
    type: data.type,
    body: data.body,
    time: data.time,
    chatType: data.chatType,
    owner: data.owner,
    fileUrl: data.fileUrl,
    fileFragment: data.fileFragment,
    thumbnailUrl: data.thumbnailUrl,
    thumbnailFragment: data.thumbnailFragment,
    fileName: data.fileName,
    fileSize: data.fileSize,
    selfDestruct: data.selfDestruct,
    at: at,
    resourceId: data.resourceUuid,
    duration: data.fileDuration,
  );
  return messageModel;
}

void _eventFire(EventBus eventBus, MessageEvent event,{bool isSaveDb=true,}) {
  int? fileState;
  if(event.type == MessageType.image) {
    if(((event.filePath?.isEmpty ?? true) || !File(event.filePath!).existsSync()) && !isEmptyObj(event.fileUrl) && !isEmptyObj(event.fileFragment)) {
      fileState = -1;
    }
  }
  AppLogger.d("message _eventFire isSaveDb:$isSaveDb fileState=$fileState");
  if(isSaveDb) {
    _updateOrInsertToDatabase(event,fileState:fileState);
    eventBus.fire(event);
  }
}

Future<MessageModel?> _fileHandle(
    File? srcFile, MessageEvent info, EventBus eventBus,
    {bool isSaveDb = true}) async {
  MessageType type = info.type;
  bool needEncry = info.chatType==ChatType.singleChat || info.chatType==ChatType.groupChat;

  BubbleItemState? state = info.state;

  String? fileUrl;
  String? thumbnailUrl;

  do {
    // 不需要操作
    if (srcFile == null) {
      break;
    }

    // state = BubbleItemState.FAILED;
    String? tempPath, tempThumbnailPath, filePath;
    if (info.filePath == null) {
      filePath = await mtCopyFile(srcFile, appSupportDir).catchError((e) {
        state = BubbleItemState.FAILED;
        return null;
      });
      if (filePath == null || filePath.isEmpty) {
        break;
      }
      // if(type == MessageType.image&&File(filePath).lengthSync()>Config.minFileSize){
      //   File? thumbImg = await thumbnailImg(filePath);
      //   AppLogger.d('_fileHandle fileSize1=${info.fileSize}');
      //   if (thumbImg != null) {
      //     filePath=thumbImg.path;
      //     info.fileSize=File(filePath).lengthSync();
      //   }
      // }
      info.filePath = filePath;
      srcFile = File(filePath);
    }

    var needThumb = false;
    AppLogger.d('_fileHandle expand=${info.expand}');
    AppLogger.d('_fileHandle filePath=${info.filePath}');

    int srcFileSize = info.fileSize ??= srcFile.lengthSync();
    Rect imageWH = Rect.zero;
    bool needImageWh = true;
    double w = info.expand?.size?.width ?? 0;
    double h = info.expand?.size?.height ?? 0;

    if (info.expand?.size != null && w > 0 && h > 0) {
      needImageWh = false;
      AppLogger.d('_fileHandle w=$w h=$h');
      imageWH = Rect.fromLTRB(-w / 2, -h / 2, w / 2, h / 2);
      AppLogger.d('_fileHandle w=${imageWH.width} h=${imageWH.height}');
    }
    if (type == MessageType.image) {
      if (needImageWh) {
        imageWH = await getImageWH(
          image: Image.file(
            File(info.filePath!),
          ),
        );
        AppLogger.d('_fileHandle imageWH:${imageWH.width},${imageWH.width}');
        info.expand = MessageExpand(
          size: MessageExpandSize(
            width: imageWH.width,
            height: imageWH.height,
          ),
        );
      }

      if(srcFileSize > Config.minFileSize){
        needThumb = true;
      }
    }
    /// 缩略图处理
    if (type == MessageType.video||needThumb) {
      if(!isEmptyObj(info.thumbnailUrl)&&!isEmptyObj(info.thumbnailFragment)){
        AppLogger.d('_fileHandle thumbnailPath 不需要上传文件，有密钥跟下载地址');
      }else{
        if (!isCipherFile(info.thumbnailPath) || isEmptyObj(info.thumbnailPath)) {
          String? thumbnailPath;
          if (needThumb) {
            File? thumbImg = await thumbnailImg(srcFile.path,imageWH: imageWH);
            if (thumbImg != null) {
              thumbnailPath=thumbImg.path;
            }
          } else {
            thumbnailPath = await VideoThumbnail.thumbnailFile(
                video: srcFile.path,
                thumbnailPath: appSupportDir?.path,
                timeMs: 100);
            if (thumbnailPath == null) {
              break;
            }
            thumbnailPath = Uri.decodeComponent(thumbnailPath);
            if (needImageWh) {
              imageWH = await getImageWH(
                image: Image.file(
                  File(thumbnailPath),
                ),
              );
              info.expand = MessageExpand(
                size: MessageExpandSize(
                  width: imageWH.width,
                  height: imageWH.height,
                ),
              );
            }

            if(File(thumbnailPath).lengthSync()>Config.minFileSize){
              File? thumbImg = await compressThumbnailImg(thumbnailPath,imageWH: imageWH);
              if (thumbImg != null) {
                thumbnailPath=thumbImg.path;
              }
            }
          }
          String? thumbnailFragment ;
          if(needEncry) {//需要加密
            tempThumbnailPath = mtRandPath(appTempDir);
            thumbnailFragment =
            await mtEncryptFile(thumbnailPath, tempThumbnailPath);
          } else {//不需要加密
            tempThumbnailPath = thumbnailPath;
          }

          info.thumbnailPath = thumbnailPath;
          info.thumbnailFragment = thumbnailFragment;

        } else {
          tempThumbnailPath = info.thumbnailPath;
          if ((info.thumbnailPath?.isNotEmpty ?? false) && needImageWh) {
            imageWH = await getImageWH(
              image: Image.file(
                File(info.thumbnailPath!),
              ),
            );
            info.expand = MessageExpand(
              size: MessageExpandSize(
                width: imageWH.width,
                height: imageWH.height,
              ),
            );
          }
        }
        _eventFire(eventBus, info,isSaveDb:isSaveDb);
        if (tempThumbnailPath == null) {
          break;
        }
        thumbnailUrl = await OssProvider()
            .upload(tempThumbnailPath.split('/').last, tempThumbnailPath);
        if (thumbnailUrl.isEmpty) {
          state = BubbleItemState.FAILED;
          break;
        }
      }

    }

    if (!isEmptyObj(srcFile.path) &&
        info.type != MessageType.stickerDefault &&
        info.type != MessageType.stickerDefaultRabbit &&
        info.type != MessageType.stickerDefaultEmoji) {
      if(!isEmptyObj(info.fileFragment)&&!isEmptyObj(info.fileUrl)){
        AppLogger.d('_fileHandle filePath 不需要上传文件，有密钥跟下载地址');
        break;
      }
      if (!isCipherFile(srcFile.path) || isEmptyObj(info.fileFragment)) {
        if(needEncry) {//需要加密
          tempPath = mtRandPath(appTempDir); //=
          String? fileFragment = await mtEncryptFile(info.filePath, tempPath);
          info.fileFragment = fileFragment;
        } else {//不需要加密
          tempPath = info.filePath;
        }
      } else {
        tempPath = info.filePath;
      }

      _eventFire(eventBus, info,isSaveDb:isSaveDb);
      if (tempPath == null) {
        AppLogger.e('_fileHandle tempPath is null');
        break;
      }
        fileUrl = await OssProvider().upload(tempPath.split('/').last, tempPath);
      if (fileUrl.isEmpty) {
        state = BubbleItemState.FAILED;
        break;
      }
    }
    state = info.state;
  } while (false);
  info.fileUrl = fileUrl ?? info.fileUrl;
  info.thumbnailUrl = thumbnailUrl ?? info.thumbnailUrl;
  MessageModel messageModel =
      _info2model(info, fileUrl: fileUrl, thumbnailUrl: thumbnailUrl);
  info.state = state;

  _eventFire(eventBus, info,isSaveDb:isSaveDb);
  return messageModel;
}

void _updateOrInsertToDatabase(MessageEvent message,{int? fileState}) {
  // 自己发送的阅后即焚消息不入库
  if (message.direction == Direction.outGoing && message.selfDestruct) return;

  MessageModel messageModel = _info2model(
    message,
  );
  var noises =
      (message.noises?.isEmpty ?? true) ? null : json.encode(message.noises);
  AppConfigService s = Get.find();
  ChatTask.instance.saveToDb(
    messageModel,
    message.owner,
    null,
    filePath: appSupporAbsolutePathToPath(message.filePath),
    thumbnailPath: appSupporAbsolutePathToPath(message.thumbnailPath),
    direction: Direction.outGoing,
    state: bubbleStateToInt(message.state),
    fileState: fileState,
    undo: message.isUndo,
    uuid: message.uuid,
    fromname: s.getUserName(),
    send: false,
    ext1: message.ext1,
    noises: noises,
    // 语音消息的时长、打电话的状态
    undoEdit: message.undoEdit, saveAdminTop: false,
  );
  // 方向只管发送，接收由其他地方处理来通知我修改本地变量
}

Future<void> sendMessage(File? srcFile, MessageEvent info,

    {PushType pushType = PushType.none, bool isSaveDb=true,}) async {
  /// 敏感词检查
  if (sensitiveWordCheck(info)) {
    info.state = BubbleItemState.SENSITIVE;
    _eventFire(Get.find<EventBus>(), info,isSaveDb:isSaveDb);
    return;
  }
  // await ChatTask.instance.parseLinkMsg(info);
  EventBus eventBus = Get.find();
  if(isSaveDb){
    AppLogger.d("message sendMessage isSaveDb:$isSaveDb");
    // _updateOrInsertToDatabase(info);
  }
  MessageModel? model = await _fileHandle(srcFile, info, eventBus,isSaveDb:isSaveDb);
  String? msgId = model?.msgId;
  BubbleItemState state = info.state ?? BubbleItemState.LOAD;
  if(state ==BubbleItemState.unsent){
    _eventFire(eventBus, info,isSaveDb:isSaveDb);
    return;
  }
  if (msgId == null ) {
    return;
  }

  do {
    bool isFile = false;
    switch (info.type) {
      case MessageType.image:
      case MessageType.audio:
      case MessageType.video:
      case MessageType.file:
      case MessageType.msgMergeForward:
        isFile = true;
        break;
      default:
    }

    // 文件加密或上传失败
    String fileUrl = model?.fileUrl ?? '';
    if (isFile && fileUrl.isEmpty) {
      break;
    }
    state = BubbleItemState.SENT;

    ChatioFFI ffi = Get.find();
    if ((info.chatType == ChatType.singleChat) && (model?.displayName?.isEmpty ?? true)) {
      model?.displayName = Get.find<AppConfigService>().getMySelfDisplayName();
    }
    var text = jsonEncode(model?.toJson());
    AppLogger.d("send text: $text");

    String? result;
    switch (info.chatType) {
      case ChatType.singleChat:
        List<String> toNumbers = [info.owner];
        result =
        await ffi.sendMessage(toNumbers, text, pushType: pushType.index);
        break;
      case ChatType.groupChat:
        List<String> toNumbers = info.toNumbers ?? [info.owner];

        result =
        await ffi.sendMessage(toNumbers, text, pushType: pushType.index);
        break;
      case ChatType.channelChat:
        var push = getChannelPushType(model?.type);
        result =
        await sendMessageRequest(info.owner, text, ats: model?.at ?? [],push: push);
        break;
      case ChatType.officialChat:
        result =
        await sendNotice(info.owner, text, ats: model?.at ?? []);
        break;
      default:
    }

    if (result == null || result.isEmpty) {
      state = BubbleItemState.FAILED;
      AppLogger.d("sendMessage failed");
      if (info.chatType == ChatType.groupChat ||
          info.chatType == ChatType.singleChat) {
        _reset();
      }
      break;
    }
    AppLogger.d("send text result: $result");
    info.uuid = result;
    state = BubbleItemState.DELIVERED;
    sendMiningTask(info.chatType);
  } while (false);

  info.state = state;
  AppLogger.d("send text state: $state");

  _eventFire(eventBus, info,isSaveDb:isSaveDb);
  return;
}

String getChannelPushType(int? type) {
  var pushType = '';
  if (type == 0 ||
      type == 1 ||
      type == 2 ||
      type == 3 ||
      type == 4 ||
      type == 18 ||
      type == 23 ||
      type == 24 ||
      type == 25 ||
      type == 26 ||
      type == 28 ||
      type == 29) {
    pushType = 'push';
  }
  return pushType;
}

_reset() {
  if (!ChatioService.isLogined) {
    writeLogToCacheFile("initToken---NetWorkConnectService");
    AppLogger.d("initToken---NetWorkConnectService");
    try {
      Get.find<HomeController>().updateToken();
    } catch (e) {
      AppLogger.d(e.toString());
    }
  }
}

void sendMiningTask(ChatType type) {
  String? tag;
  switch (type) {
    case ChatType.singleChat:
      tag = MiningTaskType.MINING_TASK_SEND_MESSAGE;
      break;
    case ChatType.channelChat:
      tag = MiningTaskType.MINING_TASK_SEND_CHANNEL;
      break;
    case ChatType.groupChat:
      break;
    case ChatType.other:
      break;
    case ChatType.officialChat:
      break;
  }
  if (tag != null) {
    Get.find<EventBus>().fire(MiningTaskEvent(tag));
  }
}

void undoMessage(MessageEvent info) {
  CmdTask().sendRevoke(info.owner, [info.msgId], info.chatType).then((value) {
    if (value) {
      info.isUndo = true;
      info.ext1 = L.you_retracted_msg.tr;
      if (info.type == MessageType.text) {
        info.undoEdit = true;
      }
      EventBus eventBus = Get.find();
      _eventFire(eventBus, info);
    } else {
      toast(L.undo_fail.tr);
    }
  });
}

void resendMessage(MessageEvent info) {
  var uuid = info.uuid;
  if (uuid == null) {
    return;
  }
  CmdTask.sendResend(info.owner, [uuid], info.chatType);
}

MessageEvent? data2event(MessageData data,
    {String? replayFilePath, String? replayFileFragment, String? replayMsgId,bool? undoEdit}) {
  var owner = data.owner;
  var chatType = data.chatType;
  if (owner == null || chatType == null) {
    return null;
  }
  var expand = data.expand;
  var extension;
  MessageExpand? messageExpand;
  if (expand != null) {
    try {
      if(data.type == MessageType.hongbao.index) {
        extension = expand;
      } else  {
        var jsonDe = json.decode(expand);
        messageExpand = MessageExpand.fromJson(jsonDe);
      }

    } catch (e) {
      AppLogger.e('data ${e.toString()}');
    }
  }

  List<double>? noises ;
  if(data.noises?.isNotEmpty ?? false){
    AppLogger.d('data.noises =${data.noises}');
    var n = json.decode(data.noises!);
    if(n!=null&&(n is List)){
      noises= [];
      for (var s in n){
        noises.add(s);
      }
    }
  }
  var type = data.type ?? MessageType.none.index;
  if(type<0){
    type = MessageType.none.index;
  }
  MessageEvent event = MessageEvent(
    data.msgId,
    owner: owner,
    from: data.from,
    chatType: ChatType.values[chatType],
    body: data.body,
    type: MessageType.values[type],
    filePath: appSupporAbsolutePath(data.filePath),
    fileFragment: data.fileFragment,
    fileName: data.fileName,
    fileSize: data.fileSize,
    direction: data.direction ?? 0,
    state: intToBubbleState(data.state),
    thumbnailPath: appSupporAbsolutePath(data.thumbnailPath),
    thumbnailFragment: data.thumbnailFragment,
    isUndo: data.undo ?? false,
    dateTime: DateTime.fromMillisecondsSinceEpoch(data.time ?? 0),
    uuid: data.uuid,
    ext1: data.ext1,
    selfDestruct: data.selfDestruct ?? false,
    replayMsg: data.replayMsg,
    resourceUuid: data.resourceUuid,
    replayFilePath:
    appSupporAbsolutePath(appSupporAbsolutePathToPath(replayFilePath)),
    replayFileFragment: replayFileFragment,
    replayMsgId: replayMsgId,
    hasShown: data.hasShown,
    messageHasRead: data.messageHasRead,
    fileDuration: data.fileDuration,
    undoEdit:undoEdit?? data.undoEdit,
    fileUrl: data.fileUrl,
    thumbnailUrl: data.thumbnailUrl,
    fileState: data.fileState,
    expand: messageExpand,
    noises: noises,
    thumbnailFileState: data.thumbnailFileState,
    hasIdentify: data.hasIdentify,
    translateMsg: data.translateMsg,
    extension: extension
  );

  switch (event.type) {
    case MessageType.audio:
      try {
        // 兼容之前语音时长放在ext1里
        if (data.fileDuration == null) {
          event.fileDuration =
          data.ext1 == null ? null : int.parse(data.ext1 ?? '');
        }
      } catch (e) {
        AppLogger.e(e.toString());
      }
      break;
    default:
      break;
  }
  return event;
}

Future<bool> forwardMessage(List<MessageEvent> messages,
    {String? blockUsername,
      bool isSaveDb = true,
      String? successHint,
      List<ContactData>? cs,
      bool onlyContact=false,
    }) async {
  successHint??="";
  AppDatabase db = Get.find();
  if(cs==null){
    List<ContactData> value;
    if (blockUsername == null || blockUsername.isEmpty) {
      value = await db.allFriendContact().get();
    } else {
      value = await db.allContactWithoutUserName(blockUsername).get();
    }
    cs=value;
  }

  // db.allFriendContact().get().then((value) async {
  var data = await showDialog(
      context: Get.context!,
      builder: (_) {
        return onlyContact?SelectContactPage(
          type: SelectContactType.multiple,
          contactDatas: cs,
          showGroup: false,
        ):ResentMessagePage(
          contactDatas: cs,
          showGroup: true,
        );
      });
  if (data == null) {
    return false;
  }
  // var data = await Get.to();
  if (data.runtimeType == SelectContactInfo) {
    var contactList = data as SelectContactInfo;
    if(contactList.contacts.isEmpty){
      return false;
    }
    var dateTime = TimeTask.instance.getNowTime();
    bool canSend = true;
    for(ContactInfo c in contactList.contacts){
      List<String>? toNumbers;
      if (c.chatType == ChatType.groupChat) {
        var userName = Get.find<AppConfigService>().getUserName();
        toNumbers = await db
            .oneGroupMemberExcludeUser(
            c.userName, userName!)
            .get();
      } else if(c.chatType == ChatType.channelChat) {
        AppLogger.d('forwardMessage message');
         var channelInfo = await db
            .oneChannelInfo(c.userName).get();
        AppLogger.d('forwardMessage message ${channelInfo.length}');

        if(channelInfo.isNotEmpty){
           var countNumber = channelInfo.first.memberCount ?? 0;
           AppLogger.d('forwardMessage message countNumber=$countNumber');
           if (countNumber >= Config.maxNumberCount) {
            canSend = false;
          }
        }
      }
      dateTime++;
      for (var element in messages) {
        dateTime++;
        if(element.type==MessageType.channelCard){
          ChannelCard? bean;
          try {
            bean = ChannelCard.fromJson(jsonDecode(element.body ?? "{}"));
            bean.action=ChannelCardAction.share;
            element.body=json.encode(bean);
          } catch (e) {
            bean = null;
          }
        }
        var tomsg = MessageEvent(uuid(),
            owner: c.userName,
            chatType: contactList.type,
            type: element.type,
            body: element.body,
            fileName: element.fileName,
            fileSize: element.fileSize,
            direction: 1,
            ext1: element.ext1,
            fileFragment: element.fileFragment,
            filePath: element.filePath,
            thumbnailFragment: element.thumbnailFragment,
            thumbnailPath: element.thumbnailPath,
            toNumbers: toNumbers,
            resourceUuid:element.resourceUuid ,
            thumbnailUrl: element.thumbnailUrl,
            fileUrl: element.fileUrl,
            expand: element.expand,
            thumbnailFileState: element.thumbnailFileState,
            hasIdentify:element.hasIdentify,
            dateTime: DateTime.fromMillisecondsSinceEpoch((dateTime)));
        AppLogger.d('forwardMessage message canSend=$canSend');
        if(!canSend){
          if (!CommUtil.instance.canSend(tomsg,isForword : true)) {
            toast(L.operation_restricted.tr);
            return false;
          }
        }
        File? srcFile;
        String? srcFilePath = element.filePath;
        if (srcFilePath != null) {
          srcFile = File(srcFilePath);
        }
        Get.find<EventBus>().fire(MessageForwardEvent(tomsg));
        sendMessage(srcFile, tomsg,isSaveDb:isSaveDb, pushType: PushType.message);
      }
    }
    if(successHint.isEmpty){
      toast(L.chat_has_forward.tr);
    }else{
      toast(successHint);
    }
    return true;
  }
  return false;
  // });
}

Future<bool> shareForwardMessageByMsgId(BuildContext context, String msgId,
    {String? blockUsername}) async {
  if(msgId.isEmpty){
    return false;
  }
  AppDatabase db = Get.find();
  var msg = await db.oneMessage(msgId).getSingleOrNull();
  if(msg!=null){
    var msgEvent = data2event(msg);
    if(msgEvent!=null){
      return await shareForwardMessage(context,[msgEvent]);
    }
  }
  return false;
}

Future<bool> shareForwardMessage(BuildContext context, List<MessageEvent> messages,
    {String? blockUsername}) async {
  AppDatabase db = Get.find();
  List<ContactData> value;
  if (blockUsername == null || blockUsername.isEmpty) {
    value = await db.allFriendContact().get();
  } else {
    value = await db.allContactWithoutUserName(blockUsername).get();
  }
  // db.allFriendContact().get().then((value) async {
  var data = await showDialog(
      context: Get.context!,
      builder: (_) {
        return ResentMessagePage(
          contactDatas: value,
          showGroup: true,
        );
      });
  if (data == null) {
    return false;
  }
  // var data = await Get.to();
  if (data.runtimeType == SelectContactInfo) {
    var contactList = data as SelectContactInfo;
    for (ContactInfo c in contactList.contacts) {
      List<String>? toNumbers;
      if (c.chatType == ChatType.groupChat) {
        var userName = Get.find<AppConfigService>().getUserName();
        toNumbers =
        await db.oneGroupMemberExcludeUser(c.userName, userName!).get();
      }
      for (var element in messages) {
        var tomsg = MessageEvent(uuid(),
            owner: c.userName,
            chatType: contactList.type,
            type: element.type,
            body: element.body,
            fileName: element.fileName,
            fileSize: element.fileSize,
            direction: 1,
            ext1: element.ext1,
            fileFragment: element.fileFragment,
            filePath: element.filePath,
            thumbnailFragment: element.thumbnailFragment,
            thumbnailPath: element.thumbnailPath,
            toNumbers: toNumbers,
            resourceUuid: element.resourceUuid,
            fileUrl: element.fileUrl,
            expand: element.expand,
            hasIdentify:element.hasIdentify,
            thumbnailUrl: element.thumbnailUrl,
            thumbnailFileState: element.thumbnailFileState,
            dateTime: TimeTask.instance.getNowDateTime());
        File? srcFile;
        String? srcFilePath = element.filePath;
        if (srcFilePath != null&&!srcFilePath.startsWith("asset")) {
          srcFile = File(srcFilePath);
        }
        Get.find<EventBus>().fire(MessageForwardEvent(tomsg));
        sendMessage(srcFile, tomsg, pushType: PushType.message);
      }
    }
    toast(L.chat_has_forward.tr);
    return true;
  }
  return false;
  // });
}

initUserInfoMex() async {

}
_updateNode() async {
  var response = await Get.find<OperationCenterApiProvider>().getNodeInfo();
  if (response?.isEmpty??true) {
     // toast('nodeInfo error}');
  } else {
    // toast('nodeInfo ${Config.deviceCode}}');
    await Get.find<AppConfigService>().saveNode(response?['node']);
    if (response?['port'] != null) {
      await Get.find<AppConfigService>().saveNodePort('${response?['port']}');
    }
  }
}

initParam({bool isRegisetered = false}) async{
  var token =  Get.find<AppConfigService>().getToken();
  if(token.isEmpty){
    AppLogger.d('token is null');
    return;
  }
  if(isRegisetered){
    await _updateNode();
  }

  d.Response<NodeConf> response;
  response = await Get.find<ApiProvider>().getNodeConf();
  if (response.data == null) {
    Future.delayed(const Duration(seconds: 2), () {
      Get.find<EventBus>()
          .fire(UnauthorizedEvent(L.service_connect_failed.tr));
    });
  }
  var userNameWithoutDomain = Get.find<AppConfigService>().getUserNameWithoutDomain();
  await Get.find<ChatioService>().initConnection();
  var inviteModel = Get.find<AppConfigService>().readInviteInfo();
  var needSubmitSharePosterNode =
      Get.find<AppConfigService>().readNeedSubmitSharePosterNode() ?? true;
  var inviteCodeIsEmpty = inviteModel?.inviteCode?.isEmpty ?? true;
  if (inviteCodeIsEmpty || needSubmitSharePosterNode) {
    Get.find<InviteApi>()
        .getSharePoster(userNameWithoutDomain, needDownLoad: inviteCodeIsEmpty);
  }

  ///主动处理绑定推荐人自动加好友发消息出错的问题
  if (inviteModel?.recommenderUserNameWithDomain?.isEmpty ?? true) {
    await Get.find<InviteApi>().inviteInfo(userNameWithoutDomain);
  }
  TimeTask.instance.freshTime();
}

Future<AuthV3GetToken> initToken(
    {String? registerCode, int? action, bool isRestore = false,bool autoRetry=true,bool needUpdateNode = false}) async {
  await getDeviceCode();
  if (!await Get.find<NetWorkConnectService>().networkConnected()) {
    //toast(L.network_unavailable.tr);
    // return AuthV3GetToken(error: L.network_unavailable.tr);
  }
  if(needUpdateNode){
      await _updateNode();
  }

  AppConfigService configService = Get.find();
  AppLogger.d('registerCode=$registerCode');
  await PrivateConfigTask().configPrivateKeyFromMnemonic();
  if (Config.privateKeyBase64.isEmpty) {
    Future.delayed(const Duration(seconds: 2), () {
      Get.find<EventBus>().fire(UnauthorizedEvent(L.service_connect_failed.tr));
    });
    return AuthV3GetToken();
  }
  var isOk = await Get.find<ApiProvider>().testNode();
  if(!isOk){
    await Get.find<OperationCenterApiProvider>().updateNodeForward();
  }
  AppLogger.d('initToken Config.nodePubUrl==${Config.nodePubUrl()}');
  var result = await ChatioNative.authV3GetToken(
      Config.nodePubUrl(), ///节点地址/pub
      registerCode ?? "", ///注册码 可以为“”
      await getDeviceCode(), ///设备唯一标识
      Config.privateKeyBase64, ///私钥base64
      action ?? 0); /// 顶替登陆 默认是0
  AppLogger.d('initToken authV3GetToken result0=$result');
  if (result == null) {
    if(autoRetry){
      await Future.delayed(
        const Duration(seconds: 2),
      );
      result = await ChatioNative.authV3GetToken(
          Config.nodePubUrl(),
          registerCode ?? "",
          await getDeviceCode(),
          Config.privateKeyBase64,
          action ?? 0);
      AppLogger.d('initToken authV3GetToken result1=$result');
      if (result == null) {
        return AuthV3GetToken(error: L.server_error_retry.tr);
      }
    }else{
      return AuthV3GetToken(error: L.server_error_retry.tr);
    }
  }
  AppLogger.d('initToken authV3GetToken result success=$result');
  Map<String, dynamic> resultJson = jsonDecode(result);
  var authV3GetToken = AuthV3GetToken.fromJson(resultJson);
  String? token = authV3GetToken.token;
  var ext = authV3GetToken.ext;
  AppLogger.d("ext==$ext");
  if (token != null && token.isNotEmpty) {
    Get.find<EventBus>().fire(UnauthorizedEvent(""));
    await configService.saveToken(token);
    await initParam();
    // }
  } else {
    AppLogger.d("initToken---not null $result");
    Get.find<EventBus>().fire(UnauthorizedEvent(L.unauthorized.tr));
    if (Get.context != null) {
      String? userName = configService.getUserName();
      String errorMsg;
      String? event;
      int errorCode;
      AppLogger.d("ext==$ext");
      if (ext == null) {
        errorMsg = L
            .the_account_is_illegal_and_forbidden_to_use_pleas_contact_the_administrator
            .trParams({'account': "\"$userName\""});
      } else {
        Map<String, dynamic> decode = json.decode(ext);
        errorMsg = decode["msg"] ?? decode["message"] ?? L.unknown.tr;
        errorCode = decode["code"] ?? -1;
        event = decode["event"];
        switch (errorCode) {
          case 403:
            Map<String, dynamic>? jsonData = decode["data"];
            if (jsonData != null) {
              if (jsonData.containsKey('type')) {
                String type = jsonData['type'];

                // 实名认证
                if (StrKey.realNameVerify == type) {
                  Get.find<EventBus>().fire(RealNameVerifyEvent(
                      RealNameVerifyModel.fromJson(jsonData)));
                  return AuthV3GetToken();
                }

                // 手机号验证
                if (StrKey.mobileVerify == type) {
                  Get.find<EventBus>().fire(
                      MobileVerifyEvent(MobileVerifyModel.fromJson(jsonData)));
                  return AuthV3GetToken();
                }
              }

              String? command = jsonData["command"];
              if (command != null && command == "destroy all") {
                await clearAppData();
                return AuthV3GetToken(error: "has been destroy");
              }
            } else {
              errorMsg = isRestore
                  ? L.authorization_code_exception.tr
                  : L.authorization_code_exception_1.tr;
            }
            break;
        }
      }
      Widget alert = AlertDialog(
        content: Text(errorMsg),
        actionsAlignment: MainAxisAlignment.center,
      );
      if (authV3GetToken.code == 401 && event == "need_active") {
        alert = AlertDialog(
          content: Text(L
              .your_account_is_already_logged_in_on_another_device_do_you_want_to_continue
              .tr),
          actions: [
            TextButton(
              child: Text(L.cancel.tr),
              onPressed: () => SmartDialog.dismiss(), //关闭对话框,
            ),
            TextButton(
              child: Text(L.continue_.tr),
              onPressed: () {
                SmartDialog.dismiss();
                if (isRestore) {
                  Get.find<EventBus>()
                      .fire(MnemonicRestoreReplaceContinueEvent());
                } else {
                  Get.find<EventBus>().fire(
                      SessionNoticeEvent(SessionNoticeType.reconnect, false));
                  initToken(action: 1).then((value) => Get.find<EventBus>()
                      .fire(SessionNoticeEvent(
                      SessionNoticeType.reconnect, true)));
                }
              }, //关闭对话框,
            ),
          ],
          actionsAlignment: MainAxisAlignment.end,
        );
      }
      dismissLoadingDialog();
      SmartDialog.show(
        builder: (c) {
          if (errorMsg == L.authorization_code_exception.tr) {
            configService.saveToken("");
          }
          return alert;
        },
        keepSingle: true,
        clickMaskDismiss: false,
        useAnimation: false,
        backDismiss: configService.getToken().isNotEmpty ||
            !configService.getNotFirstLogin(),
      );
    }
  }
  AppLogger.d('initToken authV3GetToken 2');

  return authV3GetToken;
}

///重新链接
void reConnect() async {
  Get.find<EventBus>()
      .fire(SessionNoticeEvent(SessionNoticeType.reconnect, false));
  var authV3GetToken = await initToken(needUpdateNode: true);
  Get.find<EventBus>()
      .fire(SessionNoticeEvent(SessionNoticeType.reconnect, true));

  String? token = authV3GetToken.token;
  if (token != null && token.isNotEmpty) {
    // Get.find<ChatioService>().connection();
  } else {
    toast(L.authorization_failed_please_try_again_late.tr,
        textColor: Colors.red);
  }
}

void realNameVerify(
    RealNameVerifyEvent event,
    CurrentRealNameType type, {
      String? mnemonic,
      ByteData? imageBytes,
      String? registerCode,
    }) async {
  String username =
      Get.find<AppConfigService>().getUserNameWithoutDomain();

  AppLogger.w('====ly realNameVerify username:$username');

  bool success = RealNameVerify.mRealNameSuccess[username] ?? false;
  RegisterStatus status =
      RealNameVerify.mRealNameSuccessStatus[username] ?? RegisterStatus.none;

  register(RegisterStatus status) {
    String? text;
    switch (status) {
      case RegisterStatus.none:
        text = L.trying_register.tr;
        RealNameVerify.mRealNameSuccessStatus[username] = RegisterStatus.trying;
        break;
      case RegisterStatus.trying:
        text = L.trying_wait.tr;
        RealNameVerify.mRealNameSuccessStatus[username] =
            RegisterStatus.registering;
        break;
      case RegisterStatus.registering:
        RealNameVerify.mRealNameSuccessStatus[username] = RegisterStatus.none;
        RealNameVerify.mRealNameSuccess[username] = false;
        toast(L.register_network_exception.tr);
        return;
    }

    showLoadingDialog( isBack: false, text: text);
    AppLogger.d("RegisterStatus ==$status");
    Future.delayed(Duration(seconds: Config.registerRetryWait), () async {
      if (type == CurrentRealNameType.recover ||
          type == CurrentRealNameType.register) {
        mnemonic ??=
        await Get.find<SecureStoreService>().secureStoreReadMnemonic();
        if (mnemonic == null) {
          toast("mnemonic is null!");
          return;
        }
        Get.find<AccountService>().login(
          mnemonic!,
          imageBytes: imageBytes,
          registerCode: registerCode,
        );
      } else if (type == CurrentRealNameType.login) {
        initToken().then((value) => dismissLoadingDialog());
      }
    });
  }

  if (success) {
    register(status);
  } else {
    var value = await Get.to(RealNameAuthenticatePage(
      data: event.data,
      waitSeconds: Config.registerRetryWait,
    ));

    if (StrKey.realNameSuccess == value) {
      RealNameVerify.mRealNameSuccess[username] = true;
      register(RegisterStatus.none);
    }
  }
}

void mobileVerifyRegister(
    MobileVerifyEvent event,
    CurrentRealNameType type, {
      String? mnemonic,
      String? registerCode,
    }) async {
  String username =
      Get.find<AppConfigService>().getUserNameWithoutDomain();
  AppLogger.w('====ly mobileVerifyRegister username:$username');

  bool success = MobileVerify.mMobileSuccess[username] ?? false;
  RegisterStatus status =
      MobileVerify.mMobileSuccessStatus[username] ?? RegisterStatus.none;

  register(RegisterStatus status) {
    String? text;
    switch (status) {
      case RegisterStatus.none:
        text = L.trying_register.tr;
        MobileVerify.mMobileSuccessStatus[username] = RegisterStatus.trying;
        break;
      case RegisterStatus.trying:
        text = L.trying_wait.tr;
        MobileVerify.mMobileSuccessStatus[username] =
            RegisterStatus.registering;
        break;
      case RegisterStatus.registering:
        MobileVerify.mMobileSuccessStatus[username] = RegisterStatus.none;
        MobileVerify.mMobileSuccess[username] = false;
        toast(L.register_network_exception.tr);
        return;
    }

    showLoadingDialog( isBack: false, text: text);
    AppLogger.d("RegisterStatus ==$status");
    Future.delayed(Duration(seconds: Config.registerRetryWait), () async {
      if (type == CurrentRealNameType.recover ||
          type == CurrentRealNameType.register) {
        mnemonic ??=
        await Get.find<SecureStoreService>().secureStoreReadMnemonic();
        if (mnemonic == null) {
          toast("mnemonic is null!");
          return;
        }
        Get.find<AccountService>().login(
          mnemonic!,
          registerCode: registerCode,
        );
      } else if (type == CurrentRealNameType.login) {
        initToken().then((value) => dismissLoadingDialog());
      }
    });
  }

  if (success) {
    register(status);
  } else {
    var value = await Get.to(PhonePage(
      data: event.data,
    ));

    if (StrKey.mobileSuccess == value) {
      MobileVerify.mMobileSuccess[username] = true;
      register(RegisterStatus.none);
    }
  }
}


/// 敏感词检查
bool sensitiveWordCheck(MessageEvent info) {
  bool result = false;
  if (sensitiveWords.isNotEmpty && info.type == MessageType.text) {
    var body = info.body ?? '';
    body = body.toLowerCase();

    for (var element in sensitiveWords) {
      var sensitiveWord = element.toLowerCase();
      if (body.contains(sensitiveWord)) {
        result = true;
        break;
      }
    }
  }
  return result;
}
