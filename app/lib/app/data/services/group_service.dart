import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';

import 'package:flutter_metatel/app/data/enums/enum.dart';
import 'package:flutter_metatel/app/data/providers/native/chatio/chatio/chatio_ffi.dart';
import 'package:flutter_metatel/core/task/time_task.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:flutter_metatel/core/values/config.dart';
import 'package:get/get.dart';

import '../../../core/languages/l.dart';
import '../../../core/task/announcement_task.dart';
import '../../../core/task/chat_task.dart';
import '../../../core/utils/events_bus.dart';
import '../../../core/utils/util.dart';
import '../events/events.dart';
import '../models/datas_model.dart';
import '../models/group_info_model.dart';
import '../models/group_model.dart';
import '../models/message_model.dart';
import '../models/select_contact_model.dart';
import '../providers/db/database.dart';
import '../providers/native/chatio/chatio_async.dart';
import 'config_service.dart';

class GroupService extends GetxService {
  final Map<String, Timer> _groupTimer = {};

  Future<GroupService> init() async {
    return this;
  }

  Future<String?> createGroup(List<ContactInfo> contacts) async {
    var config = Get.find<AppConfigService>();

    List<String> titles = [];
    titles.add(config.getMySelfDisplayName());

    List<GroupInfoList> groupMemberList = [];
    List<String> toNumbers = [];

    for (var element in contacts) {
      toNumbers.add(element.userName);
      groupMemberList.add(GroupInfoList(
          nickName: element.displayName, userName: element.userName));
      if (titles.length <= 3) {
        titles.add(element.displayName ?? '');
      }
    }
    String? owner = Get.find<AppConfigService>().getUserName();
    groupMemberList.add(GroupInfoList(
        nickName: Get.find<AppConfigService>().getMySelfDisplayName(),
        userName: owner));
    String title = titles.join(',');
    if (title.length > 30) {
      title = title.substring(0, 30);
    }
    GroupInfoModel info = GroupInfoModel();
    info.title = title;
    info.owner = owner;
    info.list = groupMemberList;
    info.id = uuid();
    FileUploadResult result = await sendGroupInfoChangeMessage(
        GroupOption.response, info, false, toNumbers);
    if (result.success) {
      insertDataBase(
        info,
        invalid: true,
        propertyUrl: result.fileUrl,
        propertyFragment: result.fileKey,
        createGroup: true,
      );
    }
    return result.success ? info.id : null;
  }

  /// 添加群成员
  Future<bool> addMember(String groupID, List<ContactInfo> contacts) async {
    return groupInfoChange(groupID, GroupOption.add, contacts: contacts);
  }

  /// 移除群成员
  Future<bool> removeMember(String groupID, List<ContactInfo> contacts) async {
    return groupInfoChange(groupID, GroupOption.remove, contacts: contacts);
  }

  /// 解散群
  Future<bool> disbandGroup(String groupID) async {
    return groupInfoChange(groupID, GroupOption.dissolution);
  }

  /// 退出群
  Future<bool> quitGroup(String groupID) async {
    return groupInfoChange(groupID, GroupOption.exit);
  }

  /// 更新群标题
  Future<bool> updateTitle(String groupID, String? title) async {
    return groupInfoChange(groupID, GroupOption.update, title: title);
  }

  /// 更新群描述
  Future<bool> updateDescribe(String groupID, String? describe) async {
    return groupInfoChange(groupID, GroupOption.update, describe: describe);
  }
  /// 更新群公告
  Future<bool> updateAnnouncement(String groupID, String? announcement) async {
    return groupInfoChange(groupID, GroupOption.update, announcement: announcement);
  }
  /// 更新头像
  Future<bool> updateAvatar(String groupID, String avatarPath) async {
    if (!File(avatarPath).existsSync()) {
      return false;
    }

    var result = await encryptFileAndUpload(avatarPath);
    if (!result.success) {
      return false;
    }
    AppLogger.d(
        'getDetailInfo avatarPath111=${appSupporAbsolutePathToPath(avatarPath)}');
    return groupInfoChange(groupID, GroupOption.update,
        avatarUrl: result.fileUrl,
        fragment: result.fileKey,
        avatarPath: appSupporAbsolutePathToPath(avatarPath));
  }

  Future<bool> groupInfoChange(
    String groupID,
    String action, {
    List<ContactInfo>? contacts,
    String? title,
    String? avatarUrl,
    String? avatarPath,
    String? fragment,
    String? describe,
    String? announcement,
    bool saveDB = true,
  }) async {
    if (groupID.isEmpty || action.isEmpty) {
      return false;
    }

    var db = Get.find<AppDatabase>();

    List<GroupInfoList> allMembers = [];
    List<GroupInfoList> changeMembers = [];
    List<String> toNumbers = [];
    String changeMemberName = ''; // 变化成员名称集合

    contacts = contacts ?? [];
    for (var element in contacts) {
      String userName = element.userName;
      String nickName = element.displayName ?? '';
      var memberInfo = GroupInfoList(
        userName: userName,
        nickName: nickName,
      );

      if (changeMemberName.isEmpty) {
        changeMemberName = nickName;
      } else {
        changeMemberName = '$changeMemberName,$nickName';
      }

      if (GroupOption.add == action) {
        allMembers.add(memberInfo);
      }

      changeMembers.add(memberInfo);
      toNumbers.add(userName);
    }

    var memberDatas = await db.allGroupMember(groupID).get();
    List<String> ids = [];
    for (var element in memberDatas) {
      ids.add(element.username);
    }

    var allMemberData = await db.memberContactDatas(groupID, ids).get();
    for (var element in allMemberData) {
      if (GroupOption.remove == action) {
        var resilt = contacts
            .firstWhereOrNull((data) => data.userName == element.username);
        if (resilt != null) {
          continue;
        }
      }

      var memberInfo = GroupInfoList(
        userName: element.username,
        nickName: element.displayname,
      );
      allMembers.add(memberInfo);
      toNumbers.add(element.username);
    }

    bool isAttach = false;
    bool invalid = true;
    if (GroupOption.exit == action || GroupOption.dissolution == action) {
      invalid = false;
      allMembers.clear();
      changeMembers.clear();

      if (GroupOption.exit == action) {
        isAttach = true;
      }
    }

    var groupData = await db.oneGroupInfo(groupID).getSingleOrNull();
    GroupInfoModel info = GroupInfoModel(
      id: groupID,
      owner: groupData?.owner,
      title: title ?? groupData?.title,
      avatarUrl: avatarUrl ?? groupData?.avatarUrl,
      avatarPath: avatarPath ?? groupData?.avatarPath,
      fragment: fragment ?? groupData?.avatarFragment,
      describe: describe ?? groupData?.describe,
      announcement: announcement ??groupData?.announcement,
      list: allMembers,
      newList: changeMembers,
    );

    FileUploadResult result =
        await sendGroupInfoChangeMessage(action, info, isAttach, toNumbers);
    if (!result.success) {
      return false;
    }

    if (saveDB) {
      await insertDataBase(
        info,
        propertyUrl: result.fileUrl,
        propertyFragment: result.fileKey,
        invalid: invalid,
      );
      String selfID = await Get.find<AppConfigService>().getUserName() ?? '';
      createGroupTips(action, groupID, groupData?.owner ?? '',
          memberMames: changeMemberName, fromUser: selfID);
      sendGroupInfoChangeEvent(groupID);
    }

    return true;
  }

  Future<void> insertDataBase(
    GroupInfoModel info, {
    bool? invalid,
    String? propertyUrl,
    String? propertyFragment,
    bool createGroup = false,
  }) async {
    List<GroupMemberCompanion> companions = [];
    String id = info.id ?? '';
    for (var element in info.list!) {
      String username = element.userName ?? '';

      int role = ChannelMemberRole.ordinary.index;
      if (info.owner == username) {
        role = ChannelMemberRole.owner.index;
      }

      var data = GroupMemberCompanion.insert(
        groupId: id,
        username: username,
        memberUuid: id + username,
        role: ofNullable(role),
        createTime:
            ofNullable(TimeTask.instance.getNowTime().toDouble()),
        updateTime:
            ofNullable(TimeTask.instance.getNowTime().toDouble()),
      );
      companions.add(data);
    }
    await _members(id, companions, true);

    GroupInfoCompanion infoCompanion = GroupInfoCompanion.insert(
      groupId: id,
      owner: ofNullable(info.owner),
      title: ofNullable(info.title),
      describe: ofNullable(info.describe),
      avatarUrl: ofNullable(info.avatarUrl),
      avatarPath: ofNullable(info.avatarPath),
      announcement: ofNullable(info.announcement),
      invalid: ofNullable(invalid),
      propertyUrl: ofNullable(propertyUrl),
      propertyFragment: ofNullable(propertyFragment),
    );
    Get.find<AppDatabase>().insertOrUpdateGroupInfoData([infoCompanion]);

    Get.find<AppDatabase>().insertOrUpdateSessionData(SessionCompanion.insert(
      username: id,
      displayname: ofNullable(info.title),
      chatType: ofNullable(ChatType.groupChat.index),
      avatarPath: ofNullable(info.avatarPath),
      createTime: ofNullable(TimeTask.instance.getNowTime().toDouble()),
      updateTime: ofNullable(TimeTask.instance.getNowTime().toDouble()),
      time: ofNullable(
          createGroup ? TimeTask.instance.getNowTime() : null),
    ));
  }

  Future<void> _members(
      String groupId, List<GroupMemberCompanion> members, bool all) async {
    var db = Get.find<AppDatabase>();
    List<String> deleteIDs = [];
    if (all) {
      var oldDatas = await db.allGroupMember(groupId).get();
      for (var element in members) {
        oldDatas.removeWhere((data) => data.username == element.username.value);
      }

      for (var element in oldDatas) {
        deleteIDs.add(element.username);
      }
    }
    db.insertOrUpdateMemberData(members);

    /// 删除多余的成员
    if (deleteIDs.isNotEmpty) {
      db.deleteGroupMember(groupId, deleteIDs);
    }
  }

  /// 发送群信息变化消息
  /// [action] 群操作类型
  /// [info] 群信息
  /// [isAttach] 是否附加属性 true:附加
  /// [toNumbers] 是否附加属性 true:附加
  Future<FileUploadResult> sendGroupInfoChangeMessage(
    String action,
    GroupInfoModel info,
    bool isAttach,
    List<String> toNumbers,
  ) async {
    FileUploadResult sendResult = FileUploadResult();

    String jsonData = json.encode(info.toJson());
    var body = Uint8List.fromList(utf8.encode(jsonData));

    var privateKeyBase64 = Config.privateKeyBase64;
    var privateKey = base64.decode(privateKeyBase64);

    String groupFile = getGroupInfoFilePath(info.id);
    String inFile = groupFile;
    if (!File(inFile).existsSync()) {
      inFile = '';
    }

    String outFile = appTempAbsolutePath(uuid()) ?? '';

    int result = -1;
    if (isAttach) {
      result = await ChatioNative.groupCommit(
          inFile, outFile, body, privateKey, Config.getDomain());
    } else {
      result = await ChatioNative.groupCreate(
          inFile, outFile, body, privateKey, Config.getDomain());
    }

    if (result != 0) {
      AppLogger.e('发送群信息变化消息时,调用群属性接口失败');
      return sendResult;
    }

    // 加密并上传
    var resultData = await encryptFileAndUpload(outFile);
    if (!resultData.success) {
      AppLogger.e('发送群信息变化消息，文件加密或上传失败');
      return sendResult;
    }

    /// 移除自己
    var username = await Get.find<AppConfigService>().getUserName();

    // 发送消息通知群成员
    var msgID = await sendMessage(toNumbers, action, info.id ?? '',
        fileUrl: resultData.fileUrl, fileFragment: resultData.fileKey);

    if (msgID.isEmpty &&
        toNumbers.length == 1 &&
        toNumbers.contains(username)) {
      // 这种情况属于，群里只剩自己，并且也没有其它终端
    } else if (msgID.isEmpty) {
      deleteFile(outFile);
      AppLogger.e('发送群信息变化消息, 发送应答消息失败.  toNumbers:$toNumbers');
      return sendResult;
    }

    // 拷贝
    File(outFile).copySync(groupFile);
    deleteFile(outFile);

    sendResult = resultData;
    return sendResult;
  }

  /// 发送群信息请求
  void sendGroupInfoRequest(String toNumber, String groupID) async {
    var msgID = await sendMessage([toNumber], GroupOption.request, groupID);
    if (msgID.isEmpty) {
      AppLogger.e('发送群信息请求消息，发送失败');
    }
  }

  /// 接收到成员退出，群主发送Response消息
  void receiveMemberQuitSendResponse(String groupID) {
    _groupTimer[groupID]?.cancel();

    var timer = Timer.periodic(const Duration(seconds: 5), (timer) {
      timer.cancel();
      _groupTimer.remove(groupID);

      groupInfoChange(
        groupID,
        GroupOption.response,
        saveDB: false,
      );
    });
    _groupTimer[groupID] = timer;
  }

  Future<String> sendMessage(
    List<String> toNumbers,
    String action,
    String groupID, {
    String? fileUrl,
    String? fileFragment,
  }) async {
    var msgData = GroupModel(
      chatType: ChatType.groupChat.index,
      action: action,
      fileUrl: fileUrl,
      fileFragment: fileFragment,
      msgId: uuid(),
      time: TimeTask.instance.getNowTime(),
      type: MessageType.group_operation.index,
      owner: groupID,
    ).toJson();

    var msgID = await Get.find<ChatioFFI>()
        .sendMessage(toNumbers, json.encode(msgData));
    if (msgID.isEmpty) {
      AppLogger.e('发送群信息消息，发送失败');
    }

    return msgID;
  }

  void createGroupTips(
    String action,
    String groupID,
    String ownerID, {
    String memberMames = '',
    bool bHasOneself = false,
    List<GroupInfoList>? listChangeMember,
    int msgTime = 0,
    String fromUser = '',
  }) async {
    // 消息提示
    String msgTips = '';

    String selfID = await Get.find<AppConfigService>().getUserName() ?? '';

    // 群成员自己退出
    if (GroupOption.exit == action) {
      if (selfID == fromUser) {
        msgTips = '${L.other_you.tr}${L.other_has_exit_group.tr}';
      } else {
        msgTips = '$memberMames ${L.other_has_exit_group.tr}';
      }
    }
    // 群主解散群
    else if (GroupOption.dissolution == action) {
      if (selfID == ownerID) {
        msgTips = L.this_group_has_been_disbanded_by_you.tr;
      } else {
        msgTips = L.the_group_has_been_disbanded_by_the_group_leader.tr;
      }
    }
    // 群主更新群信息
    else if (GroupOption.update == action) {
      if (selfID == ownerID) {
        msgTips = '${L.other_you.tr}${L.other_update_group_info.tr}';
      } else {
        msgTips = L.the_group_owner_modified_the_group_information.tr;
      }
    }
    // 群主邀请成员加入
    else if (GroupOption.add == action) {
      msgTips = '$memberMames ${L.other_join_group.tr}';
      // if (selfID == ownerID) {
      //   msgTips = '$memberMames ${L.other_join_group.tr}';
      // } else {
      //   if (bHasOneself) {
      //     if (listChangeMember?.length == 1) {
      //       msgTips = '${L.other_group_owner.tr}${L.other_invite_join_group.tr}';
      //     } else {
      //       msgTips = '${L.other_group_owner.tr} ${L.other_invite.tr} ${L.other_with_2.tr} $memberMames ${L.other_join_group_chat.tr}';
      //     }
      //   } else {
      //     msgTips = '${L.other_group_owner.tr} ${L.other_invite_2.tr} $memberMames ${L.other_join_group.tr}';
      //   }
      // }
    }
    // 群主踢出成员
    else if (GroupOption.remove == action) {
      msgTips = '$memberMames ${L.remove_group_chat.tr}';
      // if (selfID == ownerID) {
      //   msgTips = '$memberMames ${L.other_has.tr} ${L.other_you_small.tr} ${L.remove_group_chat.tr}';
      // } else {
      //   if (bHasOneself) {
      //     msgTips = '${L.other_you.tr} ${L.other_has.tr} ${L.other_group_owner.tr} ${L.remove_group_chat.tr}';
      //   } else {
      //     msgTips = '$memberMames ${L.other_has.tr} ${L.other_group_owner.tr} ${L.remove_group_chat.tr}';
      //   }
      // }
    }

    if (msgTips.isNotEmpty) {
      createGroupTipsMessage(groupID, msgTips, msgTime: msgTime);
    }
  }

  /// 创建群提示消息
  void createGroupTipsMessage(
    String groupID,
    String msgTips, {
    int msgTime = 0,
  }) async {
    if (msgTime == 0) {
      msgTime = TimeTask.instance.getNowTime();
    }
    var username = await Get.find<AppConfigService>().getUserName();

    var msgModel = MessageModel(
      body: msgTips,
      time: msgTime,
      type: MessageType.tip.index,
      chatType: ChatType.groupChat.index,
      msgId: uuid(),
      owner: groupID,
    );

    ChatTask.instance.saveToDb(
      msgModel,
      groupID,
      null,
      uuid: uuid(),
      fromname: username,
      send: true,
      compelUpdate: true, saveAdminTop: false,
    );
  }

  void sendGroupInfoChangeEvent(String groupId) async {
    var currSessionID = Get.find<AppConfigService>().currSessionID;
    if (currSessionID == groupId) {
      var groupData =
          await Get.find<AppDatabase>().oneGroupInfo(groupId).getSingleOrNull();
      if (groupData != null) {
        await AnnouncementTask.updateAnnouncementTask(groupId,groupData.announcement);
        Get.find<EventBus>().fire(ChannelOrGroupInfoUpdateEvent(
            id: groupData.groupId,
            type: ChatType.groupChat.index,
            title: groupData.title,
            announcement: groupData.announcement,
            invalid: groupData.invalid ?? false,
            avatarPath: groupData.avatarPath));
      }
    }
  }
}
