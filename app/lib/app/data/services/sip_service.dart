/*
 * <AUTHOR> <PERSON><PERSON>
 * @Date         : 2022-05-09 17:14:52
 * @Description  : Sip 长连接服务
 * @LastEditors  : <PERSON><PERSON>
 * @LastEditTime : 2022-09-11 18:33:33
 * @FilePath     : /flutter_metatel/lib/app/data/services/sip_service.dart
 */

import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:flutter_metatel/app/data/providers/native/chatio/chatio/chatio_ffi.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:flutter_metatel/core/utils/events_bus.dart';
import 'package:flutter_metatel/core/values/config.dart';
import 'package:get/get.dart';
import 'package:sip_ua/sip_ua.dart';
import 'package:sip_ua/src/sip_message.dart';
import 'package:worker_manager/worker_manager.dart';

import '../events/events.dart';

class SipService extends GetxService implements SipUaHelperListener {
  final SIPUAHelper _helper = SIPUAHelper();
  Worker? worker;
  var _currentIndex = 0.obs;
  Future<SipService> init() async {
    Executor().warmUp(log: true, isolatesCount: 4);

    _helper.addSipUaHelperListener(this);
    worker = interval(
      _currentIndex,
      (_) {
        () {
          Get.find<EventBus>().fire(SyncChannelMessageEvent());
        };
      },
      time: const Duration(seconds: 3),
      condition: () => true,
    );
    return this;
  }

  void connect(String name, String token) {
    AppLogger.d("Sip connect: $name, $token");
    UaSettings uaSettings = UaSettings();
    uaSettings.webSocketSettings.extraHeaders = {
      'Origin': '${Config.getSipDomain()}:8060',
    };
    uaSettings.webSocketSettings.allowBadCertificate = true;

    AppLogger.d("Sip connect: ${Config.getSipDomain()}:8060");
    uaSettings.webSocketUrl = Config.getSipUrl();
    uaSettings.uri = '$name@${Config.getSipDomain()}';
    uaSettings.authorizationUser = name;
    uaSettings.registerParams.extraContactUriParams = {
      'Authorization': token,
    };
    uaSettings.register_expires = 300;
    uaSettings.userAgent = 'Metatel';
    AppLogger.d(
        "Sip connect uaSettings: \n"
            "webSocketSettings.extraHeaders:${uaSettings.webSocketSettings.extraHeaders}\n"
            "uri:${uaSettings.uri}\n"
            "authorizationUser:${uaSettings.authorizationUser}\n"
            "uri:${uaSettings.uri}\n"
            "registerParams.extraContactUriParams:${uaSettings.registerParams.extraContactUriParams}\n"
            "register_expires:${uaSettings.register_expires}\n"
            "userAgent:${uaSettings.userAgent}"
    );
    _helper.start(uaSettings);
  }

  void register() {
    _helper.register();
  }

  @override
  void callStateChanged(Call call, CallState state) {
    AppLogger.d('Sip callStateChanged: $state');
  }

  int time=0;
  @override
  void onNewMessage(SIPMessageRequest msg) {
    AppLogger.d('Sip onNewMessage: ${msg.request}');

    try {
      Get.find<ChatioFFI>().recvMessage();
      if (msg.request.runtimeType == IncomingRequest) {
        var request = msg.request as IncomingRequest;
        if (request.body?.contains('chan') == true) {
          AppLogger.d('${request.body}');
          var t = DateTime.now().microsecondsSinceEpoch ~/ 1000000;
          AppLogger.d('onNewMessage:t= ${t}');
          if (t - time > Config.channelSynTime) {
            time = t;
            Get.find<EventBus>().fire(SyncChannelMessageEvent());
          } else{
            _currentIndex++;
          }
        }
      }
    } catch (e) {
      AppLogger.d('Sip onNewMessage Error: $e');
    }
    msg.request.reply(200);
  }

  @override
  Future<void> registrationStateChanged(RegistrationState state) async {
    AppLogger.d('Sip registrationStateChanged: ${state.state}');
    try {
      Get.find<ChatioFFI>().recvMessage();
    } catch (e) {
      AppLogger.d('registrationStateChanged Error: $e');
    }
  }

  @override
  void transportStateChanged(TransportState state) {
    // AppLogger.d('Sip transportStateChanged: ${state.state}');
  }

  @override
  void onNewNotify(Notify ntf) {
    // TODO: implement onNewNotify
  }
  @override
  void onClose() {
    worker?.dispose();
    super.onClose();
  }
}
