import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:async_task/async_task_extension.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_metatel/app/data/providers/db/database.dart';
import 'package:flutter_metatel/app/data/services/down_loads_service.dart';
import 'package:flutter_metatel/app/data/services/get_all_channel_members.util.dart';
import 'package:flutter_metatel/core/task/channel_task.dart';
import 'package:flutter_metatel/core/task/complaint_message_task.dart';
import 'package:flutter_metatel/core/task/message_task.dart';
import 'package:flutter_metatel/core/task/time_task.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:flutter_metatel/core/utils/events_bus.dart';
import 'package:flutter_metatel/core/utils/proxy_util.dart';
import 'package:flutter_metatel/core/utils/util.dart';
import 'package:flutter_metatel/core/values/channel.dart';
import 'package:flutter_metatel/core/values/code.dart';
import 'package:flutter_web3/web3dart/src/utils/quene_task.dart';
import 'package:get/get.dart' hide Response;
import 'package:intl/intl.dart';

import '../../../core/languages/l.dart';
import '../../../core/task/contact_task.dart';
import '../../../core/task/session_task.dart';
import '../../../core/values/config.dart';
import '../enums/enum.dart';
import '../events/events.dart';
import '../models/avatar_model.dart';
import '../models/channel_card_model.dart';
import '../models/channel_operation_model.dart';
import '../providers/api/channel.dart';
import '../providers/api/did.dart';
import '../providers/api/own.dart';
import '../providers/native/chatio/chatio/chatio_ffi.dart';
import 'config_service.dart';

class MemberInfo {
  MemberInfo(
    this.name, {
    this.nickname,
    this.avatarPath,
    this.state,
    this.displayname,
    this.from,
    this.isTid,
  });
  final String name;
  // 需要显示的昵称
  String? nickname;
  // 联系人本身的昵称
  String? displayname;
  String? avatarPath;
  String? from;
  int? state;
  bool? isTid;
}

class MemberContactInfo {
  final String username;
  // 群昵称
  String? nickname;
  // 联系人本身的昵称
  String? displayname;
  // 编辑过的昵称
  String? localname;
  String? avatarPath;
  String? avatarUrl;
  int? state;
  bool? isTid;
  MemberContactInfo(
    this.username, {
    this.nickname,
    this.displayname,
    this.localname,
    this.avatarPath,
    this.avatarUrl,
    this.state,
    this.isTid,
  });
}

class ChannelService extends GetxService {
  final List<String> _receiveMsgID = [];
  void svaeMsgID(String? msgID) {
    if (msgID?.isNotEmpty == true && !_receiveMsgID.contains(msgID)) {
      _receiveMsgID.add(msgID!);
    }
  }

  int _currentTime = 0;
  void updateChannel(String channelID, String msgId, {String? action}) {
    int t = TimeTask.instance.getNowTime() ~/ 1000;
    if (t - _currentTime < 2) {
      return;
    }
    if (t - _currentTime > 60) {
      Get.find<EventBus>().fire(ChannelOptionEvent(action, channelID));
    }
    Future.delayed(const Duration(milliseconds: (2000))).then((value) {
      Get.find<EventBus>().fire(ChannelOptionEvent(action, channelID));
      getChannelInfoRequest(channelID).then((value) {
        if (value.code == Code.code200) {
          ChannelTask.channels([value]);
        }
      });
    });
    _currentTime = t;
  }

  bool msgIDExists(String? msgID) => _receiveMsgID.contains(msgID);

  Future<ChannelService> init() async {
    _listenEvent();
    return this;
  }

  static final TaskQueue _taskQueue = TaskQueue();
  Map<String, int> allChannelInfoMap = {};

  /// 获取当前频道所有成员
  void getAllChannelMemberInfos(String channelID, String username) async {
    AppLogger.d('getAllChannelMemberInfos --0');
    var t = allChannelInfoMap[channelID];
    var currentTime = TimeTask.instance.getNowTime() ~/ 1000;
    if (t != null && currentTime < t + 2) {
      allChannelInfoMap[channelID] = currentTime;
      AppLogger.d('getAllChannelMemberInfos --');
      return;
    }
    allChannelInfoMap[channelID] = currentTime;
    String membersApi = await Channel.membersApi();
    String token = Get.find<AppConfigService>().getToken();
    String getMemberApi = await Channel.getMemberApi();
    String? proxy = ProxyUtil.instance.getProxy();
    var map = {
      'channelID': channelID,
      'username': username,
      'membersApi': membersApi,
      'getMemberApi': getMemberApi,
      'token': token,
      'proxy': proxy,
      'certBytes' :Config.certData!.codeUnits,
    };
    var info = await _taskQueue.submit(_getAllChannelMemberInfos, map);
    if (info == null) return;
    AppLogger.d('getAllChannelMemberInfos --4==${info.length}');
    ChannelTask.members(channelID, info, true);    
  }

  Future<List<GroupMemberCompanion>?> _getAllChannelMemberInfos(Map map) async {
    return await compute(getAllChannelMemberInfoSync,
        map); //memberInfoRequest(channelID, username, data.members);
  }

  /// 发送邀请
  void sendInvite(List<String> targetIds, String channelId, ChannelCard body) {
    var mapData = ChannelOperation(
      time: TimeTask.instance.getNowTime(),
      chatType: ChatType.singleChat.index,
      type: MessageType.channelCard.index,
      msgId: uuid(),
      owner: channelId,
      body: json.encode(body.toJson()),
    ).toJson();
    Get.find<ChatioFFI>().sendMessage(targetIds, json.encode(mapData));
  }

  /// 发送踢出群聊
  /// [undo] true：踢出时撤回所有消息
  void sendKick(
    String channelId,
    List<String> targetIds,
    List<String> targetDisplayNames,
    ChannelMemberRole operationDisplayNameRole,
    bool undo,
  ) {
    String body = "";
    String targetDisplayName = "";
    for (int i = 0; i < targetDisplayNames.length; i++) {
      String tmp = targetDisplayNames[i];
      String split = i == targetDisplayNames.length - 1 ? "" : ",";
      targetDisplayName = targetDisplayName + tmp + split;
    }
    body = L.group_kick_member.trParams({
      'targetDisplayName': targetDisplayName,
      'operationDisplayName':
          operationDisplayNameRole == ChannelMemberRole.owner
              ? L.other_group_owner.tr
              : Get.find<AppConfigService>().getMySelfDisplayName()
    });
    var mapData = ChannelOperation(
      action: ChannelOption.kick,
      time: TimeTask.instance.getNowTime(),
      chatType: ChatType.channelChat.index,
      type: MessageType.channelOpera.index,
      msgId: uuid(),
      body: body,
      targetId: targetIds,
      owner: channelId,
    ).toJson();
    sendMessageRequest(channelId, json.encode(mapData));

    if (undo) {
      int time = TimeTask.instance.getNowTime();
      // String nickname = Get.find<AppConfigService>().getMySelfDisplayName();
      var dateTime = DateTime.fromMillisecondsSinceEpoch(time);
      String timeText = DateFormat("yyyy-MM-dd HH:mm:ss").format(dateTime);
      String bodyText = '$timeText ${L.the_message_was_recall.tr}';

      var mapData = ChannelOperation(
        action: ChannelOption.recall,
        time: time,
        chatType: ChatType.channelChat.index,
        type: MessageType.channelOpera.index,
        targetId: targetIds,
        targetTime: -1,
        msgId: uuid(),
        owner: channelId,
        body: bodyText,
      ).toJson();
      sendMessageRequest(channelId, json.encode(mapData));
    }
  }

  /// 发送进群消息
  void sendJoinChannel(String channelId, String body) {
    var mapData = ChannelOperation(
      action: ChannelOption.channelJoin,
      time: TimeTask.instance.getNowTime(),
      chatType: ChatType.channelChat.index,
      type: MessageType.channelOpera.index,
      msgId: uuid(),
      owner: channelId,
      body: body,
    ).toJson();
    sendMessageRequest(channelId, json.encode(mapData));
  }
  //发送群主转让消息
  Future<bool> sendTransfer(String channelId, String body) async {
    var mapData = ChannelOperation(
      action: ChannelOption.transfer,
      time: TimeTask.instance.getNowTime(),
      chatType: ChatType.channelChat.index,
      type: MessageType.channelOpera.index,
      msgId: uuid(),
      owner: channelId,
      body: body,
    ).toJson();
    await sendMessageRequest(channelId, json.encode(mapData));
    return true;
  }

  /// 添加或移除管理员
  Future<bool> addOrRemoveAdmin(
    String channelId,
    List<String> targetIds,
    List<String> targetDisplayNames,
    bool isAdd,
  ) async {
    if (channelId.isEmpty || targetIds.isEmpty) {
      return false;
    }

    bool result = await adminOrRemoveSetRequest(channelId, targetIds, isAdd);
    if (!result) {
      return false;
    }

    List<GroupMemberCompanion> companions = [];
    for (var element in targetIds) {
      companions.add(GroupMemberCompanion.insert(
        groupId: channelId,
        username: element,
        memberUuid: channelId + element,
        role: ofNullable(isAdd
            ? ChannelMemberRole.administrator.index
            : ChannelMemberRole.ordinary.index),
        createTime: ofNullable(TimeTask.instance.getNowTime().toDouble()),
        updateTime: ofNullable(TimeTask.instance.getNowTime().toDouble()),
      ));
    }
    ChannelTask.members(channelId, companions, false);
    var dateTime = TimeTask.instance.getNowDateTime();
    String timeText = DateFormat("yyyy-MM-dd HH:mm:ss").format(dateTime);
    String body = "";
    String targetDisplayName = "";
    for (int i = 0; i < targetDisplayNames.length; i++) {
      String tmp = targetDisplayNames[i];
      String split = i == targetDisplayNames.length - 1 ? "" : ",";
      targetDisplayName = targetDisplayName + tmp + split;
    }
    body = (!isAdd
        ? L.administrator_privileges_revoked.trParams(
            {'time': timeText, 'targetDisplayName': targetDisplayName})
        : L.assigned_as_administrator.trParams(
            {'time': timeText, 'targetDisplayName': targetDisplayName}));
    var mapData = ChannelOperation(
      action: ChannelOption.admin,
      event: isAdd ? ChannelOptionEv.add_admin : ChannelOptionEv.remove_admin,
      time: TimeTask.instance.getNowTime(),
      body: body,
      chatType: ChatType.channelChat.index,
      type: MessageType.channelOpera.index,
      msgId: uuid(),
      targetId: targetIds,
      owner: channelId,
    ).toJson();
    sendMessageRequest(channelId, json.encode(mapData));

    return true;
  }

  /// 设置成员禁言到期时间 秒
  Future<bool> setMuteExpire(
      String channelId, String targetId, int mute) async {
    if (channelId.isEmpty || targetId.isEmpty) {
      return false;
    }

    bool result = await memberMuteRequest(channelId, targetId, mute);
    if (!result) {
      return false;
    }
    ChannelTask.updateMemberMuteExpire(channelId, targetId, mute);
    return true;
  }

  /// 频道无效数据更新
  void channelInvalidDbDataUpdate(String channelID) async {
    var db = Get.find<AppDatabase>();
    db.insertOrUpdateChannelInfoData([
      ChannelInfoCompanion.insert(
        channelId: channelID,
        state: ofNullable(ChannelState.invalid.index),
        updateTime: ofNullable(TimeTask.instance.getNowTime().toDouble()),
      )
    ]).then((value) {
      db.oneChannelInfo(channelID).getSingleOrNull().then((value) =>
          Get.find<EventBus>().fire(ChannelOrGroupInfoUpdateEvent(
              id: channelID,
              type: ChatType.channelChat.index,
              title: value?.title,
              announcement: value?.announcement,
              invalid: value?.state == ChannelState.normal.index,
              avatarPath: value?.avatarPath)));
    });

    var username = Get.find<AppConfigService>().getUserName() ?? '';
    db.insertOrUpdateMemberData([
      GroupMemberCompanion.insert(
        groupId: channelID,
        username: username,
        memberUuid: channelID + username,
        role: ofNullable(ChannelMemberRole.ordinary.index),
        updateTime: ofNullable(TimeTask.instance.getNowTime().toDouble()),
      )
    ]);
  }

  /// 获取成员信息
  Future<void> getMemberInfo(
    String channelID,
    List<String> names, {
    ValueChanged<List<MemberInfo>?>? callback,
    bool downloadAvatar = true,
    bool avatarAsyncNotify = false,
  }) async {
    var c = 99;
    int count = names.length ~/ c;
    int d = names.length % c;
    if (d > 0) {
      count = count + 1;
    }
    if (count == 0) {
      _getMemberInfo(
        channelID,
        names,
        callback: callback,
        downloadAvatar: downloadAvatar,
        avatarAsyncNotify: avatarAsyncNotify,
      );
    } else {
      for (int i = 0; i < count; i++) {
        int? end = (i == count - 1) ? null : (i + 1) * c;
        // AppLogger.d('getMemberInfo start=${i * c} end=$end');
        _getMemberInfo(
          channelID,
          names.sublist(i * c, end),
          callback: callback,
          downloadAvatar: downloadAvatar,
          avatarAsyncNotify: avatarAsyncNotify,
        );
      }
    }
  }

  /// 获取成员信息
  Future<void> _getMemberInfo(
    String channelID,
    List<String> names, {
    ValueChanged<List<MemberInfo>?>? callback,
    bool downloadAvatar = true,
    bool avatarAsyncNotify = false,
  }) async {
    if (channelID.isEmpty || names.isEmpty) {
      callback?.call(null);
      return;
    }

    /// 外面要是清除了names，names会跟着被清除，所以重新拷贝
    List<String> members = [];
    members.addAll(names);

    /// 先从数据库获取
    Map<String, MemberContactInfo> mapMemberContactInfo = {};
    mapMemberContactInfo = await getDBMemberData(channelID, members);
    callback?.call(_toMemberInfo(mapMemberContactInfo.values.toList()));

    /// 移除自己
    var username = Get.find<AppConfigService>().getUserName();
    if (members.contains(username)) {
      members.remove(username);
    }

    /// 从服务器获取最新数据
    var infos = await getOtherInfo(members);
    if (infos == null || infos.isEmpty) {
      return;
    }

    TaskData taskData = TaskData(channelID, []);

    List<ContactCompanion> dbCompanions = [];
    for (var element in infos) {
      AppLogger.d('getMemberInfo infos=${element.nickname}');

      String name = element.name ?? '';
      var infoData = mapMemberContactInfo[name];
      String? avatarPath;
      if (downloadAvatar &&
          element.avatar?.isNotEmpty == true &&
          (element.avatar != infoData?.avatarUrl ||
              (infoData?.avatarPath?.isEmpty ?? true) ||
              !File(appSupporAbsolutePath(infoData?.avatarPath ?? '') ?? "")
                  .existsSync())) {
        var savePath = avatarSavePath(
          userName: element.name ?? '',
          fileName: '${element.avatar?.split('/').last}',
        );

        if (avatarAsyncNotify) {
          taskData.datas.add(FileData(name, element.avatar ?? '', savePath));
        } else {
          avatarPath = await downloadFile(
            element.avatar,
            savePath: savePath,
            isComPressImageJPG: true,
          );
        }
      }

      /// 如果本地存的联系人名称及头像url，有变化则入库
      if (infoData?.displayname != element.nickname || avatarPath != null) {
        var companion = ContactCompanion.insert(
          username: name,
          avatarPath: ofNullable(appSupporAbsolutePathToPath(avatarPath)),
          avatarUrl: avatarPath == null
              ? ofNullable(null)
              : ofNullable(element.avatar),
          displayname: ofNullable(element.nickname),
          state: ofNullable(infoData?.state),
          createTime: ofNullable(TimeTask.instance.getNowTime().toDouble()),
          updateTime: ofNullable(TimeTask.instance.getNowTime().toDouble()),
        );
        dbCompanions.add(companion);
      }

      mapMemberContactInfo[name]?.displayname = element.nickname;

      if (avatarPath != null) {
        mapMemberContactInfo[name]?.avatarPath =
            appSupporAbsolutePathToPath(avatarPath);
      }
    }

    callback?.call(_toMemberInfo(mapMemberContactInfo.values.toList()));

    if (taskData.datas.isNotEmpty) {
      Get.find<DownLoadService>().add(taskData);
    }

    var db = Get.find<AppDatabase>();
    for (var element in dbCompanions) {
      var info = mapMemberContactInfo[element.username.value];
      // 更新会话名称/头像
      SessionTask.name(element.username.value,
          displayname: info?.state == ContactState.friend.index
              ? info?.localname ?? info?.displayname
              : info?.displayname,
          avatarPath: appSupporAbsolutePathToPath(info?.avatarPath));

      db.updateContactData(element);
    }
  }

  /// 获取用户信息
  /// [server] true：需要从服务器获取
  Future<void> getOwnInfo(String? name,
      {bool server = false, ValueChanged<MemberInfo>? callback}) async {
    if (name == null || name.isEmpty) return;

    return getOwnInfos([name], server: server, callback: (datas) {
      if (datas?.isNotEmpty == true) {
        callback?.call(datas!.first);
      }
    });
  }

  Future<void> getOwnInfos(List<String> names,
      {bool server = false, ValueChanged<List<MemberInfo>?>? callback}) async {
    if (names.isEmpty) return;

    List<String> nameCopys = [];
    List<String> nameRemoves = [];
    nameCopys.addAll(names);

    Map<String, MemberContactInfo> mapContactInfo = {};

    var db = Get.find<AppDatabase>();
    var conf = Get.find<AppConfigService>();
    var username = conf.getUserName();

    if (nameCopys.contains(username)) {
      var model = AvatarModel.fromJson(conf.getMySelfAvatarInfo() ?? {});

      mapContactInfo[username!] = MemberContactInfo(
        username,
        displayname: conf.getMySelfDisplayName(),
        avatarUrl: model.avatarUrl,
        avatarPath: model.path,
        state: ContactState.notFriend.index,
      );
    }

    for (var element in nameCopys) {
      if (element == username) continue;

      var contactData = await db.oneContact(element).getSingleOrNull();
      AppLogger.d('havaTid 0  isTid=${contactData?.isTid}');

      mapContactInfo[element] = MemberContactInfo(
        element,
        displayname: contactData?.displayname,
        localname: contactData?.localname,
        avatarUrl: contactData?.avatarUrl,
        avatarPath: appSupporAbsolutePathToPath(contactData?.avatarPath),
        state: contactData?.state,
        isTid: contactData?.isTid,
      );
    }
    for (var e in nameRemoves) {
      nameCopys.remove(e);
    }
    if (nameCopys.isEmpty) {
      return;
    }
    callback?.call(_toMemberInfo(mapContactInfo.values.toList()));

    /// 从服务器获取最新数据
    var infos = await getOtherInfo(nameCopys);
    if (infos == null || infos.isEmpty) {
      return;
    }

    List<ContactCompanion> dbCompanions = [];
    for (var element in infos) {
      String name = element.name ?? '';
      var infoData = mapContactInfo[name];

      bool download = true;
      // 本地有数据，则以本地为准
      bool hasFile =
          File(appSupporAbsolutePath(infoData?.avatarPath ?? '') ?? '')
              .existsSync();
      if (username == name &&
          infoData?.avatarPath?.isNotEmpty == true &&
          !hasFile) {
        download = false;
      }
      String? avatarPath;
      if (element.avatar?.isNotEmpty == true &&
          ((element.avatar != infoData?.avatarUrl && download) || !hasFile)) {
        var savePath =
            avatarSavePath(userName: element.name ?? '', fileName: '');
        avatarPath = await downloadFile(
          element.avatar,
          savePath: savePath,
          isComPressImageJPG: true,
        );
        if(avatarPath==null&&server && names.length == 1){
          ContactTask.request(names.first);
        }
      } else {
        if (server && names.length == 1) {
          ContactTask.request(names.first);
        }
      }

      if (username == name) {
        // 本地无数据，则更新
        if (!(infoData?.displayname?.isNotEmpty == true)) {
          conf.saveMySelfDisplayName(element.nickname ?? '');
        }

        if (avatarPath != null) {
          var data = AvatarModel(
            path: appSupporAbsolutePathToPath(avatarPath),
            avatarUrl: element.avatar,
          ).toJson();
          conf.saveMyselfAvatarInfo(data);
        }
      } else {
        var havaTid = await Get.find<DidApi>().findTidByAccount(name);

        /// 如果本地存的联系人名称及头像url，有变化则入库
        AppLogger.d('havaTid =$havaTid |||${infoData?.isTid}');
        if (infoData?.displayname != element.nickname || avatarPath != null || (havaTid!=null&&havaTid!=infoData?.isTid)) {
          AppLogger.d('havaTid isTid=${havaTid??infoData?.isTid}');

          var companion = ContactCompanion.insert(
            username: name,
            avatarPath: ofNullable(appSupporAbsolutePathToPath(avatarPath)),
            avatarUrl: avatarPath == null
                ? ofNullable(null)
                : ofNullable(element.avatar),
            displayname: ofNullable(element.nickname),
            state: ofNullable(infoData?.state),
            isTid:ofNullable(havaTid??infoData?.isTid),
            createTime: ofNullable(TimeTask.instance.getNowTime().toDouble()),
            updateTime: ofNullable(TimeTask.instance.getNowTime().toDouble()),
          );
          AppLogger.d('havaTid =$havaTid |||1${companion.isTid}');

          dbCompanions.add(companion);
        }
      }

      mapContactInfo[name]?.displayname = element.nickname;

      if (avatarPath != null) {
        mapContactInfo[name]?.avatarPath =
            appSupporAbsolutePathToPath(avatarPath);
      }
    }
    callback?.call(_toMemberInfo(mapContactInfo.values.toList()));

    for (var element in dbCompanions) {
      var info = mapContactInfo[element.username.value];
      AppLogger.d('havaTid = ${element.isTid.value}|||2${info?.isTid}');

      // 更新会话名称/头像
      SessionTask.name(element.username.value,
          displayname: info?.state == ContactState.friend.index
              ? info?.localname ?? info?.displayname
              : info?.displayname,
          isTid:element.isTid.value??info?.isTid,
          avatarPath: appSupporAbsolutePathToPath(info?.avatarPath));

      db.updateContactData(element);
    }
  }

  Future<Map<String, MemberContactInfo>> getDBMemberData(
    String channelID,
    List<String> members,
  ) async {
    if (channelID.isEmpty || members.isEmpty) {
      return {};
    }

    List<String> memberCopys = [];
    memberCopys.addAll(members);

    var conf = Get.find<AppConfigService>();
    var username = conf.getUserName();
    var model = AvatarModel.fromJson(conf.getMySelfAvatarInfo() ?? {});

    var db = Get.find<AppDatabase>();
    Map<String, MemberContactInfo> mapMemberContactInfo = {};

    void updateSelf(String name) {
      if (name == username) {
        mapMemberContactInfo[username]?.displayname =
            conf.getMySelfDisplayName();
        mapMemberContactInfo[username]?.avatarUrl = model.avatarUrl;
        mapMemberContactInfo[username]?.avatarPath = model.path;
      }
    }

    /// 查找存在的成员数据
    var dbResults = await db.memberContactDatas(channelID, members).get();
    for (var element in dbResults) {
      memberCopys.remove(element.username);
      mapMemberContactInfo[element.username] = MemberContactInfo(
        element.username,
        nickname: element.nickname,
        displayname: element.displayname,
        localname: element.localname,
        avatarUrl: element.avatarUrl,
        avatarPath: element.avatarPath,
        state: element.state,
      );

      updateSelf(element.username);
    }

    /// 余下的从联系人表查找
    for (var element in memberCopys) {
      var contactData = await db.oneContact(element).getSingleOrNull();
      mapMemberContactInfo[element] = MemberContactInfo(
        element,
        displayname: contactData?.displayname,
        localname: contactData?.localname,
        avatarUrl: contactData?.avatarUrl,
        avatarPath: contactData?.avatarPath,
        state: contactData?.state,
      );
      updateSelf(element);
    }
    return mapMemberContactInfo;
  }

  /// 监听事件
  void _listenEvent() {
    var bus = Get.find<EventBus>();
    bus.on<SyncChannelsEvent>().listen((event) {
      getChannelsRequest().then((channels) {
        ChannelTask.clearInvalidChannels();
        ChannelTask.channels(channels, isAll: true);
        if (channels?.isNotEmpty == true) {
          for (var e in channels!) {
            if (e.id?.isNotEmpty == true) {
              Config.channelNumbers[e.id ?? ''] = e.memberCount;
            }
          }
        }
      });
    });

    bus.on<SyncChannelMessageEvent>().listen((event) {
      Get.find<AppDatabase>().allChannelByState(1).get().then((channels) async {
        AppLogger.d('获取频道消息 channels=${channels.length}');
        if (channels.isEmpty) {
          Get.find<EventBus>()
              .fire(SessionNoticeEvent(SessionNoticeType.channelMsg, true));
          return;
        }
        List<String> channelIds = [];
        for (var e in channels) {
          channelIds.add(e.channelId);
        }
        var id = Get.find<AppConfigService>().currSessionID;
        List channls = [];
        var channelSessions = await Get.find<AppDatabase>()
            .allChannelSessionDatas(channelIds)
            .get();
        for (var element in channels) {
          if (id == element.channelId) continue;
          var maxUuid = element.maxMsgUuid;
          if (channelSessions.isNotEmpty) {
            try {
              var uuid = channelSessions
                  .firstWhereOrNull((e) => element.channelId == e.username)
                  ?.uuid;
              AppLogger.d('获取频道消息 uuid=$uuid');
              maxUuid = uuid ?? maxUuid;
            } catch (e) {}
          }
          AppLogger.d('获取频道消息 maxUuid=$maxUuid');
          if (maxUuid?.isEmpty == true) {
            channls.add({'channel_id': element.channelId});
          } else {
            channls.add({'channel_id': element.channelId, 'id': maxUuid});
          }
        }
        var c = 10;
        int count = channls.length ~/ c;
        if (count == 0) {
          ChannelTask.getMessgae(
            channls,
            5,
          );
        } else {
          for (int i = 0; i < count; i++) {
            int? end = (i == count - 1) ? null : (i + 1) * c;
            ChannelTask.getMessgae(
              channls.sublist(i * c, end),
              5,
            );
          }
        }
        Get.find<EventBus>()
            .fire(SessionNoticeEvent(SessionNoticeType.channelMsg, true));
      });
    });
  }

  List<MemberInfo> _toMemberInfo(List<MemberContactInfo> infos) {
    List<MemberInfo> memberDatas = [];

    for (var element in infos) {
      String? nickname = element.nickname;
      if (nickname == null || nickname.isEmpty) {
        nickname = element.state == ContactState.friend.index
            ? element.localname ?? element.displayname
            : element.displayname;
      }

      if (nickname == null || nickname.isEmpty) {
        if (element.username.length > 6) {
          nickname = 'u${element.username.substring(0, 6)}';
        } else {
          nickname = element.username;
        }
      }

      var data = MemberInfo(
        element.username,
        nickname: nickname,
        avatarPath: appSupporAbsolutePath(element.avatarPath),
        state: element.state,
        displayname: element.displayname,
        isTid:element.isTid,
      );
      memberDatas.add(data);
    }

    return memberDatas;
  }

  void updateAvatar(TaskData data) {
    if (data.datas.isEmpty) {
      return;
    }

    var db = Get.find<AppDatabase>();
    for (var element in data.datas) {
      // 更新会话名称/头像
      SessionTask.name(element.username,
          avatarPath: appSupporAbsolutePathToPath(element.savePath));

      var companion = ContactCompanion.insert(
        username: element.username,
        avatarPath: ofNullable(appSupporAbsolutePathToPath(element.savePath)),
        avatarUrl: ofNullable(element.url),
        createTime: ofNullable(TimeTask.instance.getNowTime().toDouble()),
        updateTime: ofNullable(TimeTask.instance.getNowTime().toDouble()),
      );

      db.updateContactData(companion);
    }
  }

  void deleteMessageByChannelId(bool check,String channelId) {
    ChannelTask.updateChannelMinUuid(channelId);
    Get.find<EventBus>()
        .fire(ClearChatHistoryEvent(channelId));
    SessionTask.delete(channelId,null,null);
    Get.find<AppDatabase>().topMsgIdByUserNameAdmin(channelId).get().then((value){
      deleteMessageAndFile(channelId,clearTop: check,noDelMsgIds: value).then((value) {
        Get.find<EventBus>()
            .fire(ClearChatHistoryEvent(channelId));
      });
    });
  }


}
