import 'dart:convert';

import 'package:flutter/services.dart';
import 'package:get/get.dart';

class AppTranslations extends Translations {
  final Map<String, String>? enUS;
  final Map<String, String>? zhCN;
  final Map<String, String>? zhTW;
  final Map<String, String>? jaJP;
  final Map<String, String>? koKR;
  final Map<String, String>? viVN;
  final Map<String, String>? ptBR;
  final Map<String, String>? msMY;

  AppTranslations({
    this.enUS,
    this.zhCN,
    this.zhTW,
    this.jaJP,
    this.koKR,
    this.viVN,
    this.ptBR,
    this.msMY,
  });

  static AppTranslations fromJson(dynamic json) {
    return AppTranslations(
      enUS: json["en_US"] != null ? Map.from(json["en_US"]) : {},
      zhCN: json["zh_CN"] != null ? Map.from(json["zh_CN"]) : {},
      zhTW: json["zh_TW"] != null ? Map.from(json["zh_TW"]) : {},
      jaJP: json["ja_JP"] != null ? Map.from(json["ja_JP"]) : {},
      koKR: json["ko_KR"] != null ? Map.from(json["ko_KR"]) : {},
      viVN: json["vi_VN"] != null ? Map.from(json["vi_VN"]) : {},
      ptBR: json["pt_BR"] != null ? Map.from(json["pt_BR"]) : {},
      msMY: json["ms_MY"] != null ? Map.from(json["ms_MY"]) : {},
    );
  }

  @override
  Map<String, Map<String, String>> get keys => {
        "en_US": enUS ??
            {
              'official_name': 'English',
            },
        "zh_CN": zhCN ??
            {
              'official_name': '简体中文',
            },
        "zh_TW": zhTW ??
            {
              'official_name': '繁體中文',
            },
        "ja_JP": jaJP ??
            {
              'official_name': '日本語',
            },
        "ko_KR": koKR ??
            {
              'official_name': '한국어',
            },
        "vi_VN": viVN ??
            {
              'official_name': 'Tiếng Việt',
            },
        "pt_BR": ptBR ??
            {
              'official_name': 'Português',
            },
        "ms_MY": msMY ??
            {
              'official_name': 'Bahasa Melayu',
            },
      };
}

class TranslationService {
  Map<String, Map<String, String>> translationMap = {};
  final languageList = [
    'follow_the_system',
    'en_US',
    'zh_CN',
    'zh_TW',
    'ja_JP',
    'ko_KR',
    'vi_VN',
    'pt_BR',
    'ms_MY',
  ];
  final languageNameList = [
    'follow_the_system'.tr,
    'English',
    '简体中文',
    '繁體中文',
    '日本語',
    '한국어',
    'Tiếng Việt',
    'Português',
    'Bahasa Melayu',
  ];
  Future<Translations> init() async {
    // get all json file
    const path = 'lib/core/languages/locales/';
    // read json file
    for (String langCode in languageList.sublist(1)) {
      var jsonPath = path + langCode + '.json';
      var key = langCode;
      final String response = await rootBundle.loadString(jsonPath);
      final Map<String, dynamic> jsonData = json.decode(response);
      translationMap[key] = Map.from(jsonData);
    }
    return AppTranslations.fromJson(translationMap);
  }

  // read json
  Future readLocalJson(String path) async {
    final String response = await rootBundle.loadString(path);
    return json.decode(response);
  }
}
