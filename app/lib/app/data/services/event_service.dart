/*
 * <AUTHOR> <PERSON><PERSON>
 * @Date         : 2022-05-20 11:28:20
 * @Description  : 广播消息相关
 * @LastEditors  : <PERSON><PERSON>
 * @LastEditTime : 2022-07-15 10:18:14
 * @FilePath     : /flutter_metatel/lib/app/data/services/event_service.dart
 */
import 'package:flutter_metatel/app/data/models/message_model.dart';
import 'package:flutter_metatel/core/utils/events_bus.dart';
import 'package:get/get.dart';

import '../enums/enum.dart';

class EventService extends GetxService {
  Future<EventBus> init() async {
    return EventBus();
  }
}

class MessageEvent {
  String msgId;
  String owner;
  String? from;
  String? body;
  MessageType type;

  ChatType chatType;
  late int direction;

  String? filePath;
  String? fileFragment;
  String? fileName;
  int? fileSize;
  String? thumbnailPath;
  String? thumbnailFragment;
  BubbleItemState? state;
  bool isUndo;
  DateTime dateTime;
  String? uuid;
  String? ext1;
  List<String>? at;
  bool selfDestruct;
  int countdown; // 阅后即焚倒计时
  List<String>? toNumbers;
  String? replayMsg;
  String? resourceUuid;
  String? replayFilePath;
  String? replayFileFragment;
  String? replayMsgId;
  bool? replayClicked;
  bool? hasShown;
  bool? messageHasRead;
  int? fileDuration;
  bool? undoEdit;
  String? fileUrl;
  String? thumbnailUrl;
  int? fileState;
  MessageExpand? expand;
  int? thumbnailFileState;
  bool? activePlay;
  List<double>? noises;
  String? sendDisplayName; ///发送者的昵称
  bool? hasIdentify;//二维码识别
  String? translateMsg;//翻译消息
  String? extension;//扩展字段
  MessageEvent(
    this.msgId, {
    required this.owner,
    this.from,
    this.body,
    this.type = MessageType.date,
    required this.chatType,
    this.direction = 1,
    this.filePath,
    this.fileFragment,
    this.fileName,
    this.fileSize,
    this.thumbnailPath,
    this.thumbnailFragment,
    this.state = BubbleItemState.LOAD,
    this.isUndo = false,
    required this.dateTime,
    this.uuid,
    this.ext1,
    this.at,
    this.selfDestruct = false,
    this.countdown = 0,
    this.toNumbers,
    this.replayMsg,
    this.resourceUuid,
    this.replayFilePath,
    this.replayFileFragment,
    this.replayMsgId,
    this.hasShown,
    this.messageHasRead,
    this.fileDuration,
    this.undoEdit,
    this.fileUrl,
    this.fileState,
    this.thumbnailUrl,
    this.expand,
    this.thumbnailFileState,
    this.activePlay,
    this.noises,
    this.hasIdentify,
    this.translateMsg,
    this.extension,
  });
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is MessageEvent &&
          runtimeType == other.runtimeType &&
          msgId == other.msgId;

  @override
  int get hashCode => hashCode;
}
