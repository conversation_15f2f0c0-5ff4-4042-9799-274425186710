/*
 * <AUTHOR> <PERSON><PERSON>
 * @Date         : 2022-04-21 10:42:08
 * @Description  : 应用程序配置相关
 * 
 * @LastEditors  : <PERSON><PERSON>
 * @LastEditTime : 2022-09-16 23:31:19
 * @FilePath     : /flutter_metatel/lib/app/data/services/config_service.dart
 */



import 'package:flutter_metatel/core/values/keys.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:get/get.dart';


class SecureStoreService extends GetxService {
  Future<SecureStoreService> init() async {
    storage = const FlutterSecureStorage(
        aOptions: AndroidOptions(encryptedSharedPreferences: true));
    return this;
  }

  late FlutterSecureStorage storage;


  Future<void> secureStoreSaveMnemonic(String mnemonic) async {
    await storage.write(key: SECURE_STORAGE_MNEMONIC, value: mnemonic);
  }
  Future<void> secureStoreSaveUserAddressMex(String userAddressMex) async {
    await storage.write(
        key: SECURE_STORAGE_USER_ADDRESS_MEX, value: userAddressMex);
  }
  Future<void> secureStoreSaveWalletPwd(String pwd) async {
    await storage.write(
        key: WALLET_PWD, value: pwd);
  }

  Future<String?> secureStoreReadMnemonic() async {
    var mnemonic = await storage.read(key: SECURE_STORAGE_MNEMONIC);
    return mnemonic;
  }
  Future<String?> secureReadUserAddressMex() async {
    var userAddressMex =
    await storage.read(key: SECURE_STORAGE_USER_ADDRESS_MEX);
    return userAddressMex;
  }
  Future<String?> secureReadWalletPwd() async {
    var pwd =
    await storage.read(key: WALLET_PWD);
    return pwd;
  }
  Future<void> secureStoreSavePin(String pin) async {
    await storage.write(key: SECURE_STORAGE_PIN, value: pin);
  }
  Future<String?> secureReadPin() async {
    var pin =
    await storage.read(key: SECURE_STORAGE_PIN);
    return pin;
  }
}
