//FileName connect_service
// <AUTHOR>
//@Date 2022/6/22 16:48
import 'dart:async';

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter_metatel/app/data/services/chatio_service.dart';
import 'package:flutter_metatel/app/data/services/config_service.dart';
import 'package:flutter_metatel/app/modules/home/<USER>';
import 'package:flutter_metatel/core/task/time_task.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:flutter_metatel/core/utils/events_bus.dart';
import 'package:get/get.dart';

import '../../../core/utils/util.dart';
import '../../../core/values/config.dart';
import '../events/events.dart';

class NetWorkConnectService extends GetxService {
  StreamSubscription? subscription;
  bool isNet=false;


  Future<void> checkNetwork() async{
    var connectivityResult = await (Connectivity().checkConnectivity());
    if (connectivityResult == ConnectivityResult.none) {
      Get.find<EventBus>()
          .fire(SessionNoticeEvent(SessionNoticeType.noNetwork, false));
    }else{
      Get.find<EventBus>()
          .fire(SessionNoticeEvent(SessionNoticeType.noNetwork, true));
    }
  }

  Future<NetWorkConnectService> init() async {
    subscription = Connectivity()
        .onConnectivityChanged
        .listen((ConnectivityResult result) async{
          AppLogger.d("onConnectivityChanged listen result==$result");
      if (result == ConnectivityResult.none) {
        Get.find<EventBus>()
            .fire(SessionNoticeEvent(SessionNoticeType.noNetwork, false));
      } else {
        Get.find<EventBus>()
            .fire(SessionNoticeEvent(SessionNoticeType.noNetwork, true));
      }
      if (result == ConnectivityResult.wifi ||
          result == ConnectivityResult.mobile) {
        if(isConnecting){
          return;
        }
        isConnecting = true;
        TimeTask.instance.freshTime();
        var notFirstAccess = Get.find<AppConfigService>().getNotFirstLogin();
        AppLogger.d("onConnectivityChanged listen notFirstAccess==$notFirstAccess");
        if (notFirstAccess && !ChatioService.isLogined) {
          AppLogger.d("initToken---NetWorkConnectService");
          try {
            initParam();
          } catch (e) {
            AppLogger.d(e.toString());
          }
          isConnecting = false;
        }
      }
    });
    return this;
  }
  bool isConnecting = false;

  Future<bool> networkConnected() async {
    var connectivityResult = await (Connectivity().checkConnectivity());
    if (connectivityResult == ConnectivityResult.mobile ||
        connectivityResult == ConnectivityResult.wifi  ||
        connectivityResult == ConnectivityResult.vpn) {
      return true;
    }
    return false;
  }

  @override
  void onClose() {
    subscription?.cancel();
    super.onClose();
  }
}
