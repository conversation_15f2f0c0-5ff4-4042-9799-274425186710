//FileName get_all_channel_members.util
// <AUTHOR>
//@Date 2023/7/31 14:39

import 'dart:convert';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:dio/io.dart';
import 'package:flutter_metatel/app/data/enums/enum.dart';
import 'package:flutter_metatel/app/data/models/channel_member_info_model.dart';
import 'package:flutter_metatel/app/data/models/channel_members_model.dart';
import 'package:flutter_metatel/app/data/providers/db/database.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:flutter_metatel/core/utils/util.dart';

/// 获取当前频道所有成员
Future<List<GroupMemberCompanion>?> getAllChannelMemberInfoSync(Map map) async {
  String channelID = map['channelID'];
  String username = map['username'];
  String membersApi = map['membersApi'];
  String getMemberApi = map['getMemberApi'];
  String token = map['token'];
  String? proxy = map['proxy'];
  var certBytes = map['proxy'];
  AppLogger.d('getAllChannelMemberInfos 0');
  var data = await _membersRequest(membersApi, token, channelID, username,
      proxy: proxy,certBytes:certBytes);
  if (data == null) return null;
  AppLogger.d('getAllChannelMemberInfos --1==${data.members?.length}');
  if(data.members?.isEmpty??true){
    return null;
  }
  List<GroupMemberCompanion> companions = [];
  if(data.members!.length>5000){

  }
  var c = 4000;
  int count = data.members!.length ~/ c;
  int d = data.members!.length % c;
  if (d > 0) {
    count = count + 1;
  }
  if (count == 0) {
    await _getMemberInfoRe(data, companions, getMemberApi, token, channelID,
        username, data.members,
        proxy: proxy);
  } else {
    for (int i = 0; i < count; i++) {
      int? end = (i == count - 1) ? null : (i + 1) * c;
      await _getMemberInfoRe(data, companions, getMemberApi, token, channelID,
          username, data.members!.sublist(i * c, end),
          proxy: proxy);
    }
  }

  AppLogger.d('getAllChannelMemberInfos companions==${companions.length}');
  return companions;
}

_getMemberInfoRe(ChannelMembersData data,List<GroupMemberCompanion> companions,String getMemberApi, String token, String channelID, String username, List<String>? members,
    {String? proxy}) async {
  var info = await _memberInfoRequest(
      getMemberApi, token, channelID, username, members,
      proxy: proxy);
  if (info == null) return null;
  AppLogger.d('getAllChannelMemberInfos 1==${info.length}');
  for (var element in info) {
    int role = ChannelMemberRole.ordinary.index;
    if (element.username?.isNotEmpty == true &&
        data.owner == element.username) {
      role = ChannelMemberRole.owner.index;
    } else if (data.admins?.contains(element.username) == true) {
      role = ChannelMemberRole.administrator.index;
    }
    companions.add(GroupMemberCompanion.insert(
      groupId: channelID,
      username: element.username ?? '',
      displayname: ofNullable(element.nickname),
      memberUuid: channelID + (element.username ?? ''),
      role: ofNullable(role),
      tags: ofNullable(json.encode(element.tags)),
    ));
  }

}

Dio _createBaseDio(BaseOptions options,String?proxy, List<int>? certBytes){
  var _dio= Dio();
  _dio.options=options;
   AppLogger.d('net request proxy=$proxy');
  _dio.interceptors.add( InterceptorsWrapper(
      onResponse: (onResponse, handler) {
        return handler.next(onResponse);

      },
      onError: (error ,handler){
        AppLogger.d('BaseDioClient onError =${error.error} response=${error.response.toString()}');
        AppLogger.d('BaseDioClient onError message=${error.message}');
        AppLogger.d('BaseDioClient onError code=${error.response?.statusCode}');
        return handler.next(error);
      }
  ));
  _dio.httpClientAdapter = IOHttpClientAdapter(
    createHttpClient: () {
      SecurityContext sc = SecurityContext();
      // var path = '${appSupportDir!.path}/tttRootCa.pem';
      // AppLogger.i("net request ${File(path).existsSync()}");
      if(certBytes!=null){
        sc.setTrustedCertificatesBytes(certBytes);
      }
      final client = HttpClient(context: sc);
      client.findProxy = (proxy?.isEmpty ?? true)
          ? null
          : (uri) {
        AppLogger.d('net request findProxy=$proxy');
        return 'PROXY $proxy';
      };
      if (proxy?.isNotEmpty ?? false) {
        client.connectionFactory = (url, proxyHost, proxyPort) {
          AppLogger.d(
              'net request url=$url proxyHost=$proxyHost proxyPort=$proxyPort');
          return SecureSocket.startConnect(proxyHost!, proxyPort!,
              onBadCertificate: (cert) {
                AppLogger.d('net onBadCertificate ');
                return true;
              });
        };
      }
      client.badCertificateCallback =
          (X509Certificate cert, String host, int port) {
        AppLogger.d('net request host=$host:$port');
        return true;
      };
      return client;

    },
  );
  return _dio;
}

Future<ChannelMembersData?> _membersRequest(
    String url, String token, String id, String username,
    {String? proxy,List<int>? certBytes}) async {
  Map<String, dynamic> mapData = {};
  mapData['id'] = id;
  mapData['username'] = username;

  Response result;
  try {
    BaseOptions options = BaseOptions();
    options.connectTimeout = const Duration(seconds: 30);
    options.sendTimeout = const Duration(seconds: 30);
    options.contentType = 'application/json; charset=utf-8';
    options.headers = {
      HttpHeaders.authorizationHeader: "Bearer $token",
    };
    result = await _createBaseDio(options,proxy,certBytes).post(
      url,
      data : json.encode(mapData),
    );
  } catch (e) {
    logger.e(e);
    return null;
  }

  if (result.statusCode != 200) {
    logger.e('获取成员列表  request code:${result.statusCode}');
    return null;
  }
  AppLogger.d('获取成员列表 request data==${result.data}');

  var model = ChannelMembersModel.fromJson(result.data);
  if (model.code != 200) {
    logger.e('获取成员列表 response code:${model.code} msg:${model.message}');
    return null;
  }

  return model.data;
}

/// 获取成员信息
Future<List<ChannelMemberInfoData>?> _memberInfoRequest(
    String url, String token, String id, String username, List<String>? members,
    {String? proxy ,List<int>? certBytes}) async {
  if (members == null || members.isEmpty) return null;

  Map<String, dynamic> mapData = {};
  mapData['id'] = id;
  mapData['username'] = username;
  mapData['members'] = members;

  Response result;
  try {
    BaseOptions options = BaseOptions();
    options.connectTimeout = const Duration(seconds: 30);
    options.sendTimeout = const Duration(seconds: 30);
    options.contentType = 'application/json; charset=utf-8';
    options.headers = {
      HttpHeaders.authorizationHeader: "Bearer $token",
    };
    result = await _createBaseDio(options,proxy,certBytes).post(
      url,
      data : json.encode(mapData),
    );
    AppLogger.d("获取成员信息 result=$result"); 
  } catch (e) {
    logger.e(e);
    return null;
  }

  if (result.statusCode != 200) {
    logger.e('获取成员信息  request code:${result.statusCode}');
    return null;
  }

  var model = ChannelMemberInfoModel.fromJson(result.data);
  if (model.code != 200) {
    logger.e('获取成员信息 response code:${model.code} msg:${model.message}');
    return null;
  }

  return model.data;
}

Future<List<GroupMemberData>> removeElememt(Map map) async {
  List<GroupMemberCompanion> members = map['members'];
  List<GroupMemberData> oldDatas = map['oldDatas'];
  for (var element in members) {
    oldDatas.removeWhere((data) => data.username == element.username.value);
  }
  return oldDatas;
}
