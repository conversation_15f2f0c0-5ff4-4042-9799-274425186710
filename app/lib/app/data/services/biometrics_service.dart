import 'dart:io';

import 'package:flutter_metatel/app/data/enums/enum.dart';
import 'package:get/get.dart';
import 'package:local_auth/local_auth.dart';
import 'package:local_auth_android/local_auth_android.dart';

import '../../../core/languages/l.dart';
import '../../../core/utils/app_log.dart';
import 'package:local_auth_ios/local_auth_ios.dart';

class BiometricsService extends GetxService{
  String? biometricsType;
  PayIdentifyType payIdentifySupportType=PayIdentifyType.password;
  final LocalAuthentication auth=LocalAuthentication();
  Future<BiometricsService> configBiometricsType() async {
    final bool canAuthenticateWithBiometrics = await auth.canCheckBiometrics;
    final authDeviceSupported = await auth.isDeviceSupported();
    final canBiometrics =
        canAuthenticateWithBiometrics || await auth.isDeviceSupported();
    if(!canBiometrics){
      biometricsType=null;
      payIdentifySupportType=PayIdentifyType.password;
      return this;
    }
    final List<BiometricType> availableBiometrics =
    await auth.getAvailableBiometrics();
    AppLogger.d(
        "availableBiometrics==$availableBiometrics\ncanAuthenticateWithBiometrics==$canAuthenticateWithBiometrics\nauthDeviceSupported==$authDeviceSupported");
    if(Platform.isAndroid){
      if (availableBiometrics.contains(BiometricType.strong)) {
        biometricsType=L.fingerprint.tr;
        payIdentifySupportType=PayIdentifyType.fingerprint;
      }else{
        biometricsType=null;
        payIdentifySupportType=PayIdentifyType.password;
      }
    }else if(Platform.isIOS){
      if (availableBiometrics.contains(BiometricType.face)){
        biometricsType=L.face_id.tr;
        payIdentifySupportType=PayIdentifyType.faceId;
      }else if(availableBiometrics.contains(BiometricType.fingerprint)){
        biometricsType=L.fingerprint.tr;
        payIdentifySupportType=PayIdentifyType.fingerprint;
      }else{
        biometricsType=null;
        payIdentifySupportType=PayIdentifyType.password;
      }
    }
    return this;
  }
  Future<bool> onBiometrics() async {
    if(biometricsType==null){
      return false;
    }
    bool didAuthenticate=false;
    try{
      didAuthenticate = await auth.authenticate(
          authMessages:  [
            AndroidAuthMessages(
              signInTitle: L.authentication_required.tr,
              cancelButton: L.cancel.tr,
              biometricHint:L.verify_credentials.tr,
              biometricNotRecognized:L.authentication_failed_please_try_again.tr, ///身份验证失败
              biometricRequiredTitle:'${L.chat_info_not_setting.tr}$biometricsType', ///has not set up biometric authentication on their device.
              biometricSuccess:null, ///身份验证成功
              deviceCredentialsRequiredTitle:null,  ///has not set up credentials authentication on their device.
              deviceCredentialsSetupDescription:null, ///device credentials on their device.
              goToSettingsButton:L.module_activity_system_setting_button_text.tr, ///Go to settings
              goToSettingsDescription:null, ///Go to settings 描述
            ),
            IOSAuthMessages(
              cancelButton: L.cancel.tr,
            ),
          ],
          localizedReason: L.please_verify_the_payment_with_message.trParams({'biometricsType':biometricsType??""}),
          options: const AuthenticationOptions(biometricOnly: true));
    }catch(e){
      AppLogger.d(e.toString());
    }
    if(didAuthenticate){
      await Future.delayed(const Duration(seconds: 1));
    }
    return didAuthenticate;
  }
}