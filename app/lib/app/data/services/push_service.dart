/*
 * <AUTHOR> <PERSON><PERSON>
 * @Date         : 2022-06-17 17:09:24
 * @Description  : TODO: Add description
 * @LastEditors  : <PERSON><PERSON>
 * @LastEditTime : 2022-07-01 12:51:39
 * @FilePath     : /flutter_metatel/lib/app/data/services/push_service.dart
 */
/*
 * @Author: your name
 * @Date: 2022-04-29 18:41:34
 * @LastEditTime: 2022-04-29 19:31:11
 * @LastEditors: your name
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \flutter_metatel\lib\app\data\services\push_service.dart
 */
// import 'package:agconnect_core/agconnect_core.dart';
import 'package:flutter_metatel/app/data/models/res/base_res_model.dart';
import 'package:flutter_metatel/core/push/fcm_push_manager.dart';
import 'package:flutter_metatel/core/push/voip_push_manager.dart';
import 'package:flutter_metatel/core/push/xiaomi_push_manager.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:get/get.dart';

import '../../../core/push/apns_push_manager.dart';
import '../../../core/push/honour_push_manager.dart';
import '../../../core/push/huawei_push_manager.dart';
import '../../../core/push/oppo_push_manager.dart';
import '../../../core/push/push_token_model.dart';
import '../../../core/push/vivo_push_manager.dart';
import '../../../core/utils/device_util.dart';
import '../../../core/values/config.dart';
import '../providers/api/api.dart';
import 'chatio_service.dart';
import 'notification_service.dart';

class PushService extends GetxService implements IBackToken {
  static const pushEnableLog=true;
  Map<String,PushTokenModel> tokenMap={};
  BasePush? basePush;
  BasePush? baseVoipPush;
  int count = 0;

  Future<PushService> init() async {
    AppLogger.d('PushService start');
    if (DeviceUtil.isIOS()) {
      intApnsPush();
      if (Config.isVoipPush) {
        // initVoipPush();
      }
    } else {
      initFcmPush();
    }
    return this;
  }

  void intHuaweiPush() {
    basePush = HuaweiPushManager();
    basePush?.initState(this);
  }

  void intApnsPush() {
    basePush = ApnsPushManager();
    basePush?.initState(this);
  }

  void initFcmPush() {
    basePush = FcmPushManager();
    basePush?.initState(this);
  }

  void initXiaomiPush() {
    basePush = XiaomiPushManager();
    basePush?.initState(this);
  }

  void initOppoPush() {
    basePush = OppoPushManager();
    basePush?.initState(this);
  }

  void initVivoPush() {

    basePush = VivoPushManager();
    basePush?.initState(this);
  }

  // void initVoipPush(){
  //   baseVoipPush = VoipPushManager();
  //   baseVoipPush?.initState(this);
  // }
  void initHonourPush() {
    basePush = HonourPushManager();
    basePush?.initState(this);
  }

  void clearAllNotifications() {
    basePush?.clearAllNotifications();
    baseVoipPush?.clearAllNotifications();
    AppLogger.d('clearAllNotifications 1');
    Get.find<NotificationService>().cancelAllNotifications();
    AppLogger.d('clearAllNotifications 2');

  }
  void setBackgroundMessageHandler() {
    if (basePush is HuaweiPushManager) {
      (basePush as HuaweiPushManager).setBackgroundMessageHandler();
    }
  }

  @override
  back(PushTokenModel tokenModel) {
    AppLogger.d('_push token type:${tokenModel.type} pushPlatform:${tokenModel.pushPlatform} token:${tokenModel.token}');
    if(tokenModel.token.isNotEmpty) {
      tokenMap[tokenModel.type] = tokenModel;
    }
    pushToken();
  }

  void pushToken() async {
    if (tokenMap.isEmpty) {
      await Future.delayed(const Duration(seconds: 2));
      basePush?.getToken();
      baseVoipPush?.getToken();
      return;
    }
    _push(count = 0);
  }

  void _push(int count) async {
    if (count > 2) {
      return;
    }
    try {
      var isLogin = ChatioService.isLogined;
      if (isLogin) {
        Get.find<ApiProvider>().putToken(tokenMap).catchError((e) {
          AppLogger.e(e.toString());
          Future.delayed(const Duration(seconds: 2), () {
            count++;
            AppLogger.d('pushToken token===error');
            _push(count);
          });
          return Response(
            body: BaseRes(),
          );
        });
      }
    }catch(e){
      AppLogger.e(e.toString());
    }
  }
}

abstract class IBackToken {
  back(PushTokenModel pushTokenModel);
}

abstract class BasePush {
  void getToken();

  void initState(IBackToken ibackToken);

  void clearAllNotifications();

  void unRegister();

  String tokenType();
}
