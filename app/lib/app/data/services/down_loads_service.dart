//FileName DownLoadService
// <AUTHOR>
//@Date 2023/4/12 11:40
import 'dart:async';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:flutter_metatel/core/utils/events_bus.dart';
import 'package:flutter_metatel/core/utils/util.dart';
import 'package:get/get.dart';

import '../../../core/task/down_load_file_task.dart';
import 'event_service.dart';

class FileData {
  final String username;
  final String url;
  final String savePath;

  const FileData(this.username, this.url, this.savePath);
}

class TaskData {
  final String own;
  final List<FileData> datas;

  const TaskData(this.own, this.datas);
}

/// 这个类以后想改造到单独隔离区运行也可以。只不过执行的功能不能使用主隔离区的内存
class FileDownLoadTask {
  /// 原始数据
  final TaskData _data;

  /// 剩余下载数
  int _count = 0;

  /// 异步对象
  Completer<TaskData>? _completer;

  /// 有效结果
  late TaskData _result;

  FileDownLoadTask(this._data) {
    _count = _data.datas.length;
    _result = TaskData(_data.own, []);
  }

  Future<TaskData> run() async {
    _completer = Completer();
    _download();
    return _completer!.future;
  }

  void _download() async {
    if (_data.datas.isEmpty) {
      _counter();
      return;
    }

    for (var element in _data.datas) {
      try {
        /// 如果存在不再下载
        if (File(element.savePath).existsSync()) {
          _result.datas.add(element);
          _counter();
          continue;
        }

        downloadFile(
          element.url,
          savePath: element.savePath,
          isComPressImageJPG: true,
        ).then((value) {
          if (value?.isNotEmpty == true) {
            _result.datas.add(FileData(element.username, element.url, value!));
          }

          _counter();
        });
      } catch (e) {
        _counter();
      }
    }
  }

  void _counter() {
    --_count;
    if (_count > 0) {
      return;
    }
    _completer?.complete(_result);
  }
}

class DownLoadService extends GetxService {
  static const tag = 'DownLoadService';

  /// 退出下载的key
  final List<String> _quitOwns = [];

  /// 队列
  final List<TaskData> _queue = [];

  /// 队列空等待
  Completer? _emptyMonitor;

  /// 是否开启循环
  bool _isStartLoop = false;

  /// 最大同时下载数
  final int _maxSyncNum = 10;

  Future<DownLoadService> init() async {
    return this;
  }

  Map<String, DownLoadFileTask> loadPathMap = {};

  Future<void> loadFile(MessageEvent event,
      {bool? updateUi = true,
      bool downThumbnail = false,
      ProgressCallback? onProgressBack,
      DownLoadFileEndCallBack? onEndBack,
      String? saveDirPath,
      bool mergeMsg = false}) async {
    String fileUrl =
        downThumbnail ? event.thumbnailUrl ?? '' : event.fileUrl ?? '';
    AppLogger.d('$tag fileUrl=$fileUrl ');
    AppLogger.d('$tag loadPathMap=${loadPathMap.toString()} ');
    if (fileUrl.isNotEmpty) {
      String key = fileUrl + event.msgId;
      if (loadPathMap.containsKey(key)) {
        var downTask = loadPathMap[key];
        downTask?.setProgressBack(onProgressBack, onEndBack);
      } else {
        var downTask = DownLoadFileTask();
        loadPathMap[key] = downTask;
        await downTask.loadFile(
          event,
          updateUi: updateUi,
          downThumbnail: downThumbnail,
          onProgressBack: onProgressBack,
          saveDirPath: saveDirPath,
          onEndBack: onEndBack,
          isMergeMsg: mergeMsg,
        );
        loadPathMap.remove(key);
      }
    }
  }

  void quit(String own) {
    if (!_quitOwns.contains(own)) {
      _quitOwns.add(own);
    }
  }

  void add(TaskData data) {
    if (data.datas.isEmpty) {
      return;
    }

    if (!_isStartLoop) {
      _isStartLoop = true;
      _startLoop();
    }

    _queue.add(data);

    if (!(_emptyMonitor?.isCompleted ?? true)) {
      _quitOwns.clear();
      _emptyMonitor?.complete();
    }
  }

  Future<void> _startLoop() async {
    while (true) {
      if (_quitOwns.isNotEmpty) {
        List<TaskData> dels = [];
        for (var element in _queue) {
          if (_quitOwns.contains(element.own)) {
            dels.add(element);
          }
        }

        for (var element in dels) {
          _queue.remove(element);
        }
        _quitOwns.clear();
      }

      if (_queue.isEmpty) {
        _emptyMonitor = Completer();
        await _emptyMonitor!.future;
        _emptyMonitor = null;
      }

      var own = _queue[0].own;
      TaskData taskData = TaskData(own, []);

      /// 最大同时下载数，由_maxSyncNum控制
      if (_queue[0].datas.length > _maxSyncNum) {
        for (var i = 0; i < _maxSyncNum; i++) {
          taskData.datas.add(_queue[0].datas.removeAt(0));
        }
      } else {
        taskData = _queue.removeAt(0);
      }

      taskData = await FileDownLoadTask(taskData).run();
      Get.find<EventBus>().fire(taskData);
    }
  }
}
