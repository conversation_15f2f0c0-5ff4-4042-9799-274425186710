//FileName webrtc_service
// <AUTHOR>
//@Date 2022/6/7 10:50
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:flutter_metatel/core/utils/events_bus.dart';
import 'package:flutter_metatel/webrtc/core/bean/webrtc_bean.dart';
import 'package:flutter_metatel/webrtc/core/webrtc_config.dart';
import 'package:get/get.dart';

import '../../../core/utils/audio_play_helper.dart';
import '../../../webrtc/core/bean/webrtc_event.dart';
import '../../../webrtc/core/state/call_state.dart';
import '../../../webrtc/core/webrtc_call.dart';
import '../../../webrtc/core/webrtc_listener.dart';

class WebRtcService extends GetxService implements WebRtcCallStateListener{
  IWebRtcCall?iWebRtcCall;
  Future<WebRtcService> init() async{
    AudioPlayHelper.instance;
    iWebRtcCall=WebRtcCallImpl(this);
    return this;
  }
  bool isCalling(){
    return iWebRtcCall?.getCallState()!=CallState.idle;
  }

  bool isVideoCalling() {
    return isCalling() && _isVideo();
  }

  bool _isVideo() {
    return iWebRtcCall?.isVideo() ?? false;
  }
  bool isRunning(){
    return iWebRtcCall?.getCallState()==CallState.incomingRinging;
  }

  bool isConnected(){
    return iWebRtcCall?.getCallState()==CallState.connected;
  }
  bool getSpeakEnable() {
    bool isOpen= iWebRtcCall?.getSpeakEnable() ?? false;
    return isOpen;
  }
  void setSpeaker(bool open ){
    iWebRtcCall?.setSpeaker(open);
  }

  void startWait(){
    iWebRtcCall?.startWait();
  }
  @override
  void webRtcCallState(IWebRtcCall iWebRtcCall, CallState state, WebRtcBean? bean) {
    AppLogger.i("state webRtcCallState=$state");
    IWebRtcCallState? call = iWebRtcCall.getDirection() == CallDirection.outgoing ?
    IWebRtcCallState.getOutCallState(state, iWebRtcCall) : IWebRtcCallState.getInComingCallState(state, iWebRtcCall);
    if (call != null) {
      call.call(iWebRtcCall, iWebRtcCall.getProcessID(), iWebRtcCall.getDirection() == CallDirection.incoming);
    }
    Get.find<EventBus>().fire(WebRtcEvent(state));
  }
}