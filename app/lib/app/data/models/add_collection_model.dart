class AddCollectionModel {
  int? code;
  String? msg;
  List<dynamic>? data;

  AddCollectionModel({this.code, this.msg, this.data});

  AddCollectionModel.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    msg = json['msg'];
    data = json['data'];
  }

  Map<String, dynamic> toJson() {
    final mapData = <String, dynamic>{};
    mapData['code'] = code;
    mapData['msg'] = msg;
    mapData['data'] = data;

    return mapData;
  }
}
