/*
 * <AUTHOR> <PERSON><PERSON>
 * @Date         : 2022-05-20 14:33:59
 * @Description  : TODO: Add description
 * @LastEditors  : <PERSON><PERSON>
 * @LastEditTime : 2022-05-20 14:54:32
 * @FilePath     : /flutter_metatel/lib/app/data/models/select_contact_model.dart
 */
import '../enums/enum.dart';

class SelectContactInfo {
  SelectContactInfo({required this.type, this.contacts = const []});
  final ChatType type;
  final List<ContactInfo> contacts;
}

class ContactInfo {
  ContactInfo({
    required this.userName,
    this.displayName,
    this.localName,
    this.avatarPath,
    this.avatarUrl,
    this.fragment,
    this.chatType=ChatType.singleChat,
    this.type,
  });

  final String userName;
  final String? displayName;
  final String? localName;
  final String? avatarPath;
  final String? avatarUrl;
  final String? fragment;
  final ChatType? chatType;
  final int? type;
}
