class PreSignModel {
  String? signUrl;
  String? targetUrl;

  PreSignModel({
    this.signUrl,
    this.targetUrl,
  });
  PreSignModel.fromJson(Map<String, dynamic> json) {
    signUrl = json['signUrl']?.toString();
    targetUrl = json['targetUrl']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['signUrl'] = signUrl;
    data['targetUrl'] = targetUrl;
    return data;
  }
}