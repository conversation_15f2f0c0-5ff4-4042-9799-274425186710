class MobileVerifyModel {
  String? keyboxid;
  String? signature;
  int? timestamp;
  String? verifyurl;
  String? type;

  MobileVerifyModel(
      {this.keyboxid,
      this.signature,
      this.timestamp,
      this.verifyurl,
      this.type});

  MobileVerifyModel.fromJson(Map<String, dynamic> json) {
    keyboxid = json['keyboxid'];
    signature = json['signature'];
    timestamp = json['timestamp'];
    verifyurl = json['verifyurl'];
    type = json['type'];
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['keyboxid'] = keyboxid;
    data['signature'] = signature;
    data['timestamp'] = timestamp;
    data['verifyurl'] = verifyurl;
    data['type'] = type;
    return data;
  }
}
