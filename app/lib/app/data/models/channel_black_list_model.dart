//FileName channel_black_list_model
// <AUTHOR>
//@Date 2023/8/22 18:35
class ChannelBlackListModel{
  String? channelId;
  String? username;
  ChannelBlackListModel({this.channelId,this.username});
  ChannelBlackListModel.fromJson(Map<String, dynamic> json) {
    channelId = json['channel_id'];
    username = json['username'];
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['channel_id'] = channelId;
    data['username'] = username;
    return data;
  }
}