class ChannelMemberInfoModel {
  int? code;
  String? message;
  List<ChannelMemberInfoData>? data;

  ChannelMemberInfoModel({this.code, this.message, this.data});

  ChannelMemberInfoModel.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    message = json['message'];
    if (json['data'] != null) {
      data = <ChannelMemberInfoData>[];
      json['data'].forEach((v) {
        data?.add(ChannelMemberInfoData.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final mapData = <String, dynamic>{};
    mapData['code'] = code;
    mapData['message'] = message;
    mapData['data'] = data?.map((v) => v.toJson()).toList();
    return mapData;
  }
}

class ChannelMemberInfoData {
  String? channelId;
  String? username;
  String? nickname;
  int? role;
  int? mute;
  List<String>? tags; /// Dao角色标签

  ChannelMemberInfoData(
      {this.channelId, this.username, this.nickname, this.role, this.mute,this.tags});

  ChannelMemberInfoData.fromJson(Map<String, dynamic> json) {
    channelId = json['channel_id'];
    username = json['username'];
    nickname = json['nickname'];
    role = json['role'];
    mute = json['mute'];
    if (json['tags'] != null) {
      tags = [];
      json['tags'].forEach((v) {
        tags?.add(v);
      });
    }
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['channel_id'] = channelId;
    data['username'] = username;
    data['nickname'] = nickname;
    data['role'] = role;
    data['mute'] = mute;
    data['tags'] = tags;
    
    return data;
  }
}
