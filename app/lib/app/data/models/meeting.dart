class Meeting {
  String? id;
  String? title;
  String? startTime; // YYYY-MM-DD HH:MM:SS 不能超过 10 天
  String? endTime; // YYYY-MM-DD HH:MM:SS
  String? description;
  String? passcode;
  String? hostId;
  String? hostName;
  String? meetingLink;
  String? hostOpenMeeting; // Host开会议
  bool? isAnyUserCanOpenMeeting; // 默认为：false
  Meeting({
    this.id,
    this.title,
    this.startTime,
    this.endTime,
    this.description,
    this.passcode,
    this.hostId,
    this.hostName,
    this.meetingLink,
    this.hostOpenMeeting,
    this.isAnyUserCanOpenMeeting,
  });

  Meeting.fromJson(Map<String, dynamic> json) {
    id = json['id']?.toString();
    title = json['title']?.toString();
    startTime = json['startTime']?.toString();
    endTime = json['endTime']?.toString();
    description = json['description']?.toString();
    passcode = json['passcode']?.toString();
    hostId = json['hostId']?.toString();
    hostName = json['hostName']?.toString();
    meetingLink = json['meetingLink']?.toString();
    hostOpenMeeting = json['hostOpenMeeting']?.toString();
    isAnyUserCanOpenMeeting =
        (json['isAnyUserCanOpenMeeting']?.toInt() ?? 0) == 0
            ? false
            : true; // 0:false; 1: true
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['id'] = id;
    data['title'] = title;
    data['startTime'] = startTime;
    data['endTime'] = endTime;
    data['description'] = description;
    data['passcode'] = passcode;
    data['hostId'] = hostId;
    data['hostName'] = hostName;
    data['meetingLink'] = meetingLink;
    data['hostOpenMeeting'] = hostOpenMeeting;
    data['isAnyUserCanOpenMeeting'] =
        (isAnyUserCanOpenMeeting ?? false) ? 1 : 0;
    return data;
  }
}
