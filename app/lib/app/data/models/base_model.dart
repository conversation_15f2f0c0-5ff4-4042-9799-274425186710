class BaseModel {
  int? type;
  int? chatType;
  String? owner;
  String? msgId;
  int? time;
  int? uuid;

  BaseModel({
    this.type,
    this.chatType,
    this.owner,
    this.msgId,
    this.time,
    this.uuid,
  });

  BaseModel.fromJson(Map<String, dynamic> json) {
    type = json['type'];
    chatType = json['chat_type'];
    owner = json['owner'];
    msgId = json['msg_id'];
    time = json['time'];
    uuid = json['id'];
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['type'] = type;
    data['chat_type'] = chatType;
    data['owner'] = owner;
    data['msg_id'] = msgId;
    data['time'] = time;
    data['id'] = uuid;
    return data;
  }
}
