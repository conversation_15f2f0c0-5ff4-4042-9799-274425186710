class StsInfoModel {
  int? code;
  String? msg;
  StsData? stsData;

  StsInfoModel({this.code, this.msg, this.stsData});

  StsInfoModel.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    msg = json['msg'];
    stsData = json['data'] != null ? StsData?.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['code'] = code;
    data['msg'] = msg;
    if (stsData != null) {
      data['data'] = stsData?.toJson();
    }
    return data;
  }
}

class StsData {
  String? endPoint;
  String? accessKeyId;
  String? accessKeySecret;
  String? regionId;
  String? bucket;
  String? sessionToken;
  String? expiration;
  String? dir;

  StsData(
      {this.endPoint,
      this.accessKeyId,
      this.accessKeySecret,
      this.regionId,
      this.bucket,
      this.sessionToken,
      this.expiration});

  StsData.fromJson(Map<String, dynamic> json) {
    endPoint = json['endPoint'];
    accessKeyId = json['accessKeyId'];
    accessKeySecret = json['accessKeySecret'];
    regionId = json['regionId'];
    bucket = json['bucket'];
    sessionToken = json['sessionToken'];
    expiration = json['expiration'];
    dir = json['dir'];
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['endPoint'] = endPoint;
    data['accessKeyId'] = accessKeyId;
    data['accessKeySecret'] = accessKeySecret;
    data['regionId'] = regionId;
    data['bucket'] = bucket;
    data['sessionToken'] = sessionToken;
    data['expiration'] = expiration;
    data['dir'] = dir;
    return data;
  }
}
