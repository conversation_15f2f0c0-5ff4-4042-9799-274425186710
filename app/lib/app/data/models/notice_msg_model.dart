import 'channel_msg_model.dart';

class NoticeMsgModel {
  int? code;
  String? message;
  NoticeBean? data;

  NoticeMsgModel({this.code, this.message, this.data});

  NoticeMsgModel.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    message = json['message'];
    data = NoticeBean.fromJson(json['data']);
  }

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['code'] = code;
    map['message'] = message;
    map['data'] = data?.toJson();
    return map;
  }
}

class NoticeBean {
  int? total;
  int? page;
  List<ChannelMsgModelData>? list;
  NoticeBean.fromJson(Map<String, dynamic> json) {
    total = json['total'];
    page = json['page'];
    if (json['list'] != null) {
      list = <ChannelMsgModelData>[];
      json['list'].forEach((v) {
        list?.add(ChannelMsgModelData.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['total'] = total;
    data['page'] = page;
    data['list'] = list?.map((v) => v.toJson()).toList();
    return data;
  }
}


