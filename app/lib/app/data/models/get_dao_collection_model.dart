class GetDaoCollectionModel {
  int? code;
  String? msg;
  GetDaoCollectionData? data;

  GetDaoCollectionModel({this.code, this.msg, this.data});

  GetDaoCollectionModel.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    msg = json['msg'];
    if(code==200){
      data = json['data'] != null ? GetDaoCollectionData?.fromJson(json['data']) : null;
    }
  }

  Map<String, dynamic> toJson() {
    final mapData = <String, dynamic>{};
    mapData['code'] = code;
    mapData['msg'] = msg;
    if (data != null) {
      mapData['data'] = data?.toJson();
    }
    return mapData;
  }
}
class GetDaoCollectionData{
  List<GetDaoCollectionDataList>? list; ///ddc名称

  GetDaoCollectionData(this.list);
  GetDaoCollectionData.fromJson(Map<String, dynamic> json) {
    if (json['list'] != null) {
      list = <GetDaoCollectionDataList>[];
      json['list'].forEach((v) {
        list?.add(GetDaoCollectionDataList.fromJson(v));
      });
    }
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    if (list != null) {
      data['list'] = list?.map((v) => v.toJson()).toList();
    }
    return data;
  }
}


class GetDaoCollectionDataList{
  String? ddcName; ///ddc名称
  String? issuer; ///发行方名称
  String? issuerHeaderImg; ///发行方头像
  String? cover; ///封面
  int? mintId;
  int? seriesId;

  GetDaoCollectionDataList({this.ddcName, this.issuer,this.issuerHeaderImg,this.cover,this.mintId,this.seriesId,});
  GetDaoCollectionDataList.fromJson(Map<String, dynamic> json) {
    ddcName = json['ddc_name'];
    issuer = json['issuer'];
    issuerHeaderImg = json['issuer_header_img'];
    cover = json['cover'];
    mintId = json['mint_id'];
    seriesId = json['series_id'];
  }

  Map<String, dynamic> toJson() {
    final mapData = <String, dynamic>{};
    mapData['ddc_name'] = ddcName;
    mapData['issuer'] = issuer;
    mapData['issuer_header_img'] = issuerHeaderImg;
    mapData['cover'] = cover;
    mapData['mint_id'] = mintId;
    mapData['series_id'] = seriesId;
    return mapData;
  }
}
