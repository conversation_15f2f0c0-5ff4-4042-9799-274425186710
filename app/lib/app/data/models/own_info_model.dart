
class OwnInfoModel {
  int? code;
  String? message;
  List<OwnInfoModelData>? infos;

  OwnInfoModel({this.code, this.message, this.infos});

  OwnInfoModel.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    message = json['message'];
    if (json['data'] != null) {
      infos = <OwnInfoModelData>[];
      json['data'].forEach((v) {
        infos?.add(OwnInfoModelData.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['code'] = code;
    data['message'] = message;
    data['data'] = infos?.map((v) => v.toJson()).toList();

    return data;
  }
}

class OwnInfoModelData {
  String? name;
  String? nickname;
  String? avatar;
  String? ioiID;
  bool? isBottom;
  String? userName;//拿来处理更新信息过后有@的情况

  OwnInfoModelData({this.name, this.nickname, this.avatar, this.isBottom,this.userName});

  OwnInfoModelData.fromJson(Map<String, dynamic> json) {
    name = json['name'];
    nickname = json['nickname'];
    avatar = json['avatar'];
    ioiID = json['ioiID'];
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['name'] = name;
    data['nickname'] = nickname;
    data['avatar'] = avatar;
    data['ioiID'] = ioiID;
    return data;
  }
}
