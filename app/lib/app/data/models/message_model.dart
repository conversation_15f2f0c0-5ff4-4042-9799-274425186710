import 'package:flutter_metatel/app/modules/message/link/link_model.dart';

class MessageModel {
  String? body;
  bool? selfDestruct;
  int? time;
  int? type;
  int? chatType;
  String? fileUrl;
  String? fileFragment;
  int? fileSize;
  String? fileName;
  String? thumbnailUrl;
  String? thumbnailFragment;
  List<String>? at;
  String? msgId;
  String? owner;
  String? resend;
  String? replayMsg;
  String? displayName;
  int? duration;
  String? resourceId;
  bool? messageHasRead;
  String? userNameFileHelper;
  String? extension;
  MessageExpand? expand;
  MessageModel(
      {this.body,
        this.selfDestruct,
        this.time,
        this.type,
        this.chatType,
        this.fileUrl,
        this.fileFragment,
        this.fileSize,
        this.fileName,
        this.thumbnailUrl,
        this.thumbnailFragment,
        this.at,
        this.msgId,
        this.owner,
        this.resend,
        this.replayMsg,
        this.resourceId,
        this.duration,
        this.messageHasRead,
        this.userNameFileHelper,
        this.expand,
        this.displayName,
        this.extension,
      });
  MessageModel.fromJson(Map<String, dynamic> json) {
    body = json['body'];
    selfDestruct = json['self_destruct'];
    time = json['time'];
    type = json['type'];
    chatType = json['chat_type'];
    fileUrl = json['file_url'];
    fileFragment = json['file_fragment'];
    fileSize = json['file_size'];
    fileName = json['file_name'];
    thumbnailUrl = json['thumbnail_url'];
    thumbnailFragment = json['thumbnail_fragment'];
    at = json['at']?.cast<String>();
    msgId = json['msg_id'];
    owner = json['owner'];
    resend = json['resend'];
    replayMsg=json['replayMsg'];
    duration = json['duration'];
    resourceId = json['resourceId'];
    displayName = json['displayName'];
    extension = json['extension'];
    var json2 = json['expand'];
    if(json2!=null){
      expand=MessageExpand.fromJson(json2);
    }
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['body'] = body;
    data['self_destruct'] = selfDestruct;
    data['time'] = time;
    data['type'] = type;
    data['chat_type'] = chatType;
    data['file_url'] = fileUrl;
    data['file_fragment'] = fileFragment;
    data['file_size'] = fileSize;
    data['file_name'] = fileName;
    data['thumbnail_url'] = thumbnailUrl;
    data['thumbnail_fragment'] = thumbnailFragment;
    data['at'] = at;
    data['msg_id'] = msgId;
    data['owner'] = owner;
    data['resend'] = resend;
    data['duration'] = duration;
    data['replayMsg']=replayMsg;
    data['resourceId'] = resourceId;
    data['expand'] = expand;
    data['displayName'] = displayName;
    data['extension'] = extension;
    return data;
  }
}
class MessageExpand{
  MessageExpandSize? size;
  LinkModel? linkModel;
  MessageExpand({this.size,this.linkModel});
  MessageExpand.fromJson(Map<String, dynamic> json){
    var json2 = json['size'];
    var json3 = json['linkModel'];
    if(json2!=null){
      size=MessageExpandSize.fromJson(json2);
    }
    if(json3!=null){
      linkModel=LinkModel.fromJson(json3);
    }

  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    if(size!=null){
      data['size']=size?.toJson();
    }
    if(linkModel!=null){
      data['linkModel']=linkModel?.toJson();
    }
    return data;
  }
}
class MessageExpandSize{
  double? width;
  double? height;
  MessageExpandSize({this.width,this.height});
  MessageExpandSize.fromJson(Map<String, dynamic> json){
    width=json['width'];
    height=json['height'];
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['width']=width;
    data['height']=height;
    return data;
  }
}
