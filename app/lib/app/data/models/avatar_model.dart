class AvatarModel {
  String? path;
  String? url; /// 加密url
  String? fragment;
  String? avatarUrl; /// 不加密url

  AvatarModel({this.path, this.url, this.fragment, this.avatarUrl});

  AvatarModel.fromJson(Map<String, dynamic> json) {
    path = json['path'];
    url = json['url'];
    fragment = json['fragment'];
    avatarUrl = json['avatarUrl'];
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['path'] = path;
    data['url'] = url;
    data['fragment'] = fragment;
    data['avatarUrl'] = avatarUrl;
    return data;
  }
}
