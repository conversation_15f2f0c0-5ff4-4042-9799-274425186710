class AuthorizationCodeResultModel {
  String? code;
  String? msg;
  Map<String, dynamic>? data;

  AuthorizationCodeResultModel({this.code, this.msg, this.data});

  AuthorizationCodeResultModel.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    msg = json['msg'];
    data = json['data'];
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['code'] = code;
    data['msg'] = msg;
    data['data'] = data;
    return data;
  }
}
