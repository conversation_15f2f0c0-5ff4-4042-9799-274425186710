class PollResponse {
  int? code;
  String? message;

  PollResponse.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    message = json['message'];
  }

  Map<String, dynamic> toJson() => {
        'code': code,
        'message': message,
      };
}

class PollListResModel extends PollResponse{
  List<Poll>? data;

  PollListResModel.fromJson(Map<String, dynamic> json) : super.fromJson(json) {
    if (json['data'] != null) {
      data = <Poll>[];
      json['data'].forEach((v) {
        data?.add(Poll.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final data = super.toJson();
    if (this.data != null) {
      final v = this.data;
      final arr0 = [];
      v!.forEach((v) {
        arr0.add(v.toJson());
      });
      data['data'] = arr0;
    }
    return data;
  }
}

class PollResModel extends PollResponse{
  Poll? data;

  PollResModel.fromJson(Map<String, dynamic> json) : super.fromJson(json) {
    if (json['data'] != null) {
      data = (json['data'] != null) ? Poll.fromJson(json['data']) : null;
    }
  }

  Map<String, dynamic> toJson() {
    final data = super.toJson();
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class Poll {
  String? id;
  String? username;
  String? channelId;
  String? title;
  String? desc;

  /// 1: General Vote, 2: Proposal Vote, 3:DAO Vote, 4: Leader Vote
  int? category;

  /// 1: One person, One vote, 2: Shareholding Voting
  int? type;

  bool? dismiss;

  /// Timestamp: 1744348588
  int? registerAt;

  /// Timestamp: 1744348588
  int? startTime;

  /// Timestamp: 1744352188
  int? endTime;
  List<PollOption>? options;

  /// 状态，0-进行中，1-无效，2-有效
  int? status;
  List<VotedMember>? votedMembers; 
 
  /// 当status为 1:Voted
  // int? selectedOptionId;

  /// 当status为 3,4: Expired
  // int? voteCount;

  /// 当status为 3,4: Expired
  // List<PollResult>? results;

  // List<String>? leaderCandidates;

  /// 频道总量
  double? total;

  int? createAt;

  Poll({
    this.id,
    this.username,
    this.channelId,
    this.title,
    this.desc,
    this.category,
    this.type,
    this.dismiss,
    this.registerAt,
    this.startTime,
    this.endTime,
    this.options,
    this.status,
    this.votedMembers,
    this.total,
    this.createAt,
  });

  Poll.fromJson(Map<String, dynamic> json) {
    id = json['id']?.toString();
    username = json['username']?.toString();
    channelId = json['channel_id']?.toString();
    title = json['title']?.toString();
    desc = json['describe']?.toString();
    category = json['category']?.toInt();
    type = json['type']?.toInt();
    dismiss = json['dismiss'];
    registerAt = json['register_at']?.toInt();
    startTime = json['start_at']?.toInt();
    endTime = json['end_at']?.toInt();
    if (json['options'] != null) {
      final v = json['options'];
      final arr0 = <PollOption>[];
      v.forEach((v) {
        arr0.add(PollOption.fromJson(v));
      });
      options = arr0;
    }
    status = json['status']?.toInt();
    votedMembers = 
      json['vote_members'] != null
        ? (json['vote_members'] as List).map((i) => VotedMember.fromJson(i)).toList()
        : null;
    total = json['total']?.toDouble();
    createAt = json['create_at']?.toInt();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['id'] = id;
    data['username'] = username;
    data['channel_id'] = channelId;
    data['title'] = title;
    data['describe'] = desc;
    data['category'] = category;
    data['type'] = type;
    data['dismiss'] = dismiss;
    data['register_at'] = registerAt;
    data['start_at'] = startTime;
    data['end_at'] = endTime;
    if (options != null) {
      final v = options;
      final arr0 = [];
      v!.forEach((v) {
        arr0.add(v.toJson());
      });
      data['options'] = arr0;
    }
    data['status'] = status;
    if(votedMembers != null) {
      final v = votedMembers;
      final arr0 = [];
      v!.forEach((v) {
        arr0.add(v.toJson());
      });
      data['vote_members'] = arr0;
    }    
    data['total'] = total;
    data['create_at'] = createAt;
    return data;
  }
}

class PollOption {
  String? id;
  /// 当为Leader投票时，为Username
  /// 当为Dao投票时，为数字字符串
  String? label;
  double? total;

  PollOption({
    this.id,
    this.label,
    this.total,
  });
  PollOption.fromJson(Map<String, dynamic> json) {
    id = json['id']?.toString();
    label = json['option']?.toString();
    total = json['total']?.toDouble();
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['id'] = id;
    data['option'] = label;
    data['total'] = total;
    return data;
  }
}

class VotedMember {
  String? optionId;
  String? username;
  VotedMember({
    this.optionId,
    this.username,
  });
  VotedMember.fromJson(Map<String, dynamic> json) {
    optionId = json['option_id']?.toString();
    username = json['username']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['option_id'] = optionId;
    data['username'] = username;
    return data;
  }
}
