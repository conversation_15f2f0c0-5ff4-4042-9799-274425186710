class GroupInfoModel {
  String? owner;
  String? title;
  String? id;
  String? avatarUrl;
  String? avatarPath;
  String? fragment;
  String? describe;
  String? announcement;
  List<GroupInfoList>? list;
  List<GroupInfoList>? newList;

  GroupInfoModel(
      {this.owner,
      this.title,
      this.id,
      this.avatarUrl,
      this.avatarPath,
      this.fragment,
      this.describe,
      this.list,
      this.announcement,
      this.newList});

  GroupInfoModel.fromJson(Map<String, dynamic> json) {
    owner = json['owner'];
    title = json['title'];
    id = json['id'];
    avatarUrl = json['avatar_url'];
    avatarPath = json['avatar_path'];
    fragment = json['fragment'];
    describe = json['describe'];
    announcement = json['announcement'];

    if (json['list'] != null) {
      list = <GroupInfoList>[];
      json['list'].forEach((v) {
        list?.add(GroupInfoList.fromJson(v));
      });
    }
    if (json['new_list'] != null) {
      newList = <GroupInfoList>[];
      json['new_list'].forEach((v) {
        newList?.add(GroupInfoList.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['owner'] = owner;
    data['title'] = title;
    data['id'] = id;
    data['avatar_url'] = avatarUrl;
    data['avatar_path'] = avatarPath;
    data['fragment'] = fragment;
    data['describe'] = describe;
    data['announcement'] = announcement;

    if (list != null) {
      data['list'] = list?.map((v) => v.toJson()).toList();
    }
    if (newList != null) {
      data['new_list'] = newList?.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class GroupInfoList {
  String? nickName;
  String? userName;

  GroupInfoList({this.nickName, this.userName});

  GroupInfoList.fromJson(Map<String, dynamic> json) {
    nickName = json['nickName'];
    userName = json['userName'];
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['nickName'] = nickName;
    data['userName'] = userName;
    return data;
  }
}
