
import 'own_info_model.dart';

class OwnMySelfModel {
  int? code;
  String? message;
  OwnInfoModelData? data;

  OwnMySelfModel({this.code, this.message, this.data});

  OwnMySelfModel.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    message = json['message'];
    if (json['data'] != null) {
      data = OwnInfoModelData.fromJson(json['data']);
    }
  }

  Map<String, dynamic> toJson() {
    final value = <String, dynamic>{};
    value['code'] = code;
    value['message'] = message;
    value['data'] = data?.toJson();
    return value;
  }
}

