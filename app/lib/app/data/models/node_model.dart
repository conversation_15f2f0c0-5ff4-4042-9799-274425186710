

import 'package:flutter_metatel/core/utils/util.dart';

class ComNodeModel {
  int? code;
  String? message;
  NodeModelData? nodeModelData;

  ComNodeModel({this.code, this.message, this.nodeModelData});

  ComNodeModel.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    message = json['message'];
    if(code==200){
      if (json['data'] != null) {
        nodeModelData=NodeModelData.fromJson(json['data']);
      }
    }else{
      toast("$message");
    }
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['code'] = code;
    data['message'] = message;
    data['data'] = nodeModelData?.toJson();

    return data;
  }
}

class NodeModelData {
  int? type;
  String? keyBoxPublicKey;
  ComNode? privateNode;
  List<ComNode>? publicNode;
  ComNode? node;

  NodeModelData.fromJson(Map<String, dynamic> json) {
    type = json['type'];
    keyBoxPublicKey = json['keybox_public_key'];
    if(json['public_node']!=null){
      publicNode=[];
      var pub=json['public_node'];
      if(pub is List){
        for(var d in pub) {
          publicNode?.add(ComNode.fromJson(d));
        }
      }else {
        publicNode?.add(ComNode.fromJson(pub));
      }

    }
    if(json['private_node']!=null){
      privateNode=ComNode.fromJson(json['private_node']);
    }
    if(json['node']!=null){
      node=ComNode.fromJson(json['node']);
    }
    // if (json['public_node'] != null) {
    //   publicNodeList = <ComNode>[];
    //   json['public_node'].forEach((v) {
    //     publicNodeList?.add(ComNode.fromJson(v));
    //   });
    // }
    // if (json['private_node'] != null) {
    //   privateNodeList = <ComNode>[];
    //   json['private_node'].forEach((v) {
    //     privateNodeList?.add(ComNode.fromJson(v));
    //   });
    // }

  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['type'] = type;
    data['keybox_public_key'] = keyBoxPublicKey;
    // data['private_node'] = privateNodeList?.map((v) => v.toJson()).toList();
    // data['public_node'] = publicNodeList?.map((v) => v.toJson()).toList();
    data['private_node'] = privateNode?.toJson();
    data['node'] = node?.toJson();
    return data;
  }
}
class ComNode {
  String? node;
  String? publicKey;
  int? port;
  bool isSelect=false; //是否选中
  int? pingTime; //ping时间
  ComNode({this.node,this.publicKey,this.port,this.isSelect=false});

  ComNode.fromJson(Map<String, dynamic> json) {
    node = json['node'];
    publicKey = json['public_key'];
    port = json['port'];
    pingTime = json['ping_time'];
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['node'] = node;
    data['public_key'] = publicKey;
    data['port'] = port;
    data['ping_time'] = pingTime;
    return data;
  }
}

