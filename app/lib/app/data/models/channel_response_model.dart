class ChannelResponse {
  int? code;
  String? msg;

  ChannelResponse.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    msg = json['message'];
  }

  Map<String, dynamic> toJson() => {
        'code': code,
        'message': msg,
      };
}

class ChannelDataRes extends ChannelResponse {
  String? data;

  ChannelDataRes.fromJson(Map<String, dynamic> json) : super.fromJson(json) {
    data = json['data'];
  }
}
class NoticeRoleRes extends ChannelResponse {
  bool? data;

  NoticeRoleRes.fromJson(Map<String, dynamic> json) : super.fromJson(json) {
    data = json['data'];
  }
}

class ChannelBlackListReq extends ChannelResponse{
  List<ChannelBlackListModel>? data;
  ChannelBlackListReq.fromJson(Map<String, dynamic> json) : super.fromJson(json) {
    if (json['data'] != null) {
      data = <ChannelBlackListModel>[];
      json['data'].forEach((v) {
        data?.add(ChannelBlackListModel.fromJson(v));
      });
    }

  }

}
class ChannelBlackListModel {
  String? channelId;
  String? username;
  ChannelBlackListModel({this.channelId,this.username});
  ChannelBlackListModel.fromJson(Map<String, dynamic> json) {
    channelId = json['channel_id'];
    username = json['username'];
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['channel_id'] = channelId;
    data['username'] = username;
    return data;
  }
}