class Role {
  String? tagId;
  String? name;
  int? color;
  List<String>? assignedUsers;

  Role({
    this.tagId,
    this.name,
    this.color,
    this.assignedUsers,
  });

  Role.fromJson(Map<String, dynamic> json) {
    tagId = json['tag_id']?.toString();
    name = json['name']?.toString();
    color = json['color'] == null 
              ? null
              : json['color'] is String
                ? int.tryParse(json['color'])
                : json['color'] is int
                  ? json['color']
                  : null;
    assignedUsers = json['assigned_users'];
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['tag_id'] = tagId;
    data['name'] = name;
    data['color'] = color;
    data['assigned_users'] = assignedUsers;
    return data;
  }
}
