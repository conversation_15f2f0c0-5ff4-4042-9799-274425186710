class CmdModel {
  String? action;
  int? chatType;
  int? time;
  int? type;
  List<String>? targetId;
  String? msgId;
  String? owner;
  Map<String, Object>? undoMsgDatas;

  CmdModel({
    this.action,
    this.chatType,
    this.time,
    this.type,
    this.targetId,
    this.msgId,
    this.owner,
    this.undoMsgDatas,
  });

  CmdModel.fromJson(Map<String, dynamic> json) {
    action = json['action'];
    chatType = json['chat_type'];
    time = json['time'];
    type = json['type'];
    targetId = json['target_id']?.cast<String>();
    msgId = json['msg_id'];
    owner = json['owner'];
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['action'] = action;
    data['chat_type'] = chatType;
    data['time'] = time;
    data['type'] = type;
    data['target_id'] = targetId;
    data['msg_id'] = msgId;
    data['owner'] = owner;
    return data;
  }
}
