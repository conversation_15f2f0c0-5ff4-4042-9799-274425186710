/*
 * <AUTHOR> <PERSON><PERSON>
 * @Date         : 2022-05-07 19:41:46
 * @Description  : 消息接收回调
 * @LastEditors  : <PERSON><PERSON>
 * @LastEditTime : 2022-06-08 14:59:59
 * @FilePath     : /flutter_metatel/lib/app/data/models/chatio_msg_model.dart
 */
/*
 * @Author: luo<PERSON> <EMAIL>
 * @Date: 2022-05-07 18:51:02
 * @LastEditors: luoyuan <EMAIL>
 * @LastEditTime: 2022-05-07 18:54:35
 * @FilePath: \flutter_metatel\lib\app\data\models\chatio_model.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */

class ChatioMessageModel {
  ChatioMessageModel({this.from, this.message, this.id});

  final String? from;
  final String? message;
  final String? id;
}

class ChatioErrorMessageModel {
  ChatioErrorMessageModel(this.fromName, this.fromDeviceId, this.id);

  final String fromName;
  final int fromDeviceId;
  final String id;
}
