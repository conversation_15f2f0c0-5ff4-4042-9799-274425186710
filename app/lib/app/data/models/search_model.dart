class SearchModel {
  String? title;
  String? userName;
  String? content;
  String? displayName;
  String? avatarPath;
  int? type;
  int? count;
  int? chatType;
  String? id;
  dynamic obj;
  bool? isTid;
  SearchModel(
      {this.title,
      this.userName,
      this.displayName,
      this.avatarPath,
      this.content,
      this.type,
      this.count,
      this.id,
      this.chatType,
      this.isTid,
      this.obj});

  SearchModel.fromJson(Map<String, dynamic> json) {
    title = json['title'];
    userName = json['user_name'];
    content = json['content'];
    type = json['type'];
    count = json['count'];
    chatType = json['chat_type'];
    displayName = json['display_name'];
    avatarPath = json['avatar_path'];
    id=json['id'];
    obj=json['obj'];
    isTid = json['isTid'];
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['title'] = title;
    data['user_name'] = userName;
    data['content'] = content;
    data['type'] = type;
    data['count'] = count;
    data['chat_type'] = chatType;
    data['display_name'] = displayName;
    data['avatar_path']=avatarPath;
    data['id']=id;
    data['obj']=obj;
    data['isTid']=isTid;
    return data;
  }
}
