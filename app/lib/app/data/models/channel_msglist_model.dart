import 'channel_msg_model.dart';

class ChannelListMsgModel {
  int? code;
  String? message;
  List<ChannelMsgModeBean>? msgBeans;

  ChannelListMsgModel({this.code, this.message, this.msgBeans});

  ChannelListMsgModel.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    message = json['message'];
    if (json['data'] != null) {
      msgBeans = <ChannelMsgModeBean>[];
      json['data'].forEach((v) {
        msgBeans?.add(ChannelMsgModeBean.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['code'] = code;
    data['message'] = message;
    data['data'] = msgBeans?.map((v) => v.toJson()).toList();
    return data;
  }
}
class ChannelMsgModeBean {
  String? channelId;
  List<ChannelMsgModelData>? msgDatas;
  ChannelMsgModeBean.fromJson(Map<String, dynamic> json) {
    channelId = json['channel_id'];
    if (json['data'] != null) {
      msgDatas = <ChannelMsgModelData>[];
      json['data'].forEach((v) {
        msgDatas?.add(ChannelMsgModelData.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['channel_id'] = channelId;
    data['data'] = msgDatas?.map((v) => v.toJson()).toList();
    return data;
  }
}

