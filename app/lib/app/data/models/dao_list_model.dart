import '../../modules/dao/bean/dao_model.dart';

class DaoList {
  int? code;
  String? msg;
  Data? data;

  DaoList({this.code, this.msg, this.data});

  DaoList.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    msg = json['msg'];
    data = json['data'] != null ? Data?.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['code'] = code;
    map['msg'] = msg;
    if (data != null) {
      map['data'] = data?.toJson();
    }
    return map;
  }
}

class Data {
  List<DaoModelData>? list;
  int? currentPage;
  int? lastPage;
  int? total;

  Data({this.list, this.currentPage, this.lastPage, this.total});

  Data.fromJson(Map<String, dynamic> json) {
    if (json['list'] != null) {
      list = <DaoModelData>[];
      json['list'].forEach((v) {
        list?.add(DaoModelData.fromJson(v));
      });
    }
    currentPage = json['current_page'];
    lastPage = json['last_page'];
    total = json['total'];
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    if (list != null) {
      data['list'] = list?.map((v) => v.toJson()).toList();
    }
    data['current_page'] = currentPage;
    data['last_page'] = lastPage;
    data['total'] = total;
    return data;
  }

}
