class ChannelCard {
  String? channelId;
  String? description;
  String? title;
  int? expiresTime;
  String? inviteeUser;
  String? action;
  String? avatarUrl;
  String? tokenAddress;
  int? channelAttribute;
  String? chainId;

  ChannelCard({this.channelId, this.description, this.title, this.inviteeUser,this.expiresTime,this.action,this.avatarUrl,this.tokenAddress,this.channelAttribute, this.chainId});

  ChannelCard.fromJson(Map<String, dynamic> json) {
    channelId = json['channel_id'];
    description = json['description'];
    title = json['title'];
    inviteeUser = json['invitee_user'];
    expiresTime=json['expires_time'];
    action=json['action'];
    avatarUrl=json['avatar_url'];
    tokenAddress=json['token_address'];
    channelAttribute=json['channel_attribute'];
    chainId=json['chain_id'];
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['channel_id'] = channelId;
    data['description'] = description;
    data['title'] = title;
    data['invitee_user'] = inviteeUser;
    data['expires_time'] = expiresTime;
    data['action'] = action;
    data['avatar_url'] = avatarUrl;
    data['token_address'] = tokenAddress;
    data['channel_attribute'] = channelAttribute;
    data['chain_id'] = chainId;
    return data;
  }
}
