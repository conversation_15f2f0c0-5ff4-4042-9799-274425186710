/*
 * @Author: <PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2022-04-28 17:40:07
 * @LastEditors: luoyuan <EMAIL>
 * @LastEditTime: 2022-05-06 15:05:46
 * @FilePath: \flutter_metatel\lib\app\data\models\v_contact_model.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import 'dart:convert';

import 'package:flutter_metatel/app/data/providers/db/database.dart';

import '../../widgets/azlist/az_common.dart';

class VContactDate extends ISuspensionBean {
  ContactData? contactData;
  String? tagIndex;
  String? pinyin;
  bool isChecked=false;
  get()=>isChecked;
  VContactDate({this.contactData, this.tagIndex, this.pinyin});

  VContactDate.fromJson(Map<String, dynamic> json) {
    contactData = json['contactData'] != null
        ? ContactData?.fromJson(json['contactData'])
        : null;
    tagIndex = json['tagIndex'];
    pinyin = json['pinyin'];
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['contactData'] = contactData?.toJson();
    data['tagIndex'] = tagIndex;
    data['pinyin'] = pinyin;
    return data;
  }

  @override
  String getSuspensionTag() => tagIndex??'#';

  @override
  String toString() => json.encode(this);

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
          other is VContactDate &&
              runtimeType == other.runtimeType &&
              contactData?.username == other.contactData?.username;

  @override
  int get hashCode => contactData.hashCode;
}
