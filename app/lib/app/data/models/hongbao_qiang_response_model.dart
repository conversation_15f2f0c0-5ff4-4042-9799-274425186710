import 'package:flutter_metatel/app/data/services/config_service.dart';
import 'package:get/get.dart';

///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class HongbaoQiangResponseModelData {
  HongbaoQiangResponseModelData({
    int? code,
    Data? data,
    String? msg,}){
    _code = code;
    _data = data;
    _msg = msg;
  }

  HongbaoQiangResponseModelData.fromJson(dynamic json) {
    _code = json['code'];
    _data = json['data'] != null ? Data.fromJson(json['data']) : null;
    _msg = json['msg'];
  }
  int? _code;
  Data? _data;
  String? _msg;
  HongbaoQiangResponseModelData copyWith({  int? code,
    Data? data,
    String? msg,
  }) => HongbaoQiangResponseModelData(  code: code ?? _code,
    data: data ?? _data,
    msg: msg ?? _msg,
  );
  int? get code => _code;
  Data? get data => _data;
  String? get msg => _msg;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['code'] = _code;
    if (_data != null) {
      map['data'] = _data?.toJson();
    }
    map['msg'] = _msg;
    return map;
  }

}

/// amount : 1
/// log : [{"account":"<EMAIL>","address":"0xcfd51dc9937bdac531c262ff97dc70d6e2f739ec","amount":1,"createtime":"2024-12-06T15:44:48+08:00","head":"https://oss.linksay.site/oss/last/2f11930f-e517-413b-981a-0c1d8d92046e","id":82233,"name":"uEFGfL4","status":2,"token_address":"0x0CB8C20eE462D350C2dA5408eBB681ddF11D25f2","topic":"********-c228-4747-b992-d6fc3ef8a1b4","txhash":"Wait for Sending"}]
/// txhash : "Wait for Sending"

class Data {
  Data({
    num? amount,
    List<Log>? log,
    String? txhash,}){
    _amount = amount;
    _log = log;
    _txhash = txhash;
  }

  Data.fromJson(dynamic json) {
    _amount = json['amount'];
    if (json['log'] != null) {
      _log = [];
      json['log'].forEach((v) {
        _log?.add(Log.fromJson(v));
      });
    }
    _txhash = json['txhash'];
  }
  num? _amount;
  List<Log>? _log;
  String? _txhash;
  Data copyWith({  double? amount,
    List<Log>? log,
    String? txhash,
  }) => Data(  amount: amount ?? _amount,
    log: log ?? _log,
    txhash: txhash ?? _txhash,
  );
  num? get amount => _amount;
  List<Log>? get log => _log;
  String? get txhash => _txhash;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['amount'] = _amount;
    if (_log != null) {
      map['log'] = _log?.map((v) => v.toJson()).toList();
    }
    map['txhash'] = _txhash;
    return map;
  }

}

/// account : "<EMAIL>"
/// address : "0xcfd51dc9937bdac531c262ff97dc70d6e2f739ec"
/// amount : 1
/// createtime : "2024-12-06T15:44:48+08:00"
/// head : "https://oss.linksay.site/oss/last/2f11930f-e517-413b-981a-0c1d8d92046e"
/// id : 82233
/// name : "uEFGfL4"
/// status : 2
/// token_address : "0x0CB8C20eE462D350C2dA5408eBB681ddF11D25f2"
/// topic : "********-c228-4747-b992-d6fc3ef8a1b4"
/// txhash : "Wait for Sending"

class Log {
  Log({
    String? account,
    String? address,
    num? amount,
    String? createtime,
    String? head,
    int? id,
    String? name,
    int? status,
    String? tokenAddress,
    String? topic,
    String? txhash,}){
    _account = account;
    _address = address;
    _amount = amount;
    _createtime = createtime;
    _head = head;
    _id = id;
    _name = name;
    _status = status;
    _tokenAddress = tokenAddress;
    _topic = topic;
    _txhash = txhash;
  }

  Log.fromJson(dynamic json) {
    _account = json['account'];
    _address = json['address'];
    _amount = json['amount'];
    _createtime = json['createtime'];
    _head = json['head'];
    _id = json['id'];
    _name = json['name'];
    _status = json['status'];
    _tokenAddress = json['token_address'];
    _topic = json['topic'];
    _txhash = json['txhash'];
  }
  String? _account;
  String? _address;
  num? _amount;
  String? _createtime;
  String? _head;
  int? _id;
  String? _name;
  int? _status;
  String? _tokenAddress;
  String? _topic;
  String? _txhash;
  Log copyWith({  String? account,
    String? address,
    num? amount,
    String? createtime,
    String? head,
    int? id,
    String? name,
    int? status,
    String? tokenAddress,
    String? topic,
    String? txhash,
  }) => Log(  account: account ?? _account,
    address: address ?? _address,
    amount: amount ?? _amount,
    createtime: createtime ?? _createtime,
    head: head ?? _head,
    id: id ?? _id,
    name: name ?? _name,
    status: status ?? _status,
    tokenAddress: tokenAddress ?? _tokenAddress,
    topic: topic ?? _topic,
    txhash: txhash ?? _txhash,
  );
  String? get account => _account;
  String? get address => _address;
  num? get amount => _amount;
  String? get createtime => _createtime;
  String? get head => _head;
  int? get id => _id;
  String? get name => _name;
  int? get status => _status;
  String? get tokenAddress => _tokenAddress;
  String? get topic => _topic;
  String? get txhash => _txhash;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['account'] = _account;
    map['address'] = _address;
    map['amount'] = _amount;
    map['createtime'] = _createtime;
    map['head'] = _head;
    map['id'] = _id;
    map['name'] = _name;
    map['status'] = _status;
    map['token_address'] = _tokenAddress;
    map['topic'] = _topic;
    map['txhash'] = _txhash;
    return map;
  }

}
