class RealNameVerifyResultModel {
  String? code;
  String? msg;
  Map<String, dynamic>? data;

  RealNameVerifyResultModel({this.code, this.msg, this.data});

  RealNameVerifyResultModel.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    msg = json['msg'];
    data = json['data'];
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['code'] = code;
    data['msg'] = msg;
    data['data'] = data;
    return data;
  }
}
