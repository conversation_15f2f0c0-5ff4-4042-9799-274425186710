class ContactCardModel {
  String? body;
  int? time;
  int? type;
  int? chatType;
  String? fileUrl;
  String? fileFragment;
  String? msgId;
  String? owner;

  ContactCardModel(
      {this.body,
      this.time,
      this.type,
      this.chatType,
      this.fileUrl,
      this.fileFragment,
      this.msgId,
      this.owner});

  ContactCardModel.fromJson(Map<String, dynamic> json) {
    body = json['body'];
    time = json['time'];
    type = json['type'];
    chatType = json['chat_type'];
    fileUrl = json['file_url'];
    fileFragment = json['file_fragment'];
    msgId = json['msg_id'];
    owner = json['owner'];
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['body'] = body;
    data['time'] = time;
    data['type'] = type;
    data['chat_type'] = chatType;
    data['file_url'] = fileUrl;
    data['file_fragment'] = fileFragment;
    data['msg_id'] = msgId;
    data['owner'] = owner;
    return data;
  }
}

class ContactBean {
  String? userName;
  String? displayName;
  String? from;

  ContactBean(this.userName, this.displayName, {this.from});

  ContactBean.fromJson(Map<String, dynamic> json) {
    userName = json['user_name'];
    displayName = json['display_name'];
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['user_name'] = userName;
    data['display_name'] = displayName;
    return data;
  }
}
