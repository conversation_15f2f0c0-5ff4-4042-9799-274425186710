import 'package:flutter_metatel/app/data/services/config_service.dart';
import 'package:flutter_metatel/core/task/hongbao_task.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:get/get.dart';

///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class HongbaoCheckStatusModelDataRedPacket {
/*
{
  "account": "<EMAIL>",
  "address": "0x2aa72d7fb417442e7c9a6f0c5c8be83f174bbd16",
  "amount": 1,
  "backhash": "",
  "createtime": "2024-12-02T08:56:04+08:00",
  "fee": 0,
  "fixed_amount": 0,
  "head": "https://oss.linksay.site/oss/last/03f6fa2b-bd14-4f7e-8deb-5d54ed579ef0",
  "id": 3210,
  "name": "uAcWSXA",
  "number": 1,
  "status": 3,
  "token_address": "0x9d4dc340ab5176788d77858e44a89c4af4c1c4c6",
  "topic": "9f29bc1b-"
} 
*/

  String? account;
  String? address;
  num? amount;
  String? backhash;
  String? createtime;
  String? fee;
  String? fixedAmount;
  String? head;
  String? id;
  String? name;
  String? number;
  String? status;
  String? tokenAddress;
  String? topic;

  HongbaoCheckStatusModelDataRedPacket({
    this.account,
    this.address,
    this.amount,
    this.backhash,
    this.createtime,
    this.fee,
    this.fixedAmount,
    this.head,
    this.id,
    this.name,
    this.number,
    this.status,
    this.tokenAddress,
    this.topic,
  });
  HongbaoCheckStatusModelDataRedPacket.fromJson(Map<String, dynamic> json) {
    account = json['account']?.toString();
    address = json['address']?.toString();
    amount = json['amount'];
    backhash = json['backhash']?.toString();
    createtime = json['createtime']?.toString();
    fee = json['fee']?.toString();
    fixedAmount = json['fixed_amount']?.toString();
    head = json['head']?.toString();
    id = json['id']?.toString();
    name = json['name']?.toString();
    number = json['number']?.toString();
    status = json['status']?.toString();
    tokenAddress = json['token_address']?.toString();
    topic = json['topic']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['account'] = account;
    data['address'] = address;
    data['amount'] = amount;
    data['backhash'] = backhash;
    data['createtime'] = createtime;
    data['fee'] = fee;
    data['fixed_amount'] = fixedAmount;
    data['head'] = head;
    data['id'] = id;
    data['name'] = name;
    data['number'] = number;
    data['status'] = status;
    data['token_address'] = tokenAddress;
    data['topic'] = topic;
    return data;
  }
}

class HongbaoCheckStatusModelDataLog {
/*
{
  "account": "<EMAIL>",
  "address": "0x2aa72d7fb417442e7c9a6f0c5c8be83f174bbd16",
  "amount": 1,
  "createtime": "2024-12-02T09:44:58+08:00",
  "head": "https://oss.linksay.site/oss/last/03f6fa2b-bd14-4f7e-8deb-5d54ed579ef0",
  "id": 82124,
  "name": "uAcWSXA",
  "status": 3,
  "token_address": "0x9d4dc340ab5176788d77858e44a89c4af4c1c4c6",
  "topic": "9f29bc1b-7818-4aad-97c1-e81a83acafe8",
  "txhash": "0x95b45f665cea309a0d5bea400aa33b65977916c413968e4df71e75546b452ebe"
} 
*/

  String? account;
  String? address;
  num? amount;
  String? createtime;
  String? head;
  String? id;
  String? name;
  String? status;
  String? tokenAddress;
  String? topic;
  String? txhash;

  HongbaoCheckStatusModelDataLog({
    this.account,
    this.address,
    this.amount,
    this.createtime,
    this.head,
    this.id,
    this.name,
    this.status,
    this.tokenAddress,
    this.topic,
    this.txhash,
  });
  HongbaoCheckStatusModelDataLog.fromJson(Map<String, dynamic> json) {
    account = json['account']?.toString();
    address = json['address']?.toString();
    amount = json['amount'];
    createtime = json['createtime']?.toString();
    head = json['head']?.toString();
    id = json['id']?.toString();
    name = json['name']?.toString();
    status = json['status']?.toString();
    tokenAddress = json['token_address']?.toString();
    topic = json['topic']?.toString();
    txhash = json['txhash']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['account'] = account;
    data['address'] = address;
    data['amount'] = amount;
  
    data['createtime'] = createtime;
    data['head'] = head;
    data['id'] = id;
    data['name'] = name;
    data['status'] = status;
    data['token_address'] = tokenAddress;
    data['topic'] = topic;
    data['txhash'] = txhash;
    return data;
  }
}

class HongbaoCheckStatusModelData {
/*
{
  "isOk": false,
  "log": [
    {
      "account": "<EMAIL>",
      "address": "0x2aa72d7fb417442e7c9a6f0c5c8be83f174bbd16",
      "amount": 1,
      "createtime": "2024-12-02T09:44:58+08:00",
      "head": "https://oss.linksay.site/oss/last/03f6fa2b-bd14-4f7e-8deb-5d54ed579ef0",
      "id": 82124,
      "name": "uAcWSXA",
      "status": 3,
      "token_address": "0x9d4dc340ab5176788d77858e44a89c4af4c1c4c6",
      "topic": "9f29bc1b-7818-4aad-97c1-e81a83acafe8",
      "txhash": "0x95b45f665cea309a0d5bea400aa33b65977916c413968e4df71e75546b452ebe"
    }
  ],
  "redPacket": {
    "account": "<EMAIL>",
    "address": "0x2aa72d7fb417442e7c9a6f0c5c8be83f174bbd16",
    "amount": 1,
    "backhash": "",
    "createtime": "2024-12-02T08:56:04+08:00",
    "fee": 0,
    "fixed_amount": 0,
    "head": "https://oss.linksay.site/oss/last/03f6fa2b-bd14-4f7e-8deb-5d54ed579ef0",
    "id": 3210,
    "name": "uAcWSXA",
    "number": 1,
    "status": 3,
    "token_address": "0x9d4dc340ab5176788d77858e44a89c4af4c1c4c6",
    "topic": "9f29bc1b-"
  }
} 
*/

  bool? isOk;
  HongbaoCheckStatusModelDataLog? mylog;
  List<HongbaoCheckStatusModelDataLog?>? log;
  HongbaoCheckStatusModelDataRedPacket? redPacket;

  HongbaoCheckStatusModelData({
    this.isOk,
    this.log,
    this.redPacket,
  });
  HongbaoCheckStatusModelData.fromJson(Map<String, dynamic> json) {
    isOk = json['isOk'];
  if (json['log'] != null) {
  final v = json['log'];
  final arr0 = <HongbaoCheckStatusModelDataLog>[];
    v.forEach((v) {
      var logmodel = HongbaoCheckStatusModelDataLog.fromJson(v);
      if (logmodel.account == Get.find<AppConfigService>().getUserName()) {
        mylog = logmodel;
      }
    arr0.add(logmodel);
    });
    log = arr0;
    }
    redPacket = (json['redPacket'] != null) ? HongbaoCheckStatusModelDataRedPacket.fromJson(json['redPacket']) : null;

  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['isOk'] = isOk;
    if (log != null) {
      final v = log;
      final arr0 = [];
  v!.forEach((v) {
  arr0.add(v!.toJson());
  });
      data['log'] = arr0;
    }
    if (redPacket != null) {
      data['redPacket'] = redPacket!.toJson();
    }
    return data;
  }
}

class HongbaoCheckStatusModel {
/*
{
  "code": 200,
  "data": {
    "isOk": false,
    "log": [
      {
        "account": "<EMAIL>",
        "address": "0x2aa72d7fb417442e7c9a6f0c5c8be83f174bbd16",
        "amount": 1,
        "createtime": "2024-12-02T09:44:58+08:00",
        "head": "https://oss.linksay.site/oss/last/03f6fa2b-bd14-4f7e-8deb-5d54ed579ef0",
        "id": 82124,
        "name": "uAcWSXA",
        "status": 3,
        "token_address": "0x9d4dc340ab5176788d77858e44a89c4af4c1c4c6",
        "topic": "9f29bc1b-7818-4aad-97c1-e81a83acafe8",
        "txhash": "0x95b45f665cea309a0d5bea400aa33b65977916c413968e4df71e75546b452ebe"
      }
    ],
    "redPacket": {
      "account": "<EMAIL>",
      "address": "0x2aa72d7fb417442e7c9a6f0c5c8be83f174bbd16",
      "amount": 1,
      "backhash": "",
      "createtime": "2024-12-02T08:56:04+08:00",
      "fee": 0,
      "fixed_amount": 0,
      "head": "https://oss.linksay.site/oss/last/03f6fa2b-bd14-4f7e-8deb-5d54ed579ef0",
      "id": 3210,
      "name": "uAcWSXA",
      "number": 1,
      "status": 3,
      "token_address": "0x9d4dc340ab5176788d77858e44a89c4af4c1c4c6",
      "topic": "9f29bc1b-"
    }
  }
} 
*/

  String? code;
  HongbaoCheckStatusModelData? data;

  HongbaoCheckStatusModel({
    this.code,
    this.data,
  });
  HongbaoCheckStatusModel.fromJson(Map<String, dynamic> json) {
    code = json['code']?.toString();
    data = (json['data'] != null) ? HongbaoCheckStatusModelData.fromJson(json['data']) : null;
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['code'] = code;
    data['data'] = this.data!.toJson();
      return data;
  }
}
