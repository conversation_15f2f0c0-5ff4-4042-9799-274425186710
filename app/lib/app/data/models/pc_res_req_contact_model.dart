class PcResReqContactModel {
  int? chatType;
  int? time;
  String? msgId;
  int? type;
  String? action;
  PcContactBody? body;
  String? fileUrl;
  String? fileFragment;

  PcResReqContactModel(
      {this.chatType,
      this.time,
      this.msgId,
      this.type,
      this.action,
      this.fileFragment,
      this.fileUrl,
      this.body});

  PcResReqContactModel.fromJson(Map<String, dynamic> json) {
    chatType = json['chat_type'];
    time = json['time'];
    msgId = json['msg_id'];
    type = json['type'];
    action = json['action'];
    fileFragment = json['file_fragment'];
    fileUrl = json['file_url'];

    if (json['body'] != null) {
      body = PcContactBody.fromJson(json['body']);

    }
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['chat_type'] = chatType;
    data['time'] = time;
    data['msg_id'] = msgId;
    data['type'] = type;
    data['action'] = action;
    data['file_url'] = fileUrl;
    data['file_fragment'] = fileFragment;
    if (body != null) {
      data['body'] = body?.toJson();
    }
    return data;
  }
}
class PcContactBody {
  List<PcContact>? contactList;
  PcContact? owner;
  PcContactBody(
      {this.contactList,this.owner});

  PcContactBody.fromJson(Map<String, dynamic> json) {
    if (json['contact_list'] != null) {
      contactList = <PcContact>[];
      json['contact_list'].forEach((v) {
        contactList?.add(PcContact.fromJson(v));
      });
    }
    if(json['owner']!=null){
      owner=PcContact.fromJson(json['owner']);
    }
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    if (contactList != null) {
      data['contact_list'] = contactList?.map((v) => v.toJson()).toList();
    }
    if (owner != null) {
      data['owner'] = owner?.toJson();
    }
    return data;
  }
}
class PcContact {
  String? username;
  String? displayname;
  String? localname;
  String? avatarUrl;
  String? chatBackgroundUrl;
  int? state;
  String? mobile;
  double? updateTime;

  PcContact(
      {this.username,
      this.displayname,
      this.localname,
      this.avatarUrl,
      this.chatBackgroundUrl,
      this.state,
      this.mobile,
      this.updateTime});

  PcContact.fromJson(Map<String, dynamic> json) {
    username = json['username'];
    displayname = json['displayname'];
    localname = json['localname'];
    avatarUrl = json['avatarUrl'];
    chatBackgroundUrl = json['chatBackgroundUrl'];
    state = json['state'];
    mobile = json['mobile'];
    updateTime = json['updateTime'];
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['username'] = username;
    data['displayname'] = displayname;
    data['localname'] = localname;
    data['avatarUrl'] = avatarUrl;
    data['chatBackgroundUrl'] = chatBackgroundUrl;
    data['state'] = state;
    data['mobile'] = mobile;
    data['updateTime'] = updateTime;
    return data;
  }
}
