import 'channel_info_model_data.dart';

class ChannelsModel {
  int? code;
  String? message;
  List<ChannelInfoModelData>? channels;

  ChannelsModel({this.code, this.message, this.channels});

  ChannelsModel.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    message = json['message'];
    if (json['data'] != null) {
      channels = <ChannelInfoModelData>[];
      json['data'].forEach((v) {
        channels?.add(ChannelInfoModelData.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['code'] = code;
    data['message'] = message;
    data['data'] = channels?.map((v) => v.toJson()).toList();

    return data;
  }
}
