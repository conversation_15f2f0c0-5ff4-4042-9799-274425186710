class BackupModel {
  String? username;
  List<dynamic>? contacts;
  List<dynamic>? blackContacts;
  List<dynamic>? sessions;

  BackupModel({this.username, this.contacts, this.blackContacts, this.sessions});

  BackupModel.fromJson(Map<String, dynamic> json) {
    username = json['username'];
    contacts = json['contacts'];
    blackContacts = json['blackContacts'];
    sessions = json['sessions'];
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['username'] = username;
    data['contacts'] = contacts;
    data['blackContacts'] = blackContacts;
    data['sessions'] = sessions;
    return data;
  }
}

class SessionBackInfo {
  String? username;
  bool? top;

  SessionBackInfo({this.username, this.top});

  SessionBackInfo.fromJson(Map<String, dynamic> json) {
    username = json['username'];
    top = json['top'];
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['username'] = username;
    data['top'] = top;
    return data;
  }
}
