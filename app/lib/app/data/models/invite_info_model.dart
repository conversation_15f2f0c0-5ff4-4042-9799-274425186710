import 'dart:convert';

import 'package:flutter_metatel/core/utils/app_log.dart';

///推广分享AppConfig信息
class InviteInfoModel {
  bool? isBindRecommender;
  bool? needPopInviteDialog;
  String? inviteCode;
  String? invitePosterPath;///邀请海报本地地址
  String? recommenderUserNameWithDomain;///推荐人userName带节点信息

  InviteInfoModel({
    bool? isBindRecommender,
    bool? needPopInviteDialog,
    String? inviteCode,
    String? recommenderUserNameWithDomain,
  });

  InviteInfoModel.fromJsonStr(String jsonStr) {
    Map<String,dynamic> map=json.decode(jsonStr);
    isBindRecommender=map["isBindRecommender"];
    needPopInviteDialog=map["needPopInviteDialog"];
    inviteCode=map["inviteCode"];
    invitePosterPath=map["invitePosterPath"];
    recommenderUserNameWithDomain=map["recommenderUserNameWithDomain"];
  }

  String toJsonStr() {
    final data = <String, dynamic>{};
    data['isBindRecommender'] = isBindRecommender;
    data['needPopInviteDialog'] = needPopInviteDialog;
    data['inviteCode'] = inviteCode;
    data['invitePosterPath'] = invitePosterPath;
    data['recommenderUserNameWithDomain'] = recommenderUserNameWithDomain;
    var encode = json.encode(data);
    AppLogger.d("InviteInfoModel.toJsonStr=$encode");
    return encode;
  }
}
