//FileName market_bean
// <AUTHOR>
//@Date 2022/10/26 12:04
class AnnouncementMode {
  AnnouncementMode({this.userName,this.id, this.time,this.displayName, this.body,this.avatarPath,this.isNeedShow=true});
  String? userName;
  String? id;
  int? time;
  String?displayName;
  String? body;
  String? avatarPath;
  bool? isNeedShow;
  AnnouncementMode.fromJson(Map<String, dynamic> json) {
    userName = json['user_name'];
    time = json['time'];
    displayName = json['display_name'];
    body = json['body'];
    id=json['id'];
    isNeedShow=json['isNeedShow'];
    avatarPath=json['avatarPath'];
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['user_name'] = userName;
    data['time'] = time;
    data['display_name'] = displayName;
    data['body'] = body;
    data['id'] = id;
    data['isNeedShow']=isNeedShow;
    data['avatarPath']=avatarPath;
    return data;
  }
}
