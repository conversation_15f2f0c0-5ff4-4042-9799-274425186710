
class TurnModel{
   String? credential;
   String? username;
   List<String>? urls;
   TurnModel.fromJson(Map<String, dynamic> json) {
      username = json['username'];
      credential = json['credential'];
      urls = json['urls']?.cast<String>();
   }

   Map<String, dynamic> toJson() {
      final data = <String, dynamic>{};
      data['username'] = username;
      data['credential'] = credential;
      data['urls'] = urls;
      return data;
   }
}