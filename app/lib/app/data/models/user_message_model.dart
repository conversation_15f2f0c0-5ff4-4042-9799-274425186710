/*
 * <AUTHOR> <PERSON><PERSON>
 * @Date         : 2022-05-07 19:34:06
 * @Description  : 用户、群聊界面跳转时详情对象
 * @LastEditors  : Daen
 * @LastEditTime : 2022-06-13 15:14:48
 * @FilePath     : /flutter_metatel/lib/app/data/models/user_message_model.dart
 */
class UserMessage {
  int? chatType;
  String? userName;
  String? displayName;
  String? avatarPath;
  bool? isFriend;
  bool? isGroupValid;
  String? fileHelperUserName;
  String? msgId;
  bool? isTid;
  String? tokenAddress;
  int? channelAttribute;
  String? chainId;
  UserMessage(
      {required this.chatType,
      required this.displayName,
      required this.userName,
      required this.avatarPath,this.fileHelperUserName,this.msgId,this.isFriend,this.isTid, this.tokenAddress, this.channelAttribute,this.chainId});

  UserMessage.fromJson(Map<String, dynamic> json) {
    chatType = json['chat_type'];
    userName = json['username'];
    avatarPath = json['avatar_path'];
    displayName = json['displayname'];
    isFriend = json['is_friend'];
    isGroupValid = json['is_group_Valid'];
    msgId= json['msgId'];
    isTid = json['isTid'];
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['chat_type'] = chatType;
    data['username'] = userName;
    data['avatar_path'] = avatarPath;
    data['displayname'] = displayName;
    data['is_friend'] = isFriend;
    data['is_group_Valid'] = isGroupValid;
    data['msgId'] = msgId;
    data['isTid'] = isTid;

    return data;
  }
}
