class ReplayMessage {
  String? body;
  int? type;
  int? chatType;
  String? fileName;
  String? displayName;
  String? userName;
  String? msgId;
  String? owner;

  ReplayMessage(
      {this.body,
      this.type,
      this.chatType,
      this.fileName,
      this.displayName,
      this.userName,
      this.msgId,
      this.owner});

  ReplayMessage.fromJson(Map<String, dynamic> json) {
    body = json['body'];
    type = json['type'];
    chatType = json['chat_type'];
    fileName = json['file_name'];
    displayName = json['display_name'];
    userName = json['user_name'];
    msgId = json['msg_id'];
    owner = json['owner'];
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['body'] = body;
    data['type'] = type;
    data['chat_type'] = chatType;
    data['file_name'] = fileName;
    data['display_name'] = displayName;
    data['user_name'] = userName;
    data['msg_id'] = msgId;
    data['owner'] = owner;
    return data;
  }
}
