//FileName market_bean
// <AUTHOR>
//@Date 2022/10/26 12:04
class AdsInfoReq{
  int? code;
  String? message;
  List<AdsBean>? data;

  AdsInfoReq({this.code, this.message, this.data});

  AdsInfoReq.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    message = json['message'];
    if (json['data'] != null) {
      data = <AdsBean>[];
      json['data'].forEach((v) {
        data?.add(AdsBean.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['code'] = code;
    map['message'] = message;
    map['data'] = data?.map((v) => v.toJson()).toList();
    return map;
  }
}

class AdsBean {
  AdsBean({this.type, this.content,this.image, this.id, this.title});
  int? type;
  String? content;
  String? image;
  String? id;
  String? title;
  AdsBean.fromJson(Map<String, dynamic> json) {
    type = json['type'];
    content = json['content'];
    image = json['image'];
    id = json['id'];
    title = json['title'];
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['type'] = type;
    data['content'] = content;
    data['image'] = image;
    data['id'] = id;
    data['title'] = title;
    return data;
  }
}
