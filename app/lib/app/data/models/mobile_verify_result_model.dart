class MobileVerifyResultModel {
  String? code;
  String? msg;
  Map<String, dynamic>? data;

  MobileVerifyResultModel({this.code, this.msg, this.data});

  MobileVerifyResultModel.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    msg = json['msg'];
    data = json['data'];
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['code'] = code;
    data['msg'] = msg;
    data['data'] = data;
    return data;
  }
}
