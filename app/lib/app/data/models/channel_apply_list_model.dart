class ChannelApplyListModel {
  int? code;
  String? message;
  List<String>? data;

  ChannelApplyListModel({this.code, this.message, this.data});

  ChannelApplyListModel.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    message = json['message'];
    data = json['data']?.cast<String>();
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['code'] = code;
    data['message'] = message;
    data['data'] = data;
    return data;
  }
}
