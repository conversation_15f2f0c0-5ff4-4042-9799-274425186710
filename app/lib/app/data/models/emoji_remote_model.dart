class EmojiRemoteModel {
  String? title;
  List<Datas>? datas;
  List<Groups>? groups;

  EmojiRemoteModel({this.title, this.datas, this.groups});

  EmojiRemoteModel.fromJson(Map<String, dynamic> json) {
    title = json['title'];
    if (json['datas'] != null) {
      datas = <Datas>[];
      json['datas'].forEach((v) {
        datas?.add(Datas.fromJson(v));
      });
    }
    if (json['groups'] != null) {
      groups = <Groups>[];
      json['groups'].forEach((v) {
        groups?.add(Groups.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['title'] = title;
    if (datas != null) {
      data['datas'] = datas?.map((v) => v.toJson()).toList();
    }
    if (groups != null) {
      data['groups'] = groups?.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Datas {
  String? symbol;
  String? path;

  Datas({this.symbol, this.path});

  Datas.fromJson(Map<String, dynamic> json) {
    symbol = json['symbol'];
    path = json['path'];
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['symbol'] = symbol;
    data['path'] = path;
    return data;
  }
}

class Groups {
  Group? group;
  List<Datas>? datas;

  Groups({this.group, this.datas});

  Groups.fromJson(Map<String, dynamic> json) {
    group = json['group'] != null ? Group?.fromJson(json['group']) : null;
    if (json['datas'] != null) {
      datas = <Datas>[];
      json['datas'].forEach((v) {
        datas?.add(Datas.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    if (group != null) {
      data['group'] = group?.toJson();
    }
    if (datas != null) {
      data['datas'] = datas?.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Group {
  String? enUS;
  String? zhCN;

  Group({this.enUS, this.zhCN});

  Group.fromJson(Map<String, dynamic> json) {
    enUS = json['en_US'];
    zhCN = json['zh_CN'];
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['en_US'] = enUS;
    data['zh_CN'] = zhCN;
    return data;
  }
}
