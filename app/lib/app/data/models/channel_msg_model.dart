import 'package:flutter_metatel/app/data/models/channel_info_model_data.dart';

class ChannelMsgModel {
  int? code;
  String? message;
  List<ChannelMsgModelData>? msgDatas;

  ChannelMsgModel({this.code, this.message, this.msgDatas});

  ChannelMsgModel.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    message = json['message'];
    if (json['data'] != null) {
      msgDatas = <ChannelMsgModelData>[];
      json['data'].forEach((v) {
        msgDatas?.add(ChannelMsgModelData.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['code'] = code;
    data['message'] = message;
    data['data'] = msgDatas?.map((v) => v.toJson()).toList();

    return data;
  }
}

class ChannelMsgModelData {
  String? id;
  String? channelId;
  String? username;
  String? body;
  List<String>? ats;
  String? status;
  int? createTime;
  int? updateTime;
  String? op;
  ///本地用
  double? topMsgAdminTime;
  ChannelMsgModelData({
    this.id,
    this.channelId,
    this.username,
    this.body,
    this.ats,
    this.status,
    this.createTime,
    this.updateTime,
    this.op,
    this.topMsgAdminTime,
  });

  ChannelMsgModelData.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    channelId = json['channel_id'];
    username = json['username'];
    body = json['body'];
    ats = json['ats']?.cast<String>();
    status = json['status'];
    createTime = json['create_time'];
    updateTime = json['update_time'];
    op = json['op'];
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['id'] = id;
    data['channel_id'] = channelId;
    data['username'] = username;
    data['body'] = body;
    data['ats'] = ats;
    data['status'] = status;
    data['create_time'] = createTime;
    data['update_time'] = updateTime;
    data['op'] = op;
    return data;
  }
}
