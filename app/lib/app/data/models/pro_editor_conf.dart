import 'package:get/get.dart';
import 'package:pro_image_editor/pro_image_editor.dart';

ProImageEditorConfigs proEditorConfigs = ProImageEditorConfigs(
  i18n: I18n(
    various: I18nVarious(
      loadingDialogMsg: 'pro_editor_various_loadingDialogMsg'.tr,
      closeEditorWarningMessage:
          'pro_editor_various_closeEditorWarningMessage'.tr,
      closeEditorWarningTitle: 'pro_editor_various_closeEditorWarningTitle'.tr,
      closeEditorWarningConfirmBtn: 'pro_editor_various_closeEditorWarningConfirmBtn'.tr,
      closeEditorWarningCancelBtn: 'pro_editor_various_closeEditorWarningCancelBtn'.tr,
    ),
    paintEditor: I18nPaintingEditor(
      bottomNavigationBarText: 'pro_editor_paint_bottomNavigationBarText'.tr,
      freestyle: 'pro_editor_paint_freestyle'.tr,
      arrow: 'pro_editor_paint_arrow'.tr,
      line: 'pro_editor_paint_line'.tr,
      rectangle: 'pro_editor_paint_rectangle'.tr,
      circle: 'pro_editor_paint_circle'.tr,
      dashLine: 'pro_editor_paint_dashLine'.tr,
      lineWidth: 'pro_editor_paint_lineWidth'.tr,
      toggleFill: 'pro_editor_paint_toggleFill'.tr,
      undo: 'pro_editor_paint_undo'.tr,
      redo: 'pro_editor_paint_redo'.tr,
      done: 'pro_editor_paint_done'.tr,
      back: 'pro_editor_paint_back'.tr,
      smallScreenMoreTooltip: 'pro_editor_paint_smallScreenMoreTooltip'.tr,
    ),
    textEditor: I18nTextEditor(
      inputHintText: 'pro_editor_text_inputHintText'.tr,
      bottomNavigationBarText: 'pro_editor_text_bottomNavigationBarText'.tr,
      back: 'pro_editor_text_back'.tr,
      done: 'pro_editor_text_done'.tr,
      textAlign: 'pro_editor_text_textAlign'.tr,
      fontScale: 'pro_editor_text_fontScale'.tr,
      backgroundMode: 'pro_editor_text_backgroundMode'.tr,
      smallScreenMoreTooltip: 'pro_editor_text_smallScreenMoreTooltip'.tr,
    ),
    cropRotateEditor: I18nCropRotateEditor(
      bottomNavigationBarText: 'pro_editor_crop_rotate_bottomNavigationBarText'.tr,
      rotate: 'pro_editor_crop_rotate_rotate'.tr,
      ratio: 'pro_editor_crop_rotate_ratio'.tr,
      back: 'pro_editor_crop_rotate_back'.tr,
      done: 'pro_editor_crop_rotate_done'.tr,
      cancel: 'pro_editor_crop_rotate_cancel'.tr,
      prepareImageDialogMsg: 'pro_editor_crop_rotate_prepareImageDialogMsg'.tr,
      applyChangesDialogMsg: 'pro_editor_crop_rotate_applyChangesDialogMsg'.tr,
      smallScreenMoreTooltip: 'pro_editor_crop_rotate_smallScreenMoreTooltip'.tr,
      reset: 'pro_editor_crop_rotate_reset'.tr,
    ),
    filterEditor: I18nFilterEditor(
      bottomNavigationBarText: 'pro_editor_filter_bottomNavigationBarText'.tr,
      applyFilterDialogMsg: 'pro_editor_filter_applyFilterDialogMsg'.tr,
      back: 'pro_editor_filter_back'.tr,
      done: 'pro_editor_filter_done'.tr,
    ),
    blurEditor: I18nBlurEditor(
      applyBlurDialogMsg: 'pro_editor_blur_applyBlurDialogMsg'.tr,
      bottomNavigationBarText: 'pro_editor_blur_bottomNavigationBarText'.tr,
      back: 'pro_editor_blur_back'.tr,
      done: 'pro_editor_blur_done'.tr,
    ),
    emojiEditor: I18nEmojiEditor(
      bottomNavigationBarText: 'pro_editor_emoji_bottomNavigationBarText'.tr,
      search: 'pro_editor_emoji_search'.tr,
      noRecents: 'pro_editor_emoji_noRecents'.tr,
    ),
    stickerEditor: I18nStickerEditor(
      bottomNavigationBarText: 'pro_editor_sticker_bottomNavigationBarText'.tr,
    ),
    cancel: 'pro_editor_cancel'.tr,
    undo: 'pro_editor_undo'.tr,
    redo: 'pro_editor_redo'.tr,
    done: 'pro_editor_done'.tr,
    remove: 'pro_editor_remove'.tr,
    doneLoadingMsg: 'pro_editor_doneLoadingMsg'.tr,
  ),
);
