import 'role.dart';

class ChannelInfoModelData {
  int? code;
  String? id;
  String? title;
  String? avatar;
  String? wallpaper;
  String? owner;
  String? describe;
  bool? mute;
  bool? share;
  bool? approve;
  int? createTime;
  int? memberCount;
  String? announcement;
  int? applyCount;
  List<String>? admins;
  int? attribute;
  ///json 字符串
  String? options;
  int on = 0;
  int? limit;
  String? type;
  /// Dao相关属性
  String? chain;
  String? tokenAddress;
  double? minNumToken;
  List<Role>? tags; // DAO角色标签
  String? walletAddress;

  ChannelInfoModelData({
    this.code,
    this.id,
    this.title,
    this.avatar,
    this.wallpaper,
    this.owner,
    this.describe,
    this.mute,
    this.share,
    this.approve,
    this.createTime,
    this.memberCount,
    this.applyCount,
    this.admins,
    this.announcement,
    this.attribute,
    this.options,
    this.on=0,
  });

  ChannelInfoModelData.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    code = json['code'];
    title = json['title'];
    avatar = json['avatar'];
    wallpaper = json['wallpaper'];
    owner = json['owner'];
    describe = json['describe'];
    mute = json['mute'];
    share = json['share'];
    approve = json['approve'];
    createTime = json['create_time'];
    memberCount = json['member_count'];
    applyCount = json['apply_count'];
    announcement = json['announcement'];
    admins = json['admins']?.cast<String>();
    attribute = json['attribute'];
    options = json['options'];
    on = json['on']??0;
    limit = json['limit'];
    type = json['type'];
    chain = json['chain'];
    tokenAddress = json['token_address'];
    minNumToken = json['min_num_token'].toDouble();
    if (json['tags'] != null) {
      tags = <Role>[];
      json['tags'].forEach((v) {
        tags?.add(Role.fromJson(v));
      });
    }
    walletAddress = json['user_address'];
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['id'] = id;
    data['title'] = title;
    data['avatar'] = avatar;
    data['wallpaper'] = wallpaper;
    data['owner'] = owner;
    data['describe'] = describe;
    data['mute'] = mute;
    data['share'] = share;
    data['approve'] = approve;
    data['create_time'] = createTime;
    data['member_count'] = memberCount;
    data['apply_count'] = applyCount;
    data['admins'] = admins;
    data['code'] = code;
    data['announcement'] = announcement;
    data['attribute'] = attribute;
    data['options'] = options;
    data['on'] = on;
    data['limit'] = limit;
    data['type'] = type;
    data['chain'] = chain;
    data['token_address'] = tokenAddress;
    data['min_num_token'] = minNumToken;
    if (tags != null) {
      data['tags'] = tags?.map((v) => v.toJson()).toList();
    }
    data['user_address'] = walletAddress;
    return data;
  }

  @override
  bool operator ==(Object? other) =>
      identical(this, other) || other is ChannelInfoModelData && id == other.id;

  @override
  int get hashCode => super.hashCode;
}

class ChannelOptions{
  bool? contactLimit;
  List<TopMsgAdminData>? topMsgDataList;
  int? count;///options修改次数
  ChannelOptions({this.contactLimit});
  ChannelOptions.fromJson(Map<String, dynamic> json) {
    contactLimit = json['contact_limit'];
    count = json['count'];
    var jsonList = json['topMsgDataList'];
    if(jsonList!=null){
      topMsgDataList = <TopMsgAdminData>[];
      jsonList.forEach((v) {
        topMsgDataList?.add(TopMsgAdminData.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['contact_limit'] = contactLimit;
    data['count'] = count;
    data['topMsgDataList'] = topMsgDataList?.map((v) => v.toJson()).toList();
    return data;
  }
}
class TopMsgAdminData{
  String? msgUuid;
  int? topTime;///置顶时间

  TopMsgAdminData({required this.topTime,required this.msgUuid});
  TopMsgAdminData.fromJson(Map<String, dynamic> json) {

    msgUuid = json['msgUuid'];
    topTime = json['topTime'];
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['msgUuid'] = msgUuid;
    data['topTime'] = topTime;
    return data;
  }
}