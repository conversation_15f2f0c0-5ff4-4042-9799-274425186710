import 'channel_info_model_data.dart';

class ChannelInfoModel {
  int? code;
  String? message;
  ChannelInfoModelData? info;

  ChannelInfoModel({this.code, this.message, this.info});

  ChannelInfoModel.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    message = json['message'];
    info = json['data'] != null
        ? ChannelInfoModelData?.fromJson(json['data'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['code'] = code;
    data['message'] = message;
    data['data'] = info?.toJson();
    return data;
  }
}
