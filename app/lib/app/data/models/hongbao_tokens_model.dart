///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class HongbaoTokensModelData {
/*
{
  "id": 1,
  "chainid": "756",
  "token_address": "0x0CB8C20eE462D350C2dA5408eBB681ddF11D25f2",
  "symbol": "SUN",
  "decimals": 18,
  "enable": true,
  "min_amount": 1
} 
*/

  String? id;
  String? chainid;
  String? tokenAddress;
  String? symbol;
  String? decimals;
  bool? enable;
  String? minAmount;
  bool? isSelected;

  HongbaoTokensModelData({
    this.id,
    this.chainid,
    this.tokenAddress,
    this.symbol,
    this.decimals,
    this.enable,
    this.minAmount,
  });
  HongbaoTokensModelData.fromJson(Map<String, dynamic> json) {
    id = json['id']?.toString();
    chainid = json['chainid']?.toString();
    tokenAddress = json['token_address']?.toString();
    symbol = json['symbol']?.toString();
    decimals = json['decimals']?.toString();
    enable = json['enable'];
    minAmount = json['min_amount']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['id'] = id;
    data['chainid'] = chainid;
    data['token_address'] = tokenAddress;
    data['symbol'] = symbol;
    data['decimals'] = decimals;
    data['enable'] = enable;
    data['min_amount'] = minAmount;
    return data;
  }
}

class HongbaoTokensModel {
/*
{
  "code": 200,
  "msg": "success",
  "data": [
    {
      "id": 1,
      "chainid": "756",
      "token_address": "0x0CB8C20eE462D350C2dA5408eBB681ddF11D25f2",
      "symbol": "SUN",
      "decimals": 18,
      "enable": true,
      "min_amount": 1
    }
  ]
} 
*/

  String? code;
  String? msg;
  List<HongbaoTokensModelData?>? data;

  HongbaoTokensModel({
    this.code,
    this.msg,
    this.data,
  });
  HongbaoTokensModel.fromJson(Map<String, dynamic> json) {
    code = json['code']?.toString();
    msg = json['msg']?.toString();
  if (json['data'] != null) {
  final v = json['data'];
  final arr0 = <HongbaoTokensModelData>[];
  v.forEach((v) {
  arr0.add(HongbaoTokensModelData.fromJson(v));
  });
    this.data = arr0;
    }
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['code'] = code;
    data['msg'] = msg;
    if (this.data != null) {
      final v = this.data;
      final arr0 = [];
  v!.forEach((v) {
  arr0.add(v!.toJson());
  });
      data['data'] = arr0;
    }
    return data;
  }
}
