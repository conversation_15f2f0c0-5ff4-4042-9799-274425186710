class FileOptionOperationModel {
  String? action;
  int? chatType;
  int? time;
  String? event;
  String? body;
  int? type;
  String? msgId;
  String? owner;

  FileOptionOperationModel(
      {this.action,
      this.chatType,
      this.time,
      this.event,
      this.body,
      this.type,
      this.msgId,
      this.owner});

  FileOptionOperationModel.fromJson(Map<String, dynamic> json) {
    action = json['action'];
    chatType = json['chat_type'];
    time = json['time'];
    event = json['event'];
    body = json['body'];
    type = json['type'];
    msgId = json['msg_id'];
    owner = json['owner'];
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['action'] = action;
    data['chat_type'] = chatType;
    data['time'] = time;
    data['event'] = event;
    data['body'] = body;
    data['type'] = type;
    data['msg_id'] = msgId;
    data['owner'] = owner;
    return data;
  }
}
