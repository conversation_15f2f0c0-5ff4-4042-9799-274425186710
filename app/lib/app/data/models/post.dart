

import 'res/moment/comment_res_model.dart';

class Post {
  String? id;
  String? groupId;
  User? user;
  String? content;
  List<String>? links;
  List<String>? imageUrls; // max 9
  String? videoUrl;
  String? videoThumbnail;
  double? videoThumbnailRatio;
  int? likeCount;
  int? shareCount;
  int? commentsCount;
  bool? isLike;
  String? createdAt;
  // comment pagination's page
  CommentPaginationModel? commentPaginationModel;
 
  Post(this.id, this.content, this.links, this.imageUrls, this.videoUrl, this.videoThumbnail);

  Post.fromJson(Map<String, dynamic> json) {
    id = json['postId']?.toString();
    groupId = json['groupId']?.toString();
    user = User(json['username']?.toString());
    content = json['content']?.toString();
    if (json['links'] != null) {
      final v = json['links'];
      final arr0 = <String>[];
      v.forEach((v) {
        arr0.add(v.toString());
      });
      links = arr0;
    }
    if (json['imageUrls'] != null) {
      final v = json['imageUrls'];
      final arr0 = <String>[];
      v.forEach((v) {
        arr0.add(v.toString());
      });
      imageUrls = arr0;
    }
    videoUrl = json['videoUrl']?.toString();
    videoThumbnail = json['videoThumbnail']?.toString();
    if (json['comments'] != null && json['comments'] is List) {
      commentPaginationModel = CommentPaginationModel.init(json["comments"]);
    }
    likeCount = json['likeCount'];
    shareCount = json['shareCount'];
    commentsCount = json['commentsCount'];
    isLike = json['isLike'];
    createdAt = json['createdTime']?.toString();
  }

  // Future<void> addVideoThumbnailRatio() async {
  //   if(videoThumbnail != null){ // 查缩略图的比例
  //     // var v = Image(image: NetworkImage(videoThumbnail!),);
  //     File image = File(videoThumbnail!);
  //     var decodedImage = await decodeImageFromList(image.readAsBytesSync());
  //     final width = decodedImage.width;
  //     final height = decodedImage.height;
  //     videoThumbnailRatio = (width / height);
  //   } 
  // }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['postId'] = id;
    data['groupId'] = groupId;
    data['username'] = user?.userName;
    data['content'] = content;
    if (links != null) {
      final v = links;
      final arr0 = [];
      v!.forEach((v) {
        arr0.add(v);
      });
      data['links'] = arr0;
    }
    if (imageUrls != null) {
      final v = imageUrls;
      final arr0 = [];
      v!.forEach((v) {
        arr0.add(v);
      });
      data['imageUrls'] = arr0;
    }
    data['videoUrl'] = videoUrl;
    if (commentPaginationModel != null) {
      final v = commentPaginationModel?.comments;
      final arr0 = [];
      v!.forEach((v) {
        arr0.add(v.toJson());
      });
      data['comments'] = arr0;
    }
    data['likeCount'] = likeCount;
    data['shareCount'] = shareCount;
    data['commentsCount'] = commentsCount;
    data['isLike'] = isLike;
    data['createdTime'] = createdAt;
    return data;
  }
}

class Comment {
  String? id;
  User? user;
  String? comment;
  String? createdAt;

  Comment.fromJson(Map<String, dynamic> json) {
    id = json['id']?.toString();
    user = User(json['username']?.toString());
    comment = json['comment']?.toString();
    createdAt = json['createdTime']?.toString();
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['id'] = id;
    data['username'] = user?.userName;
    data['comment'] = comment;
    data['createdTime'] = createdAt;
    return data;
  }
}

class User {
  String? userName;
  String? nickname;
  String? avatarUrl;

  User(String? userName) {
    this.userName = userName;
  }
}
