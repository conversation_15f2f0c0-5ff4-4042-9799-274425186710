import 'dart:convert';
import 'dart:typed_data';

import 'package:flutter_metatel/app/data/models/pc_res_req_contact_model.dart';
import 'package:flutter_metatel/core/utils/app_log.dart';

class PcScanLoginBean {
  PcContact? mySelf;
  String? fileFragment;
  String? fileUrl;
  String? privateKey;
  String? config; ///配置json

  PcScanLoginBean({this.privateKey});

  Uint8List getByte() {
    var jsonStr = jsonEncode(toJson());
    AppLogger.d("PcScanLoginBean json==$jsonStr");
    var jsonBytes = Uint8List.fromList(utf8.encode(jsonStr));
    return jsonBytes;
  }
  Map<String, dynamic> toJson() {
    final mapData = <String, dynamic>{};
    mapData['mySelf'] = mySelf?.toJson();
    mapData['fileFragment'] = fileFragment;
    mapData['fileUrl'] = fileUrl;
    mapData['privateKey'] = privateKey;
    mapData['config'] = config;
    return mapData;
  }
}
