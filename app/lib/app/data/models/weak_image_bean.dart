//FileName weak_image_bean
// <AUTHOR>
//@Date 2022/8/5 15:50
import 'dart:typed_data';

class WeakImageBean{
    String path;
    Uint8List? data;
    WeakImageBean(this.path,this.data);
    @override
    bool operator ==(Object other) =>
        identical(this, other) ||
            other is WeakImageBean &&
                runtimeType == other.runtimeType &&
               path == other.path;

    @override
    int get hashCode => hashCode;
}
