class ChannelOperation {
  String? action;
  int? chatType;
  int? time;
  String? event;
  String? body;
  int? type;
  int? targetTime;
  List<String>? targetId;
  String? msgId;
  String? owner;

  ChannelOperation(
      {this.action,
      this.chatType,
      this.time,
      this.event,
      this.body,
      this.type,
      this.targetTime,
      this.targetId,
      this.msgId,
      this.owner});

  ChannelOperation.fromJson(Map<String, dynamic> json) {
    action = json['action'];
    chatType = json['chat_type'];
    time = json['time'];
    event = json['event'];
    body = json['body'];
    type = json['type'];
    targetTime = json['target_time'];
    targetId = json['target_id']?.cast<String>();
    msgId = json['msg_id'];
    owner = json['owner'];
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['action'] = action;
    data['chat_type'] = chatType;
    data['time'] = time;
    data['event'] = event;
    data['body'] = body;
    data['type'] = type;
    data['target_time'] = targetTime;
    data['target_id'] = targetId;
    data['msg_id'] = msgId;
    data['owner'] = owner;
    return data;
  }
}
