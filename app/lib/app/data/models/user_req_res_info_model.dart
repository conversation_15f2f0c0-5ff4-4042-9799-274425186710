class UserReqResInfoModel {
  int? chatType;
  String? action;
  Body? body;
  String? msgId;
  int? time;
  int? type;
  String? owner;

  UserReqResInfoModel(
      {this.chatType,
      this.action,
      this.body,
      this.msgId,
      this.time,
      this.type,
      this.owner});

  UserReqResInfoModel.fromJson(Map<String, dynamic> json) {
    chatType = json['chat_type'];
    action = json['action'];
    body = json['body'] != null ? Body?.fromJson(json['body']) : null;
    msgId = json['msg_id'];
    time = json['time'];
    type = json['type'];
    owner = json['owner'];
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['chat_type'] = chatType;
    data['action'] = action;
    if (body != null) {
      data['body'] = body?.toJson();
    }
    data['msg_id'] = msgId;
    data['time'] = time;
    data['type'] = type;
    data['owner'] = owner;
    return data;
  }
}

class Body {
  String? userName;
  String? displayName;
  String? downUrl;
  String? fileFragment;
  String? firstName;
  String? lastName;
  String? phoneNumber;

  Body(
      {this.userName,
      this.displayName,
      this.downUrl,
      this.fileFragment,
      this.firstName,
      this.lastName,
      this.phoneNumber});

  Body.fromJson(Map<String, dynamic> json) {
    userName = json['user_name'];
    displayName = json['display_name'];
    downUrl = json['down_url'];
    fileFragment = json['file_fragment'];
    firstName = json['first_name'];
    lastName = json['last_name'];
    phoneNumber = json['phone_number'];
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['user_name'] = userName;
    data['display_name'] = displayName;
    data['down_url'] = downUrl;
    data['file_fragment'] = fileFragment;
    data['first_name'] = firstName;
    data['last_name'] = lastName;
    data['phone_number'] = phoneNumber;
    return data;
  }
}
