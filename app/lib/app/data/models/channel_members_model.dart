class ChannelMembersModel {
  int? code;
  String? message;
  ChannelMembersData? data;

  ChannelMembersModel({this.code, this.message, this.data});

  ChannelMembersModel.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    message = json['message'];
    data = json['data'] != null
        ? ChannelMembersData?.fromJson(json['data'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final mapData = <String, dynamic>{};
    mapData['code'] = code;
    mapData['message'] = message;
    mapData['data'] = data?.toJson();
    return mapData;
  }
}

class ChannelMembersData {
  String? owner;
  List<String>? admins;
  List<String>? members;

  ChannelMembersData({this.owner, this.admins, this.members});

  ChannelMembersData.fromJson(Map<String, dynamic> json) {
    owner = json['owner'];
    admins = json['admins']?.cast<String>();
    members = json['members']?.cast<String>();
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['owner'] = owner;
    data['admins'] = admins;
    data['members'] = members;
    return data;
  }
}
