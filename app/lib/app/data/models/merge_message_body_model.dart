
class MergeMessageBody {

  List<MergeBean>? mergeBeanList;
  String? title;
  int? chatType;

  MergeMessageBody({this.mergeBeanList, this.title, this.chatType});

  MergeMessageBody.fromJson(Map<String, dynamic> json) {

    if (json['margeBeanList'] != null) {
      mergeBeanList = <MergeBean>[];
      json['margeBeanList'].forEach((v) {
        mergeBeanList?.add(MergeBean.fromJson(v));
      });
    }
    title = json['title'];
    chatType = json['chatType'];
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    if (mergeBeanList != null) {
      data['margeBeanList'] = mergeBeanList?.map((v) => v.toJson()).toList();
    }
    data['title'] = title;
    data['chatType'] = chatType;
    return data;
  }
}

class MergeBean {
  String? displayName;
  String? fileName;
  String? filePath;
  int? fileSize;
  String? body;
  String? msgId;
  DateTime? time;
  int? type;
  String? userName;
  String? owner;
  String? avatarPath;
  String? fileFragment; //密钥
  String? thumbnailPath;
  String? thumbnailUrl;
  String? thumbnailFragment;
  String? fileUrl;
  int fileState=-1;
  int? progress;
  MergeBean getSendMsgBean(){
    MergeBean bean=MergeBean();
    bean.displayName=displayName;
    bean.type=type;
    bean.userName=userName;
    bean.owner=owner;
    bean.msgId=msgId;
    bean.avatarPath=avatarPath;
    bean.fileName=fileName;
    bean.filePath=filePath;
    bean.fileUrl=fileUrl;
    bean.fileUrl=thumbnailUrl;
    bean.fileState=fileState;
    bean.progress=progress;
    if ((body??"").length>25){
      bean.body=body!.substring(0,25);
    } else {
      bean.body=body;
    }
    return bean;
  }
  MergeBean(
      {this.displayName,
      this.fileName,
      this.filePath,
      this.body,
      this.msgId,
      this.time,
      this.type,
      this.userName,
      this.owner,
      this.avatarPath,
      this.fileFragment,
      this.fileState=-1,
      this.thumbnailUrl,
      this.fileUrl,
      this.progress,
      });
  MergeBean.fromJson(Map<String, dynamic> json) {
    displayName = json['display_name'];
    fileName = json['file_name'];
    filePath = json['file_path'];
    body = json['body'];
    msgId = json['msg_id'];
    int? timeTemp = json['time'];
    time=timeTemp==null?null:DateTime.fromMillisecondsSinceEpoch(timeTemp);
    type = json['type'];
    userName = json['user_name'];
    owner = json['owner'];
    avatarPath = json['avatar_path'];
    fileFragment = json['file_fragment'];
    fileSize = json['fileSize'];
    thumbnailPath=json['thumbnailPath'];
    thumbnailFragment=json['thumbnailFragment'];
    fileUrl=json['fileUrl'];
    thumbnailUrl=json['thumbnailUrl'];
    progress=json['progress'];
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['display_name'] = displayName;
    data['file_name'] = fileName;
    data['file_path'] = filePath;
    data['body'] = body;
    data['msg_id'] = msgId;
    data['time'] = time?.millisecondsSinceEpoch;
    data['type'] = type;
    data['user_name'] = userName;
    data['owner'] = owner;
    data['avatar_path'] = avatarPath;
    data['fileSize'] = fileSize;
    data['file_fragment'] = fileFragment;
    data['thumbnailPath'] = thumbnailPath;
    data['thumbnailFragment'] = thumbnailFragment;
    data['fileUrl'] = fileUrl;
    data['thumbnailUrl'] = thumbnailUrl;
    data['progress'] = progress;

    return data;
  }
}
