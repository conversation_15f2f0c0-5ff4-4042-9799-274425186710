class GroupModel {
  int? chatType;
  String? action;
  String? fileUrl;
  String? fileFragment;
  String? msgId;
  int? time;
  int? type;
  String? owner;

  GroupModel(
      {this.chatType,
      this.action,
      this.fileUrl,
      this.fileFragment,
      this.msgId,
      this.time,
      this.type,
      this.owner});

  GroupModel.fromJson(Map<String, dynamic> json) {
    chatType = json['chat_type'];
    action = json['action'];
    fileUrl = json['file_url'];
    fileFragment = json['file_fragment'];
    msgId = json['msg_id'];
    time = json['time'];
    type = json['type'];
    owner = json['owner'];
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['chat_type'] = chatType;
    data['action'] = action;
    data['file_url'] = fileUrl;
    data['file_fragment'] = fileFragment;
    data['msg_id'] = msgId;
    data['time'] = time;
    data['type'] = type;
    data['owner'] = owner;
    return data;
  }
}
