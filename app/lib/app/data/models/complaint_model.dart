class Complaint {
  String? title;
  String? type;
  String? body;
  List<String>? message;
  List<String>? attachment;
  String? operator;
  String? owner;

  Complaint(
      {this.title,
      this.type,
      this.body,
      this.message,
      this.attachment,
      this.owner,
      this.operator});

  Complaint.fromJson(Map<String, dynamic> json) {
    title = json['title'];
    type = json['type'];
    body = json['body'];
    message = json['message'].cast<String>();
    attachment = json['attachment'].cast<String>();
    owner = json['owner'];
    operator = json['operator'];
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['title'] = title;
    data['type'] = type;
    data['body'] = body;
    data['message'] = message;
    data['attachment'] = attachment;
    data['owner'] = owner;
    data['operator'] = operator;
    return data;
  }
}
