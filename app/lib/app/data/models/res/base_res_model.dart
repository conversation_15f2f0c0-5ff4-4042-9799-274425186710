import 'package:flutter_metatel/core/utils/app_log.dart';

class BaseRes {
  int? code;
  String? msg;
  String? message;

  BaseRes({this.code, this.msg, this.message});

  BaseRes.fromJson(Map<String, dynamic> json) {
    var c = json['code'];
    if(c.runtimeType==String){
      try{
        code=int.parse(c);
      }catch(e){
        code=-1;
      }
    }else{
      code=c;
    }
    msg = json['msg'];
    message = json['message'];
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['code'] = code;
    data['msg'] = msg;
    data['message'] = message;
    return data;
  }
}