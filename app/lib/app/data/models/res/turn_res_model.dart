import '../turn_model.dart';
import 'base_res_model.dart';

class TurnResModel extends BaseRes {
  TurnModel? data;

  TurnResModel({int? code, String? msg, this.data})
      : super(code: code, msg: msg);

  TurnResModel.fromJson(Map<String, dynamic> json) : super.fromJson(json) {
    data = json['data'] != null ? TurnModel?.fromJson(json['data']) : null;
  }

  @override
  Map<String, dynamic> toJson() {
    final body = super.toJson();
    body['data'] = data?.toJson();
    return body;
  }
}
