import 'package:flutter_metatel/core/utils/app_log.dart';

class MeetingBaseRes {
  String? code;
  String? msg;
  Bean? data;

  MeetingBaseRes({this.code, this.msg, this.data});

  MeetingBaseRes.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    msg = json['msg'];
    data = Bean.fromJson(json['data']);
  }

  Map<String, dynamic> toJson() {
    final bean = <String, dynamic>{};
    bean['code'] = code;
    bean['msg'] = msg;
    bean['data'] = data?.toJson();
    return bean;
  }
}

class Bean {
  String? joinUrl;

  Bean.fromJson(Map<String, dynamic> json) {
    joinUrl = json['join_url'];
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['join_url'] = joinUrl;
    return data;
  }
}
