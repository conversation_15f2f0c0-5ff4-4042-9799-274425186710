/// code : 200
/// msg : "success"
/// data : [{"id":1,"account":"azsdfasdsdsds","main_address":"0x1fb5ADD6Fe1f17D84FC38cF34E2A21Fc1dd3ADdD","service_address":"0x9b91747859cbd80215a704cf440521e5d06ee6da","domain":"n001.linksay.site","create_at":0}]

class StakingServiceListRes {
  StakingServiceListRes({
      int? code, 
      String? msg, 
      List<ServiceListData>? data,}){
    _code = code;
    _msg = msg;
    _data = data;
}

  StakingServiceListRes.fromJson(dynamic json) {
    _code = json['code'];
    _msg = json['msg'];
    if (json['data'] != null) {
      _data = [];
      json['data'].forEach((v) {
        _data?.add(ServiceListData.fromJson(v));
      });
    }
  }
  int? _code;
  String? _msg;
  List<ServiceListData>? _data;
StakingServiceListRes copyWith({  int? code,
  String? msg,
  List<ServiceListData>? data,
}) => StakingServiceListRes(  code: code ?? _code,
  msg: msg ?? _msg,
  data: data ?? _data,
);
  int? get code => _code;
  String? get msg => _msg;
  List<ServiceListData>? get data => _data;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['code'] = _code;
    map['msg'] = _msg;
    if (_data != null) {
      map['data'] = _data?.map((v) => v.toJson()).toList();
    }
    return map;
  }

}

/// id : 1
/// account : "azsdfasdsdsds"
/// main_address : "0x1fb5ADD6Fe1f17D84FC38cF34E2A21Fc1dd3ADdD"
/// service_address : "0x9b91747859cbd80215a704cf440521e5d06ee6da"
/// domain : "n001.linksay.site"
/// create_at : 0

class ServiceListData {
  ServiceListData({
      int? id, 
      String? account, 
      String? mainAddress, 
      String? serviceAddress, 
      String? domain, 
      int? createAt,}){
    _id = id;
    _account = account;
    _mainAddress = mainAddress;
    _serviceAddress = serviceAddress;
    _domain = domain;
    _createAt = createAt;
}

  ServiceListData.fromJson(dynamic json) {
    _id = json['id'];
    _account = json['account'];
    _mainAddress = json['main_address'];
    _serviceAddress = json['service_address'];
    _domain = json['domain'];
    _createAt = json['create_at'];
  }
  int? _id;
  String? _account;
  String? _mainAddress;
  String? _serviceAddress;
  String? _domain;
  int? _createAt;
ServiceListData copyWith({  int? id,
  String? account,
  String? mainAddress,
  String? serviceAddress,
  String? domain,
  int? createAt,
}) => ServiceListData(  id: id ?? _id,
  account: account ?? _account,
  mainAddress: mainAddress ?? _mainAddress,
  serviceAddress: serviceAddress ?? _serviceAddress,
  domain: domain ?? _domain,
  createAt: createAt ?? _createAt,
);
  int? get id => _id;
  String? get account => _account;
  String? get mainAddress => _mainAddress;
  String? get serviceAddress => _serviceAddress;
  String? get domain => _domain;
  int? get createAt => _createAt;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['id'] = _id;
    map['account'] = _account;
    map['main_address'] = _mainAddress;
    map['service_address'] = _serviceAddress;
    map['domain'] = _domain;
    map['create_at'] = _createAt;
    return map;
  }

}