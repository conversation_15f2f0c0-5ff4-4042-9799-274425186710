/// code : 200
/// msg : "success"
/// data : {"available_node":[{"desc":"通信","is_selecked":false,"value":3}],"nodes":[{"desc":"存储","number":20000,"tokens":[{"address":"0xB10F3702C32E53A1b2D9E57b3824e63acf898456","decimals":18,"desc":"BossLP"}],"value":1},{"desc":"会议","number":2400,"tokens":[{"address":"0x3638b2c71D4CFDbEFeADEeAAA4F5514ac9a0754e","decimals":18,"desc":"K"}],"value":2},{"desc":"通信","number":20000,"tokens":[{"address":"0x807d73D2f8e8085345D04b3513840873beDD3a6B","decimals":18,"desc":"T"}],"value":3},{"desc":"频道","number":20000,"tokens":[{"address":"0x0CB8C20eE462D350C2dA5408eBB681ddF11D25f2","decimals":18,"desc":"SUN"}],"value":4},{"desc":"转发","number":20000,"tokens":[{"address":"0x0CB8C20eE462D350C2dA5408eBB681ddF11D25f2","decimals":18,"desc":"SUN"}],"value":5},{"desc":"区块","number":800,"tokens":[{"address":"0x0000000000000000000000000000000000002005","decimals":18,"desc":"govK"}],"value":6},{"desc":"前置","number":2000,"tokens":[{"address":"0x0CB8C20eE462D350C2dA5408eBB681ddF11D25f2","decimals":18,"desc":"SUN"}],"value":7},{"desc":"直播","number":3600,"tokens":[{"address":"0x3638b2c71D4CFDbEFeADEeAAA4F5514ac9a0754e","decimals":18,"desc":"K"}],"value":8},{"desc":"钱包","number":20000,"tokens":[{"address":"0x0CB8C20eE462D350C2dA5408eBB681ddF11D25f2","decimals":18,"desc":"SUN"}],"value":9},{"desc":"视频","number":100,"tokens":[{"address":"0x0CB8C20eE462D350C2dA5408eBB681ddF11D25f2","decimals":18,"desc":"SUN"}],"value":10},{"desc":"跨链","number":800,"tokens":[{"address":"0x3638b2c71D4CFDbEFeADEeAAA4F5514ac9a0754e","decimals":18,"desc":"K"}],"value":11},{"desc":"交易","number":800,"tokens":[{"address":"0x3638b2c71D4CFDbEFeADEeAAA4F5514ac9a0754e","decimals":18,"desc":"K"}],"value":12}],"staking_contract":"0x3638b2c71D4CFDbEFeADEeAAA4F5514ac9a0754e"}

class StakingNodeInfoRes {
  StakingNodeInfoRes({
    int? code,
    String? msg,
    StakingNodeInfoData? data,}){
    _code = code;
    _msg = msg;
    _data = data;
  }

  StakingNodeInfoRes.fromJson(dynamic json) {
    _code = json['code'];
    _msg = json['msg'];
    _data = json['data'] != null ? StakingNodeInfoData.fromJson(json['data']) : null;
  }
  int? _code;
  String? _msg;
  StakingNodeInfoData? _data;
  StakingNodeInfoRes copyWith({  int? code,
    String? msg,
    StakingNodeInfoData? data,
  }) => StakingNodeInfoRes(  code: code ?? _code,
    msg: msg ?? _msg,
    data: data ?? _data,
  );
  int? get code => _code;
  String? get msg => _msg;
  StakingNodeInfoData? get data => _data;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['code'] = _code;
    map['msg'] = _msg;
    if (_data != null) {
      map['data'] = _data?.toJson();
    }
    return map;
  }

}

/// available_node : [{"desc":"通信","is_selecked":false,"value":3}]
/// nodes : [{"desc":"存储","number":20000,"tokens":[{"address":"0xB10F3702C32E53A1b2D9E57b3824e63acf898456","decimals":18,"desc":"BossLP"}],"value":1},{"desc":"会议","number":2400,"tokens":[{"address":"0x3638b2c71D4CFDbEFeADEeAAA4F5514ac9a0754e","decimals":18,"desc":"K"}],"value":2},{"desc":"通信","number":20000,"tokens":[{"address":"0x807d73D2f8e8085345D04b3513840873beDD3a6B","decimals":18,"desc":"T"}],"value":3},{"desc":"频道","number":20000,"tokens":[{"address":"0x0CB8C20eE462D350C2dA5408eBB681ddF11D25f2","decimals":18,"desc":"SUN"}],"value":4},{"desc":"转发","number":20000,"tokens":[{"address":"0x0CB8C20eE462D350C2dA5408eBB681ddF11D25f2","decimals":18,"desc":"SUN"}],"value":5},{"desc":"区块","number":800,"tokens":[{"address":"0x0000000000000000000000000000000000002005","decimals":18,"desc":"govK"}],"value":6},{"desc":"前置","number":2000,"tokens":[{"address":"0x0CB8C20eE462D350C2dA5408eBB681ddF11D25f2","decimals":18,"desc":"SUN"}],"value":7},{"desc":"直播","number":3600,"tokens":[{"address":"0x3638b2c71D4CFDbEFeADEeAAA4F5514ac9a0754e","decimals":18,"desc":"K"}],"value":8},{"desc":"钱包","number":20000,"tokens":[{"address":"0x0CB8C20eE462D350C2dA5408eBB681ddF11D25f2","decimals":18,"desc":"SUN"}],"value":9},{"desc":"视频","number":100,"tokens":[{"address":"0x0CB8C20eE462D350C2dA5408eBB681ddF11D25f2","decimals":18,"desc":"SUN"}],"value":10},{"desc":"跨链","number":800,"tokens":[{"address":"0x3638b2c71D4CFDbEFeADEeAAA4F5514ac9a0754e","decimals":18,"desc":"K"}],"value":11},{"desc":"交易","number":800,"tokens":[{"address":"0x3638b2c71D4CFDbEFeADEeAAA4F5514ac9a0754e","decimals":18,"desc":"K"}],"value":12}]
/// staking_contract : "0x3638b2c71D4CFDbEFeADEeAAA4F5514ac9a0754e"

class StakingNodeInfoData {
  StakingNodeInfoData({
    List<AvailableNode>? availableNode,
    List<Nodes>? nodes,
    String? stakingContract,}){
    _availableNode = availableNode;
    _nodes = nodes;
    _stakingContract = stakingContract;
  }

  StakingNodeInfoData.fromJson(dynamic json) {
    if (json['available_node'] != null) {
      _availableNode = [];
      json['available_node'].forEach((v) {
        _availableNode?.add(AvailableNode.fromJson(v));
      });
    }
    if (json['nodes'] != null) {
      _nodes = [];
      json['nodes'].forEach((v) {
        _nodes?.add(Nodes.fromJson(v));
      });
    }
    _stakingContract = json['staking_contract'];
  }
  List<AvailableNode>? _availableNode;
  List<Nodes>? _nodes;
  String? _stakingContract;
  StakingNodeInfoData copyWith({  List<AvailableNode>? availableNode,
    List<Nodes>? nodes,
    String? stakingContract,
  }) => StakingNodeInfoData(  availableNode: availableNode ?? _availableNode,
    nodes: nodes ?? _nodes,
    stakingContract: stakingContract ?? _stakingContract,
  );
  List<AvailableNode>? get availableNode => _availableNode;
  List<Nodes>? get nodes => _nodes;
  String? get stakingContract => _stakingContract;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    if (_availableNode != null) {
      map['available_node'] = _availableNode?.map((v) => v.toJson()).toList();
    }
    if (_nodes != null) {
      map['nodes'] = _nodes?.map((v) => v.toJson()).toList();
    }
    map['staking_contract'] = _stakingContract;
    return map;
  }

}

/// desc : "存储"
/// number : 20000
/// tokens : [{"address":"0xB10F3702C32E53A1b2D9E57b3824e63acf898456","decimals":18,"desc":"BossLP"}]
/// value : 1

class Nodes {
  Nodes({
    String? desc,
    int? number,
    List<Tokens>? tokens,
    int? value,}){
    _desc = desc;
    _number = number;
    _tokens = tokens;
    _value = value;
  }

  Nodes.fromJson(dynamic json) {
    _desc = json['desc'];
    _number = json['number'];
    if (json['tokens'] != null) {
      _tokens = [];
      json['tokens'].forEach((v) {
        _tokens?.add(Tokens.fromJson(v));
      });
    }
    _value = json['value'];
  }
  String? _desc;
  int? _number;
  List<Tokens>? _tokens;
  int? _value;
  Nodes copyWith({  String? desc,
    int? number,
    List<Tokens>? tokens,
    int? value,
  }) => Nodes(  desc: desc ?? _desc,
    number: number ?? _number,
    tokens: tokens ?? _tokens,
    value: value ?? _value,
  );
  String? get desc => _desc;
  int? get number => _number;
  List<Tokens>? get tokens => _tokens;
  int? get value => _value;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['desc'] = _desc;
    map['number'] = _number;
    if (_tokens != null) {
      map['tokens'] = _tokens?.map((v) => v.toJson()).toList();
    }
    map['value'] = _value;
    return map;
  }

}

/// address : "0xB10F3702C32E53A1b2D9E57b3824e63acf898456"
/// decimals : 18
/// desc : "BossLP"

class Tokens {
  Tokens({
    String? address,
    int? decimals,
    String? desc,}){
    _address = address;
    _decimals = decimals;
    _desc = desc;
  }

  Tokens.fromJson(dynamic json) {
    _address = json['address'];
    _decimals = json['decimals'];
    _desc = json['desc'];
  }
  String? _address;
  int? _decimals;
  String? _desc;
  Tokens copyWith({  String? address,
    int? decimals,
    String? desc,
  }) => Tokens(  address: address ?? _address,
    decimals: decimals ?? _decimals,
    desc: desc ?? _desc,
  );
  String? get address => _address;
  int? get decimals => _decimals;
  String? get desc => _desc;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['address'] = _address;
    map['decimals'] = _decimals;
    map['desc'] = _desc;
    return map;
  }

}

/// desc : "通信"
/// is_selecked : false
/// value : 3

class AvailableNode {
  AvailableNode({
    String? desc,
    bool? isSelecked,
    int? value,}){
    _desc = desc;
    _isSelecked = isSelecked;
    _value = value;
  }

  AvailableNode.fromJson(dynamic json) {
    _desc = json['desc'];
    _isSelecked = json['is_selecked'];
    _value = json['value'];
  }
  String? _desc;
  bool? _isSelecked;
  int? _value;
  AvailableNode copyWith({  String? desc,
    bool? isSelecked,
    int? value,
  }) => AvailableNode(  desc: desc ?? _desc,
    isSelecked: isSelecked ?? _isSelecked,
    value: value ?? _value,
  );
  String? get desc => _desc;
  bool? get isSelecked => _isSelecked;
  int? get value => _value;

  void setSelected(bool selected) {
    _isSelecked = selected;
  }

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['desc'] = _desc;
    map['is_selecked'] = _isSelecked;
    map['value'] = _value;
    return map;
  }

}