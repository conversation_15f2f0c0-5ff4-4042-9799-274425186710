class BaseResNew {
  int? code;
  String? msg;
  String? message;
  int? data;

  BaseResNew({this.code, this.msg, this.message ,this.data});

  BaseResNew.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    msg = json['msg'];
    message = json['message'];
    data = json['data'];
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['code'] = code;
    data['msg'] = msg;
    data['message'] = message;
    data['data'] = data;
    return data;
  }
}