
import 'package:flutter_metatel/app/data/models/res/base_res_model.dart';

class Oauth2AuthorizeModel extends BaseRes{
  Oauth2AuthorizeModelData? data;
  Oauth2AuthorizeModel({int? code, String? msg, this.data}) : super(code: code, msg: msg);
  Oauth2AuthorizeModel.fromJson(Map<String,dynamic> sourceJson):super.fromJson(sourceJson){
    if(code==200){
      // var decode = json.decode(sourceJson["data"]);
      data = sourceJson['data'] != null ? Oauth2AuthorizeModelData?.fromJson(sourceJson["data"]) : null;
    }
  }
}
class Oauth2AuthorizeModelData{
  String? code;
  String? state;
  String? publicKey;
  String? privateKey;
  String? address;
  String? userName; ///自己的userName
  String? displayName; ///自己的昵称
  String? avatarUrl; ///自己的头像地址
  Oauth2AuthorizeModelData(this.code,this.state);
  Oauth2AuthorizeModelData.fromJson(Map<String,dynamic> sourceJson){
    code=sourceJson['code'];
    state=sourceJson["state"];
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['code'] = code;
    data['state'] = state;
    data['public_key'] = publicKey;
    data['private_key'] = privateKey;
    data['address'] = address;
    data['user_name'] = userName;
    data['display_name'] = displayName;
    data['avatar_url'] = avatarUrl;
    return data;
  }
}

