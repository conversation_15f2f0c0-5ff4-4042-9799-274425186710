/*
 * <AUTHOR> <PERSON><PERSON>
 * @Date         : 2022-06-28 10:53:46
 * @Description  : TODO: Add description
 * @LastEditors  : <PERSON><PERSON>
 * @LastEditTime : 2022-08-29 11:08:58
 * @FilePath     : /flutter_metatel/lib/app/data/models/res/node_conf.dart
 * 
 * {
    "code": 200,
    "msg": "success",
    "data": {
        "base": {
            "chan": "",
            "oss": "",
            "own": "",
            "url": "https://dev.metatel.com.cn:8443"
        },
        "s3": {
            "bucket": "metatel",
            "endpoint": "dev.metatel.com.cn:4201",
            "region": "us-east-1",
            "ssl": "false",
            "use_virtual_adressing": false,
            "version": "2006-03-01"
        },
        "turn": {
            "credential": "\"#EDC4rfv\"",
            "urls": [
                "turn:**************:18872?transport=tcp",
                "turn:**************:18872?transport=udp",
                "turn:************:18872?transport=tcp",
                "turn:************:18872?transport=udp"
            ],
            "username": "turn"
        },
        "v": 1
    }
}
 */
import 'package:flutter_browser/flutter_browser.dart';
import 'package:flutter_metatel/app/data/models/res/base_res_model.dart';

import '../../../modules/dao/bean/banner_bean.dart';

/// eg：{"code":200,"msg":"success","data":{"base":{"stare_url":"https://dpapi.mex.show:443/#/pages/cs/index"},"dao_view":{"create":true,"create_center":true,"my_account":true,"my_order":true,"points_management":true,"pub_dynamic":true,"pub_work":true},"dao_conf":{"dao_base_url":"http://frp.metatel.com.cn:18099","dao_android_url":"http://frp.metatel.com.cn:18121/?node=","dao_ios_url":"http://frp.metatel.com.cn:18120/?node="},"s3":{"bucket":"metatel","endpoint":"dev.metatel.com.cn:4201","region":"us-east-1","ssl":false,"sts":"https://dev.metatel.com.cn:8443/metatelConf/v1/Aws/GetStsAssumeRole","use_virtual_addressing":false,"version":"2006-03-01"},"turn":{"credential":"MrX0DWolGL3BdFtD","urls":["turn:turn01.metatel.com.cn:5349?transport=tcp","turn:turn01.metatel.com.cn:5349?transport=udp"],"username":"turn"},"v":1}}
class NodeConf extends BaseRes{
  NodeConfData? data;
  NodeConf({int? code, String? msg, this.data}) : super(code: code, msg: msg);

  NodeConf.fromJson(Map<String, dynamic> json) : super.fromJson(json) {
    if(code==200){
      data = json['data'] != null ? NodeConfData?.fromJson(json['data']) : null;
    }
  }
}

class NodeConfData {
  Base? base;
  S3? s3;
  Turn? turn;
  DaoViewConf? daoViewConf;
  DaoConf? daoConf;
  InviteConf? inviteConf;
  HomeConf? homeConf;
  BrowserConf? browserConf;
  VirtualCardConf? virtualCardConf;
  NodeConfData.fromJson(Map<String, dynamic> json) {
    base = Base.fromJson(json["base"]);
    s3 = S3.fromJson(json["s3"]);
    turn = Turn.fromJson(json["turn"]);
    var jsonDaoView = json["dao_view"];
    if(jsonDaoView!=null){
      daoViewConf = DaoViewConf.fromJson(jsonDaoView);
    }
    var jsonDaoConf = json["dao"];
    if(jsonDaoConf!=null){
      daoConf = DaoConf.fromJson(jsonDaoConf);
    }
    var jsonInviteConf = json["invite"];
    if(jsonInviteConf!=null){
      inviteConf=InviteConf.fromJson(jsonInviteConf);
    }
    var jsonHomeConf = json["home"];
    if(jsonHomeConf!=null){
      homeConf=HomeConf.fromJson(jsonHomeConf);
    }
    var jsonBrowserConf = json["browser"];
    if(jsonBrowserConf!=null){
      browserConf=BrowserConf.fromJson(jsonBrowserConf);
    }
    var jsonVirtualCard = json["virtual_card"];
    if(jsonVirtualCard!=null){
      virtualCardConf=VirtualCardConf.fromJson(jsonVirtualCard);
    }
  }
}

class Base {
  String? channel;
  String? own;
  Sip? sip;
  String? mex;
  String? e2e; //连接
  String? push;
  String? stareUrl;
  String? inviteUrl; ///邀请推广接口url
  String? invitePosterUrl; ///邀请海报url
  String? hostingUrl; ///托管
  String? threeRc20TokenUrl;//3rc20token地址
  String? hongBaoUrl;//红包地址
  String? cstNftCardsAddress;//cstNft 地址
  int? maxNumberCount;
  List<int>? exchangeArray;
  bool? redeemable;
  String? quickRedemptionUrl;
  String? netWorkUrl;
  String? stakingUrl;
  bool? verifyHuman;
  String? meetingUrl;
  String? plan21Url;
  String? dolphinApiUrl;
  List<String>?  fullScreenList;

  Base.fromJson(Map<String, dynamic> json) {
    channel = json['chan'];
    mex = json['mex'];
    own = json['own'];
    e2e = json['e2e'];
    push = json['push'];
    stareUrl = json['stare_url'];
    sip=Sip.fromJson(json["sip"]);
    inviteUrl=json["invite_url"];
    invitePosterUrl=json["invite_poster_url"];
    hostingUrl= json['hosting_url'];
    threeRc20TokenUrl = json['three_rc20_token_url'];
    hongBaoUrl = json['hong_bao_url'];
    cstNftCardsAddress = json['cst_nft_cards_address'];
    maxNumberCount = json['max_number_count'];
    exchangeArray = json['exchange_array']?.cast<int>();
    redeemable = json['redeemable'];
    quickRedemptionUrl = json['quick_redemption_url'];
    verifyHuman = json['verify_human'];
    netWorkUrl = json['netWork_url'];
    stakingUrl = json['staking_url'];
    meetingUrl = json['meeting_url'];
    plan21Url = json['plan_21_url'];
    dolphinApiUrl = json['dolphin_url'];
    fullScreenList = json['full_screen_list']?.cast<String>();
  }
}

class S3 {
  String? bucket;
  String? endpoint;
  String? region;
  bool? ssl;
  bool? useVirtualAddressing;
  String? version;
  String? sts;//获取token的url
  S3.fromJson(Map<String, dynamic> json) {
    bucket = json['bucket'];
    endpoint = json['endpoint'];
    region = json['region'];
    ssl = json['ssl'];
    useVirtualAddressing = json['use_virtual_addressing'];
    version = json['version'];
    sts = json['sts'];

  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['bucket'] = bucket;
    data['endpoint'] = endpoint;
    data['region'] = region;
    data['ssl'] = ssl;
    data['use_virtual_addressing'] = useVirtualAddressing;
    data['version'] = version;
    data['sts'] = sts;
    return data;
  }
}

class Turn {
  String? credential;
  List<String>? urls;
  String? username;
  String? iceTransportPolicy;
  Turn.fromJson(Map<String, dynamic> json) {
    username = json['username'];
    credential = json['credential'];
    urls = json['urls']?.cast<String>();
    iceTransportPolicy = json['iceTransportPolicy'];
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['username'] = username;
    data['credential'] = credential;
    data['urls'] = urls;
    data['iceTransportPolicy'] = iceTransportPolicy;
    return data;
  }
}

class Sip{
  String? wss;
  String? domain;
  Sip.fromJson(Map<String, dynamic> json) {
    wss = json['wss'];
    domain = json['domain'];
  }
}
class DaoViewConf{
  bool? create; ///创建Dao
  bool? pubDynamic; ///发布动态
  bool? pubWork; ///发布作品
  bool? myOrder; ///我的订单
  bool? createCenter; ///创作中心
  bool? pointsManagement; //积分
  bool? pledge; ///质押
  bool? myAccount; ///我的账户

  DaoViewConf({this.create, this.pubDynamic, this.pubWork, this.myOrder,
    this.createCenter, this.pointsManagement, this.pledge,this.myAccount});
  DaoViewConf.fromJson(Map<String, dynamic> json) {
    create = json['create'];
    pubDynamic = json['pub_dynamic'];
    pubWork = json['pub_work'];
    myOrder = json['my_order'];
    createCenter = json['create_center'];
    pointsManagement=json["points_management"];
    pledge=json["pledge"];
    myAccount=json["my_account"];
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['create'] = create;
    data['pub_dynamic'] = pubDynamic;
    data['pub_work'] = pubWork;
    data['my_order'] = myOrder;
    data['create_center'] = createCenter;
    data['points_management'] = pointsManagement;
    data['my_account'] = myAccount;
    data['pledge'] = pledge;
    return data;
  }
}
class DaoConf {
  String? daoBaseUrl;
  String? daoAndroidUrl;
  String? daoIosUrl;
  List<BannerBean>? banner;
  ///
  DaoConf({this.daoBaseUrl, this.daoAndroidUrl, this.daoIosUrl,this.banner});

  DaoConf.fromJson(Map<String, dynamic> json) {
    daoBaseUrl = json['base_url'];
    daoAndroidUrl = json['android_url'];
    daoIosUrl = json['ios_url'];
    // if (json['banner'] != null) {
    //   banner = <BannerBean>[];
    //   json['banner'].forEach((v) {
    //     banner?.add(BannerBean.fromJson(v));
    //   });
    // }
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['base_url'] = daoBaseUrl;
    data['android_url'] = daoAndroidUrl;
    data['ios_url'] = daoIosUrl;
    // if (banner != null) {
    //   data['banner'] = banner?.map((v) => v.toJson()).toList();
    // }
    return data;
  }
}
class InviteConf{
  ///邀请推广接口url
  String? inviteUrl;

  ///邀请海报url
  String? invitePosterUrl;

  ///必须填邀请码
  bool? inviteCodeMust;
  //采矿地址
  String? miningUrl;
  bool? miningWithdrawAddressModify;
  InviteConf.fromJson(Map<String, dynamic> json) {
    inviteUrl=json["invite_url"];
    invitePosterUrl=json["invite_poster_url"];
    inviteCodeMust = json['invite_code_must'];
    miningUrl = json['mining_url'];
    miningWithdrawAddressModify = json['mining_withdraw_address_modify'];
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['invite_url'] = inviteUrl;
    data['invite_poster_url'] = invitePosterUrl;
    data['invite_code_must'] = inviteCodeMust;
    data['mining_url'] = miningUrl;

    return data;
  }
}
class HomeConf{
  ///是否显示浏览器
  bool? isBrowser;
  HomeConf.fromJson(Map<String, dynamic> json) {
    isBrowser=json["is_browser"];
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['is_browser'] = isBrowser;
    return data;
  }
}
class VirtualCardConf{
  String? apiUrl;
  String? rechargeDappUrl;
  bool? visible;
  VirtualCardConf.fromJson(Map<String, dynamic> json) {
    visible=json["visible"];
    rechargeDappUrl=json["recharge_dapp_url"];
    apiUrl=json["api_url"];
  }
}