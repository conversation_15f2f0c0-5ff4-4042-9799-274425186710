/// Keyboxid : ""
/// active : "flase"
/// address : "0x5A9b8Ca53587D586f45501C9156c13Cec8CDd079"
/// code : 200
/// keyboxPublicKey : ""
/// node : "test.linksay.site"
/// nodePublickKey : ""
/// role : 0
/// status : 0

class KeyBoxInfoRes {
  KeyBoxInfoRes({
      String? keyboxid, 
      bool? active,
      String? address, 
      num? code, 
      String? keyboxPublicKey, 
      String? node, 
      String? nodePublickKey,
      String? blockSyncStatus,
      num? blockHight,
      num? role, 
      num? status,}){
    _keyboxid = keyboxid;
    _active = active;
    _address = address;
    _code = code;
    _keyboxPublicKey = keyboxPublicKey;
    _node = node;
    _nodePublickKey = nodePublickKey;
    _role = role;
    _status = status;
    _blockHight = blockHight;
    _blockSyncStatus = blockSyncStatus;
}

  KeyBoxInfoRes.fromJson(dynamic json) {
    _keyboxid = json['Keyboxid'];
    _active = json['active'];
    _address = json['address'];
    _code = json['code'];
    _keyboxPublicKey = json['keyboxPublicKey'];
    _node = json['node'];
    _nodePublickKey = json['nodePublickKey'];
    _role = json['role'];
    _status = json['status'];
    _blockHight = json['blockHight'];
    _blockSyncStatus = json['block_sync_status'];
  }
  String? _keyboxid;
  bool? _active;
  String? _address;
  num? _code;
  String? _keyboxPublicKey;
  String? _node;
  String? _nodePublickKey;
  num? _role;
  num? _status;
  num? _blockHight;
  String? _blockSyncStatus;
KeyBoxInfoRes copyWith({  String? keyboxid,
  bool? active,
  String? address,
  num? code,
  String? keyboxPublicKey,
  String? node,
  String? nodePublickKey,
  num? role,
  num? status,
  num? blockHight,
}) => KeyBoxInfoRes(  keyboxid: keyboxid ?? _keyboxid,
  active: active ?? _active,
  address: address ?? _address,
  code: code ?? _code,
  keyboxPublicKey: keyboxPublicKey ?? _keyboxPublicKey,
  node: node ?? _node,
  nodePublickKey: nodePublickKey ?? _nodePublickKey,
  role: role ?? _role,
  status: status ?? _status,
  blockHight:blockHight ?? _blockHight,
);
  String? get keyboxid => _keyboxid;
  bool? get active => _active;
  setActive( bool active){
    _active = active;
  }
  String? get address => _address;
  num? get code => _code;
  String? get keyboxPublicKey => _keyboxPublicKey;
  String? get node => _node;
  String? get nodePublickKey => _nodePublickKey;
  num? get role => _role;
  num? get status => _status;
  num? get blockHight => _blockHight;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['Keyboxid'] = _keyboxid;
    map['active'] = _active;
    map['address'] = _address;
    map['code'] = _code;
    map['keyboxPublicKey'] = _keyboxPublicKey;
    map['node'] = _node;
    map['nodePublickKey'] = _nodePublickKey;
    map['role'] = _role;
    map['status'] = _status;
    map['blockHight'] = _blockHight;
    map['block_sync_status'] = _blockSyncStatus;
    return map;
  }

}