import 'base_res_model.dart';

class TempTbsRes extends BaseRes {
  TempTbsData? data;

  TempTbsRes({int? code, String? msg, this.data}) : super(code: code, msg: msg);

  TempTbsRes.fromJson(Map<String, dynamic> json) : super.fromJson(json) {
    data = json['data'] != null ? TempTbsData?.fromJson(json['data']) : null;
  }

  @override
  Map<String, dynamic> toJson() {
    final body = super.toJson();
    body['data'] = data?.toJson();
    return body;
  }
}

class TempTbsData {
  int? number;
  int? day;
  String? sbt;
  TempTbsData(
      {this.number,
      this.day,
      this.sbt,});

  TempTbsData.fromJson(Map<String, dynamic> json) {
    number = json['number'];
    day = json['day'];
    sbt = json['sbt'];
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['sbt'] = sbt;
    data['day'] = day;
    data['number'] = number;
    return data;
  }
}
