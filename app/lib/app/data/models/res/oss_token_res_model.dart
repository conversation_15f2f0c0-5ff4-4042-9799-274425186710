
import 'base_res_model.dart';

class OssTokenResModel extends BaseRes {
  OssTokenData? data;

  OssTokenResModel({int? code, String? msg, this.data})
      : super(code: code, msg: msg);

  OssTokenResModel.fromJson(Map<String, dynamic> json) : super.fromJson(json) {
    data = json['data'] != null ? OssTokenData?.fromJson(json['data']) : null;
  }

  @override
  Map<String, dynamic> toJson() {
    final body = super.toJson();
    body['data'] = data?.toJson();
    return body;
  }
}
class OssTokenData {
  String? accessKeyID;
  String? secretAccessKey;
  String? sessionToken;
  OssTokenData({this.accessKeyID, this.secretAccessKey, this.sessionToken});

  OssTokenData.fromJson(Map<String, dynamic> json) {
    accessKeyID = json['AccessKeyID'];
    secretAccessKey = json['SecretAccessKey'];
    sessionToken = json['SessionToken'];
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['AccessKeyID'] = accessKeyID;
    data['SecretAccessKey'] = secretAccessKey;
    data['SessionToken'] = sessionToken;
    return data;
  }
}