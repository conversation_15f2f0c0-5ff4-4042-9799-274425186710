
import 'package:flutter_metatel/app/data/models/res/base_res_model.dart';

class NodeInfoModel extends BaseRes{
  NodeInfoModelData? data;
  NodeInfoModel({int? code, String? msg, this.data}) : super(code: code, msg: msg);
  NodeInfoModel.fromJson(Map<String,dynamic> sourceJson):super.fromJson(sourceJson){
    if(code==200){
      // var decode = json.decode(sourceJson["data"]);
      data = sourceJson['data'] != null ? NodeInfoModelData?.fromJson(sourceJson["data"]) : null;
    }
  }
}
class NodeInfoModelData{
  String? node;
  String? publicKey;
  int? port;
  NodeInfoModelData.fromJson(Map<String,dynamic> sourceJson){
    node=sourceJson['node'];
    publicKey=sourceJson["public_key"];
    port = sourceJson['port'];
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['node'] = node;
    data['public_key'] = publicKey;
    data['port'] = port;
    return data;
  }
}

