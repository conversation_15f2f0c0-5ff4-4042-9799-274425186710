
import 'package:flutter_metatel/app/data/models/res/base_res_model.dart';

class NodeForward extends BaseRes{
  NodeForwardData? data;
  NodeForward({int? code, String? msg, this.data}) : super(code: code, msg: msg);
  NodeForward.fromJson(Map<String,dynamic> sourceJson):super.fromJson(sourceJson){
    if(code==200){
      // var decode = json.decode(sourceJson["data"]);
      data = sourceJson['data'] != null ? NodeForwardData?.fromJson(sourceJson["data"]) : null;
    }
  }
}
class NodeForwardData{
  int? port;
  String? protocol;
  String? uri;

  NodeForwardData.fromJson(Map<String,dynamic> sourceJson){
    port=sourceJson['port'];
    protocol=sourceJson["protocol"];
    uri = sourceJson["uri"];
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['protocol'] = protocol;
    data['port'] = port;
    data['uri'] = uri;
    return data;
  }
}

