/// code : 200
/// msg : "success"
/// data : [{"id":3,"nickname":"测试数据","msg_account":"aaaaaaaaaaa","main_address":"0xc4e52933c9c9cccb2c9324c1b55c3f2a56f55d17","service_address":"0x9b91747859cbd80215a704cf440521e5d06ee6da","server_domain":"el01.linksaychat.com","server_port":8080,"server_public_key":"xxxxxxxxxx","keybox_public_key":"xxxxxxxxxx","keybox_id":"el01","node_type":3,"keybox_status":false,"review_status":1,"description":"申请测试数据","create_at":*************},{"id":1,"nickname":"测试数据","msg_account":"aaaaaaaaaaaaaaaaaaa","main_address":"0xc4e52933c9c9cccb2c9324c1b55c3f2a56f55d17","service_address":"0x0ee3ec041ef6d18ccc113e26d0d150c39f7f8c93","server_domain":"n001.linksay.site","server_port":8080,"server_public_key":"aaaaaaaaaaa","keybox_public_key":"aaaaaaaaaaaaaaaa","keybox_id":"n001","node_type":1,"keybox_status":false,"review_status":1,"description":"申请测试数据","create_at":*************}]

class StakingListModelRes {
  StakingListModelRes({
      int? code, 
      String? msg, 
      List<StakingListData>? data,}){
    _code = code;
    _msg = msg;
    _data = data;
}

  StakingListModelRes.fromJson(dynamic json) {
    _code = json['code'];
    _msg = json['msg'];
    if (json['data'] != null) {
      _data = [];
      json['data'].forEach((v) {
        _data?.add(StakingListData.fromJson(v));
      });
    }
  }
  int? _code;
  String? _msg;
  List<StakingListData>? _data;
StakingListModelRes copyWith({  int? code,
  String? msg,
  List<StakingListData>? data,
}) => StakingListModelRes(  code: code ?? _code,
  msg: msg ?? _msg,
  data: data ?? _data,
);
  int? get code => _code;
  String? get msg => _msg;
  List<StakingListData>? get data => _data;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['code'] = _code;
    map['msg'] = _msg;
    if (_data != null) {
      map['data'] = _data?.map((v) => v.toJson()).toList();
    }
    return map;
  }

}

/// id : 3
/// nickname : "测试数据"
/// msg_account : "aaaaaaaaaaa"
/// main_address : "0xc4e52933c9c9cccb2c9324c1b55c3f2a56f55d17"
/// service_address : "0x9b91747859cbd80215a704cf440521e5d06ee6da"
/// server_domain : "el01.linksaychat.com"
/// server_port : 8080
/// server_public_key : "xxxxxxxxxx"
/// keybox_public_key : "xxxxxxxxxx"
/// keybox_id : "el01"
/// node_type : 3
/// keybox_status : false
/// review_status : 1
/// description : "申请测试数据"
/// create_at : *************

class StakingListData {
  StakingListData({
      int? id, 
      String? nickname, 
      String? msgAccount, 
      String? mainAddress, 
      String? serviceAddress, 
      String? serverDomain, 
      int? serverPort, 
      String? serverPublicKey, 
      String? keyboxPublicKey, 
      String? keyboxId, 
      int? nodeType, 
      bool? keyboxStatus, 
      int? reviewStatus, 
      String? description, 
      int? createAt,}){
    _id = id;
    _nickname = nickname;
    _msgAccount = msgAccount;
    _mainAddress = mainAddress;
    _serviceAddress = serviceAddress;
    _serverDomain = serverDomain;
    _serverPort = serverPort;
    _serverPublicKey = serverPublicKey;
    _keyboxPublicKey = keyboxPublicKey;
    _keyboxId = keyboxId;
    _nodeType = nodeType;
    _keyboxStatus = keyboxStatus;
    _reviewStatus = reviewStatus;
    _description = description;
    _createAt = createAt;
}

  StakingListData.fromJson(dynamic json) {
    _id = json['id'];
    _nickname = json['nickname'];
    _msgAccount = json['msg_account'];
    _mainAddress = json['main_address'];
    _serviceAddress = json['service_address'];
    _serverDomain = json['server_domain'];
    _serverPort = json['server_port'];
    _serverPublicKey = json['server_public_key'];
    _keyboxPublicKey = json['keybox_public_key'];
    _keyboxId = json['keybox_id'];
    _nodeType = json['node_type'];
    _keyboxStatus = json['keybox_status'];
    _reviewStatus = json['review_status'];
    _description = json['description'];
    _createAt = json['create_at'];
  }
  int? _id;
  String? _nickname;
  String? _msgAccount;
  String? _mainAddress;
  String? _serviceAddress;
  String? _serverDomain;
  int? _serverPort;
  String? _serverPublicKey;
  String? _keyboxPublicKey;
  String? _keyboxId;
  int? _nodeType;
  bool? _keyboxStatus;
  int? _reviewStatus;
  String? _description;
  int? _createAt;
StakingListData copyWith({  int? id,
  String? nickname,
  String? msgAccount,
  String? mainAddress,
  String? serviceAddress,
  String? serverDomain,
  int? serverPort,
  String? serverPublicKey,
  String? keyboxPublicKey,
  String? keyboxId,
  int? nodeType,
  bool? keyboxStatus,
  int? reviewStatus,
  String? description,
  int? createAt,
}) => StakingListData(  id: id ?? _id,
  nickname: nickname ?? _nickname,
  msgAccount: msgAccount ?? _msgAccount,
  mainAddress: mainAddress ?? _mainAddress,
  serviceAddress: serviceAddress ?? _serviceAddress,
  serverDomain: serverDomain ?? _serverDomain,
  serverPort: serverPort ?? _serverPort,
  serverPublicKey: serverPublicKey ?? _serverPublicKey,
  keyboxPublicKey: keyboxPublicKey ?? _keyboxPublicKey,
  keyboxId: keyboxId ?? _keyboxId,
  nodeType: nodeType ?? _nodeType,
  keyboxStatus: keyboxStatus ?? _keyboxStatus,
  reviewStatus: reviewStatus ?? _reviewStatus,
  description: description ?? _description,
  createAt: createAt ?? _createAt,
);
  int? get id => _id;
  String? get nickname => _nickname;
  String? get msgAccount => _msgAccount;
  String? get mainAddress => _mainAddress;
  String? get serviceAddress => _serviceAddress;
  String? get serverDomain => _serverDomain;
  int? get serverPort => _serverPort;
  String? get serverPublicKey => _serverPublicKey;
  String? get keyboxPublicKey => _keyboxPublicKey;
  String? get keyboxId => _keyboxId;
  int? get nodeType => _nodeType;
  bool? get keyboxStatus => _keyboxStatus;
  setKeyboxStatus(bool state){
    _keyboxStatus = state;
  }
  int? get reviewStatus => _reviewStatus;
  String? get description => _description;
  int? get createAt => _createAt;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['id'] = _id;
    map['nickname'] = _nickname;
    map['msg_account'] = _msgAccount;
    map['main_address'] = _mainAddress;
    map['service_address'] = _serviceAddress;
    map['server_domain'] = _serverDomain;
    map['server_port'] = _serverPort;
    map['server_public_key'] = _serverPublicKey;
    map['keybox_public_key'] = _keyboxPublicKey;
    map['keybox_id'] = _keyboxId;
    map['node_type'] = _nodeType;
    map['keybox_status'] = _keyboxStatus;
    map['review_status'] = _reviewStatus;
    map['description'] = _description;
    map['create_at'] = _createAt;
    return map;
  }

}