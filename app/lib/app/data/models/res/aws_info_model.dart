import 'base_res_model.dart';

class AwsInfo extends BaseRes {
  AwsInfoData? data;

  AwsInfo({int? code, String? msg, this.data}) : super(code: code, msg: msg);

  AwsInfo.fromJson(Map<String, dynamic> json) : super.fromJson(json) {
    data = json['data'] != null ? AwsInfoData?.fromJson(json['data']) : null;
  }

  @override
  Map<String, dynamic> toJson() {
    final body = super.toJson();
    body['data'] = data?.toJson();
    return body;
  }
}

class AwsInfoData {
  String? bucket;
  String? endpoint;
  String? region;
  bool? ssl;
  bool? useVirtualAdressing;
  String? version;

  AwsInfoData(
      {this.bucket,
      this.endpoint,
      this.region,
      this.ssl,
      this.useVirtualAdressing,
      this.version});

  AwsInfoData.fromJson(Map<String, dynamic> json) {
    bucket = json['bucket'];
    endpoint = json['endpoint'];
    region = json['region'];
    ssl = json['ssl'];
    useVirtualAdressing = json['use_virtual_adressing'];
    version = json['version'];
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['bucket'] = bucket;
    data['endpoint'] = endpoint;
    data['region'] = region;
    data['ssl'] = ssl;
    data['use_virtual_adressing'] = useVirtualAdressing;
    data['version'] = version;
    return data;
  }
}
