import 'package:flutter_metatel/app/modules/dao/bean/banner_bean.dart';

import 'base_res_model.dart';

class EmoticonResModel extends BaseRes {
  List<EmBean>? data;

  EmoticonResModel({int? code, String? msg, this.data})
      : super(code: code, msg: msg);

  EmoticonResModel.fromJson(Map<String, dynamic> json) : super.fromJson(json) {
    if (json['data'] != null) {
      data = <EmBean>[];
      json['data'].forEach((v) {
        data?.add(EmBean.fromJson(v));
      });
    }
  }

  @override
  Map<String, dynamic> toJson() {
    final body = super.toJson();
    if (data != null) {
      body['data'] = data?.map((v) => v.toJson()).toList();
    }
    return body;
  }
}

class EmBean {
  int? mintId;
  String? name;

  EmBean.fromJson(Map<String, dynamic> json) {
    mintId = json['mint_id'];
    name = json['name'];
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['mint_id'] = mintId;
    data['name'] = name;
    return data;
  }
}
