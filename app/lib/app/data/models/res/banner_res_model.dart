import 'package:flutter_metatel/app/modules/dao/bean/banner_bean.dart';

import 'base_res_model.dart';

class BannerResModel extends BaseRes {
  List<BannerBean>? data;
  BannerResModel({int? code, String? msg, this.data})
      : super(code: code, msg: msg);

  BannerResModel.fromJson(Map<String, dynamic> json) : super.fromJson(json) {
    if (json['data'] != null) {
      data = <BannerBean>[];
      json['data'].forEach((v) {
        data?.add(BannerBean.fromJson(v));
      });
    }
  }

  @override
  Map<String, dynamic> toJson() {
    final body = super.toJson();
    if (data != null) {
      body['data'] = data?.map((v) => v.toJson()).toList();
    }
    return body;
  }
}
