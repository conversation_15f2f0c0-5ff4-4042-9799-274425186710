import 'base_res_model.dart';

class UserModel extends BaseRes{
  UserBody? data;

  UserModel({int? code, String? msg, this.data
  }):super(code: code,msg: msg);

  UserModel.fromJson(Map<String, dynamic> json):super.fromJson(json) {
    data = json['data'] != null ? UserBody?.fromJson(json['data']) : null;
  }

  @override
  Map<String, dynamic> toJson() {
    final body =super.toJson();
    body['data'] = data?.toJson();
    return body;
  }
}

class UserBody {
  String? phone;
  String? name;

  UserBody({this.phone, this.name});

  UserBody.fromJson(Map<String, dynamic> json) {
    phone = json['phone'];
    name = json['name'];
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['phone'] = phone;
    data['name'] = name;
    return data;
  }
}
