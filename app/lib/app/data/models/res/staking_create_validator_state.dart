import 'package:flutter_metatel/app/data/models/res/base_res_model.dart';

/// data : {"total":300,"level1":200,"level2":100,"size":100,"page":1,"value":[{"account":"8TzZudAZ4CkfbZ3NA5WfXkve9t6SXrpt3NzRUZavnMgs","time":"2023-3-18 14:00:12","reward":100},{"account":"9TzZudAZ4CkfbZ3NA5WfXkve9t6SXrpt3NzRUZavnMgs","time":"2023-3-19 14:00:12","reward":100}]}

class CreateValidatorStateRes extends BaseRes {
  CreateValidatorStateRes({
    this.status,
  });

  CreateValidatorStateRes.fromJson(dynamic json) :super.fromJson(json){
    status = json['status'];
  }

  int? status;

  @override
  Map<String, dynamic> toJson() {
    final map = super.toJson();
    if (status != null) {
      map['data'] = status;
    }
    return map;
  }
}

