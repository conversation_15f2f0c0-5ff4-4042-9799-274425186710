
import 'package:flutter_metatel/app/data/models/res/base_res_model.dart';

class MexGetDdcListResModel extends BaseRes{
  List<MexGetDdcListResDataList>? data;
  MexGetDdcListResModel({int? code, String? msg, this.data}) : super(code: code, msg: msg);
  MexGetDdcListResModel.fromJson(Map<String,dynamic> json):super.fromJson(json){
    if (json['data'] != null) {
      data = <MexGetDdcListResDataList>[];
      json['data'].forEach((v) {
        data?.add(MexGetDdcListResDataList.fromJson(v));
      });
    }
  }
}

// class MexGetDdcListResData{
//   MexGetDdcListResData(this.list);
//   List<MexGetDdcListResDataList>? list;
//   MexGetDdcListResData.fromJson(Map<String,dynamic> sourceJson){
//     if(sourceJson["list"]!=null){
//       list=[];
//       for (var element in (sourceJson["list"] as List)) {
//         list?.add(MexGetDdcListResDataList.fromJson(element));
//       }
//     }
//   }
// }
///暂时不用
///{"Total-Count":1, "Page-Count":1, "Current-Page":1, "Per-Page":20 }
class MexGetDdcListResDataPage{

}
///    {"name":"元交所619", "file_thumbnail":"/writable/uploads/2022-06-09/1654763599_7010568997fa29707330.jpeg","file_type":"image","file_url":"/writable/uploads/2022-06-09/1654763599_b2443ef3e70ea858f463.jpeg",
///     "author_info":{ "name":"元交所","picture_card":"/writable/uploads/2022-06-09/1654763599_613066257dd515ba442c.jpeg","about":{"text":"这里是文字介绍", "file_url":""}},
///     "tags":["创世活动", "BSN-DDC","活动限定", "头像" ]}
class MexGetDdcListResDataList{
  MexGetDdcListResDataList(this.name);
  String? name;
  String? fileThumbnail;
  String? fileType;
  String? fileUrl;
  bool hasSet=false;
  MexGetDdcListResDataListAuthorInfo? authorInfo;
  MexGetDdcListResDataList.fromJson(Map<String,dynamic> sourceJson){
    name=sourceJson["name"];
    fileThumbnail=sourceJson["file_thumbnail"];
    fileType=sourceJson["file_type"];
    fileUrl=sourceJson["file_url"];
    // var decode = json.decode(sourceJson["author_info"]);
    authorInfo=MexGetDdcListResDataListAuthorInfo.fromJson(sourceJson["author_info"]);
  }
}


///暂时不用
///"tags":[ "创世活动", "BSN-DDC", "活动限定","头像" ]
class MexGetDdcListResDataListTags{

}
///author_info":{ "name":"元交所", "picture_card":"/writable/uploads/2022-06-09/1654763599_613066257dd515ba442c.jpeg","about":{ "text":"这里是文字介绍","file_url":""}}
class MexGetDdcListResDataListAuthorInfo{
  String? name;
  String? pictureCard;
  MexGetDdcListResDataListAuthorInfoAbout? about;
  MexGetDdcListResDataListAuthorInfo.fromJson(Map<String,dynamic> sourceJson){
    name=sourceJson["name"];
    pictureCard=sourceJson["picture_card"];
    // var decode = json.decode(sourceJson["about"]);
    about=MexGetDdcListResDataListAuthorInfoAbout.fromJson(sourceJson["about"]);
  }

}
///:{ "text":"这里是文字介绍", "file_url":"" }
class MexGetDdcListResDataListAuthorInfoAbout{
  String? text;
  String? fileUrl;

  MexGetDdcListResDataListAuthorInfoAbout.fromJson(Map<String,dynamic> json){
    text=json["text"];
    fileUrl=json["file_url"];
  }

}

