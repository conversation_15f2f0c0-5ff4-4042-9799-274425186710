import 'package:flutter_metatel/app/data/models/res/base_res_model.dart';

class MexGetUserInfoModel extends BaseRes{
  MexGetUserInfoData? data;
  MexGetUserInfoModel({int? code, String? msg, this.data}) : super(code: code, msg: msg);

  MexGetUserInfoModel.fromJson(Map<String, dynamic> json) : super.fromJson(json) {
    if(code==200){
      data = json['data'] != null ? MexGetUserInfoData?.fromJson(json['data']) : null;
    }
  }
  @override
  Map<String, dynamic> toJson() {
    final body = super.toJson();
    body['data'] = data?.toJson();
    return body;
  }
}
class MexGetUserInfoData{
  int? mobile;
  String? userName;
  String? address;
  MexGetUserInfoData.fromJson(Map<String, dynamic> json) {
    mobile = json['mobile'];
    userName = json['username'];
    address = json['address'];
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['mobile'] = mobile;
    data['username'] = userName;
    data['address'] = address;
    return data;
  }
}
