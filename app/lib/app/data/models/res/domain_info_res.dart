/// code : 200
/// msg : "success"
/// data : {"id":1,"domain":"test.linksay.test","node_type":3,"chanid":"","create_at":1727246060129}

class DomainInfoRes {
  DomainInfoRes({
      int? code, 
      String? msg, 
      DomainInfoData? data,}){
    _code = code;
    _msg = msg;
    _data = data;
}

  DomainInfoRes.fromJson(dynamic json) {
    _code = json['code'];
    _msg = json['msg'];
    _data = json['data'] != null ? DomainInfoData.fromJson(json['data']) : null;
  }
  int? _code;
  String? _msg;
  DomainInfoData? _data;
DomainInfoRes copyWith({  int? code,
  String? msg,
  DomainInfoData? data,
}) => DomainInfoRes(  code: code ?? _code,
  msg: msg ?? _msg,
  data: data ?? _data,
);
  int? get code => _code;
  String? get msg => _msg;
  DomainInfoData? get data => _data;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['code'] = _code;
    map['msg'] = _msg;
    if (_data != null) {
      map['data'] = _data?.toJson();
    }
    return map;
  }

}

/// id : 1
/// domain : "test.linksay.test"
/// node_type : 3
/// chanid : ""
/// create_at : 1727246060129

class DomainInfoData {
  DomainInfoData({
      int? id, 
      String? domain, 
      int? nodeType, 
      String? chanid, 
      int? createAt,}){
    _id = id;
    _domain = domain;
    _nodeType = nodeType;
    _chanid = chanid;
    _createAt = createAt;
}

  DomainInfoData.fromJson(dynamic json) {
    _id = json['id'];
    _domain = json['domain'];
    _nodeType = json['node_type'];
    _chanid = json['chanid'];
    _createAt = json['create_at'];
  }
  int? _id;
  String? _domain;
  int? _nodeType;
  String? _chanid;
  int? _createAt;
DomainInfoData copyWith({  int? id,
  String? domain,
  int? nodeType,
  String? chanid,
  int? createAt,
}) => DomainInfoData(  id: id ?? _id,
  domain: domain ?? _domain,
  nodeType: nodeType ?? _nodeType,
  chanid: chanid ?? _chanid,
  createAt: createAt ?? _createAt,
);
  int? get id => _id;
  String? get domain => _domain;
  int? get nodeType => _nodeType;
  String? get chanid => _chanid;
  int? get createAt => _createAt;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['id'] = _id;
    map['domain'] = _domain;
    map['node_type'] = _nodeType;
    map['chanid'] = _chanid;
    map['create_at'] = _createAt;
    return map;
  }

}